import PortalHeader from "../portal-header";
import AsideMenu from "../aside-menu";
// 引入样式表
import {theme as deep} from "../theme/deep";
import {theme as light} from "../theme/light";
import {globalBus} from "@/api/globalBus";

export default {
    name: "protalWrap",
    props: {
        defaultHome: {
            type: String,
            default: ""
        },
        navs: {
            type: Array,
            default() {
                return [];
            }
        },
        config: {
            //门户总配置
            type: Object,
            default() {
                return {};
            }
        },
        opens: {
            type: Array,
            default() {
                return [];
            }
        },
        isPreview: Boolean
    },
    computed: {
        currTheme : {
            get(){
                return this.config.theme === "deep" ? deep : light;
            }
        },
        pageUrl() {
            let url = "/*?id=500";
            if (this.sourceType === "outHref" && this.IsURL(this.src)) {
                url = this.src;
            } else if (this.sourceType === "dashboard") {
                url = this.src;
            }
            return url;
        }
    },
    watch: {
        navs: {
            handler(val) {
                const vm = this;
                // 当前路由如果和用户当前配置的菜单一致，页面只需要实时响应对应配置页面
                // currTreeNodeKey 右侧菜单当前编辑项 path
                vm.handleDivisionNavs(vm.config.plan);
                vm.setCurrentMenu();
            },
            deep: true
        },
        "config.plan": {
            handler(to) {
                this.handleDivisionNavs(to);
                this.setCurrentMenu();
            },
            deep: true,
            immediate: true
        },

        $route: {
            handler(to) {
                const vm = this;
                // 设置当前菜单激活项
                vm.headerCurrActive = to.params.head && `/${vm.prefix || "portal"}/${to.params.head}`;
                // 这边原本可以直接设置 to.path 但是在预览模式下 to.path 会多/preview参数 导致无法匹配左侧菜单激活项
                vm.currActive = to.params.id ? `${vm.headerCurrActive}/${to.params.id}` : vm.headerCurrActive;
            },
            immediate: true
        }
    },
    data() {
        return {
            headNavs: [],
            asideNavs: [],
            currActive: "", // 左部当前激活菜单
            src: "",
            dashboard: "",
            headerCurrActive: "", //顶部当前激活菜单
            parent: null,
            current: null,
            prevNav: {},
            resetMenu: true,
            sourceType: "",

        };
    },
    methods: {
        /**
         *
         * @param str_url
         * @returns {boolean}
         * @constructor
         */
        IsURL(str_url) {
            let re = /^((https|http|ftp|rtsp|mms)?:\/\/)+[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/;
            return re.test(str_url);
        },
        /**
         * 设置当前编辑选择菜单
         */
        setCurrentMenu() {
            const vm = this, {config, parent, current} = vm;
            if (!parent || !current) return;
            if (config.plan === "double") {
                vm.headerCurrActive = parent.id;
                vm.currActive = current.id;
                vm.selectMenu(vm.headerCurrActive, true);
                vm.selectAsideMenu(vm.currActive, true);
            } else if (config.plan === "left") {
                vm.currActive = current.id;
                vm.selectAsideMenu(vm.currActive, true);
            } else if (config.plan === "top") {
                vm.headerCurrActive = current.id;
                vm.selectAsideMenu(vm.headerCurrActive, true);
            }
        },
        /**
         * 选择菜单事件
         * @param key
         * @param clear 清空 current , parent
         */
        selectMenu(key, clear) {
            if (!clear) {
                this.parent = null;
                this.current = null;
            }
            this.handleClickNav(key);

        },
        selectAsideMenu(key, clear) {
            if (!clear) {
                this.parent = null;
                this.current = null;
            }
            this.handleToPage(key);
        },
        /**
         * 选择菜单事件
         * @param route
         */
        handleClickNav(route) {
            const vm = this;
            const {config} = vm;
            if (config.plan === "double") {
                const navs = JSON.parse(JSON.stringify(vm.navs));
                const nav = vm.handleFilterNav(route, vm.navs) || navs[0];
                vm.handleSetAsideNavs(nav);
            }
            vm.handleToPage(route);
        },
        /**
         * 设置左侧菜单
         * @param {object} nav 当前一级菜单
         */
        handleSetAsideNavs(nav) {
            const vm = this;
            vm.asideNavs = nav && nav.children || [];
        },
        /**
         *
         * @param nav
         */
        setPreviewFocus(nav) {
            if (!nav) return;
            const vm = this, {config} = vm;
            if (vm.isPreview) {
                if (config.plan === "double") {
                    vm.headerCurrActive = nav.root;
                    vm.currActive = nav.id;
                    let p_nav = vm.handleFilterNav(nav.root, vm.navs);
                    vm.handleSetAsideNavs(p_nav);
                } else if (config.plan === "left") {
                    vm.currActive = nav.id;
                } else if (config.plan === "top") {
                    vm.headerCurrActive = nav.id;
                }
                vm.prevNav = JSON.parse(JSON.stringify(nav));
            }
        },
        /**
         * 跳转用户配置对应的页面
         * @param key 路由参数
         * @param isLoadTo
         */
        async handleToPage(key, isLoadTo) {
            const vm = this, {parent, current} = vm;
            const nav = await vm.handleFilterNav(key, vm.navs) || vm.navs[0];
            const isHasChild = nav && nav.children && nav.children.length > 0;
            if (vm.isHasSource(nav) && !isHasChild) {
                const {openMethods, source, sourceType} = nav.config;
                vm.sourceType = sourceType;
                let src = sourceType === "outHref" ? vm.setOutLink(source) : vm.setFrameSrc(source);
                if (openMethods === "_blank" && vm.isPreview) {
                    vm.handleOpenNewWindow(nav, src, isLoadTo);
                } else {
                    vm.src = src;
                    vm.setPreviewFocus(nav);
                }
            } else {
                isHasChild && !current && vm.handlePreview(nav); // 如果点击的非叶子节点 先找寻底下第一个叶子节点 跳转页面
                vm.src = "";
                vm.setPreviewFocus(nav);
            }
        },
        /**
         *
         * @param source
         */
        setFrameSrc(source) {
            let {href} = this.$router.resolve({name: 'visualView', query: {rowid: source}});
            return href;
        },
        /**
         *
         * @param source
         */
        setOutLink(source) {
            let src = source.replace(/\s+/g, '');
            if (src.indexOf("http://") !== 0 && src.indexOf("https://") !== 0 && src.indexOf("ftp://") !== 0) {
                src = "http://" + src;
            }
            return src;
        },
        /**
         * 打开新窗口
         * @param nav
         * @param src
         * @param isLoadTo
         */
        handleOpenNewWindow(nav, src, isLoadTo) {
            const vm = this;
            const {query} = vm.$route;
            const {href} = vm.$router.resolve({
                name: "homeView",
                query: {id: query.id, boardId: nav.id}
            });
            if (isLoadTo) {
                vm.setPreviewFocus(nav);
                vm.src = src;
            } else {
                vm.resetMenu = false;
                vm.setPreviewFocus(vm.prevNav);
                window.open(href, "_blank");
            }
            setTimeout(() => {
                vm.resetMenu = true;
            }, 100);
        },

        /**
         * 预览菜单第一项叶子节点
         */
        handlePreview(nav) {
            const vm = this;
            while (nav && !nav.isLeaf) {
                nav = nav.children[0];
            }
            vm.currActive = nav.id;
            vm.handleToPage(nav.id)
        },
        /**
         * 判断当前是否存在数据源 满足以下条件 才会存在数据源
         * @param {object} nav 当前菜单数据
         * @returns {boolean} true or false
         */
        isHasSource(nav) {
            return nav && nav.config && nav.config.source && !nav.config.isEmpty;
        },

        /**
         * 根据菜单布局分割菜单结构
         * @param {string} layout 菜单布局
         */
        handleDivisionNavs(layout) {
            const vm = this;
            const {params} = vm.$route;
            if (layout === "top") {
                vm.headNavs = vm.navs;
                vm.asideNavs = [];
            } else if (layout === "left") {
                vm.headNavs = [];
                vm.asideNavs = vm.navs;
            } else {
                // 当前只有当菜单布局为 double时
                // 这边有一个问题，每当用户修改右侧菜单配置项时，都要去重新渲染菜单
                const menu = JSON.parse(JSON.stringify(vm.navs));
                // 每次左侧只显示当前一级菜单下的 二级菜单 所以要查找当前是在哪个一级菜单下
                const nav = vm.handleFilterNav(params.head, vm.navs) || menu[0];
                menu.forEach((item) => {
                    // double 结构下 头部的菜单永远只有一级 所以要删除底下的children 这样才不会渲染在头部
                    if (item.hasOwnProperty("children")) {
                        vm.$delete(item, "children");
                    }
                });

                vm.headNavs = menu;
                vm.asideNavs = nav && nav.children || []; // 侧边菜单为当前一级菜单下的  children 数据
            }
        },
        /**
         * 筛选菜单项
         * @param {string} key 条件
         * @param {array} ary 数组
         */
        handleFilterNav(key, ary) {
            const vm = this;
            const undoList = [...ary];
            while (undoList.length > 0) {
                const item = undoList.shift();
                if (item.id === key) {
                    return item;
                } else {
                    if (Array.isArray(item.children) && item.children.length > 0) {
                        undoList.push(...item.children);
                    }
                }
            }
        },
        menuChange(parent, current) {
            this.parent = parent;
            this.current = current;
        },
        bindBusEvent() {
            globalBus.$on("menuChange", this.menuChange);
        },
        unbindBusEvent() {
            globalBus.$off("menuChange", this.menuChange);
        }
    },
    components: {
        PortalHeader,
        AsideMenu
    },
    created() {
        this.bindBusEvent();
    },
    mounted() {
        //初始化跳转
        this.isPreview && this.handleToPage(this.defaultHome, true);
    },
    destroyed() {
        this.unbindBusEvent();
    }
}
