<template>
    <div class="ce-share">
        <el-tabs class="ce-share_tab" v-model="activeName" @tab-click="tabClick">
            <el-tab-pane v-for="(tab , inx) in tabPanes" :key="inx" :label="tab.label" :name="tab.value">
            </el-tab-pane>
        </el-tabs>
        <keep-alive>
            <component ref="share" class="ShareList_cont" :is="activeName" :data="shareData"></component>
        </keep-alive>
    </div>
</template>

<script>
import ShareCardList from "@/projects/DataCenter/views/home/<USER>/share-page/ShareCardList";
import ShareTableList from "@/projects/DataCenter/views/home/<USER>/share-page/ShareTableList";

export default {
    name: "ShareDetail",
    components: {
        ShareCardList,
        ShareTableList
    },
    data() {
        return {
            activeName: "ShareCardList",
            tabPanes: [
                {label: "来自分享", value: "ShareCardList"},
                {label: "我分享的", value: "ShareTableList"},
            ],
            shareData: []
        }
    },
    methods: {
        tabClick() {
            const vm = this, {activeName} = vm;
            vm.pageSize = activeName === "ShareCardList" ? 9 : 5;
            vm.$nextTick(() => {
                if (activeName === "ShareCardList") {
                    //  参数 资源类型 , 用户 , 关键字
                    vm.$refs.share.searchTable();
                } else {
                    // 参数 资源类型 资源名称关键字 用户名 角色名
                    vm.$refs.share.searchTable();
                }
            })
        }
    },
    mounted() {
        const vm = this;
        let {tab, operate, type} = this.$route.params;
        if (tab) {
            this.activeName = tab;
            vm.$nextTick(() => {
                if(type) vm.$refs.share.activeN = type;
                vm.tabClick();
                if (operate) {
                    vm.$refs.share.showShare();
                }
            })
        }
    }
}
</script>
<style lang="less">
@import "./share";
</style>
<style scoped lang="less">
.ce-share {
    height: calc(100% - 12px);
    background: #fff;
    border-radius: 2px;
    box-sizing: border-box;
    padding: 10px 0;
}

.ce-share_tab {
    &.el-tabs--top /deep/ .el-tabs__item.is-bottom:nth-child(2),
    &.el-tabs--top /deep/ .el-tabs__item.is-top:nth-child(2) {
        padding-left: 30px;
    }
}

.ShareList_cont {
    height: calc(100% - 54px);
    padding: 10px 28px;
    box-sizing: border-box;
}
</style>
<style>
.ce-case-source_name > .cell::before {
    content: "";
    padding-left: 10px;
}
</style>
