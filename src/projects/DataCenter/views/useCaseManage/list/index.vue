<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <div class="ce-table_btns">
                <dg-button slot="reference" type="primary"  @click="APICreate('add')" v-if="rights.indexOf('useCaseManagementAddUseCase') > -1">{{ createUseCase }}</dg-button>
                <!--<dg-button slot="reference"-->
                    <!--type="primary" v-if="rights.indexOf('serviceManagementBatchSharing') > -1" @click="batchShare">{{ buttonTxt }}-->
                <!--</dg-button>-->
            </div>
            <div>
                <div class="ce-table__search">
                    <el-input
                            size="mini"
                            class="ml10"
                            placeholder="请输入名称搜索"
                            v-model.trim="inputValueTable"
                            v-input-limit:trim
                            @input="inputFilterSpecial($event , 'inputValueTable')"
                            @keyup.enter.native="searchTableEvent"
                    >
                        <i
                                class="el-icon-search el-input__icon poi"
                                slot="suffix"
                                @click="searchTableEvent">
                        </i>
                    </el-input>
                </div>
            </div>
        </div>
        <div class="d-s-r-table">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    :max-height="tableBodyH"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
                    @selection-change="handleSelectionChange"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action"
                        v-for="(item,index) in hasRightOptIcon"
                        :key="index"
                        v-if="index < 3 && item.show(row)"
                    >
                        <el-button type="text"
                                :class="item.class"
                                @click="item.clickFn( row , $index)"
                                :title="item.name"
                        >{{ item.name }}</el-button> <!--v-html="item.icon"-->
                    </span>
                    <dir-edit-action v-if="countHasRightMenu > 3"
                                placement="bottom"
                                @command="menuCommand($event , row , $index)" :data="hasRightOptIcon.slice(3)"
                                :node="row"
                                :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>{{moreTxt}}</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
            </common-table>
        </div>
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>
