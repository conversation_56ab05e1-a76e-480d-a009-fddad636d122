<!--字段选择表格-->
<template>
    <div class="FieldTableCheck"
        :style="{
            '--title-c' : titleColor
        }"
    >
        <div class="field-title" :title="title">{{title}}</div>
        <div class="field-header">
            <div class="field-count" v-text="`${countTxt} (${selectField.length}/${data.length})`"></div>
            <el-input class="field-filter"
                      v-model="filterTxt"
                      v-input-limit:trim
                      :placeholder="placeholder" suffix-icon="el-icon-search"></el-input>
        </div>
        <el-common-table
                ref="table"
                :data="showTableList"
                :columns="tableHead"
                :height="tableHeight"
                @select="selectionChange"
                @select-all="selectionChange"
        ></el-common-table>
    </div>
</template>

<script>
export default {
    name: "FieldTableCheck",
    props : {
        title : String ,
        titleColor : {
            type : String,
            default : '#555'
        },
        data : {
            type : Array ,
            default(){
                return [];
            },
            required : true
        },
        tableHeight : {
            type : String ,
            default : '410px'
        },
        selectedList : {
            type : Array ,
            default(){
                return [];
            },
        },
        headProps : {
            type : Array ,
            default(){
                return [
                    {
                        prop : 'code',
                        label : '字段名'
                    },{
                        prop : 'cnName',
                        label : '字段中文名'
                    },
                ]
            }
        },
        nameKey : {
            type : String,
            default : 'cnName'
        }
    },
    computed : {
        showTableList (){
            return this.data.filter(li => !this.filterTxt || li[this.nameKey].toLowerCase().includes(this.filterTxt.toLowerCase()));
        },
        selectField : {
            get(){
                return this.selectedList;
            },
            set(val){
                this.$emit('update:selectedList' ,val);
            }
        }
    },
    watch : {
        filterTxt(){
            this.backSelectFields();
        },
    },
    data(){
        return {
            filterTxt : '',
            placeholder :'请输入字段名搜索',
            countTxt : '字段选择',
            tableHead : [
                {
                    type : 'selection'
                },
                ...this.headProps
            ],
        }
    },
    methods : {
        selectionChange(selection){
            this.selectField = selection;
        },
        /**
         *  回显勾选字段
         */
        backSelectFields(){
            const vm = this;
            this.$nextTick(()=>{
                vm.selectField.forEach(row => {
                    vm.$refs.table.$refs.table.toggleRowSelection(row);
                })
            })
        }
    }
}
</script>

<style scoped lang="less">
.field {
    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 2rem;
        margin-bottom: 10px;
        font-size: 14px;
    }
    &-filter {
        width: 200px;
    }
    &-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color : var(--title-c);
        line-height: 30px;
    }
    &-count {
        color: #666;
    }
}
</style>
