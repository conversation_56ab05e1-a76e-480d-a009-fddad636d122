<template>
    <div class="moduleManage">
        <ce-list-table
                :border="false"
                :filter-text.sync="filterText"
                :operate-icons="operateIcons"
                :t-head-data="tHeadData"
                :table-data="tableData"
                @changePage="changePage"
        >
            <el-button slot="button" type="primary" @click="addModule">{{addBtnTxt}}</el-button>

            <template slot="name" slot-scope="{row , $index }" >
                <div class="moduleManage-name" @click="moduleClick(row)" :key="$index">{{row.name}}</div>
            </template>
        </ce-list-table>
    </div>
</template>

<script src="./moduleManage.js"></script>
<style scoped lang="less" src="./moduleManage.less"></style>
