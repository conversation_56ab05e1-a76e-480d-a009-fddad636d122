<template>
    <div class="fieldSequence ce-plug_cont" v-loading="settings.loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont">
            <div class="ce-plug-btn_box">
                <el-button type="primary"
                           size="mini"
                           @click="add"
                           plain
                >{{addbtn}}
                </el-button>
            </div>
            <div class="ce-plug_attr">
                <div class="ce-list_cont mr-8" ref="list">
                    <el-form
                            class="ce-form height100"
                            size="mini"
                            ref="form"
                            label-width="80px"
                            label-position="right"
                            :model="{ list: streamingData }"
                            @submit.native.prevent
                    >
                        <el-table
                                height="100%"
                                cell-class-name="ce-cell_p0"
                                border
                                :show-header="status"
                                :data="streamingData"
                                size="mini">

                            <el-table-column type="index" align="center" width="40px"></el-table-column>
                            <el-table-column align="center">
                                <template slot-scope="{row , $index}">
                                    <el-form-item :prop="`list.${$index}.field`"
                                                  :rules="rules.field"
                                                  label="字段:"
                                    >
                                        <dg-select size="mini" v-model="row.field"
                                                   @change="fieldChange(arguments , $index)"
                                                   :data="fieldOpt"></dg-select>
                                    </el-form-item>
                                    <el-form-item :prop="`list.${$index}.order`"
                                                  label="排序:"
                                    >
                                        <dg-select size="mini" v-model="row.order" :data="sortOpt"></dg-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column width="30px" align="center">
                                <template v-for="(col , inx) in operateIcons" slot-scope="{row , $index}">
                                    <el-popconfirm
                                            size="mini"
                                            :key="inx"
                                            title="确认删除?"
                                            @confirm="col.clickFn(row, $index)"
                                    >
                                    <i
                                            slot="reference"
                                            class="icon ce_link model_status"
                                            :title="col.tip"
                                            v-html="col.icon"
                                    ></i>
                                    </el-popconfirm>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                </div>
            </div>

            <div class="attr_btn">
                <el-button
                        v-for="(btn , inx) in buttons"
                        :key="inx"
                        size="mini"
                        :type="btn.type"
                        @click="btn.clickFn"
                >{{btn.name}}
                </el-button>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script src="./field-sequence.js"></script>
<style scoped lang="less" src="./field-sequence.less"></style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
