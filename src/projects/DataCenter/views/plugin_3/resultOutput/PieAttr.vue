<template>
    <div class="pieAttr ce-plug_cont" v-loading="settings.loading">
        <div class="ce-plug_attr">
            <el-form :model="plug_data"
                     size="mini"
                     label-position="top"
                     :label-width="width"
                     class="attrForm"
                     ref="form"
            >
                <el-form-item
                        v-for="(attr , i ) in attrsList"
                        :key="i"
                        :label="attr.label + ' :'"
                        :prop="attr.props"
                        :rules="attr.rule"
                >
                    <el-input class="ce-attr_item"
                              :placeholder="attr.placeholder"
                              v-if="attr.type ==='input'" v-model="attr.value"
                    ></el-input>
                    <div v-if="attr.type ==='select'">
                        <i class="el-icon-info ce-tip" v-if="attr.hasTip" :title="attr.tip"></i>
                        <el-select filterable class="ce-attr_item" v-model="attr.value"
                                   @change="axisLabel($event , attr.option , i)" :placeholder="attr.placeholder">
                            <el-option
                                    v-for="item in attr.option"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </div>

                </el-form-item>
            </el-form>
            <FormTip/>
        </div>
        <div>
            <el-dialog :title="plug_data.title"
                       :visible.sync="dialogVisible"
                       :modal-append-to-body="true"
                       :append-to-body="true"
                       :close-on-click-modal="false"
                       @closed="clearData"
            >
                <div class="ce-chart_box" v-loading="loadChart.loading">
                    <PieChart v-if="reNew" :pieChartData="chartData" :plugData="plug_data"></PieChart>
                    <div v-else class="ce-data-none p30">{{data_none}}</div>
                </div>
            </el-dialog>
        </div>
        <!-- <div class="attr_btn">
            <el-button
                    v-for="(btn , inx) in buttons"
                    :key="inx"
                    size="mini"
                    :type="btn.type"
                    @click="btn.clickFn"
            >{{btn.name}}
            </el-button>
        </div> -->
    </div>
</template>

<script>
    import FormTip from './FormTip'
    import PieChart from './charts/PieChart'
    import {chartMixins} from "./chart-mixins/chart-mixins";
    import {commonMixins} from "@/api/commonMethods/common-mixins";
    import {servicesMixins} from "./service-mixins";

    export default {
        name: "PieAttr",
        components: {
            FormTip,
            PieChart
        },
        mixins: [chartMixins, commonMixins, servicesMixins],
        props: {
            sourceData: Object,
            rowData: Object
        },
        computed: {
            plug_data() {
                return {
                    title: this.attrsList.title.value,
                    measureData: this.attrsList.measure.value,
                    axisLabel: this.attrsList.measure.axisLabel,
                    chartCode: ""
                }
            }
        },
        data() {
            return {
                pieChartData: {},
                attrsList: {
                    title: {
                        label: '图表标题',
                        type: 'input',
                        value: '饼图图表展示',
                        placeholder: '请输入图表标题',
                        props: 'title',
                        rule: [
                            {required: true, message: '标题名称不能为空', trigger: 'blur'},
                        ]
                    },
                    measure: {
                        label: '度量字段',
                        type: 'select',
                        value: '',
                        axisLabel: "",
                        option: [],
                        props: 'measureData',
                        placeholder: '请选择度量字段',
                        rule: [
                            {required: true, message: '请选择度量字段', trigger: ['blur', 'change']},
                        ],
                    },
                    /*   chartCode : {
                           label: '表码配置',
                           type: 'select',
                           value: '',
                           option: [],
                           placeholder: '请选择表码配置',
                           props: 'chartCode',
                           rule: [],
                       }*/
                },
                pieChartsPlugin: {},
            }
        },
        methods: {
            init() {
                const vm = this, {resultServices, resultMock, settings, pie_root} = this;
                let services = vm.getServices(resultServices, resultMock);
                settings.loading = true;
                let path = pie_root + "getPieChartsPluginPage";
                services.getChartsPluginPage(vm.sourceData.id, settings, path).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.attrsList.measure.option = [];
                        // vm.attrsList.chartCode.option = [];
                        vm.attrsList.measure.option = result.columns.map(item => {
                            return {
                                label: item.columnZhName,
                                value: item.columnName,
                                columnType: item.columnType,
                                length: item.length,
                                precsn: item.presn,
                                stepCode: item.stepCode,
                                stepId: item.stepId,
                                stepName: item.stepName,
                            };
                        });
                        /* vm.attrsList.chartCode.option.push({
                             label: "无",
                             value: "",
                         });
                         result.bmCode.forEach(item => {
                             vm.attrsList.chartCode.option.push({
                                 label: item.name,
                                 value: item.name,
                             })
                         });*/
                        vm.pieChartsPlugin = result.pieChartsPlugin;
                        if (vm.pieChartsPlugin.title !== null) {
                            vm.attrsList.title.value = vm.pieChartsPlugin.title;
                        }
                        vm.attrsList.measure.value = vm.pieChartsPlugin.columnCode || "";
                        vm.savedChange(vm.attrsList.measure.value);
                        vm.axisLabel(vm.attrsList.measure.value, vm.attrsList.measure.option, "measure");
                        /*if (vm.pieChartsPlugin.bmCode !== null) {
                            vm.attrsList.chartCode.value = vm.pieChartsPlugin.bmCode;
                        }*/
                    }
                })
            },
            save(saveName,showTip) {
                const vm = this, {resultServices, resultMock, settings, pie_root} = this;
                let services = vm.getServices(resultServices, resultMock);
                vm.$refs.form.validate(valid => {
                    if (valid) {
                        let chartsType = "PIE";
                        settings.loading = true;
                        let path = pie_root + "savePieChartsPlugin";
                        services.saveChartsPlugin(vm.sourceData.id, vm.plug_data.title, chartsType, vm.plug_data.measureData,  vm.plug_data.chartCode , path ,  settings)
                            .then(res => {
                                if (res.data.status === 0) {
                                    vm.saved = true;
                                    showTip && vm.$message.success(res.data.data);
                                    vm.$emit("setPreviewAble");
                                    if(saveName)saveName();
                                } else {
                                    vm.$message.error("保存失败！");
                                }
                            })
                    }
                })

            }
        }
    }
</script>

<style lang="less" scoped ></style>
