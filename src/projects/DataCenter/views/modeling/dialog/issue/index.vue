<template>
    <common-dialog
                   v-loading="settings.loading"
                   :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <div v-if="reNew">
            <model-form v-show="showForm"  ref="form"></model-form>
            <model-params v-show="!showForm" :rowData="rowData" ref="params"></model-params>
        </div>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button type="primary" v-if="showForm" @click="prev">{{btnPrevTxt}}</dg-button>
            <dg-button type="primary" v-else @click="next">{{btnNextTxt}}</dg-button>
            <dg-button type="primary" v-if="showForm" @click="submit">{{issueBtnTxt}}</dg-button>
        </span>
    </common-dialog>
</template>

<script src="./issue.js"></script>