import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";

export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common, listMixins, coustTableH],
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        },
        // 是其他用户
        isOtherUser() {
            return this.currentUserId !== this.userInfo?.id;
        },
    },
    props: {
        renewListLoad: Function,
    },
    watch: {
        "settings.loading": {
            handler(val) {
                this.renewListLoad(val);
            },
            deep: true
        }
    },
    data() {
        return {
            inputValueTable: "",
            buttonTxt: "新建门户",
            addThemePortal: $right["themePortalCreatePortal"],
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "名称",
                    minWidth: "160",
                    align: "left"
                },
                {
                    prop: "createName",
                    label: "创建人",
                    width: "",
                    align: "center"
                },
                {
                    prop: "operateTime",
                    label: "最后编辑时间",
                    minWidth: 160,
                    align: "center",
                    resizable: false
                },
                {
                    prop: 'operate',
                    label: '操作',
                    width: 180,
                    align: "center",
                    resizable: false
                },
            ],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe792;",
                    name: "查看",
                    clickFn: this.viewPanel,
                    condition: (right) => right.indexOf($right["themePortalGetPortalPageByUrl"]) > -1,
                    show: () => true
                },
                {
                    icon: "&#xe840;",
                    name: "编辑",
                    clickFn: this.editPanel,
                    condition: (right) => right.indexOf($right["themePortalUpDataPortal"]) > -1 && !this.isOtherUser,
                    show: (row) => row.isCreator
                },
                /* {icon: "&#xe6c9;", tip: "安全策略", clickFn: this.moveTo, condition: (right) => true},*/
                {
                    icon: "&#xe65f;",
                    name: "删除",
                    clickFn: this.deletePanel,
                    condition: (right) => right.indexOf($right["themePortalDeletePortal"]) > -1 && !this.isOtherUser,
                    show: (row) => row.isCreator
                },
            ],
            groupId: "",
            treeNode: {},
            currentUserId: '',
        }
    },
    methods: {
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
        changePage(index) {
            const vm = this, {portalServices, portalMock, settings} = this;
            let services = vm.getServices(portalServices, portalMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = {
                dirId: vm.groupId,
                keyWord: vm.inputValueTable,
                pageNum: index,
                pageSize: vm.paginationProps.pageSize,
                operatorId: vm.currentUserId,
            };
            services.getPortalList(data, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.total = result.totalCount;
                    vm.tableData = result.dataList;
                }
            })
        },
        getData(groupId, filterTxt) {
            this.groupId = groupId;
            this.inputValueTable = filterTxt;
            this.changePage(1);
        },
        searchTableEvent() {
            this.changePage(1);
        },
        initTableData(treeNodeData) {
            this.treeNode = treeNodeData;
            this.groupId = treeNodeData.id;
            this.currentUserId = treeNodeData.currentUserId;
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        addPortal() {
            if (this.treeNode && (this.treeNode.level === 1 || this.treeNode.id === "2")) {
                this.$message.warning("门户只能挂载 '我的' 及其子目录,或管理员的'标准模型'");
                return;
            }
            this.$emit('showPanel', this.inputValueTable, this.groupId);
        },
        viewPanel(row) {
            let routeData = this.$router.resolve({name: "homeView", query: {id: row.id}});
            window.open(routeData.href, '_blank');
        },
        editPanel(row, index) {
            this.$emit('showPanel', this.inputValueTable, row.ownerId, row, this.treeNode.id);
        },
        moveTo(row, index) {
            // this.$refs.move.moveShow(row );
            const _this = this;
            let layer = this.$dgLayer({
                title: "安全菜单",
                content: require("../dialog/security-policy/index"),
                maxmin: false,
                move: false,
                props: {tableData: this.tableData[index]},
                on: {
                    updateSuccess() {
                        _this.changePage(_this.paginationProps.currentPage);
                    }
                },
                cancel(index, layero) {
                    // 关闭对应弹窗的ID
                    layer.close(index);
                    return true;
                },
                area: ["530px", "600px"]
            });

        },
        deletePanel(row) {
            const vm = this, {portalServices, portalMock} = this;
            let services = vm.getServices(portalServices, portalMock);
            vm.confirm("删除", `确定删除\"${row.name}\"门户吗?`, () => {
                services.deletePortal(row.id).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功!");
                        vm.changePage(1);
                    }
                })
            })
        },

    },
    created() {
        // this.changePage(1);
    }
}
