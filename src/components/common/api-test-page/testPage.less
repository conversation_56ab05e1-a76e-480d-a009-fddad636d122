.is-require {
    &::before {
        content: "*";
        color: #F5222D;
        margin-right: 4px;
    }
}

.api {
    &-outer {
        display: flex;
        height: 100%;
        padding: 16px;
        box-sizing: border-box;
    }
    &-left {
        height: 100%;
        flex-shrink: 0;
        width: 45%;
        padding-right: 16px;
        box-sizing: border-box;
        border-right: 1px solid @borderColor;
        &-form {
            height: calc(100% - 2rem);
        }
        &-batch {
            width: 360px;
        }
        &-select {
            width: 220px;
        }
    }

    &-batch {
        &-box {
            max-height: calc(100% - 54px - 2rem);
            min-height:260px;
            margin-bottom: 14px;
            box-sizing: border-box;
            border-radius: 2px;
            padding: 12px;
            border:1px solid @borderColor;
            overflow: auto;
        }
        &-table {
            height: calc(100% - 40px);
        }
    }
    &-title {
        line-height: 40px;
        height: 40px;
        font-weight: bold;
        color: @fontMainColor;
        font-size: 14px;
    }
    &-params-list {
        height: calc(100%  - 8rem - 114px);
        margin-bottom: 14px;
    }
    &-right {
        width: 55%;
        flex-shrink: 0;
        box-sizing: border-box;
        padding-left: 16px;
        &-batch {
            flex: 1;
        }
        &-page {
            height: calc(100% - 4rem - 40px);
            position: relative;
        }
        &-btn {
            &-group {
                position: absolute;
                right: 0;
                top: 10px;
                height: 40px;
                display: flex;
                align-items: center;
                z-index: 99;
            }
        }
        &-tab[class] {
            height: 100%;

            /deep/.el-table__expanded-cell[class*=cell] {
                padding: 6px 0;
            }
        }
        &-respond {
            padding:10px 0 0 0;
            height: calc(100% - 54px);

            &_input {
                height: calc(100% - 40px);

                /deep/.el-textarea__inner{
                    height: 100%;
                }
            }
        }
    }
    &-list {
        &-empty {
            color: @fontDisColor;
            height: 236px;
        }
    }

    &-test {
        &-res {
            height: 240px;
            overflow: auto;
            padding: 5px 12px;
            border: 1px solid rgba(0, 0, 0, 0.25);
            box-sizing: border-box;
        }
        &-deal {
            display: none;
            flex-shrink: 0;
        }
        &-icon {
            cursor: pointer;
            padding: 4px;
        }
        &-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: @fontSecColor;
            height: 36px;
            padding: 0 12px;
            border: 1px solid @borderColor;
            border-radius: 2px;
            cursor: move;
            box-sizing: border-box;
            width: 100%;
            & + & {
                margin-top: 10px;
            }
            &:hover {
                border-color: @sysHoverColor;
                color: @sysMainColor;
                background-color: @bgLightHoverC;
                > .api-test-deal {
                    display: flex;
                }
                > .api-test-txt {
                    width: calc(100% - 44px);
                }
                .api-test-num {
                    color: #fff;
                    background-color: @link-color;
                }
            }
        }
        &-txt {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            width: 100%;
        }
        &-name {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        &-pop {
            max-height: 130px;
            overflow: auto;
            &-item {
                line-height: 24px;
                padding: 0 6px;
                color: @fontDesColor;
            }
        }
        &-num {
            background-color: @boxShadowC;
            padding: 4px 6px;
            border-radius: 4px;
            display: block;
            margin:0 6px;
            line-height: 1;
        }
    }
}
