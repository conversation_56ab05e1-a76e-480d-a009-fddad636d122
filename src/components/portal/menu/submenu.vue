<!-- 
    name: 菜单组件 用于递归渲染
    author: linxp
    time: 2020/10/12
-->
<template>
    <div class="">
        <template v-for="nav in children">
            <el-menu-item v-if="!nav.children || nav.children.length === 0" :index="nav.id" :key="nav.id">{{
                nav.label
            }}</el-menu-item>
            <el-submenu popper-class="reset-submenu" v-else :index="nav.id" :key="nav.id">
                <template slot="title">{{ nav.label }}</template>
                <nav-menu :children="nav.children"></nav-menu>
            </el-submenu>
        </template>
    </div>
</template>
<script>
export default {
    name: "nav-menu",
    props: {
        children: {
            type: Array,
            default() {
                return [];
            }
        }
    }
};
</script>