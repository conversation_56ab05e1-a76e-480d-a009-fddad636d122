<template>
    <div class="file">
        <el-form :model="plug_data"
                 size="mini"
                 label-position="right"
                 :label-width="width"
                 class="attrForm"
        >
            <el-form-item
                    v-for="(attr , i ) in attrsList"
                    :key="i"
                    :label="attr.label + ' :'"
                    :prop="attr.props"
                    :rules="attr.rule"
            >
                <el-input class="ce-attr_item"
                          :placeholder="attr.placeholder"
                          v-if="attr.type ==='input'" v-model="attr.value"
                >
                    <span v-if="attr.suffix" slot="suffix">{{attr.suffix}}</span>
                </el-input>
                <el-select v-if="attr.type === 'select'" class="ce-attr_item" v-model="attr.value"
                           :placeholder="attr.placeholder">
                    <el-option
                            v-for="item in attr.option"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
                <el-switch
                        v-model="attr.value"
                        :active-text="attr.activeTxt"
                        :inactive-text="attr.inactiveTxt"
                        v-if="attr.type === 'switch'">

                </el-switch>
                <div v-if="attr.type === 'radioCustom'">
                    <el-radio  v-model="attr.value" v-for="(radio , inx) in attr.radioOptions" :key="inx" :label="radio.value">
                        <span v-if="radio.type">{{radio.label}}
                            <el-input v-model="radio.value" @change="customChange(radio.value , attr.value)"></el-input>
                        </span>
                        <span v-else>{{radio.label}}</span>
                    </el-radio>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "File",
        computed: {
            plug_data() {
                return {};
            }
        },
        data() {
            return {
                width: '140px',
                attrsList: [
                    {
                        label: '文件路径',
                        type: 'input',
                        value: '',
                        placeholder: '请输入文件路径',
                        props: 'path',
                        rule: [
                            {required: true, message: '请输入文件路径', trigger: 'blur'},
                        ]
                    }, {
                        label: '文件名',
                        type: 'input',
                        value: '',
                        placeholder: '请输入文件名',
                        props: 'name',
                        rule: [
                            {required: true, message: '请输入文件名', trigger: 'blur'},
                        ]
                    },
                    {
                        label: '文件切分方式',
                        type: 'select',
                        value: '',
                        option: [],
                        props: 'cut_way',
                        rule: []
                    },
                    {
                        label: '文件切分值',
                        type: 'input',
                        value: '',
                        placeholder: '请输入文件切分值',
                        props: 'cut_value',
                        rule: [
                            {required: true, message: '请输入文件切分值', trigger: 'blur'},
                        ],
                        suffix: '行/MB'
                    }, {
                        label: '文件后缀',
                        type: 'select',
                        value: '',
                        option: [],
                        props: 'file_suffix',
                        rule: []
                    }, {
                        label: '编码',
                        type: 'select',
                        value: '',
                        option: [],
                        props: 'code',
                        rule: []
                    }, {
                        label: '行标签',
                        type: 'input',
                        value: '',
                        placeholder: '请输入文件名',
                        props: 'row_label',
                        rule: [
                            {required: true, message: '请输入行标签', trigger: 'blur'},
                        ]
                    }, {
                        label: 'csv格式',
                        type: 'select',
                        value: '',
                        option: [],
                        props: 'csv_format',
                        rule: []
                    }, {
                        label: '换行符',
                        type: 'input',
                        value: '',
                        placeholder: '请输入换行符',
                        props: 'linefeed',
                        rule: []
                    },{
                        label: '分隔符',
                        type: 'input',
                        value: '',
                        placeholder: '请输入分隔符',
                        props: 'separate',
                        rule: [
                            {required: true, message: '请输入分隔符', trigger: 'blur'},
                        ]
                    },{
                        label: '是否转义关键字符',
                        type: 'input',
                        value: '',
                        placeholder: '请输入换行符',
                        props: 'unescaped',
                        rule: []
                    },{
                        label: '是否显示标题',
                        type: 'switch',
                        value: false ,
                        activeTxt : '显示' ,
                        inactiveTxt : '隐藏' ,
                        props: 'show_title',
                        rule: []
                    },{
                        label: '是否标识文件',
                        type: 'switch',
                        value: false ,
                        activeTxt : '标识' ,
                        inactiveTxt : '不标识' ,
                        props: 'mark',
                        rule: []
                    }, {
                        label: '参数字段',
                        type: 'select',
                        value: '',
                        option: [],
                        props: 'parameter',
                        rule: []
                    },{
                        label: '是否转义关键字符',
                        type: 'input',
                        value: '',
                        placeholder: '请输入换行符',
                        props: 'unescaped',
                        rule: []
                    }, {
                        label : '行键连接符' ,
                        type: 'radioCustom',
                        value : 'default' ,
                        radioOptions : [
                            {
                                label : '默认' ,
                                value : 'default',
                            },{
                                label : '自定义' ,
                                value : '' ,
                                type : 'input'
                            }
                        ] ,
                        change : this.changeFn
                    }
                ]
            }
        },
        methods :{
            changeFn(value){
                console.log(value)
            },
            customChange( value , groupValue ){
                // console.log(groupValue)
                // groupValue = value;
            }
        }
    }
</script>

<style scoped lang="less">
    @import "../../../plugin/css/attrForm";
</style>