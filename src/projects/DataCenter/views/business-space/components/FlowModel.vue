<!--
流程模块组件
-->
<template>
    <div class="flowModel" :style="{ height: height }">
        <h1 class="flowModel_title">{{ title }}</h1>
        <ul class="flowModel_list"
            :style="{'justify-content' : alignCenter ? 'center' : 'space-between'}"
            :class=" {'flowModel_vertical' : mode === 'vertical'}">
            <template v-for="(flow, inx) in flowList">
                <div class="flowModel_box" :key="inx">
                    <li class="flowModel_item" v-for="(item , i) in flow" :key="i" @click="item.clickFn(item)">
                        <div class="flowModel_icon">
                            <i :class="item.icon"></i>
                        </div>
                        <p class="flowModel_name">{{ item.label }}</p>
                    </li>
                </div>
                <div
                        class="flowModel_arrow"
                        :class="{'flowModel_arrow_vertical' : mode === 'vertical' , 'flowModel_arrow_img' : !noArrowInx.includes(inx)}"
                        :key="inx + 'a'"
                        v-if="inx !== flowList.length - 1"
                ></div>
            </template>
        </ul>
    </div>
</template>

<script>
export default {
    name: "FlowModel",
    props: {
        title: {
            type: String,
            default: "",
        },
        flowList: {
            type: Array,
            default: () => [],
        },
        height: {
            type: String,
            default: "",
        },
        mode : {
            type : String ,
            default : "horizontal" ,//horizontal / vertical
        },
        alignCenter : {
            type : Boolean ,
            default : false ,// small
        },
        noArrowInx : {
            type : Array ,
            default : ()=>[]
        }
    },
};
</script>

<style scoped lang="less">
.flowModel {
    padding: 24px 28px 0;
    border: 1px dashed rgba(0, 136, 255, 0.45);
    border-radius: 4px;
    background: rgba(0, 136, 255, 0.04);
    position: relative;
    box-sizing: border-box;
    &_title {
        font-size: 16px;
        line-height: 32px;
        color: #fff;
        text-align: center;
        background-image: linear-gradient(90deg, #59B1FF 0%, #0088FF 100%);
        border-radius: 2px;
        width: 152px;
        height: 32px;
        position: absolute;
        top: -16px;
        left: 50%;
        margin-left: -76px;
    }

    &_list {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    &_box {
        flex: 1;
        max-width: 198px;
    }

    &_item {
        background: #ffffff;
        box-shadow: 0 0 8px 0 rgba(16, 0, 0, 0.15);
        border-radius: 2px;
        display: flex;
        align-items: center;
        flex: 1;
        margin:24px 0;
        border:1px solid #fff;
        padding: 20px 10px 20px 18px;
        max-height: 96px;
        cursor: pointer;
        &:hover {
            border-color: #0088FF;
            box-shadow: 0 0 8px 0 rgba(0,136,255,0.15);
        }
    }
    &_vertical {
        flex-direction: column;
        height: calc(100% - 18px);
        >.flowModel_box {
            width:100%;
            &:first-child {
                margin-top: 10px;
            }
            >.flowModel_item {
                margin: 0;
            }
        }
    }

    &_icon {
        background: url("../../../assets/images/business/flow-bg.png") no-repeat center;
        background-size: contain;
        color: #fff;
        text-align: center;
        line-height: 54px;
        min-width: 54px;
        width: 54px;
        height: 54px;
        > i {
            font-size: 20px;
        }
    }

    &_name {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        padding: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: calc(100% - 54px);
    }

    &_arrow {
        max-width: 32px;
        width: 32px;
        height: 26px;
        flex: 1;
        margin:0 18px;
        &_vertical {
            max-height: 32px;
            width: 26px;
            height: 32px;
            transform: rotate(90deg);
        }
        &_img {
            background:url("../../../assets/images/business/arrow-small.png") no-repeat center;
        }
    }
}
</style>
