<template>
    <!--  模型市场  -->
    <el-container class="x-main-container layout">
        <el-header class="x-main-header" :style="{'background-color' : $layoutBg }">
            <div class="ce-market_header">
                <div class="x-main-logo">
                    <div class="logo" :style="{'color' : $layoutColor }">{{ marketName }}</div>
                </div>
                <div class="x-main-menu">
                    <div
                            :class="['x-main-menu-item', active[0] === item.path && 'is-active']"
                            @click="handleToSubRoute(item)"
                            v-for="item in topMenus"
                            :key="item.path"
                            :style="{'color' : $layoutColor }"
                    >
                        <i :class="['dg-iconp', item.meta.icon]"></i>
                        <span>{{ item.meta.title }}</span>
                    </div>
                </div>
                <div class="x-main-right ce-user__list" :style="{'color' : $layoutColor }">
                    <li>
                        <div class="ce-link_home" @click="goHome">{{ systemName }}</div>
                    </li>
                    <div>
                        <el-divider direction="vertical"/>
                    </div>
                    <div class="ce-input_item" v-click-out="hideSearchInp">
                        <el-autocomplete
                                class="ce-search"
                                :class="showInp ? 'ce-search_show' : 'ce-search_hide'"
                                ref="search"
                                placeholder="请输入模型名称搜索"
                                v-model.trim="searchTxt"
                                v-input-limit:trim
                                :fetch-suggestions="searchGlobal"
                                @select="selectModel"
                        >
                            <i slot="suffix" class="el-icon-search"></i>
                        </el-autocomplete>
                        <i v-show="!showInp" class="el-icon-search poi" @mouseenter="showSearchInp"></i>
                    </div>
                    <li>
                        <div class="ce-user__img">
                            <i class="icon f18">&#xe690;</i>
                            <span class="ml6">{{ userName }}</span>
                        </div>
                    </li>
                    <li>
                        <a class="ce-logout" title="退出" :style="{'color' : $layoutColor }" href="javascript:void(0);" @click="loginOut">
                            <i class="icon">&#xe6f7;</i>
                        </a>
                    </li>
                </div>
            </div>

        </el-header>
        <el-container class="x-main-body">
            <el-main class="x-main-content">
                <div class="ce-market_contain">
                    <div class="breadcrumb">
                        <el-breadcrumb separator-class="el-icon-arrow-right">
                            <template v-for="v in $route.matched">
                                <el-breadcrumb-item v-if="v.meta.title && v.meta.breadcrumb" :to="{ path: v.redirect }"
                                                    :key="v.path">{{ v.meta.title }}
                                </el-breadcrumb-item>
                                <el-breadcrumb-item v-else-if="v.meta.title && v.path && !v.meta.hidecrumb"
                                                    :key="v.path">{{ v.meta.title }}
                                </el-breadcrumb-item>
                            </template>
                        </el-breadcrumb>
                    </div>
                    <div class="x-main-cont">
                        <router-view/>
                    </div>
                </div>
            </el-main>
        </el-container>

    </el-container>
</template>

<script>
import Breadcrumb from "@/components/layout/breadcrumb";
import {getMarketMenus} from '@/projects/DataCenter/router'
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {mapGetters, mapState} from "vuex";
import {headerMixins} from "@/projects/DataCenter/views/header-mixins/header-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/model-market/service-mixins/service-mixins";
export default {
    name: "Index",
    mixins: [commonMixins, headerMixins , servicesMixins],
    components: {
        Breadcrumb,
    },
    data() {
        return {
            active: [],
            subRoutes: [],
            searchTxt: "",
            showInp: false
        }
    },
    computed: {
        topMenus() {
            let routerData = getMarketMenus();
            return routerData.filter((item) => !item.hidden);
        },
        userName() {
            if (this.userInfo) {
                return this.userInfo.objCode;
            } else {
                return "";
            }
        },
        ...mapGetters(["userInfo"]),
        ...mapState({
            loginWay(state) {
                return state.user.loginWay
            }
        })
    },

    methods: {
        selectModel(row){
            this.searchTxt = "";
            let routeData = this.$router.resolve({path: '/market/modelMarket', query: {id: row.id}});
            window.open(routeData.href, '_blank');
        },
        async searchGlobal(queryString, cb) {
            const vm = this, {
                manageServices,
                manageMock,
            } = this;
            let services = vm.getServices(manageServices, manageMock);
            let params = {
                "objType": "", //传code
                "appType": "",
                "policeType": "",
                "searchType": "name",
                "searchCond": queryString,
                "sortType": "publishTime" ,//focus:关注 browse：浏览 score：评分  publishTime：发布时间
                "sort": "desc" ,
                "pageNum": 1,
                "pageSize": 8
            };

            let {data} = await services.queryModelMarketPage(params);
            if (data.status === 0) {
                let result = data.data.dataList.map(item => {
                    item.value = item.resourceName;
                    return item;
                });
                cb(result);
            }else {
                cb([]);
            }
        },
        hideSearchInp(){
            this.showInp = false;
        },
        showSearchInp() {
            this.showInp = true;
        },
        /**
         * 初始化路由路径页面
         * */
        async init() {
            await this.getRight();
            this.setCurrentTab();
        },
        /**
         * 获取缓存的权限 设置菜单
         * */
        getRight() {
            const vm = this;
            let userRight = vm.$store.getters.userRight;
            if (!userRight) {
                userRight = vm.userRight = JSON.parse(localStorage.getItem("userRight"));
                vm.$store.dispatch("setRight", userRight);
            }
        },
        /**
         * 设置当前 路径高亮tab
         */
        setCurrentTab() {
            let routerData = getMarketMenus();
            const {matched} = this.$route;
            this.active = matched.map((item) => item.path || "/");
            let fitItem = routerData.find((item) => item.path === matched[0].path);
            this.subRoutes = [];
            if (fitItem) {
                this.subRoutes = fitItem.children;
            }
        }
    },

}
</script>
<style lang="less" scoped src="../index.less"></style>
<style scoped lang="less">
.logo {
    line-height: 60px;
    height: 60px;
    padding-left: 40px;
    font-weight: bold;
    font-size: 28px;
    color: #ffffff;
    background: url(../../../../assets/images/header/logo.png) no-repeat left center;
}

.layout {
    width: 100%;
    min-width: 1360px;
}

@width: 96rem;
.ce-market_header {
    width: @width;
    margin: auto;
    display: flex;
}

.ce-market_contain {
    width: @width;
    margin: auto;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
}
.ce-search {
    transition: 600ms;
    overflow: hidden;
    &_show {
        width: 200px;
    }
    &_hide {
        width: 0;
        height: 0;
        /deep/.el-input__inner {
            display: none;
        }
        * {
            display: none;
        }
    }
}
.ce-input_item {
    height: 2rem;
    line-height: 2rem;
}
</style>


