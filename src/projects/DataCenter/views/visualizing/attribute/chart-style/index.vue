<template>
    <div class="chartStyle">
        <span v-for="(item , key) in localForm" :key="key" :class="{'ce-sty_item' : !item.isLine}">
            <el-checkbox class="ce-select_item" v-model="form[key]"
                         v-if="item.type === 'checkbox'">{{ item.label }}</el-checkbox>
            <div v-if="item.type === 'checkbox_block'">
                <el-checkbox class="ce-select_item" v-model="form[key]">{{ item.label }}</el-checkbox>
                <div v-if="item.tip" class="ce-tip pl24">{{ item.tip }}</div>
            </div>
            <el-form class="ce-form__m0" v-if="item.type === 'sty_radio'">
                <el-form-item :label="item.label + ' :'">
                     <el-radio-group v-model="form[key]" v-removeAria>
                        <el-radio v-for="rad in editOpt" :key="rad.value" :label="rad.value">{{ rad.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'style_radio'">
                <el-form-item :label="item.label + ' :'">
                    <div class="ce-radio_with-ico">
                        <div class="ce-radio_item" v-for="rad in radarRadio" :key="rad.value">
                            <svg class="style_icon">
                                <use :xlink:href="rad.icon"></use>
                            </svg>
                            <el-radio v-model="form[key]" :label="rad.value">{{ rad.label }}</el-radio>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'formStyle_radio'">
                <el-form-item :label="item.label + ' :'">
                    <div class="ce-radio_with-ico">
                        <div class="ce-radio_item" v-for="rad in formRadio" :key="rad.value">
                            <svg class="style_icon">
                                <use :xlink:href="rad.icon"></use>
                            </svg>
                            <el-radio v-model="form[key]" :label="rad.value"
                                      @change="formRadioChange($event,form)">{{ rad.label }}</el-radio>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <dg-select class="ce-select_item w100"
                       v-if="item.type === 'select_font' && form[item.relKey || 'custom_style'] === 'custom'"
                       size="mini"
                       v-model="form[key]" :data="fontFamilyOpt"></dg-select>
            <dg-select class="ce-select_item" :class="item.class" v-if="item.type === 'select'"
                       size="mini"
                       v-model="form[key]" :data="item.options"></dg-select>
            <dg-select class="ce-select_item w90"
                       v-if="item.type === 'select_w' && form[item.relKey || 'custom_style'] === 'custom'"
                       size="mini"
                       v-model="form[key]" :data="fontWeightOpt"></dg-select>
            <dg-select v-if="item.type === 'select_size' && form[item.relKey || 'custom_style'] === 'custom'"
                       size="mini"
                       class="ce-select_item w80"
                       filterable
                       allow-create
                       @visible-change="visibleChange(key)"
                       @change="sizeChange($event,key)"
                       v-model="form[key]"
                       :data="fontSizeOpt"
            >
            </dg-select>

            <el-input v-if="item.type === 'input_txt'" size="mini" :maxLength="item.maxlength" v-model="form[key]"
                      :placeholder="item.placeholder"></el-input>
            <el-checkbox-group v-if="item.type === 'checkBtn' && form[item.relKey || 'custom_style'] === 'custom'"
                               v-model="form[key]"
                               class="ce-btn_group ce-select_item">
                 <el-checkbox-button
                     class="ce-mini_btn"
                     v-for="check in textStyleOpt"
                     :key="check.value"
                     :label="check.value"><span :class="check.class">{{ check.label }}</span></el-checkbox-button>
            </el-checkbox-group>
            <div v-if="item.type === 'font_color' && form[item.relKey || 'custom_style'] === 'custom'"
                 class="state_box">
                    <el-button class="ce-mini_btn" plain size="small">
                        <span class="b" :style="{'color' : form[key]}">A</span>
                    </el-button>
                    <span class="tip_color" :style="{'background': form[key]}"></span>
                    <el-color-picker v-model="form[key]" show-alpha class="ce-color"></el-color-picker>
            </div>
            <el-radio-group v-removeAria
                            v-if="item.type === 'font_align' && form[item.relKey || 'custom_style'] === 'custom'"
                            size="mini"
                            v-model="form[key]">
                <el-radio-button class="ce-mini_btn" v-for="rad in textAlignOpt"
                                 :key="rad.value"
                                 :label="rad.value"
                                 :title="rad.label">
                    <em class="iconfont align-font" v-html="rad.html"></em>
                </el-radio-button>
            </el-radio-group>
            <el-form v-if="item.type ==='legend_color'" :label-width="item.width" :label-position="item.pos">
                <el-form-item :label="item.label + ' :'">
                    <dg-radio-group
                        class="mb10"
                        size="mini"
                        v-model="form[key+'_type']"
                        :data="colorOpt"
                        value-name="value"
                        label-name="label"
                        type="button"
                        call-off
                    ></dg-radio-group>
                    <div class="h32">
                        <dg-select v-if="form[key+'_type'] === 'theme'" size="mini" v-model="form[key+'_theme']"
                                   :data="colorTheme"></dg-select>
                        <ul class="ce-node" v-else>
                            <div class="ce-item_name" @click="changePredefine(key , true)">
                                <span>{{ color_add }}</span>
                                <el-color-picker :ref="key+'_p'" v-model="color" size="medium" :predefine="predefine"
                                                 show-alpha class="ce-color ce-color_legend"
                                                 @change="changeColor($event , key , isColorNew , colorInx)"></el-color-picker>
                            </div>
                            <li class="ce-node_item poi" v-for="(c , inx ) in form[key]" :key="inx"
                                @click="changeColorSingle(key , inx)">
                                <span class="ce-color_box" :style="{'background': c}"></span>
                                <i class="el-icon-close ce-node_close" @click.stop="deleteNode(inx , key)"></i>
                            </li>
                        </ul>
                    </div>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type ==='form_select'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item :label="item.label + ':'">
                    <dg-select size="mini" v-model="form[key]" :data="item.options"></dg-select>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type ==='formColumn_select'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item :label="item.label + ':'">
                    <dg-select size="mini" v-model="form[key]" :data="oneColumnOptions"
                               v-if="form.show_style==='oneColumn'"></dg-select>
                    <dg-select size="mini" v-model="form[key]" :data="twoColumnOptions" v-else></dg-select>
                </el-form-item>
            </el-form>
            <el-form size="mini" class="ce-form__m0" v-if="item.type ==='radio_btns'">
                <div v-if="item.title">{{ item.title }}:</div>
                <el-form-item :align="item.align" class="pl10">
                    <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <el-radio-group v-removeAria v-model="form[key]" @change="radioChange($event , key)">
                        <el-radio-button v-for="rad in item.options"
                                         :key="rad.value"
                                         :label="rad.value"
                        >{{ rad.label }}</el-radio-button>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" :class="{'dib' : item.isLine}" v-if="item.type ==='number'">
                <el-form-item>
                    <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <el-input-number class="mr8" size="mini" @change="checkedNumber($event , key , item.min)"
                                     v-if="item.type === 'number'" v-model="form[key]" controls-position="right"
                                     :step="item.step" :min="item.min" :max="item.max"></el-input-number>
                </el-form-item>
            </el-form>

            <el-form class="ce-form__m0" :class="{'dib' : item.isLine}"
                     v-if="item.type ==='maxNumber' && form.max_show">
                <el-form-item>
                    <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <!--<el-input-number class="mr8" size="mini" @change="checkedNumber($event , key , item.min)"
                                     v-if="item.type === 'maxNumber'" v-model="form[key]" controls-position="right"
                                     :step="item.step" :min="item.min" :max="item.max"></el-input-number>-->
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4}"
                        placeholder="请按顺序输入最大值(用' , '隔开)限输入数字"
                        @input="limitInputNumber($event , key)"
                        v-if="item.type === 'maxNumber'" v-model.trim="form[key]"></el-input>
                </el-form-item>
            </el-form>

            <el-form class="ce-form__m0" :class="{'dib' : item.isLine}" v-if="item.type ==='rotate'">
                <el-form-item>
                    <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <el-input-number class="mr8" size="mini" @change="checkedNumber($event , key , item.min)"
                                     v-if="item.type === 'rotate'" v-model="form.startRotate" controls-position="right"
                                     :step="item.step" :min="-90" :max="form.endRotate"></el-input-number>--
                    <el-input-number class="mr8" size="mini" @change="checkedNumber($event , key , item.min)"
                                     v-if="item.type === 'rotate'" v-model="form.endRotate" controls-position="right"
                                     :step="item.step" :min="form.startRotate" :max="90"></el-input-number>
                </el-form-item>
            </el-form>
            <el-color-picker class="ce-select_item vm" size="small" v-if="item.type === 'color_picker'"
                             v-model="form[key]" show-alpha></el-color-picker>
            <el-form class="ce-form__m0" v-if="item.type === 'form_color_picker'">
                <el-form-item class="ce-select_item lh32">
                    <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <el-color-picker class="" size="small" v-model="form[key]"
                                     @change="colorPickerChange($event , item.defaultColor , key)"
                                     show-alpha></el-color-picker>
                </el-form-item>
            </el-form>

            <el-form class="ce-form__m0" v-if="item.type === 'form_input'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item>
                     <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <el-input size="mini" v-model.trim="form[key]" v-input-filter="form[key]"
                              :maxlength="item.maxlength" :placeholder="item.placeholder"></el-input>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'form_textarea'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item>
                     <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <el-input type="textarea"
                              v-model="form[key]"
                              :rows="item.rows"
                              show-word-limit
                              :maxlength="item.maxlength"
                              :placeholder="item.placeholder"></el-input>
                </el-form-item>
            </el-form>
            <div class="grid_box" v-if="item.type === 'number_slider'">
                <span class="grid_label">{{ item.label }} :</span>
                 <dg-radio-group
                     class="grid_btn"
                     size="mini"
                     v-model="form[key+'_type']"
                     :data="gridOpt"
                     value-name="value"
                     label-name="label"
                     type="button"
                     call-off
                     @change="gridChange($event , key)"
                 ></dg-radio-group>
                <el-input-number class="grid_num" size="mini" v-if="form[key+'_type'] === 'abs'"
                                 @change="checkedNumber($event , key , item.min)" v-model="form[key]"
                                 controls-position="right" :step="item.step" :min="item.min"></el-input-number>
                <el-slider class="grid_slider" :format-tooltip="(value)=> value+'%'" v-model="form[key]"
                           v-else></el-slider>
            </div>
            <el-form class="ce-form__m0" v-if="item.type ==='field_select'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item :label="item.label + ':'">
                    <dg-select size="mini" v-model="form[key]" :data="form.options" @change="fieldChange"></dg-select>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type ==='combination_select'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item :label="item.label + ':'">
                    <el-select size="mini" v-model="form[key]" multiple placeholder="请选择">
                        <el-option
                            v-for="opt in form.chooses"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.label">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" size="mini" v-if="item.type === 'show_area' && form.y_show"
                     :label-width="item.width"
                     :label-position="item.pos">
                <!-- <el-form-item :label="item.label + ':'">
                    <dg-select size="mini" v-model="form[key]"  :data="form.chooses" ></dg-select>
                </el-form-item> -->
                <el-form-item :label="item.label + ':'">
                    <el-select v-model="form[key]" multiple placeholder="请选择">
                        <el-option
                            v-for="opt in form.chooses"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.label">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'y_name'&& form.y_show" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item>
                    <span slot="label" v-if="item.label">{{ item.label }}:</span>
                    <el-input size="mini" v-model.trim="form[key]"
                              :maxLength="item.maxlength"
                              :placeholder="item.placeholder"></el-input>
                </el-form-item>
            </el-form>
             <el-form v-if="item.type === 'field_alias'" :label-width="item.width"
                      :label-position="item.pos">
                <el-form-item v-for="field in form.options" :key="field.id">
                     <span slot="label" v-if="item.label">{{ field.label }}:</span>
                    <el-input size="mini" @input="editAlias($event , field.id )" v-model.trim="form[field.code]"
                              v-input-filter="form[field.code]" :maxLength="item.maxlength"
                              :placeholder="item.placeholder"></el-input>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'slider'" :label-width="item.width"
                     :label-position="item.pos">
                <div class="lh30" v-if="item.title">{{ item.title }} :</div>
                <el-form-item>
                     <div class="lh38 pr6" slot="label" v-if="item.label">{{ item.label }}:</div>
                    <dg-radio-group
                        class="grid_btn"
                        size="mini"
                        v-model="form[key+'_type']"
                        :data="gridOpt"
                        value-name="value"
                        label-name="label"
                        type="button"
                        @change="sliderGridChange($event , key)"
                        call-off
                    ></dg-radio-group>
                     <el-input-number class="grid_num" size="mini" v-if="form[key+'_type'] === 'abs'"
                                      @change="checkedNumber($event , key , item.min)" v-model="form[key]"
                                      controls-position="right" :step="item.step" :min="item.min"></el-input-number>
                     <el-slider class="grid_slider_f" v-else :format-tooltip="(value)=> value+'%'"
                                v-model="form[key]"></el-slider>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'form_checkbox'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item>
                     <div slot="label" v-if="item.label">{{ item.label }}:</div>
                      <dg-checkbox-group v-model="form[key]" :data="item.options"></dg-checkbox-group>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'radio_number'">
                <el-form-item>
                     <span slot="label" v-if="item.label">{{ item.label }} :</span>
                    <div>
                         <el-radio-group v-removeAria v-model="form['setSpan']">
                            <el-radio v-for="rad in editOpt" :key="rad.value"
                                      :label="rad.value">{{ rad.label }}</el-radio>
                         </el-radio-group>
                        <span v-if="form['setSpan'] === 'custom'">
                             <el-input-number class="grid_num ml5" size="mini"
                                              @change="checkedNumber($event , key , item.min)" v-model="form[key]"
                                              controls-position="right" :step="item.step"
                                              :min="item.min"></el-input-number>
                        {{ item.unit }}
                        </span>

                    </div>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'num_area' && form.zoomLock">
                <el-form-item>
                     <span slot="label" v-if="item.label">{{ item.label }}:</span>
                    <div>
                        <span>{{ item.start }} :</span>
                         <el-input-number class="grid_num ml5" size="mini"
                                          @change="checkedNumber($event , 'start' , item.min)" v-model="form['start']"
                                          controls-position="right" :step="item.step" :min="item.min"
                                          :max="item.max"></el-input-number>
                         {{ item.unit }}
                         <span class="pl10">{{ item.end }}:</span>
                         <el-input-number class="grid_num ml5" size="mini"
                                          @change="checkedNumber($event , 'end' , item.min)" v-model="form['end']"
                                          controls-position="right" :step="item.step" :min="item.min"
                                          :max="item.max"></el-input-number>
                        {{ item.unit }}
                    </div>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" label-position="top" :inline="true" v-if="item.type === 'color_map'">
                <el-form-item>
                    <span slot="label" v-if="item.label">{{ item.label }}:</span>
                    <div class="ce-form_item" v-if="item.tipMin">
                        <span class="pr10">{{ item.tipMin }} :</span>
                        <el-checkbox v-model="form.minAuto">{{ item.minLabel || item.tipLabel }}</el-checkbox>
                        <el-input-number v-if="!form.minAuto" @change="checkedNumber($event , 'min' , 0)"
                                         v-model="form.min" controls-position="right" :min="0"></el-input-number>
                    </div>
                    <div class="ce-form_item" v-if="item.tipMax">
                        <span class="pr10">{{ item.tipMax }} :</span>
                        <el-checkbox v-model="form.maxAuto">{{ item.maxLabel || item.tipLabel }}</el-checkbox>
                        <el-input-number v-if="!form.maxAuto" @change="checkedNumber($event , 'max' , 0)"
                                         v-model="form.max" controls-position="right" :min="0"></el-input-number>
                    </div>
                    <div class="ce-form_item" v-if="item.splitTip">
                        <div class="pr10 w90">{{ item.splitTip }} :</div>
                        <dg-select v-model="form.splitNumber" :data="splitOpt"></dg-select>
                    </div>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'cascader'" :label-width="item.width"
                     :label-position="item.pos">
                <el-form-item>
                    <span slot="label" v-if="item.label">{{ item.label }}:</span>
                    <el-cascader
                        class="style-cascader"
                        v-model="form[key]"
                        :options="item.options"
                        :props="item.props"
                        :placeholder="item.placeholder"
                        clearable
                        filterable
                    ></el-cascader>
                </el-form-item>
            </el-form>
            <el-form class="ce-form__m0" v-if="item.type === 'form_list'" :inline="item.inLine">
                <div class="lh36">{{ item.title }}:</div>
                <el-form-item v-for="(list , inx) in item.list" :key="inx">
                        <span slot="label" v-if="list">{{ list }}:</span>
                        <el-input v-model="form[inx]"></el-input>
                </el-form-item>
            </el-form>
            <div v-if="item.type === 'checkbox_group_fix'">
                <el-checkbox :indeterminate="form.isIndeterminate" v-model="form.checkAll"
                             @change="fixCheckAllChange($event , key)">{{ item.checkAllText }}</el-checkbox>
                <el-checkbox-group class="check_group-item" v-model="form[key]"
                                   @change="fixCheckedChange($event , key)">
                    <el-checkbox v-for="opt in item.options" :label="opt.value"
                                 :key="opt.value">{{ opt.label }}</el-checkbox>
                    <el-checkbox label="row"><span>{{ item.lastTxt }}</span></el-checkbox>
                    <dg-select v-show="form[key].includes('row')" class="ml5 mr5 check_group-sel"
                               v-model="form.fixedRow" multiple collapse-tags clearable
                               :data="item.rowOptions"></dg-select>
                </el-checkbox-group>
            </div>
            <div v-if="item.type ==='checkbox_page'">
                <el-checkbox v-model="form[key]">{{ item.label }}</el-checkbox>
                <dg-select class="mr5 w80" v-model="form.pageSize" :data="item.options"></dg-select>
                <span>{{ item.lastTxt }}</span>
            </div>
            <el-form class="ce-form__m0" v-if="item.type === 'form_radio'" :label-position="item.pos"
                     :label-width="item.width">
                 <el-form-item>
                     <span slot="label" v-if="item.label">{{ item.label }} :</span>
                     <div class="pl45">
                          <dg-radio-group
                              size="mini"
                              v-model="form[key]"
                              :data="item.options"
                              value-name="value"
                              label-name="label"
                              call-off
                          ></dg-radio-group>
                         <el-input-number class="ce-custom_input"
                                          @change="checkedNumber($event , 'customH' , 0)"
                                          v-if="item.isCustom && form[key] === 'custom'"
                                          :min="item.min"
                                          v-model="form.customH" controls-position="right"></el-input-number>
                     </div>

                </el-form-item>
            </el-form>
            <div v-if="item.type === 'tab_list'">
                <el-button type="primary" size="mini" @click="addTabList">{{ item.buttonTxt }}</el-button>
                <transition-group name="list-complete" tag="ul">
                    <ul class="ce-tab_cont list-complete-item" v-for="(list , k) in form[key]" :key="list.value">
                        <li class="ce-tab_title">
                            <div class="ell">{{ list.inxT }}{{ k + 1 }}</div>
                            <div class="ce-tab_btns">
                                <el-button type="text" v-if="k" title="上移" icon="el-icon-top"
                                           @click="upGo(form[key] , k)"></el-button>
                                <el-button type="text" v-if="k !== form[key].length -1" title="下移"
                                           icon="el-icon-bottom"
                                           @click="downGo(form[key] , k)"></el-button>
                                <el-button type="text" title="删除" icon="el-icon-delete"
                                           @click="deleteTabs(k , list)"></el-button>
                            </div>
                        </li>
                        <li class="ce-tab_tip">{{ list.tip }}</li>
                        <li class="ce-tab_name">
                            <el-input v-model.trim="list.label" clearable></el-input>
                        </li>
                    </ul>
                </transition-group>
            </div>
            <div v-if="item.type === 'map_code'">
                 <el-form class="ce-form" :label-width="item.width"
                          :label-position="item.pos">
                        <el-form-item>
                            <span slot="label" v-if="item.label">{{ item.label }}:</span>
                            <dg-select size="mini" v-model="form.map_type" :data="item.type_options"
                                       @change="mapTypeChange"></dg-select>
                        </el-form-item>
                         <el-form-item>
                             <span slot="label" v-if="item.sec_label">{{ item.sec_label }}:</span>
                             <dg-select v-if="form.map_type === 'country'" size="mini" v-model="form[key]"
                                        :data="item.sec_options"></dg-select>
                             <el-cascader
                                 v-else
                                 style="width:100%;"
                                 v-model="form[key]"
                                 :options="item.options"
                                 :props="item.props"
                                 :placeholder="item.placeholder"
                                 clearable
                                 filterable
                             ></el-cascader>
                         </el-form-item>
                    </el-form>
            </div>
            <div v-if="item.type === 'attr_title'">
                <div>{{ item.title }}</div>
            </div>
            <div v-if="item.type === 'mark_condition'">
              <el-row :gutter="6" class="mt8" v-for="(con , inx) in form.condition" :key="inx">
                    <el-col :span="2"><div class="tr lh40">{{ markCondition.tip }}</div></el-col>
                    <el-col :span="19">
                        <ul class="ce-condition_box">
                            <li class="ce-condition_item"><div class="ell ce-logic_f"
                                                               :title="markLabel">{{ markLabel }}</div></li>
                            <li class="ce-condition_item">
                              <dg-select class="w60" v-model="con.logic" :data="item.options"></dg-select>
                            </li>
                            <li class="ce-condition_item">
                                <el-input clearable class="ce-logic_w" v-model.trim="con.con_value"
                                          v-input-limit:trim></el-input>
                            </li>
                          <span @click="getPredefine">
                            <el-color-picker v-model="con.color" :predefine="predefine" show-alpha
                                             class="ml8"></el-color-picker>
                          </span>
                        </ul>
                    </el-col>
                    <el-col :span="3">
                        <el-button type="text" icon="el-icon-plus" :title="tip.plus" size="medium"
                                   @click="addCondition('condition')"></el-button>
                         <el-popconfirm
                             size="mini"
                             title="确认删除该条件?"
                             @confirm="removeCondition(inx , 'condition' ,'useCondition')"
                         >
                             <el-button slot="reference" type="text" icon="el-icon-minus" :title="tip.minus"
                                        size="medium"></el-button>
                    </el-popconfirm>

                    </el-col>
                </el-row>
            </div>
          <el-checkbox class="ce-select_item" v-model="form[key]"
                       v-if="item.type === 'mark_checkbox'"
                       @change="useConditionChange($event , 'condition')">{{ item.label }}</el-checkbox>
            <template v-if="item.type === 'widthSetting'">
                <div class="lh36 b">{{ item.label }} : </div>
                <el-row class="lh30 mb8" :gutter="10" v-for="(col, i) in item.columns" :key="i">
                    <el-col class="width-setting-col" :span="8">{{ col.label }}:</el-col>
                    <el-col :span="6">
                        <dg-select v-model="form[key][col.code].prop" :data="item.options"></dg-select>
                    </el-col>
                    <el-col :span="10">
                        <el-input-number
                            v-model="form[key][col.code].value"
                            @change="numberChange($event, form[key][col.code])"
                            controls-position="right"
                        ></el-input-number>
                    </el-col>
                </el-row>
            </template>
        </span>
    </div>
</template>

<script lang="js" src="./chart-style.js"></script>
<style scoped lang="less" src="./chart-style.less"></style>
<style scoped lang="less" src="../css/attr.less"></style>
<!--
  使用说明
  根据type 使用 (新增类型时，添加下使用在什么情景说明)

  mark_condition : 表格 颜色条件配置,
  mark_checkbox : 表格 颜色条件配置，change事件 若 没有条件length === 0 勾选"启用条件格式"，则添加一条空条件
-->
