<template>
    <div class="ce-list" v-loading="settings.loading">
        <div class="ce-list_filter">
            <el-radio-group v-model="activeN" @change="tabChange">
                <el-radio-button v-for="(item, key) in tabData" :key="key" :label="item.value" :value="item.value">{{
        item.label }}</el-radio-button>
            </el-radio-group>
            <div>
                <el-input class="ce-list_input" size="mini" placeholder="请输入模型名称查询" v-model.trim="filterTxt"
                    v-input-limit:trim @keyup.enter.native="changePage(1)">
                    <i class="el-icon-search el-input__icon poi" slot="suffix" @click="changePage(1)">
                    </i>
                </el-input>
            </div>
        </div>
        <div class="ce-list_table">
            <common-table v-show="tableData.length" :data="tableData" :columns="tableColumns" :border="false"
                :pagination-props="paginationProps" :pagination-total="totalCount"
                :paging-type="isMock ? 'client' : 'server'" noneImg :max-height="tableBodyH"
                @change-current="isMock ? () => { } : changePage($event)" @change-size="changeSize">
                <template slot="operate" slot-scope="{row}">
                    <span class="r-c-action" v-for="(opt, inx) in operator" :key="inx"
                        v-if="opt.condition(row, rights)">
                        <dg-button type="text" :title="opt.label" :disabled="opt.disabledFn(row)"
                            @click="opt.clickFn(row)">{{ opt.label }}</dg-button>
                    </span>
                </template>
                <template #auditState="{ row }">
                    <span>{{ auditState(row.audit_state) }}</span>
                </template>
            </common-table>
            <no-data v-if="tableData.length === 0" :src="emptyImg" :title="emptyTxt" top="5%">
            </no-data>
        </div>

        <publish ref="publish" show-apply :type="activeN" @refresh="changePage(1)"></publish>
    </div>
</template>

<script>
import { common } from "@/api/commonMethods/common";
import { commonMixins } from "@/api/commonMethods/common-mixins"
import emptyImg from "@/assets/images/common/empty-img.png";
import NoData from "@/components/no-data";
import { coustTableH } from "@/api/commonMethods/count-table-h";
import $right from "@/assets/data/right-data/right-data";
import { servicesMixins } from "../../service-mixins/service-mixins";
import { mapGetters } from "vuex";
import publish from "../../dialog/publish"
import marketUse from "@store/modules/market/market-constant"
export default {
    name: "releasedList",
    mixins: [common, coustTableH, servicesMixins, commonMixins, marketUse],
    components: {
        NoData,
        publish
    },
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        auditState() {
            return (val) => {
                let state = this.m_market_list_audit_state.find(it => it.value === val);
                return state && state.label;
            }
        },
    },
    props: {
        tabData: Array
    },
    data() {
        return {
            states: [
                { type: 'none', text: '暂无状态', icon: 'icon icon-state' },
                { type: 'success', text: '任务完成', icon: 'icon icon-state' },
                { type: 'error', text: '异常', icon: 'icon icon-state' },
                { type: 'ongoing', text: '执行中', icon: 'el-icon-loading' },
            ],
            filterTxt: "",
            shareData: [],
            pageSizes: [],
            totalCount: 0,
            activeN: this.tabData.length ? this.tabData[0].value : "",
            emptyImg,
            emptyTxt: "暂无待发布模型",
            tableData: [],
            //操作栏
            operator: [
                {
                    label: '发布',
                    clickFn: this.onPublish,
                    condition: (row, right) => true,
                    disabledFn: (row) => !this.m_market_has_audit(row.audit_state),
                    //condition: (row, right) => right.indexOf($right["roleManagementUpdataRole"]) > -1,
                }
            ],
            tableColumns: [
                {
                    prop: "name",
                    label: "模型名称",
                    align: "left"
                },
                {
                    prop: "path",
                    label: "所属目录",
                    align: "left"
                }, {
                    prop: "userName",
                    label: "创建人",
                    align: "center"
                },
                {
                    prop: "auditState",
                    label: "状态",
                    align: "center"
                }, {
                    prop: "releasetime",
                    label: "更新时间",
                    align: "center"
                },
                {
                    prop: "operate",
                    label: "操作",
                    width: "120",
                    align: "center",
                }
            ],
        }
    },
    methods: {
        //发布
        onPublish(row) {
            row.transId = row.id;
            this.$refs.publish.show('add', row);
        },
        changePage(index) {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            vm.tableData = [];
            let data = {
                type: vm.activeN,
                pageNum: index,
                pageSize: vm.paginationProps.pageSize,
                condition: vm.filterTxt
            }
            services.unPublishModelList(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.tableData = res.data.data.dataList;
                    vm.totalCount = res.data.data.totalCount;

                }
            })
        },

        tabChange(val, tab) {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
    },
    mounted() {
        this.triggerEvent('resize', 300);
    },
    created() {
        this.changePage(1);
    },
}
</script>

<style scoped lang="less">
.red {
    color: red;
}

.alertClass {
    width: 700px;
}

.ce-list {
    height: 100%;

    .r-c-action:last-child::after {
        width: 0;
    }

    &_filter {
        height: 32px;
        text-align: right;
        padding: 10px 0;
        display: flex;
        justify-content: space-between;
    }

    &_input {
        width: 270px;
        margin-left: 10px;
    }

    &_table {
        overflow: hidden;
        height: calc(100% - 52px);

        .ce-table-h__auto {
            overflow-y: hidden;
        }
    }

    &_com {
        width: 320px;
        margin-left: 10px;
    }

    &_preSel {
        width: 100px;
    }
}
</style>
