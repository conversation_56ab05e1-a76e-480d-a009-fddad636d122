<!--日志详情-->
<template>
    <div class="detail">
        <el-form label-position="right" label-width="130px">
            <el-row>
                <el-col v-for="(item , k) in form" :key="k" :span="item.span" v-show="item.show(row)">
                    <el-form-item>
                        <span class="ce-detail_tip" slot="label">{{item.label}}</span>
                        <div class="ce-detail_value">
                            <el-tooltip class="item" :content="row[k]" placement="bottom-start" effect="light">
                                <div>{{row[k]}}</div>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-button v-footer @click="close">{{closeBtn}}</el-button>
    </div>
</template>

<script>
export default {
    name: "detail",
    props : {
        row : Object,
        type:String,
    },
    data(){
        return{
            closeBtn : "关 闭" ,
            layoutForm : {
                id : {
                    label : "记录标识:" ,
                    span : 12 ,
                    show : ()=>true
                },
                certificateNumber : {
                    label : "用户标识:" ,
                    span : 12,
                    show : ()=>true
                },
                userName : {
                    label : "用户名:" ,
                    span : 12,
                    show : ()=>true
                },
                /*unit : {
                    label : "单位名称:" ,
                    span : 12,
                    show : ()=>true
                },*/
                operateTime : {
                    label : "操作时间:" ,
                    span : 12,
                    show : ()=>true
                },ipAddress : {
                    label : "终端标识:" ,
                    span : 12,
                    show : ()=>true
                },operateType : {
                    label : "操作类型:" ,
                    span : 12,
                    show : ()=>true
                },
                stateName : {
                    label : "操作结果:" ,
                    span : 12,
                    show : ()=>true
                },
                message : {
                    label : "失败原因:" ,
                    span : 24,
                    show : (row)=> row.state === 'fail'
                },
                modelName : {
                    label : "功能模块:" ,
                    span : 24,
                    show : ()=>true
                },
                operateCondition : {
                    label : "操作条件:" ,
                    span : 24,
                    show : ()=>true
                },
            },
            serviceForm: {
                numId : {
                    label : "记录标识:" ,
                    span : 12 ,
                    show : ()=>true
                },
                regId : {
                    label : "应用系统标识:" ,
                    span : 12,
                    show : ()=>true
                },
                interfaceName : {
                    label : "接口名称:" ,
                    span : 12,
                    show : ()=>true
                },
                requester : {
                    label : "请求名称:" ,
                    span : 12,
                    show : ()=>true
                },
                userId : {
                    label : "用户标识:" ,
                    span : 12,
                    show : ()=>true
                },
                organization : {
                    label : "单位名称:" ,
                    span : 12,
                    show : ()=>true
                },
                organizationId : {
                    label : "单位机构代码:" ,
                    span : 12,
                    show : ()=>true
                },
                userName : {
                    label : "用户姓名:" ,
                    span : 12,
                    show : ()=>true
                },
                startTime : {
                    label : "接口服务开始时间:" ,
                    span : 12,
                    show : ()=>true
                },
                endTime : {
                    label : "接口服务结束时间:" ,
                    span : 12,
                    show : ()=>true
                },
                terminalId : {
                    label : "终端标识:" ,
                    span : 12,
                    show : ()=>true
                },
                stateName : {
                    label : "接口服务结果:" ,
                    span : 12,
                    show : ()=>true
                },
                resultCount : {
                    label : "结果返回数量:" ,
                    span : 12,
                    show : ()=>true
                },
                errorCode : {
                    label : "失败原因代码:" ,
                    span : 24,
                    show : ()=>true
                },
                interfaceCondition : {
                    label : "接口服务条件:" ,
                    span : 24,
                    show : ()=>true
                },
            },
            form: {},
        }
    },
    mounted(){
        this.init();
    },
    methods : {
        init(){
            this.form = this.type === 'service' ? this.serviceForm : this.layoutForm;
            for(let key in this.form){
                this.row[key] = this.row[key] === 0 || this.row[key] ? this.row[key].toString() : '';
            }
        },
        close (){
            this.$emit("close");
        }
    }
}
</script>

<style scoped lang="less">
    .ce-detail {
        &_tip {
            color: rgba(0,0,0,.45);
        }
        &_value {
            color: rgba(0,0,0,.65);
            div{
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
</style>
