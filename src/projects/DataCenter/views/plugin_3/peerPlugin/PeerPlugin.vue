<template>
    <div class="classificationCounting ce-plug_cont" v-loading="loading">
        <div class="ce-tab_cont">

            <div class="ce-plug_attr">
                <el-row :gutter="5" class="ce-row-item">
                    <el-col class="ce-normal_item ce-normal_head is-require" :span="7">
                        <span>时间字段: </span>
                    </el-col>
                    <el-col :span="17">
                        <el-select
                            class="mr10"
                            size="mini"
                            v-model="timeFieldName"
                            placeholder="请选择"
                            filterable
                            clearable
                            collapse-tags
                        >
                            <el-option
                                v-for="item in columns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-row>
                <el-row :gutter="5" class="ce-row-item">
                    <el-col class="ce-normal_item ce-normal_head is-require" :span="7">
                        <span>条件字段: </span>
                    </el-col>
                    <el-col :span="17">
                        <el-select
                            class="mr10"
                            size="mini"
                            v-model="groupFieldName"
                            placeholder="请选择"
                            filterable
                            multiple
                            clearable
                            collapse-tags
                            @change="groupFiledChange"
                        >
                            <el-option
                                v-for="item in columns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="ce-tab_cont">
            <div class="ce-plug_attr" v-show="false">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <!-- <div class="attr_btn">
            <el-button
                v-for="(btn , inx) in buttons"
                :key="inx"
                size="mini"
                :type="btn.type"
                @click="btn.clickFn"
            >{{ btn.name }}
            </el-button>
        </div> -->
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {pluginMixins} from "@/projects/DataCenter/views/plugin/component/plugin-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import SettingSenior from "@/projects/DataCenter/views/plugin/component/SettingSenior"
import {servicesMixins} from "../service-mixins/service-mixins"

export default {
    name: "PeerPlugin",
    components: {
        PreView,
        SettingSenior,
    },
    mixins: [pluginMixins, commonMixins, servicesMixins],
    props: {
        sourceData: Object,
        row: Object
    },
    data() {
        return {
            isSeniorShow: {},
            exps: [],
            loading: false,
            dataTypes: ['Float', 'Double', 'BigDecimal'],
            buttons: [
                {
                    name: '保存',
                    clickFn: this.saveFn,
                    type: 'primary'
                }, {
                    name: '预览',
                    clickFn: this.previewFn,
                    type: '',
                }
            ],
            status: false,

            dataDispose: "",//数据处理表达式输入框绑定的数据
            inputType: [],
            rowData: '',
            timeFieldName: "",
            groupFieldName: [],
            partitionFieldName: [],
            columns: []
        }
    },
    methods: {
        /**
         * 条件字段修改
         * **/
        groupFiledChange(val){
            this.setPartitionColumns();
            this.$refs.settingSenior.removeValue(val);
        },
        saveRowData() {
            this.dataDispose = this.rowData.serviceOrgId;
        },
        init() {
            const vm = this, {services} = vm;
            this.stepId = this.sourceData.id;
            services.getPeerView(vm.stepId).then(res => {
                if (res.data.status === 0) {
                    let pluginMeta = res.data.data.pluginMeta;
                    let columns = res.data.data && res.data.data.column;

                    //初始化页面数据
                    vm.timeFieldName = pluginMeta.timeField || "";
                    if (pluginMeta.partitionFileds !== null && pluginMeta.partitionFileds !== undefined) {
                        pluginMeta.partitionFileds.forEach(item => {
                            vm.partitionFieldName.push(item.fieldCode)
                        });
                    }
                    if (pluginMeta.groupByFields !== null && pluginMeta.groupByFields !== undefined) {
                        pluginMeta.groupByFields.forEach(item => {
                            vm.groupFieldName.push(item.fieldCode);
                        });
                    }
                    vm.$nextTick(()=>{
                        //初始化输入字段
                        vm.columns = vm.$refs.settingSenior.setAllColumns(columns);
                        vm.$refs.settingSenior.setPartitionName(vm.partitionFieldName);
                        vm.setPartitionColumns();
                    })
                }
            })
        },
        /**
         * 设置分区字段选项
         */
        setPartitionColumns(){
            let columns = this.columns.filter(col => this.groupFieldName.indexOf(col.code) > -1);
            this.$refs.settingSenior.setPartitionColumns(columns);
        },
        // 分组统计删除操作
        deleteClassificationData(row, index) {
            this.classificationFormData.splice(index, 1);
        },
        preview() {
            this.$refs.preview.show(this.row, this.sourceData);
        },
        save(saveName,showTip) {
            const vm = this , {services} = vm;
            // vm.resetSenior();
            let pluginMeta = {};
            if (!vm.timeFieldName) {
                vm.$message.error("请选择时间字段！");
                return;
            }
            vm.partitionFieldName = vm.$refs.settingSenior.partitionFieldName;
            if ( vm.groupFieldName.length <= 0 ) {
                vm.$message.error("请选择条件字段！");
                return;
            }
            pluginMeta['timeField'] = vm.timeFieldName;
            // fieldCode
            let partitionFieldName = [];
            let groupFieldName = [];
            vm.partitionFieldName.forEach(item => {
                partitionFieldName.push({
                    "fieldCode": item,
                    "fieldName": item
                });
            });
            vm.groupFieldName.forEach(item => {
                groupFieldName.push({
                    "fieldCode": item,
                    "fieldName": item
                });
            });
            pluginMeta['partitionFileds'] = partitionFieldName;
            pluginMeta['groupByFields'] = groupFieldName;
            vm.loading = true;
            services.savePeer(vm.stepId, pluginMeta , vm).then(res => {
                if (res.data.status === 0) {
                    showTip && vm.$message.success("保存成功")
                    if(saveName) saveName();
                    vm.$emit("setPreviewAble");
                }
            })
        },
        initSettingSenior() {
            this.$refs.settingSenior.initValue();
        },
        saveColumn() {
            this.$refs.settingSenior.saveFieldTable();
        },
        checkDataType(val) {
            if (this.dataTypes.indexOf(val) > -1) {
                return true;
            } else {
                return false;
            }
        },
    },
    created() {
        this.init();
    }
}
</script>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped>
.countingRadio {
    width: 100%;
    text-align: center;
}

.countingContent {
    margin-top: 10px;
}

.countingSelect {
    height: 5px;
}

.btn_new_model {
    float: right;
    margin-bottom: 5px;
    margin-top: -27px;
    margin-left: 3px;
}
</style>
