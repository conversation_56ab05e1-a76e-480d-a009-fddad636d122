import chartStyle from "../../chart-style"
import {globalBus} from "@/api/globalBus";
export default {
    name: "styleList",
    components : {
        chartStyle
    },
    data(){
        return {
            setData:false ,
            activeTabs :["title"],
            styleList : {
                title : {
                    label :"基础设置",
                    settingForm : {
                        show : {
                            label : "显示标题" ,
                            type : "checkbox",
                        },
                        custom_style : {
                            label : "字体样式" ,
                            type : "sty_radio"
                        },
                        fontFamily : {
                            type : "select_font",
                            isLine : true
                        },
                        fontSize :{
                            type : "select_size" ,
                            isLine : true
                        },
                        fontWeight : {
                            type : "select_w",
                            isLine : true
                        },
                        fontStyle : {
                            type : "checkBtn" ,
                            isLine : true
                        },
                        color : {
                            type : "font_color" ,
                            isLine : true
                        },
                        align :{
                            type :"font_align" ,
                            isLine : true
                        },
                        text : {
                            type : "form_input" ,
                            pos : "right" ,
                            label : "控件名称" ,
                            maxlength :30 ,
                            width : "76px",
                            placeholder :"请輸入控件名称(限30个字符)"
                        },
                        hideSearchBtn : {
                            type : "checkbox_block" ,
                            label : "隐藏查询按钮" ,
                            tip : "谨慎开启，开启后每次操作都将发起筛选"
                        },
                        showClearBtn : {
                            type : "checkbox" ,
                            label : "显示清空按钮"
                        },
                        contLayout : {
                            label :  "内容排版" ,
                            type : "radio_btns" ,
                            options : [
                                {label : "左侧对齐" ,value : "left"},
                                {label : "居中对齐" ,value : "center"},
                            ]
                        }
                    },
                    form : {
                        show : false,
                        custom_style : "default",
                        fontFamily : "sans-serif",
                        fontSize : "12px",
                        fontWeight : "normal",
                        fontStyle : [],
                        color : "#666" ,
                        text : "" ,
                        align : "center",
                        hideSearchBtn : false ,
                        showClearBtn : false ,
                        contLayout: "left"
                    }
                }
            },
            emitStyle :{}
        }
    },
    methods : {
        styleRenew(val , key){
            const vm = this;
            vm.emitStyle[key] = val;
            if(vm.setData){
                vm.$emit('nodeDataRenew');
            }
        },
        getAllStyle(){
            const {emitStyle} = this;
            let styles = {};
            for(let k in emitStyle){
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        initValue(node){
            const vm = this ;
            vm.$nextTick(()=>{
                vm.setData = false;
                vm.styleList.title.form.text = node.label;
                if (!node.widget) return;
                let {style} = JSON.parse(JSON.parse(node.widget).beforeData) ;
                let {title} = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                vm.setData = true;
            })

        }
    },
}