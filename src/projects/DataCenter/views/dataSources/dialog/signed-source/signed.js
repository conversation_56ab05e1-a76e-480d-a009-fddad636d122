import {servicesMixins} from "../../service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {treeMethods} from "@/api/treeNameCheck/treeName"
export default {
    name: "signed" ,
    mixins : [commonMixins , servicesMixins ]  ,
    props :{
        nodeData :Object ,
        treeNode :Object ,
    },
    data(){
        return {
            form :{
                baseType : "POSTGRESQL" ,
                baseInstance : "",
                site : this.nodeData.id ,
                name : "",
                schemaId : "",
                softwareId :"" ,
                nodeData : this.nodeData ,
                treeNode : this.treeNode ,
            } ,
            form_local : {
                dataSourceName : {
                    label : "数据源名称",
                    type : "input",
                    placeholder: "请输入数据源名称",
                },
                baseType : {
                    label : "数据库类型",
                    type : "select",
                    options : [
                        {
                            label:"POSTGRESQL" ,
                            value:"POSTGRESQL"
                        },{
                            label:"GREENPLUM" ,
                            value:"GREENPLUM"
                        },{
                            label:"OR<PERSON><PERSON>" ,
                            value:"ORACLE"
                        },{
                            label:"Elasticsearch" ,
                            value:"Elasticsearch"
                        },{
                            label:"Hive" ,
                            value:"Hive"
                        }, {
                            label: "hbase",
                            value: "hbase"
                        },
                        // },{
                        //     label:"Gbase" ,
                        //     value:"Gbase"
                        // },
                        // {
                        //     label:"zk" ,
                        //     value:"zk"
                        // },{
                        //     label:"ftp" ,
                        //     value:"ftp"
                        // },
                        {
                            label:"libra" ,
                            value:"hwmpp"
                        },
                    ],
                    changeFn : this.getBaseI
                },
                baseInstance : {
                    label : "数据库实例",
                    type : "selected",
                    options : [],
                    changeFn : this.getSourceData
                },
                site : {
                    label :"目录位置" ,
                    type : "select_drop",
                    options :[],
                    changeFn : this.siteChange
                }
            } ,
            rules : {
                baseType : [
                    {required : true ,message : "数据库类型不能为空" , trigger : ["blur","change"]}
                ],
                baseInstance : [
                    {required : true ,message : "数据库实例不能为空" , trigger : ["blur","change"]}
                ],
                site : [
                    {required : true ,message : "目录位置" , trigger : ["blur" , "change"]}
                ],
                dataSourceName : [
                    {required : true ,message : "数据源名称不能为空" , trigger : ["blur" , "change"]}
                ],
            } ,
            width : "160px" ,
            treeBind : {
                "default-expand-all" : true ,
            },
            defaultProps: {
                value: 'id',
                label: 'label',
                children: 'children',
                title: 'title'
            },
        }
    },
    methods : {
        filterNode : treeMethods.methods.filterNode ,
        siteChange(data , node){
            const { form } = this;
            form.nodeData = data;
            form.treeNode = node;
        },
        getSourceData(val){
            let data = {};
            for(let i = 0; i < this.form_local.baseInstance.options.length; i++) {
                if(val === this.form_local.baseInstance.options[i].id) {
                    data = this.form_local.baseInstance.options[i];
                }
            }
            const {form} = this;
            form.name = data.name;
            form.schemaId = val;
            form.softwareId = data.ownerId;
        },
        getResult(){
            return this.form;
        },
        validate(...arg){
            this.$refs.form.validate(...arg);
        },
        getBaseI(val){
            const vm = this;let {listApi , listMock ,form} = this;
            let services = vm.getServices(listApi , listMock);
            form.baseInstance = "";
            services.getDataSetByDataSetType(val).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    vm.form_local.baseInstance.options = [];
                    if(!result) return;
                    vm.form_local.baseInstance.options = res.data.data.map(item => {
                        item.label = item.name;
                        item.value = item.id;
                        item.title = '(数据库名:'+item.name+') (ip地址:'+item.ip+') (端口号为:'+item.port+')';
                        return item;
                    });
                    if(res.data.data && res.data.data.length){
                        form.baseInstance = res.data.data[0].id;
                        form.name = res.data.data[0].name;
                        form.schemaId = res.data.data[0].id;
                        form.softwareId = res.data.data[0].ownerId;
                    }
                }
            })

        },
        filterInstance(data){
            const vm = this;
            return data.map(item => {
                if(item.level < 2){
                    if(item.level === 1){
                        item.disabled = true;
                    }
                    item.children = vm.filterInstance(item.children);
                } else {
                    item.children = [];
                }
                return item;
            })
        },
        getTree(){
            const vm = this;let {listApi , listMock} = this;
            let services = vm.getServices(listApi , listMock);
            services.queryDirTree().then(res => {
                if(res.data.status === 0){
                    vm.form_local.site.options = vm.filterInstance(res.data.data);
                }
            })
        }
    },
    created(){
        this.getBaseI(this.form.baseType);
        this.getTree()
    }
}