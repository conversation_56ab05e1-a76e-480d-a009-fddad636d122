/**
 * 流程操作 接口
 * @Author: wangjt
 * @Date: 2020-05-14
 * @Project cicada-dataCenter-webui
 */

import * as reqUtil from '@/api/reqUtil'
const root = "/transSchedule/";

/*
*  保存
 * @param
 * @returns {Promise<*>}
* */
export async function updateTrans( params ) {
    return await reqUtil.post({
        url: root + 'updateTrans',
    }, params );
}
/*
*  保存为模板
 * @param transId
 * @returns {Promise<*>}
* */
export async function save( transId ) {
    return await reqUtil.get({
        url: '/template/save',
    }, {transId} );
}