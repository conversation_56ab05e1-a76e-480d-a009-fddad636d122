import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../../service-mixins/service-mixins";

import headerTabs from "./header-tabs"
export default {
    name: "merge" ,
    mixins : [ dialog , commonMixins , servicesMixins],
    components : {
        headerTabs
    },
    data() {
        return {
            title : "合并数据集" ,
            width : "1000px" ,
            datasetId :"" ,
            canEdit : true ,
            nodeData :{},
            history :[]
        }
    },
    methods : {
        /**
         * 获取历史记录
         * @returns {Promise<void>}
         */
        async getDataSetStepRelation(){
            const vm = this, {datasetServices, datasetMock ,datasetId} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            await services.getDataSetStepRelation(datasetId).then(res => {
                if(res.data.status === 0){
                    vm.history = res.data.data;
                }
            })
        },
        /**
         *
         * @param datasetId
         * @returns {Promise<void>}
         */
        async show(datasetId) {
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.datasetId = datasetId;
            vm.canEdit = true;
            vm.nodeData = {};
            vm.title = "合并数据集";
            await vm.getDataSetStepRelation();
            let tabName = "join";
            for(let item of vm.history.reverse()){
                if(item.code === "unionTable"){
                    tabName = "union";
                    break;
                }
            }
            vm.$nextTick(() => {
                vm.$refs.join.showPage(tabName);
            })
        },
        stepShow(data){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.datasetId = data.result.dataSetId;

            vm.canEdit = true;
            vm.title = "合并数据集";
            vm.$nextTick(()=> {
                vm.$refs.join.showPage('union',data);
            })
        },
        showHistory(datasetId , data , type){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.nodeData = data;
            vm.datasetId = datasetId;
            vm.canEdit = data.canEdit;
            vm.title = "合并数据集(轨迹步骤)";
            vm.$nextTick(()=> {
                vm.$refs.join.showPage(type , data);
            })
        },
        submit(){
            this.$refs.join.submit();
        },
        setLabel(data , id){
            return data.map(it => {
                it.label = it.name || it.code;
                it.belongDataSetId = id;
                return it;
            })
        },
        setFields(items){
            const vm = this , [columns] = items.columns;
            let measure = vm.setLabel(columns.measure , columns.id )  , dimension = vm.setLabel(columns.dimension , columns.id) ;
            return {dimension , measure , id : columns.id , code : columns.code , name : columns.name};
        },
        join(data , id){
            const vm = this , {datasetServices , datasetMock ,settings ,nodeData }  = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;
            if(nodeData.step === true){
                let params = {
                    dataSetId: data.dataSetId ,
                    stepId : nodeData.stepId ,
                    joinTableStep : data
                };
                services.editDataStep(params ,settings).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("步骤编辑成功");
                        vm.$emit("mergeSuccess");
                        vm.visible = false;
                    }
                })
            }else {
                services.join(data , settings).then(res => {
                    if(res.data.status === 0){
                        let params =  res.data.data ? vm.setFields(res.data.data) : res.data.data;
                        vm.$emit("mergeSuccess" , params);
                        vm.$message.success("合并成功");
                        vm.visible = false;
                    }
                })
            }

        },
        merge(data , id){
            const vm = this , {datasetServices , datasetMock ,settings ,nodeData }  = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;
            if(nodeData.step === true){
                let params = {
                    dataSetId: data.result.dataSetId ,
                    stepId : nodeData.stepId ,
                    unionTableStep : data.result
                };
                services.editDataStep(params ,settings).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("步骤编辑成功");
                        vm.$emit("mergeSuccess");
                        vm.visible = false;
                    }
                })
            }else {
                services.union(data , settings).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("合并成功");
                        let params =  res.data.data ? vm.setFields(res.data.data) : res.data.data;
                        vm.$emit("mergeSuccess" , params);
                        vm.visible = false;
                    }
                })
            }

        }
    }
}