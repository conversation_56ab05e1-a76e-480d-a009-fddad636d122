<template>
    <common-dialog
            custom-class="ce-common_dialog ce-dialog__body-p0"
            :width="width"
            title="编辑/导入"
            :visible.sync="dialogVisible"
            @closed="clearD"
    >
        <CodeTableEdit ref="tabEdit" v-if="reNew" :row="row"/>
        <div slot="footer">
            <el-button @click="closeEditDialog" size="mini">取消</el-button>
            <el-button @click="importBase" size="mini" type="primary">{{buttonTxt}}</el-button>
        </div>
    </common-dialog>
</template>

<script>
    import CodeTableEdit from "./CodeTableEdit";
    import CommonDialog from "@/components/common/dialog/CommonDialog"
    export default {
        name: "EditDialog",
        components: {
            CodeTableEdit,
            CommonDialog
        },
        data() {
            return {
                dialogVisible: false,
                width: '800px',
                reNew: false,
                row: {},
                buttonTxt : '',
            }
        },
        methods: {
            //弹窗
            show(row) {
                this.dialogVisible = true;
                this.reNew = true;
                this.row = row;
                this.buttonTxt = row && row.id ? '保存' : '导入';
            },
            clearD() {
                this.reNew = false
            },
            closeEditDialog() {
                this.dialogVisible = false;
            },
            async importBase() {
                this.$refs.tabEdit.validate();
                let cnName = this.$refs.tabEdit.form.cnName,
                    enName = this.$refs.tabEdit.form.enName,
                    keyFileId = this.$refs.tabEdit.search.nodes,
                    valFileId = this.$refs.tabEdit.search.nodeV,
                    tabData = this.$refs.tabEdit.tableData,
                    base = this.$refs.tabEdit.search.base ,
                    chart = this.$refs.tabEdit.search.chart;
                let data = [cnName ,enName , keyFileId , valFileId , tabData ,base ,chart];
                for(let i = 0; i < data.length; i++){
                    if(data[i] === "" || data[i].length === 0){
                        return ;
                    }
                }
                let r = {name: cnName, code: enName, keyFileId: keyFileId, valFileId: valFileId, tabData: tabData , dwInstanceId : base , dwTableId : chart };
                if(this.row&&this.row.id){
                    r.id=this.row.id;
                }
                let flag = false;
                let tableIndex = "" , target = "" , keyStore = [] , valStore = [];
                for(let i = 0; i < r.tabData.length ; i++){
                    let item = r.tabData[i];
                    if(item.code ==="" || item.code === null ){
                        flag = true;
                        tableIndex = i;
                        target = "code";
                        this.$message.warning(`码val存在为空，不可${this.buttonTxt}！`);
                        break ;
                    }else if(item.codeValue === "" || item.codeValue === null){
                        flag = true;
                        tableIndex = i;
                        target = "codeValue";
                        this.$message.warning(`码key存在为空，不可${this.buttonTxt}！`);
                        break ;
                    }else if(keyStore.includes(item.codeValue)){
                        flag = true;
                        tableIndex = i;
                        target = "codeValue";
                        this.$message.warning("码key重复，请修改！");
                        break;
                    }else if(valStore.includes(item.code)){
                        flag = true;
                        tableIndex = i;
                        target = "code";
                        this.$message.warning("码val重复，请修改！");
                        break;
                    }
                    keyStore.push(item.codeValue);
                    valStore.push(item.code);
                }

                if(flag){
                    this.$refs.tabEdit.editFocus(tableIndex , target);
                    return ;
                }
                this.$axios.post("/standermb/saveAndUpdate", r).then(res => {
                    if (res.data.status === 0) {
                        this.$message.success(this.buttonTxt + "成功!");
                        this.dialogVisible = false;
                        if(this.row){
                            this.row.id=res.data.data;
                        }
                        this.$emit("searchData");
                    }else {
                        this.$message.error(res.data.msg);

                    }
                });
            }
        }
    }
</script>

<style scoped>

</style>