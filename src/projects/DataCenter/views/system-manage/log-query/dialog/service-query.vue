<!--高级检索-->
<template>
    <div class="senior-query">
        <el-form class="ce-form" ref="form" :model="form" label-width="100px" label-position="right">
            <el-form-item v-for="(item , k) in layForm" :key="k" :prop="k" :label="item.label" :rules="item.rules ? item.rules :[]">
                <el-input v-if="item.type ==='input'" clearable
                          v-model.trim="form[k]"
                          :placeholder="item.placeholder"
                          v-input-limit:[item.limit]
                          :ce-custom-reg="item.reg"
                />
                <dg-checkbox-group v-if="item.type === 'checkbox'" v-model="form[k]" :data="item.option"></dg-checkbox-group>
                <el-date-picker
                        class="senior-query_date"
                        v-if="item.type === 'datePicker'"
                        v-model="form[k]"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                </el-date-picker>
                <dg-select v-if="item.type === 'select'" v-model="form[k]" :data="item.option" :placeholder="item.placeholder"></dg-select>
            </el-form-item>
            <el-form-item label-width="0" class="tc">
                <el-button type="primary" @click="query">{{queryTxt}}</el-button>
                <el-button @click="reSetForm">{{resetTxt}}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    import {time} from '@/api/time'
    export default {
        name: "service-query",
        data(){
            return {
                form : {
                    requester : "" ,
                    interfaceName: "" ,
                    dataTime: "" ,
                    interfaceResult:''
                },
                queryTxt : "查询",
                resetTxt : "重置",
                layForm : {
                    interfaceName : {
                        label : "接口名称:" ,
                        type : "input" ,
                        placeholder : "请输入接口名称",
                        limit : "trim"
                    },
                    requester : {
                        label : "请求方名称:" ,
                        type : "input" ,
                        placeholder : "请输入请求方名称",
                        limit : "fieldName"
                    },
                    dataTime : {
                        label : "接口服务时间:" ,
                        type : "datePicker",
                        placeholder : "请选择接口服务时间"
                    },
                    result : {
                        label : "操作结果:" ,
                        type : "checkbox" ,
                        option : [
                            {label : '成功' ,value : '1'} ,
                            {label : '失败' ,value : '0'} ,
                        ]
                    },
                }
            }
        },
        methods : {
            setTimeFormat(pattern , replace ,val){
                let formatedDate = val.replace(pattern, replace);
                return new Date(formatedDate);
            },
            init(form){
                const vm =this;
                vm.form.requester = form.requester ;
                vm.form.interfaceName = form.interfaceName ;
                vm.form.dataTime = [];
                if(form.interfaceTime){
                    vm.form.dataTime[0] = vm.setTimeFormat(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/ , '$1/$2/$3 $4:$5:$6' ,form.interfaceTime);
                }
                if(form.resultTime){
                    vm.form.dataTime[1] = vm.setTimeFormat(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/ , '$1/$2/$3 $4:$5:$6' ,form.resultTime);
                }

            },
            reSetForm(){
                this.$refs.form.resetFields();
            },
            query(){
                const vm  = this , {form } = vm;
                vm.$refs.form.validate(valid => {
                    if(valid){
                        let interfaceTime = "" , resultTime = "";
                        if(form.dataTime && form.dataTime[0] && form.dataTime[1]){
                            interfaceTime = time.formatTimeToStr(form.dataTime[0],'yyyyMMddhhmmss');
                            resultTime = time.formatTimeToStr(form.dataTime[1],'yyyyMMddhhmmss');
                        }
                        let {requester , interfaceName , result} = vm.form;
                        if( !result || (result && result.split(',').length === 2)) {
                            result = "";
                        }
                        else{
                            result = result === "1" ? true : false;
                        }
                        this.$emit("serviceQuery" , {requester , interfaceName, interfaceTime, resultTime , result});
                    }
                })
            }
        }
    }
</script>

<style scoped lang="less">
    .senior-query {
        padding: 0 24px 0 0;
        &_date.el-input__inner {
            width: 100%;
        }
    }
</style>
