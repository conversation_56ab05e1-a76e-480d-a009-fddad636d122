<template>
    <div class="plugin-tree ce-plugin selectn">
        <ul class="ce-plugin_ul" v-if="dataList.length">
            <template v-for="item in dataList">
                <div class="ce-plugin_cont" :class="markedTarget" :key="item.id" @mouseenter="appearChildren($event , item)"
                     @mouseleave="offAppear(item)">
                    <li class="ce-plugin_item" :class="{'is-focus' : item.id === focusItem}"
                    @click="triggerChildren(item)"
                     @mouseenter="setFocusItem(item)">
                        <div class="ce-plugin_label" :title="item.label">
                            <i class="dg-iconp" :class="item.icon"></i>
                            <span>{{ item.label }}</span>
                        </div>
                        <i v-if="!item.extend && item[listProps.children] && item[listProps.children].length"
                           class="el-icon-arrow-right poi" @click.stop="showChildren(item)"></i>
                        <i v-else-if="item[listProps.children] && item[listProps.children].length"
                           class="el-icon-arrow-down poi" @click.stop="hideChildren(item)"></i>
                    </li>
                    <el-collapse-transition>
                        <ul class="ce-plugin_children" :class="[markedTarget , {'ce-plugin_appear' : item.appear}]"
                            v-if="item.extend || item.appear">
                            <li class="ce-plugin_child" 
                            v-for="child in item.children" 
                            :key="child.id"
                            :draggable="true"
                            @dragstart.stop="addNode(child)"
                            @dragend="dragend"
                            >
                                <div class="ce-plugin_child-label"
                                     :class="{
                                    'node--primary': pluginColor.blue.indexOf(child.keyWord) > -1,
                                    'node--info': pluginColor.orange.indexOf(child.keyWord) > -1,
                                    'node--success': pluginColor.cyan.indexOf(child.keyWord) > -1,
                                    'node--chart': pluginColor.purple.indexOf(child.keyWord) > -1,
                                }"
                                     :title="child.description ? child.description : child.label"
                                >
                                    <svg class="eda_icon">
                                        <use :xlink:href="child.icon"></use>
                                    </svg>
                                    <span>{{ child.label }}</span>
                                </div>
                            </li>
                        </ul>
                    </el-collapse-transition>
                </div>
            </template>
        </ul>
        <empty class="lh40 f14" v-else></empty>
    </div>
</template>

<script>
import * as $ from "jquery"

export default {
    name: "plugin-tree",
    props: {
        pluginColor:Object,
        transPlugins: {
            type: Array,
            default: () => []
        },
        outputPlugins: {
            type: Array,
            default: () => []
        },
        chartsPlugins: {
            type: Array,
            default: () => []
        },
        doubleInputPlugins: {
            type: Array,
            default: () => []
        },
        filterMethod: {
            type: Function,
            default: (value, data, props ,parent) => {
                if (value.trim() === '') return true;
                value = value.trim().toLowerCase();
                let label = data[props.label] || '', children = data[props.children];
               /*  if(parent) parent.extend = label.toLowerCase().includes(value) && parent[props.children] &&  parent[props.children].length;
                else  */
                data.extend =  label.toLowerCase().includes(value);
                return label.toLowerCase().includes(value);
            }
        },
        data: {
            type: Array,
            default: () => []
        },
        listProps: {
            type: Object,
            default: () => {
                return {
                    id: "id",
                    label: "label",
                    icon: "icon",
                    children: "children",
                    keyword: "keyword"
                }
            }
        },
        extendAll: {
            type: Boolean,
            default: false
        },
        markedTarget : { //控制显示浮层的标记 class
            type : String ,
            default : "ce-model__plugin-target"
        }
    },
    data() {
        return {
            dataList: [],
            appearList: null ,
            focusItem : ""
        }
    },
    watch: {
        data(val) {
            this.dataList = this.init();
        }
    },
    methods: {
        addNode(data){
            let result = data.data && data.data.foo || data;
            this.$emit("addNode" ,result);
        },
        dragend(evt){
            this.$emit("dragend" ,evt);
        },
        setFocusItem(item){
            this.focusItem = item.id;
        },
        getDivPosition(div) {
            let x = div.getBoundingClientRect().left,
                y = div.getBoundingClientRect().top ,
                w = div.clientWidth;
            return {x, y , w};
        },
        init(filterShow) {
            const vm = this, {extendAll, data} = vm;
            let dataList = vm.copyArrayObj(data);
            if (extendAll || filterShow) {
                dataList.forEach(item => {
                    item.extend = true;
                })
            }
            return dataList;
        },
        triggerChildren(item){
            item.extend = !item.extend;
            item.appear = false;
            this.disappearChildren();
        },
        showChildren(item) {
            item.extend = true;
            item.appear = false;
            this.disappearChildren();
        },
        hideChildren(item) {
            item.extend = false;
        },
        appearChildren(evt , item) {
            const vm = this, {listProps} = vm;
            if(vm.appearList)vm.appearList.remove();
            if (item.extend || !item[listProps.children] || !item[listProps.children].length) return;
            item.appear = true;
            let target = evt.currentTarget;
            let {x , y ,w } = vm.getDivPosition(target);
            vm.$nextTick(()=> {
                vm.appearList = $(target).find(".ce-plugin_children").clone();
                vm.appearList.css({position:"fixed" , left : x + w ,top : y ,zIndex : 2800});
                vm.appearList.mouseleave(function (){
                    vm.disappearChildren(item);
                })
                
                 vm.appearList.children().each( ( i , child ) =>{
                   
                     $(child).bind("dragstart" , {foo:item.children[i]} , vm.addNode)
                     $(child).bind("dragend" , vm.dragend);
                 })
             
            
                $("body").append(vm.appearList);
                vm.appearList.show();


            })
        },
        moveEvent(e){
            const vm = this , {markedTarget} = vm ;
            let target = e.target;
            if(typeof target.className === "string" && target.className.includes(markedTarget)  || $(target).parents('.'+markedTarget)[0] ){
                return;
            }
            vm.disappearChildren();
        },
        bindDocEvent(){
            document.addEventListener('mousemove' , this.moveEvent,false)
        },
        unbindDocEvent(){
            document.removeEventListener("mousemove" , this.moveEvent,false );
        },
        disappearChildren(item) {
            item && this.offAppear(item);
            this.appearList && this.appearList.remove();
            this.appearList = null;
        },
        offAppear(item){
            item.appear = false;
        },
        /**
         * 过滤
         */
        filterList(val) {
            const vm = this, {listProps, filterMethod} = vm;
            vm.disappearChildren();
            let dataList = vm.init(!!val);
            vm.dataList = dataList.filter(item => {
                let filterRes = filterMethod(val, item, listProps);
                if(!filterRes){
                    item[listProps.children] = item[listProps.children] ?
                        item[listProps.children].filter(child => {
                            return filterMethod(val, child, listProps);
                        }) : [];
                    item.extend = item[listProps.children].length;
                }
                return filterRes || !!(item[listProps.children] && item[listProps.children].length);
            });
        }
    },
    mounted() {
        this.bindDocEvent();
    },
    beforeDestroy() {
        this.unbindDocEvent();
    }
}
</script>

<style scoped lang="less">
.ce-plugin {
    background: #fff;
    padding: 5px 0;

    &_cont {
        position: relative;
        padding: 5px 0;
    }

    &_item {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
        > i {
            line-height: 2.25rem;
            padding: 0 8px;
        }

        &:hover, &.is-focus {
            background: rgba(24, 144, 255, .12);
        }

    }

   

    &_label {
        max-width: calc(100% - 40px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 2.25rem;
        line-height: 2.25rem;
        font-size: 14px;

        > i {
            padding: 0 8px;
        }
    }

    &_child-label {
        display: flex;
        align-items: center;

        .eda_icon {
            width: 1.2rem;
            height: 1.2rem;
        }

        > span {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 10px;
            font-size: 14px;
        }
    }

    &_children {
        padding: 6px 14px 0 30px;
    }

    &_appear {
        width: 240px;
        display: none;
        background: #fff;
        padding: 1.25rem;
        border: 1px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.09);
    }

    &_child {
        line-height: 2rem;
        padding-left: 16px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-radius: 2px;
        cursor: move;

        &:hover {
            background: rgba(0, 136, 255, 0.05);
            border-color: rgba(0, 136, 255, 0.65);
        }

        &:not(:last-child) {
            margin-bottom: 10px;
        }
    }
}

.node--primary svg {
    fill: #1890ff
}

.node--info svg {
    fill: #faad14;
}

.node--success svg {
    fill: #52c41a;
}

.node--chart svg {
    fill: #6D87F1;
}

</style>
