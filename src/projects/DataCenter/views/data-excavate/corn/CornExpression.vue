<template>
    <el-dialog :close-on-click-modal="false" :width="width" :title="dialogTitle"
               :visible.sync="dialogVisible"
               @closed="clearD"
               :append-to-body="true">
        <CornPanel ref="cornPanel" v-if="reNewData" />
        <div slot="footer">
            <el-button @click="save" type="primary" size="mini">保存</el-button>
        </div>
    </el-dialog>
</template>

<script>
    import CornPanel from './CornPanel'
    import {globalBus} from "@/api/globalBus";

    export default {
        name: "CornExpression",
        data(){
            return {
                dialogTitle : 'cron表达式',
                dialogVisible :false ,
                width : '880px' ,
                reNewData : false
            }
        },
        components : {
            CornPanel
        },
        methods : {
            clearD(){
                this.reNewData = false;
            },
            show(data){
                this.reNewData = true;
                this.dialogVisible = true;
                this.initData(data);
            },
            initData( data ){
                globalBus.$emit('cornData' ,data);
            },
            close(){
                this.dialogVisible = false;
            },
            save(){
                let corn = this.$refs.cornPanel.getResult();
                this.close();
                this.$emit('save' , corn);

            }
        }
    }
</script>

<style scoped>

</style>
