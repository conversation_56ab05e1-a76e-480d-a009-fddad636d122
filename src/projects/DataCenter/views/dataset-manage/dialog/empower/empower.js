import {treeMethods} from "@/api/treeNameCheck/treeName"
import * as dataSourcesServices from "../../../dataSources/service-mixins/service-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins";
import {checkTree} from "@/api/commonMethods/check-tree-mixins";

export default {
    name: "empower" ,
    mixins : [treeMethods , dataSourcesServices.servicesMixins , commonMixins , servicesMixins , checkTree],
    data(){
        return {
            savePosition : "" ,
            savePositionDatas : [] ,
            treeBind : {
                "default-expand-all" : true
            },
            labelP : "保存位置" ,
            allDataObj : [],
            multiple : true,
            val : []
        }
    },
    methods :{
        set_checked_tree(data){ //暂时无效
            this.level_disabled(data);
        },
        level_disabled(nodes , level){
            const vm = this;
            nodes.forEach(item => {
                if(item.children && item.children.length){
                    vm.level_disabled(item.children , level);
                }
                if(item.level < level){
                    item.disabled = true;
                }
            })
        },
        setDataObj(item){
            const vm = this;
            if(item.level === 4 || item.instanceType === 'FileDataObj'){
               vm.allDataObj.push(item.id);
            }
        },
        setNodeLabel(nodes){
            const vm = this;
            return nodes.filter(item => {
                if(item.children && item.children.length){
                    item.children = vm.setNodeLabel(item.children);
                }
                vm.setDataObj(item);

                if(!item.label ){
                    item.label = item.name || item.code;
                }
                if(item.level < 4){
                    return item.children && item.children.length;
                }else  {
                    return item.level === 4;
                }
            })
        },
        initTree(){
            const vm = this , {listApi, listMockApi , datasetServices , datasetMock , settings} = this;
            let listServices = vm.getServices(listApi, listMockApi) , services = vm.getServices(datasetServices , datasetMock) ;
            settings.loading = true;
            listServices.getDatabaseDataObjectTree(settings).then(res =>{
                if (res.data.status === 0){
                    let result = res.data.data;
                    vm.data = vm.setNodeLabel(result);
                }
            });
            /*services.queryDataSetTree(false).then(res => {
                if (res.data.status === 0){
                    vm.savePositionDatas = res.data.data[0].children.filter(item => {
                        if(item.code === 'DATASET_DIR_MY'){
                            vm.savePosition = item.id;
                        }
                        return item.code === 'DATASET_DIR_MY';
                    });
                }
            })*/
        },
        getAllDataObjsAndSite(){
            const vm = this;
            let selectedObj = vm.$refs.tree.getCheckedKeys(true);
            let res_obj_ids = selectedObj.filter(ids => vm.allDataObj.indexOf(ids) > -1);
            return {
                ids : res_obj_ids ,
                //site : vm.savePosition
            }
        }

    }
}