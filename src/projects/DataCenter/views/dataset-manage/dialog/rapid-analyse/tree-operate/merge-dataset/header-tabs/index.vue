<template>
    <div>
        <el-tabs v-model="mergeTabActive" type="card">
            <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.value" :disabled="tab.disabled">
            </el-tab-pane>
        </el-tabs>
        <left-right-join ref="join" :datasetId="datasetId" v-show="mergeTabActive === 'join'" @join="join"></left-right-join>
        <up-down-union ref="union" :datasetId="datasetId" :isStep="isStep" v-show="mergeTabActive === 'union'" @merge="merge"></up-down-union>
    </div>

</template>

<script src="./header-tabs.js"></script>
