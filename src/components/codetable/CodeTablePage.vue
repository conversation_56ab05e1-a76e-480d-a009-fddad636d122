<template>
    <div class="codeTablePage">
        <tree-and-list :show-left="false">
            <Breadcrumb slot="crumb" :crumb="defaultCrumb"/>
            <template>
                <table-menu>
                    <template slot="prepend">
                        <el-form-item>
                            <el-input v-model.trim="searchVal" clearable size="small" placeholder="请输入中文/英文名称"
                                      @keyup.enter.native="searchData"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button size="mini" icon="el-icon-search" type="primary" @click="searchData">查询</el-button>
                        </el-form-item>
                    </template>
                    <template slot="append">
                        <el-button
                                type="primary"
                                size="mini"
                                @click="inputBase"
                        >库导入</el-button>
                        <el-button
                                type="primary"
                                size="mini"
                                @click="flushCache"
                        >刷新系统码表信息</el-button>
                    </template>
                </table-menu>
                <CommonTable
                        :data="tableData"
                        :columns="tHeadData"
                        tooltip-effect="light"
                        cell-class-name="ce-ellipsis"
                        size="mini"
                        v-loading="loading"
                >
                    <template slot="operate" slot-scope="scope">
                        <em class="icon ce_link p5"
                            v-for="( ico , key ) in operateIcon"
                            :key="key"
                            :title="ico.label"
                            v-html="ico.icon"
                            @click="ico.clickFn(scope.row , scope.$index)"
                        ></em>
                        <el-popconfirm
                                size="mini"
                                title="确认删除?"
                                @confirm="deleteInfo.clickFn(scope.row , scope.$index)"

                        >
                            <em
                                    v-if="scope.row.id !== 'c5defa002b9d4b229731a6237e231d44'"
                                    slot="reference"
                                    class="icon ce_link p5"
                                    :title="deleteInfo.label"
                                    v-html="deleteInfo.icon"
                            ></em>
                        </el-popconfirm>

                    </template>
                </CommonTable>
                <el-pagination
                        class="pagination"
                        background
                        @current-change="changePage"
                        :current-page="currentPage"
                        :page-size="pageSize"
                        layout="total, prev, pager, next, jumper"
                        :total="total"
                ></el-pagination>
            </template>
        </tree-and-list>
        <EditDialog ref="edit" @searchData="searchData"/>
        <DetailDialog ref="detail"/>
    </div>
</template>

<script>
    import CommonTable from '@/components/common/CommonTable'
    import EditDialog from "./EditDialog";
    import DetailDialog from "./DetailDialog";
    import Breadcrumb from "@/components/crumb/Breadcrumb";
    import TreeAndList from "@/components/layout/treeAndList/TreeAndList"
    import TableMenu from "@/components/common/tableMenu/TableMenu"
    import * as codeApi from "./request/codeTableApi"
    export default {
        name: "CodeTablePage",
        props: {
            crumb: Array
        },
        components: {
            Breadcrumb,
            CommonTable,
            EditDialog,
            DetailDialog,
            TreeAndList ,
            TableMenu
        },
        data() {
            return {
                defaultCrumb: ['数据源管理', '码表管理'],
                searchVal: '',
                currentPage: 1,
                pageSize: 10,
                total: 0,
                tableData: [],
                tHeadData: [
                    {
                        label: '序号',
                        type: 'index',
                        align: 'center',

                    }, {
                        label: '码表中文名',
                        prop: 'codeCnName',
                        align: 'center'
                    }, {
                        label: '码表英文名',
                        prop: 'codeEnName',
                        align: 'center'
                    }, {
                        label: '数据源表名',
                        prop: 'chartName',
                        align: 'center'
                    }, {
                        label: 'key数据源字段名',
                        prop: 'keyFiledName',
                        align: 'center'
                    },
                    {
                        label: 'Val数据源字段名',
                        prop: 'valFiledName',
                        align: 'center'
                    },
                    {
                        label: '操作',
                        prop: 'operate',
                        align: 'center',
                        width: '120px'
                    }
                ],
                loading: false,
                deleteInfo: {
                    label: '删除',
                    icon: '&#xe65f;',
                    clickFn: this.deleteFn
                },
                operateIcon: {
                    edit: {
                        label: '编辑',
                        icon: '&#xe6c0;',
                        clickFn: this.editFn
                    },
                    detail: {
                        label: '详情',
                        icon: '&#xe6c5;',
                        clickFn: this.detailFn
                    },
                }
            }
        },

        created: function () {
            this.searchData();
        },
        methods: {
            flushCache(){
                codeApi.flushCache().then(res =>{
                    if(res.data.status === 0){
                        this.$message.success("刷新成功！");
                    }else{
                        this.$message.error("刷新失败！");

                    }
                })
            },
            searchData() {
               this.changePage(1);
            },
            inputBase() {
                this.editFn()
            },
            changePage(index) {
                this.currentPage = index;
                this.loading = true;
                let searchBody = JSON.stringify({searchVal: this.searchVal, currentPage: this.currentPage, pageSize: this.pageSize});
                codeApi.getList(searchBody).then(res => {
                    if (res.data.status === 0) {
                        this.tableData = res.data.data.dataList;
                        this.total = res.data.data.totalCount;
                    }
                    this.loading = false;
                }).catch(err => {
                    this.loading = false;
                    this.$message.error("服务器异常，请联系管理员！" + err);
                });
            },
            editFn(row, index) {
                this.$refs.edit.show(row);
            },
            detailFn(row, index) {
                this.$refs.detail.show(row);
            },
            deleteFn(row, index) {
                codeApi.deleteData(row.id,{}).then(res => {
                    if (res.data.status === 0) {
                        this.tableData.splice(index, 1);
                        this.$message.success("删除成功!");
                        this.changePage(1);
                    }
                }).catch(err => {
                    this.$message.error("服务器异常，请联系管理员！" + err);
                });

            },

        }
    }
</script>

<style scoped>
    .codeTablePage {
        position: relative;
        height: 100%;
    }

    .ce-content {
        margin: 10px;
        background: #fff;
        padding: 10px;
        height: calc(100% - 88px);
        border: 1px solid #ddd;
        overflow: auto;
    }

    .pagination {
        margin: 5px 0;
        text-align: center;
    }

    .btn_new_model {
        float: right;
        margin-left: 8px;
    }

    .ce-filter-box {
        height: 32px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .ce-search {
        float: left;
        height: 32px;
    }
</style>
