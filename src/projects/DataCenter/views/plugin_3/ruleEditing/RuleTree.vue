<template>
    <div class="ruleTree">
        <el-input :placeholder="placeholder" v-model="filterText" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn" v-loading="settings.loading">
            <dg-tree
                    ref="tree"
                    node-key="id"
                    :filter-node-method="filterNode"
                    :data="treeData"
                    default-expand-all
                    :props="defaultProps"
                    @node-click="udfClick"
            >
                <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                    <span :draggable="isCustom()"
                          v-if="data.udfType === 'UDF_OPERATOR' ? node.level > 2 : ( !data.children || !data.children.length)"
                          :class="['node-label', {'node-label_drag': isCustom()}]"
                          @dragstart="getUdfOperator(data , 'inputExpress')"
                          @dragend="dragend"
                          @dblclick="isCustom() ? getUdfOperator(data , 'dblInputExpress' ): ()=>{}"
                          :title="node.label"
                    >{{ node.label }}</span>
                    <span v-else :title="node.label" class="el-tree-node__label">{{ node.label }}</span>
                </span>
            </dg-tree>
        </div>
    </div>
</template>

<script>
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";


export default {
    name: "RuleTree",
    mixins: [treeMethods, servicesMixins],
    props: {
        isGlobal: {
            type: Boolean,
            default: false,
        },
        // 算子切换之前
        beforeChange: {
            type: Function,
            default:()=>{}
        }
    },
    inject:['isCustom'],
    data() {
        return {
            defaultProps: {
                value: "id",
                label: "label",
                children: "children"
            },
            filterText: '',
            placeholder: '请输入名称过滤',
            treeData: [],
            loading: false,
            specialOperator: ["aviatorGetMaxOrMinOperator"],
            specialType: ["windowStreaming", "serviceOrganization"],
            udf_operator: [],
            udf_operator_map: {},
            currentKey: "",
            triggerDefNode: false,// 触发选中默认第一个算子 （标准输入 首次进入）
        }
    },
    methods: {
        loadTreeUdf() {
            const {services} = this;
            if (this.isGlobal) {
                return Promise.all([
                    services.getVariableOperatorTree(),
                    services.getVariableExpression()
                ])
            }
            return Promise.all([
                services.getExpressionOperatorTree(process.env.VUE_APP_SECRET, 'expressionOperator'),
                services.getExpression()
            ])
        },
        // 初始化 回显
        setCurrentKey(key){
            this.currentKey = key;
            this.$refs.tree?.setCurrentKey(key);
        },
        /**
         * 更新暂存 当前节点 id
         */
        updateCurrentKey(){
            this.currentKey = this.$refs.tree?.getCurrentKey() || "";
        },
        setDefKey(){
            this.triggerDefNode = true;
        },
        /**
         * 初始化树
         * */
        initTree() {
            const vm = this, {settings} = this;
            settings.loading = true;
            vm.treeData = [];
            this.loadTreeUdf()
                .then(([res, res1]) => {
                    if (res1.data.status === 0) {
                        let result = res1.data.data;
                        vm.setAllUdf(result);
                    }
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.treeData = vm.setChildren(result);
                        if(vm.currentKey) {
                            vm.$nextTick(()=>{
                                vm.$refs.tree?.setCurrentKey(vm.currentKey);
                                if(vm.triggerDefNode) {
                                    let data = vm.$refs.tree?.getCurrentNode();
                                    vm.udfClick(data)
                                }
                            })
                        }
                    }
                })
                .finally(() => {
                    settings.loading = false;
                })
        },
        /**
         * 设置全部算子
         * */
        setAllUdf(data) {
            this.udf_operator_map = data || {};
            this.udf_operator = [];
            for (let k in data) {
                this.udf_operator.push(data[k]);
            }
            this.$emit("setAllUdf", this.udf_operator);
        },
        /**
         * 设置树的数据
         * */
        setChildren(data, parent) {
            const vm = this;
            return data.map(item => {
                item.label = item.name;
                if (!item.udfType && parent) {
                    if(vm.triggerDefNode && !vm.currentKey) vm.currentKey = item.id;
                    item.udfType = parent.udfType;
                    const udfCode = vm.udf_operator_map[item.name];
                    if(udfCode) item.label = item.name + ` (${udfCode})`;
                }
                if (item.children && item.children.length) {
                    item.children = vm.setChildren(item.children, item);
                }
                return item;
            })
        },
        /**
         * 获取算子表达式
         *
         */
        getUdfOperator(data, funcName) {
            const vm = this, {services} = vm;
            return services.getUdfOperatorById(data.id).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.$emit(funcName, result);
                }
            })
        },
        /**
         * 拖拽结束
         */
        dragend() {
            this.$emit("inputFinish");
        },
        /**
         * 算子点击获取描述
         */
        async udfClick(data) {
            const vm = this;
            if (data.version) {
                const res = await vm.beforeChange();
                if(!res) return this.setLastCurrentKey();
                await vm.getUdfOperator(data, "setDescription");
                this.updateCurrentKey();
            } else {
                vm.$emit("setDescription", {});
            }
        },
        /**
         * 设置 回退上一次选中的节点
         */
        setLastCurrentKey(){
            if(this.currentKey) this.$refs.tree?.setCurrentKey(this.currentKey);
        }
    }
}
</script>

<style scoped lang="less">

.ruleTree {
    height: 100%;
}

.ce-tree {
    height: calc(100% - 2rem);
    overflow: auto;
    box-sizing: border-box;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
}

.node-label {
    max-width: 166px;
    overflow: hidden;
    text-overflow: ellipsis;
    &::before {
        padding-right: 5px;
    }
    &_drag {
        cursor: move;
    }
}

</style>
