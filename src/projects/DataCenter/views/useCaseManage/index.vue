<template>
    <tree-and-list left-width="15.8%">
        <tree-card ref="treeCard" slot="left" :card-name="cardName" @filterData="filterTree">
            <i slot="add" :title="newFile" class="el-icon-folder-add poi" @click="showAddDialog()"></i>
            <el-popover slot="sort" popper-class="ce-popover" v-model="showAddBox" trigger="click" width="96">
                <div class="ce-tree__pop-box">
                    <div class="ce-tree__pop-item" v-for="(menu , k) in sortMenu"
                         :class="{'active' : k === sortVal}"
                         @click="setTreeSort(k)" :key="k">
                        <i class="eda_icon" :class="menu.icon"></i>
                        <span>{{ menu.label }}</span>
                    </div>
                </div>
                <i slot="reference" class="eda_icon ce-sort_icon"
                   :title="sortVal === 'letter'?letterSort : timeSort"
                   :class="{'iconsort' : sortVal === 'letter' , 'iconshijianpaixutubiao' : sortVal === 'time' }"></i>
            </el-popover>
            <tree
                    slot="tree"
                    ref="dataTree"
                    :hasSave="false"
                    @reSearch="reSearch"
                    @addTreeDir="showAddDialog"
                    @setTreeData="setTreeData"
                    @nodeClick="treeDir"
                    @initTable="initTable"
                    @moveToDir="moveToDir"
            ></tree>
        </tree-card>

        <list ref="modelingCenterTable"
              @moveTo="moveTo"></list>
        <div slot="other">
            <!--移动-->
            <!--新建树 目录-->
            <add-tree-dir ref="treeDir" v-loading="settings.loading" @addTreeDir="addTreeDir" @moveTreeDir="moveTreeDir" @filterData="filterEditTree" >
                <tree slot="tree" ref="editTree" slot-scope="scope" v-bind="scope" @nodeClick="nodeClick" />
            </add-tree-dir>
        </div>
    </tree-and-list>
</template>
<script src="./useCaseManage.js">
</script>
<style scoped lang="less"></style>
