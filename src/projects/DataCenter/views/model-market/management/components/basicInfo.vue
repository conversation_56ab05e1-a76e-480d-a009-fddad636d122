<template>
    <div class="basicInfo">
        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
            <el-form-item label="模型名称：" required >
                <el-input v-model="infoList.name" disabled></el-input>
            </el-form-item>
            <el-form-item label="模型LOGO："  prop="upload">
                <dg-upload
                        class="avatar-uploader"
                        v-model="form.upload"
                        :accept="'.png,.jpg,.jpeg'"
                        list-type="pictureList"
                        :auto-upload="false"
                        :on-change="handleBefore"
                        :on-remove="removeFile"
                        carousel-height="55px"
                        icon-delete
                        leaflet
                        action="#"
                        :props="{ url: 'value' }"
                >
                </dg-upload>
                <div class="el-upload__tip">
                    仅支持JPG、JPEG、PNG 格式图片，不能超过2M
                </div>
            </el-form-item>
            <el-form-item label="模型版本：" required prop="version">
                <el-input v-model="form.version" :placeholder="placeholder_version"></el-input>
            </el-form-item>
            <el-form-item label="模型简介：" required prop="desc">
                <el-input type="textarea" resize="none" v-model="form.desc" :placeholder="placeholder_desc" :autosize="{ minRows: 4, maxRows: 6}" maxlength="100" show-word-limit></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "basicInfo",
        props:{
            infoList:Object,
            openType:String,
        },
        data(){
            let validateVersion = (rule, value, callback) => {
                let req = /^([A-Z]{1})+\d+\.\d+((\.\d+)*)$/;
                if(value === ''){
                    callback(new Error('请输入模型版本！'));
                }
                if (!req.test(value)) {
                    callback(new Error('仅支持大写字母+数字，比如：V1.0、V3.1.2'));
                } else {
                    callback();
                }
            };
            return{
                placeholder_desc:'请简要介绍您的模型信息，此信息将在超市的页面进行展示，长度不超过100 字。',
                placeholder_version:'仅支持大写字母+数字，比如：V1.0、V3.1.2',
                rules: {
                    version: [
                        { required: true, message:'请输入模型版本！', trigger: "change" },
                        { validator: validateVersion,trigger: 'blur' },
                    ],
                    desc: [{ required: true, message: '请输入模型简介', trigger: ["blur", "change"] }]
                },
                form: {
                    name: '',
                    version:'V1.0',
                    desc:'',
                    upload:[]
                },
                files: "",
            }
        },
        methods:{
            beforeAvatarUpload(file) {
                let acceptList = ['JPEG', 'JPG', 'PNG'];
                const isImage = acceptList.includes(file.type.toUpperCase());
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isImage) {
                    return this.$message.error('模型LOGO只能是JPG、JPEG、PNG 格式!');
                }
                else if (!isLt2M) {
                    return this.$message.error('模型LOGO大小不能超过 2M!');
                }
                if(isImage && isLt2M){
                    this.form.upload.push(file);
                }
                return isImage && isLt2M ;
            },
            handleBefore(file) {
                let isCheck = this.beforeAvatarUpload(file);
                if(!isCheck)
                    return false;
                const vm = this;;
                let reader = new FileReader();
                reader.readAsDataURL(file.raw);
                reader.onload = function(e) {
                    vm.files = e.target.result ;// 图片base64化
                };
            },
            //移除文件
            removeFile(file){
                this.files = ''
            },
            validate(){
                let flag = true;
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                    } else {
                        flag = false;
                        return false;
                    }
                })
                return flag;
            },
        },
        created(){
            if(this.openType === 'edit'){
                this.form = {
                    version:this.infoList.version,
                    desc:this.infoList.introduction,
                    upload:this.infoList.logo
                }
                this.files = this.infoList.logo.length ? this.infoList.logo[0].value : "";
            }
            else{
                this.form.desc = this.infoList.memo;
            }
        }
    }
</script>

<style scoped lang="less">
    .basicInfo{
        /deep/.dg-upload--pictureList:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        /deep/ .dg-upload--picture-card{
            width: 55px;
            height: 55px;
        }
        /deep/ .el-upload{
            .dg-upload--pictureList{
                width: 71px;
                height: 71px;
                line-height:71px;
            }
            .dg-upload--text{
                bottom:40px;
            }
        }
        /*/deep/ .el-upload-list--picture-card .dg-upload-list__item{*/
            /*width: 55px;*/
            /*height: 55px;*/
            /*line-height: 55px;*/
        /*}*/
        /*/deep/ .dg-upload-list--picture-card .dg-upload-list__item-thumbnail{*/
            /*width: 55px;*/
            /*height: 55px;*/
            /*line-height: 55px;*/
        /*}*/
        /*/deep/ .avatar{*/
            /*width: 55px;*/
            /*height: 55px;*/
        /*}*/
        /deep/.dg-upload__carousel{
            width: 55px;
        }
    }
</style>