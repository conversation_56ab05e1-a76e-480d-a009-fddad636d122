import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";

export default {
    name: "dataList" ,
    props: {
        node: Object,
        dir_dataSetId : String
    },
    mixins : [attrMixins],
    data(){
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show: ()=> true,
            }
        },
        dataLimit = {
            label : '数据数量' ,
            type : "limit_number",
            placeholder: "请输入数据数量",
            prop : "limit",
            value : "",
            show : ()=>true,
        };
        return {
            showDrill : true , //显示钻取
            axisList : {
                drilling : {
                    label : "钻取/维度" ,
                    type: 'drill_select',
                    list : [],
                    show : (axis)=> axis.list.length
                },
                xAxis: {
                    label: '值轴/度量',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动度量字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: ()=> true,
                    rule: [
                        {required: true, message: '请添加度量', trigger: 'blur'}
                    ]
                },
                yAxis: {
                    label: '类别轴/维度',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 2,
                    showLimit: true,
                    show: ()=> true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ]
                },
                ...formList ,
                dataLimit
            },
            query : null ,

        }
    },
    computed: {
        chartData() {
            return {
                xAxis: this.axisList.xAxis.value,
                xData: this.axisList.xAxis.options,
                yAxis: this.axisList.yAxis.value,
                yData: this.axisList.yAxis.options,
                filterData: this.filterSelects,
                datasetId: this.datasetId,
                classifierStatId: this.classifierStatId,
                dataset: this.dataset ,
                filterList : this.query ,
                drill :this.axisList.drilling.list,
                limit : this.axisList.dataLimit.value
            };
        }
    },
    methods : {
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            /*if (this.node.code !== 'TableChartWidget') {
                if (this.axisList[inx].prop === 'xAxis' && this.dragTreeNode.indexType === "DIMENSION") {
                    this.$message.warning('请拖入维度输入框');
                    return;
                } else if (this.axisList[inx].prop === 'yAxis' && this.dragTreeNode.indexType === "MEASURE") {
                    this.$message.warning('请拖入度量输入框');
                    return;
                }
            }*/
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx , this.dragTreeNode.indexType , 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        setTransverseBarChart(){
            this.axisList.xAxis.label = "度量";
            this.axisList.yAxis.label = "维度/Y轴";
        },
        initValue(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let transverseCode = ['TransverseStackBarChartWidget' , 'TransverseBarChartWidget'];
                if(transverseCode.indexOf(vm.node.code) > -1 ){
                    vm.setTransverseBarChart();
                }
                let {beforeData , query} = JSON.parse(vm.node.widget),nodeData = JSON.parse(beforeData);
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId :true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.xAxis.options = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.xAxis.value = nodeData.xAxis ? nodeData.xAxis : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];
                vm.axisList.dataLimit.value = nodeData.limit !== undefined ? nodeData.limit : "";
                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                //查询
                // vm.query  = query && query.length ? JSON.parse(query) : null;
                vm.query = [];
                //钻取
                vm.axisList.drilling.list = nodeData.drill ? JSON.parse(JSON.stringify(nodeData.drill)) : [];
                //隐藏下拉
                vm.dropDownHide([vm.axisList.xAxis.options , vm.axisList.yAxis.options]);
                vm.inputData();
                vm.addAxisName("","",vm.axisList);
            })
        },
        validate(){
            if (this.chartData.xAxis.length === 0 && this.node.code !== 'TableChartWidget') {
                this.$message.info('请添加度量!');
                return;
            } else if (this.chartData.yAxis.length === 0) {
                this.$message.info('请添加维度!');
                return;
            }
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setHistogramPanel', this.node.id);
        },
    }
}
