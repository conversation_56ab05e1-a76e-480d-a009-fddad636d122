<!--
    linxp
    2021.06.11

    联调提示 : 每个插件 方法名 保存 save 预览 preview
    插件及参数弹窗，都放在plugin_3 下， 需要混入 mixins : [pluginMixins 弹窗参数，方法 , servicesMixins 请求接口 ],
    弹窗显示方法 show 关闭方法 close

-->

<template>
    <div class="params" :class="{'ce-disabled_model' : isCase}">
        <component ref="params" v-bind="$props" v-on="$listeners" :isCase="isCase" :is="sourceData.keyWord"></component>
    </div>
</template>
<script>

import fileUpLoadPlugin from "@/projects/DataCenter/views/plugin_3/fileUpLoadPlugin/FileUpLoadPlugin"
import cicadaInnerJoinPlugin from "@/projects/DataCenter/views/plugin_3/collectionOperations/CicadaInnerJoinPlugin";
import cicadaSubtractByKeyPlugin from "@/projects/DataCenter/views/plugin_3/collectionOperations/CicadaSubtractByKeyPlugin";
import cicadaUnionJoinPlugin from "@/projects/DataCenter/views/plugin_3/collectionOperations/CicadaUnionJoinPlugin";
import cicadaCodeTableConversion  from "@/projects/DataCenter/views/plugin_3/codeTableConversion";
import cicadaCollisionPlugin from "@/projects/DataCenter/views/plugin_3/collectionOperations/CicadaCollisionPlugin";
import jsonParsing from "@/projects/DataCenter/views/plugin_3/jsonParsing/JsonParsing";
import objectToJson from "@/projects/DataCenter/views/plugin_3/objectToJsonPlugin/ObjectToJson";
import mlSqlScript from "@/projects/DataCenter/views/plugin_3/mlSqlScript/MlSqlScript";
//import manyJoinPlugin from "@/projects/DataCenter/views/plugin_3/manyJoinPlugin/MlSqlScript";
//import addFieldPlugin from "@/projects/DataCenter/views/plugin_3/addFieldPlugin/addFieldPlugin";
//import manyJoinPlugin from "@/projects/DataCenter/views/plugin_3/manyJoinPlugin/ManyJoinPlugin";
import manyJoinPlugin from "@/projects/DataCenter/views/plugin_3/manyJoinPlugin/ManyJoin";
import dateZipperTablePlugin from "@/projects/DataCenter/views/plugin_3/zipperTablePlugin/dateZipperTablePlugin";
import numberZipperTablePlugin from "@/projects/DataCenter/views/plugin_3/zipperTablePlugin/numberZipperTablePlugin";
import cicadaServiceOrganization from "@/projects/DataCenter/views/plugin_3/ruleEditing/cicadaServiceOrganization"
import cicadaStandardSqlOutput from "@/projects/DataCenter/views/plugin_3/metaDataOutput/standardSql/RelationBase";
import cicadaFullTextOutput from '@/projects/DataCenter/views/plugin_3/metaDataOutput/fullText/FullTextBase';
import cicadaReducePlugin from '@/projects/DataCenter/views/plugin_3/group-statis/index';
import cicadaDataSort from '@/projects/DataCenter/views/plugin_3/dataSort/index';
import conditionFilterPlugin from "@/projects/DataCenter/views/plugin_3/conditionFilterPlugin/conditionFilterPlugin";
import removeDuplicateDataPlugin from "@/projects/DataCenter/views/plugin_3/removeDuplicateDataPlugin/removeDuplicateData"
import dataMarking from "@/projects/DataCenter/views/plugin_3/labelPlugin/LabelPlugin"
import cicadaFieldsSettings from "@/projects/DataCenter/views/plugin_3/fieldsSettings/CicadaFieldsSettings";
import jwqMapper from "@/projects/DataCenter/views/plugin_3/jwqMapper/JwqMapper";
import efficitivePolicePlugin from "@/projects/DataCenter/views/plugin_3/efficitivePolicePlugin/EfficitivePolicePlugin";
import fieldFilteringPlugin from "@/projects/DataCenter/views/plugin_3/fieldFilteringPlugin/FieldFilteringPlugin";


import ServiceInputPlugin from "@/projects/DataCenter/views/plugin_3/serviceInput/index"
import ScatterAttr from '@/projects/DataCenter/views/plugin_3/resultOutput/ScatterAttr'
import GanttAttr from '@/projects/DataCenter/views/plugin_3/resultOutput/GanttAttr'
import RadarAttr from '@/projects/DataCenter/views/plugin_3/resultOutput/RadarAttr'
import PieAttr from '@/projects/DataCenter/views/plugin_3/resultOutput/PieAttr'
import HistogramAttr from '@/projects/DataCenter/views/plugin_3/resultOutput/HistogramAttr'
import LineAttr from '@/projects/DataCenter/views/plugin_3/resultOutput/LineAttr'
import rowDenormailiser from '@/projects/DataCenter/views/plugin_3/rowDenormaliser/RowDenormaliser'
// import FullTextInput from '@/projects/DataCenter/views/plugin_3/metaDataInput/fullText/FullTextInput'
import GeoMappingPlugin from "@/projects/DataCenter/views/plugin_3/geoMappingPlugin/GeoMappingPlugin.vue"
import Normalization from "@/projects/DataCenter/views/plugin_3/normalizationPlugin/Normalization"
import MarkingTime from "@/projects/DataCenter/views/plugin_3/markingtime/MarkingTime";
import PeerPlugin from "@/projects/DataCenter/views/plugin_3/peerPlugin/PeerPlugin";
import algorithm from "@/projects/DataCenter/views/plugin_3/algorithm/algorithm";
import StandardSqlInput from '@/projects/DataCenter/views/plugin_3/metaDataInput/standardSql/StandardSqlInput'
import KvBaseInput from '@/projects/DataCenter/views/plugin_3/metaDataInput/keyValue/KvBaseInput'
import FullTextInput from '@/projects/DataCenter/views/plugin_3/metaDataInput/fullText/FullTextInput'
import FileDownLoadPlugin from "@/projects/DataCenter/views/plugin_3/fileDownLoadPulgin/FileDownLoadPlugin"
import ExcavateExpressionExcuter from '@/projects/DataCenter/views/plugin/ruleEditing/ExcavateExpressionExcuter'
import metaServiceInput from '@/projects/DataCenter/views/plugin_3/metaService/metaServiceInput'
import metaServiceOutPut from '@/projects/DataCenter/views/plugin_3/metaService/metaServiceOutPut'
import modelService from '@/projects/DataCenter/views/plugin_3/modelService/index'
import localModelService from '@/projects/DataCenter/views/plugin_3/localModelService/index'
import compareServiceModelService from '@/projects/DataCenter/views/plugin_3/compareServiceModelService/index'
import peerContentMeta from '@/projects/DataCenter/views/plugin_3/peerContentMeta/index'
import infoVerificationModelService from '@/projects/DataCenter/views/plugin_3/infoVerificationModelService/index'
import metaServiceCheckOutPut from '@/projects/DataCenter/views/plugin_3/metaService/metaServiceCheckOutPut'
import ruleExecutor from '@/projects/DataCenter/views/plugin_3/ruleExecutor/ruleExecutor'

import kafkaInput from '@/projects/DataCenter/views/plugin_3/kafkaInput/index'
import kafkaOutput from '@/projects/DataCenter/views/plugin_3/kafkaOutput/index'

import specialBusinessMeta from '@/projects/DataCenter/views/plugin_3/specialBusinessMeta/index'


export default {
    name: "params",
    data() {
        return {

        };
    },
    components : {
        cicadaKafkaInputMeta  : kafkaInput,//kafka输入
        cicadaKafkaOutputMeta  : kafkaOutput,//kafka输出
        cicadaCodeTableConversion : cicadaCodeTableConversion,//代码转换插件
        cicadaPeerContentMeta : peerContentMeta,//同行插件
        cicadaAiModelService : modelService, //模型服务
        cicadaDataCollisionModelService : localModelService,
        cicadaAnalysisModelService : localModelService,
        cicadaInfoCheckModelService: localModelService,
        cicadaCompareServiceModelService : compareServiceModelService,
        cicadaInfoVerificationModelService : infoVerificationModelService,
        cicadaInnerJoinPlugin, //交集比对
        cicadaFileInputMeta : fileUpLoadPlugin , //文件上传
        cicadaSubtractByKeyPlugin, //差集
        cicadaUnionJoinPlugin, //并集 合并列
        cicadaCollisionPlugin, //合并行
        cicadaJsonParsingContent:jsonParsing,//json解析
        cicadaObjectToJsonMeta:objectToJson,//字段转对象
        cicadaScriptMeta:mlSqlScript,//脚本插件
        cicadaManyJoinMeta:manyJoinPlugin,//多join
        //addFieldPluginMeta:addFieldPlugin,
        cicadaDateCicadaZipperTablePlugin:dateZipperTablePlugin,//时间拉链表
        cicadaNumberCicadaZipperTablePlugin:numberZipperTablePlugin,//数值拉链表
        cicadaServiceOrganization , //表达式处理
        cicadaStandardSqlOutput:cicadaStandardSqlOutput ,//关系库表输出
        cicadaDataDistinctMeta:removeDuplicateDataPlugin,//数据去重
        cicadaFullTextOutput,//全文库输出
        cicadaReducePlugin,//分组统计
        cicadaDataSortPlugin:cicadaDataSort,//数据排序
        cicadaLabelMeta:dataMarking,//数据打标
        cicadaConditionFilterPlugin:conditionFilterPlugin,//条件过滤
        cicadaFieldsSettings,//字段設置
        cicadaJwqMapperMeta:jwqMapper,//警务区计算
        cicadaEffectivePolice:efficitivePolicePlugin,//24小时有效警情
        cicadaFieldFilteringMeta:fieldFilteringPlugin,//字段过滤

        cicadaServiceInputMeta:ServiceInputPlugin,//请求服务专用输入
        cicadaRadarChartsPlugin : RadarAttr,//雷达图
        cicadaPieChartsPlugin : PieAttr,//饼图
        cicadaBarChartsPlugin : HistogramAttr,//柱状图
        cicadaLineChartsPlugin : LineAttr,//折线图
        cicadaScatterChartsPlugin : ScatterAttr,//散点图
        // cicadaFullTextInput : FullTextInput,//全文库输入
            // GanttAttr,//甘特图
        cicadaDlwzMapperMeta : GeoMappingPlugin,//标准地址映射
        cicadaNormalizationMeta : Normalization, //归一化插件
        cicadaMarkingTimeMeta :MarkingTime,//时间打标插件
        cicadaPeerPluginMeta : PeerPlugin,//同行插件
        cicadaRowDenormaliserMeta: rowDenormailiser,//行转列插件
        cicadaPyTaskRunnerMeta: algorithm,//py执行插件
        cicadaStandardSqlInput : StandardSqlInput,//关系库输入
        cicadaKVInput : KvBaseInput,//kv库输入
        cicadaFullTextInput : FullTextInput,//全文库输入
        cicadaFileOutputMeta : FileDownLoadPlugin,//文件输出
        serviceOrganization : ExcavateExpressionExcuter , //算子编排

        cicadaMetaServiceInput : metaServiceInput , //API请求入参
        cicadaMetaServiceOutput : metaServiceOutPut , //API响应结果
        cicadaMetaServiceCheckOutPut : metaServiceCheckOutPut,
        ruleExecutorPlugin: ruleExecutor, //融合规则插件
        cicadaSpecialBusinessMeta : specialBusinessMeta,//特征离线计算
    },
    props: {
        sourceData: Object,
        rowData: Object ,
        loadParams : Object ,
        previewObj:Object,
        hideMsg : {
            type : Boolean ,
            default : false
        },
        isCase : {
            type : Boolean,
            default : false
        }
    },
    methods: {
        /**
         * 设置预览按钮置灰
         */
        setPreviewAble() {
            this.$emit('setPreviewAble');
        },
        save(saveName){
            this.$refs.params.save(saveName);
        },
        preview(){
            this.$refs.params.preview();
        },
        downLoad(){
            this.$refs.params.downLoad();
        },
        runScript(){
            this.$refs.params.runScript();
        }
    },
    created() {
    }
};
</script>
<style lang="less" scoped>
.params {
    height: 100%;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    overflow: auto;
    flex: 1;
    > * {
        width: 100%;
    }
    &__title {
        display: inline-block;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 12px;
        position: relative;
        &::before {
            content: "*";
            position: absolute;
            top: 0;
            left: calc(100% + 6px);
            color: #f5222d;
        }
    }
    &__condition-row {
        margin-bottom: 6px;
    }
    &__condition-col {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    &__prev {
        margin-top: auto;
    }
}
</style>
