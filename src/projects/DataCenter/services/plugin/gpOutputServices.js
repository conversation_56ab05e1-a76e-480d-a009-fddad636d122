/**
 * gp输出 插件 接口
 * @Author: wangjt
 * @Date: 2020-07-02
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/gpOutPlugin/";


/*
* 初始化
* @params tranStepId
* @returns {Promise<*>}
* */
export async function gpOutPage( tranStepId , settings) {
    return await reqUtil.get({
        url: root + 'gpOutPage' ,
        settings : settings
    }, {tranStepId});
}

/*
* 保存
* @params datavo
* @returns {Promise<*>}
* */

export async function savePlugin(stepId  , dataVo , settings) {
    return await reqUtil.post({
        url: root + 'savePlugin?stepId=' + stepId ,
        settings : settings
    }, dataVo);
}