<template>
    <div class="taskLogDetail">
        <common-dialog
                custom-class="log"
                   :width="width"
                   :title="dialogTitle"
                   :visible.sync="dialogVisible"
                   @closed="clearD"
        >
            <LogDetailPanel v-if="reNew" :taskData = "taskData" ref="logDetailPanel"/>
        </common-dialog>
    </div>
</template>

<script>
    import LogDetailPanel from './LogDetailPanel'
    export default {
        name: "TaskLogDetail" ,
        components : {
            LogDetailPanel
        },
        data(){
            return {
                dialogTitle : '日志详情',
                width : '900px' ,
                dialogVisible :false ,
                reNew :false,
                taskData:{},
            }
        },
        methods : {

            show(row){
                // console.log("详情：",row)
                this.taskData = row;
                this.dialogVisible = true;
                this.reNew = true;
            },
            close(){
                this.dialogVisible = false;
            },
            clearD(){ //清空操作数据
                this.reNew = false;
                this.$refs.logDetailPanel.clearTime();
            }
        }
    }
</script>

<style scoped>

</style>