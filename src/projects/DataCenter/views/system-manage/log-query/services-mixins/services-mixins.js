import {commonMixins} from "@/api/commonMethods/common-mixins";

export const servicesMixins = {
    mixins : [commonMixins],
    data(){
        let logQueryServices = require("@/projects/DataCenter/services/system-manage-services/log-query-services") ,
            logQueryMock = require("@/projects/DataCenter/mock/system-manage-mock/log-query-mock");
        return {
            logQueryServices,
            logQueryMock,
            logServices : this.getServices(logQueryServices , logQueryMock)
        }
    }
}
