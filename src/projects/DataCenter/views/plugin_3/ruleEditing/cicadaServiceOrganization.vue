<template>
    <div class="cicadaServiceOrganization">
        <params-page ref="page" table-h="100%" :inputColumns="inputColumns" :rowData="rowData"></params-page>
        <preView ref="preview"></preView>
    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import paramsPage from "@/projects/DataCenter/views/plugin_3/ruleEditing/params-page"
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
export default {
    name: "cicadaServiceOrganization",
    mixins: [pluginMixins, servicesMixins],
    components: {paramsPage,preView},
    props: {
        sourceData: Object,
        loadParams: Object,
        rowData : Object
    },
    data() {
        return {
            btnName: "表达式配置",
            inputColumns: [],
            outputColumns: [],
            fieldTypes: [],
            width: "1200px",
            listVo: []
        }
    },
    methods: {
        /**
         * 显示配置弹窗
         */
        showPanel() {
            const vm = this;
            const {fieldTypes, inputColumns, outputColumns} = this;
            this.$nextTick(() => {
                vm.$refs.page.initData(fieldTypes, inputColumns, outputColumns);
            })

        },
        /**
         * 初始化
         * */
        init() {
            const vm = this, {services, sourceData, loadParams} = vm;
            loadParams.loading = true;
            services.operatorPage(sourceData.id, loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.inputColumns = result.inputColumn.map(col => {
                        col.label = col.columnName || col.columnCode;
                        col.value = col.columnId;
                        return col;
                    });
                    vm.outputColumns = result.outputColumns;
                    vm.fieldTypes = result.types;
                    if(result.outputColumns.length) vm.$emit("setPreviewAble");
                    vm.showPanel();
                }
            })
        },
        /**
         * 保存
         */
        async save(saveName,showTip) {
            const vm = this, {services, sourceData, loadParams} = vm;
            let list = vm.$refs.page.tableData;
            vm.$refs.page.validate(async valid => {
                if (valid) {
                    vm.outputColumns = list;
                    let params = vm.setParamsVo();
                    if (!params.length) {
                        // vm.$message.warning("暂无配置数据");
                        return;
                    }
                    loadParams.loading = true;
                    let {data} = await services.saveOperatorExp(sourceData.id, {cicadaServiceOrgOutputs: params}, loadParams);
                    if(data.status === 0){
                        showTip && vm.$message.success("保存成功");
                        vm.$emit("setPreviewAble");
                        if(saveName)saveName();
                    }
                }
            })


        },
        /**
         * 保存参数
         *
         * columnName 输出字段名称
         * columnCode 输出字段Code
         * columnId 输出字段id
         * type 输出字段类型
         * length 输出字段长度
         * precision 输出字段精度
         * columnAliasCode 输出字段别名
         * expressionScript 表达式脚本
         * isCommonUseOperator 是否常用表达式
         * commonUseOperatorParams 常用表达式参数
         * {
         *     ParamId 参数id
         *     paramName 参数中文名称
         *     paramCode 参数英文名称
         *     paramValue 参数值
         * }
         * usedOperatorId 常用表达式算子id
         *
         * */
        setParamsVo() {
            const vm = this, {outputColumns} = vm;
            return outputColumns.map(li => {
                return {
                    columnName: li.columnName,
                    columnCode: li.columnCode,
                    columnId: li.columnId,
                    type: li.type,
                    length: li.length,
                    precision: li.precision,
                    expressionScript: li.expressionScript,
                    isCommonUseOperator: li.isCommonUseOperator,
                    commonUseOperatorParams: li.commonUseOperatorParams,
                    usedOperatorId: li.usedOperatorId,
                    ifCustomInput: li.ifCustomInput,
                }
            });
        },
        /**
         * 预览
         */
        preview(){
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
    },
    mounted() {
        this.init();
    }
}
</script>
<style lang="less" scoped src="../../plugin/css/plugin.less"></style>
<style scoped>
.cicadaServiceOrganization {
    width: 100%;
    height: 100%;
}

.ce-normal_item {
    text-align: left;
}
</style>
