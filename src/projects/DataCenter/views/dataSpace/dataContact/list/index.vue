<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <div class="ce-table_btns">
                <dg-button class="mr5" type="primary" @click="addContact('sign')"
                    v-if="treeNode && treeNode.level && rights.indexOf(addDataObj) > -1">{{ addDataSourceTxt
                    }}</dg-button>
                <!-- 暂时隐藏3.7.0-->
                <!--                <dg-button slot="reference"  @click="addContact('signed')" v-show="treeNode && treeNode.level  && rights.indexOf(addDataObjImport) > -1">{{fromDataSourceTxt}}</dg-button>-->

                <!--<el-popover placement="bottom" class="ml5" width="430" trigger="hover" v-show="treeNode.level!=1&&rights.indexOf(addDataObj) > -1"-->
                <!--:content="dataSourceTip">-->
                <!--<dg-button slot="reference"  @click="addContact('signed')">{{fromDataSourceTxt}}</dg-button>-->
                <!--</el-popover>-->
                <el-button v-if="userInfo.objCode === $userCode" @click="showResultAnalysis">{{ resultAnalysisTxt
                    }}</el-button>

            </div>
            <!--<dg-select v-model="da" :data="data"> </dg-select>-->
            <div class="data-contact__search">
                <el-row type="flex" :gutter="10">
                    <el-col :span="8">
                        <dg-select v-model="selectType" :data="selectData" @change="changePage(1)"> </dg-select>

                    </el-col>
                    <el-col :span="16">
                        <el-input size="mini" placeholder="请输入数据源名称搜索" v-model.trim="inputValueTable" v-input-limit:trim
                            @input="inputFilterSpecial($event, 'inputValueTable')"
                            @keyup.enter.native="searchTableEvent">
                            <i class="el-icon-search el-input__icon poi" slot="suffix" @click.stop="searchTableEvent">
                            </i>
                        </el-input>
                    </el-col>
                </el-row>

            </div>
        </div>
        <div class="d-s-r-table" v-if="!settings.loading">
            <common-table :data="tableData" :columns="tHeadData" :paging-type="isMock ? 'client' : 'server'"
                :pagination-props="paginationProps" class="width100" :border="false" noneImg :max-height="tableBodyH"
                :pagination-total="total" @change-current="isMock ? () => { } : changePage($event)"
                @change-size="changeSize">
                <template slot="name" slot-scope="{row }">
                    <div class="detailName poi" @click="detailShow(row)">{{ row.name }}</div>
                </template>
                <template slot="instanceType" slot-scope="{row }">
                    <span>{{ row.instanceType ? typeMap[row.instanceType.toUpperCase()] : '' }}</span>
                </template>
                <template slot="connectStatus" slot-scope="{row , $index}">
                    <span>
                        <i :title="row.connectStatus ? '连接成功' : '连接失败'" class="dg-iconp poi"
                            :class="[row.connectStatus ? 'dg-iconp icon-f-circle-check status-success' : 'el-icon-warning status-error']"></i>
                        <!-- <i class="el-icon-circle-check end_time success" v-if="row.execute_status === 'SUCCESS'"></i>
                        <i class="el-icon-circle-check end_time" v-else></i> -->
                        <!-- {{row.end_time}} -->
                    </span>
                    <!-- <span v-else>-</span> -->
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action" v-for="(item, index) in hasRightOptIcon" :key="index"
                        v-if="index < 3 && item.show(row)">
                        <el-button type="text" @click="item.clickFn(row, $index)" :title="item.name">{{ item.name
                            }}</el-button> <!--v-html="item.icon"-->
                    </span>
                    <dir-edit-action v-if="countHasRightMenu > 3" placement="bottom"
                        @command="menuCommand($event, row, $index)" :data="hasRightOptIcon.slice(3)" :node="row"
                        :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>更多</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
            </common-table>
        </div>
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>
