<template>
    <div class="main">
        <div class="dataSetTreeClass">
            <el-form :model="selectForm">
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="查询：" label-width="120px">
                            <el-input placeholder="请输入名称" v-model="selectForm.selectName" @input="querySparkTable" size="mini" style="margin-top: 5px">
                                <el-button slot="append" icon="el-icon-search" @click="querySparkTable"></el-button>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="缓存级别：" label-width="120px">
                            <el-select v-model="selectForm.cacheLevel" size="mini" @change="querySparkTable">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="dataSetTableClass">
            <el-table :data="tableData" height="100%">
                <el-table-column prop="globalName" label="全局名称"></el-table-column>
                <el-table-column prop="engName" label="英文名称"></el-table-column>
                <el-table-column prop="chName" label="中文名称"></el-table-column>
                <el-table-column prop="cacheLevel" label="缓存级别"></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
    export default {
        name: "PreviewSparkTable",
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        data() {
            return {
                options: [
                    {
                        value: "",
                        label: "全部"
                    },
                    {
                        value: "DISK_ONLY",
                        label: "硬盘"
                    },
                    {
                        value: "MEMORY_ONLY",
                        label: "内存"
                    },
                    {
                        value: "NONE",
                        label: "无缓存"
                    }
                ],
                selectForm: {
                    selectName: "",
                    cacheLevel: "",
                },
                tableData: [
                    /*{
                        globalName: "",
                        engName: "",
                        chName: "",
                        cacheLevel: "",
                    }*/
                ],
                filterText: "",
                data: [
                    /*{
                        id: 1,
                        label: '一级 1',
                        children: [{
                            id: 4,
                            label: '二级 1-1',
                            children: [{
                                id: 9,
                                label: '三级 1-1-1'
                            }, {
                                id: 10,
                                label: '三级 1-1-2'
                            }]
                        }]
                    }*/
                ],
                defaultProps: {
                    children: 'children',
                    label: 'name'
                },

            }
        },
        methods: {
            querySparkTable() {
                this.getSparkTable(this.selectForm.selectName, this.selectForm.cacheLevel);
            },
            init() {
                this.getSparkTable("", "");
            },
            getSparkTable(globalCode, cacheLevel) {
                let data ={
                    globalCode:globalCode,
                    cacheLevel:cacheLevel,
                };
                this.$axios.post("/manage/sql/getSparkTablesInfo",data).then(res => {
                    if (res.data.status === 0) {
                        let data = res.data.data.dataList;
                        this.tableData = [];
                        data.forEach(item => {
                            this.tableData.push(
                                {
                                    globalName: item.globalCode,
                                    engName: item.tableName,
                                    chName: item.tableZhName,
                                    cacheLevel: item.cacheLevel === null ? "无缓存" : item.cacheLevel,
                                }
                            )
                        })
                    } else {
                        this.$message.error("查询Spark注册表失败！");
                    }
                }).catch(err => {
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                })
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.name.indexOf(value) !== -1;
            }
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>
    /*.dataSetTreeClass{*/
    /*    float: left;*/
    /*    margin: 0px 0 0 10px;*/
    /*    width: 230px;*/
    /*    padding: 10px;*/
    /*    background: #fff;*/
    /*    border: 1px solid #ddd;*/
    /*    height: calc(100% - 88px);*/
    /*}*/
    .dataSetTableClass{
        background: #fff;
        margin: 10px 10px 0 0;
        padding: 10px;
        height: calc(100% - 88px);
        border: 1px solid #ddd;
        overflow: auto;
    }
    .main {
        position: relative;
        height: calc(100vh - 124px);
    }
    .filter-tree {
        height: calc(100% - 40px);
        overflow: auto;
    }


</style>