<template>
    <div class="filterTime">
        <el-form size="mini">
            <el-form-item>
                <el-radio v-model="filterResult" label="default">
                    <span class="ce-radio_txt">{{defaultCheckTxt + ' : '}}</span>
                    <el-select class="ce-radio_select" v-model="checkedVal" @change="defaultChange">
                        <el-option
                                v-for="opt in defaultOption"
                                :key="opt.value"
                                :label="opt.label"
                                :value="opt.value"></el-option>
                    </el-select>
                </el-radio>
            </el-form-item>
            <el-form-item>
                <el-radio v-model="filterResult" label="custom">
                    <span class="ce-radio_txt">{{customTxt + ' : '}}</span>
                    <el-radio-group v-show="filterResult === 'custom'" v-model="customWay">
                        <el-radio-button
                                v-for="radio in wayOptions"
                                :key="radio.value"
                                :label="radio.value">{{radio.label}}
                        </el-radio-button>
                    </el-radio-group>
                </el-radio>
                <div class="mt15" v-show="filterResult === 'custom'">
                    <el-date-picker
                            v-show="customWay === 'fix'"
                            v-model="dateTime"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                    <div class="ce-input_select" v-show="customWay === 'near'">
                        <el-input :placeholder="placeholder"
                                  v-model.number="nearValue"
                                  @keyup.native="typeCheck(nearValue)"
                                  class="input-with-select">
                            <el-select class="ce-select_in" v-model="timeType" slot="append">
                                <el-option v-for="opt in timeOptions" :key="opt.value" :label="opt.label"
                                           :value="opt.value"></el-option>
                            </el-select>
                            <span slot="prefix" class="ce-pre_txt">近</span>
                        </el-input>
                    </div>
                </div>


            </el-form-item>
        </el-form>

    </div>
</template>

<script>
    export default {
        name: "FilterTime",
        data() {
            return {
                defaultCheckTxt: '快速选择',
                customTxt: '自定义',
                checkedVal: 12*60*60*1000,
                defaultOption: [
                    {
                        label: '近12个小时',
                        value: 12*60*60*1000
                    }, {
                        label: '近1天',
                        value: 24*60*60*1000
                    }, {
                        label: '近3天',
                        value: 3*24*60*60*1000
                    }, {
                        label: '近1周',
                        value: 7*24*60*60*1000
                    }, {
                        label: '近1个月',
                        value: 30*24*60*60*1000
                    }, {
                        label: '近3个月',
                        value: 90*24*60*60*1000
                    },
                ],
                filterResult: 'default',
                customWay: 'fix',
                wayOptions: [
                    {
                        label: '固定时间区间',
                        value: 'fix'
                    }, {
                        label: '滚动时间区间',
                        value: 'near'
                    }
                ],
                dateTime: '',
                placeholder: '请输入时间数',
                nearValue: 0,
                timeType: 60*1000 ,
                timeOptions: [
                    {
                        label: '分钟',
                        value: 60*1000
                    }, {
                        label: '小时',
                        value: 60*60*1000
                    }, {
                        label: '天',
                        value: 24*60*60*1000
                    }, {
                        label: '月',
                        value: 30*24*60*60*1000
                    },
                ]
            }
        },
        methods: {
            typeCheck( val ){
                if( val === '' || isNaN(val)) {
                    this.nearValue = 0;
                    this.$message.warning('请输入数字')
                }
            },
            defaultChange(value) {
                this.filterResult = 'default';
            },
            getResult() {
                let result, type , cusType;
                type = this.filterResult;
                if (this.filterResult === 'default') {
                    result = this.checkedVal;
                } else {
                    cusType = this.customWay;
                    if (this.customWay === 'fix') {
                        result = this.getDateTime();
                    } else if (this.customWay === 'near') {
                        result = {
                            cusVal : this.nearValue ,
                            cusUnit : this.timeType
                        };
                    }
                }
                if(!result){
                    this.$message.warning('请选择时间范围!');
                    return false;
                } else {
                    return {
                        type: type ,
                        cusType : cusType ,
                        result : result
                    };
                }
            },
            initValue(data){
                if( !data.type ) return;
                this.transformValue(data);
            },
            getDateTime(){
                let result ;
                result = this.dateTime ? [
                    this.dateTime[0].getTime() ,this.dateTime[1].getTime()
                ] : '';
                return result;
            },
            transformValue(data){
                this.filterResult = data.type;
                if (data.type === 'default' ) {
                    this.checkedVal = data.result;
                }else if(data.type === 'custom'){
                    this.customWay = data.cusType;
                    if (data.cusType === 'fix') {
                        this.dateTime =[new Date(data.result[0]) , new Date(data.result[1])];
                    } else if ( data.cusType === 'near') {
                        this.timeType = data.result.cusUnit ;
                        this.nearValue = data.result.cusVal;
                    }
                }
            }
        },
        computed: {}
    }
</script>

<style scoped>
    .ce-radio_txt {
        display: inline-block;
        line-height: 28px;
        margin-right: 10px;
        vertical-align: middle;
    }

    .ce-radio_select {
        display: inline-block;
        width: 192px;
    }

    .ce-select_in {
        width: 90px;
    }

    .ce-pre_txt {
        line-height: 26px;
        color: #555;
    }

    .ce-input_select {
        width: 292px;
    }
</style>
