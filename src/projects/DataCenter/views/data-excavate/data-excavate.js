import {servicesMixins} from "./service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi/index.vue"
import Tree from "./tree"
import List from "./list"
import NewModel from "./dialog/NewModel";
import ModelTabs from "./dialog/ModelTabs";
import RenameDailog from "./dialog/RenameDailog"
import ModelEditDailogVue from "./dialog/ModelEditDailog.vue";
import OpenModel from './dialog/OpenModel'
import preview from "./dialog/preview"
import share from "./dialog/share"
import Issue from "./dialog/issue"
export default {
    name: "modeling" ,
    mixins : [servicesMixins , commonMixins],
    components : {
        TreeAndList ,
        Tree ,
        List,
        NewModel,
        ModelTabs,
        RenameDailog,
        ModelEditDailogVue,
        OpenModel,
        preview ,
        share,
        Issue
    },
    data(){
        return {
            newModel: false, //新建模型
            modelTabs: false,
            treeData:[],
        }
    },
    methods : {
        serveIssue(row){
            this.$refs.issue.show(row)
        },
        shareFn(row){
            this.$refs.share.show(row);
        },
        previewResult(row){
            this.$refs.preview.show(row);
        },
        moveTo(row , type , operType){
            this.$refs.move.open(row, type , operType);
        },
        setTreeData(data) {
            this.treeData = data;
        },
        treeDir(value, id , node) {
            this.$refs.modelingCenterTable.setDirType(value, id , node);
        },
        initTable(data){
            this.$refs.modelingCenterTable.deleteInit(data);
        },
        openNewModel(dirId) {
            this.addModelTab(false , dirId);
           // this.newModel = true;
        },
        closeNewModel() {
            this.newModel = false;
        },
        openModelTabs() {
            this.modelTabs = true;
        },
        closeModelTabs() {
            this.modelTabs = false;
        },
        backTo() {
            //返回
            this.closeNewModel();
            this.closeModelTabs();
            this.$refs.modelingCenterTable.changePage(1);
        },
        addModelTab(isTemplate , dirId) {
            const vm = this;
            this.openModelTabs();
            this.closeNewModel();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab({},!!isTemplate ,dirId );
            })
        },
        addModule(data){
            const vm = this;
            this.closeNewModel();
            this.openModelTabs();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab(data);
            })

        },
        update(){
            this.$refs.modelingCenterTable.changePage(1);
        },
        dialogRenameEmit(newName, index,transId) {
            // this.$refs.modelPage.reName(newName,transId);
            this.$refs.modelingCenterTable.tDataChangeWithName(newName, index);
        },
        rename(row, index) {
            this.$refs.refRenameDialog.setName(row, index);
            this.$refs.refRenameDialog.show();
        },
        onTableItemEvent(row) {
            this.$refs.modelEditDailog.show(row);
        },
        modelEdit(rowData) {
            const vm = this;
            this.openModelTabs();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab(rowData , false);
            })
        },
    }
}