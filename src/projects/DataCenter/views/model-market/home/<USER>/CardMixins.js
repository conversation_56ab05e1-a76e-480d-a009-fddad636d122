export const CardMixins = {
    methods: {
        /**
         * 转换卡片数据
         */
        transCardList(data) {
            if (!data) return [];
            return data.map(item => {
                return {
                    ...item,
                    id : item.id,
                    type: item.type,
                    name: item.resourceName,
                    focuscount: item.focusCount,
                    browsecount: item.browserCount,
                    avgscore: item.score ? parseFloat(item.score.toFixed(2))  : '0',
                    label : item.labels && item.labels.map(lab => lab.labelName) ,
                    introduction : item.modelDesc ,
                    userName : item.releaseUser || "-" ,
                    publishtime : item.releaseTime || "-"
                };
            })
        }
    }
}
