<template>
    <div class="attrCont">
        <div class="ce-plug_box">
            <component :sourceData="sourceData" :rowData="rowData" :row="rowData" :is="sourceData.keyWord"></component>
        </div>

    </div>
</template>

<script>
    // import AcrossLibrary from '@/projects/DataCenter/views/plugin/metaDataInput/acrossLibrary/AcrossLibrary'
    import removeDuplicateDataPlugin from "@/projects/DataCenter/views/plugin/removeDuplicateDataPlugin/removeDuplicateData"
    import effictivePolicePlugin from "@/projects/DataCenter/views/plugin/effictivePolicePlugin/EffictivePolicePlugin"
    import ServiceInputPlugin from "@/projects/DataCenter/views/plugin/serviceInput/index"
    import SubtractByKey from '@/projects/DataCenter/views/plugin/collectionOperations/SubtractByKey'
    import ScatterAttr from '@/projects/DataCenter/views/plugin/resultOutput/ScatterAttr'
    import GanttAttr from '@/projects/DataCenter/views/plugin/resultOutput/GanttAttr'
    import RadarAttr from '@/projects/DataCenter/views/plugin/resultOutput/RadarAttr'
    import PieAttr from '@/projects/DataCenter/views/plugin/resultOutput/PieAttr'
    import HistogramAttr from '@/projects/DataCenter/views/plugin/resultOutput/HistogramAttr'
    import LineAttr from '@/projects/DataCenter/views/plugin/resultOutput/LineAttr'
    import StandardSqlInput from '@/projects/DataCenter/views/plugin/metaDataInput/standardSql/StandardSqlInput'
    import KvBaseInput from '@/projects/DataCenter/views/plugin/metaDataInput/keyValue/KvBaseInput'
    import FullTextInput from '@/projects/DataCenter/views/plugin/metaDataInput/fullText/FullTextInput'
    import LeftOrRightJoin from '@/projects/DataCenter/views/plugin/collectionOperations/LeftOrRightJoin'
    import FullJoin from '@/projects/DataCenter/views/plugin/collectionOperations/FullJoin'
    import InnerJoin from '@/projects/DataCenter/views/plugin/collectionOperations/InnerJoin'
    import UnionJoin from '@/projects/DataCenter/views/plugin/collectionOperations/UnionJoin'
    import CollisionPlugIn from '@/projects/DataCenter/views/plugin/collectionOperations/CollisionPlugIn'

    import FullTextBase from '@/projects/DataCenter/views/plugin/metaDataOutput/fullText/FullTextBase'
    import KvBase from '@/projects/DataCenter/views/plugin/metaDataOutput/keyValue/KvBase'
    import RelationBase from '@/projects/DataCenter/views/plugin/metaDataOutput/standardSql/RelationBase'

    import ClassificationCounting from '@/projects/DataCenter/views/plugin/classificationCounting/ClassificationCounting'
    import StreamingDispose from '@/projects/DataCenter/views/plugin/streamingDispose/streaming/StreamingDispose.vue'
    import SamplingAndShunting from '@/projects/DataCenter/views/plugin/processControl/SamplingAndShunting'
    import StartProgram from '@/projects/DataCenter/views/plugin/processControl/StartProgram'

    import ExcavateExpressionExcuter from '@/projects/DataCenter/views/plugin/ruleEditing/ExcavateExpressionExcuter'
    import ZipperTable from '@/projects/DataCenter/views/plugin/zipperTablePlugin/zipperTablePlugin'
    import DateZipperTable from '@/projects/DataCenter/views/plugin/zipperTablePlugin/dateZipperTablePlugin'
    import NumberZipperTable from '@/projects/DataCenter/views/plugin/zipperTablePlugin/numberZipperTablePlugin'
    import ConditionFilterPlugin from '@/projects/DataCenter/views/plugin/conditionFilterPlugin/conditionFilterPlugin'

    // import RuleEditingPanel from '@/projects/DataCenter/views/plugin/ruleEditing/RuleEditingPanel'
    import FtpUpload from "@/projects/DataCenter/views/plugin/ftpUpload/FtpUpload.vue"
    import csvOutput from "@/projects/DataCenter/views/plugin/metaDataOutput/csv/csvOutput.vue"
    import gbaseOutput from "@/projects/DataCenter/views/plugin/metaDataOutput/gbase/gbaseOutput.vue"
    import gpOutput from "@/projects/DataCenter/views/plugin/metaDataOutput/greenplum/gpOutput.vue"
    import dataSortPlugin from "@/projects/DataCenter/views/plugin/dataSortPlugin/index"
    import AddressClean from "@/projects/DataCenter/views/plugin/addressCleanPlugin/AddressClean"
    import LabelPlugin from "@/projects/DataCenter/views/plugin/labelPlugin/LabelPlugin.vue"
    import PersonCreatorPlugin from "@/projects/DataCenter/views/plugin/personCreatorPlugin/PersonCreatorPlugin.vue"
    import GeoMappingPlugin from "@/projects/DataCenter/views/plugin/geoMappingPlugin/GeoMappingPlugin.vue"
    import ElementLinkPlugin from "@/projects/DataCenter/views/plugin/elementLinkPlugin/ElementLinkPlugin.vue"
    import MlSqlScript from '@/projects/DataCenter/views/plugin/mlSqlScriptr/MlsqlScript'
    import RowDenormaliser from '@/projects/DataCenter/views/plugin/rowDenormaliser/RowDenormaliser'
    import FieldFilteringPlugin from "@/projects/DataCenter/views/plugin/FieldFilteringPlugin/FieldFilteringPlugin";
    import JsonParsing from "@/projects/DataCenter/views/plugin/jsonParsing/JsonParsing"
    import JwqMapper from "@/projects/DataCenter/views/plugin/jwqMapper/JwqMapper"
    import FileUploadPlugin from "@/projects/DataCenter/views/plugin/fileUpLoadPlugin/FileUpLoadPlugin"
    import FileDownLoadPlugin from "@/projects/DataCenter/views/plugin/fileDownLoadPulgin/FileDownLoadPlugin"
    import MarkingTime from "@/projects/DataCenter/views/plugin/markingtime/MarkingTime";
    import PeerPlugin from "@/projects/DataCenter/views/plugin/peerPlugin/PeerPlugin";
    import Normalization from "@/projects/DataCenter/views/plugin/normalizationPlugin/Normalization"
    import ObjectToJson from "@/projects/DataCenter/views/plugin/objectToJsonPlugin/objectToJson"
    import Algorithm from "@/projects/DataCenter/views/plugin/algorithm/algorithm"

    export default {
        name: "AttrCont",
        props: {
            sourceData: Object,
            treeData: Array,
            rowData: Object
        },
        // keyWord : component
        components: {
            normalizationMeta : Normalization, //归一化插件
            peerPluginMeta : PeerPlugin,//同行插件
            markingTimeMeta :MarkingTime,//时间打标插件
            serviceInputMeta:ServiceInputPlugin,//数据服务输入插件
            fileOutputMeta : FileDownLoadPlugin,//文件输出
            fileInputMeta : FileUploadPlugin,//文件上传
            // dataSetPlugin:AcrossLibrary,
            subtractByKeyPlugin : SubtractByKey,//左右排除
            scatterChartsPlugin : ScatterAttr,//散点图
            // GanttAttr,//甘特图
            jsonParsingContent : JsonParsing,//json解析
            addressCleanMeta : AddressClean, //地理位置数据清洗
            jwqMapperMeta : JwqMapper,//警务区计算
            radarChartsPlugin : RadarAttr,//雷达图
            pieChartsPlugin : PieAttr,//饼图
            barChartsPlugin : HistogramAttr,//柱状图
            lineChartsPlugin : LineAttr,//折线图
            standardSqlInput : StandardSqlInput,//关系库输入
            kVInput : KvBaseInput,//kv库输入
            leftOrRightJoinPlugin : LeftOrRightJoin,//左右连接
            fullJoinPlugin : FullJoin,//全连接
            innerJoinPlugin : InnerJoin, //交集
            unionJoinPlugin : UnionJoin, //并集
            collisionPlugin : CollisionPlugIn, //碰撞插件

            serviceOrganization : ExcavateExpressionExcuter,//表达式处理
            zipperTablePlugin:ZipperTable,//拉链表
            dateZipperTablePlugin:DateZipperTable,//时间拉链表
            numberZipperTablePlugin:NumberZipperTable,//数值拉链表
            conditionFilterPlugin:ConditionFilterPlugin,//条件过滤组件

            reducePlugin : ClassificationCounting,//分组统计
            windowStreaming : StreamingDispose, //时间窗
            samplingAndShuntingPlugin:SamplingAndShunting,//采样分流
            startTaskPlugin:StartProgram,//启动任务
            fullTextOutput : FullTextBase,//全文库表输出
            kVOutput : KvBase,//kv库表输出
            standardSqlOutput : RelationBase,//关系库表输出
            fullTextInput : FullTextInput,//全文库输入
            // RuleEditingPanel,//规则编排
            fileUpload:FtpUpload,//ftp文件上传
            csvOutput :csvOutput,//csv 输出
            gbaseTableOutput : gbaseOutput,//gbase 输出
            gpOut : gpOutput,//gp 输出
            dataSortPlugin:dataSortPlugin,//排序
            scriptMeta:MlSqlScript,//脚本插件
            labelMeta : LabelPlugin,//标签插件
            dlwzMapperMeta : GeoMappingPlugin,//标准地址映射
            elementLinkMeta : ElementLinkPlugin, // 实体链接
            personCreatorMeta : PersonCreatorPlugin, //人员标准库映射
            effectivePolice : effictivePolicePlugin, // 有效警情插件
            rowDenormaliserMeta : RowDenormaliser,//行转列
            fieldFilteringMeta:FieldFilteringPlugin,//字段过滤,
            objectToJsonMeta:ObjectToJson, //字段转对象
            pyTaskRunnerMeta:Algorithm,//Ray执行插件
            dataDistinctMeta:removeDuplicateDataPlugin//数据去重插件
        },
        data() {
            return {}
        },
        methods: {
            initPreviewBtn() { //是否有存储了数据 ，才可预览

            },
            close() {
                this.$emit('closeAttr', false);
            },
        },
        created() {
            this.initPreviewBtn();
        }
    }
</script>

<style scoped>
    .attrCont {
        height: calc(100% - 2px);
        border: 1px solid #ddd;
        background: #fff;
        box-sizing: border-box;
    }

    .ce-plug_box {
        height: 100%;
    }
</style>
