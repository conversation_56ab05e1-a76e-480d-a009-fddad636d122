/**
 * 流程插件 接口
 * @Author: wangjt
 * @Date: 2020-05-27
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const area_root = "/plugin/area/";

/*
*  初始化 地区范围标识
 * @param tranStepId
 * @returns {Promise<*>}
* */
export async function query( tranStepId , settings) {
    return await reqUtil.get({
        url: area_root + 'query' ,
        settings : settings
    }, {tranStepId} );
}

/*
*  获取字段 地区范围标识
 * @param tranStepId
 * @returns {Promise<*>}
* */
export async function columns( tranStepId ) {
    return await reqUtil.get({
        url: area_root + 'columns'
    }, {tranStepId} );
}

/*
*  保存 地区范围标识
 * @param tranStepId , AreaCompare
 * @returns {Promise<*>}
* */
export async function saveArea( tranStepId , pluginMeta ,settings ) {
    return await reqUtil.get({
        url: area_root + 'save',
        settings:settings
    }, {tranStepId , pluginMeta} );
}

