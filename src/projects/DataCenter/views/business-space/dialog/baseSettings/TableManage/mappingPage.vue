<template>
    <div class="mappingPage">
        <code-form-page
                v-on="$listeners"
                :layoutForm="layoutForm"
                :form="form"
                :init-base="initBase"
                :get-target-table="getTargetTable"
                :get-code-data="getCodeData"
        >
        </code-form-page>
    </div>
</template>

<script>
import codeFormPage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/TableManage/codeFormPage";
import {outputMixins} from "@/api/commonMethods/output";

export default {
    name: "mappingPage",
    mixins:[outputMixins],
    components: {codeFormPage},
    data() {
        return {
            form: {
                base: "",
                chart: "",
                name: "",
                code: "",
                enumeration: ""
            },
            layoutForm: {
                base: {
                    label: "库选择",
                    type: 'selectTree',
                    placeholder: '请选择库',
                    option: [],
                    rules: [
                        {required: true, message: '请选择库', trigger: ['change']}
                    ]
                },
                chart: {
                    label: "码表选择",
                    type: 'select',
                    placeholder: '请选择码表',
                    option: [],
                    rules: [
                        {required: true, message: '请选择码表', trigger: ['change']}
                    ]
                },
                name: {
                    label: "编码中文名",
                    type: 'input',
                    limit: "fieldName",
                    placeholder: "请输入编码中文名",
                    rules: [
                        {required: true, message: '请输入编码中文名', trigger: ['blur', 'change']}
                    ]
                },
                code: {
                    label: "编码英文名",
                    type: 'input',
                    limit: "fieldCode",
                    placeholder: "请输入编码英文名",
                    rules: [
                        {required: true, message: '请输入编码英文名', trigger: ['blur', 'change']}
                    ]
                },
                enumeration: {
                    label: "枚举值",
                    type: "table",
                    rules: [
                        {required: true, message: '请添加枚举值', trigger: 'change'},
                    ]
                }
            },
            dbType: ["ORACLE", "GBASE","MYSQL", "POSTGRESQL", "LIBRA", "HWMPP" ,"GREENPLUM","HIVE"],

        }
    },
    methods: {
        async initBase(resolve){
            const vm = this, {services} =vm;
            let res = null; //接口
            if (!res || res.data.code !== 0) return;
            resolve(vm.resTreeDataDispose(res.data.data));
        },
        async getTargetTable(id){
            const vm = this, {services} =vm;
            return ; //接口
        },
        getCodeData(id){

        }
    },
    created() {

    }
}
</script>

<style scoped lang="less">
.ce-edit_box {
    min-height: 2rem;
    line-height: 2rem;
    border-radius: 2px;
    overflow: hidden;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.ce-edit_box:hover {
    cursor: pointer;
}
</style>
