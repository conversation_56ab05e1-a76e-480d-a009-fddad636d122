<template>
    <div class="sourceTree" v-loading="settings.loading">
        <h1 class="sourceTree_title mb5">{{ sourceTi }}</h1>
        <ce-select-drop
                class="expect-select dgSelect"
                ref="select_drop"
                :props="defaultProps"
                v-model="value"
                :data="sourcesList"
                :tree-props="{
                    'default-expanded-keys' : [value],
                    nodeKey: 'id',
                    isFrozenRoot: true,
                }"
                filterable
                check-leaf
                check-strictly
                :filterNodeMethod="filterNode"
                @current-change="handleChange"
                :placeholder="placeholder"
        >
        </ce-select-drop>
        <div class="sourceTree_dataset mb5">
            <h1 class="sourceTree_title">{{ datasetTi }}</h1>
            <el-popover popper-class="ce-popover" v-model="showAddBox" trigger="click" width="96">
                <div class="ce-tree__pop-box">
                    <div class="ce-tree__pop-item" v-for="(menu , k) in sortMenu"
                         :class="{'active' : k === sortVal}"
                         @click="setTreeSort(k)" :key="k">
                        <i class="eda_icon" :class="menu.icon"></i>
                        <span>{{ menu.label }}</span>
                    </div>
                </div>
                <i slot="reference" class="eda_icon ce-sort_icon"
                   :title="sortVal === 'letter'? letterSort : timeSort"
                   :class="{'iconsort' : sortVal === 'letter' , 'iconshijianpaixutubiao' : sortVal === 'time' }"></i>
            </el-popover>
        </div>
        <el-input v-model.trim="filterText" maxlength="25"
                  placeholder="请输入名称搜索" @keyup.enter.native="filterSet" @input="filterSet"></el-input>
        <div class="sourceTree_tree">
            <dg-scrollbar native>
                <ul class="set-list">
                    <template v-if="dataSetList.length">
                        <el-tooltip v-for="item in dataSetList" :key="item.id" class="item" effect="light" placement="right">
                            <div slot="content">
                                <p>表名：{{ item.code }}</p>
                                <p>表中文名：{{ item.name }}</p>
                            </div>
                            <li
                                    :class="['set-item',{'dragged':draggedItems.includes(item.id)}]"
                                    draggable="true"
                                    @dragstart="handleDragStart($event, item)">
                                <span> {{ item.name ? item.name : item.code }}</span>
                            </li>
                        </el-tooltip>
                    </template>
                    <empty v-else ></empty>
                </ul>
            </dg-scrollbar>
        </div>
    </div>
</template>

<script>
import {listMixins} from "@/api/commonMethods/list-mixins";
import {treeMethods} from "@/api/treeNameCheck/treeName";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import {mapGetters} from "vuex";
import {outputMixins} from "@/api/commonMethods/output";
import {globalBus} from "@/api/globalBus";

export default {
    name: "left",
    mixins: [listMixins, servicesMixins, commonMixins, outputMixins],
    data() {
        return {
            placeholder: '请选择数据源',
            sourceTi: "选择数据源",
            datasetTi: "选择数据集",
            value: '',
            filterText: '',
            sourcesList: [],
            dataSetList: [],
            defaultProps: {
                value: 'id',
                label: 'label',
                children: 'children',
                title: 'label',
            },
            showAddBox: false,
            sortMenu: {
                time: {label: "按时间排序", icon: "iconshijianpaixutubiao"},
                letter: {label: "按字母排序", icon: "iconsort"}
            },
            sortVal: "time",
            timeSort: "按时间排序",
            letterSort: "按字母排序",
            dbType: ["GREENPLUM", "ORACLE", "POSTGRESQL", "hwmpp", "Mysql" , "HIVE","TBASE" , "vertica","gbase"],
            draggedItems : []
        }
    },
    computed: {
        ...mapGetters(["userInfo", "userRight"]),
        rights() {
            const vm = this;
            let rights = [];
            if (vm.userRight) rights = vm.userRight.map(r => r.funcCode);
            return rights;
        },
    },
    created() {
        this.getDataSource();
        this.bindGlobalFn();
    },
    beforeDestroy() {
        this.offGlobalFn();
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        treeSortByWay: treeMethods.methods.treeSortByWay,
        sortInTime: treeMethods.methods.sortInTime,
        sortInLetter: treeMethods.methods.sortInLetter,
        setTreeSort(val) {
            this.showAddBox = false;
            this.sortTreeData(val);
        },
        async sortTreeData(val , data) {
            const vm = this;
            vm.sortVal = val;
            let sortList =  data || vm.dataSetList;
            if (val === 'time') {
                vm.dataSetList =  vm.treeSortByWay(sortList , 'opTime', 'sortInTime', true);
            } else if (val === 'letter') {
                vm.dataSetList =  vm.treeSortByWay(sortList, 'name', 'sortInLetter', true);
            }
        },
        filterSet() {
            let list = this.filterText !== '' ?
                    this.dataSetListCopy.filter(n => (n.name ? n.name.search(this.filterText) !== -1 : false) || n.code.search(this.filterText) !== -1) :
                    this.dataSetListCopy;
            this.sortTreeData(this.sortVal , list);
        },
        //获取数据源
        async getDataSource() {
            const vm = this, {settings, rights} = this;
            let services = vm.$services('dataSource');
            settings.loading = true;
            vm.sourcesList = [];
            let res;
            if (rights.includes("dataConnectionDataWarehouse")) {
                res = await services.getDataPreparationTree("", "", "", settings);
            } else {
                res = await services.queryDirTree("", "", "", settings);
            }
            if (res.data.code === 0) {
                let result = res.data.data;
                this.sourcesList = vm.resTreeDataDispose(result);
            }

        },
        //获取数据集
        getDataSet(n) {
            const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            let data = {
                pageSize: -1,
                pageIndex: 1,
                name: '',
                sortField: "name",
                sortOrder: "desc",
                dwDbId: n.id,
                isStdlib: false,
            };
            settings.loading = true;
            services.queryDataBaseTable(data, settings).then(res => {
                if (res.data.status === 0) {
                    let dataSetList = res.data.data.dataList ? res.data.data.dataList.map(li => {
                        li.name = li.name || li.code;
                        return li;
                    }) : [];
                    vm.dataSetListCopy = dataSetList;
                    vm.sortTreeData(vm.sortVal , dataSetList);
                }
            })
        },
        handleChange(n) {
            this.$emit("clearJoin");
            this.getDataSet(n);
        },
        handleDragStart(e, data) {
            e.dataTransfer.dropEffect = "move";
            if(this.draggedItems.includes(data.id)) {
                this.$message.warning("当前数据表已经存在,不要重复关联!")
                return;
            }
            e.dataTransfer.setData("drag_data", JSON.stringify(data));
        },
        storeDraggedData(data ,dwInstanceId){
            const vm = this;
            if(this.settings.loading) return setTimeout(()=>{vm.storeDraggedData(data,dwInstanceId)},100)
            this.draggedItems = data.map(li => li.tableId);
            if(dwInstanceId){
                this.value = dwInstanceId;
                this.$refs.select_drop.$refs.tree.setCurrentKey(dwInstanceId);
                this.getDataSet({id:dwInstanceId});
            }
        },
        bindGlobalFn(){
            globalBus.$on('as-dataset-renew' , this.storeDraggedData)
        },
        offGlobalFn(){
            globalBus.$off('as-dataset-renew' , this.storeDraggedData)
        }
    },

}
</script>

<style scoped lang="less">
.readyLeft {
    .dgSelect {
        width: 100%;

        /deep/ .el-select {
            width: 100%;
        }
    }
}

.set {
    &-list {
        padding: 0 10px 10px 0;
    }

    &-item {
        border: 1px solid #dcdcdc;
        border-radius: 5px;
        padding: 8px 10px;
        margin-top: 8px;
        cursor: move;
        box-sizing: border-box;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &.dragged {
            color: rgba(#1890ff , .7);
        }
        &:hover {
            background-color: rgba(24, 144, 255, .12);
            color: #1890ff;
        }
    }
}

.sourceTree {
    width: 266px;
    min-width: 266px;
    height: 100%;
    //border-right: 1px solid #ddd;
    padding: 12px 14px;
    box-sizing: border-box;

    &_title {
        line-height: 30px;
        font-size: 14px;
        font-weight: bold;
        color: rgba(0, 0, 0, .85);
    }

    &_dataset {
        display: flex;
        justify-content: space-between;
        line-height: 30px;
    }

    &_tree {
        height: calc(100% - 78px - 4rem);
        padding-top: 10px;
        box-sizing: border-box;
    }
}

.expect-select {
    /deep/ .el-select {
        display: block;
    }
}

</style>
