<template>
    <div class="businessInfo">
        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
            <el-form-item label="模型分类：" class="selectGroup" required>
                <el-row type="flex">
                    <el-col :span="8" class="selectType">
                        <el-form-item prop="objectType">
                            <dg-select v-model="form.objectType" filterable :data="typeDataList.objectTypeList"
                                placeholder="请选择对象类型"></dg-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" class="selectType">
                        <el-form-item prop="applyType">
                            <dg-select v-model="form.applyType" filterable :data="typeDataList.applyList"
                                placeholder="请选择应用类型"></dg-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" class="selectType">
                        <el-form-item prop="policeType">
                            <dg-select v-model="form.policeType" filterable :data="typeDataList.policeList"
                                placeholder="请选择警种类型"></dg-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8" class="selectType">
                        <el-form-item prop="caseTypeCode">
                            <dg-select v-model="form.caseTypeCode" filterable :data="typeDataList.caseTypeList"
                                placeholder="请选择案件类型"></dg-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" class="selectType">
                        <el-form-item prop="areaTypeCode">
                            <dg-select v-model="form.areaTypeCode" filterable :data="typeDataList.areaTypeList"
                                placeholder="请选择区类型"></dg-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" class="selectType">
                        <el-form-item prop="controlTypeCode">
                            <dg-select v-model="form.controlTypeCode" filterable :data="typeDataList.controlTypeList"
                                placeholder="请选择打防管控类型"></dg-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="模型标签：">
                <el-select v-model="form.label" @change="labelChange" multiple clearable remote filterable allow-create
                    ref="selectTable" :remote-method="remoteMethod" placeholder="请添加标签" default-first-option
                    @keyup.enter.native="onKeyUp">
                    <el-option :label="item.label" :value="item" v-for="(item) in labelData" :key="item.value"
                        :title="item.label"></el-option>
                </el-select>
                <!--<dg-select-->
                <!--v-model="form.label"-->
                <!--multiple-->
                <!--clearable-->
                <!--filterable-->
                <!--remote-->
                <!--allow-create-->
                <!--action="#"-->
                <!--placeholder="请添加标签"-->
                <!--:remote-method="remoteMethod"-->
                <!--:data="labelData"-->
                <!--@change="labelChange"-->
                <!--default-first-option-->
                <!--&gt;-->
                <!--</dg-select>-->
            </el-form-item>
            <el-form-item label="模型配图：">
                <dg-upload class="avatar-uploader" v-model="form.upload" list-type="viewer" action="#" multiple
                    :auto-upload="false" :limit="limit" :accept="'.png,.jpg,.jpeg,.gif'" :on-change="handleBefore"
                    :on-exceed="masterFileMax" :on-remove="removeFile" :props="{ url: 'value' }"></dg-upload>
                <div class="el-upload__tip">
                    请上传模型图片，上架后在商品详情页面显示，不超过5个。<br>
                    图片支持JPG/JPEG/PNG/GIF格式，大小不得超过2M。
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { commonMixins } from "@/api/commonMethods/common-mixins";
import { servicesMixins } from "../../service-mixins/service-mixins";

export default {
    name: "businessInfo",
    mixins: [servicesMixins, commonMixins],
    props: {
        labelList: Array,
        typeDataList: Object,
        infoList: Object,
        openType: String,
    },
    watch: {
        labelList: {
            handler(newVal) {
                const vm = this;
                vm.labelData = newVal;
                if (this.openType === 'edit')
                    vm.showLabel()
            },
            deep: true
        }
    },
    data() {
        const validateType = (msg) => (rule, value, callback) => {
            if (value === '') {
                callback(new Error(msg));
            } else {
                callback();
            }
        };
        return {
            limit: 5,
            labelData: [],
            rules: {
                objectType: [{ required: true, validator: validateType('请选择对象类型！'), trigger: 'change' }],
                applyType: [{ required: true, validator: validateType('请选择应用类型！'), trigger: 'change' }],
                policeType: [{ required: true, validator: validateType('请选择警种类型！'), trigger: 'change' }],
                caseTypeCode: [{ required: true, validator: validateType('请选择案件类型！'), trigger: 'change' }],
                areaTypeCode: [{ required: true, validator: validateType('请选择区类型！'), trigger: 'change' }],
                controlTypeCode: [{ required: true, validator: validateType('请选择打防管控类型！'), trigger: 'change' }],
            },
            form: {
                objectType: '',
                applyType: '',
                policeType: '',
                caseTypeCode: '',
                areaTypeCode: '',
                controlTypeCode: '',
                label: '',
                upload: []
            },
            files: []
        }
    },
    methods: {
        // getTitle(val){
        //     let info = val.reduce((a,b)=>{return {label:a.label+'、'+b.label}});
        //     return info.label;
        // },
        validate() {
            let flag = true;
            this.$refs['form'].validate((valid) => {
                if (valid) {
                } else {
                    flag = false;
                    return false;
                }
            })
            return flag;
        },
        //解决模型标签不显示问题
        showLabel() {
            const vm = this;
            let obj = {};
            let labels = vm.infoList.label ? vm.infoList.label.map(n => {
                return { label: n.labelName, value: n.labelId }
            }) : [];
            vm.labelData = vm.labelData.concat(labels).reduce((item, next) => {
                obj[next.value] ? '' : obj[next.value] = true && item.push(next)
                return item
            }, []);
        },
        //限制输入标签的长度
        limitLength() {
            document.querySelector('.el-select .el-select__tags input').setAttribute('maxLength', 20);
        },
        onKeyUp() {
            this.$emit('queryLabel', '')
        },
        remoteMethod(query) {
            this.labelData = [];
            if (query !== '') {
                setTimeout(() => {
                    this.$emit('queryLabel', query)
                    // this.labelData = this.labelList.filter(item => {
                    //     return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
                    // });
                }, 200);
            } else {
                this.labelData = this.labelList;
            }
        },
        labelChange(val) {
            if (val.length > 5) {
                this.form.label = val.slice(0, 5);
                this.$message.warning('最多只能设置5个标签！');
            }
            this.$emit('queryLabel', '')
        },
        //上传图片前验证
        beforeAvatarUpload(file) {
            let acceptList = ['JPEG', 'GIF', 'JPG', 'PNG'];
            const isImage = acceptList.includes(file.type.toUpperCase());
            const isLt2M = file.size / 1024 / 1024 < 2;
            let list = Object.assign([], this.form.upload);
            if (!isImage) {
                this.form.upload = list;
                this.$message.error('模型配图只能是 JPG、GIF、JPEG、PNG 格式!');
                return false;
            }
            if (!isLt2M) {
                this.form.upload = list;
                this.$message.error('模型配图大小不能超过 2MB!');
                return false;
            }
            this.form.upload.push(file);
            return isImage && isLt2M;
        },
        handleBefore(file) {
            const vm = this;
            let isCheck = vm.beforeAvatarUpload(file);
            if (!isCheck)
                return false;
            let reader = new FileReader();
            reader.readAsDataURL(file.raw);
            // 图片base64化
            reader.onload = function (e) {
                let info = {
                    pictureName: file.name.split('.')[0],
                    picture: e.target.result
                }
                vm.files.push(info);
            };
        },
        //移除文件
        removeFile(file) {
            let index = this.files.findIndex(n => n.picture == file._url);
            this.files.splice(index, 1)
        },
        masterFileMax(files, fileList) {
            if (fileList.length >= this.limit || (fileList.length + files.length) > this.limit) {
                this.$message.warning(`请最多上传 ${this.limit} 个模型配图。`);
            }
        },
    },
    created() {
        if (this.openType === 'edit') {
            let labels = this.infoList.label ? this.infoList.label.map(n => {
                return { label: n.labelName, value: n.labelId }
            }) : [];
            this.form = {
                objectType: this.infoList.objectType,
                applyType: this.infoList.applyType,
                policeType: this.infoList.policeType,
                caseTypeCode: this.infoList.caseTypeCode,
                areaTypeCode: this.infoList.areaTypeCode,
                controlTypeCode: this.infoList.controlTypeCode,
                label: labels,
                upload: this.infoList.upload,
            }
            this.files = this.infoList.upload.map(n => {
                return { pictureName: n.name, picture: n.value }
            })
        }
    },
    mounted() {
        this.limitLength()
    }
}
</script>

<style scoped lang="less">
.selectGroup {
    margin-bottom: 0;
}

.businessInfo {
    /deep/.selectType {
        flex: 1;
        min-width: 32%;
    }

    /deep/.el-row--flex {
        gap: 6px;
        flex-wrap: wrap;
    }

    .el-upload__tip {
        line-height: 18px;
    }

    /deep/ .el-upload--picture-card {
        width: 80px;
        height: 80px;
        line-height: 80px;
    }

    /deep/ .avatar-uploader-icon {
        width: 80px;
        height: 50px;
        line-height: 50px;
    }

    /deep/ .el-upload {
        overflow: hidden;

        /deep/ .dg-upload--pictureList {
            width: 80px;
            height: 80px;
            line-height: 80px;
        }

    }

    /deep/ .dg-upload--text {
        bottom: 30px;
    }

    /deep/ .dg-upload .el-upload-list.el-upload-list--picture-card>li {
        width: 64px;
        height: 64px;
    }

    /deep/ .el-select .el-tag .el-select__tags-text {
        max-width: none;
    }

    /deep/ .el-select .el-tag {
        max-width: none;
    }
}
</style>
