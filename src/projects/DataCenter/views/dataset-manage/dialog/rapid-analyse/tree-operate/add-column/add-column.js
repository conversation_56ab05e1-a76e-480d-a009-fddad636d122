import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import columnPage from "./column-page/index.vue"
import {mapGetters} from "vuex";
import {servicesMixins} from "../../../../service-mixins/service-mixins";
export default {
    name: "addColumn" ,
    mixins : [ dialog , commonMixins ,servicesMixins ],
    components : {columnPage},
    props:{
        datasetId : String
    },
    computed : {
        ...mapGetters(["userInfo"])
    },
    data(){
        return {
            title : "新增计算字段" ,
            width : "930px" ,
            indexType : '' ,
            paramsVo : {},
            canEdit : true ,
            nodeData : {} ,
            selectedData : {}
        }
    },
    methods : {
        show(indexType , nodeData){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.selectedData = nodeData;
            vm.indexType = indexType;
            vm.canEdit = true;
            vm.title = "新增计算字段";
        } ,
        showHistory(data){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.nodeData = data;
            vm.paramsVo = data.result;
            vm.indexType = data.result.indexType || 'DIMENSION';
            vm.canEdit = data.canEdit;
            vm.title = "新增计算字段(轨迹步骤)";
            vm.$nextTick(()=>{
                vm.$refs.columnPage.init(data.result ,!data.canEdit);
            })
        },
        setFields(form){
            const vm = this , {indexType ,selectedData } = this;
            let measure = [] , dimension = [];
            let item = {
                id : form.id,
                code : form.name ,
                name : form.name,
                label : form.name,
                format : form.format,
                columnAlias : form.columnAlias ,
                indexType : indexType,
                memo : form.memo,
                numberFormat : "" ,
                belongDataSetId : selectedData.belongDataSetId
            };
            if(indexType === 'DIMENSION'){
                dimension.push(item);
            }else if(indexType === 'MEASURE'){
                measure.push(item);
            }
            return [{dimension , measure , id : selectedData.belongDataSetId}];
        },
        submit(){
            const vm = this , {datasetServices , datasetMock ,datasetId ,userInfo ,settings,indexType , nodeData , selectedData } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            vm.$refs.columnPage.validate(valid => {
                if(valid){
                    let data= vm.$refs.columnPage.ruleForm ;
                    let {req_exp,expColumns} = vm.$refs.columnPage.setExp();
                    let req_data = {
                        columnCode : "",
                        indexType : indexType,
                        columnName : data.name ,
                        dataSetId : datasetId ,
                        dataType : data.dataType ,
                        exp : data.expression ,
                        markInfo:  data.markInfo ,
                        userId : userInfo.id ,
                        belongDataSetId : "" ,
                        expColumnIds : JSON.stringify(expColumns)
                    };

                    settings.loading = true;
                    if(nodeData.step === true){
                        // vm.editStep(services ,data , vm , nodeData , settings);
                        req_data.belongDataSetId = nodeData.result.belongDataSetId;
                        req_data.columnCode = nodeData.result.columnCode;
                        let params = {
                            dataSetId: datasetId,
                            stepId : nodeData.stepId ,
                            addColumnStep : req_data
                        };
                        services.editDataStep(params ,settings ).then(res =>{
                            if(res.data.status === 0){
                                vm.$message.success("步骤编辑成功");
                                vm.$emit("reNewColumn");
                                vm.visible = false;

                            }
                        })
                    }else {
                        req_data.belongDataSetId = selectedData.belongDataSetId;
                        req_data.columnCode = data.columnCode;
                        services.addDataSetColumn(req_data ,settings ).then(res => {
                            if(res.data.status === 0){
                                vm.visible = false;
                                let field = vm.setFields(res.data.data );
                                vm.$emit("appendColumns", field , true);
                            }
                        })
                    }

                }
            })

        }
    }
}
