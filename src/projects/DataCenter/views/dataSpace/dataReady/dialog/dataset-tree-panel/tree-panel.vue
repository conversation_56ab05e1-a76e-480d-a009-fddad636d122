<!--
    @Describe: 数据集、数仓 树 选择面板
    @Author: wangjt
    @Date: 2024/9/10
-->
<template>
    <div class="dataset-panel" v-loading="settings.loading">
        <div class="dataset-panel__tabs">
            <span @click="changeTabs('myData')" :class="tabActive === 'myData' && 'is-active'">数据集</span>
            <span @click="changeTabs('theme')" :class="tabActive === 'theme' && 'is-active'"
                  v-if="isShowHouse">数据仓库</span>
        </div>
        <div class="dataset-panel__bar">
            <el-input v-model="filterText"></el-input>
        </div>
        <div class="dataset-panel__tree">
            <div v-show="tabActive === 'myData'" class="height100">
                <div class="tree-type">
                    <dg-select
                            v-model="treeType"
                            :data="typeOpt"
                            @change="typeChangeFn"
                    ></dg-select>
                </div>
                <div class="tree-content">
<!--                    <dg-scrollbar ref="tree-bar">-->
                        <dg-tree
                            class="scroll-cont"
                            :data="data"
                            node-key="id"
                            :expand-on-click-node="true"
                            :props="defaultProps"
                            :filter-node-method="filterNode"
                            ref="tree"
                            highlight-current
                            :show-checkbox="showCheckbox"
                            :default-expanded-keys="expandData"
                            @node-click="(...args) => nodeClick('dgtree',args)"
                            @check="checkChange"
                            @current-change="currentChange"
                    >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <template v-if="!data.belongType">
                            <div class="custom-tree-node__file">
                                 <div class="custom-tree-node__name">
                                        <i
                                                :class="{
                                                'el-icon-folder' : !node.expanded,
                                                'el-icon-folder-opened' : node.expanded
                                              }"
                                        ></i>
                                        <span class="pl5" :title="data.label">{{ data.label }}</span>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <span :title="data.label">{{ data.label }}</span>
                        </template>
                    </span>
                    </dg-tree>
<!--                    </dg-scrollbar>-->
                </div>
            </div>
<!--            <dg-scrollbar ref="dgtree-bar" native>-->
            <div v-show="tabActive === 'theme'" class="scroll-cont">
                <dg-tree
                        :data="sourceDatasetTree"
                        node-key="id"
                        :expand-on-click-node="true"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        ref="dgtree"
                        :default-expanded-keys="expandedKeys"
                        highlight-current
                        :show-checkbox="showCheckbox"
                        @node-click="(...args) => nodeClick('tree', args)"
                        @check="checkChange"
                        @current-change="currentChange"
                >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <template v-if="!data.belongType">
                            <div class="custom-tree-node__file">
                                 <div class="custom-tree-node__name">
                                    <i
                                            :class="{
                                        'el-icon-folder' : !node.expanded,
                                        'el-icon-folder-opened' : node.expanded
                                    }"
                                    ></i>
                                    <span class="pl5" :title="data.label">{{ node.label }}</span>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <span :title="data.label">{{ data.label }}</span>
                        </template>
                    </span>
                </dg-tree>
            </div>
<!--            </dg-scrollbar>-->
        </div>
    </div>
</template>

<script>
import {DatasetMixins} from "./dataset-mixins"
import {mapGetters} from "vuex";
import $right from "@assets/data/right-data/right-data";
import {treeMethods} from "@/api/treeNameCheck/treeName";

export default {
    name: "tree-panel",
    mixins: [DatasetMixins],
    computed: {
        ...mapGetters(["userRight","userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        isShowHouse(){ // dc_super用户才显示数据仓库
            return this.userInfo?.objCode === "dc_super" && this.rights.includes(this.warehouse);
        },
    },
    watch: {
        filterText(val){
            this.filterByText(val)
        }
    },
    props: {
        showCheckbox: {
            type: Boolean,
            default: false
        }
    },
    inject:["setVal"],
    data() {
        return {
            filterText: "",
            tabActive: "myData",
            warehouse: $right['dataSetOperationDataWarehouse'],//数据仓库权限
            defaultProps: {
                value: "id",
                label: "label",
                children: "children"
            },
            expandData: [],
            expandedKeys: [],
            sourceDatasetTree: [], // 数仓树
            settings: {
                loading: false
            },
            storeAllData: [],
            isHandled: false, // 手动操作选择节点
            noChangeTab: false, // 切换数据集类型时， tab 不根据已选择的节点 自动切换
        }
    },
    methods: {
        // 树节点过滤
        filterNode: treeMethods.methods.filterNode,
        filterByText(val) {
            this.$refs.dgtree.filter(val);
            this.$refs.tree.filter(val);
        },
        /**
         * 切换 tab
         * @param type
         */
        changeTabs(type) {
            this.tabActive = type;
        },
        /**
         * 单击树节点 更新选中的值
         * 保留往外 触发nodeClick事件
         * @param data
         * @param rest
         * @param ref 另一树 ref ，取消高亮
         */
        nodeClick(ref, [data, ...rest]) {
            if (data.belongType && !this.showCheckbox) {
                this.$emit("updateVal", data, true);
                this.$emit("nodeClick", data,...rest);
                this.$refs[ref].setCurrentKey(null);
            }
        },
        /**
         * 多选 值改变 更新选中的值
         * isHandled 这个变量 控制手动操作时，不执行回显 tab 及滚动到选择的节点
         */
        checkChange() {
            this.isHandled = true;
            let nodes = this.$refs.tree.getCheckedNodes(),
                sourceData = this.$refs.dgtree.getCheckedNodes();
            let allData = [...nodes, ...sourceData].filter(o => !!o.belongType);
            this.$emit("updateVal", allData);
        },
        /**
         * 对外 触发 current-change 事件 ，兼容 下拉树触发的事件
         * @param args
         */
        currentChange(...args){
            this.$emit("current-change",...args);
        },
        /**
         * 回显选择的值到树，及 tab，并滚动到选中的节点
         * 重置 isHandled
         * @param val
         * @returns {Promise<boolean>}
         */
        async setSelectedVal(val) {
            const {showCheckbox} = this;
            // 手动更新值 则不执行
            if(this.isHandled) return this.isHandled = false;
            if (showCheckbox) {
                let mNode = this.getCheckedNodes(val, 'tree', 'expandData');
                let sNode = this.getCheckedNodes(val, 'dgtree', 'expandedKeys');
                // tab 不自动更新
                if(!this.noChangeTab) {
                    if (mNode.length) {
                        this.tabActive = 'myData';
                    } else if (sNode.length) {
                        this.tabActive = 'theme';
                    }
                }
                await this.$nextTick();
                this.scrollToCurrent(val, 'is-checked');
            } else {
                this.getCurrentNode(val, 'tree', 'myData', 'expandData');
                this.getCurrentNode(val, 'dgtree', 'theme', 'expandedKeys');
            }
        },
        /**
         * 滚动到指定节点
         * @param id  选择节点 id {string | string[]}
         * @param className {string} 选中节点样式 is-checked  is-current
         */
        scrollToCurrent(id,className){
            if(!id) return;
            let current = this.$el.getElementsByClassName(className)[0];
            if(!current) return;
            current.scrollIntoView({
                behavior: "instant",
                block: "center"
            })
        },
        /**
         * 获取勾选的节点
         * @param val {string[]} 勾选节点id数组
         * @param ref {string} 树的 ref
         * @param expandKey {string} 树绑定默认展开的变量
         * @returns {*[]}
         */
        getCheckedNodes(val, ref, expandKey) {
            let node = [];
            if (val.length) {
                this.$refs[ref].setCheckedKeys(val);
                node = this.$refs[ref].getCheckedNodes().filter(o => !!o.belongType);
                let nodeIds = node.map(o => o.id).filter(i => !this[expandKey].includes(i));
                this[expandKey].push.apply(this[expandKey], nodeIds);
            }else this.$refs[ref].setCheckedKeys(val);
            return node;
        },
        /**
         * 回显选中的节点及 tab
         * @param val {string} 选中的节点 id
         * @param ref {string} 树的 ref
         * @param tab {string} 对应 tab值 如果 val是当前树的节点，则高亮当前 tab
         * @param expandKey {string} 树绑定默认展开的变量
         * @returns {Promise<void>}
         */
        async getCurrentNode(val, ref, tab, expandKey) {
            let node = this.$refs[ref].getNode(val);
            if (node) {
                if(!this.noChangeTab) this.tabActive = tab;
                await this.$nextTick();
                if (!this[expandKey].includes(val)) this[expandKey].push(val);
                this.$refs[ref].setCurrentKey(val);
                this.scrollToCurrent(val, 'is-current');
            }
        },
        /**
         * 数据集 类型切换
         * 类型切换时，不做tab 回显
         * @returns {Promise<void>}
         */
        async typeChangeFn(){
            this.noChangeTab = true;
            await this.treeTypeChange(true);
            await this.$nextTick();
            let val = this.setVal();
            if(val) {
                await this.setSelectedVal(val);
            }
            this.noChangeTab = false;
        },
        /**
         * 初始化 加载树
         * @returns {Promise<void>}
         */
        async initTree() {
            const vm = this;
            this.settings.loading = true;
            await Promise.all([
                this.treeTypeChange(),
                this.getDataSource(),
            ]).finally(() => {
                vm.settings.loading = false;
            })
            this.$emit("updateOptions", this.storeAllData);
        },
        /**
         * 获取数据集 基于 目录
         * @param isLoading
         * @returns {*}
         */
        getDatasetByCatalog(isLoading) {
            const vm = this, {$services} = vm;
            let settings;
            if(isLoading) {
                settings = vm.settings;
                settings.loading = true;
            }
            vm.expandData = [];
            return $services("dataReady").queryDataSetTree(true,settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.data = vm.filterChildren(result[0].children);
                    vm.setExpandData(vm.data, 'expandData');
                }
            })
        },
        /**
         * 获取数据集 基于分类
         * @param isLoading
         * @returns {*}
         */
        getDatasetBySource(isLoading) {
            const vm = this, {$services} = vm;
            let settings;
            if(isLoading) {
                settings = vm.settings;
                settings.loading = true;
            }
            vm.expandData = [];
            return $services("dataReady").queryDataSetTreeByDsType({
                hasDataObj: true,
            }, settings).then(res => {
                if (res.data.status === 0) {
                    vm.data = vm.filterChildren(res.data.data);
                    vm.setExpandData(vm.data, 'expandData');
                }
            })
        },
        /**
         * 获取数据仓库
         * @returns {*}
         */
        getDataSource() {
            const vm = this, {$services} = vm;
            if(!vm.isShowHouse) return;
            vm.expandedKeys = [];
            return $services("dataSource").getDataSourceTree(null, true).then(res => {
                if (res.data.status === 0) {
                    vm.sourceDatasetTree = vm.filterChildren(res.data.data);
                    vm.setExpandData(vm.sourceDatasetTree, 'expandedKeys');
                }
            });
        },
        /**
         * 设置默认展开层级
         * @param {*} data
         * @param key
         */
        setExpandData(data, key) {
            this[key].push(...data.map(item => item.id))
        },
        /**
         * 过滤空的父节点
         * @param data
         * @returns {*}
         */
        filterChildren(data) {
            const vm = this;
            return data.filter(child => {
                if (child.belongType) {
                    let item = vm.storeAllData.find(o => o.id === child.id);
                    if (!item) vm.storeAllData.push(child);
                }
                if (child.children && child.children.length) child.children = vm.filterChildren(child.children);
                return child.children && child.children.length || child.belongType;
            })
        }
    },
    created() {
        this.initTree();
    }
}
</script>

<style scoped lang="less">
.dataset {
    &-panel {
        //height: 40vh;
        height: 22rem;

        &__tabs {
            display: flex;
            align-items: center;
            padding: 10px 0;

            > span {
                font-size: .8rem;
                color: rgba(0, 0, 0, 0.44);
                margin-right: 20px;
                cursor: pointer;
                line-height: 1;

                &.is-active {
                    font-weight: bold;
                    color: #0088ff;
                }
            }
        }

        /deep/ .el-input__validateIcon {
            display: none;
        }

        &__tree {
            height: calc(100% - 3rem - 20px);
        }
    }
}

.tree-type {
    padding-top: 8px;
}
.scroll-cont {
    padding: 8px;
    box-sizing: border-box;
    height: 100%;
    overflow:auto;
}
.tree-content {
    height: calc(100% - 2rem - 10px);
    overflow:auto;
}

</style>
