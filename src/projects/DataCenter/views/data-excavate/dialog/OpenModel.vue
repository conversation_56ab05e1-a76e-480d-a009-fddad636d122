<template>
    <div class="openModel">
        <dg-dialog :close-on-click-modal="false"
                   :width="width"
                   :title="title"
                   :visible.sync="dialogVisible"
                   @closed="clearD"
                   :append-to-body="true"
                   class="model-dialog"
        >
            <ModelTree @nodeClick="checkNode" :row="row" :notFirst="true" :isNew="isNew" :has-save="true" v-if="reNew" />
            <div slot="footer">
                <el-button @click="checked" type="primary" size="mini">确定</el-button>
                <el-button @click="close" size="mini">取消</el-button>
            </div>
        </dg-dialog>
    </div>
</template>

<script>
    import ModelTree from './ModelTree'
    export default {
        name: "OpenModel" ,
        data(){
            return {
                width : '316px' ,
                dialogVisible : false ,
                reNew : false ,
                classifyId : "",
                title :"",
                treeNode:"",
                row:{},
                treeType:"",
                operType:"",
                isSQLModel:false,
            }
        },
        components : {
            ModelTree,
        },
        methods : {
            getCenterTable(data){
                if(data.parentId !== "-1"){
                    this.treeNode = {
                        transId: data.id,
                    };
                }
            },
            checkNode(label,value,node){
                this.treeNode = {};
                if(node.level === 3 || ( this.operType === 'move')){ //node.level === 2 &&
                    this.treeNode = {
                        dirParentId: "",
                        modelName: label,
                        modelSpec: "",
                        modelType: "",
                        releaseDate: "",
                        status: "",
                        transId: value,
                        isNew:false
                    };
                }

                // this.classifyId = value;
            },
            open(row,type,operType){
                this.treeType = type;
                this.operType = operType;
                this.row = row ;
                this.dialogVisible = true;
                switch (type) {
                    case 'dataCenter':
                        this.reNew = true;
                        if(operType === 'open'){
                            this.isNew = true;
                            this.title = "打开";
                        }else{
                            this.isNew = false;
                            this.title = "移动";
                        }
                        break;
                    case 'sqlModel':
                        this.isSQLModel = true;
                        this.reNew = false;
                        break;
                }
            },
            close(){
                this.dialogVisible = false;
            },
            checked(){
                const  _this = this;
                let url = "";
                if(_this.treeNode.transId === undefined){
                    _this.$message.warning("请选择模型！");
                }else{
                    if(this.operType === 'open'){
                        _this.dialogVisible  = false;
                        _this.$emit("openPage",_this.treeNode);
                    }else{
                        let id = _this.row.transId;
                        if(id === undefined){
                            id = _this.row.id;
                        }
                        url = "/dataModeling/moveModel?elementId="+id+"&classifyId="+_this.treeNode.transId;
                        this.$axios.get(url).then(res =>{
                            if(res.data.status === 0){
                                _this.$message.success("移动成功");
                                _this.$emit("update");
                                _this.dialogVisible = false;
                            }else{
                                _this.$message.warning(res.data.msg);
                            }
                        }).catch(err =>{
                            this.$message.error("服务器异常，请联系管理员！");
                        })
                    }
                }
            },
            clearD(){
                this.reNew = false;
            }
        }
    }
</script>

<style scoped>
    .model-dialog .dataTree {
        height: calc( 75vh - 192px);
        margin: 0;
        float: inherit;
    }
</style>