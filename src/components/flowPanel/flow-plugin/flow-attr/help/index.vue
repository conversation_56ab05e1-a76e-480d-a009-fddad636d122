<!-- 
    linxp
    2021.06.11
-->
<template>
    <div class="help">
        <p>{{description}}</p>
        <el-button type="text" v-if="showTip" @click="showMore(sourceData.label , sourceData.keyWord , ['784px','503px'])">了解详情</el-button>
    </div>
</template>
<script>
import {getDescription} from "@/assets/data/model-plugin/plugin-name-description";
export default {
    name : "help" ,
    props : {
        sourceData : Object
    },
    computed :{
        description (){
            return getDescription()[this.sourceData.keyWord];
        },
        showTip(){
            let codes = ["cicadaSubtractByKeyPlugin" , "cicadaCollisionPlugin"];
            return codes.includes(this.sourceData.keyWord);
        }
    },
    methods : {
        showMore(title , code, area){
            this.$dgLayer({
                content: require("../example-demo"),
                title,
                props: {
                    code
                },
                move :false,
                maxmin: false,
                noneBtnField: true,
                area
            });
        }
    }
}
</script>
<style lang="less" scoped>
.help {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    overflow: auto;
    flex: 1;
    p {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 21px;
        text-align: justify;
    }
}
</style>
