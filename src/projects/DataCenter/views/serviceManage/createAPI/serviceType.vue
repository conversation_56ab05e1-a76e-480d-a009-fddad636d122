<template>
    <div>
        <el-tabs class="ce-share_tab" v-model="activeName" @tab-click="changeTabs">
            <el-tab-pane v-for="(tab , inx) in tab_panel" :key="inx" :label="tab.label" :name="tab.name">
            </el-tab-pane>
        </el-tabs>
        <div class="serviceType" :class="{'ce-event_not-allow': type === 'edit'}">
            <div v-for="item in tabsList" :key="item.value" class="serviceType-tabs" @click="selectItem(item)" :class="{'isActive':selectType === item.value}">
                <div class="serviceType-tabs_icon">
                    <svg aria-hidden="true" class="serviceType-tabs_svg">
                        <use :xlink:href="'#'+item.icon" class="serviceType-tabs_use"></use>
                    </svg>
                </div>
                <p>{{item.name}}</p>
            </div>
        </div>
    </div>

</template>

<script>
    export default {
        name: "serviceType",
        props:{
            region: String,
            type: String,
            showTabs: Array,
            entrance:String
        },
        watch:{
            showTabs(val){
                this.changeTabs();
            },
            region(val){
                this.selectType = val;
            }
        },
        computed: {
            tab_panel() {
                this.activeName = 'basedDataSet';
                let data = [
                    { label: '基于数据集创建', name: 'basedDataSet'},
                    { label: '基于模型创建', name: 'basedModel'},
                ];
                if(this.entrance === 'dataSet'){
                    data = [{ label: '基于数据集创建', name: 'basedDataSet'}];
                    this.activeName = 'basedDataSet';
                }
                else if(this.entrance === 'model'){
                    data = [{ label: '基于模型创建', name: 'basedModel'}];
                    this.activeName = 'basedModel';
                }
                return  data;
            }
        },
        data() {
            return {
                list: [
                    { name: '数据查询', value: '4' , icon: 'icon-data-set' },
                    { name: '信息核查', value: '6' , icon: 'icon-data-c2'},
                    { name: '比对订阅', value: '5' , icon: 'icon-a-valuesubstitution'},
                    { name: '数据碰撞', value: '7' , icon: 'icon-collision'},
                    { name: '模型分析', value: '1' , icon: 'icon-model1'},
                    { name: 'AI算法', value: '2' , icon: 'icon-AImodel'},
                ],
                selectType: this.region,
                tabsList:[],
                activeName: 'basedDataSet',
            }
        },
        methods:{
            selectItem(info){
                this.selectType = info.value;
            },
            changeTabs(){
                const vm = this;
                let list = vm.list.filter(n=> {
                    let type = vm.activeName === 'basedDataSet' ? ['4','6'] :  ['6','5','7','1','2'].filter(it => vm.$hideAI ? it !== '2' : true);
                    return type.includes(n.value) && vm.showTabs.includes(n.value)
                })
                this.tabsList = list;
            }
        },
        mounted(){
            const vm = this;
            vm.changeTabs();
        }
    }
</script>

<style scoped lang="less">
.serviceType{
    min-height:142px;
    height: 100%;
    width: 70%;
    margin: auto;
    padding: 10px 20px;
    grid-template-columns: repeat(3,33.3%);
    display: grid;
    grid-row-gap: 14px;
    grid-column-gap: 14px;
    &-tabs{
        text-align: center;
        align-items: center;
        justify-content: center;
        border: 1px solid rgba(0,0,0,.09);
        height: 100px;
        padding: 10px;
        border-radius: 2px;
        cursor: pointer;
        p{
            color:#333;
        }
        &_icon{
            width:80%;
            background: url("../../../assets/images/home/<USER>") no-repeat center center;
            background-size: 70px 70px;
            border-radius: 4px;
            margin: auto;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        &_svg{
            width: 30px;
            height: 30px;
        }
        &_use{
            fill: rgba(0, 136, 255, 0.8);
        }
    }
    &-tabs:hover {
        border: 1px solid #08f;
        box-shadow: 0 5px 10px 0 rgba(24,144,255,.3);
    }
}
    .isActive{
        border: 1px solid #08f;
        box-shadow: 0 5px 10px 0 rgba(24,144,255,.3);
    }

    .notOperation{

    }
</style>
