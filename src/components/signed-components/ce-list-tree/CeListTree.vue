<!--树插件
    props :
        showSort  是否显示排序 默认不显示
        addOnlyNode 只能添加的节点 通过返回条件 默认返回false, 方法 的参数 节点的 node ,data , 如设置一级节点只能添加 不能修改 删除 则方法 return node.level === 1;
        filterPlaceholder 过滤输入框的占位描述
        treeBind dg-tree 设置默认的属性
        treeProps dg-tree 的props ,
        isEdited 节点是否可编辑 默认 返回 ture , 跟addOnlyNode 一致
        isDropAction 编辑菜单是下拉显示 或者 图标形式全部展示 默认false
        addBtnTxt 新增子目录名称
        treeMenus 树编辑菜单数据
        treeMenus: [
                {
                    name: "新增子目录",  名称
                    icon: "el-icon-plus", 图标
                    fnName: 'add', 方法绑定 仅 add , rename , move , delete
                    show: (right, node) => node && node.level < 5 , 显示条件 right 是所有权限code , node 当前节点node
                },
                {
                    name: '重命名目录',
                    icon: 'el-icon-edit-outline',
                    fnName: 'rename',
                    show: () => true
                },
                {//分割线
                    name: "divider",
                    show: () => true
                },
                {
                    name: '删除目录',
                    icon: 'el-icon-delete',
                    fnName: 'delete',
                    show: () => true
                }
            ],

        searchStyle 搜索框外层 style
        rootDirType 根节点的 dirType 默认空
        renameInterface 重命名接口方法
        addChildDirInterface 新增子目录接口
        addDirInterface 新增目录接口
        moveDirInterface 移动目录接口
        getTreeInterface 获取树接口
        deleteNodeInterface 删除节点接口
        limitLevel 目录最多层级限制
        sortTimeKey  排序时间字段 "operateTime"
        sortNameKey  排序名称字段 "name"
方法 ：
    nodeLink 节点点击事件 参数 节点 name ,id ,  node

    使用例子
     <ce-list-tree
                        ref="tree"
                        :search-style="{
                            'padding' : '18px 20px 18px 0'
                        }"
                        rootDirType="TRANS_DIR_MF_MY"
                        :treeProps="treeProps"
                        :treeMenus="treeMenus"
                        :getTreeInterface="setTreeInterface('queryTransTree')"
                        :addChildDirInterface="setTreeInterface('createTransClassify')"
                        :moveDirInterface="setTreeInterface('moveTreeDir')"
                        :deleteNodeInterface="setTreeInterface('deleteTransClassify')"
                        :renameInterface="setTreeInterface('updateTransClassify')"
                        @nodeLink="nodeLink"
                >
                </ce-list-tree>


-->
<template>
    <div class="CeListTree list-tree" v-loading="settings.loading">
        <div class="list-tree__search" :style="searchStyle">
            <el-input v-model.trim="search" v-input-limit:trim suffix-icon="el-icon-search"
                      :placeholder="filterPlaceholder"/>
            <slot name="sort">
                <el-popover ref="sortPop" v-if="showSort" popper-class="ce-popover" trigger="click" width="96">
                    <div class="ce-tree__pop-box">
                        <div class="ce-tree__pop-item" v-for="(menu , k) in sortMenu"
                             :class="{'active' : k === sortVal}"
                             @click="setTreeSort(k)" :key="k">
                            <i class="eda_icon" :class="menu.icon"></i>
                            <span>{{ menu.label }}</span>
                        </div>
                    </div>
                    <i slot="reference" class="eda_icon ce-sort_icon"
                       :title="sortVal === 'letter'?letterSort : timeSort"
                       :class="{'iconsort' : sortVal === 'letter' , 'iconshijianpaixutubiao' : sortVal === 'time' }"></i>
                </el-popover>
            </slot>
        </div>
        <div class="list-tree__tree">
            <dg-tree
                    ref="tree"
                    v-bind='{
                        "filter-node-method": filterSourceNode ,
                        ...treeDefProps,
                        ...treeBind
                    }'
                    v-on="$listeners"
                    @node-click="nodeLink"
            >
                <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }"
                      @mouseenter="rightMenu($event , node , data)">
                    <slot name="nodeLabel" :node="node" :data="data">
                          <span class="node-label"
                                :title="data[treeProps.label]"
                                :class="{
                        'el-icon-folder' : !node.expanded ,
                        'el-icon-folder-opened' : node.expanded,
                      }"
                          >{{ data[treeProps.label] }} </span>
                    </slot>
                    <span class="tree__action"
                          :style="{'min-width' : isDropAction || addOnlyNode(node,data) ?'': `${menus.length * 22 - 4}px`}"
                          v-if="isEdited(node,data)">
                       <dg-button type="text" :title="addBtnTxt"
                                  icon="el-icon-plus"
                                  class="tree_add"
                                  @click.stop="addChildDir($event , node , data)"
                                  v-if="addOnlyNode(node,data)"></dg-button>
                            <dir-edit-action v-else-if="menus.length && isDropAction"
                                             :value="node.data"
                                             @command="menuCommand($event , node , data)"
                                             :data="menus"
                                             :rights="rights" :node="node">
                            </dir-edit-action>
                            <span class="icon_list" v-else>
                                <el-button type="text"
                                           v-for="(menu , i) in menus"
                                           :key="i"
                                           :title="menu.name"
                                           v-if="menu.name !== 'divider' && menu.show(rights , node)"
                                           :icon="menu.icon"
                                           @click="menuCommand(menu, node , data)">
                                </el-button>
                            </span>
                    </span>
                </span>
            </dg-tree>
        </div>
        <!--新建树 目录-->
        <add-tree-dir ref="treeDir" v-loading="settings.loading" @addTreeDir="addTreeDir" @moveTreeDir="moveTreeDir"
                      @filterData="filterEditTree">
            <dg-tree slot="tree" ref="editTree"
                     v-bind="{
                    ...treeDefProps ,
                    'filter-node-method': filterSourceNode ,
                   'default-expanded-keys': editExpandData,
                   'data' : editData
                            }"
                     @node-click="nodeClick">
                <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                    <span class="node-label"
                          :title="data[treeProps.label]"
                          :class="{
                        'el-icon-folder' : !node.expanded ,
                        'el-icon-folder-opened' : node.expanded,
                      }"
                    >{{ data[treeProps.label] }} </span>
                </span>
            </dg-tree>
            >
        </add-tree-dir>
        <!-- 新建目录 重命名 -->
        <common-dialog custom-class="ReNameNode"
                       :width="width"
                       :title="title"
                       :visible.sync="visible"
                       @closed="clearData"
        >
            <re-name-node v-if="reNew" ref="rename"></re-name-node>
            <div slot="footer">
                <el-button @click="visible = false" size="mini">{{ btnCancelTxt }}</el-button>
                <el-button @click="submit" type="primary" size="mini">{{ btnCheckTxt }}</el-button>
            </div>
        </common-dialog>
    </div>
</template>

<script>
import {mapGetters} from "vuex";
import {treeMethods} from "@/components/signed-components/ce-list-tree/pages/treeMixins";
import {TreeEdit} from "@/components/signed-components/ce-list-tree/pages/tree-edit";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {dialog} from "@/api/commonMethods/dialog-mixins";
import ReNameNode from "@/components/signed-components/ce-list-tree/pages/ReNameNode";
export default {
    name: "CeListTree",
    components : {ReNameNode},
    mixins: [treeMethods, TreeEdit , commonMixins ,dialog ],
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) rights = this.userRight.map(r => r.funcCode);
            return rights;
        },
        treeDefProps() {
            const vm = this;
            return {
                "data": vm.data,
                "node-key": "id",
                "default-expanded-keys": vm.expandData,
                "highlight-current": true,
                "props": vm.treeProps,
                "expand-on-click-node": false,
            }
        }
    },
    props: {
        showSort:{
            type : Boolean,
            default : false
        },
        addOnlyNode: {
            type: Function,
            default: (node, data) => {
                return false;
            }
        },
        filterPlaceholder: {
            type: String,
            default: "请输入名称搜索"
        },
        treeBind: {
            type: Object,
            default: () => {
            }
        },
        treeProps: {
            type: Object,
            default: () => {
                return {
                    label: "label",
                    children: "children"
                }
            }
        },
        isEdited: {
            type: Function,
            default: (node, data) => {
                return true;
            }
        },
        isDropAction: {
            type: Boolean,
            default: false
        },
        addBtnTxt: {
            type: String,
            default: "新增子目录"
        },
        searchStyle: {
            type: Object,
            default: () => {
            }
        },
        rootDirType: { //根节点属性
            type: String,
            default: ""
        },
        treeMenus: {
            type: Array,
            default: () => []
        },
        renameInterface: { //重命名接口
            type: Function,
            default: () => new Promise(resolve => resolve())
        },
        addChildDirInterface: { //新增子目录接口
            type: Function,
            default: () => new Promise(resolve => resolve())
        },
        addDirInterface: { //新增目录接口
            type: Function,
            default: () => new Promise(resolve => resolve())
        },
        moveDirInterface: { //移动目录接口
            type: Function,
            default: () => new Promise(resolve => resolve())
        },
        getTreeInterface: { //获取树接口
            type: Function,
            default: () => new Promise(resolve => resolve())
        },
        deleteNodeInterface: { //删除节点接口
            type: Function,
            default: () => new Promise(resolve => resolve())
        },
        limitLevel: {//目录最多层级限制
            type: Number,
            default: 5
        },
        sortTimeKey : {
            type : String ,
            default : "operateTime"
        },
        sortNameKey : {
            type : String ,
            default : "name"
        }
    },
    data() {
        return {
            sortMenu : {
                time : {label : "按时间排序" , icon : "iconshijianpaixutubiao"} ,
                letter : {label : "按字母排序", icon : "iconsort"}
            },
            sortVal : "time",
            data: [],
            search: "",
            expandData: [],
            editExpandData: [], //弹窗 树默认展开项
            menus: [],
            treeMenuFn: { //树操作事件 增 删 改 移动
                add: () => this.addChildDir,
                rename: () => this.renameDir,
                delete: () => this.deleteDir,
                move: () => this.moveDir
            },
            editData: [],
            treeDefault: {
                id: "",
                label: "",
                children: [],
                pId: "",
                isActive: false
            },
            currentId : "" ,//当前节点id
            width : "600px",
        }
    },
    watch: {
        search(val) {
            this.$refs.tree.filter(val);
        }
    },
    methods: {
        /**
         * 排序树
         * @param val
         */
        setTreeSort(val){
            this.$refs.sortPop.doClose();
            this.sortVal = val;
            this.sortTreeData(val);
        },
        /**
         * 排序树节点
         * @param val
         */
        async sortTreeData(val){
            const vm = this , {sortTimeKey , sortNameKey} = vm;
            if(val === 'time'){
                vm.data = await vm.treeSortByWay(vm.data , sortTimeKey , 'sortInTime' , true);
            }else if(val === 'letter') {
                vm.data = await vm.treeSortByWay(vm.data , sortNameKey , 'sortInLetter' , true);
            }
            vm.$nextTick(()=>{
                if(vm.$refs.tree){
                    vm.$refs.tree.setCurrentKey(vm.currentId);
                    vm.reSearch();
                }
            });
        },
        /**
         * 树过滤方法
         * @param arg
         * @return {boolean}
         */
        filterSourceNode(...arg) {
            return this.filterNode(...arg, this.treeProps);
        },
        /**
         * 新建一级目录
         */
        addDir() {
            this.showReName('新建目录', "", this.addFirstTree, '目录名称' , true);
        },
        /**
         * 新建子目录
         * @param event
         * @param node
         * @param data
         */
        addChildDir(event, node, data) {
            const vm = this, {selectedData, selectedNode} = vm;
            let childD = data || selectedData, childN = node || selectedNode;
            vm.$refs.treeDir.show(childD);
            vm.nodeClick(childD, childN);
            vm.setAddTreeCurrent(childD);
            vm.getEditData(childD);
        },
        /**
         * 重命名
         */
        renameDir() {
            this.showReName('重命名目录', this.selectedData.name, this.editTree,'目录名称' );
        },
        /**
         * 删除节点
         */
        deleteDir() {
            const vm = this;
            let node = vm.getTreeNode() , nodeN = node.data.name;
            vm.confirm(`确认删除\"${nodeN}\"及同步删除\"${nodeN}\"下的数据吗` , '删除' , ()=>{
                vm.deleteTreeDir(node);
            })
        },
        /**
         * 移动节点
         */
        moveDir() {
            const vm = this, {selectedData, selectedNode, limitLevel} = vm;
            let level = selectedData.children && selectedData.children.length || selectedNode.level >= limitLevel ? selectedNode.level - 1 : limitLevel - 1;
            vm.$refs.treeDir.show(selectedData, 'move');
            vm.nodeClick(selectedData, selectedNode);
            vm.setAddTreeCurrent(selectedNode.parent.data);
            vm.getEditData(selectedData, level, 'move');
        },
        /**
         * 菜单绑定事件
         * @return {T[]}
         */
        setMenus() {
            const vm = this, {treeMenuFn} = vm;
            return vm.treeMenus.map(item => {
                if (item.fnName) item.fn = treeMenuFn[item.fnName] ? treeMenuFn[item.fnName]() :
                        () => {};
                return item;
            });
        },
        /**
         * 设置弹窗树 选中项
         * @param data
         */
        setAddTreeCurrent(data) {
            const vm = this;
            let curId = data.id;
            vm.editExpandData = [];
            vm.$nextTick(() => {
                vm.editExpandData.push(curId);
                vm.$refs.editTree.setCurrentKey(curId);
            })
        },
        /**
         *  获取弹窗树 数据
         * @param level
         * @param type
         * @param selData
         */
        getEditData(selData, level, type) {
            const vm = this;
            let data = vm.copyArrayObj(vm.data);
            vm.editData = vm.setEditData(data, type, selData, level);
        },
        /**
         * 设置 过滤节点
         * @param data
         * @param type
         * @param selData
         * @param level
         * @return {*}
         */
        setEditData(data, type, selData, level) {
            const vm = this, {filterCurNode, limitLevel, treeProps} = vm;
            return filterCurNode(data, selData, type).map(item => {
                let disabled = type === 'move' ? item.level >= level : item.level >= limitLevel - 1;
                item.children = item[treeProps.children] && item[treeProps.children].length && !disabled ?
                        vm.setEditData(item[treeProps.children], type, selData, level) : [];
                return item;
            })
        },
        /**
         * 移动时 过滤当前节点
         */
        filterCurNode(data, selData, type) {
            let result;
            if (type === 'move') result = data.filter(item => item.id !== selData.id);
            else result = data;
            return result;
        },
        /**
         * 初始化树数据
         * @return {Promise<void>}
         */
        async initTree() {
            const vm = this, {getTreeInterface, rootDirType , settings , showSort} = vm;
            settings.loading = true;
            let res = await getTreeInterface('', '', 'TRANS_DIR_MF', settings);
            if ( !res || res.data.code !== 0 ||  res.data.data.length === 0) {
                settings.loading = false;
                return;
            }
            let result = res.data.data;
            let curId, curN = result.find(no => no.dirType === rootDirType);
            if (curN) curId = curN.id;
            else curId = result[0].id;
            vm.data = await vm.setTreeNode(result, 1);
            vm.$nextTick(() => {
                if (vm.$refs.tree) {
                    vm.expandData.push(curId);
                    if(showSort) vm.sortTreeData(vm.sortVal);
                    vm.$refs.tree.setCurrentKey(curId);
                    let curData = vm.$refs.tree.getCurrentNode();
                    let curNode = vm.$refs.tree.getNode(curData);
                    vm.nodeLink(curData, curNode);
                }
            });
        },
        /**
         * 设置 树节点数据
         * @param data
         * @param level
         * @return {*}
         */
        setTreeNode(data, level) {
            const vm = this, {treeProps, treeDefault} = vm;
            let child_l = level + 1;
            return data.map(item => {
                item[treeProps.children] = item[treeProps.children] && item[treeProps.children].length ? vm.setTreeNode(item[treeProps.children], child_l) : [];
                let propL = vm.setPropsKey(item, treeDefault);
                return {
                    ...propL,
                    ...item,
                    pId: item.pId === "-1" ? "0" : item.pId,
                    level,
                }
            });
        },
        /**
         * 设置树节点属性
         * @param data
         * @param treeLists
         * @return {{}}
         */
        setPropsKey(data, treeLists) {
            const {treeProps} = this;
            let res = {};
            for (let k in treeLists) {
                let tar = treeLists[k];
                if (treeProps[k]) {
                    res[treeProps[k]] = data[treeProps[k]] && JSON.parse(JSON.stringify(data[treeProps[k]])) || JSON.parse(JSON.stringify(tar));
                } else {
                    res[k] = data[k] && JSON.parse(JSON.stringify(data[k]))  || tar;
                }
            }
            return res;
        },
        /**
         * 树点击事件
         * @param data
         * @param node
         */
        nodeLink(data, node) {
            const vm = this, {treeProps} = vm;
            let name = data[treeProps.label], id = data.id;
            vm.currentId = id;
            vm.$emit('nodeLink', name, id, node);
        },

    },
    created() {
        this.initTree();
        this.menus = this.setMenus();
    }
}
</script>

<style scoped lang="less">
.list-tree {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__search {
        padding: 18px 20px;
        min-height: 2rem;
        display: flex;
        align-items: center;
    }

    &__tree {
        flex: 1;
        overflow: auto;
    }
}

.el-tree-node__content:hover {
    .tree__action {
        opacity: 1;
    }
}

.tree__action {
    min-width: 42px;
    opacity: 0;
    text-align: right;
    /deep/ i.icon-more-b {
        cursor: pointer;
        color: #1890ff;
        height: 28px;
        line-height: 28px;
        text-indent: 0;
        padding: 2px;
        padding-right: 16px;
        transition: all .3s;
    }

    > .el-button {
        padding: 0;
    }

    > .tree_add, .icon_list {
        padding-right: 16px;
    }
}

</style>
