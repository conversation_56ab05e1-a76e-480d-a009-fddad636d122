<!--
 * @Author: linxp
 * @Date: 2021-06-15 10:10:55
 * @LastEditTime: 2021-06-17 09:00:43
 * @LastEditors: Please set LastEditors
 * @Description: 步骤条
 * @FilePath: \trunk\src\pages\model-space\data-model\models\rapid-analysis\model\steps\index.vue
-->

<template>
    <div class="steps" v-if="reNew">
        <div class="steps__item" v-for="(step, index) in steps" :key="index">
            <div
                    :class="[
          'step',
          active === step.index && 'is-active',
          step.type === 'error' && 'is-error',
        ]"
                    @click="handleClickStep(step)"
            >
        <span class="step__icon">
          <i :class="['dg-iconp', step.icon]"></i>
        </span>
                <div class="steps__item-name">
          <span
                  :title="step.label"
                  v-if="!edits[index]"
                  @dblclick="onResetName(index,step.label)"
                  class="step__title"
          >{{ step.label }}</span
          >
                    <el-input
                            v-else
                            ref="ipt"
                            maxlength="50"
                            v-model="step.label"
                            v-input-limit:fieldName
                            size="mini"
                            @blur="onSubmitName(index, step)"
                    ></el-input>
                </div>

                <span
                        class="step__more"
                        v-if="index > 0"
                        @click.stop="handleDel(index)"
                >
          <i class="dg-iconp icon-delete-b"></i>
        </span>
            </div>
            <div class="step__line">
                <el-popover
                        placement="right"
                        :offset="60"
                        width="136"
                        trigger="click"
                        :ref="`popover__${index}`"
                >
                    <div class="condition__box">
                        <div
                                class="condition__item"
                                @click="handleAddCondition(condition, index)"
                                v-for="condition in conditions"
                                :key="condition.value"
                        >
                            <i :class="['dg-iconp', condition.icon]"></i>
                            <span>{{ condition.label }}</span>
                        </div>
                    </div>
                    <i slot="reference" class="dg-iconp icon-l-check-add"></i>
                </el-popover>
            </div>
        </div>
        <el-popover
                placement="right"
                :visible-arrow="false"
                :offset="60"
                width="136"
                trigger="click"
                ref="popover"
        >
            <div class="condition__box">
                <div
                        class="condition__item"
                        @click="handleAddCondition(condition, -1)"
                        v-for="condition in conditions"
                        :key="condition.value"
                >
                    <i :class="['dg-iconp', condition.icon]"></i>
                    <span>{{ condition.label }}</span>
                </div>
            </div>
            <div class="step step__add" slot="reference">
                <span class="step__icon">
                  <i :class="['dg-iconp', 'icon-l-plus']"></i>
                </span>
                <span class="step__title">添加条件</span>
            </div>
        </el-popover>
    </div>
</template>
<script>
import {mapActions, mapState} from "vuex";
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import {tabMixins} from "@/projects/DataCenter/views/modeling/dialog/model-tab/model-tab-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";

const conditions = [
            {
                label: "条件过滤",
                icon: "icon-l-filter",
                value: "filterCondtions",
                id: "cicadaConditionFilterPlugin",
            },
            {
                label: "分组统计",
                icon: "icon-relationship",
                value: "groupStatistics",
                id: "cicadaReducePlugin",
            },
            {
                label: "新增列",
                icon: "icon-column",
                value: "newCol",
                id: "cicadaServiceOrganization",
            },
            {
                label: "数据排序",
                icon: "icon-sort1",
                value: "dataSort",
                id: "cicadaDataSortPlugin",
            },
            // {
            //     label: "求交集",
            //     icon: "icon-intersection",
            //     value: "mixContrast",
            //     id: "cicadaInnerJoinPlugin",
            // },
            {
                label: "求差集",
                icon: "icon-union",
                value: "diffContrast",
                id: "cicadaSubtractByKeyPlugin",
            },
            {
                label: "合并行",
                icon: "icon-row",
                value: "rowMerge",
                id: "cicadaUnionJoinPlugin",
            },
            {
                label: "合并列",
                icon: "icon-column1",
                value: "colMerge",
                id: "cicadaCollisionPlugin",
            },
        ],
        inputNode = {
            icon: "icon-file",
            value: "chooseField",
            label: "选择字段",
            id: "chooseField",
            config: {}
        };
import {common} from "@/api/commonMethods/common";
import {getDescription, getName} from "@/assets/data/model-plugin/plugin-name-description";

export default {
    name: "steps",
    mixins: [tabMixins, servicesMixins, common ,listMixins],
    data() {
        return {
            conditions,
            active: "",
            steps: [],
            edits: [],
            storeName : "",
            delErrorArray:[],
            reNew : true
        };
    },
    props: {
        data: Object,
    },
    computed: {
        ...mapState({
            planId: (state) => state.plans.planId,
        }),
    },
    watch: {
        "steps.length": {
            handler(to) {
                this.edits = [];
                for (var i = 0; i < to.length; i++) {
                    this.editList.push(false);
                }
            },
            immediate: true,
            deep: true,
        },
    },
    mounted() {
        this.stepInit();
    },
    methods: {
        ...mapActions(["addNode","setNodeConfig"]),
        /**
         * 初始化
         * */
        stepInit() {
            const vm = this, {data} = vm;
            let index = new Date().valueOf();
            let conPlugin = {
                ...inputNode,
                index
            };
            if (data.nodeList.length === 0) {
                vm.steps.push(conPlugin);
            } else {
                data.nodeList.forEach((item, i) => {
                    index = new Date().getTime() + i;
                    if (item.type === 'INPUT' && i === 0) {
                        conPlugin = {
                            ...inputNode,
                            index,
                            config: {...item},
                            label: item.name
                        };
                    } else {
                        let plug = conditions.find(con => item.keyWord === con.id);
                        conPlugin = {
                            config: {...item},
                            ...plug,
                            index,
                            label: item.name
                        }
                    }
                    if (i === 0 && item.type !== 'INPUT') {
                        index = new Date().getTime() + 1;
                        vm.steps.push({
                            ...inputNode,
                            index
                        });
                    }
                    vm.steps.push(conPlugin);
                })
            }
            vm.addNode(vm.steps);
            vm.handleClickStep(vm.steps[0]);
        },
        /**
         * 设置是否编辑
         */
        onResetName(index , name) {
            this.$set(this.edits, index, true);
            this.storeName = name;
            this.$nextTick(() => {
                this.$refs.ipt[0].focus();
                this.$refs.ipt[0].select();
            });
        },
        onSubmitName(index, node) {
            if(!(node.config && node.config.id)) {
                this.$message.warning("请先配置字段");
                return;
            }else if(node.label === ""){
                this.$message.warning("插件名不能为空");
                return;
            }
            this.$set(this.edits, index, false);
            if(node.label === this.storeName) return ;
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            services.updateTransStepName(node.config.id, node.label).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("修改成功！");
                    node.config.name = node.label;
                    vm.setNodeConfig(node);
                }
            })
        },
        /**
         * 选中条件
         * @param {object} step 当前步骤数据
         */
        handleClickStep(step) {
            this.active = step.index;
            this.$emit("on-step-click", step);
        },
        /**
         * 添加条件
         * @param {object} condition 选中条件
         * @param index
         */
        async handleAddCondition(condition, index) {
            let target = new Date().valueOf();
            let node = {...condition};
            if (!this.steps[0].config.id) {
                this.$message.warning("请先配置输入字段!");
                return;
            }
            let idx = index >= 0 ? index + 1 : this.steps.length;
            let pluginKeyWord = node.id;
            // 调用新增插件接口
            const {data} = await this.services.addPluginTranStep(
                    pluginKeyWord,
                    node.label,
                    0,
                    0,
                    this.planId
            );
            if (data.status === 0) {
                let config = {
                    left: 0 + 'px',
                    top: 0 + 'px',
                    id: data.data,
                    name: node.label,
                    code: node.label,
                    keyWord: pluginKeyWord,
                    type: "plug", //需要type 判断是数据资源还是组件
                    label: getName()[pluginKeyWord] || "",
                    description: getDescription()[pluginKeyWord] || "",
                    status: true
                };
                this.$set(node, "nodeId", data.data);
                this.$set(node, "index", target);
                this.$set(node, "config", config);
                this.steps.splice(idx, 0, node);
                this.addNode(this.steps);
                this.$message.success("新增条件成功!");
                await this.setLines(index, idx);
                this.handleClickStep(node);
            }
            if (index >= 0) {
                this.$refs["popover__" + index][0].doClose();
            } else {
                this.$refs["popover"].doClose();
            }
        },
        /**
         * 连线
         * */
        setLines(inx, idx) {
            const vm = this, {steps} = vm;
            const collection = ["cicadaSubtractByKeyPlugin" , "cicadaUnionJoinPlugin" ,"cicadaCollisionPlugin"];
            const {modelingServices, modelingMock, planId} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let fromN = steps[idx - 1].config, toN = steps[idx].config, afterN;
            let beforeDirection , afterDirection;
            beforeDirection = collection.includes(toN.keyWord) ? '/left' : '';
            if (inx < 0 || idx === steps.length - 1) {
                services.addTransHopWithDirection(fromN.name, toN.name, fromN.id, toN.id, planId , beforeDirection);
            } else {
                afterN = steps[idx + 1].config;
                afterDirection = collection.includes(afterN.keyWord) ? '/left' : '';
                services.addQuickAnalysisTransHop(fromN.name, fromN.id, toN.name, toN.id, afterN.name, afterN.id,beforeDirection , afterDirection, planId);
            }
        },

        /**
         * 新增
         */
        handleAdd(step, index) {
            this.$refs[`popover-` + index][0].doClose();
        },
        /**
         * 重命名
         */
        handleReset(step, index) {
            this.$refs[`popover-` + index][0].doClose();
        },
        /**
         * 删除插件
         */
        async handleDel(index) {
            const vm = this, node = vm.steps[index].config;
            vm.confirm('提示', '确定要删除节点' + node.name + '?', async () => {
                vm.deleteCheck(index, vm.steps, vm, node);
            })

        },
        deleteCheck(index, steps, vm, node) {
            let stepIdArray = [];
            steps.forEach(item => {
                if(item.config.id)stepIdArray.push(item.config.id);
            });
            let params = {
                index: index,
                stepIdArray: stepIdArray
            }
            vm.services.deleteCheck(params).then(res => {
                let errorIdArray = res.data.data;
               /* for (let i = index + 1; i < vm.steps.length; i++) {
                    if (vm.steps[i].id === 'cicadaUnionJoinPlugin'){
                        vm.steps[i].type = 'error';
                    }
                }*/
               errorIdArray.forEach(item => {
                   vm.delErrorArray.push(item);
               });
                for (let i = index + 1; i < vm.steps.length; i++) {
                    for (let j = 0; j < errorIdArray.length; j++) {
                        let step = vm.steps[i];
                        if (step.config.id === errorIdArray[j]) {
                            vm.steps[i].type = 'error';
                        }
                    }
                }

                vm.services.deleteTransStepforQuick(node.id, vm.planId).then(res => {
                    if (res.data.status === 0) {
                        vm.$delete(vm.steps, index);
                        vm.addNode(this.steps);
                        vm.$message.success("成功删除节点");
                        let step = this.steps[index - 1];
                        vm.handleClickStep(step);
                        if(vm.steps[index] )vm.setLines(-1 ,index);
                    }
                });
                vm.$emit('getDelErrArray',vm.delErrorArray);
            })


        },
        setErrorType(errArray) {
            const vm = this;
            for (let i = 0; i < vm.steps.length; i++) {
                let step = vm.steps[i];
                if(step.config && step.config.id && errArray.includes(step.config.id)){
                    vm.steps[i].type = 'error';
                }else  {
                    vm.steps[i].type = '';
                }
            }
            vm.reNew = false;
            setTimeout(()=>{
                vm.reNew = true;
            },200)
            //vm.handleClickStep(vm.steps[0]);
        },
        setSuccessType(id) {
            const vm = this;
            for (let i = 0; i < vm.steps.length; i++) {
                if (id === vm.steps[i].config.id){
                    delete vm.steps[i].type;
                }
            }
            vm.reNew = false;
            setTimeout(()=>{
                vm.reNew = true;
            },200)
            //vm.handleClickStep(vm.steps[0]);
        }

    },
}
</script>
<style lang="less" scoped>
.steps {
    &__item {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-name {
            width: 110px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0 4px;

            /deep/ input {
                border: none;
                height: auto;
                line-height: 1;
                background-color: transparent;
            }
        }
    }

    .step {
        padding: 6px;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 100px;
        display: flex;
        align-items: center;
        align-self: stretch;
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 0.04);

        &__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            width: 28px;
            height: 28px;
            color: #0088ff;
            border-radius: 50%;

            i {
                font-size: 13.3px;
            }
        }

        &__title {
            flex: 1;
            padding-left: 14px;
            box-sizing: border-box;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }

        &__more {
            display: none;
            padding-right: 6px;

            i {
                font-size: 14px;
                color: #0088ff;
            }
        }

        &__line {
            position: relative;
            height: 34px;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.15);

            &:hover {
                .icon-l-check-add {
                    display: block;
                }
            }

            .icon-l-check-add {
                display: none;
                position: absolute;
                top: 50%;
                left: 50%;
                font-size: 14px;
                transform: translate(-50%, -50%);
                cursor: pointer;
                z-index: 9;
                color: #0088ff;
            }

            &::before {
                position: absolute;
                bottom: 0;
                left: -5px;
                content: "";
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid #ccc;
            }
        }

        &__add {
            background: #ffffff;
            border: 1px solid rgba(0, 0, 0, 0.09);
            cursor: pointer;

            .step__icon {
                background: #0088ff;

                i {
                    color: #fff;
                }
            }

            .step__title {
                color: #0088ff;
            }
        }

        &:hover {
            background: rgba(0, 136, 255, 0.05);

            .step__title {
                color: #0088ff;
            }

            .step__more {
                display: block;
            }
        }

        &.is-active {
            background: rgba(0, 136, 255, 0.05);
            border: 1px solid rgba(0, 136, 255, 0.65);

            .step__icon {
                background-color: #0088ff;

                i {
                    color: #fff;
                }
            }

            .step__title {
                color: #0088ff;
            }

            .step__more {
                display: block;
            }
        }

        &.is-error {
            background: rgba(250, 64, 64, 0.05);
            border: 1px solid rgba(250, 64, 64, 0.12);

            .step__icon {
                background-color: #fff;

                i {
                    color: #fa4040;
                }
            }

            .step__title {
                color: #fa4040;
            }

            .step__more {
                i {
                    color: #fa4040;
                    color: #fa4040;
                }
            }
        }
    }
}
</style>
