import {globalBus} from "@/api/globalBus";
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../dataset-manage/service-mixins/service-mixins";
import {mapGetters} from "vuex"
import * as $ from "jquery";
import $right from "@/assets/data/right-data/right-data"
import * as source from "../../../dataSources/service-mixins/service-mixins"


export default {
    name: "tree" ,
    mixins : [treeMethods , commonMixins , servicesMixins , source.servicesMixins],
    computed : {
        ...mapGetters(["userRight"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
            this.$refs.ds_tree.filter(val);
        }
    },
    data(){
        return {
            ds_data : [],
            activeName : "custom" ,
            tab_panel : [
                {
                    label :'自定义' ,
                    value : 'custom'
                },{
                    label :'标准' ,
                    value : 'standard'
                }
            ],
            defaultProps : {
                value : "id" ,
                label : "label" ,
                children : "children"
            },
            treeMenu : [] ,
            addBtnTxt : '新增子目录' ,
            menu: [
                {
                    name: '修改目录',
                    fn: this.editNode ,
                    show : () => true
                }, {
                    name: '删除目录',
                    fn: this.deleteNode ,
                    show : () => true
                }
            ],
            btnTxt : "授权数据集" ,
            rightOfAdd : $right["dataSetOperationAccreditLogicDataObj"] ,

        }
    },
    methods : {
        isDrag(node){
            return false;
        },
        dragend() {
            globalBus.$emit('dragend');
        },
        addNode(plug) {
            globalBus.$emit('plugNode', plug);
        },

        async initTree(){
            const vm = this , {datasetServices , datasetMock , settings , listApi ,listMock } = this;
            let services = vm.getServices(datasetServices , datasetMock) , listServices = vm.getServices(listApi , listMock);
            settings.loading = true;
            services.queryDataSetTree(true , settings).then(res => {
                if(res.data.status === 0){
                    let result =  res.data.data;
                    let curData = result[0].children.filter(d => d.code === "DATASET_DIR_MY")[0] , curId = curData? curData.id : "";
                    vm.data = result;
                    vm.$nextTick(()=>{
                        let {treeNodeId} = vm.$route.params;
                        if(treeNodeId){
                            curId = treeNodeId;
                            vm.nodeClickByKey(treeNodeId);
                        }else {
                            vm.$emit("getTableData" , curData);
                        }
                        vm.$nextTick(()=>{
                            if(vm.$refs.tree){
                                vm.$refs.tree.setCurrentKey(curId);
                            }

                        });
                    });
                }
            });
            listServices.getDataObjectTree(settings).then(res => {
                if(res.data.status === 0) {
                    vm.ds_data = res.data.data;
                }
            })
        },
        getCenterTable(nodeData) {
            if(nodeData.isParent === true)return;
            this.nodeClick(nodeData);
        },
        nodeClickByKey(key){
            let node = this.$refs.tree.getNode(key);
            this.nodeClick(node.data);
        },
        empowerShow(){
            this.$emit("empowerShow");
        },
        rightMenu( event , node, object ) {
            $(".el-dropdown-menu").hide();
            this.treeMenu = this.menu;
        },
        nodeClick(data){
            let curData = {}; const vm = this;
            if(data.id){
                curData = data;
                vm.$emit('getTableData',curData);
            }else {
                vm.$nextTick(()=>{
                    if(vm.$refs.tree){
                        curData = this.$refs.tree.getNode(data);
                        vm.$refs.tree.setCurrentKey(data);
                        vm.$emit('getTableData',curData.data);
                    }
                });
            }

        },
        menuCommand(command){
            command.fn();
        },
    }
}