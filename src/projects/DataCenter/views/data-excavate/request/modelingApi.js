/**
 * 模板配置 接口
 * @Author: wangjt
 * @Date: 2020-05-13
 * @Project cicada-dataCenter-webui
 */

import * as reqUtil from '@/api/reqUtil'
const root = "/manage/dataWarehouse/";
const root1 = "/template/";

/*
*  数仓树 数据
 * @param keyword
 * @returns {Promise<*>}
* */
export async function getModelDirTree( keyword="" ) {
    return await reqUtil.get({
        url: root + 'getModelDirTree',
    }, {keyword} );
}
/*
*  获取模板
 * @param
 * @returns {Promise<*>}
* */
export async function getList() {
    return await reqUtil.get({
        url: root1 + 'list',
    });
}

/*
*  上传前重名验证
 * @param modelName
 * @returns {Promise<*>}
* */
export async function check(modelName) {
    return await reqUtil.get({
        url: root1 + 'check',
    },{modelName});
}
/*
*  删除
 * @param modelName
 * @returns {Promise<*>}
* */
export async function deleteM(modelName) {
    return await reqUtil.get({
        url: root1 + 'delete',
    },{modelName});
}

/*
*  下载
 * @param modelName
 * @returns {Promise<*>}
* */
export async function download() {
    return root1 + 'download?modelName=';
}

/*
*  模板配置
 * @param modelName
 * @returns {Promise<*>}
* */
export async function parse(modelName) {
    return await reqUtil.get({
        url: root1 + 'parse',
    },{modelName});
}
/*
*  打开模板
 * @param modelName
 * @returns {Promise<*>}
* */
export async function create(modelName , inputParams) {
    return await reqUtil.post({
        url: root1 + 'create',
    },{modelName , inputParams});
}
