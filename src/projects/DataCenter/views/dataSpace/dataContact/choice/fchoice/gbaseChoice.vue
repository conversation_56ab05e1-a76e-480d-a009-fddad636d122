<template>
	<div>
		<el-form  ref="dataSetInfo" :rules="rules" :model="dataSetInfo">
			<el-form-item label-width="120px" label="数据源名称:" prop="dbName">
				<el-input size="mini" v-model.trim="dataSetInfo.dbName" placeholder="自定义的名称，用于标识该数据连接" ></el-input>
			</el-form-item>
			<slot name="custom"></slot>
			<el-form-item label-width="120px" label="数据库版本:" prop="softwareId">
				<el-select size="mini" v-model="dataSetInfo.softwareId" :disabled="disabledButton">
					<el-option v-for="(item , index) in dataVersion" :key="index" :label="item.version" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label-width="120px" label="数据库实例名:" prop="dbCode" >
				<el-input size="mini" v-model.trim="dataSetInfo.dbCode" placeholder="用于登录数据库的实例名，例如:metadata" :disabled="disabledButton"></el-input>
			</el-form-item>
			<el-form-item label-width="120px" label="数据库地址:" prop="ip" >
				<el-input size="mini" v-model.trim="dataSetInfo.ip" placeholder="部署的数据库所在的服务器IP地址，例如:**************" @change="serachIp" :disabled="disabledButton"></el-input>
			</el-form-item>
			<el-form-item label-width="120px" label="端口号:" prop="port" >
				<el-input size="mini" v-model.trim.number="dataSetInfo.port" placeholder="数据库的端口号，例如：5432" :disabled="disabledButton"></el-input>
			</el-form-item>
			<el-form-item label-width="120px" label="用户名:" prop="username">
				<el-input size="mini" v-model.trim="dataSetInfo.username" placeholder="用于登录数据库的用户名"></el-input>
			</el-form-item>
			<el-form-item label-width="120px" label="密码:">
				<el-input type="password" autocomplete="new-password" size="mini"  v-model.trim="dataSetInfo.password" placeholder="用于登录数据库的用户密码" ></el-input>
			</el-form-item>
		</el-form>
		<el-button type="text" @click="showParams=!showParams">高级参数</el-button>
		<el-divider direction="horizontal" v-if="showParams"></el-divider>
		<el-form ref="paramInfo" :rules="paramInfoRules" :model="paramInfo" v-if="showParams">
			<el-form-item label-width="120px" label="jdbcURL:" prop="jdbcUrl">
				<el-input size="mini" v-model.trim="paramInfo.jdbcUrl" placeholder="请输入分布式数据库连接地址"></el-input>
			</el-form-item>
		</el-form>
	</div>
</template>

<script>
	import {commonMixins} from "@/api/commonMethods/common-mixins";
	import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
	export default {
		name: "gbaseChoice",
		mixins : [commonMixins , servicesMixins],
		watch: {
			paramInfo: {
				handler() {
					this.changeDataSetInfo();
				},
				deep: true
			},
			dataSetInfo: {
				handler() {
					this.changeDataSetInfo();
				},
				deep: true
			},
			dataVersion: {
				handler() {
					if(this.isAdd){
						this.dataSetInfo.softwareId = this.dataVersion&&this.dataVersion[0]?this.dataVersion[0].id:'';
					}
				},
				deep: true
			}
		},
		props: {
			dataVersion: Array,
			isAdd: Boolean,
		},
		methods: {
			setDataSetInfo(val) {
				this.disabledButton = false;
				this.dataSetInfo.dbCode = val.dbCode;
				this.dataSetInfo.dbName = val.dbName;
				this.dataSetInfo.ip = val.ip;
				this.dataSetInfo.port = Number(val.port);
				this.dataSetInfo.username = val.username;
				this.dataSetInfo.password = val.password;
				this.dataSetInfo.softwareId = val.version||val.softwareId;
				this.paramInfo.jdbcUrl = val.jdbcUrl;
			},
			valdate() {
				let flag = true;
				this.$refs['dataSetInfo'].validate((valid) => {
					if (!valid) {
						flag = false;
						return false;
					}
				})
				return flag;
			},
			serachIp(val) {
				let reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
				if(reg.test(val)) {
					this.$emit("serachIp", val);
				}
				else {
					this.$message.error("请输入正确的数据库地址");
				}
			},
			changeDataSetInfo() {
				this.dataSetInfo.jdbcUrl = this.paramInfo.jdbcUrl ;
				this.$emit("changeDataSetInfo", this.dataSetInfo);
			}
		},

		data() {
			const validateIP = (rule, value, callback) => {
				let reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
				if (!reg.test(value)) {
					callback(new Error("请输入正确的数据库地址"));
				} else {
					callback();
				}
			};
			return {
				paramInfoRules : {
					jdbcUrl: [
						// {required: true, message: '请输入分布式数据库连接地址', trigger: 'blur'},
					],
				},
				paramInfo : {
					jdbcUrl :""
				},
				showParams :true,//是否展示高级参数
				disabledButton: false,
				rules: {
					softwareId: [{required: true, message: '请选择数据库版本', trigger: 'blur'},],
					ip: [
							{required: true, message: '请输入数据库地址', trigger: 'blur'},
							{required: true,validator: validateIP,trigger: "blur"}
							],
					port: [{required: true, message: '请输入端口号', trigger: 'blur'}, { type: 'number', message: '端口必须为数字值'}],
					dbCode: [{required: true, message: '请输入数据库实例名', trigger: 'blur'}],
					dbName: [{required: true, message: '请输入数据库名称', trigger: 'blur'}],
					username: [{required: true, message: '请输入用户名', trigger: 'blur'}]
				},
				dataSetInfo: {
					softwareId:'',
					dbCode: "",//数据库实例名
					dbName: "",//数据库名称
					ip:"" ,//主机ip
					port: 5432,//端口号
					username:"", //用户名
					password: "",//密码
				},
			}
		},
		mounted(){
			if(this.isAdd){
				this.dataSetInfo.softwareId = this.dataVersion&&this.dataVersion[0]?this.dataVersion[0].id:'';
			}
		}
	}
</script>

<style scoped>

</style>
