<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <div class="ce-table_btns">
                <dg-button v-if="ifHasRight(addThemePortal) && !isOtherUser" @click="addPortal" type="primary">{{buttonTxt}}</dg-button>
            </div>
            <div class="ce-table__search">
                <el-input
                        size="mini"
                        placeholder="请输入名称搜索"
                        v-model.trim="inputValueTable"
                        v-input-filter="inputValueTable"
                        @input="inputFilterSpecial($event , 'inputValueTable')"
                        @keyup.enter.native="searchTableEvent"
                >
                    <i
                            class="el-icon-search el-input__icon poi"
                            slot="suffix"
                            @click="searchTableEvent">
                    </i>
                </el-input>
            </div>
        </div>
        <div class="d-s-r-table">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    noneImg
                    :max-height="tableBodyH"
                    :border="false"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action"
                        v-for="(item,index) in hasRightOptIcon"
                        :key="index"
                        v-if="index < 3 && item.show(row)"
                    >
                        <dg-button type="text"
                                :class="item.class"
                                @click="item.clickFn( row , $index)"
                                :title="item.name"
                                v-html="item.name"
                        ></dg-button>
                    </span>
                    <dir-edit-action v-if="countHasRightMenu > 3"
                                     placement="bottom"
                                     @command="menuCommand($event , row , $index)" :data="hasRightOptIcon.slice(3)"
                                     :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>{{moreTxt}}</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
            </common-table>
        </div>
        
    </div>
</template>

<script src="./list.js"></script>

<style scoped lang="less" src="./list.less"></style>
