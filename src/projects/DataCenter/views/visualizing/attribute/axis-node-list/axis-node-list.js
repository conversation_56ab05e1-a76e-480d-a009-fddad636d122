import DropdownOpt from '../DropdownOpt'
import CeSortable from "@/components/common/ce-drag-list/CeSortable";
export default {
    name: "AxisNodeList",
    components: {DropdownOpt , CeSortable},
    data() {
        return {}
    },
    props: {
        axis: Object,
        keyVal: String,
        showDrill: <PERSON>olean,
        hiddenOpt :{
            type : <PERSON><PERSON><PERSON>,
            default : false
        }
    },
    methods: {
        funcName(node){
            return node.label.match(/\(.*\)/) && node.label.match(/\(.*\)/).length ? node.label.match(/\(.*\)/)[0] : "";
        },
        changeFastCount() {
            this.$emit("changeFastCount" , this.axis);
        },
        changeVal(arg, axis, index) {
            this.$emit("changeVal", arg, axis, index);
        },
        duplicateFun(val, axis, index) {
            this.$emit("duplicateFun", val, axis, index)
        },
        nodeFilter(val, axis, index) {
            this.$emit("nodeFilter", val, axis, index)
        },
        showDrilling(axis, index) {
            this.$emit("showDrilling", axis, index)
        },
        deleteNode(key, index) {
            this.$emit("deleteNode", key, index)
        },
        checkMove(e){
            if (e.dataTransfer.types.length && e.dataTransfer.types[0] !== 'node') {
                e.stopPropagation();
            }
        }
    }
}
