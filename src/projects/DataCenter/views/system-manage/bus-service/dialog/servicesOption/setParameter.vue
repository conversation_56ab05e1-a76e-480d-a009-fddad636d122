<template>
    <div>
        <parameter :type="type" title="接口请求参数"></parameter>
        <parameter :type="type" title="接口响应参数"></parameter>
        <div v-if="type!='look'">
            <dg-button v-footer @click="close">取消</dg-button>
            <dg-button v-footer type="primary" @click="submit">确定</dg-button>
        </div>
    </div>
</template>

<script>
    import parameter from './parameter'
    export default {
        name: "setParameter",
        props:{
            info: Object,
            type: String,
        },
        components:{
            parameter,
        },
        data(){
            return {

            }
        },
        methods:{
            close(){
                this.$emit('close');
            },
            submit(){
                const vm = this;
                vm.close();
            }
        }
    }
</script>

<style scoped>

</style>