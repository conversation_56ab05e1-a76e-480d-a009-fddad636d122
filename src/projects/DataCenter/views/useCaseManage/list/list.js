import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {transList} from "@/projects/DataCenter/views/modeling/dialog/rapid-analysis/model/model-panel/trans-list-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins"
import {createComponent} from '@/api/commonMethods/createComponent';
import helpDocument from "@/components/signed-components/helpDocument/helpDocument";
export default {
    name: "list",
    mixins: [commonMixins, common, listMixins, coustTableH , transList, servicesMixins],
    components: { helpDocument, },
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        }
    },
    data() {
        return {
            activeN : "",
            inputValueTable: "",
            buttonTxt: "批量分享",
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "用例名称",
                    minWidth: 100,
                    align: "left"
                },
                {
                    prop: "path",
                    label: "所属目录",
                    align: "center"
                },
                {
                    prop: "memo",
                    label: "用例描述",
                    width: "130",
                    resizable: false
                },
                {
                    prop: "operate_time",
                    label: "更新时间",
                    align: "center"
                },
                {
                    prop: 'operate',
                    label: '操作',
                    width: 300,
                    align: "center",
                    resizable: false
                }
            ],
            total: 0,
            operateIcons: [

                {
                    icon: "&#xe650;",
                    name: "编辑",
                    clickFn: this.edit,
                    show: (row) => true,
                    condition: (right) => right.indexOf($right['useCaseManagementEditUseCase']) > -1
                },
                {
                    icon: "&#xe6c0;",
                    name: "删除",
                    clickFn: this.deleteUseCase,
                    show: () => true,
                    condition: (right) => right.indexOf($right['useCaseManagementDeleteUseCase']) > -1,
                },
                {
                    icon: "&#xe6bf;",
                    name: "运行",
                    clickFn: this.runApi,
                    show: () => true,
                    condition: (right) => right.indexOf($right['useCaseManagementRunUseCase']) > -1
                },
                // {
                //     icon: "&#xe6c0;",
                //     name: "订阅测试",
                //     clickFn: this.subscribeTest,
                //     show: (r,row) => { return row.serviceType === '比对订阅'},
                //     condition: (right) => right.indexOf($right['serviceManagementDeactivate']) > -1
                // },
                // {
                //     icon: "&#xe6c0;",
                //     name: "分享",
                //     clickFn: this.share,
                //     show: () => true,
                //     condition: (right) => right.indexOf($right['serviceManagementSharing']) > -1
                // },
                // {
                //     icon: "&#xe6c0;",
                //     name: "查看版本",
                //     clickFn: this.lookVersion,
                //     show: () => true,
                //     condition: (right) => true
                // },

            ],
            createUseCase: "添加用例",
            apiName :"",
            moreTxt:"更多" ,
        }
    },
    methods: {
        setDir(value, id , node){
            this.dirId = id;
            this.changePage(1);
        },
        runApi(row) {
            const vm=this;
            let layer = vm.$dgLayer({
                title : "运行" ,
                content : require("./dialog/runUseCase"),
                maxmin:false,
                move:false,
                props : {
                    row : row,
                },
                area : ["904px" , "620px"],
                on : {
                    submit(){

                    }
                },
                success: function (layero, index) {
                    createComponent(helpDocument , { code : 'runUseCase'} , layero.find(".layui-layer-title")[0])
                },
            })
        },
        deleteUseCase(row) {
            const vm = this, {settings} = this;
            let services = vm.$services("userCaseManagement");
            vm.confirm('提示', `确定要删除该用例?`, () => {
                settings.loading = true;
                services.deleteUseCase(row.id, settings).then(res => {
                    if (res.data.status === 0) {
                        this.$message.success("删除成功!");
                        this.changePage(1);
                    }
                })
            })
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        reNewTable(){
            this.changePage(1);
        },
        /**
         * 用例编辑
         * @param {*} row
         */
        edit(row) {
            const vm = this;
            let layer = vm.$dgLayer({
                title : "编辑用例" ,
                content : require("./dialog/addUseCase.vue"),
                maxmin:false,
                move:false,
                props : {
                    row
                },
                area : ["804px" , "620px"],
                on : {
                    submit(){
                        let [tableData, form ,detailInfo ,settings] = [...arguments];
                        vm.editUseCase(tableData, form, detailInfo,settings , layer);
                    }
                },
                success: function (layero, index) {
                    createComponent(helpDocument , { code : 'editUseCase'} , layero.find(".layui-layer-title")[0])
                },
            })
        },
        editUseCase(tableData, form,detailInfo,settings, layer) {
            const vm = this;
            if(vm.caseValidate(tableData)) return settings.loading = false;
            let services = vm.$services("userCaseManagement");
            let useCaseApis = [];
            tableData.forEach(item =>{
                useCaseApis.push(item.paramObject)
            })
            let params = {
                'useCaseId' : detailInfo.useCaseId,
                "useCaseName": form.useCaseName,
                "classifyId": form.classifyId,
                "memo": form.memo,
                "useCaseApis":useCaseApis
            };
            services.editUseCase(params , settings).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("修改成功");
                    layer.close(layer.dialogIndex);
                    vm.changePage();
                }
            })
        },
        /**
         * 清空服务编辑入口获取的数据
         */
        clear(){
            this.isServiceManageUpdate = false;
            this.modelInfo = {};
        },
        getButton(row){
            return row.status === '已启用';
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },

        /**
         * 新建用例
         */
        APICreate() {
            const vm=this;
            let layer = vm.$dgLayer({
                title : "添加用例" ,
                content : require("./dialog/addUseCase.vue"),
                maxmin:false,
                move:false,
                props : {
                    currentNodeId : vm.dirId,
                },
                area : ["804px" , "620px"],
                on : {
                    submit(tableData, form ,settings){
                        vm.addUseCase(tableData, form , settings, layer);
                    }
                },
                success: function (layero, index) {
                    createComponent(helpDocument , { code : 'addUseCase'} , layero.find(".layui-layer-title")[0])
                },
            })
        },
        /**
         * 添加用例，修改的 校验
         */
        caseValidate(tableData){
            if (tableData.length === 0) {
                this.$message.warning('请至少添加一个API');
                return true;
            }
            let caseItem = tableData.filter(item => !item.paramObject);
            if(caseItem.length) {
                let names = caseItem.map(li => li.name);
                this.$message.warning(`" ${names.join('、')} "参数未配置完整`);
                return true;
            }
            return false;
        },
        addUseCase(tableData, form ,settings, layer) {
            const vm = this;
            let services = vm.$services("userCaseManagement");
            let useCaseApis = [];
            if(vm.caseValidate(tableData)) return settings.loading = false;
            tableData.forEach(item =>{
                useCaseApis.push(item.paramObject)
            })
            let params = {
                "useCaseName": form.useCaseName,
                "classifyId": form.classifyId,
                "memo": form.memo,
                "useCaseApis":useCaseApis
            };
            services.newUseCase(params , settings).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("添加成功");
                    layer.close(layer.dialogIndex);
                    vm.changePage();
                }
            })
        },
        async changePage(index=1) {
            const vm = this, { settings} = this;
            let services = vm.$services("userCaseManagement");
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                pageSize: vm.paginationProps.pageSize,
                pageIndex: index,
                useCaseName : vm.inputValueTable,
                classifyId: vm.dirId,
            };
            vm.tableData = [];
            await services.getUseCaseList(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.tableData = res.data.data.dataList ? res.data.data.dataList : [];
                    vm.total = res.data.data.totalCount;
                }
            })

        },
        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
    },
    created() {

    }
}
