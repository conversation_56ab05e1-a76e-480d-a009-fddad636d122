<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/6/20
-->
<template>
<div class="analysis-outer">
    <div class="analysis-icon" :style="{'--icon-bg-color':iconColor}">
        <i :class="['analysis-icon_val',icon]" ></i>
    </div>
    <dl class="analysis-cont">
        <dt class="analysis-title">{{title}}</dt>
        <dd class="analysis-value analysis-text" v-if="type==='text'" >{{value | numberToCurrencyNo}}</dd>
        <dd class="analysis-value" v-else-if="type === 'rate'">
            <el-rate
                    :value="value"
                    disabled >
            </el-rate>
        </dd>
    </dl>
</div>
</template>

<script>
export default {
    name: "analysisCard",
    filters:{
        numberToCurrencyNo(value) {
            if (!value) return 0
            // 获取整数部分
            const intPart = Math.trunc(value)
            // 整数部分处理，增加,
            const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
            // 预定义小数部分
            let floatPart = ''
            // 将数值截取为小数部分和整数部分
            const valueArray = value.toString().split('.')
            if (valueArray.length === 2) { // 有小数部分
                floatPart = valueArray[1].toString() // 取得小数部分
                return intPartFormat + '.' + floatPart
            }
            return intPartFormat + floatPart
        }
    },
    props:{
        title : {
            type: String,
            default : '标题内容'
        },
        icon: String,
        iconColor: {
            type : String,
            default: '#1890ff',
        },
        type : {
            type : String,
            default: "text" , // text ， rate
        },
        value : {
            type : [Number , String],
            default : ''
        }
    },
    data() {
        return {}
    }
}
</script>

<style scoped lang="less">
@size: 3.75rem;
.analysis {
    &-outer {
        height:@size;
        display:flex;
        padding:12px;
        gap:1.25rem;
    }

    &-icon {
        flex-shrink: 0;
        width: @size;
        height: @size;
        background-color:  var(--icon-bg-color);
        border-radius: 50%;
        text-align:center;
        color: #fff;
        &_val {
            font-size: 2rem;
            line-height: @size;
            font-weight: bold;
        }
    }
    &-cont {
        flex:1;
        display: flex;
        flex-direction:column;
    }
    &-title {
        color: @fontSecColor;
        font-size: .875rem;
    }
    &-value {
        flex: 1;
        display: flex;
        align-items:center;
        font-size: 1.8rem;
        color: @fontMainColor;
        font-family: Arial,serif;
    }

    &-text {
        overflow:hidden;
        white-space:nowrap;
        text-overflow:ellipsis;
    }
}

</style>
