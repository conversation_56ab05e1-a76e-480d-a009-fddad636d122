/**
 * 服务空间 导入导出
 * @Author: chenzt
 * @Date: 2022-10-31
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/dataTransImportExport/";

/**
 * 获取指定API
 * @param {*} settings 
 */
export async function getServiceClassify ( settings,) {
    return await reqUtil.get({
        url : root + "getServiceClassify",
        settings : settings
    },{})
}

/**
 * 获取指定用例
 * @param {*} settings 
 */
export async function queryUseCaseClassifyTree ( settings,) {
    return await reqUtil.get({
        url : root + "queryUseCaseClassifyTree ",
        settings : settings
    },{})
}

/**
 * 导入解析
 * @param {*} param0 
 */
export async function parseDataCenterServiceModel({file , settings}){
    return await reqUtil.post({
        url : '/dataTransImportExport/parseDataCenterServiceModel' ,
        settings
    },file,{headers: {
        'Content-Type': 'multipart/form-data;charset=utf-8',
    }});
}