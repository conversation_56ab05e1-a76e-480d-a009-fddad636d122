<template>
    <el-dialog
            title="算子信息"
            :close-on-click-modal="false"
            :visible.sync="visible"
            :modal-append-to-body="false"
            :width="width"
    >
        <el-form :model="node" ref="dataForm" label-width="80px" @submit.native.prevent>
            <el-form-item label="名称" prop="name" :rules="rule">
                <el-input v-model.trim="node.name" @keyup.enter.native="submit"></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer">
            <el-button size="small" @click="visible = false" >取消</el-button>
            <el-button size="small" type="primary" @click="submit" >确定</el-button>
        </span>
    </el-dialog>
</template>

<script>
    export default {
        name: "NodeForm",
        data() {
            return {
                width :"500px" ,
                visible: false,
                node: {
                    name:'',
                    id :''
                },
                rule : [
                    {required :true , message : '插件名称不为空' , trigger : 'change'}
                ]
            }
        },
        methods: {
            init(data, id) {
                this.visible = true ;
                data.nodeList.forEach((node) => {
                    if (node.id === id) {
                        this.node.name = node.name;
                        this.node.id = node.id;
                    }
                })
            },
            submit(){
                if(this.node.name === ''){
                    return;
                }
                else if (this.node.name.length > 300) {
                    this.$message.warning("名称长度不能超过300！");
                    return;
                }
                this.visible = false ;
                this.$emit('change',this.node);
            }
        }
    }
</script>

<style scoped>

</style>