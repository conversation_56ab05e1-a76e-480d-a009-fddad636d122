/**
 * ftp上传 插件 接口
 * @Author: wangjt
 * @Date: 2020-06-15
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/ftpPlugin/";

/*
* 初始化
* @params tranStepId
* @returns {Promise<*>}
* */

export async function fileUploadPage( tranStepId , settings) {
    return await reqUtil.get({
        url: root + 'fileUploadPage' ,
        settings : settings
    }, {tranStepId});
}

/*
* 初始化
* @params tranStepId
* @returns {Promise<*>}
* */

export async function savefileUploadData( tranStepId ,fuVo , settings) {
    return await reqUtil.post({
        url: root + 'savefileUploadData?tranStepId='+tranStepId ,
        settings : settings
    }, fuVo);
}
