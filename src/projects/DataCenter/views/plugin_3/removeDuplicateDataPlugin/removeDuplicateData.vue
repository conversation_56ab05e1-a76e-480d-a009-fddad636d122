<template>
    <div class="removeDuplicateData" v-loading="settings.loading">
        <!-- <dialog1 ref="detailDialog" :sourceData="sourceData" :rowData="rowData" @getChooseColumns="getChooseColumns"></dialog1> -->
        <el-transfer
                filterable
                :titles="['源字段列表', '已选字段列表']"
                v-model="value"
                :data="data">
        </el-transfer>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import CommonTable from "@/components/common/CommonTable";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins"

    export default {
        name: "removeDuplicateData",
        props: {
            sourceData: Object,
            rowData: Object
        },
        mixins: [common, commonMixins, servicesMixins],
        components: {
            CommonTable,
            PreView,
        },
        data() {
            return {
                columns:[],
                canSave: false,
                isSeniorShow: {},
                addTxt: '点击添加字段处理',
                data:[],
                value:[],
                columnsData:[],
                filterMethod(query, item) {
                    return data.indexOf(query) > -1;
                }
            }
        },
        methods: {
            getSaveVo(){
                this.columnsData = [];
                this.value.forEach(element => {
                    this.data.forEach(item => {
                        if (element === item.key) {
                            let object = {
                                columnName:item.attribute.columnName,
                                columnZhName:item.attribute.columnZhName,
                                columnCode:"",
                                columnId:item.attribute.dataColumnId,
                                type:item.attribute.columnType,
                                length:item.attribute.length,
                                precision:item.attribute.precsn,
                            }
                            this.columnsData.push(object);
                        }
                    });
                });
            },
            async save(saveName,showTip) { //保存后才能预览
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                vm.getSaveVo();
                if (this.columnsData.length === 0) {
                    this.$message.warning("请选择去重字段");
                    return
                }
                let dataDistinctMeta = {
                    distinctColumnSet : this.columnsData,
                };
                services.removeDistinctSave(vm.sourceData.id, dataDistinctMeta, settings).then(res => {
                    if(res.data.status === 0 ) {
                        showTip && this.$message.success("保存成功");
                        if(saveName) saveName();
                        vm.$emit("setPreviewAble");
                    }
                })
            },
            async init(){
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                await services.queryData(vm.sourceData.id, settings).then(res => {
                    this.data = [];
                    let result = res.data.data;
                    if (result.fieldFilteringMeta.distinctColumnSet && result.fieldFilteringMeta.distinctColumnSet.length !== 0 ) {
                        result.fieldFilteringMeta.distinctColumnSet.forEach(element => {
                            result.inputColumn.forEach(item => { 
                                if (item.columnName === element.columnName) this.value.push(element.columnName);
                            })
                        });
                    }
                    result.inputColumn.forEach(element => {
                        let object = {
                            label: element.columnZhName||element.columnName,
                            key: element.columnName,
                            attribute: element
                        };
                        this.data.push(object);
                    });
                })
                this.getSaveVo();
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
        },
        created() {
            this.init();
        },
    }
</script>

<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
<style lang="less" scoped>
.removeDuplicateData {
    height: 100%;
}
/deep/ .el-transfer {
   
    height:100%;
    background: #fff;
    &-panel {
        height: 100%;
        width: calc(50% - 80px);
        background: #fff;

        &__header {
            background: fade(#1890ff, 20%);
        }
        &__body {
            height: calc(100% - 51px);
            border-top: 1px solid transparent;
        }
        &__list {
            height: calc(100% - 56px);
            box-sizing: border-box;
        }
    }

    &__button {
        width: 100px;
        height: 24px;
        line-height: 22px;
        text-align: center;
        padding: 0;
        min-width: auto;
        display: block;
        margin-left:0;
    }
}
.tree-transfer {
    /deep/ .el-transfer {
        &-panel {
            height: 500px;
            box-sizing: border-box;

            &__body {
                height: calc(100% - 40px);
                box-sizing: border-box;
            }

            &__list {
                height: 100%;
                overflow: hidden;
            }
        }
    }

    /deep/ .el-tree {
        height: 386px;
        overflow-y: auto;
    }
}
</style>
