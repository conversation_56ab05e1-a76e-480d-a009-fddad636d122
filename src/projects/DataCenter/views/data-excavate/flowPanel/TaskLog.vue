<template>
    <div>
        <common-dialog custom-class="log" :width="width"
                       title="任务日志"
                       :visible.sync="dialogFormVisible"
                       @closed="clearD"
        >
            <common-table
                    height="calc(75vh - 154px)"
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"

            >
                <template slot="operate" slot-scope="{row , $index}">
                    <i
                            class="icon ce_link model_status"
                            v-for="(col , inx) in operateIcons"
                            :key="inx"
                            :title="col.tip"
                            v-html="col.icon"
                            v-if="col.show(isSparkTask)"
                            @click="col.clickFn(row,$index)"
                    ></i>
                </template>
            </common-table>
        </common-dialog>
        <!--        查看历史任务日志-->
        <HistoryTask ref="historyTask"/>
        <HistoryExecute ref="historyExecute"/>
        <TaskLogDetail ref="logDetail"/>
    </div>
</template>

<script>
    import HistoryTask from "./HistoryTask"
    import HistoryExecute from "./HistoryExecute"
    import TaskLogDetail from './TaskLogDetail'
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {common} from "@/api/commonMethods/common";

    export default {
        name: "TaskLog",
        mixins: [commonMixins, common],
        components: {
            HistoryTask,
            HistoryExecute,
            TaskLogDetail
        },
        data() {
            return {
                dialogFormVisible: false,
                width: '1200px',
                active: true,
                // 分页
                currentPage: 1,
                total: 0,
                pageSize: 10,
                openDialog: false,
                isSparkTask: false,
                rowData: {},
                tableData: [],
                HistoryTableData: [],
                tHeadData: [
                    {
                        prop: "taskName",
                        label: "任务名称",
                        width: "",
                        align: "left"
                    }, /*{
                        prop: "taskType",
                        label: "任务类型",
                        width: "",
                        align: "center"
                    },*/
                    {
                        prop: "startTime",
                        label: "开始时间",
                        width: "180",
                        align: "center"
                    },
                    {
                        prop: "endTime",
                        label: "结束时间",
                        width: "180",
                        align: "center"
                    },
                    {
                        prop: "executeResult",
                        label: "执行结果",
                        width: "",
                        align: "center"
                    },
                    {
                        prop: "executeInfo",
                        label: "异常信息",
                        width: "",
                        align: "center",
                        resizable: false
                    }, {
                        prop: "operate",
                        label: "操作",
                        align: "center",
                        width: '200',
                        resizable: false
                    }

                ],
                operateIcons: [
                    {icon: "&#xe660;", tip: "查看历史任务日志", show: () => true, clickFn: this.historyTask},
                    /*  {icon: "&#xe72c;", tip: "查看历史执行日志",show : (isSparkTask)=> !isSparkTask, clickFn: this.historyExecute},*/
                    // {icon: "&#xe76b;", tip: "日志详情", clickFn: this.logDetails},
                    {icon: "&#xe65f;", tip: "删除日志", show: () => true, clickFn: this.deleteLog},
                ]
            };
        },
        methods: {
            changeSize(val) {
                this.paginationProps.pageSize = val;
                this.changePage(1);
            },
            insertStr(soure, start, newStr) {
                return soure.slice(0, start) + newStr + soure.slice(start);
            },
            show(rowData) {
                this.rowData = rowData;
                this.dialogFormVisible = true;
                this.changePage(1);
            },
            //查看历史任务日志
            historyTask(row) {
                const _this = this;
                if (row.executeResult === "执行中") {
                    this.$message.info("任务正在执行中！");
                    return
                }
                _this.$axios.get("/mlsql/getLogsByTastsId?startTime=" + row.startTime + "&endTime=" + row.endTime + "&tasksString=" + row.tasksString + '&groupId=' + row.taskId).then(res => {
                    if (res.data.data.length === 0) {
                        if (row.executeResult === "执行成功") {
                            this.$message.warning("查询不到执行日志，或是执行日志已过期！");
                        } else {
                            this.$message.error("任务解析异常，未提交到引擎执行，暂无执行日志！");
                        }

                    } else {
                        if (res.data.status === 0) {
                            _this.HistoryTableData = [];
                            res.data.data.forEach(item => {
                                let execStatus = item.status;
                                switch (item.status) {
                                    case 'SUCCEEDED':
                                        execStatus = '执行成功';
                                        break;
                                    case 'RUNNING':
                                        execStatus = '执行中';
                                        break;
                                    case 'FAILED':
                                        execStatus = '执行失败';
                                        break;
                                    case 'UNKNOWN':
                                        execStatus = '未知';
                                        break;
                                    default:
                                        execStatus = '执行失败';
                                        break;
                                }
                                // let tableNode = {
                                //
                                //     groupId: item.groupId,
                                //     name: item.name,
                                //     status: execStatus,
                                //     description: item.description === "None" ? "" : item.description,
                                //     submissionTime: item.submissionTime,
                                //     completionTime: item.completionTime,
                                //     jobId: item.jobId
                                // };
                                _this.HistoryTableData.push(item)
                            })
                            this.$refs.historyTask.show(row, _this.HistoryTableData);
                        }
                    }

                }).catch(err => {
                    console.log(err);
                    _this.$message.error("服务器异常，请联系管理员！")
                })


            },
            // 查看任务执行日志
            historyExecute(row) {
                // console.log(row);
                this.$refs.historyExecute.show(row);
            },
            // 日志详情
            logDetails(row) {
                this.$refs.logDetail.show(row);
            },
            formatDate: function (value) {
                let date = new Date(value);
                let y = date.getFullYear();
                let MM = date.getMonth() + 1;
                MM = MM < 10 ? ('0' + MM) : MM;
                let d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                let h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                let m = date.getMinutes();
                m = m < 10 ? ('0' + m) : m;
                let s = date.getSeconds();
                s = s < 10 ? ('0' + s) : s;
                return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
            },


            // 删除日志
            deleteLog(row, index) {
                const _this = this;
                this.confirm('提示', '确认删除日志？', () => {
                    _this.$axios.get("/mlsql/transDeleteHistoryTaskLog?taskId=" + row.taskId).then(res => {
                        if (res.data.status === 0) {
                            _this.$message({
                                type: 'success',
                                message: "删除成功！",
                            });
                            _this.changePage(this.currentPage);
                        } else {
                            _this.$message.error(res.data.msg);
                        }
                    }).catch(err => {
                        _this.$message.error("服务器发生异常，请联系管理员")
                    })
                })
            },
            changePage(index) {
                // this.currentPage = index;
                // console.log(this.rowData);
                const vm = this;
                vm.paginationProps.currentPage = index;
                let data = {
                    pageSize: vm.paginationProps.pageSize,
                    pageIndex: index,
                    sortField: "name",
                    sortOrder: "desc",
                    transMetaId: vm.rowData.transId,
                };
                vm.$axios.post("/taskLog/transTaskLogListPage", data).then(res => {
                    //console.log(res.data);
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.total = result.totalCount;
                        vm.isSparkTask = result.staInfo.isSparkTask;
                        vm.tableData = [];
                        if (result.dataList && result.dataList.length) {
                            result.dataList.forEach(item => {
                                /* let execStatus = item.EXEC_STATUS;
                                 switch (item.EXEC_STATUS) {
                                     case 'SUCCESS':
                                         execStatus = '执行成功';
                                         break;
                                     case 'EXECING':
                                         execStatus = '执行中';
                                         break;
                                     case 'ALLOT':
                                         execStatus = '分配中';
                                         break;
                                     case 'SCHEDULE':
                                         execStatus = '调度中';
                                         break;
                                     case 'WAITING':
                                         execStatus = '等待';
                                         break;
                                     case 'EXECUTED':
                                         execStatus = '已执行';
                                         break;
                                     case 'APPLY':
                                         execStatus = '准备中';
                                         break;
                                     case 'SCHEDULE_SUCCESS':
                                         execStatus = '等待分配';
                                         break;
                                     default:
                                         execStatus = '执行错误';
                                         break;
                                 }*/

                                /*let taskType = '';
                                switch (item.TASK_TYPE) {
                                    case 'SINGLE':
                                        taskType = '单机';
                                        break;
                                    case 'SPARK':
                                        taskType = 'spark';
                                        break;
                                    default:
                                        taskType = '分布式';
                                        break;
                                }*/
                                //let startTime = this.insertStr(this.insertStr(this.insertStr(this.insertStr(this.insertStr(item.START_TIME, 4, "-"), 7, "-"), 10, "  "), 14, ":"), 17, ":00");
                                //let endTime = this.insertStr(this.insertStr(this.insertStr(this.insertStr(this.insertStr(item.END_TIME, 4, "-"), 7, "-"), 10, "  "), 14, ":"), 17, ":00");
                                let startTime = this.formatDate(item.start_time)
                                let endTime = this.formatDate(item.end_time).indexOf("1970") === 0 ? "" : this.formatDate(item.end_time)
                                let execStatus = item.execute_status;
                                let jobDeteil = item.job_detail;
                                switch (item.execute_status) {
                                    case 'SUCCESS':
                                        execStatus = '执行成功';
                                        jobDeteil = '无';
                                        break;
                                    case 'ERROR':
                                        execStatus = '执行失败';
                                        jobDeteil = item.job_detail;
                                        break;
                                    case 'RUNNING':
                                        execStatus = '执行中';
                                        jobDeteil = '无';
                                        break;
                                    default:
                                        execStatus = '执行失败';
                                        break;
                                }

                                let taskNode = {
                                    taskId: item.id,
                                    taskName: item.trans_name,
                                    /*taskType: taskType,*/
                                    startTime: startTime,
                                    endTime: endTime,
                                    executeResult: execStatus,
                                    executeInfo: jobDeteil,
                                    tasksString: item.task_string,
                                    ...item
                                };
                                vm.tableData.push(taskNode);
                            })
                        }
                    }
                })
            },
            clearD() {
                this.openDialog = false;
            },
        },
    }
</script>

<style scoped>
    .pagination {
        margin: 5px 0;
        text-align: center;
    }

    .model_status {
        padding: 0 5px;
        font-size: 18px;
    }

</style>
