<template>
    <div class="RuleEditingPanel ce-plug_cont" v-loading="settings.loading">
        <div class="ce-tab_cont">
            <div class="select_class">
                <div class="ce-normal_item  is-require" >
                    <span>项目路径: </span>
                    <el-input v-model="projectPath" class="input_width">

                    </el-input>
                </div>
                <div class="ce-normal_item  is-require" >
                    <span>集群地址: </span>
                    <el-input v-model="rayIpAndPort" size="mini" class="input_width" @blur="clusterAddressJudge()">

                    </el-input>
                </div>
                <div class="ce-normal_item  is-require" >
                    <span>任务类: </span>
                    <dg-select v-model="taskClass"  filterable :data="taskOptions"  popper-class="optionLimitClass"
                                @change="logicChange(arguments)"></dg-select>
                </div>
                <div class="ce-normal_item  is-require" >
                    <span>是否预处理: </span>
                    <el-radio-group v-model="isPreDeal">
                        <el-radio label="yes">是</el-radio>
                        <el-radio label="no">否</el-radio>
                    </el-radio-group>
                </div>
            </div>

            <div class="ce-plug_attr1" >
                <el-form ref="form" :model="form" label-width="80px" class="ce-form__m0">
                    <el-table
                            class="h_auto-table"
                            border
                            :data="data"
                            size="mini"
                            :row-style="{height:'50px'}"
                            :cell-style="{padding:'0px'}">
                        <el-table-column align="center" label="JSON串">
                            <template slot-scope="scope">
                                <el-row :gutter="5">
                                    <el-col class="ce-normal_item is-require" :span="7">
                                        <span>{{scope.row.desc}} </span>
                                    </el-col>
                                    <el-col :span="17">
                                        <el-input size="mini" v-model="scope.row.value"
                                                    class="inputWidth" v-if="scope.row.chooseType === 'input'"></el-input>
                                        <el-select size="mini" v-model="scope.row.value"
                                                v-if="scope.row.chooseType === 'select'" filterable >
                                                <el-option
                                                    v-for="item in jsonSelectOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                        </el-select>
                                    </el-col>
                                </el-row>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import CommonTable from "@/components/common/CommonTable";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";

    export default {
        name: "algorithm",
        props: {
            sourceData: Object,
            rowData: Object
        },
        mixins: [common, commonMixins, servicesMixins],
        components: {
            CommonTable,
            PreView
        },
        data() {
            return {
                jsonSelectOptions:[],
                data : [], 
                form: {
                    name: '',
                },
                projectPath:"",
                rayIpAndPort:"",
                taskClass:"",
                taskClassPacketName:"",
                taskClassName:"",
                taskOptions: [],
                isPreDeal:"yes",
                activeName: 'setting_base',
                isSeniorShow: {},
                numType: [],
            }
        },
        methods: {
            save(...args){
              this.saveFn(...args);
            },
            preview(){
              this.previewFn();
            },
            logicChange(value) {
                this.taskClassPacketName = value[1].taskPackageName;
                this.taskClassName = value[1].value;
                value[1].params_info.forEach(element => {
                    let e = JSON.parse(JSON.stringify(element));
                    e.value = "";
                    e.chooseType = 'input';
                    this.data.push(e);
                });
                value[1].input_cols.forEach(element => {
                    let e = JSON.parse(JSON.stringify(element));
                    e.value = "";
                    e.chooseType = 'select';
                    this.data.push(e);
                });
            },
            clusterAddressJudge() {
                let reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;

                let re = new RegExp(reg);
                if(!re.test(this.rayIpAndPort)){
                    this.$message.warning("请填写有效的IP地址！");
                    return false;
                }else return true;
            },
            saveFn(saveName,showTip) {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);

                let clusterAddress = this.clusterAddressJudge();
                if(!clusterAddress) {
                    return ;
                }
                if (this.isPreDeal === '' || this.rayIpAndPort === '' || this.projectPath === '') {
                    this.$message.warning("请填写完整信息!");
                    return;
                }
                if (this.taskClass === '') {
                    this.$message.warning("请选择任务类!");
                    return;
                }
                let isJsonComplete = true;
                this.data.forEach(element => {
                    if (element.value === '') isJsonComplete = false;
                });
                if (!isJsonComplete) {
                    this.$message.warning("请填写完整JSON串信息!");
                    return;
                }

                
                let metaInfoS = this.setJsonVo();
                settings.loading = true;
                services.rayRunnerSavePlugin(vm.sourceData.id, metaInfoS, settings).then(res => {
                    if (res.data.status === 0) {
                        showTip && this.$message.success("保存成功!");
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                    }
                })
            },
            init() {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                services.queryAllTask(vm.sourceData.id, settings).then(res => {
                    let result = res.data.data;
                    result.forEach(element => {
                        element.taskMetaBean.input_cols.forEach(element1 => {
                            vm.numType.push(element1.col_Type);
                        });
                        
                        let taskOption = {
                            label: element.taskName,
                            value: element.taskClassName,
                            taskPackageName: element.taskPackageName,
                            params_info : element.taskMetaBean.params_info,
                            input_cols : element.taskMetaBean.input_cols
                        }
                        vm.taskOptions.push(taskOption);
                    });
                    if(result.length) vm.$emit("setPreviewAble");
                    this.getSelectOption();
                    this.pluginPreview();
                })
            },
            getSelectOption(){
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                services.getNormalizationPage(vm.sourceData.id, settings).then(res => { 
                    let tableOption = res.data.data.column;
                    tableOption.forEach(item => {
                        if (item.columnZhName == null) {
                            item.label = item.columnName;
                        } else item.label = item.columnZhName;
                        item.value = item.columnName;
                        return item;
                    });
                    vm.jsonSelectOptions = tableOption;
                })
            },
            checkType(data) {
                const vm = this;
                vm.numType = Array.from(new Set(vm.numType))
                let tmpData = [];
                data.forEach(item => {
                        if (vm.numType.includes(item.columnType.toLowerCase())) {
                            tmpData.push(item);
                        }
                    }
                );
                return tmpData;
            },
            pluginPreview() {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                services.rayRunnerPreview(vm.sourceData.id, settings).then(res => {
                    let result = res.data.data.pluginMeta;
                    this.previewInfo(result);
                })
            },
            previewInfo(result){
                const vm = this;
                this.isPreDeal = result.isPreDeal === 'TRUE'?'yes' : 'no';
                this.projectPath = result.pythonProjectPath;
                this.rayIpAndPort = result.rayIpAndPort;
                this.taskClass = result.taskClassName;
                let parameterMetaInfo  = JSON.parse(result.parameterMetaInfo);
                this.taskClassPacketName = result.taskClassPacketName;
                this.taskClassName = result.taskClassName;
                this.previewJsonInfo(parameterMetaInfo);
            },
            previewJsonInfo(parameterMetaInfo) {
                if (!parameterMetaInfo) return;
                const vm = this;
                for (let key in parameterMetaInfo.param ) {
                    vm.taskOptions.forEach(element => {
                        element.params_info.forEach(jsonElement => {
                            if (key === jsonElement.param_Name) {
                                let e = JSON.parse(JSON.stringify(jsonElement));
                                e.value = parameterMetaInfo.param[key];
                                e.chooseType = 'input';
                                this.data.push(e);
                            }
                        });
                    });
                }
                for (let key in parameterMetaInfo.inputCol ) {
                    vm.taskOptions.forEach(element => {
                        element.input_cols.forEach(jsonElement => {
                            if (key === jsonElement.col_Name) {
                                let e = JSON.parse(JSON.stringify(jsonElement));
                                e.value = parameterMetaInfo.inputCol[key];
                                e.chooseType = 'select';
                                this.data.push(e);
                            }
                        });
                    });
                }
            },
            setJsonVo() {
                let params_info_jsonVo = {}, input_cols_jsonVo = {};
                this.data.forEach(element => {
                    let params_Name = element.param_Name,
                        input_cols_Name = element.col_Name;
                    if (element.col_Name === undefined) params_info_jsonVo[params_Name] = element.value;
                        else input_cols_jsonVo[input_cols_Name] = element.value;
                });
                let jsonVo = {
                    inputCol : input_cols_jsonVo,
                    param : params_info_jsonVo
                }
                let metaInfo = {
                    pythonProjectPath : this.projectPath,
                    rayIpAndPort : this.rayIpAndPort,
                    taskClassPacketName : this.taskClassPacketName,
                    taskClassName : this.taskClassName,
                    isPreDeal : this.isPreDeal === 'yes'?'TRUE':'FALSE',
                    parameterMetaInfo: JSON.stringify(jsonVo)
                }
                return metaInfo;
            },
            previewFn() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
        },
        created() {
            this.init();
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: false,
                isSampleExpr: false,
                isThread: false,
                isException: false,
            }
        },
    }
</script>

<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped>
    .inputWidth {
        width:72%;
    }
    .form_class {
        border: #956cf5 5px;
    }
    .input_width {
        width: 42%;
    }
    .ce-normal_item {
        padding-left: 10px;
        text-align: left ;
    }
    .ce-normal_item.is-require::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }
    .select_class{
        padding-top: 10px;
        padding-left: 10px;
    }
    .ce-plug_attr1 {
        height: calc(100% - 204px) ;
        padding: 10px;
        box-sizing: border-box;
        overflow: auto;
    }
    .ce-add_b {
        line-height: 200px;
        border: 3px dashed #ddd;
        cursor: pointer;
        text-align: center;
        color: #999;
        font-size: 18px;
        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
    }
</style>
