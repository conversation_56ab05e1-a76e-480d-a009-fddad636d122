<template>
    <div class="newModelName">
        <el-form :model="nameForm" v-loading="settings.loading" :rules="rules" size="small" ref="form" label-width="100px">
            <el-form-item :label="label.chLabel" prop="chName" >
                <el-input v-model.trim="nameForm.chName" ref="name" v-input-limit:fieldName maxlength="25"
                          :placeholder="c_placeholder"></el-input>
            </el-form-item>
            <el-form-item :label="label.engLabel" prop="engName" v-if="isNew">
                <el-input v-model.trim="nameForm.engName" v-input-limit:fieldCode
                          maxlength="100" :placeholder="e_placeholder"></el-input>
            </el-form-item>
            <el-form-item label="位置:" prop="savePath" v-show="isNew">
                <div class="content__wrap">
                    <save-tree-view ref="treeView" :data="pathOpt" :expand-data="[nodeId]" @node-click="pathChange" />
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
    import {outputMixins} from "@/api/commonMethods/output";
    import saveTreeView from "@/components/common/saveTreeView/saveTreeView";

    export default {
        name: "NewModelName",
        mixins: [commonMixins, servicesMixins, outputMixins],
        props: {
            label: Object,
        },
        components : {saveTreeView},
        data() {
            return {
                nodeId: "",
                pathOpt: [],
                nameForm:
                    {
                        chName: '',
                        engName: '',
                        savePath: '',
                    }
                ,
                e_placeholder: "请输入英文名(限100个字符)",
                c_placeholder: "请输入中文名(限100个字符)",
                rules: {
                    chName: [
                        {required: true, message: '请输入中文名', trigger: ['blur', 'change']},
                    ],
                    engName: [
                        {required: true, message: '请输入英文名', trigger: ['blur', 'change']}
                    ],
                    savePath: [
                        {required: true, message: '请选择位置', trigger: ['blur', 'change']},
                    ],
                },
                defaultProps: {
                    children: "children",
                    label: "label",
                    value: "id"
                },
                shareId:'6a155c5cdf9d5ddcbd8459f05b37186a',
                isNew: true
            }
        },
        methods: {
            pathChange(data){
                this.nameForm.savePath = data.id;
            },
            validate(...arg) {
                this.$refs.form.validate(...arg);
            },
            showSelect(nodeId ,row) {
                this.nodeId = nodeId || "";
                this.isNew = !row.id;
                this.nameForm.savePath = nodeId || "";
                this.setName(row);
            },
            setName(row) {
                this.nameForm.chName = row.name||"";
                this.nameForm.engName = row.code||"";
            },
            queryDataSetTree() {
                const vm = this, {datasetServices, datasetMock, settings} = this;
                let services = vm.getServices(datasetServices, datasetMock);
                settings.loading = true;
                //数据集位置
                services.queryDataSetTree(false, settings).then(res => {
                    if (res.data.status === 0) {
                        vm.pathOpt =  res.data.data[0].children ? res.data.data[0].children.filter(n=>n.id!==vm.shareId) : [];
                        vm.$nextTick(()=>{
                            vm.$refs.treeView.setCurrentNode(vm.nodeId);
                        })
                    }
                });
            },
        },
        created() {
            this.queryDataSetTree();
        }
    }
</script>

<style scoped>
    .content__wrap {
        padding: 14px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        box-sizing: border-box;
        height: 250px;
    }
</style>
