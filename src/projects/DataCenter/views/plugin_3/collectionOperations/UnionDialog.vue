<template>
    <div>
        <el-container class="inner_container">
            <el-container>
<!--                <el-header class="inner_header" style="height: 30px">-->
<!--                    <div class="ce-header_cont">-->
<!--                        <el-radio-group v-model="tabInfo">-->
<!--&lt;!&ndash;                            <el-radio-button  label="预览数据"></el-radio-button>&ndash;&gt;-->
<!--&lt;!&ndash;                            <el-radio-button  label="字段列表"></el-radio-button>&ndash;&gt;-->
<!--                        </el-radio-group>-->
<!--&lt;!&ndash;                        <div class="rules__wrap-reflash"><i class="dg-iconp icon-refresh"></i></div>&ndash;&gt;-->
<!--                    </div>-->
<!--&lt;!&ndash;                    <div>&ndash;&gt;-->
<!--&lt;!&ndash;                        使用&ndash;&gt;-->
<!--&lt;!&ndash;                        <el-select style="width: 120px" v-model="values">&ndash;&gt;-->
<!--&lt;!&ndash;                            <el-option  v-for="item in options1" :label="item.label" :value="item.value">&ndash;&gt;-->
<!--&lt;!&ndash;                            </el-option>&ndash;&gt;-->
<!--&lt;!&ndash;                        </el-select>&ndash;&gt;-->
<!--&lt;!&ndash;                        数据进行分析&ndash;&gt;-->
<!--&lt;!&ndash;                        <el-input style="width: 80px"></el-input>&ndash;&gt;-->
<!--&lt;!&ndash;                        行&ndash;&gt;-->
<!--&lt;!&ndash;                    </div>&ndash;&gt;-->
<!--                </el-header>-->
                <el-main>
                    <el-table
                            v-if="tabInfo === '预览数据'"
                            style="width: 100%">
                    </el-table>
                    <div v-else>
                        <common-table max-height="393px"
                                      :columns="resultColumns"
                                      :pagination="false"
                                      :data="aggData"
                                      >
                            <template
                                    slot="opers"
                                    slot-scope="scope"
                            >
                                <el-popconfirm
                                        title="确定删除吗？"
                                        @confirm="handleClickDelete(scope.row, scope.$index)"
                                >
                                    <i
                                            style="cursor:pointer"
                                            slot="reference"
                                            class="icon icon__btn icon__btn_delete pl5 pr5 f18 colorB"
                                            title="删除"
                                    >&#xe847;</i>
                                </el-popconfirm>
                            </template>


<!--                            <i slot="opers"-->
<!--                               slot-scope="scope"-->
<!--                               class="icon icon__btn icon__btn_delete pl5 pr5 f18"-->
<!--                               title="删除"-->
<!--                               @click.stop="handleClickDelete(scope.row, scope.$index)"-->
<!--                            >&#xe847;</i>-->
                            <el-input slot="colCode"
                                      slot-scope="scope"
                                      v-model.trim="scope.row.colCode"
                                      @input="sout(scope.row.colCode, scope.$index)"
                            ></el-input>
                            <el-input slot="colName"
                                        slot-scope="{ row }"
                                        v-model.trim="row.colName"
                            >
                            </el-input>
                            <el-select slot="belongTableColCodeOne"
                                       slot-scope="scope"
                                       v-model="scope.row.belongTableColCodeOne"
                            >
                                <el-option v-for="item in leftTable.fields"
                                            :label="item.colName"
                                           :value="item.colCode"
                                >
                                </el-option>
                            </el-select>
                            <el-select slot="belongTableColCodeTwo"
                                       slot-scope="{ row }"
                                       v-model="row.belongTableColCodeTwo">
                                <el-option v-for="item in rightTable.fields"
                                           :label="item.colName"
                                           :value="item.colCode"
                                >
                                </el-option>
                            </el-select>
                        </common-table>

                        <el-button style="margin-top: 10px" @click="autoUnion">自动合并字段</el-button>
                        <el-button style="margin-left: 10px;margin-top: 10px"  @click="addUnion" type="primary">+添加合并字段</el-button>
                    </div>
                </el-main>
            </el-container>
        </el-container>

    </div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin/service-mixins/service-mixins";
    export default {
        name: "UnionDialog",
        props: {
            tableInfo: Object,
            leftTable: Object,
            rightTableC: Object,
            leftSelected: Array,
            rightSelected: Array,
            btnActive: Number,
            aggDataC: Array,
        },
        mixins : [pluginMixins , servicesMixins],
        data() {
            return {
                aggData: [],
                rightTable: {},
                leftTableC: {},
                tabInfo:"字段列表",
                defaultProps: {
                    label:"colCode",
                },
                options1: [{
                    value: '选项1',
                    label: '全部'
                }, {
                    value: '选项2',
                    label: '前1000行'
                }, {
                    value: '选项3',
                    label: '前1万行'
                },{
                    value: '选项4',
                    label: '前10万行'
                }
                ],

                // leftTable: {fields: [{colCode: "sas",id:"1"}, {colCode: "adad",id:"2"}], checked: []},
                leftSearchValue: "",
                isIndeterminateLeft: true,
                checkLeftAll: "", //left为左表

                // rightTable: {fields: [{colCode: "adadadada",id:"1"}, {colCode: "adacad",id:"2"}],checked: []},  //右边的数据
                rightSearchValue: "",
                isIndeterminateRight: "",
                checkRightAll:"",
                aa: "dad",
                resultColumns: [
                    {prop: 'colCode', label: '合并结果字段名'},
                    {prop: 'colName', label: '合并结果字段中文名'},
                    {prop: 'belongTableColCodeOne', label: '合并来源表:'+this.tableInfo.left.tableName},
                    {prop: 'belongTableColCodeTwo', label: '合并来源表:'+this.tableInfo.right.tableName},
                    {prop: 'opers', label: '操作',align: 'center', width: 70},
                ]
            }
        },
        methods: {
            sout(val, index) {
                let codeReg = new RegExp("^[a-zA-Z0-9_]+$");
                let len = val.length;
                let str = '';
                for(let i = 0; i < len; i++){
                    if(codeReg.test(val[i])){
                        str+=val[i];
                    }
                }
                this.aggData[index].colCode = str;
            },
            addUnion() {
                this.aggData.push({
                    colCode: "",
                    colName: "",
                    objId: this.tableInfo.left.tableId,
                    objCode: this.tableInfo.left.tableCode,
                    replaceFieldName: "",
                    newFieldName: "",
                    valType: "",
                    length: "",
                    precision: "",
                    columnZhName: "",
                    belongTableColCodeOne: "",
                    belongTableColCodeTwo:""
                })
            },
            autoUnion() {
               this.aggData = [];
              for(let i = 0; i < this.leftTableC.fields.length; i++) {
                  for(let j = 0; j < this.rightTable.fields.length; j++) {
                        if(this.leftTableC.fields[i].colName === this.rightTable.fields[j].colName ) {
                            this.aggData.push({
                                objId: this.tableInfo.left.tableId,
                                objId: this.tableInfo.left.tableId,
                                objCode: this.tableInfo.left.tableCode,
                                replaceFieldName: this.leftTableC.fields[i].colCode,
                                newFieldName: this.leftTableC.fields[i].colCode,
                                colCode: this.leftTableC.fields[i].colCode,
                                colName: this.leftTableC.fields[i].colName,
                                valType: this.leftTableC.fields[i].type,
                                length: this.leftTableC.fields[i].length,
                                precision: this.leftTableC.fields[i].precsn,
                                columnZhName: this.leftTableC.fields[i].colName,
                                belongTableColCodeOne: this.leftTableC.fields[i].colCode,
                                belongTableColCodeTwo: this.rightTable.fields[j].colCode
                            })
                            break;
                        }
                  }
              }
              if(this.aggData.length === 0) {
                  this.$message.warning("目前暂无可合并字段");
              }else {
                  // this.$message.success("合并成功");
              }
            },
            cancel() {
                this.$emit("cancel")
            },
            submit() {
                for(let i = 0; i < this.aggData.length; i++) {
                    if(this.aggData[i].colCode === ""
                        || this.aggData[i].colName === ""
                        || this.aggData[i].belongTableColCodeOne === ""
                        || this.aggData[i].belongTableColCodeTwo === ""
                    ) {
                        this.$message.warning("请不要留有空字段");
                        return;
                    }
                    else {
                        for(let j = i + 1; j < this.aggData.length; j++) {
                            if(this.aggData[i].colCode === this.aggData[j].colCode) {
                                this.$message.warning("存在同名字段" + this.aggData[j].colCode+",请修改配置");
                                return;
                            }
                            if(this.aggData[i].colName === this.aggData[j].colName) {
                                this.$message.warning("存在同名字段" + this.aggData[j].colName+",请修改配置");
                                return;
                            }
                        }
                        this.aggData[i].replaceFieldName = this.aggData[i].colCode;
                        this.aggData[i].newFieldName = this.aggData[i].colCode;
                        this.aggData[i].replaceFieldName = this.aggData[i].colCode;
                        this.aggData[i].columnZhName = this.aggData[i].colName;
                    }
                }
                this.$emit("updateOutput",this.aggData);
            },

            handleLeftCheckAllChange(val) {
                this.$refs.leftTree.setCheckedNodes(val ? this.leftTableC.fields: []);
            },

            checkLeftChange(data, data1) {
                let checkedCount = this.$refs.leftTree.getCheckedNodes().length;
                this.checkLeftAll = checkedCount === this.leftTableC.fields.length;
                this.isIndeterminateLeft = checkedCount > 0 && checkedCount < this.leftTableC.fields.length;
                if(data1 === true) {
                    this.leftTableC.checked.push(data);
                }
                else {
                    for(let i = 0; i < this.leftTableC.checked.length; i++) {
                        if(this.leftTableC.checked[i].colCode === data.colCode && this.leftTableC.checked[i].colName === data.colName) {
                            this.leftTableC.checked.splice(i,1);
                            break;
                        }
                    }
                }

            },
            filterNode(value, data) {
                if (!value) return true;
                return data.colCode.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
            leftFilter() {
                this.$refs.leftTree.filter(this.leftSearchValue);
            },

            handleRightCheckAllChange(val) {
                this.$refs.rightTree.setCheckedNodes(val ? this.rightTable.fields: []);
            },
            checkRightChange(data, data1) {
                let checkedCount = this.$refs.rightTree.getCheckedNodes().length;
                this.checkRightAll = checkedCount === this.rightTable.fields.length;
                this.isIndeterminateRight = checkedCount > 0 && checkedCount < this.rightTable.fields.length;
                if(data1 === true) {
                    this.rightTable.checked.push(data);
                }
                else {
                    for(let i = 0; i < this.rightTable.checked.length; i++) {
                        if(this.rightTable.checked[i].colCode === data.colCode && this.rightTable.checked[i].colName === data.colName) {
                            this.rightTable.checked.splice(i,1);
                            break;
                        }
                    }
                }
            },
            rightFilter() {
                this.$refs.rightTree.filter(this.rightSearchValue);
            },
            handleClickDelete(row, index) {

                this.aggData.splice(index, 1);
            },

        },
        computed: {
        },
        created() {
            this.leftTableC = JSON.parse(JSON.stringify(this.leftTable));
            this.rightTable = JSON.parse(JSON.stringify(this.rightTableC));
            this.aggData = JSON.parse(JSON.stringify(this.aggDataC));
            if(this.aggData.length === 0) {
                this.autoUnion();
            }
        },

    }
</script>

<style scoped>
    .ce-header_cont {
        float: left;
        display: flex;
    }  .rules__wrap-reflash {
           text-align: center;
           line-height: 1.875rem;
           width: 1.875rem;
           height: 1.875rem;
           border: 1px solid rgba(0, 0, 0, 0.15);
           border-radius: 0.125rem;
           margin:0 0.25rem;
           cursor: pointer;
       }
    .inner_container {
        border: 1px solid #eee
    }
    .inner_container_main {
        height: 250px;
        border: 1px solid #eee;
        margin-top: 10px;
        margin-left: 10px
    }
    .inner_container_main_title {
        border-radius: 5px;
        width: 100%;
        font-size: 15px;
        height: 30px;
    }
    .centerTi {
        text-align: center
    }

    .blueColor {
        background-color: rgba(77, 167, 253, 0.498039215686275);
    }
    .orageColor {
        background-color: rgba(255, 153, 0, 0.498039215686275);
    }
    .inner_container_main_input {
        width: 80%;
        margin-top: 10px;
    }
    .inner_header {
        height: 30px;
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
    }
    .colorC {
        color: dodgerblue;
    }
    .colorB {
        color: #1890ff
    }
</style>