<template>
    <div class="ce-plug_cont check" v-loading="settings.loading">
        <el-tabs v-model="activeName" type="" class="">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div v-show="activeName === 'checkSetting'" class="check-content">
            <el-form :model="{ list: tableData }" ref="tableForm" class="check-setting_form">
                <common-table
                        :columns="tHeadData"
                        :data="tableData"
                        :pagination="false"
                        height="calc(100% - 15px)"
                >
                    <template slot="checkConditionColumn" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.checkConditionColumn`" :rules="[...rules.checkConditionColumn ,...checkSameName($index , '请勿选择相同字段' , tableData , 'checkConditionColumn')]">
                            <dg-select filterable :data="serviceInputInfo.stepColumns || []" v-model="row.checkConditionColumn" value-name="columnName" label-name="columnZhName" @change="selectChange(row)"></dg-select>
                        </el-form-item>
                    </template>
                    <template slot="bindColumn" slot-scope="{row , $index}">
                        <el-form-item>
                            <span class="link_txt" @click="addBind(row)">{{isBind(row) ? '已绑定' : '添加绑定'}}</span>
                        </el-form-item>
                    </template>

                    <template slot="header-operate" slot-scope="scope">
                        <i class="link_txt icon" @click="addLine" title="添加">&#xe6e1;</i>
                    </template>
                    <template slot="header-checkConditionColumn" slot-scope="{row , $index , column}">
                        <span class="is-require">{{column.label}}</span>
                    </template>
                    <template slot="header-bindColumn" slot-scope="{row , $index , column}">
                        <span class="is-require">{{column.label}}</span>
                    </template>
                    <template slot="operate" slot-scope="scope">
                        <el-form-item>
                            <span class="link_txt" @click="deleteLine(scope.$index)">删除</span>
                        </el-form-item>
                    </template>
                </common-table>
            </el-form>
        </div>
        <div v-show="activeName === 'outputField'" class="check-content">
            <el-form :model="{ list: resultTableData }" ref="resultTableForm">
                <common-table
                        :columns="resultHeadData"
                        :data="resultTableData"
                        :pagination="false"
                >
                    <template slot="checkResultName" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.checkResultName`" :rules="rules.checkResultName">
                            <el-input v-model.trim="row.checkResultName" v-input-limit:fieldCode></el-input>
                        </el-form-item>
                    </template>
                    <template slot="checkResultDescribe" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.checkResultDescribe`">
                            <el-input v-model.trim="row.checkResultDescribe"></el-input>
                        </el-form-item>
                    </template>
                    <template slot="checkForwardValue" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.checkForwardValue`" :rules="rules.checkForwardValue">
                            <el-input v-model.trim="row.checkForwardValue"></el-input>
                        </el-form-item>
                    </template>
                    <template slot="checkReverseValue" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.checkReverseValue`" :rules="rules.checkReverseValue">
                            <el-input v-model.trim="row.checkReverseValue"></el-input>
                        </el-form-item>
                    </template>
                    <template slot="checkDataSourceStepName" slot-scope="{row , $index}">
                        <el-form-item>
                            {{row.checkDataSourceStepName}}
                        </el-form-item>
                    </template>
                    <template slot="columnType" slot-scope="{row , $index}">
                        <el-form-item>
                            {{row.columnType}}
                        </el-form-item>
                    </template>
                    <template slot="header-checkResultName" slot-scope="{row , $index , column}">
                        <span class="is-require">{{column.label}}</span>
                    </template>
                    <template slot="header-checkForwardValue" slot-scope="{row , $index , column}">
                        <span class="is-require">{{column.label}}</span>
                    </template>
                    <template slot="header-checkReverseValue" slot-scope="{row , $index , column}">
                        <span class="is-require">{{column.label}}</span>
                    </template>
                </common-table>
            </el-form>

        </div>
        <div v-show="activeName === 'seniorSetting'" class="check-content">
            <el-form label-width="150px">
                <el-form-item label="服务调用方式：" required>
                    <el-radio-group v-model="callAsync">
                        <el-radio :label="true">异步调用</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="返回结果是否分页：" required>
                    <el-radio-group v-model="pageFlag">
                        <el-radio :label="false">不分页</el-radio>
                        <el-radio :label="true">分页</el-radio>
                    </el-radio-group>
                    <div class="el-upload__tip">
                        当返回结果记录数大于100时请选择分页，不分页则最多返回100条记录。
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <preView ref="preview"></preView>
    </div>
</template>

<script>
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    export default {
        name: "metaServiceCheckOutPut",
        mixins: [commonMixins,],
        components: {
            PreView
        },
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return {
                activeName:'checkSetting',
                tab_panel: [
                    {
                        label: '核查配置',
                        name: 'checkSetting',
                    },
                    {
                        label: "输出配置",
                        name: "outputField",
                    },
                    // {
                    //     label: "高级设置",
                    //     name: "seniorSetting",
                    // }
                ],
                tableData:[],
                tHeadData:[
                    {
                        prop: "checkConditionColumn",
                        label: "核查条件字段",
                    },
                    {
                        prop: "bindColumn",
                        label: "绑定字段",
                    },
                    {
                        prop: "operate",
                        label: "操作",
                        width: "80px",
                        align: "center"
                    },
                ],
                resultTableData:[],
                resultHeadData:[
                    {
                        prop: "checkDataSourceStepName",
                        label: "核查数据集",
                    },
                    {
                        prop: "checkResultName",
                        label: "参数名",
                    },
                    {
                        prop: "checkResultDescribe",
                        label: "参数描述",
                    },
                    {
                        prop: "columnType",
                        label: "参数类型",
                    },
                    {
                        prop: "checkForwardValue",
                        label: "参数值(存在记录)",
                    },
                    {
                        prop: "checkReverseValue",
                        label: "参数值(不存在记录)",
                    }
                ],
                rules:{
                    checkConditionColumn:[{ required: true, message: '请选择核查条件字段', trigger: ['blur','change'] }],
                    checkResultName:[{ required: true, message: '请输入参数名', trigger: ['blur','change'] }],
                    checkForwardValue:[{ required: true, message: '请输入', trigger: ['blur','change'] }],
                    checkReverseValue:[{ required: true, message: '请输入', trigger: ['blur','change'] }],
                },
                inputList:[],
                serviceInputStepId:'',
                dataSetList: [],
                serviceInputInfo: {},//上游传下来的左侧的相关信息
                callAsync: true,
                pageFlag: false,
            }
        },
        mounted(){
            this.init();
        },
        methods:{
            selectChange(row){
                let par = this.serviceInputInfo.stepColumns.find(n=>n.columnName == row.checkConditionColumn) || {};
                row.columnType = par.columnType;
                row.columnZhName = par.columnZhName;
            },
            isBind(row){
                return row.tableData.length && !row.tableData.find(n=>n.bindColumn === '')
            },
            //添加行
            addLine(){
                this.tableData.push({columnZhName:'',columnType:'',tableData:[]});
            },
            //删除行
            deleteLine(index){
                this.tableData.splice(index, 1);
            },
            //添加绑定
            addBind(row){
                const vm = this;
                if(!row.checkConditionColumn) return vm.$message.warning("请先设置核查条件字段");
                let layer = vm.$dgLayer({
                    title: '添加绑定',
                    content: require("./components/addBind"),
                    maxmin: false,
                    props: {
                        dataSetList: vm.dataSetList,
                        columnList: vm.tableData.map(n=>{n.code = n.checkConditionColumn; n.name = n.columnZhName; return n}),
                        currentColumn : row.checkConditionColumn
                    },
                    area: ['1000px', "60%"],
                    on: {
                        close() {
                            layer.close(layer.dialogIndex);
                        },
                        save(list){
                            vm.tableData = list;
                            layer.close(layer.dialogIndex);
                        }
                    },
                });
            },

            resetInfo(){
                const vm = this;
                vm.resultTableData = [];
                vm.pageFlag = false;
                vm.callAsync = true;

                //输出配置
                vm.dataSetList.forEach((n, i)=>{
                    let obj = {
                        checkDataSourceStepName: n.dataSet,//核查数据集
                        checkDataSourceStepId: n.id,//核查数据集id
                        checkResultName: '',//参数名
                        checkResultDescribe:'',//参数描述
                        checkForwardValue:'存在',//参数值（存在记录）
                        checkReverseValue:'不存在',//参数值（不存在记录）
                        columnType:'String',
                        columnLength:'',
                    }
                    vm.resultTableData.push(obj);
                })
            },
            /**
             * 初始化-查询信息
             * */
            init(){
                const vm = this, {loadParams} = this;
                let services = vm.$services("servicesServices");
                loadParams.loading = true;
                vm.tableData = [];
                //获取核查输出插件信息（回显）
                services.queryDataCheckOutput(vm.sourceData.id, loadParams).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.serviceInputInfo = result.inputPluginList.find(n=>n.stepId == result.leftInputId) || {};
                        vm.pageFlag = result.cicadaMetaServiceCheckOutPut.pageFlag;
                        vm.callAsync = result.cicadaMetaServiceCheckOutPut.async || true;

                        let dataSetList = result.inputPluginList.filter(n=>n.stepId !== result.leftInputId);
                        let resultTableData = result.cicadaMetaServiceCheckOutPut.checkOutputColumnMetas || []
                        vm.dataSetList = []; //数据集列表
                        vm.resultTableData = []
                        dataSetList.forEach(n=>{
                            let obj = { id: n.stepId, dataSet: n.stepName, code: n.stepCode, column: []};
                            obj.column = n.stepColumns.map(p=>{return {label:p.columnZhName || p.columnName, value: p.columnName, dataType: p.columnType}});
                            vm.dataSetList.push(obj);

                            let outPut = resultTableData.find(p=>p.checkDataSourceStepId == obj.id);
                            if(outPut){
                                vm.resultTableData.push(outPut);
                            }
                            else{
                                vm.resultTableData.push({
                                    checkDataSourceStepName: obj.dataSet,//核查数据集
                                    checkDataSourceStepId: obj.id,//核查数据集id
                                    checkResultName: '',//参数名
                                    checkResultDescribe:'',//参数描述
                                    checkForwardValue:'存在',//参数值（存在记录）
                                    checkReverseValue:'不存在',//参数值（不存在记录）
                                    columnType:'String',
                                    columnLength:'',
                                });
                            }
                        })

                        let arrIds = vm.dataSetList.map(n=>n.id)
                        vm.resultTableData = vm.resultTableData.filter(n=> arrIds.includes(n.checkDataSourceStepId)) || []

                        if(!result.leftInputId){
                            vm.resetInfo();
                            return vm.$message.warning('请先连接上游插件');
                        }
                        if(result.cicadaMetaServiceCheckOutPut.serviceInputStepId && result.cicadaMetaServiceCheckOutPut.serviceInputStepId != result.leftInputId){
                            vm.resetInfo();
                            return vm.$message.warning('上游插件已变化，请重新配置');
                        }
                        if(vm.resultTableData && !vm.resultTableData.length){
                            vm.resetInfo();
                        }
                        let checkContentConfigMetas = result.cicadaMetaServiceCheckOutPut.checkContentConfigMetas || []

                        checkContentConfigMetas.forEach(n=>{
                            if(vm.serviceInputInfo.stepColumns.find(d=>d.columnName == n.checkConditionColumn)){
                                let info = {
                                    columnZhName: n.checkConditionColumnZh,
                                    columnType: n.checkConditionDataType,
                                    checkConditionColumn: n.checkConditionColumn,
                                    tableData: [],
                                }
                                let p = JSON.parse(n.bindingConfiguration);
                                vm.dataSetList.forEach(g=>{
                                    let hasSet = p.find(t=>g.id == t.dataSourcesStepId);
                                    let bindColumn = hasSet && g.column.find(f=>f.value == hasSet.dataSourceStepColumn) ? hasSet.dataSourceStepColumn: '';
                                    info.tableData.push({bindColumn: bindColumn, id: hasSet ? hasSet.dataSourcesStepId : g.id, dataSet: hasSet ? hasSet.dataSourceStepName : g.dataSet, column: g.column || []});
                                })
                                vm.tableData.push(info);
                            }
                        })
                    }
                })
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            /**
             * 插件保存
             * */
            async save(fn,showTip) {
                const vm = this, {loadParams} = this;
                if(!vm.tableData.length){
                    return vm.$message.warning("请先进行核查配置");
                }
                let flag = true;
                this.$refs['tableForm'].validate((valid) => {
                    if (valid) {
                    } else {
                        flag = false;
                        return false;
                    }
                })
                if(!flag) return vm.$message.warning("请先进行核查条件配置");

                if(vm.tableData.find(n=>!n.tableData.length)){
                    return vm.$message.warning("请先绑定字段");
                }
                let info = [];
                info = vm.tableData.reduce( (sum,current,index)=>{
                    let p= sum.concat(current.tableData)
                    return p
                },[])
                if(info.find(n=>n.bindColumn == '')){
                    return vm.$message.warning("请先绑定字段");
                }

                let resultFlag = true;
                this.$refs['resultTableForm'].validate((valid) => {
                    if (valid) {

                    } else {
                        resultFlag = false;
                        return false;
                    }
                })
                if(!resultFlag) return vm.$message.warning("请先进行输出配置");
                let outPutArr = vm.resultTableData.map(n=>n.checkResultName);
                if(new Set(outPutArr).size !== outPutArr.length){
                    vm.$message.warning("输出配置参数名不能重复");
                    return
                }
                for(let i = 0; i < vm.resultTableData.length; i++) {
                    for (let j = i + 1; j < vm.resultTableData.length; j++) {
                        if (vm.resultTableData[i].checkResultDescribe && vm.resultTableData[j].checkResultDescribe && vm.resultTableData[i].checkResultDescribe === vm.resultTableData[j].checkResultDescribe) {
                            this.$message.warning("存在重名的描述" + vm.resultTableData[i].checkResultDescribe + ",请重新配置");
                            return;
                        }
                    }
                }

                let services = vm.$services("servicesServices");

                let data = {
                    serviceInputStepId: vm.serviceInputInfo.stepId,//核查输入步骤id
                    serviceInputStepName: vm.serviceInputInfo.stepName,//核查输入步骤名称
                    checkContentConfigMetas: '', //核查条件内容映射配置
                    checkOutputColumnMetas: vm.resultTableData, //核查输出映射配置
                    checkDataSourceMetas: '',//核查输入数据源
                    pageFlag: vm.pageFlag,
                    async: vm.callAsync
                }

                let checkContentConfigMetas = [];
                vm.tableData.forEach(n=>{
                    let obj = {
                        checkConditionColumn: n.checkConditionColumn,//核查条件字段
                        checkConditionColumnZh: n.columnZhName,//核查条件字段中文名
                        bindingColumn:'',//绑定字段
                        checkConditionDataType: n.columnType,//核查条件字段类型
                        bindingConfiguration:'',//核查条件绑定配置
                    }
                    let bindingConfiguration = [];
                    n.tableData.forEach(p=>{
                        let par = {
                            dataSourceStepName: p.dataSet,//数据资源步骤名称
                            dataSourcesStepId: p.id,//数据资源步骤id
                            checkConditionColumn: n.checkConditionColumn ,//核查条件字段
                            dataSourceStepColumn: p.bindColumn,//右侧上游输入的字段名
                            dataSourceStepColumnZh: (p.column.find(k=>k.value == p.bindColumn) || {}).label,//绑定字段中文
                            pageFlag: vm.pageFlag,
                            async: vm.callAsync
                        }
                        bindingConfiguration.push(par);
                    })
                    obj.bindingConfiguration = JSON.stringify(bindingConfiguration)
                    checkContentConfigMetas.push(obj);
                })
                data.checkContentConfigMetas = checkContentConfigMetas;

                data.checkDataSourceMetas = vm.dataSetList.map(k=>{
                    return { dataSourceStepId: k.id, dataSourceStepName: k.dataSet}
                })
                loadParams.loading = true;
                data.pluginCode = vm.sourceData.keyWord;
                services.savePluginCheckOutput(vm.sourceData.id, data, loadParams).then(res => {
                    if(res.data.status === 0) {
                        showTip && vm.$message.success('保存成功');
                        vm.$emit("saveSuccess");
                        vm.$emit("setPreviewAble");
                        fn && fn();
                    }
                })
            },
        }
    }
</script>
<style src="../css/plugin.less" scoped lang="less"></style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style scoped lang="less">
    .check{
        &-setting{
            &_title{
                display: flex;
                justify-content: space-between;
                &_span{
                    font-weight: bold;
                }
            }
            &_form{
                height: 100%;
            }
        }
        &-content{
            height:calc(100% - 54px);
            overflow: auto;
            p{
                font-weight: bold;
            }
        }

        .is-require {
            position: relative;

            &::before {
                content: "*";
                position: absolute;
                top: 0;
                left: calc(100% + 2px);
                color: #F5222D;
            }
        }

        .link_txt{
            color: #1890ff;
            cursor: pointer;
        }
    }
</style>
