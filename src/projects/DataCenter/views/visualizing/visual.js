import {servicesMixins} from "./service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi_3/index.vue"
import Tree from "./tree/index.vue"
import List from "./list/index.vue"
import VisualizationEdit from './edit-panel/VisualizationEdit'
import treeCard from "@/components/layout/tree-card"
export default {
    name: "visual",
    mixins: [servicesMixins, commonMixins] ,
    components : {
        TreeAndList,
        Tree,
        List ,
        VisualizationEdit,
        treeCard
    },
    data(){
        return {
            showEditPanel: false ,
            datasetId : "",
            cardName : "仪表盘列表" ,
            isCase : false,//是否是场景案例跳转
            listLoad: false,
        }
    },
    methods : {
        /**
         * 更新列表加载状态
         * @param val
         */
        renewListLoad(val){
            this.listLoad = val;
        },
        //新建目录树
        showAddDialog(data){
            data = data ? data : this.$refs.tree.curData
            this.$refs.treeDir.show(data);
        },
        addTreeDir(name, node){
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            services.addOrUpdate(name , "" , node.id ).then(res => {
                if (res.data.status === 0) {
                    let data = res.data.data;
                    vm.successAddTip(name);
                    let newTreeNode = {
                        children: [],
                        id: data.id,
                        label: data.name,
                        isParent: false,
                        pId: node.id,
                    };
                    node.children.unshift(newTreeNode);
                    vm.$refs.treeDir.stop();
                    vm.$refs.tree.initTree();
                }
            })
        },
        /**
         * 树点击事件
         * @param name
         * @param id
         * @param node
         */
        nodeClick(name , id , node) {
            this.$refs.treeDir.nodeClick(name , id , node);
        },
        filterTree(val){
            this.$refs.tree.setFilterText(val);
        },
        closeEditPanel(groupId , filterTxt) {
            this.showEditPanel = false;
            this.$refs.visual_table.getData(groupId,filterTxt);
        },
        getTableData(treeNode){
            this.$refs.visual_table.initTableData(treeNode);
        },
        openEditPanel(state , rowId , filterText , groupId ,dataSetId) {
            this.showEditPanel = true;
            this.$nextTick(()=>{
                //this.$refs.visual_panel.initState(state );
                this.$refs.visual_panel.getRouterData( rowId ,filterText , groupId , dataSetId);
            })
        },
        issue(row){
            this.$refs.visual_table.release(row );
        },
        changeNodeKey(id){
            this.$refs.tree.setCheckedNode(id);
        },
        init(){
            const vm = this;
            let {datasetId , rowList} = this.$route.params;
            if(datasetId){
                this.datasetId = datasetId;
                let name = rowList.name || rowList.code;
                vm.$nextTick(()=>{
                    vm.$refs.visual_table.addVisualPanel(name , datasetId);
                })

            }
        }
    },
    created(){
        this.init();
        const vm = this;
        let {rapidShowName : tab} = this.$route.params;
        if (tab) {
            vm.$nextTick(()=>{
                this.$refs.visual_table.addVisualPanel();
            })
        }
        // let {node} = this.$route.params;
        // if (node) {
        //     node.id = node.caseId;
        //     vm.isCase = true;
        //     vm.$nextTick(()=>{
        //         this.$refs.visual_table.editPanel(node);
        //     })
        // }
    }
}
