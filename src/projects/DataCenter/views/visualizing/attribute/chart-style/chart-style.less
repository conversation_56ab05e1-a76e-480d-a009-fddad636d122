.ce-sty_item {
  display: block;
  margin: 6px 0;
}

.ce-select_item {
  padding-right: 8px;
  margin: 6px 0;
}

.ce-btn_group {
  display: inline;
  vertical-align: middle;
}

.state_box {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  margin-right: 5px;

  >.tip_color {
    position: absolute;
    width: 18px;
    height: 4px;
    left: 50%;
    margin-left: -9px;
    bottom:15%;
    border-radius: 2px;
    background: #000;
  }
  .el-button {
    min-width: auto;
  }
}

.ce-color {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  height: auto;
  opacity: 0;

  /deep/ .el-color-picker__trigger {
    height: 30px;
    width: 30px;
    padding: 0;
  }
}
.ce-mini_btn {
  .align-font {
    font-size: 12px;
  }
}

.ce-mini_btn.el-radio-button--mini {
  /deep/.el-radio-button__inner  {
    padding: .2rem 8px;
  }
  /deep/.el-checkbox-button__inner  {
     padding: .2rem 10px;
    border-radius: 2px;
   }
}

.ce-node {
  border: 1px solid #ddd;
  min-height: 28px;
  border-radius: 4px;
  position: relative;
  padding-left: 50px;
  overflow: hidden;
}

.ce-item_name {
  position: absolute;
  padding: 0 10px;
  background: #e7eaf0;
  float: left;
  color: #555;
  top: 0;
  left: 0;
  bottom: 0;
  line-height: 28px;
}
.ce-color_legend {
  left: 10px;
  transform: translate( 0 , -50%);
}

.ce-node_item {
  float: left;
  font-size: 12px;
  color: #909399;
  background: #f4f4f5;
  height: 24px;
  padding: 0 5px;
  line-height: 19px;
  border: 1px solid #e9e9eb;
  box-sizing: border-box;
  margin: 2px 0 2px 6px;
  border-radius: 4px;
}
.ce-color_box {
  display: inline-block;
  padding:6px 10px;
  border-radius:4px;
  vertical-align: middle;
}
.grid_box {
  overflow: hidden;
  line-height: 38px;
}
.grid_label,.grid_btn {
  float: left;
  line-height: 38px;
  margin-right: 10px;
}
.grid_num {
  margin-right: 10px;
  margin-top: 5px;
 }
.grid_slider {
  float: left;
  width: calc(100% - 260px);
  line-height: 38px;
  margin-left: 10px;
}
.grid_slider_f{
  width: calc(100% - 165px);
  float: left;
  line-height: 38px;
  margin-left: 10px;
}

.ce-form_item {
  padding: 0 14px 6px 14px;
  display: flex;
}
.ce-custom_input {
  width : 60px;
  margin:0 10px;
}
.ce-tab_cont {
  margin-top: 10px;
  border-bottom: 1px solid #ddd;
  background: #fff;
  transition: all 500ms;
}
.ce-tab_title {
  border : 1px solid #dcdcdc;
  background: #dcdedf;
  height: 30px;
  line-height: 30px;
  padding:0 72px 0 10px;
  position: relative;
  border-radius:3px;
  color: #333;
}

.ce-tab_btns {
  position: absolute;
  right: 0;
  width: 58px;
  top: 0;
  text-align: right;
  padding-right:10px ;
}
.ce-tab_tip {
  line-height: 28px;
  padding:5px 16px;
  color: #333;
}
.ce-tab_name {
  margin-bottom: 10px;
}


.slide-fade-item {
  transition: all 1s;
}
.slide-fade-enter, .slide-fade-leave-to ,.slide-fade-leave-active {
  opacity: 0;
  transform: translateX(30px);
}
.slide-fade-leave-active {
  position: absolute;
  right: 100px;
}

.style_icon {
  width: 60px;
  height: 40px;
  display: block;
}
.right_icon{
  float: right;
}
.all_icon{
  position: relative;
}
.style_radio{
  margin-top: 20px;
}

.list-complete-enter, .list-complete-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
.list-complete-leave-active {
  position: absolute;
}
.ce-radio_with-ico {
  display: flex;
  align-items:flex-start;
  width: 180px;
}
.ce-radio_item {
  flex: 1;
}

.ce-condition_box {
  padding-left: 1px;
  overflow: hidden;
  .ce-condition_item {
    height: 40px;
    line-height: 38px;
    float: left;
    border: 1px solid #ddd;
    margin-left:-1px;
    box-sizing: border-box;
    padding:0 10px;
  }
}
.ce-logic_f {
  width:100px;
  line-height: 40px;
}
.ce-logic_w.el-input {
  width: 100px;
}

.style-cascader {
  line-height: 2rem;
  width: 100%;
}
.check_group {

  &-item {
    height: 2rem;
    display: flex;
    align-items: center;
    padding: 10px 0 10px 1.5rem;
  }

  &-sel {
    flex: 1;
  }
}

.width-setting-col {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: right;
}
