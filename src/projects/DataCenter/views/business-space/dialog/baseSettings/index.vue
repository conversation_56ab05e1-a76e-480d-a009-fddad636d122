<template>
    <div class="manage">
        <ce-tab-panel :data="tabs" :value.sync="actTab"></ce-tab-panel>
        <div class="manage-cont">
            <keep-alive>
                <component class="manage-page" ref="cont" :is="actTab" :data="row" ></component>
            </keep-alive>
        </div>
    </div>
</template>

<script>

import CeTabPanel from "@/components/signed-components/ceTabPanel/CeTabPanel";
import BusinessManage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/BusinessManage/index";
import TableManage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/TableManage/index"
import RoleManage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/RoleManage/index"
import BusinessFeature from "@/projects/DataCenter/views/business-space/dialog/baseSettings/BusinessFeature/index"
import BusinessScopeManage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/BusinessScopeManage/index"
import BusinessDomainManage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/BusinessDomainManage/index"

export default {
    name: "index",
    components: {
        CeTabPanel,
        BusinessManage,
        TableManage,
        RoleManage,
        BusinessFeature,
        BusinessScopeManage,
        BusinessDomainManage
    },
    props: {
        row:Object
    },
    data(){
        return {
            actTab : "BusinessManage",
            tabs : [
                {
                    label : "业务元管理",
                    value : "BusinessManage"
                },
                {
                    label : "码表管理",
                    value : "TableManage"
                },
                {
                    label : "论元角色管理",
                    value : "RoleManage"
                },
                {
                    label : "业务特征概念",
                    value : "BusinessFeature"
                },
                {
                    label : "标签作用域管理",
                    value : "BusinessScopeManage"
                },
                {
                    label : "标签业务域管理",
                    value : "BusinessDomainManage"
                }
            ]
        }
    }
}
</script>

<style scoped lang="less">
.manage {
    height: 100%;
    &-cont {
        height: calc(100% - 33px);
    }
    &-page {
        height: calc(100% - 14px);
        padding-top: 14px;
    }
}


</style>
