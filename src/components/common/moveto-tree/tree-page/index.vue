<template>
    <div class="dataTree">
        <el-input placeholder="请输入名称" v-model="filterText" size="medium" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn" v-loading="settings.loading">
            <dg-tree
                    class="filter-tree"
                    :data="data"
                    :props="defaultProps"
                    node-key="id"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    :highlight-current="true"
                    @node-click="nodeClick"
                    :default-expand-all="true"
                    ref="tree">

                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-label el-tree-node__label"
                          :title="data.label"
                          :class="{
                            'el-icon-folder' : !node.expanded && node.level <= 3 ,
                            'el-icon-folder-opened' : node.expanded && node.level <= 3,
                            'el-icon-document' : data.isParent
                          }"
                    >{{ data.label }}</span>
                </span>

            </dg-tree>
        </div>
    </div>
</template>

<script src="./tree-page.js"></script>
<style scoped lang="less" src="./tree-page.less"></style>