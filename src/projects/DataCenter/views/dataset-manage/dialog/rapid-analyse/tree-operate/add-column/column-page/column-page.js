import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../../../../service-mixins/service-mixins";

export default {
    name: "columnPage",
    mixins: [commonMixins, servicesMixins, common],
    props:{
        datasetId : String ,
        indexType : String
    },
    data(){
        return {
            ruleForm: {
                name: "",
                columnCode:"",
                expression: "",
                dataType: "TEXT",
                markInfo: ""
            },
            name_placeholder : "请输入名称(限100个字符)",
            exp_placeholder : "请输入表达式,可通过 [ 唤出列表插入字段名" ,
            memo_placeholder : "请输入备注信息",
            code_placeholder : "请输入code(限100个字符)",
            //配置form表单规则
            rules: {
                name: [
                    { required: true, message: "请输入名称", trigger: ["blur","change"] },
                    ],
                expression: [{ required: true, message: "请输入表达式", trigger: ["blur","change"] }],
                columnCode : [
                    { required: true, message: "请输入code", trigger: ["blur","change"] },
                    {validator : this.checkedCommon , reg : /^[a-zA-Z][A-Za-z_0-9]*$/ ,message : "别名由字母、数字、下划线组成且不以数字开头" , trigger : ["blur" , "change"]}
                ]
            },
            //新增字段搜索条件
            searchVal: "",
            typeOpts : [
                {label : "文本", value :"TEXT" , show :()=> true},
                {label : "数字", value :"NUMBER" , show : ()=> true},
                // {label : "日期时间(源格式：yyyy-MM-dd)", value :"DATE" , show :(indexType)=> indexType === 'DIMENSION'},
            ],
            columnTips : {
                use : "用法" ,
                explain : "说明" ,
                example :"实例"
            },
            //新增字段内容
            columnTable: [],
            allFunction : [],
            expStyle : {
                "height":"150px" ,
                "position":"relative" ,
                "z-index": 100
            },
            expression : "" ,
            req_exp : "" ,
            store_all_node: [],
            disabled : false ,
            fields : [],
            showCode : true
        }
    },
    methods : {
        init(data , disabled){
            const vm = this;
            let {ruleForm} = this;
            ruleForm.name = data.columnName;
            ruleForm.expression = data.exp;
            ruleForm.dataType = data.dataType;
            ruleForm.markInfo = data.markInfo;
            vm.disabled = disabled;
            vm.showCode = false;
         },
        expChange(val){
            this.setExp(val);
        },
        setExp(){
            const vm = this;
            let req_exp , exp =  vm.ruleForm.expression , expColumns = [];
            vm.store_all_node.forEach(item => {
                let nodeN = item.name || item.code , code = item.code;
                if(exp.indexOf(nodeN) > -1){
                    req_exp = exp.replace(nodeN , code);
                    expColumns.push(item.id);
                }
            });
            vm.req_exp = req_exp;
            return {req_exp,expColumns};
        },
        selectNode(item){
            const vm = this , {expression} = this;
            vm.ruleForm.expression = expression.split('[')[0] + item.value;
        },
        async getFields(){
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            await services.getLogicColumn("" , vm.datasetId).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data , datas = [];
                    vm.fields = [];
                    if(result.length === 0){
                        vm.$message.info("暂无字段");
                    }
                    result.forEach(item => {
                        vm.fields.push({
                            value : item.name || item.code ,
                            code : item.code
                        })
                    });
                }
            })
        },
        async querySearchAsync(queryString, cb){
            const vm = this;
            let inx = queryString.indexOf('[') ,
                node = queryString.substring(inx),
                s_str = node.split('[')[1];

            if(vm.fields.length === 0){
                await vm.getFields();
            }
            vm.expression = queryString;
            if(queryString.indexOf('[' > -1) && s_str !== undefined){
                let datas = [];
                datas = vm.fields.filter(f => {
                    let name = f.value;
                    return name.toLowerCase().indexOf(s_str.toLowerCase()) > -1;
                });
                if(datas.length === 0){
                    vm.$message.info("暂无查询字段");
                }
                cb(datas);
            }else {
                cb([]);
            }
        },
        filterFun(val=""){
            const vm = this;
            vm.columnTable = vm.allFunction.filter(fun =>
                fun.name.indexOf(val) > -1 ||
                fun.use.toLowerCase().indexOf(val.toLowerCase()) > -1 ||
                fun.example.toLowerCase().indexOf(val.toLowerCase()) > -1
            );
        },
        getFunction(){
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            vm.columnTable = [];
            services.getFunction().then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    result.forEach(item => {
                        vm.allFunction.push({
                            name : item.name ,
                            use : item.use ,
                            example :item.example
                        })
                    });
                    vm.filterFun();
                }
            });
            services.getLogicColumn("" , vm.datasetId).then(res => {
                if(res.data.status === 0){
                    vm.store_all_node = res.data.data;
                }
            })
        },
        validate(...arg){
            this.$refs.ruleForm.validate(...arg);
        }
    },
    created(){
        this.getFunction();
    }
}
