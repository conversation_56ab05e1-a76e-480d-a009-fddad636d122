<template>
    <div class="main">
        <div class="dataSetTreeClass">
            <el-input
                    placeholder="输入关键字搜索"
                    v-model="filterText">
            </el-input>

            <el-tree
                    class="filter-tree"
                    :data="data"
                    :props="defaultProps"
                    :filter-node-method="filterNode"
                    @node-click="getField"
                    ref="tree"
            >
            </el-tree>
        </div>
        <div class="dataSetTableClass">
            <el-table :data="tableData" height="100%" >
                <el-table-column prop="dataEngName" label="数据项英文名"></el-table-column>
                <el-table-column prop="dataChName" label="数据项中文名"></el-table-column>
                <el-table-column prop="dataType" label="数据项类型"></el-table-column>
                <el-table-column prop="dataRemark" label="备注"></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
    export default {
        name: "ExistionDataSet",
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        data() {
            return {
                tableData: [
                    /*{
                        dataEngName: "",
                        dataChName: "",
                        dataType: "",
                        dataRemark: "",
                    }*/
                ],
                filterText: "",
                data: [
                    /*{
                        id: 1,
                        label: '一级 1',
                        children: [{
                            id: 4,
                            label: '二级 1-1',
                            children: [{
                                id: 9,
                                label: '三级 1-1-1'
                            }, {
                                id: 10,
                                label: '三级 1-1-2'
                            }]
                        }]
                    }*/
                ],
                defaultProps: {
                    children: 'children',
                    label: 'name'
                },

            }
        },
        methods: {
            getField(val){
                if(val.classifierId === null){
                    return;
                }
                this.$axios.get("/manage/sql/getFields?id="+val.classifierId).then(res =>{
                    this.tableData = [];
                    if(res.data.status === 0){
                        res.data.data.forEach(item =>{
                            this.tableData.push(
                                {
                                    id:item.id,
                                    dataEngName: item.code,
                                    dataChName: item.name,
                                    dataType: item.dataType,
                                    dataRemark: item.memo,
                                }
                            )
                        })
                    }
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                })
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            }
        },
        created() {
            this.$axios.get("/manage/sql/getExisingDataSet").then(res =>{
                if(res.data.status === 0){
                    this.data = res.data.data;
                }
            })
        }
    }
</script>

<style scoped>
    .dataSetTreeClass{
        float: left;
        margin: 0px 0 0 10px;
        width: 230px;
        padding: 10px;
        background: #fff;
        border: 1px solid #ddd;
        height: calc(100% - 88px);
    }
    .dataSetTableClass{
        background: #fff;
        margin: 10px 10px 0 270px;
        padding: 10px;
        height: calc(100% - 88px);
        border: 1px solid #ddd;
        overflow: auto;
    }
    .main {
        position: relative;
        height: calc(100vh - 124px);
    }
    .filter-tree {
        height: calc(100% - 40px);
        overflow: auto;
    }
</style>