import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters, mapState} from "vuex"

export default {
    name: "tree",
    mixins: [treeMethods, commonMixins, servicesMixins],
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        ...mapState({
            myCatalog: (state) => state.plans.myCatalog,
            otherUserCatalogId: (state) => state.plans.otherUserCatalogId,
            // 可编辑节点 非分享，非他人目录(不等于他人目录 Id ，没有用户 id 值)
            nodeEditable: (state) => {
                return (data) => data.id !== state.plans.shareId && data.id !== state.plans.otherUserCatalogId && data.currentUserId === state.user.userInfo?.id;
            }
        }),
        disableClick() {
            return (data) => data.disabled;
        }
    },
    data() {
        return {
            defaultProps: {
                value: "id",
                label: "label",
                children: "children"
            },
            treeMenu: [],
            addBtnTxt: '新增子目录',
            menu: [
                /*{
                    name: '新增子目录',
                    fn: this.addDialog,
                    show: (right,node) => node && node.level < 5
                },*/
                {
                    name: '重命名目录',
                    fn: this.editNode,
                    show: () => true
                }, {
                    name: '删除目录',
                    fn: this.deleteNode,
                    show: () => true
                }
            ],
            treeCurId: '',
            expandData: []
        }
    },
    props: {
        hasSave: Boolean,
    },
    methods: {
        nodeClick(v, i) {
            this.currentId = v.id;
            this.$emit('nodeClick', v.name, v.id, i);
        },
        setCheckedNode(id) {
            this.$refs.tree.setCurrentKey(id);
        },
        setCurrentKey(key) {
            this.$refs.tree.setCurrentKey(key);
        },
        setTreeCurrentId(id) {
            this.treeCurId = id;
        },
        async initTree() {
            const vm = this, {visualServices, visualMock, settings} = this;
            let services = vm.getServices(visualServices, visualMock);
            settings.loading = true;
            vm.data = [];
            services.queryTree(settings).then(res => {
                if (res.data.status === 0) {
                    vm.setTreeData(res.data.data);
                }
            })

        },
        setTreeData(data) {
            const vm = this;
            let allList = data.allList,
                myList = data.myList,
                shareList = data.shareList,
                otherList = data.otherList,
                caseList = data.caseList;
            const other = vm.arrToTree(vm.setNodeProp(otherList || []) ,'-1','pId');
            let curId = this.treeCurId ? this.treeCurId : "1";
            let my = [{
                label: '我的空间',
                name: 'myFile',
                id: '1',
                pId: "0",
                icon: "dg-iconp icon-notebook",
                isParent: true,
                currentUserId: vm.userInfo.id,
                children: vm.getChildList(myList, "1")
            }];
            let share = shareList == null ? [] : [{
                label: "标准模型",
                name: "shareList",
                id: "2",
                pId: "0",
                icon: "dg-iconp icon-journal-b",
                isParent: true,
                currentUserId: vm.userInfo.id,
                children: vm.getChildList(shareList, "2")
            }];
            let caseSce = caseList == null ? [] : [{
                label: "场景案例",
                name: "caseList",
                id: "c1c92f3b20f2487cae20e2a40c4e60cd",
                pId: "0",
                icon: "dg-iconp icon-journal-b",
                isParent: true,
                currentUserId: vm.userInfo.id,
                children: vm.getChildList(caseList, "c1c92f3b20f2487cae20e2a40c4e60cd")
            }];
            const otherCatalog = other.length && !vm.hasSave ? other :[];
            let allChild = [...my, ...share, ...caseSce, ...otherCatalog];
            /*if (vm.hasSave && share && caseSce) {
                allChild = [my, share, caseSce];
            } else if (share == null && caseSce == null) {
                allChild = [my];
            } else if (caseSce == null && share != null) {
                allChild = [my, share];
            } else if (share == null && caseSce != null) {
                allChild = [my, caseSce];
            } else if (share != null && caseSce != null) {
                allChild = [my, share, caseSce];
            } else {
                allChild = [my];
            }*/
            /*let all = {
                label: '全部',
                name: 'all',
                id: '0',
                isParent: true,
                children: allChild
            };*/
            vm.data = allChild;
            vm.$nextTick(() => {
                if (vm.$refs.tree) {
                    vm.expandData.push(curId);
                    vm.$refs.tree.setCurrentKey(curId);
                    let currentNode = vm.$refs.tree.getCurrentNode();
                    if (!vm.hasSave) vm.$emit("getCenterTable", currentNode);
                }
            });
        },
        setNodeProp(list=[]){
            return list.map(d => {
                return {
                    id: d.id,
                    pId: d.parentId,
                    name: d.name,
                    label: d.name,
                    isParent: false,
                    isActive: false,
                    currentUserId: d.operateUserId,
                    disabled: d.id === this.otherUserCatalogId,
                }
            })
        },
        getChildList(list, pId) {
            if (!list) return;
            const vm = this;
            return list.map(d => {
                let children = d.children && d.children.length ?
                    vm.getChildList(d.children, d.id) : [];
                return {
                    id: d.id,
                    pId: pId,
                    name: d.name,
                    label: d.name,
                    isParent: false,
                    children,
                    isActive: false,
                    currentUserId: d.operateUserId,
                    disabled: false,
                };
            });
        },
        getCenterTable(nodeData, i) {
            this.nodeClick(nodeData, i);
            this.$emit("getCenterTable", nodeData);
        },
        addDialog(data) {
            // let dir = data || this.selectedData;
            // this.$emit("addTreeDir", dir);
            this.showMenu(this.selectedNode, this.selectedData);
            this.prompt('新增子目录', '', this.addTreeNode, '目录名称', 'child');
        },
        addChildren(event, node, object) {//新增子目录
            this.showMenu(node, object);
            this.prompt('新增子目录', '', this.addTreeNode, '目录名称', 'child');
        },
        addTreeNode(value) {
            const vm = this, {visualServices, visualMock, userInfo} = this;
            let services = vm.getServices(visualServices, visualMock);
            /*let pId = vm.data[0].children.filter(item => {
                return item.label === '我的' && item.pId === "0"
            })[0].id;*/
            let pId = vm.selectedData.id;
            services.addOrUpdate(value, "", pId).then(res => {
                if (res.data.status === 0) {
                    let data = res.data.data;
                    vm.successAddTip(value);
                    let newTreeNode = {
                        children: [],
                        id: data.id,
                        label: data.name,
                        isParent: false,
                        pId: pId,
                        currentUserId: userInfo.id,
                    };
                    vm.selectedData.children.unshift(newTreeNode);
                    vm.expandData.push(newTreeNode.id);
                    vm.$nextTick(() => {
                        vm.$refs.tree.setCurrentKey(newTreeNode.id);
                        let currentNode = vm.$refs.tree.getCurrentNode();
                        vm.getCenterTable(currentNode);
                    });
                }
            })
        },
        editNode(value) {//编辑
            this.prompt('重命名目录', this.selectedData.label, this.editTree, '目录名称', 'child');
        },
        deleteNode() {//删除
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            let node = this.getTreeNode(), nodeN = node.data.label;
            let currentNode = vm.$refs.tree.getCurrentNode();
            const index = node.children.findIndex(d => d.id === node.data.id);
            vm.confirm(`确认删除\"${nodeN}\"及同步删除\"${nodeN}\"下包含的仪表盘吗`, '删除', () => {
                services.deleteById(node.data.id).then(res => {
                    if (res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        node.children.splice(index, 1);
                        if (currentNode.id === node.data.id) {
                            vm.$emit('getCenterTable', vm.selectedNode.parent.data);
                            vm.$refs.tree.setCurrentKey(vm.selectedNode.parent.data.id);
                        }

                    }
                })
            });

        },
        editTree(value) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            let node = this.selectedData;
            services.addOrUpdate(value, node.id).then(res => {
                if (res.data.status === 0) {
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.label = value;
                }
            });
        },
    }
}
