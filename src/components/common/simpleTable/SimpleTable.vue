<template>
    <div class="SimpleTable">
        <div class="ce-simple__header selectn" :style="dataOver">
            <table class="ce-simple_table">
                <colgroup>
                    <col v-for="(headCol , key) in columns" :key="key" :width="headCol.width">
                </colgroup>
                <thead>
                <tr>
                    <th v-for="(column , index) in columns"
                        :key="index"
                    >
                        <slot :name="`header-${column.prop}`"
                        ></slot>
                        <template>
                            {{column.label}}
                        </template>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="ce-simple__body" :style="{'height' : height}">
            <table class="ce-simple_table" ref="tbody">
                <colgroup>
                    <col v-for="(headCol , key) in columns" :key="key" :width="headCol.width">
                </colgroup>
                <tbody>
                <tr v-for="(column , index) in data"
                    :key="index" >
                    <td v-for="(col , key) in columns"  :key="key"  >
                        <slot :name="`${col.prop}`" :row="column" :$index="index"></slot>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
    export default {
        name: "SimpleTable" ,
        props : {
            data : {
                type :Array
            },
            columns : {
                type :Array
            },
            height : {
                type : [String , Number] ,
                default : ""
            },
        },
        data(){
            return {
                dataOver : {}
            }
        },
        watch : {
            data(val){
                this.isOverflow();
            }
        },
        methods :{
            isOverflow(){
                const vm = this;
                vm.$nextTick(()=>{
                    vm.dataOver = {'padding-right' : vm.$refs.tbody.clientHeight > parseInt(vm.height)  ? '8px' : '0' };
                })
            }
        },
        computed : {
            dataCol(){
                let cols = [];
                this.columns.forEach(c =>{
                    cols.push(c.prop);
                });
                return cols;
            },
            bodyHeight(){
                return parseInt(this.height) + 'px';
            }
        }
    }
</script>

<style scoped lang="less">
    @import "css/table.less";
</style>