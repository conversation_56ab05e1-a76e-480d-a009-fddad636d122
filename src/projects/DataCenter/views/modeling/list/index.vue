<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <div class="ce-table_btns">
                <!--<el-popover placement="bottom" class="mr5" width="430" trigger="hover" v-if="rights.indexOf(analyseR) > -1"-->
                            <!--:content="analyseTip">-->
                    <!--<dg-button slot="reference" type="success" @click="rapidAnalysis">{{ analyseTxt }}</dg-button>-->
                <!--</el-popover>-->
                <el-popover placement="bottom" class="mr5" width="430" trigger="hover"
                            :content="modelTip">
                    <dg-button slot="reference" v-if="ifHasRight(addModelR) && !isOtherUser" @click="openNewModel"
                               type="primary">{{ buttonTxt }}
                    </dg-button>
                </el-popover>
                <!-- <dg-button @click="outputModel">{{analyseTxt outputTxt }}</dg-button>
                 <dg-button @click="inputModel">{{ inputTxt }}</dg-button>-->

            </div>
            <div class="ce-table__search">
                <el-row type="flex" :gutter="10">
                    <el-col :span="8">
                        <dg-select placeholder="运行状态" clearable v-model="state" :data="statusList" @change="changePage(1)"> </dg-select>

                    </el-col>
                    <el-col :span="16">
                        <el-input
                                size="mini"
                                placeholder="请输入名称搜索"
                                v-model.trim="inputValueTable"
                                v-input-limit:trim
                                @input="inputFilterSpecial($event , 'inputValueTable')"
                                @keyup.enter.native="searchTableEvent"
                        >
                            <i
                                    class="el-icon-search el-input__icon poi"
                                    slot="suffix"
                                    @click="searchTableEvent">
                            </i>
                        </el-input>
                    </el-col>
                </el-row>
                <!--<el-input-->
                        <!--size="mini"-->
                        <!--placeholder="请输入名称搜索"-->
                        <!--v-model.trim="inputValueTable"-->
                        <!--v-input-limit:trim-->
                        <!--@input="inputFilterSpecial($event , 'inputValueTable')"-->
                        <!--@keyup.enter.native="searchTableEvent"-->
                <!--&gt;-->
                    <!--<i-->
                            <!--class="el-icon-search el-input__icon poi"-->
                            <!--slot="suffix"-->
                            <!--@click="searchTableEvent">-->
                    <!--</i>-->
                <!--</el-input>-->
            </div>
        </div>
        <div class="d-s-r-table" v-if="!settings.loading">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    :max-height="tableBodyH"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                <template slot="end_time" slot-scope="{row , $index}">
                    <span v-if="row.execute_status && row.execute_status !== 'none'">
                        <i :title="transStatus(row.execute_status).text" :class="[transStatus(row.execute_status).icon , {'dg-iconp icon-f-circle-check' : row.execute_status === 'SUCCESS' }]"></i>
                        <!-- <i class="el-icon-circle-check end_time success" v-if="row.execute_status === 'SUCCESS'"></i>
                        <i class="el-icon-circle-check end_time" v-else></i> -->
                        {{row.end_time}}
                    </span>
                    <span v-else>-</span>
                </template>
                <template slot="operate" slot-scope="{row , $index}" v-if="!isOtherUser">
                    <span class="r-c-action"
                          v-for="(item,index) in operateIconShow(row).slice(0 , 3)"
                          :key="index"
                    >
                        <el-button type="text"
                                   :class="item.class"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.name"
                        >{{ item.name }}</el-button> <!--v-html="item.icon"-->
                    </span>
                    <dir-edit-action v-if="operateIconShow(row).length > 3"
                                     placement="bottom"
                                     @command="menuCommand($event , row , $index)" :data="operateIconShow(row).slice(3)"
                                     :node="row"
                                     :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>{{moreTxt}}</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
                <template slot="modelName" slot-scope="{row}">
                    <div class="detailName poi" v-if="ifHasRight(lookOverR)" @click="detailShow(row)">{{ row.modelName }}</div>
                    <span v-else>{{row.modelName}}</span>
                </template>
            </common-table>
        </div>
        <!--定时运行-->
        <timing ref="timing" :rowData="rowData" @changePage="changePage(1)"/>
        <!--生成api-->
        <create-api ref="createAPI" :modelInfo="rowData" :entrance="'model'" />
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>
