<template>
    <div class="classificationCounting ce-plug_cont" v-loading="loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont" v-show="activeName === 'setting_base'">
            <div class="ce-plug_attr">
                <div>
                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="4">
                            <span>目标表: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="zipperTableId"
                                    placeholder="请选择"
                                    filterable
                                    @change="tableChange"
                            >
                                <el-option
                                        v-for="item in tableInfo"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>

                <div>

                    <span>字段映射: </span>


                    <el-table
                            :data="tableData"
                            style="width: 100%"
                            size="small"
                            border
                            max-height="200"
                    >
                        <el-table-column v-for="(item , inx) in tHead"
                                         :key="inx"
                                         :label="item.label"
                                         :prop="item.prop"
                                         align="center"
                                         :show-overflow-tooltip="true"

                        >
                            <template slot-scope="scope">
                                <label v-if="item.prop==='addColumn'">{{scope.row[item.prop]}}</label>

                                <el-select
                                        v-if="item.prop==='zipperColumn'"
                                        size="small"
                                        v-model="scope.row.zipperColumn"
                                        placeholder="请选择"
                                        filterable
                                        clearable
                                >
                                    <el-option
                                            v-for="item in zipperColumns"
                                            :key="item.code"
                                            :label="item.name"
                                            :value="item.code">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>

                        <el-table-column
                                label="操作"
                                prop="operate"
                                width="70"
                                align="center"
                        >
                            <template slot="header" slot-scope="scope">
                                <em class="icon ce_link f14 ml5" title="添加" @click="addRuleEdit">&#xe6e1;</em>
                            </template>
                            <template slot-scope="scope">
                                <i
                                        class="icon ce_link model_status"
                                        v-for="(col , inx) in operateIcons"
                                        :key="inx"
                                        :title="col.tip"
                                        v-html="col.icon"
                                        @click="col.clickFn(scope.row,scope.$index)"
                                ></i>
                            </template>
                        </el-table-column>

                    </el-table>


                </div>


                <div>

                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="5">
                            <span>业务字段: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="businessField"
                                    placeholder="请选择"
                                    filterable
                            >
                                <el-option
                                        v-for="item in incColumns"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>

                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="5">
                            <span>排序字段: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="timeField"
                                    placeholder="请选择"
                                    filterable
                            >
                                <el-option
                                        v-for="item in incColumns"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>

                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="5">
                            <span>分组字段: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="groupField"
                                    placeholder="请选择"
                                    filterable
                                    multiple
                            >
                                <el-option
                                        v-for="item in incColumns"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>


                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="5">
                            <span>拉链方向: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="zipperOrder"
                                    placeholder="请选择"
                                    filterable
                            >
                                <el-option
                                        v-for="item in zipperDirection"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>

                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="5">
                            <span>填充值:</span>
                        </el-col>
                        <el-col :span="8">
                            <el-input size="mini" v-model="fillField"
                                      class="inputField"></el-input>
                        </el-col>
                    </el-row>

                    <el-row :gutter="5">
                        <el-col class="ce-normal_item is-require" :span="5">
                            <span>时间字段: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="dateField"
                                    placeholder="请选择"
                                    filterable
                            >
                                <el-option
                                        v-for="item in zipperColumns"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>

                  <el-row :gutter="5">
                    <el-col class="ce-normal_item is-require" :span="5">
                      <span>管理时间字段: </span>
                    </el-col>
                    <el-col :span="8">
                      <el-select
                          size="small"
                          v-model="managerTime"
                          placeholder="请选择"
                          filterable
                      >
                        <el-option
                            v-for="item in zipperColumns"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>

                    <el-row :gutter="4">
                        <el-col class="ce-normal_item" :span="6">
                            <span>拉链开始字段: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="startColumn"
                                    placeholder="请选择"
                                    filterable
                            >
                                <el-option
                                        v-for="item in zipperColumns"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>


                    <el-row :gutter="4">
                        <el-col class="ce-normal_item" :span="6">
                            <span>拉链结束字段: </span>
                        </el-col>
                        <el-col :span="8">
                            <el-select
                                    size="small"
                                    v-model="endColumn"
                                    placeholder="请选择"
                                    filterable
                            >
                                <el-option
                                        v-for="item in zipperColumns"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <div class="attr_btn">
                <el-button
                        v-for="(btn , inx) in buttons"
                        :key="inx"
                        size="mini"
                        :type="btn.type"
                        @click="btn.clickFn"
                >{{btn.name}}
                </el-button>
            </div>
        </div>
        <div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
        <PlugSetting ref="setting" @saveFn="saveRowData"/><!--组件配置弹窗-->
    </div>
</template>

<script>
    import PlugSetting from '../ruleEditing/PlugSetting'
    import SettingSenior from '../component/SettingSenior'
    import PreView from "../component/PreView"
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import * as listApi from "@/projects/DataCenter/views/dataSources/service-mixins/service-mixins";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {mapGetters} from "vuex"

    export default {
        name: "numberZipperTablePlugin",
        components: {
            PlugSetting,
            SettingSenior,
            PreView
        },
        mixins: [commonMixins, listApi.servicesMixins],
        props: {
            sourceData: Object,
            row: Object
        },
        data() {
            return {
                operateIcons: [
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteData}
                ],
                tHead: [
                    {
                        prop: "addColumn",
                        label: "增量表字段",
                    },
                    {
                        prop: "zipperColumn",
                        label: "拉链表字段",
                    },
                ],
                statsDisable: true,
                disabled: true,
                addbtn: "添加",
                loading: false,
                dataTypes: ['Float', 'Double', 'BigDecimal'],
                isSeniorShow: {},
                activeName: 'setting_base',
                tab_panel: [
                    {
                        label: '基本设置',
                        name: 'setting_base'
                    }, {
                        label: '高级设置',
                        name: 'setting_senior'
                    }
                ],
                buttons: [
                    {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary'
                    }, {
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                ],
                status: false,

                dataDispose: "",//数据处理表达式输入框绑定的数据
                //分组统计
                classificationFormData: [],
                inputType: [],
                rowData: '',

                businessField: '',
                timeField: '',
                groupField: [],
                statField: '',
                incrementField: '',
                zipperOrder: '',
                fillField: '',
                dateField: '',
                startDate: '',
                endDate: '',
                updataStat: '',
                columns: [],
                incColumns: [],
                zipperColumns: [],
                updataStatList: [
                    {
                        name: "数据覆盖",
                        value: "dataCoverage"
                    },
                    {
                        name: "数据累积",
                        value: "dataAccumulation"
                    }
                ],
                zipperDirection: [
                    {
                        name: "向前",
                        value: "asc" //FORWARD
                    },
                    {
                        name: "向后",
                        value: "desc" //BACKWARD
                    }
                ],
                isAdd: [
                    {
                        name: "true",
                        value: "true"
                    },
                    {
                        name: "false",
                        value: "false"
                    }
                ],

                realZipperTableCode: "",
                zipperTableId: "",
                incTableId:"",
                tableData: [],
                selectTreeOpt: [],
                tableTreeOpt: [],
                databaseId: "",
                treeProps: {
                    id: 'id',
                    label: "label",
                    children: "children",
                },
                defaultProps: {
                    value: 'id',
                    label: "label",
                    children: "children",
                },
                startColumn: "",
                endColumn: "",
                managerTime :"",
                tableInfo: [],
                oldZipperTableId:"",
                oldIncTableId:"",
            }
        },
        methods: {
            addRuleEdit() {
                if (this.tableData.length == this.incColumns.length) {
                    this.$message.error("暂无未映射字段！");
                    return;
                }

                let addColumns = [];
                this.tableData.forEach(i => {
                    addColumns.push(i.addColumn);
                });

                for (let i = 0; i < this.incColumns.length; i++) {
                    if (addColumns.indexOf(this.incColumns[i].code) == -1) {
                        this.tableData.push({
                            addColumn: this.incColumns[i].code,
                            zipperColumn: ''
                        });
                        break;
                    }
                }
            },
            deleteData(row, index) {
                this.tableData.splice(index, 1);
            },
            tableChange(value) {
                let _this = this;

                this.tableData = [];
                _this.businessField = '';
                _this.timeField = '';
                _this.zipperOrder = '';
                _this.groupField = [];
                _this.fillField = '';
                _this.dateField = '';
                _this.startColumn = '';
                _this.endColumn = '';
                _this.managerTime = '';

                let middleColumn = [];
                middleColumn = _this.incColumns;
                _this.incColumns = _this.zipperColumns;
                _this.zipperColumns = middleColumn;


                _this.incColumns.forEach(i => {
                    _this.tableData.push({
                        "addColumn": i.code,
                        "zipperColumn": ""
                    })
                });
                let middleClassifierStatId = "";
                middleClassifierStatId = _this.oldZipperTableId;
                _this.oldZipperTableId = _this.oldIncTableId;
                _this.oldIncTableId = middleClassifierStatId;
            },
            updataChange(updataStat) {
                if (updataStat == "dataCoverage") {
                    this.statsDisable = false;
                } else {
                    this.statField = null;
                    this.statsDisable = true;
                }

            },
            saveRowData() {
                this.dataDispose = this.rowData.serviceOrgId;
            },
            editFn(data) {
                let rowData = {
                    columnCode: '数据处理表达式code',
                    columnName: '数据处理表达式',
                    serviceOrgId: data,
                };
                this.rowData = rowData;
                this.$refs.setting.openSetting(this.sourceData, this.rowData);
            },

            selectDataType(val) {
                val.hasPrecision = this.checkDataType(val.outputTypeValue);
            },
            init() {
                let _this = this;
                this.stepId = this.sourceData.id;
                this.$axios.get("/zipperTable/queryNumberZipperData?tranStepId=" + this.stepId + "&transId=" + this.row.transId)
                    .then(res => {
                            if (res.data.status === 1) {
                                _this.$message.error(res.data.msg);
                            } else {
                                let inputColumn = res.data.data.inputColumn;

                                if (inputColumn !== null && inputColumn !== undefined) {
                                    inputColumn.forEach(i => {
                                        let column = {
                                            name: i.columnZhName !== null ? i.columnZhName : i.columnName,
                                            code: i.columnName,
                                            type: i.columnType
                                        };
                                        _this.columns.push(column);
                                    });
                                }

                                _this.tableInfo = res.data.data.tableInfo;

                                let pluginMeta = res.data.data.zipperTablePlugin;

                                _this.incTableId = pluginMeta.incTableId;

                                if( _this.tableInfo.length >=2){
                                    _this.zipperTableId = (pluginMeta.zipperTableId == null || _this.zipperTableIsChange()) ? _this.tableInfo[1].value : pluginMeta.zipperTableId;

                                    _this.oldZipperTableId = (pluginMeta.zipperTableClassifierStatId === null|| pluginMeta.zipperTableClassifierStatId==="")? _this.tableInfo[0].classifierStatId : pluginMeta.zipperTableClassifierStatId;
                                    _this.oldIncTableId = (pluginMeta.incTableClassifierStatId === null || pluginMeta.incTableClassifierStatId==="")? _this.tableInfo[1].classifierStatId : pluginMeta.incTableClassifierStatId;
                                }else {
                                    _this.zipperTableId = "";
                                }

                                if (inputColumn !== null && inputColumn !== undefined) {
                                    inputColumn.forEach(i => {
                                        let column = {
                                            name: i.columnZhName !== null ? i.columnZhName : i.columnName,
                                            code: i.columnName,
                                            type: i.columnType
                                        };
                                        if (i.stepId === _this.zipperTableId) {
                                            _this.zipperColumns.push(column);
                                        } else {
                                            _this.incColumns.push(column);
                                        }
                                    });
                                }




                                let isNullTableData = null !== pluginMeta.mappingColumnsJSON && '' !== pluginMeta.mappingColumnsJSON && undefined !== pluginMeta.mappingColumnsJSON;

                                if (isNullTableData && _this.tableIsChange()) {
                                    _this.tableData = JSON.parse(pluginMeta.mappingColumnsJSON);
                                } else {
                                    _this.incColumns.forEach(i => {
                                        _this.tableData.push({
                                            "addColumn": i.code,
                                            "zipperColumn": ""
                                        })
                                    })
                                }


                                if (null !== pluginMeta.groupFieldJSON && '' !== pluginMeta.groupFieldJSON && undefined !== pluginMeta.groupFieldJSON) {
                                    if (_this.tableIsChange()) {
                                        _this.groupField = JSON.parse(pluginMeta.groupFieldJSON);
                                    } else {
                                        _this.groupField = [];
                                    }
                                } else {
                                    _this.groupField = [];
                                }

                                if(_this.tableIsChange()){
                                    _this.businessField = pluginMeta.businessField;
                                    _this.timeField = pluginMeta.timeField;
                                    _this.zipperOrder = pluginMeta.zipperOrder;
                                    _this.fillField = pluginMeta.fillField;
                                    _this.dateField =  pluginMeta.dateField;
                                    _this.startColumn =pluginMeta.startColumn;
                                    _this.endColumn = pluginMeta.endColumn;
                                    _this.managerTime = pluginMeta.managerTime;
                                } else {
                                    _this.businessField = "";
                                    _this.timeField = "";
                                    _this.zipperOrder = "";
                                    _this.fillField = "";
                                    _this.dateField = "";
                                    _this.startColumn = "";
                                    _this.endColumn = "";
                                    _this.managerTime = "";
                                }


                            }

                        }
                    ).catch(err => {
                    console.log(err);
                    _this.$message.error("服务器发生异常，请联系管理员")
                })
            },

            zipperTableIsChange() {
                let _this = this;
                let tableId = [];
                _this.tableInfo.forEach(i => {
                    tableId.push(i.value);
                });

                if (tableId.indexOf(_this.zipperTableId) === -1) {
                    return true;
                }

                return false;
            },

            tableIsChange() {
                let _this = this;
                let classifierStatId = [];
                _this.tableInfo.forEach(i => {
                    classifierStatId.push(i.classifierStatId);
                });

                if (classifierStatId.indexOf(_this.oldIncTableId) === -1) {
                    return false;
                }

                if (classifierStatId.indexOf(_this.oldZipperTableId) === -1) {
                    return false;
                }

                return true;
            },
            isInColumn(columns, column) {
                for (let i = 0; i < columns.length; i++) {
                    if (columns[i].code == column) {
                        return column;
                    }
                }
                return '';
            },

            isALLInColumn(columns, columns2) {

                let inColumns = [];
                columns.forEach(i => {
                    inColumns.push(i.code);
                });

                for (let j = 0; j < columns2.length; j++) {
                    if (inColumns.indexOf(columns2[j]) == -1) {
                        return false;
                    }
                }
                return true;
            },
            // 分组统计删除操作
            deleteClassificationData(row, index) {
                this.classificationFormData.splice(index, 1);
            }
            ,
            saveFn() {
                let _this = this;
                let pluginMeta = {};


                if (_this.businessField === '' || _this.businessField === null) {
                    _this.$message.error("请先选择拉链业务字段！");
                    return;
                }

                if (_this.timeField === '' || _this.timeField === null) {
                    _this.$message.error("请先选择排序字段！");
                    return;
                }

                if (this.groupField.length === 0) {
                    _this.$message.error("必须至少配置一个分组规则！");
                    return;
                }


                if (_this.zipperOrder === '' || _this.zipperOrder === null) {
                    _this.$message.error("请先选择拉链方向！");
                    return;
                }

                if (_this.fillField === '' || _this.fillField === null) {
                    _this.$message.error("请先填写填充字段！");
                    return;
                }

                if (_this.managerTime === '' || _this.managerTime === null) {
                    _this.$message.error("请先填写时间管理字段！");
                    return;
                }

                pluginMeta["incTableClassifierStatId"] = _this.oldIncTableId;
                pluginMeta["zipperTableClassifierStatId"] = _this.oldZipperTableId;

                pluginMeta["zipperTableId"] = _this.zipperTableId;
                pluginMeta["incTableId"] = _this.incTableId;


                pluginMeta['mappingColumnsJSON'] = JSON.stringify(_this.tableData);

                pluginMeta['businessField'] = _this.businessField;
                //排序字段
                pluginMeta['timeField'] = _this.timeField;
                pluginMeta['groupFieldJSON'] = JSON.stringify(_this.groupField);
                pluginMeta['zipperOrder'] = _this.zipperOrder;
                pluginMeta['fillField'] = _this.fillField;
                pluginMeta['dateField'] = _this.dateField;

                pluginMeta['startColumn'] = _this.startColumn;
                pluginMeta['endColumn'] = _this.endColumn;
                pluginMeta['managerTime'] = _this.managerTime;

                pluginMeta['startDataType'] = _this.getDataType(_this.startColumn);
                pluginMeta['endDataType'] = _this.getDataType(_this.endColumn);

                let inColumns = [];
                _this.incColumns.forEach(i => {
                    inColumns.push(i.code);
                });
                let ziColumns = [];
                _this.zipperColumns.forEach(i => {
                    ziColumns.push(i.code);
                });
                pluginMeta['inputColumns'] = JSON.stringify(inColumns);
                pluginMeta['zipperColumns'] = JSON.stringify(ziColumns);


                _this.loading = true;
                _this.$axios.post("/zipperTable/saveNumberZipperPlugin?tranStepId=" + _this.stepId, pluginMeta).then(res => {
                    if (res.data.status === 1) {
                        _this.$message.error(res.data.msg);
                    } else {
                        _this.$message.success("保存成功")
                    }
                    _this.loading = false;
                }).catch(err => {
                    _this.loading = false;
                    _this.$message.error("服务器发生异常，请联系管理员")
                })

            },


            getDataType(field) {
                let _this = this;
                let type = 'String';
                for (let i = 0; i < _this.zipperColumns.length; i++) {
                    if (field === _this.zipperColumns[i].code) {
                        type = _this.zipperColumns[i].type;
                        break;
                    }
                }
                return type;
            }
            ,

            checkDataType(val) {
                if (this.dataTypes.indexOf(val) > -1) {
                    return true;
                } else {
                    return false;
                }
            }
            ,
            previewFn() {
                this.$refs.preview.show(this.row, this.sourceData.id);
            },
            initDBTree() {
                const vm = this, {listApi, listMock, settings} = this;
                let dw_service = vm.getServices(listApi, listMock);
                settings.loading = true;
                dw_service.queryDirTree().then(res => {
                    // console.log("业务库数据：", res);
                    vm.resTreeDataDispose(res.data.data);
                })
            },
            resTreeDataDispose(data) {
                const _this = this;
                for (let i = data.length - 1; i >= 0; i--) {
                    if (data[i].code !== "DATAWAREHOUSE_DIR") {
                        data.splice(i, 1);
                    } else {
                        let isHas = _this.packageData(data[i].children);
                        if (!isHas) {
                            data.splice(i, 1)
                        }
                    }
                }
                this.selectTreeOpt = data;
            },
            packageData(val) {
                const _this = this;
                let isHasDB = true;
                for (let i = val.length - 1; i >= 0; i--) {
                    if (val[i].children !== null) {
                        let isHas = _this.packageData(val[i].children);
                        if (!isHas) {
                            val.splice(i, 1)
                        }
                    } else {
                        val[i].children = [];
                    }
                }

                if (val.length <= 0) {
                    isHasDB = false;
                }
                return isHasDB;
            },
            selectTreeChange(val, value) {
                const _this = this;
                // if (null != value.data.id) {
                //     this.getTargetTable(value.data.id, "");
                // }

            },
            getTargetTable(instanceId, content) {
                const vm = this, {listApi, listMock} = this;
                let dw_service = vm.getServices(listApi, listMock);
                vm.tableTreeOpt = [];
                dw_service.queryDataBaseTableAll(instanceId).then(res => {
                    if (res.data.status === 0) {
                        let data = res.data.data;
                        data.forEach(item => {
                            let tableNode = {
                                name: item.name,
                                label: item.code,
                                value: item.id,
                            };
                            vm.tableTreeOpt.push(tableNode);
                        })
                    }
                })
            },
        },
        created() {
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: false,
                isSampleExpr: false,
                isThread: false,
                isException: false,
                notTransition: false,
            };
            this.init();
            this.initDBTree();
        }
    }
</script>

<style scoped>
    .el-row {
        margin-bottom: 5px;
    }

    .countingRadio {
        width: 100%;
        text-align: center;
    }

    .countingContent {
        margin-top: 10px;
    }

    .countingSelect {
        height: 5px;
    }

    .btn_new_model {
        float: right;
        margin-bottom: 5px;
        margin-top: -27px;
        margin-left: 3px;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
