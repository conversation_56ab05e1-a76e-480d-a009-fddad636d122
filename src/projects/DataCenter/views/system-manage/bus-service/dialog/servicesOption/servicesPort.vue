<template>
    <div>
        <ce-list-table
                :pagination="false"
                :border="false"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                :operate-icons="operateIcons"
        >
            <template slot="button"></template>
        </ce-list-table>
        <div>
            <dg-button v-footer @click="close">取消</dg-button>
            <dg-button v-footer type="primary" @click="submit">确定</dg-button>
        </div>
    </div>
</template>

<script>
    export default {
        name: "servicesPort",
        data(){
            return {
                filterText:'',
                operateIcons:[
                    {
                        name: "测试",
                        clickFn: this.test,
                        show: () => true,
                        condition: (right) => true
                    },
                ],
                tableData:[{name:''}],
                tHeadData:[
                    {
                        prop: 'name',
                        label: "接口名称",
                        minWidth: 100,
                    },{
                        prop: 'type',
                        label: "接口类型",
                        minWidth: 100,
                    },{
                        prop: 'method',
                        label: "请求方法",
                        minWidth: 120,
                    },{
                        prop: 'operate',
                        label: "操作",
                        width: 120,
                        align : "center",
                    },
                ]
            }
        },
        methods:{
            close(){
                this.$emit('close');
            },
            submit(){

            },
            //测试
            test(){

            }
        }
    }
</script>

<style scoped>

</style>