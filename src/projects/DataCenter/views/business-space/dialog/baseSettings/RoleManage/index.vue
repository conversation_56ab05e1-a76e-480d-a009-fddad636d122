<template>
    <div class="RoleManage">
        <ce-list-table
                :border="false"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                @changePage="changePage"
                :filter-placeholder="filterPlaceholder"
        >
            <template slot="button">
                <el-button type="primary" @click="inputData" >{{inputTxt}}</el-button>
                <el-button @click="outputData" >{{outputTxt}}</el-button>
            </template>
        </ce-list-table>
    </div>
</template>

<script>

export default {
    name: "RoleManage",
    data(){
        return {
            filterPlaceholder:'请输入论元角色名称搜索',
            inputTxt : 'Excel导入' ,
            outputTxt : 'Excel导出' ,
            filterText : "" ,
            tHeadData:[
                {
                    prop: "type" ,
                    label : "分类",
                    minWidth:"140px"
                },{
                    prop: "roleName" ,
                    label : "论元角色名称",
                    minWidth:"140px"
                },{
                    prop: "roleCode" ,
                    label : "论元角色编码",
                    minWidth:"100px"
                },{
                    prop: "memo" ,
                    label : "备注",
                    minWidth:"100px"
                },
            ],
            tableData:[],
        }
    },
    methods : {
        changePage(inx){

        },
        inputData(){
            const vm = this;
            let layer = vm.$dgLayer({
                title : 'Excel导入',
                content : require('@views/business-space/dialog/baseSettings/components/excelImport.vue'),
                maxmin:false,
                move:false,
                area : ["640px" , "260px"],
                on : {
                    submit(list){

                    }
                }
            })
        },
        outputData(){}
    }
}
</script>

<style scoped>

</style>
