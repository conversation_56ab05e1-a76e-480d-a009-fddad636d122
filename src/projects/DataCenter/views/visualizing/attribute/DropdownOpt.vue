<template>
  <!--选中的值 activeKey 等于 key 或者 该项的val 值 -->
    <ul class="DropdownOpt selectn">
        <template v-for="(item , key) in data">
        <li :key="key" v-if="item.label && optCondition(key , data) "
            :class="{'active_li':activeKey === key || activeKey === item.val ,'disabled' : item.disabled}"
            @click="checkItem(key , item)"
            @mouseenter="showOption($event , item , key)"
            @mouseleave="hideOption(item)">
            <span>{{item.label}}</span> <span v-if="item.value && key !== 'data' && key !== 'format'">( <span v-html="item.name"></span> )</span>
            <i class="el-icon-arrow-right ce-item_right" v-if="item.children"></i>
            <DropdownOpt :class="{'showOpt': showOpt}" :style="style" @changeVal="changeVal(arguments , key)" :value="item.value" :can-checked="true" :data="option" v-if="showOpt === key && item.children" />
        </li>
        </template>
    </ul>
</template>

<script>
    import DropdownOpt from './DropdownOpt'
    import * as $ from "jquery"
    export default {
        name: "DropdownOpt" ,
        components : {
            DropdownOpt
        },
        props: {
            data : Object ,
            canChecked : {
                type : Boolean ,
                default : false
            },
            value : {
                type : String ,
                default: ''
            }
        },
        data(){
            return {
                option : {} ,
                showOpt : '' ,
                left: 0 ,
                activeKey : '' ,
                style : {}
            }
        },
        methods : {
            optCondition(key , data){
                let condition;
                if(data.data){
                    if(data.data.indexType === 'DIMENSION'){
                        condition = key !== "data" && key !== "format" //&& key !== 'func';
                    }else if(data.data.indexType === 'MEASURE') {
                        condition = key !== "data" && key !== "format" ;
                    }
                }else {
                    condition = true;
                }
                return condition;
            },
            init(){
                this.activeKey = this.value;

            },
            showOption(e , item ,key){
                if(!item.children) return;
                let pageHeight = document.documentElement.clientHeight,
                    y = e.clientY,
                    h = 34 * Object.keys(item.children).length ,
                    pageWidth = document.documentElement.clientWidth;
                this.showOpt = key;
                this.option = item.children;
                let n , left , l , parentW , childW;
                l = $(e.currentTarget).parents(".el-popover").position().left;
                parentW = $(e.currentTarget).parents(".DropdownOpt").outerWidth();
                this.$nextTick(()=>{
                    childW = $(e.currentTarget).parents(".DropdownOpt").find(".DropdownOpt").outerWidth();
                    if(pageWidth - 300 > l){
                        left = parentW + "px";
                    }else {
                        left = "-" + childW + "px"
                    }
                    if(pageHeight - y > h ){
                        this.style = {
                            top : '-5px' ,
                            left : left
                        };
                    }else {
                        n = Math.floor(( pageHeight - y - h) / 34 ) * 34;
                        this.style = {
                            top : n + 'px' ,
                            left : left
                        };
                    }
                })
            },
            hideOption(item){
                this.showOpt = '';
            },
            checkItem(key ,item){
                if(item.func){
                    this.$emit(item.func , this.data );
                }
                if(!this.canChecked ) return;
                this.activeKey = item.val || key;
                this.$emit('changeVal' , this.activeKey ,item.label);
            },
            changeVal(params , key ){
                this.$emit('changeVal' ,params[0] , key , params[1]);
            },
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>
    .DropdownOpt {
        padding:5px 0;
        position: relative;
        z-index: 10;
    }
    .DropdownOpt li {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 30px 0 20px;
        height: 34px;
        line-height: 34px;
        outline: none;
        color: #666;
        cursor: pointer;
        white-space: nowrap;
    }
    .DropdownOpt li:hover ,.DropdownOpt li.active_li {
        background: #F5F7FA;
        color: #409EFF;
        white-space: nowrap;
    }
    .ce-item_right {
        position: absolute;
        right: 10px;
        font-size: 14px;
    }
    .showOpt {
        position: absolute;
        top: -5px;
        left: 220px;
        min-width: 120px;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        border-radius: 4px;
    }
</style>
