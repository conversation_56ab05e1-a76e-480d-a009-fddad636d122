/**
 * 数据源 接口
 * @Author: wangjt
 * @Date: 2020-06-15
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/manage/dataWarehouse/";

/*
* 获取 数据源 库
* @params id pId keyword
* @returns {Promise<*>}
* */

export async function queryDirTree( id = "" , pId = "" , keyword = "") {
    return await reqUtil.get({
        url: root + 'queryDirTree'
    }, {id , pId , keyword});
}

/*
* 获取表 数据
* @params dwId
* @returns {Promise<*>}
* */
export async function queryDataBaseTableAll( dwId) {
    return await reqUtil.get({
        url: root + 'queryDataBaseTableAll'
    }, {dwId});
}