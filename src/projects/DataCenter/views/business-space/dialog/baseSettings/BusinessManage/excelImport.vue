<template>
    <div class="excelImport">
        <el-form label-width="80px" label-position="right">
            <el-form-item label="选择文件:">
                <dg-upload
                        class="upload-demo"
                        ref="upload"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel,.csv,.xlsx"
                        action=""
                        :on-preview="handlePreview"
                        :on-remove="handleRemove"
                        :on-change="fileChange"
                        v-model="fileList"
                        multiple
                        :limit="limit"
                        :auto-upload="false"
                >
                    <el-button slot="trigger" size="small" icon="el-icon-upload2">{{ chooseFile }}</el-button>
                    <el-link class="ml20" type="primary" :underline="false">{{ download }}</el-link>
                    <div slot="tip" class="el-upload__tip">
                        <span>共上传</span>
                        <span class="tip-red">{{fileLen}}</span>
                        <span>个文件,点击相应文件可查看详情</span>
                        <span>{{tipMsg}}</span>
                    </div>
                    <template slot="fileList" slot-scope="scope">
                        <div class="import-file" :title="scope.name + (scope.size/1024)+'KB'">
                            <i class="el-icon-paperclip"></i>
                            {{ `${scope.name} (${scope.size/1024})KB` }}
                        </div>
                    </template>
                </dg-upload>
            </el-form-item>
        </el-form>
        <el-button v-footer @click="close">{{btnCancelTxt}}</el-button>
        <el-button v-footer type="primary" @click="submit">{{btnCheckTxt}}</el-button>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "excelImport",
    mixins:[commonMixins],
    computed: {
        fileLen(){
            return this.fileList.length;
        }
    },
    data() {
        return {
            chooseFile: "选取文件",
            fileList: [],
            download: "下载模板" ,
            tipMsg : "支持xls,xlsx,csv类型的文件",
            limit : 3
        }
    },
    methods: {
        showPreview(tableData , tableHead , file){
            let layer = this.$dgLayer({
                title : file.name ,
                content :require("./preview.vue") ,
                maxmin:false,
                move:false,
                noneBtnField: true,
                area : ['1000px' , '500px'],
                props : {
                    tableData  ,
                    tableHead
                }
            })
        },
        handlePreview(file) {
            this.importFileData(file.raw);
        },
        async importFileData(row){
            const vm = this;
            const reader = new FileReader();
            reader.readAsArrayBuffer(row);
            reader.onload = await function (){
                let tableData = [] , tableHead = [];
                const buffer = reader.result;
                const bytes = new Uint8Array(buffer);
                const length = bytes.byteLength;
                let binary = '';
                for (let i = 0; i < length; i++) {
                    binary += String.fromCharCode(bytes[i]);
                }
                const XLSX = require('xlsx');
                const wb = XLSX.read(binary, {
                    type: 'binary'
                });
                const outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
                tableData = [...outdata];
                let head = {};
                tableData.map((item) => {
                    for(let k in item){
                        if(!head[k]){
                            tableHead.push({
                                prop : k ,
                                label: k ,
                                minWidth : '100px'
                            })
                            head[k] = item[k];
                        }
                    }
                });
                vm.showPreview(tableData , tableHead , row);
            }
        },
        handleRemove() {

        },
        fileChange(file , fileList){
            if(fileList.length <= this.limit){
                this.fileList = fileList;
            }
        },
        submit(){
            if(!this.fileList.length){
                this.$message.warning("请选取文件!");
                return;
            }
            this.$emit('submit' , this.fileList);
            this.close();
        },
        close(){
            this.$emit('close')
        }
    }
}
</script>

<style scoped lang="less">
    .tip-red {
        color: #F56C6C;
        padding: 0 2px;
    }
    .import-file {
        color: rgba(0,0,0,.45);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:hover {
            color: #1890FF;
        }
    }
</style>
