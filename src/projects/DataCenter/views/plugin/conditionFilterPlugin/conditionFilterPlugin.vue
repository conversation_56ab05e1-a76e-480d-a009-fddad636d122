<template>
    <div class="classificationCounting ce-plug_cont" v-loading="loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont" v-show="activeName === 'setting_base'">
            <div class="ce-plug_attr">
                <el-row :gutter="10">
                    <el-col :span="5" class=" ce-normal_head">
                        <el-button type="primary"
                                   size="mini"
                                   @click="add"
                                   plain
                        >{{addbtn}}
                        </el-button>
                    </el-col>
                    <el-col :span="19" class="ce-normal_head">
                        <el-radio-group v-model="relationCondition">
                            <el-radio v-for="rad in radioList" :key="rad.value" :label="rad.value">{{rad.label}}
                            </el-radio>
                        </el-radio-group>
                    </el-col>
                </el-row>
                <div class="ce-list_cont">
                    <el-table
                            height="100%"
                            class="h_auto-table"
                            border
                            :show-header="status"
                            :data="tableData"
                            size="mini">
                        <el-table-column type="index" align="center" width="50px"></el-table-column>
                        <el-table-column align="center">
                            <template slot-scope="{row , $index}">
                                <el-row :gutter="5">
                                    <el-col :span="8">
                                        <dg-select
                                                size="mini"
                                                v-model="row.field"
                                                :data="columns"
                                                filterable
                                                @change="codeChange($event , $index)"
                                        >
                                        </dg-select>
                                    </el-col>
                                    <el-col :span="5">
                                        <dg-select
                                                size="mini"
                                                v-model="row.condition"
                                                filterable
                                                @change="conditionChange($event , $index)"
                                                :data="numberCompareSymbol"
                                        >
                                        </dg-select>
                                    </el-col>
                                    <el-col :span="11">
                                        <dg-select
                                                size="mini"
                                                v-model="row.value"
                                                :disabled="row.isInput"
                                                filterable
                                                allow-create
                                                :data="valueColumns"
                                        ></dg-select>
                                    </el-col>
                                </el-row>
                            </template>
                        </el-table-column>
                        <el-table-column width="50px" align="center">
                            <template slot-scope="{row , $index}">
                                <el-popconfirm
                                        size="mini"
                                        v-for="(col , inx) in operateIcons"
                                        title="确认删除?"
                                        @confirm="col.clickFn(row,$index )"
                                >
                                    <i
                                            slot="reference"
                                            class="icon ce_link model_status"
                                            :key="inx"
                                            :title="col.tip"
                                            v-html="col.icon"
                                    ></i>
                                </el-popconfirm>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="attr_btn">
                <el-button
                        v-for="(btn , inx) in buttons"
                        :key="inx"
                        size="mini"
                        :type="btn.type"
                        @click="btn.clickFn"
                >{{btn.name}}
                </el-button>
            </div>
        </div>
        <div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
        <PlugSetting ref="setting" @saveFn="saveRowData"/><!--组件配置弹窗-->
    </div>
</template>

<script>
    import PlugSetting from '../ruleEditing/PlugSetting'
    import SettingSenior from '../component/SettingSenior'
    import PreView from "../component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins"

    export default {
        name: "conditionFilterPlugin",
        components: {
            PlugSetting,
            SettingSenior,
            PreView
        },
        props: {
            sourceData: Object,
            row: Object
        },
        mixins: [commonMixins, servicesMixins],
        data() {
            return {
                radioList: [
                    {
                        value: 'and',
                        label: '且'
                    },
                    {
                        value: 'or',
                        label: '或'
                    },
                ],
                addbtn: "添加",
                loading: false,
                dataTypes: ['Float', 'Double', 'BigDecimal'],
                isSeniorShow: {},
                activeName: 'setting_base',
                numberCompareSymbol: [
                    {
                        label: '大于',
                        value: '>'
                    }, {
                        label: '小于',
                        value: '<'
                    }, {
                        label: '等于',
                        value: '='
                    }, {
                        label: '不等于',
                        value: '!='
                    }, {
                        label: '大于等于',
                        value: '>='
                    }, {
                        label: '小于等于',
                        value: '<='
                    }, {
                        label: '模糊匹配',
                        value: 'like'
                    }, {
                        label: '包含',
                        value: 'in'
                    }, {
                        label: '不为空',
                        value: 'is not null'
                    }, {
                        label: '为空',
                        value: 'is null'
                    }
                ],
                stringCompareSymbol: [
                    {
                        label: '模糊匹配',
                        value: 'like'
                    }, {
                        label: '包含',
                        value: 'in'
                    }, {
                        label: '不为空',
                        value: 'is not null'
                    }, {
                        label: '为空',
                        value: 'is null'
                    }
                ],
                relationList: [
                    {
                        label: '且',
                        name: 'and'
                    }, {
                        label: '或',
                        name: 'or'
                    }
                ],
                tab_panel: [
                    {
                        label: '基本设置',
                        name: 'setting_base'
                    }, {
                        label: '高级设置',
                        name: 'setting_senior'
                    }
                ],
                buttons: [
                    {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary'
                    }, {
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                ],
                status: false,

                dataDispose: "",//数据处理表达式输入框绑定的数据
                //分组统计
                tableData: [],
                inputType: [],
                rowData: '',
                columns: [],
                valueColumns: [],
                relationCondition: 'and',
                judgCondition: [],
                operateIcons: [
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteClassificationData}
                ]
            }
        },
        methods: {
            conditionChange(condition, i) {
                if ('is null' === condition || 'is not null' === condition) {
                    this.tableData[i].isInput = true;
                    this.tableData[i].value = "";
                } else {
                    this.tableData[i].isInput = false;
                }
            },
            getInputType(condition) {
                return 'is null' === condition || 'is not null' === condition;
            },
            saveRowData() {
                this.dataDispose = this.rowData.serviceOrgId;
            },

            selectDataType(val) {
                val.hasPrecision = this.checkDataType(val.outputTypeValue);
            },
            setColumns(inputColumn , vm) {
                if (!inputColumn) return;
                inputColumn.forEach(i => {
                    let column = {
                        label: i.columnZhName || i.columnName,
                        code: i.columnName,
                        value: i.columnName,
                        type: i.columnType,
                    };
                    let column2 = {
                        label: i.columnZhName || i.columnName,
                        code: "`" + i.columnName + "`",
                        value: "`" + i.columnName + "`",
                        type: i.columnType
                    };
                    vm.columns.push(column);
                    vm.valueColumns.push(column2);
                });

            },
            setCondition(pluginMeta , vm){
                vm.tableData = [];
                if (pluginMeta.judgCondition) {
                    let jCondition = pluginMeta.judgCondition;
                    jCondition.forEach((item, i) => {
                        vm.tableData.push({
                            field: item.field,
                            condition: item.condition,
                            value: item.value,
                            fieldType: item.fieldType,
                            // compareSymbol: this.getNumberOrStringCompareSymbol(_this.getDataType(item.field)),
                            isInput: vm.getInputType(item.condition),
                        })
                    });
                } else {
                    vm.add();
                }
            },
            init() { //初始化
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                vm.stepId = vm.sourceData.id;
                vm.loading = true;
                services.conditionFilterQueryData(vm.stepId , vm).then(res => {
                    if (res.data.code === 0) {
                        let result = res.data.data;
                        vm.setColumns(result.inputColumn , vm);
                        let pluginMeta = result.conditionFilterPlugin;
                        if (pluginMeta.relationCondition) {
                            vm.relationCondition = pluginMeta.relationCondition;
                        }
                        vm.setCondition(pluginMeta , vm);
                    }
                });
            },

            getNumberOrStringCompareSymbol(type) {
                let stringList = ["string", "text"];
                if (stringList.indexOf(type.toLowerCase()) !== -1) {
                    return this.stringCompareSymbol;
                } else {
                    return this.numberCompareSymbol;
                }
            },

            deleteClassificationData(row, index) {
                this.tableData.splice(index, 1);
            },
            saveFn() {
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let pluginMeta = {};
                pluginMeta["relationCondition"] = vm.relationCondition;
                let jCondotion = [];
                vm.tableData.forEach(item => {
                    let filedCondition = {};
                    filedCondition["field"] = item.field;
                    filedCondition["condition"] = item.condition;
                    filedCondition["value"] = item.value;
                    filedCondition["fieldType"] = vm.getDataType(item.field);
                    jCondotion.push(filedCondition);
                });
                pluginMeta["judgCondition"] = jCondotion;
                vm.loading = true;
                services.conditionSavePlugin(vm.stepId , pluginMeta , vm).then(res => {
                    if(res.data.code === 0){
                        vm.$message.success("保存成功")
                    }
                })
            },
            getDataType(field) {
                let _this = this;
                let type = 'String';
                for (let i = 0; i < _this.columns.length; i++) {
                    if (field === _this.columns[i].value) {
                        type = _this.columns[i].type;
                        break;
                    }
                }
                return type;
            },

            codeChange(field, i) {
                this.tableData[i].condition = "";
                // this.tableData[i].compareSymbol = this.getNumberOrStringCompareSymbol(this.getDataType(field));
            },

            checkDataType(val) {
                return this.dataTypes.indexOf(val) > -1;
            },
            previewFn() {
                this.$refs.preview.show(this.row, this.sourceData.id);
            },
            add() {
                this.tableData.push({
                    field: "",
                    condition: "",
                    value: "",
                    fieldType: "",
                    isInput: true,
                })
            },
        },
        created() {
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: false,
                isSampleExpr: false,
                isThread: false,
                isException: false,
                notTransition: false,
            };
            this.init();
        }
    }
</script>

<style scoped>

    .countingRadio {
        width: 100%;
        text-align: center;
    }

    .countingContent {
        margin-top: 10px;
    }

    .countingSelect {
        height: 5px;
    }

    .btn_new_model {
        float: right;
        margin-bottom: 5px;
        margin-top: -27px;
        margin-left: 3px;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
