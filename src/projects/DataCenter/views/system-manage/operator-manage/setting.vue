<template>
    <div class="height100" v-loading="operatorForm.loading">
        <el-form
                ref="form"
                size="small"
                :model="currentData"
                status-icon
                label-width="100px"
                class="demo-ruleForm"
                :hide-required-asterisk="operatorForm.state === 'detail'"
                :rules="rules"
        >
            <el-form-item label="算子名称" prop="name">
                <el-input ref="name" class="el-input--suffix" maxlength="100" :title="currentData.account"
                            v-model.trim="currentData.name"
                            :disabled="operatorForm.state === 'detail'"
                            placeholder="请输入算子名称(字母、数字、下划线，非数字开头，限100个字符以内)"></el-input>
            </el-form-item>

            <el-form-item label="分类" prop="classify">
                <ce-select-drop
                        class="expect-select"
                        ref="tree"
                        :props="defaultProps"
                        v-model="currentData.classify"
                        :filterNodeMethod="filterNode"
                        :data="classifyList"
                        filterable
                        check-leaf
                        check-strictly
                        :disabled="operatorForm.state === 'detail'"
                ></ce-select-drop>
            </el-form-item>
            <el-form-item label="返回类型" prop="returnType">
                <el-select v-model="currentData.returnType" size="mini">
                    <el-option v-for="opt in ruleTypeOptions"
                                :key="opt.label"
                                :value="opt.label"
                                :label="opt.label"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="函数分类" prop="funcClassify">
                <el-radio v-model="currentData.funcClassify" label="system">系统函数</el-radio>
                <el-radio v-model="currentData.funcClassify" label="custom">自定义函数</el-radio>
            </el-form-item>
            <el-form-item label="函数逻辑代码" prop="langType" v-if="currentData.funcClassify === 'custom'">
                <dg-select 
                        v-model="currentData.langType"
                        :data="langTypeOption"
                        filterable
                ></dg-select>
            </el-form-item>
            <el-form-item label="" prop="functionCode" v-if="currentData.funcClassify === 'custom'">
                <codeEditor ref="codeEditor" v-if="currentData.funcClassify === 'custom'" :langType="currentData.langType" :code="currentData.functionCode"></codeEditor>
            </el-form-item>
            <el-form-item label="类名" prop="className" v-if="currentData.funcClassify === 'custom'">
                <el-input v-model="currentData.className"></el-input>
            </el-form-item>
            <el-form-item label="方法名" prop="methodName" v-if="currentData.funcClassify === 'custom'">
                <el-input v-model="currentData.methodName"></el-input>
            </el-form-item>
            <!-- <el-form-item label="可用版本" prop="returnType">
                <el-select v-model="currentData.belong" size="mini">
                    <el-option v-for="opt in versionOptions"
                                :key="opt.value"
                                :value="opt.value"
                                :label="opt.label"
                    ></el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item label="表达式" prop="expression">
                <el-input v-model="currentData.expression"
                            type="textarea"
                            :rows="4"
                            @change="getExpression"
                            :title="currentData.expression"
                            :disabled="operatorForm.state === 'detail'"
                            :placeholder="operatorForm.state === 'detail' ? '': '请输入表达式'"></el-input>
            </el-form-item>
            <el-form-item label="算子说明" prop="memo">
                <el-input v-model="currentData.memo"
                            type="textarea"
                            :rows="4"
                            :title="currentData.memo"
                            :disabled="operatorForm.state === 'detail'"
                            :placeholder="operatorForm.state === 'detail' ? '': '请输入算子说明'"></el-input>
            </el-form-item>
            <el-form-item label="示例说明" prop="example">
                <el-input v-model="currentData.example"
                            type="textarea"
                            :rows="4"
                            :title="currentData.example"
                            :disabled="operatorForm.state === 'detail'"
                            :placeholder="operatorForm.state === 'detail' ? '': '请输入示例说明'"></el-input>
            </el-form-item>
            <el-form-item label="" prop="example" v-if="currentData.udfParameterVos.length === 0">
                <el-button type="primary" @click="handleAddChild">添加参数</el-button>
            </el-form-item>
        </el-form>
        <div v-for="(item, index) in currentData.udfParameterVos" :key="index">
            <el-form :inline="true" class="demo-ruleForm" ref="ruleForm" :model="item" :rules="paramRules">
                <el-form-item label="中文名称" prop="name">
                    <el-input v-model="item.name"></el-input>
                </el-form-item>
                <el-form-item label="英文名称" prop="code">
                    <el-input v-model="item.code" ></el-input>
                </el-form-item>
                <el-form-item label="数据类型" prop="dataType">
                    <el-select v-model="item.dataType" size="mini">
                        <el-option v-for="opt in ruleTypeOptions"
                                    :key="opt.label"
                                    :value="opt.label"
                                    :label="opt.label"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="测试值">
                    <el-input v-model="item.value" ></el-input>
                </el-form-item>
                <el-link :underline="false"
                        type="primary"
                        icon="el-icon-plus"
                        title="新增"
                        @click="handleAddChild"
                ></el-link>
                <el-popconfirm
                        size="mini"
                        title="确认删除?"
                        @confirm="handleDeleteChild(index)"
                >
                    <el-link
                            slot="reference"
                            :underline="false"
                            type="primary"
                            title="删除"
                            icon="el-icon-delete"
                    ></el-link>
                </el-popconfirm>
            </el-form>
        </div>
        <div>
            <dg-button v-footer @click="test">算子测试</dg-button>
        <dg-button v-footer v-if="operatorForm.state !== 'detail'" @click="close">取 消</dg-button>
        <dg-button v-footer type="primary" :disabled="operatorForm.loading" v-if="operatorForm.state === 'add'"
                    @click="addUserSubmit(true)">确 定</dg-button>
        <dg-button v-footer type="primary" :disabled="operatorForm.loading" v-else-if="operatorForm.state === 'edit'"
                    @click="addUserSubmit(false)">确 定</dg-button>
        <dg-button v-footer v-else @click="close">关 闭</dg-button>
        </div>
        
    </div>

</template>

<script>
import businessSpaceCommonApi from "dc-front-plugin/src/domain/business-define/businessSpaceCommonApi";
import moduleInfo from "dc-front-plugin/src/store/modules/module-info/module-info";
import dialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common";
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {servicesMixins} from "../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import codeEditor from "./codeEditor";


export default {
    name: "settings" ,
    mixins: [moduleInfo, commonMixins, servicesMixins, common, listMixins],
    props: {
        operatorForm : Object ,
        classifyList :Array,
        ruleTypeOptions : Array,
        rowData : Object,
    },
    components : {
        dialogFooterBtn,
        codeEditor,
    },
    data() {
        return {
            langTypeOption : [
                {
                    value : "java",
                    label : "java",
                }, {
                    value : "scala",
                    label : "scala",
                }
            ],//函数逻辑代码下拉框
            defaultProps: {
                value: 'id',
                label: 'name',
                children: 'children'
            },
            rules: {
                example: [
                    {required: true, message: "示例不能为空", trigger: ["blur", "change"]},
                    {
                        validator: this.checkedCommon,
                        trigger: "blur",
                        message: "请输入字母、数字、下划线(非数字开头)"
                    },
                ],
                name: [
                    {required: true, message: "算子名称不能为空", trigger: ["blur", "change"]},
                    {
                        validator: this.checkedCommon,
                        trigger: "blur",
                        reg: /^[a-zA-Z0-9_（）\u4e00-\u9fa5]+$/,
                        message: "请输入中文、字母、数字、下划线"
                    }
                ],
                classify: [
                    {required: true, message: "分类不能为空", trigger: 'change'}
                ],
                className: [
                    {required: true, message: "类名不能为空", trigger: 'change'}
                ],
                methodName: [
                    {required: true, message: "方法名不能为空", trigger: 'change'}
                ],
                expression: [
                    {required: true, trigger: 'change', message: "表达式不能为空"}
                ],
                returnType: [
                    {required: true, trigger: 'change', message: "返回类型不能为空"}
                ],
                memo: [
                    {
                        required: true, trigger: 'change', message: "算子说明不能为空"
                    }
                ],
                belong : [
                    {required : true , trigger: 'change', message: "可用版本不能为空"}
                ],
            },
            paramRules : {
                name : [
                    {required : true , trigger: "change", message: "中文名称不能为空"}
                ],
                code : [
                    {required : true , trigger: "change", message: "英文名称不能为空"}
                ],
                dataType : [
                    {required : true , trigger: 'change', message: "数据类型不能为空"}
                ],
            },
            versionOptions : [
                {label : "基础版",value : "basic"},
                {label : "标准版",value : "standard"},
            ],
            //算子新增弹窗form数据
            currentData: {
                id: "",
                example: "",
                name: "",
                classify: "",
                expression: "",
                returnType: '',
                memo: "",
                status: "",
                belong : "basic",
                funcClassify : "system",
                functionCode : "",
                langType : "java",
                className : "",
                methodName : "",
                //用户信息新增弹窗复选框绑定值
                roleVal: [],
                password: "",
                udfParameterVos: [
                    {
                        name: '',
                        code: '',
                        dataType: '',
                        value : ""
                    }
                ]
            },
        };
    },
    methods : {
        filterNode: treeMethods.methods.filterNode,
        getExpression() {
            const vm = this , {operatorServices, operatorMock} = this;
            let services = vm.getServices(operatorServices, operatorMock);
            vm.currentData.udfParameterVos = [];
            services.expressionParser(vm.currentData.expression).then(res => {
                if (res.data.status === 0) {
                    res.data.data.forEach(item => {
                        vm.currentData.udfParameterVos.push({
                            name: '',
                            code: item,
                            dataType: '',
                            value : ''
                        });

                    });
                }
            });
        },
        //提交添加
        async addUserSubmit(isNew) {
            const vm = this;
            let {currentData, operatorForm, operatorServices, operatorMock} = this;
            let services = vm.getServices(operatorServices, operatorMock);
            let data = {
                id: currentData.id,
                example: currentData.example,
                name: currentData.name,
                classify: currentData.classify,
                expression: currentData.expression,
                belong : currentData.belong,
                memo: currentData.memo,
                returnType: currentData.returnType,
                udfParameterVos: currentData.udfParameterVos,

                funcClassify : currentData.funcClassify,
                functionCode : currentData.funcClassify === 'custom'? vm.$refs.codeEditor.statement : "",
                langType : currentData.funcClassify === 'custom'? currentData.langType : "",
                className : currentData.funcClassify === 'custom'? currentData.className : "",
                methodName : currentData.funcClassify === 'custom'? currentData.methodName : "",
                //editor: vm.userInfo.id
            };
            let nameCheck = false;
            let {settings, searchVal} = this;
            await vm.$refs.form.validateField("name", valid => {
                nameCheck = valid === "";
                if (valid !== "") vm.$refs.name.focus();
            });
            let message = '';
            for(let i = 0;i<vm.currentData.udfParameterVos.length ; i++){
                if(vm.currentData.udfParameterVos[i].name === ''){
                     message = "中文名称不能为空！";
                     break
                }
                else if(vm.currentData.udfParameterVos[i].dataType === ''){
                    message = "数据类型不能为空！";
                    break
                }
                else if(vm.currentData.udfParameterVos[i].code === ''){
                    message = "英文名称不能为空！";
                    break
                }
            }
            if(message){
                return vm.$message.warning(message);
            }
            vm.validate(valid => {
                if (valid) {
                    operatorForm.loading = true;
                    services.createUdfOperator(data, operatorForm).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success(isNew ? "新建成功！" : "修改成功！");
                            this.$emit('submit');
                        }
                    })
                }
            })
        },
        close() {
            this.$emit('close');
        },
        /**
         * 算子测试
         */
        test() {
            const vm = this;
            let {currentData, operatorForm, operatorServices, operatorMock} = this;
            let services = vm.getServices(operatorServices, operatorMock);
            let data = {
                id: currentData.id,
                example: currentData.example,
                name: currentData.name,
                classify: currentData.classify,
                expression: currentData.expression,
                belong : currentData.belong,
                memo: currentData.memo,
                returnType: currentData.returnType,
                udfParameterVos: currentData.udfParameterVos,

                funcClassify : currentData.funcClassify,
                functionCode : currentData.funcClassify === 'custom'? vm.$refs.codeEditor.statement : "",
                langType : currentData.funcClassify === 'custom'? currentData.langType : "",
                className : currentData.funcClassify === 'custom'? currentData.className : "",
                methodName : currentData.funcClassify === 'custom'? currentData.methodName : "",
                //editor: vm.userInfo.id
            };
            operatorForm.loading = true;
            services.testUdfFunction(data, operatorForm).then(res => {
                if (res.data.status === 0) {
                    let resultMsg = currentData.expression;
                    currentData.udfParameterVos.forEach(item =>{
                        let odVal = ':'+item.code;
                        resultMsg = resultMsg.replace(odVal, item.value);
                    })
                    let result = JSON.parse(res.data.data);
                    resultMsg = resultMsg + '=' + result.data[0].value;
                    this.$confirm(resultMsg, '测试结果', {
                        // confirmButtonText: '确定',
                        showConfirmButton :false,
                        cancelButtonText: '确定',
                        type: 'success'
                    }).then(() => {

                    }).catch(() => {

                    });
                }
            })
        },
        handleAddChild() {
            this.currentData.udfParameterVos.push({
                name: '',
                code: "",
                dataType: "",
                value : ""

            });
        },
        handleDeleteChild(index) {
            this.currentData.udfParameterVos.splice(index , 1);
        },
        submit() {
            this.$emit('submit', this.tableData);
            this.$emit('close');
        },
        //校验
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
    },
    created() {
        if (this.rowData) this.currentData = this.rowData;
    }
}
</script>

<style scoped lang="less" src="./operator-manage.less"></style>

