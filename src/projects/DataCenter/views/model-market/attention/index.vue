<template>
    <div  class="ce-list">
        <div class="ce-list_filter" >
            <div>
                <el-radio-group v-model="activeN" @change="typeChange">
                    <el-radio-button v-for="(item, index) in btnOptions" :key="index" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </div>
            <div>
                <dg-select v-model="selectType" :data="selectData" @change="changePage()" class='wid20'> </dg-select>
                <el-input
                        class="ce-list_input"
                        size="mini"
                        placeholder="请输入模型名称查询"
                        v-model.trim="filterTxt"
                        v-input-limit:trim
                        @keyup.enter.native="changePage(1)"
                >
                    <i
                            class="el-icon-search el-input__icon poi"
                            slot="suffix"
                            @click="changePage(1)">
                    </i>
                </el-input>
            </div>
        </div>
        <div class="ce-list_table">
            <div class="height100">
                <common-table
                        v-show="tableData.length"
                        :data="tableData"
                        :columns="tableColumns"
                        :border="false"
                        :pagination-props="paginationProps"
                        :pagination-total="total"
                        noneImg
                        :max-height="tableBodyH"
                        @change-current="changePage($event) "
                        @change-size="changeSize"
                        @selection-change="selectionChange"
                >
                    <template slot="name" slot-scope="{row , $index}">
                        <div class="source poi" @click="detailsLook(row)">{{ row.name }}</div>
                    </template>
                    <template slot="status" slot-scope="{row}">
                        <span class="status" :class="{'upStatus':row.state === '0'}">{{row.state === '1'? '已下架' : '已上架'}}</span>
                    </template>
                    <template slot="operate" slot-scope="{row}">
                        <span class="r-c-action" v-for="(opt , inx) in operator" :key="inx"
                              v-if="opt.condition(row , rights)">
                            <dg-button type="text"  :title="opt.label" v-html="opt.label"
                                       @click="opt.clickFn(row)">{{opt.label}}</dg-button>
                        </span>
                    </template>
                </common-table>
                <no-data v-if="tableData.length === 0" :src="emptyImg" :title="emptyTxt" top="5%">
                </no-data>
            </div>
        </div>
        <evaluation ref="evaluation" @save="saveEvaluate"></evaluation>
        <!--仪表盘详情页面-->
        <VisualizationEdit ref="visual_panel" v-if="showEditPanel" @closePanel="closeEditPanel" :isCase="true" />
        <!--主题门户详情页面-->
        <edit-panel v-if="showHomehuEditPanel" ref="protal_panel" @closePanel="closeHomehuEditPanel" :isCase="true"></edit-panel>
    </div>
</template>

<script>
    import {common} from "@/api/commonMethods/common";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {coustTableH} from "@/api/commonMethods/count-table-h";
    import NoData from "@/components/no-data";
    import {mapGetters} from "vuex";
    import emptyImg from "@/assets/images/common/empty-img.png";
    import {servicesMixins} from "../service-mixins/service-mixins";
    import evaluation from "./evaluation"
    import VisualizationEdit from '@/projects/DataCenter/views/visualizing/edit-panel/VisualizationEdit';
    import EditPanel from "@/projects/DataCenter/views/homeHu/edit-panel"
    import marketUse from "@store/modules/market/market-constant";
    export default {
        name: "publishedList",
        mixins: [ common ,coustTableH, servicesMixins, commonMixins , marketUse],
        components: {NoData , evaluation, VisualizationEdit, EditPanel},
        computed: {
            ...mapGetters(["userRight"]),
            rights() {
                let rights = [];
                if (this.userRight) {
                    this.userRight.forEach(r => {
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            },
            btnOptions(){
                return this.m_market_get_model_types();
            },
        },
        data() {
            return {
                activeN: "",
                filterTxt: "",
                shareData: [],
                selectData:[
                    { label :'全部', value:''},
                    { label :'已上架', value: "0"},
                    { label :'已下架', value: "1"}
                ],
                selectType:'',
                pageSizes: [],
                total:2,
                paginationProps: {
                    currentPage: 1,
                    pageSizes: [10, 20],
                    pageSize: 10,
                },
                emptyImg,
                emptyTxt: "暂未关注相关模型，请进入模型超市中，对感兴趣的模型进行关注。",
                tableData:[],
                //操作栏
                operator: [
                    {
                        label: '查看',
                        clickFn: this.onLook,
                        condition: (row, right) => true,
                        //condition: (row, right) => right.indexOf($right["roleManagementUpdataRole"]) > -1,
                    }, {
                        label: '取消关注',
                        clickFn: this.unfollow,
                        condition: (row, right) => true,
                    }, {
                        label: '评价',
                        clickFn: this.onEvaluate,
                        condition: (row, right) => row.modelEvaluation.operate_content === undefined,
                    }, {
                        label: '查看评价',
                        condition: (row, right) => row.modelEvaluation.operate_content !== undefined,
                        clickFn: this.onEvaluate,
                    }
                ],
                tableColumns: [
                    // {type: 'selection' , width: 50, reserveSelection: true},
                    {
                        prop: "name",
                        label: "模型名称",
                        align: "left"
                    },
                    {
                        prop: "object_type_name",
                        label: "对象类型",
                        align: "center"
                    },  {
                        prop: "application_type_name",
                        label: "应用类型",
                        align: "center"
                    },{
                        prop: "police_type_name",
                        label: "警种类型",
                        align: "center"
                    },{
                        prop: "label",
                        label: "标签",
                        align: "center"
                    },{
                        prop: "status",
                        label: "上架状态",
                        align: "center"
                    }, {
                        prop: "publishTime",
                        label: "发布时间",
                        width: "200",
                        align: "center"
                    }, {
                        prop: "operate",
                        label: "操作",
                        width: "230"
                    }
                ],
                checkInfo:[],
                showEditPanel : false,
                showHomehuEditPanel:false,
            }
        },
        methods:{
            typeChange(){
                this.changePage(1);
            },
            changePage(index=1){
                const vm = this, {manageServices, manageMock,settings} = this;
                let services = vm.getServices(manageServices, manageMock);
                vm.tableData = [];
                vm.paginationProps.currentPage = index;
                let params = {
                    "modelType" : this.activeN,
                    "modelState" : vm.selectType,
                    "modelName" : vm.filterTxt,
                    "pageNum" : index,
                    "pageSize" : vm.paginationProps.pageSize,
                };
                settings.loading = true;
                services.myFocusModelPage(params, settings).then(res =>{
                    if (res.data.status === 0 && res.data.data) {
                        vm.tableData = res.data.data.dataList.map((item) =>{
                            let label = item.modelLabels.map((item) =>{
                                            return item.labelName;
                                        }).join('、');
                            item.object_type_name = item.modelAttachedInformation.object_type_name;
                            item.police_type_name = item.modelAttachedInformation.police_type_name;
                            item.application_type_name = item.modelAttachedInformation.application_type_name;
                            item.label = label;
                            return item;
                        });
                        vm.total = res.data.data.totalCount;
                    }
                })
                // vm.$nextTick(()=>{
                //     vm.tableData = [
                //         { name:'123', targetType:'人员',targetType2:'统计分析类',status:0,time:'2019-03-02 11:12:23',},
                //         { name:'测试', targetType:'人员',targetType2:'统计分析类',status:1,time:'2019-03-02 11:12:23',}
                //     ];
                // })
            },
            //取消关注
            unfollow(row){
                const vm = this;
                if(row){
                    vm.confirm( '取消关注' ,`模型取消关注后从已关注的模型列表中移除。确认要取消关注当前模型吗？`, ()=>{
                        const vm = this, {manageServices, manageMock ,settings} = this;
                        let services = vm.getServices(manageServices, manageMock);
                        let data = {
                            focusType:  '1' ,
                            id: row.id,
                        }
                        services.focusOrUnFocusModel(data).then(res=>{
                            if (res.data.status === 0) {
                                this.changePage(1);
                            }
                        })
                    })
                }
                else{
                    if(!this.checkInfo.length){
                        this.$message.warning("请先选择要取消关注的模型！");
                        return
                    }
                }
            },
            //评价
            onEvaluate(row){
                this.$refs.evaluation.show(row);
            },
            saveEvaluate(id, form){
                this.changePage(1);
            },
            selectionChange(value) {
                this.checkInfo = value;
            },

            changeSize(val) {
                this.paginationProps.pageSize = val;
                this.changePage(1);
            },
            //查看模型市场详情
            detailsLook(row){
                let routeData = this.$router.resolve({path: '/market/modelMarket', query: {id: row.id}});
                window.open(routeData.href, '_blank');
            },
            //查看ai模型
            lookAI(row){
                const vm = this;
                row.transId = row.transId;
                row.modelName = row.name;
                let layer = this.$dgLayer({
                    title: "查看模型",
                    content: require("@/projects/DataCenter/views/AIModeling/dialog/new-model/index.vue"),
                    maxmin: false,
                    props: {
                        isLook:true,
                    },
                    area: ["100%", "100%"],
                    on: {
                        close() {
                            layer.close(layer.dialogIndex);
                        }
                    },
                });
                layer.$children[0].show(row);
            },
            //查看模型
            lookModel(row){
                const vm = this;
                let data = {
                    transId : row.transId,
                    modelName : row.name,
                    caseId:row.transId,
                    isNew : false,
                }
                let layer = this.$dgLayer({
                    content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                    skin: "new-model",
                    maxmin: true,
                    noneBtnField: true,
                    props: {
                        dirId: "-1",
                        isCase : true,
                        isLook :true,
                    },
                    area: ["100%", "100%"],
                    on: {
                        close() {
                            layer.close(layer.dialogIndex);
                        }
                    },
                });
                layer.$children[0].addTab(data, false);
            },
            //查看仪表盘
            lookDash(row){
                this.showEditPanel = true;
                this.$nextTick(()=>{
                    this.$refs.visual_panel.getRouterData( row.transId ,'' , '' ,'');
                })
            },
            closeEditPanel(groupId , filterTxt) {
                this.showEditPanel = false;
            },
            closeHomehuEditPanel() {
                this.showHomehuEditPanel = false;
            },
            //查看主题门户
            openHomehuPanel( row ) {
                this.showHomehuEditPanel = true;
                row.id = row.transId;
                this.$nextTick(()=>{
                    this.$refs.protal_panel.panelInit( "" , row.transId ,row , "");
                })
            },
            //查看
            onLook(row){
                if(this.activeN === 'TRANS'){
                    this.lookModel(row)
                }
                else if(this.activeN === 'DASHBOARDS'){
                    this.lookDash(row)
                }
                else if(this.activeN === 'PORTAL'){
                    this.openHomehuPanel(row);
                }
                else{
                    this.lookAI(row);
                }
            },
        },
        created(){
            this.activeN = this.btnOptions.length ? this.btnOptions[0].value : "";
            this.changePage(1);
        }
    }
</script>

<style scoped lang="less">
    .wid20 {
        width: 20%;
    }
    .red{
        color:red;
    }
    .alertClass{
        width:700px;
    }
    .ce-list {
        height: calc(100% - 12px);
        background: #fff;
        padding:10px;
        .r-c-action:last-child::after{
            width:0;
        }
        .source{
            color:#0088FF;
        }
        &_filter {
            height: 32px;
            text-align: right;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
        }

        &_input {
            width: 270px;
            margin-left: 10px;
        }

        &_table {
            height: calc(100% - 20px - 2rem);
            .status{
                padding: 0px 10px;
                border: 1px solid #dcdcdc;
                border-radius: 8px;
                color: rgba(0,0,0,0.45);
            }
            .upStatus{
                border: 1px solid rgba(82,196,26,0.60);
                color: #52C41A;
            }
        }

        &_com {
            width: 320px;
            margin-left: 10px;
        }

        &_preSel {
            width: 100px;
        }
    }
</style>
