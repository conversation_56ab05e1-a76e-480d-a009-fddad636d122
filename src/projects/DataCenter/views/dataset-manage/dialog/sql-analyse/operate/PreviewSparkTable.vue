<template>
    <div class="main">
        <div class="dataSetTreeClass">
            <!--<div class="pb10">
                <span class="pr10">{{loadTxt}}</span>
                <dg-switch v-model="loadFinished" :title="loadFinished ? active_txt : inactive_txt"
                           :active-text="active_txt"
                           :inactive-text="inactive_txt"
                           disabled></dg-switch>
            </div>-->

<!--            <el-input placeholder="请输入名称" v-model.trim="filterText" size="mini"></el-input>-->
        </div>
        <div class="dataSetTableClass">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :pagination="false"
                    height="100%"
                    v-loading="settings.loading"
            >

            </common-table>
            <!--<dg-tree
                    v-loading="settings.loading"
                    node-key="id"
                    class="tree"
                    :data="data"
                    :props="defaultProps"
                    :filter-node-method="filterNode"
                    ref="tree"
                    default-expand-all
            >
                 <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                        <span class="node-label"
                              :title="data.label"
                              :class="{
                            'el-icon-folder' : !node.expanded && node.level <= 2 ,
                            'el-icon-folder-opened' : node.expanded && node.level <= 2,
                            'el-icon-document' : data.fileIco
                          }"
                        >{{ data.label }}</span>
                 </span>
            </dg-tree>-->
        </div>
    </div>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../../../service-mixins/service-mixins";
    // import {treeMethods} from "@/api/treeNameCheck/treeName"

    export default {
        name: "PreviewSparkTable",
        mixins: [commonMixins, servicesMixins],
        watch: {
            filterText(val) {
                // this.$refs.tree.filter(val);
                this.filterList(val);
            }
        },
        props:{
            schemaId:String,
        },
        data() {
            return {
                data: [],
                filterText: "",
                loadFinished: false,
                loadTxt: "是否加载完成:",
                active_txt: '是',
                inactive_txt: '否',
                tableData: [],
                storeData : [],
                tHeadData: [
                    /*{
                        prop: "globalCode",
                        label: "全局名称" ,
                        minWidth : "120" ,
                        align : "center"
                    },*/
                    {
                        prop : "label" ,
                        label : "中文名称",
                        minWidth : "120" ,
                        align : "center"
                    },{
                        prop : "code" ,
                        label : "英文名称",
                        minWidth : "120" ,
                        align : "center"
                    }
                    /*,{
                        prop : "tableType" ,
                        label : "表类型名称",
                        minWidth : "120" ,
                        align : "center" ,
                        type : "status-tpl",
                        'cmp-props-options' : [
                            {name : "物理表" ,status : "ENTITY"},
                            {name : "文件" ,status : "FILE"},
                            {name : "逻辑表" ,status : "LOGIC"},
                        ]
                    }, {
                        prop: "fast",
                        label: "是否极速表",
                        type: "status-tpl",
                        align : "center",
                        "cmp-props-options": [
                            { name: "是", status: true },
                            { name: "否", status: false }
                        ]
                    },{
                        prop : "success" ,
                        label : "是否加载完成" ,
                        type: "status-tpl",
                        align : "center",
                        "cmp-props-options": [
                            { name: "是", status: true },
                            { name: "否", status: false }
                        ]
                    }*/
                ]
            }
        },
        methods: {
            init() {
                // this.getSparkTable();
            },
            setChildren(data, key, label, suf) {
                let item, children = [];
                data[key].forEach(li => {
                    if (li) {
                        children.push({
                            label: li,
                            id: li,
                            fileIco: true
                        })
                    }
                });
                item = {
                    label: label,
                    id: key + '_' + suf,
                    children: children
                };
                return item;
            },
            setList(data, suf) {
                const vm = this;
                let list = [];
                for (let key in data) {
                    let item;
                    if (key === 'ENTITY') {
                        item = vm.setChildren(data, key, '物理表', suf);
                    } else if (key === 'FILE') {
                        item = vm.setChildren(data, key, '文件', suf);
                    } else if (key === 'LOGIC') {
                        item = vm.setChildren(data, key, '逻辑表', suf);
                    }
                    list.push(item);
                }
                return list;
            },
            filterList(val = ""){
                const vm = this;let word = val.toLowerCase();
                if(word === ""){
                    vm.tableData = vm.storeData
                }else {
                    vm.tableData = vm.storeData.filter(item => {

                        return item.globalCode && item.globalCode.toLowerCase().indexOf(word) > -1 ||
                            item.label && item.label.toLowerCase().indexOf(word) > -1 ||
                            item.code && item.code.toLowerCase().indexOf(word) > -1
                    })
                }

            },
            getSparkTable(val) {
                const vm = this, {datasetServices, datasetMock , settings} = this;
                let services = vm.getServices(datasetServices, datasetMock);
                settings.loading = true;
                services.registered(settings,val).then(res => {
                    if (res.data.status === 0) {
                        let data = res.data.data;
                        data.forEach(item =>{
                            this.tableData.push({
                                label:item.name ? item.name : item.code,
                                code:item.code
                            })
                        })
                       /* let data = res.data.data;
                        vm.loadFinished = data.registerFinish;
                        vm.storeData = data.registerInfos;
                        vm.filterList();*/
                        /*let success_list, error_list;
                        success_list = vm.setList(data.successList, 's');
                        error_list = vm.setList(data.errorList, 'e');
                        vm.data = [
                            {
                                label: "注册成功",
                                id: "success",
                                children: success_list
                            }, {
                                label: "注册失败",
                                id: "error",
                                children: error_list
                            },
                        ];*/
                    }
                })
            },
            initTree() {}
        },
        created() {
            this.getSparkTable(this.schemaId);
        }
    }
</script>

<style scoped>
    .dataSetTableClass {
        background: #fff;
        margin-top: 15px;
        height: calc(100% - 80px);
        overflow: auto;
    }

    .main {
        position: relative;
        height: calc(100vh - 105px);
    }


</style>