<template>
    <div class="visualTable">
        <div class="ce-form_top">
            <el-form :inline="true" size="mini" @submit.native.prevent>
                <el-form-item>
                    <el-input
                            v-model.trim="filterText"
                            size="small"
                            placeholder="请输入中文名称/英文名称"
                            clearable
                            style="width:200px"
                            @keyup.enter.native="searchData"
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="searchData">查询</el-button>
                </el-form-item>
                <el-button type="primary" size="mini" @click="addVisualPanel" v-if="rights.indexOf(addModelRight) > -1" class="btn_right">{{btn_add_txt}}
                </el-button>
            </el-form>
        </div>
        <el-table
                :data="tableData"
                border
                tooltip-effect="light"
                class="table_content"
                size="mini"
                :stripe="is_stripe"
                cell-class-name="ce-ellipsis"
                v-loading="loading"
        >
            <el-table-column
                    v-for="(item,index) in tHeadData"
                    :key="index"
                    :prop="item.prop"
                    :width="item.width"
                    :label="item.label"
                    :align="item.align"
                    :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column label="操作" width="180" align="center">
                <template slot-scope="scope">
                    <i
                            v-for="(item,index) in operateIcons"
                            :key="index"
                            class="icon ce_link pl5 pr5 f18"
                            @click="item.clickFn(scope.$index, scope.row)"
                            v-if="item.show(rights)"
                            :title="item.tip"
                            v-html="item.icon"
                    ></i>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                class="mt5 mb5 tc"
                background
                @current-change="changePage"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="total, prev, pager, next, jumper"
                :total="total"
        ></el-pagination>
        <el-dialog :title="title"
                   :visible.sync="dialogVisible"
                   width="30%"
                   custom-class="ce-prompt"
                   @closed="closeDialog"
        >
            <NewModelName v-if="reNew" ref="newName" :treeNode="treeNode" :label="label"/>
            <div slot="footer">
                <el-button @click="closeNewMode" size="mini">取消</el-button>
                <el-button @click="newModeEvent" size="mini" type="primary">确定</el-button>
            </div>
        </el-dialog>
        <ModelMove ref="move" @update="update"></ModelMove>
    </div>
</template>

<script>
    import NewModelName from './NewModelName'
    import ModelMove from '../dialog/OpenModel'
    import {mapGetters} from "vuex"
    import $right from "@/assets/data/right-data/right-data"

    export default {
        name: "BeatLibraryModelingTable",
        components: {NewModelName,ModelMove},
        computed : {
            ...mapGetters(["userRight"]),
            rights (){
                let rights = [];
                if(this.userRight){
                    this.userRight.forEach(r =>{
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            }
        },
        data() {
            return {
                reNew : false ,
                label:{},
                loading: false,
                dirData: {},
                title: "",
                dialogVisible: false,
                filterText: '',
                btn_add_txt: '新建数据模型',
                tHeadData: [
                    {
                        prop: "name",
                        label: "中文名称",
                        width: "",
                        align: "left"
                    },
                    {
                        prop: "code",
                        label: "英文名称",
                        width: "",
                        align: "left"
                    },
                    {
                        prop: "creator",
                        label: "创建人",
                        width: "",
                        align: "center"
                    },
                    /*{
                        prop: "editor",
                        label: "最后修改人",
                        width: "",
                        align: "center"
                    },*/ {
                        prop: "time",
                        label: "最后编辑时间",
                        width: "190",
                        align: "center"
                    }
                ],
                tableData: [],
                is_stripe: true,
                addModelRight : $right["SQLModelingSaveModel"] ,
                operateIcons: [
                    {icon: "&#xe6c0;", tip: "编辑", clickFn: this.editPanel , show : (right) => right.indexOf($right["SQLModelingSqlEditPage"]) > -1},
                    {icon: "&#xe607;", tip: "重命名", clickFn: this.renamePanel , show : (right) => right.indexOf($right["SQLModelingUpdateSqlModelName"]) > -1},
                    {icon: "&#xe6bf;", tip: "移动", clickFn: this.moveTo , show : (right) => right.indexOf($right["SQLModelingMoveModel"]) > -1},
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.deletePanel , show : (right) => right.indexOf($right["SQLModelingDelSqlObj"]) > -1},
                ],
                currentPage: 1,
                total: 0,
                pageSize: 10,
                treeNode: {},
                rowData: {},
            }
        },
        methods: {

            update(){
                this.changePage(1);
            },

            moveTo(index,row){
                this.$refs.move.open(row,"sqlModel","move");
            },


            newModeEvent() {
                if(this.title === '新增子目录'){
                    this.$refs.newName.$refs.name.focus();
                    this.$refs.newName.$refs.name.blur();
                }else {
                    this.$refs.newName.validate();
                }
                let newName = this.$refs.newName.getFormData();

                if (newName.chName === "") {
                    return;
                }
                if (newName.chName.length >300) {
                    this.$message.warning("中文名长度不能超过300！");
                    return;
                }

                //新建
                if (this.title === "新建跨库模型") {
                    if (newName.engName === "") {
                        return;
                    }
                    if (newName.engName.length >300) {
                        this.$message.warning("英文名长度不能超过300！");
                        return;
                    }
                    //保存
                    let data ={
                        chName:newName.chName,
                        engName:newName.engName,
                        parentId:newName.savePath,
                    };
                    this.$axios.post("/manage/sql/saveModel",data).then(res => {
                        if (res.data.status === 0) {
                            if (res.data.data.length === 32) {
                                this.$message.success("新建成功！");
                                this.$emit('showPanel', '新增', {name: newName.chName, id: res.data.data});
                                this.dialogVisible = false;
                            } else {
                                this.$message.warning(res.data.data);
                            }
                        }
                    }).catch(err => {
                        console.log(err);
                        this.$message.error("服务器异常，请联系管理员！");
                    })

                } else if (this.title === "模型重命名") {
                    //重命名
                    if (newName.engName === "") {
                        return;
                    }
                    if (newName.engName.length >300) {
                        this.$message.warning("英文名长度不能超过300！");
                        return;
                    }
                    if(this.rowData.name === newName.chName && this.rowData.code === newName.engName){
                        this.$message.success("保存成功");
                        this.dialogVisible = false;
                        return;
                    }
                    this.$axios.get("/manage/sql/updateSqlModelName?name=" + newName.chName + "&engName=" + newName.engName + "&id=" + this.rowData.id).then(res => {
                        if (res.data.status === 0) {
                            if (res.data.data === "修改成功") {
                                this.$message.success(res.data.data);
                                this.dialogVisible = false;
                                this.changePage(this.currentPage);
                            } else {
                                this.$message.warning(res.data.data);
                            }
                        }
                    })

                } else if (this.title === "新增子目录") {
                    //新增子目录
                    let data = {
                        parentId : this.dirData.id,
                        nodeName : newName.chName,
                        // nodeCode : newName.engName
                    };

                    this.$axios.post("/manage/sql/addSqlTreeNode",data).then(res => {
                        console.log(res);
                        if (res.data.status === 0) {
                            if (res.data.data.length === 32) {
                                this.$message.success("保存成功！");
                                this.$emit("init");
                                this.dialogVisible = false;
                            } else {
                                this.$message.info(res.data.data);
                            }

                        } else {
                            this.$message.error("保存失败！");
                        }
                    }).catch(err => {
                        console.log(err);
                        this.$message.error("服务器异常，请联系管理员！");
                    });
                } else {
                    //修改子目录
                    let cName = newName.chName;
                    // let eName = escape(newName.engName);
                    let data = {
                      id:this.dirData.id,
                      newName: cName,
                    };
                    this.$axios.post("/manage/sql/changeSqlTreeNodeName",data).then(res => {
                        console.log(res);
                        if (res.data.status === 0) {
                            if (res.data.data.success === true) {
                                this.$message.success("修改成功！");
                                this.dialogVisible = false;
                                this.$emit("updateDirName", newName);
                            } else {
                                this.$message.info(res.data.data.errorMessages[0]);
                            }
                        } else {
                            this.$message.success("修改失败！");
                        }
                    }).catch(err => {
                        console.log(err);
                        this.$message.error("服务器异常，请联系管理员！");
                    })
                }
            },
            closeNewMode() {
                this.dialogVisible = false;
            },
            initTable(treeNodeData) {
                this.treeNode = treeNodeData;
                this.changePage(1);
            },
            searchData() {
                const _this = this;
                // let _reg = /[\]@/'\"$%&^*{}<>\\\\[:\;]+/;
                // if(_reg.test(this.filterText)){
                //     _this.$message.warning("不允许输入特殊字符！");
                //     // _this.filterText = _this.filterText.substring(0,_this.filterText.length-1);
                //     return;
                // }

                this.changePage(1);
            },
            addVisualPanel() {
                this.label = {
                    engLabel: "英文名:",
                    chLabel: "中文名:",
                };
                this.title = "新建跨库模型";
                this.dialogVisible = true;
                this.reNew = true;
                this.$nextTick(() => {
                    this.$refs.newName.showSelect("model");
                })
            },
            changePage(index) {
                this.loading = true;
                const _this = this;
                this.currentPage = index;
                let treeId = this.treeNode.id;
                if (this.treeNode.parentId === "-1" || this.treeNode.parentId === undefined) {
                    treeId = "";
                }
                let data = { // 查询的条件写这里 , 条件
                    pageSize: _this.pageSize,
                    pageNum: index,
                    sortField: "name",
                    sortOrder: "desc",
                    condition: _this.filterText,
                    parentId: treeId,
                };
                this.$axios.post("/manage/sql/queryModel", data).then(res => {
                    if (res.data.status === 0) {
                        _this.tableData = [];
                        if (res.data.data.totalCount === 0) {
                            setTimeout(this.loadingEnd, 300);
                        } else {
                            res.data.data.dataList.forEach(item => {
                                let data = {
                                    id: item.id,
                                    code: item.code,
                                    name: item.name,
                                    creator: '',
                                    editor: '',
                                    time: item.operate_time,
                                    data: [
                                        {
                                            width: 300,
                                            height: 180,
                                            top: 200,
                                            left: 200,
                                            minW: 120,
                                            minH: 120,
                                            zIndex: 'auto', //层级后期完善
                                            label: '查询',
                                            id: '123'
                                        }
                                    ]
                                };
                                _this.tableData.push(data);
                            });
                        }
                        _this.total = res.data.data.totalCount;
                        this.loading = false;
                    } else {
                        this.$message.error("获取模型失败！");
                    }

                })
            },
            loadingEnd() {
                this.loading = false;
            },
            //编辑
            editPanel(index, row) {
                this.label = {
                    engLabel: "模型英文名:",
                    chLabel: "模型中文名:",
                };
                this.$emit('showPanel', '编辑', row);
            },
            //重命名
            renamePanel(index, row) {
                this.rowData = row;
                this.title = "模型重命名";
                this.label = {
                    engLabel: "模型英文名:",
                    chLabel: "模型中文名:",
                };
                this.dialogVisible = true;
                this.reNew = true;
                this.$nextTick(() => {
                    this.$refs.newName.setName(row,"model");
                })
            },
            deletePanel(index, row) {
                this.$confirm('确定删除该表吗?', '删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // console.log(row);
                    this.$axios.get("/manage/sql/delSqlObj?id=" + row.id).then(res => {
                        if (res.data.status === 0) {
                            if(res.data.data !== undefined){
                                this.$message.warning(res.data.data);
                            }else{
                                this.$message.success("删除成功!");
                                // this.tableData.splice(index, 1);
                                // this.total = this.tableData.length;
                                this.changePage(1);
                            }
                        }
                    }).catch(err => {
                        console.log(err);
                        this.$message.error("服务器异常，请联系管理员！");
                    })

                }).catch(() => {

                })
            },
            closeDialog() {
                this.$refs.newName.clearName();
                this.reNew = false;
            },
            showDirNewName(val, data) {
                // alert(val);
                this.label = {
                    engLabel: "目录英文名:",
                    chLabel: "目录中文名:",
                };
                this.dirData = data;
                this.dialogVisible = true;
                this.reNew = true;
                this.$nextTick(() => {
                    if (data.parentId === "-1") {

                        this.title = "新增子目录";
                        this.$refs.newName.setSelect();
                    } else {
                        this.title = "修改子目录";
                        this.$refs.newName.setName(data);
                    }
                })


            },

        },
        created() {
            this.changePage(1);
        }
    }
</script>

<style scoped>

</style>
<style>
    .ce-form_top {
        height: 32px;
        overflow: hidden;
    }

    .table_content {
        margin-top: 10px;
    }

    .btn_right {
        float: right;
    }
</style>