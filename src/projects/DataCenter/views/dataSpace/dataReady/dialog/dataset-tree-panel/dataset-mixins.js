/**
 *
 */

export const DatasetMixins = {
    data() {
        return {
            data: [],
            treeType: "catalogue",
            typeOpt: [
                {label: "按目录分类", value: "catalogue"},
                {label: "按数据来源类型", value: "datasource"},
            ],
        }
    },
    computed:{
        isCatalog(){
            return this.treeType === "catalogue";
        }
    },
    methods: {
        treeTypeChange(isLoading){
            const {treeType} = this;
            if(treeType === "catalogue") {
                return this.getDatasetByCatalog(isLoading);
            }else if(treeType === "datasource") {
                return this.getDatasetBySource(isLoading);
            }
        },
        getDatasetByCatalog(){

        },
        getDatasetBySource(){},
    }
}
