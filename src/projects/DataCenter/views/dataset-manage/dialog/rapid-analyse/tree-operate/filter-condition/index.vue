<template>
    <common-dialog custom-class="filter"
                   v-loading="settings.loading"
                   :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <condition-page ref="condition" v-if="reNew" :datasetId="datasetId"></condition-page>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button v-if="canEdit" type="primary" @click="submit">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>

<script src="./filter.js"></script>