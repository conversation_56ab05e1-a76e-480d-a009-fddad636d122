import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import {servicesMixins} from "../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";

export default {
    name: "user-group-manage",
    mixins : [commonMixins , servicesMixins ,listMixins,coustTableH],
    components: { UserFormPop },
    computed : {
        ...mapGetters(["userInfo" , "userRight"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    } ,
    data() {
        return {
            //角色管理搜索
            searchVal: "",
            //编辑弹窗控制显示与否
            editVisible: false,
            //角色信息编辑
            userFormData: {
                objCode:"",
                objName: "",
                fromAuthObj: "",
                userGroups: [],
                editUser: ""
            },
            // 角色管理表格数据
            tableData: [],
            tableHead : [
                {
                    prop: "objCode",
                    label: "用户组ID",
                    minWidth: "160",
                    align: "left"
                },
                {
                    prop: "objName",
                    label: "用户组名称",
                    align: "left"
                },
                {
                    prop: "belong",
                    label: "所属上级",
                    align: "center"
                },
                {
                    prop: "operate",
                    label: "操作",
                    align: "center",
                    width:220 ,
                    resizable :false
                },
            ],
            addGroupRight : $right["groupManagementAddUserGroup"] ,
            //操作栏
            operator : [
                {
                    label : '编辑',
                    clickFn : this.edit,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["groupManagementEditUserGroup"]) > -1,
                    icon : "&#xe6ad;"
                },{
                    label : '详情',
                    clickFn : this.rowDetail,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["groupManagementQueryGroup"]) > -1,
                    icon : "&#xe6c5;"
                },{
                    label : '删除',
                    clickFn : this.deleteRow,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["groupManagementDeleteUserGroup"]) > -1,
                    icon : "&#xe847;"
                }
            ],
            //分页
            paginationProps : {
                currentPage: 1,
                pageSizes: [10, 20],
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            total : 0 ,
            userForm : {
                title : "" ,
                visible : false ,
                loading : false ,
                state : "" ,
                label :"",
                functionCode :"",
            },
            userGroups : []
        };
    },
    methods: {
        async queryAllUserGroups(id){
            const vm = this , {groupServices , groupMock} = this;
            let services = vm.getServices(groupServices , groupMock);
            await services.queryAllUserGroups(id).then(res => {
                if (res.data.status === 0) {
                    vm.userGroups = res.data.data;
                }
            })
        },
        //添加用户组
        addUserGroup(){
            const vm = this, {groupServices , groupMock} = this;
            let {userForm} = this;
            userForm.title = "添加用户组信息";
            userForm.label = "用户组";
            userForm.state = "add";
            userForm.functionCode = "groupManagementAddUserGroup";
            let services = vm.getServices(groupServices , groupMock);
            services.getGroupRandom().then(async res => {
                if (res.data.status === 0) {
                    await vm.queryAllUserGroups();
                    vm.userFormData = {
                        id: "",
                        objName: "",
                        objCode: res.data.data,
                        editUser: vm.userInfo.objName,
                        fromAuthObj:  vm.userGroups && vm.userGroups.length ? vm.userGroups[0].id : "",
                        userGroups: vm.userGroups
                    };
                    vm.showPrev = true;
                    userForm.visible = true;
                }
            });

        },
        async changePage(index){
            const vm = this, {groupServices , groupMock} = this;
            let {settings , searchVal} = this;
            settings.loading = true;
            let pageSize = vm.paginationProps.pageSize;
            let services = vm.getServices(groupServices , groupMock);
            let data = {
                code : searchVal ,
                name : searchVal ,
                pageNum : index ,
                pageSize:pageSize
            };
            await services.queryUserGroupsList(data , settings).then(res =>{
                if(res.data.status === 0){
                    let result = res.data.data.dataList;
                    vm.tableData = result;
                    vm.total = res.data.data.totalCount;
                    vm.paginationProps.currentPage = res.data.data.pageIndex;
                    vm.getBelong(result);
                }
            })
        },
        getBelong(data){
            const vm = this;
            data.forEach((d , i) => {
                if(d.tSysAuthObjRelSet[0] && d.tSysAuthObjRelSet[0].relationType === "2"){
                    vm.tableData[i].belong = d.tSysAuthObjRelSet[0].toAuthObj.objName;
                    vm.tableData[i].belong_id = d.tSysAuthObjRelSet[0].toAuthObj.id;
                    vm.tableData[i].belong_code = d.tSysAuthObjRelSet[0].toAuthObj.objCode;
                }else {
                    vm.tableData[i].belong = "";
                    vm.tableData[i].belong_id = "";
                    vm.tableData[i].belong_code = "";
                }
            })
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        rowDetail(row) {
            this.edit(row , 'detail' , '用户组信息详情');
        },
        //编辑弹窗功能显示与否
        async edit(row, state, title) {
            const vm = this;
            let {userForm} = this;
            userForm.title = title || "用户组信息编辑";
            userForm.state = state || 'edit';
            userForm.label = "用户组";
            userForm.functionCode = title ? 'groupManagementQueryGroup' : 'groupManagementEditUserGroup';
            await vm.queryAllUserGroups(row.id);
            vm.userFormData = {
                id: row.id,
                objName: row.objName,
                objCode: row.objCode,
                editUser: row.updateObj.objName,
                fromAuthObj: row.belong_id,
                userGroups: vm.userGroups
            };
            userForm.visible = true;
        },
        //删除表格行
        deleteRow(row) {
            const vm = this, {groupServices , groupMock} = this;
            let services = vm.getServices(groupServices , groupMock);
            this.$dgAlert(`<span>你确定删除\"${row.objName}\"吗? </span>`, "提示", { //<br>（同时会删除用户拥有的角色）
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true,
                dangerouslyUseHTMLString: true
            }).then(()=>{
                services.deleteUserGroup(row.id).then(res=>{
                    if(res.data.status === 0){
                        if(res.data.data === "删除成功"){
                            vm.$message.success(res.data.data);
                            vm.changePage(1);
                        }else {
                            vm.$message.warning(res.data.data);
                        }
                    }
                })
            }).catch(_=>{});
        },

        updateUser(data){
            const vm = this, {groupServices , groupMock} = this;
            let {userForm} = this;
            let services = vm.getServices(groupServices , groupMock);
            let dataVo = {
                code: data.objCode,
                editor: vm.userInfo.id,
                id: data.id,
                name: data.objName,
                parent : data.fromAuthObj
            };
            userForm.loading = true;
            services.editUserGroup(dataVo ,userForm).then(res=>{
                if(res.data.status === 0){
                    vm.$message.success("保存成功");
                    vm.changePage(1);
                    userForm.visible = false;
                }
            })
        },
        saveUser(data) {
            const vm = this, {groupServices , groupMock} = this;
            let {userForm} = this;
            let services = vm.getServices(groupServices , groupMock);
            let dataVo = {
                code: data.objCode,
                editor: vm.userInfo.id,
                id: data.id,
                name: data.objName,
                parent : data.fromAuthObj
            };
            userForm.loading = true;
            services.addUserGroup(dataVo , userForm).then(res =>{
                if(res.data.status === 0){
                    vm.$message.success("添加成功");
                    vm.changePage(1);
                    userForm.visible = false;
                }
            })
        },
        //mock 查询列表数据
        async searchTableData(){
            const vm = this; let searchVal = vm.searchVal.toLowerCase();
            await vm.changePage(1);
            vm.tableData = vm.tableData.filter(tab => {
                return tab.objName.toLowerCase().indexOf(searchVal) > -1 ||  tab.objCode.toLowerCase().indexOf(searchVal) > -1;
            })
        }
    },
    created(){
        this.changePage(1);
    }
};
