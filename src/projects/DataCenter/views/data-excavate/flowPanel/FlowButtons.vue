<template>
    <div class="flowButtons">
        <el-button-group class="ce-button-group">
            <el-button
                    v-for="(icon , inx) in icons"
                    :key="inx" :title="icon.title"
                    size="mini" v-if="icon.show"
                    type="primary"
                    @click="icon.fn" plain><i class="icon" v-html="icon.icon"></i></el-button>

        </el-button-group>
        <div class="state_box">
            <el-button :title="modelState.text" class="f18" size="mini"
                       type="text"><i class="status_icon" :class="modelStateIcon"></i></el-button>
        </div>
        <TaskLog ref="TaskLog"/>
        <SelectExample ref="SelectExample" @saveValue="saveValue"/>
        <SaveModel ref="SaveModel" @setIcons="setIcons"/>
        <SqlPage ref="sql"/>

    </div>
</template>

<script>
    import TaskLog from "./TaskLog"
    import SelectExample from "./SelectExample";
    import SaveModel from "./SaveModel";
    import {globalBus} from "@/api/globalBus";
    import {common} from "@/api/commonMethods/common"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import SqlPage from "../../visualizing/visual/sql-page/index"
    import {servicesMixins} from "../service-mixins/service-mixins";

    export default {
        name: "FlowButtons",
        mixins: [common, commonMixins, servicesMixins],
        props: {
            status: Number,
            rowData: Object,
            lines: Array,
            isTemplate: Boolean,
            isFocus:Boolean
        },
        components: {
            SaveModel,
            SelectExample,
            TaskLog,
            SqlPage,
        },
        computed: {
            modelStateIcon() {
                return this.modelState.icon + ' status-' + this.modelState.type;
            },
            flowLine(){
                return this.lines;
            }
        },
        data() {
            return {
                saveInstance: "",
                icons : {
                    run : {
                        title: '执行',
                        icon: '&#xe623;',
                        show: true,
                        fn: this.model_run
                    },
                    stop : {
                        title: '停止',
                        icon: '&#xe621;',
                        show: true,
                        fn: this.model_stop
                    },
                    // clear : {
                    //     title: '清除',
                    //     icon: '&#xe847;',
                    //     show: true,
                    //     fn: this.model_clear
                    // },
                    task : {
                        title: '任务日志',
                        icon: '&#xe76c;',
                        show: true,
                        fn: this.model_log
                    },
                    save : {
                        title: '保存',
                        icon: '&#xe6fa;',
                        show: !this.isTemplate,
                        fn: this.model_save
                    },
                    sql : {
                        title: 'SQL',
                        icon: 'sql',
                        show: true,
                        fn: this.model_sql
                    }
                },
                states: [
                    {type: 'none', text: '暂无状态', icon: 'icon icon-state'},
                    {type: 'success', text: '任务完成', icon: 'icon icon-state'},
                    {type: 'abnormal', text: '异常', icon: 'icon icon-state'},
                    {type: 'ongoing', text: '分配中', icon: 'el-icon-loading'},
                    {type: 'none', text: '取消成功', icon: 'icon icon-state'},
                    {type: 'ongoing', text: '执行中', icon: 'el-icon-loading'},
                ],
                modelState: {},
                instanceList: "",

                timeState: "",
                rowL: {},
                relationObj: [],
                marked: [],
                allEdge: []
            }
        },
        methods: {
            setRelation() {
                const vm = this;
                vm.allEdge = [];
                vm.flowLine.forEach(l => {
                    vm.relationObj[l.from] = {};
                    vm.relationObj[l.from].from = [];
                    vm.relationObj[l.from].to = [];
                    vm.relationObj[l.to] = {};
                    vm.relationObj[l.to].from = [];
                    vm.relationObj[l.to].to = [];

                    if (!vm.allEdge.includes(l.from)) {
                        vm.allEdge.push(l.from)
                    }
                    if (!vm.allEdge.includes(l.to)) {
                        vm.allEdge.push(l.to)
                    }
                })
            },
            async graph() {
                const vm = this;
                await vm.setRelation();
                vm.lines.forEach(l => {
                    vm.relationObj[l.from].to.push(l.to);
                    vm.relationObj[l.to].from.push(l.from);
                    vm.marked[l.from] = false;
                    vm.marked[l.to] = false;
                });
            },
            hasPathTo(v) {
                return this.marked[v];
            },
            resetMark(){
                for(let edge of this.allEdge){
                    this.marked[edge] = false;
                }
            },
            async getAllPath() {
                const vm = this;
                await vm.graph();
                let all = [];
                for (let k in vm.relationObj) {
                    if (!vm.hasPathTo(k)) {
                        all.push(vm.setPath(k));
                    }
                }
                let result = false;
                if (all.length > 1 ) {
                    vm.$message.error('不允许存在多条链路');
                    result = true;
                }
                return result;
            },
            setPath(n) {
                const vm = this;
                let path = [];
                path.push(n);
                vm.marked[n] = true;
                let to = vm.relationObj[n].to,
                    from = vm.relationObj[n].from;
                let allList = [...to , ...from];
                for (let i in allList) {
                    let r = allList[i];
                    if (!vm.hasPathTo(r)) {
                        path = path.concat(vm.setPath(r));
                    }
                }
                return path;
            },
            /**
             * 按方向获取链路
             * @param n
             * @param path
             */
            getDirectionPath(n, path) {
                let result = [...path];
                if (result.indexOf(n) === 0) {
                    result.push(true);
                }
                if (!this.marked[n]) {
                    result.push(n);
                    this.marked[n] = true;
                    for (let i in this.relationObj[n].to) {
                        let relation = this.relationObj[n].to[i];
                        result = this.getDirectionPath(relation, result);
                    }
                }
                return result;
            },
            async hasClosePath(){
                const vm = this;
                let allPath = [];
                for(let edge of vm.allEdge){
                    await this.resetMark();
                    allPath.push(vm.getDirectionPath(edge , []));
                }
                let result = false;
                allPath.forEach(p =>{
                    if(p.indexOf(true) > -1){
                        result = true;
                    }
                })
                if(result) vm.$message.error('不允许链路闭环');
                return result;
            },
            saveValue(val) {
                this.saveInstance = val;
            },
            setInstanceList(value) {
                if (value) this.instanceList = value;
            },
            async model_run() {
                const vm = this;
                let testCount = await vm.getAllPath();
                if (testCount) return;
                let testClose = await vm.hasClosePath();
                if(testClose) return;
                vm.confirm("提示", '此操作将执行任务', () => {
                    vm.startJob();//开始执行
                })
            },
            startJob() {
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                vm.$message({
                    type: 'success',
                    message: '开始执行！',
                });
                services.runJob(vm.rowData.transId).then(res => {
                    console.log(res.data.data);
                })
            },
            model_stop() {
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                /*let condition = vm.rowL.isNew ? !vm.saveInstance : vm.rowL.isNew;
                if (condition) {
                    vm.$message.warning('请先选择实例!');
                    return;
                }*/
                vm.confirm('提示', '确认停止任务？', () => {
                    services.stopJob(vm.rowData.transId).then(res => {
                        // console.log("停止任务：",res);
                        if (res.data.status === 0) {
                            vm.$message.success("任务已停止!");
                        }
                    })
                });
            },
            model_clear() {
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                let condition = vm.rowL.isNew ? !vm.saveInstance : vm.rowL.isNew;
                if (condition) {
                    vm.$message.warning('请先选择实例!');
                    return;
                }
                vm.confirm('提示', '清除前请确认有执行停止操作，否则会有不可预料的风险！！！', () => {
                    services.cleanJob(vm.rowData.transId).then(res => {
                        // console.log("清除步骤：",res);
                        if (res.data.status === 0) {
                            vm.$message.success("清除成功！");
                        }
                    })
                })
            },
            model_log() {
                this.$refs.TaskLog.show(this.rowData);
            },
            model_ex() {
                this.$refs.SelectExample.show(this.rowData, this.instanceList, this.saveInstance);
            },
            model_save(e, isModel = false, transId) {
                if (transId && transId !== this.rowData.transId) return;
                this.$refs.SaveModel.show(this.rowData, this.saveInstance, isModel);
            },
            model_sql() {
                const vm = this;
                vm.$axios.get("/dataPreview/getSQL?transId=" + vm.rowData.transId).then(res => {
                    if (res.data.status === 0) {
                        vm.$refs.sql.show(res.data.data, vm.rowData.modelName);
                    } else {
                        vm.$message.error(res.data.msg);
                    }
                })
            },
            stateInit(n) { // n 传进来状态
                this.modelState = this.states[n];
            },
            setIcons() {
                // this.icons[5].show = false;
                this.icons.save.show = false;
                this.rowL.isNew = false;
            },
            getState() {
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                services.jobStatusMlsql(vm.rowData.transId).then(res => {
                    if (res.data.status === 0) {
                        let state;
                        if (res.data.data.status === "none") {
                            state = 0;
                        } else if (res.data.data.status === "success") {
                            state = 1;
                            globalBus.$emit('fileDownLoadsuccess', res.data.data.status);
                        } else if (res.data.data.status === "running") {
                            state = 5;
                        } else {
                            state = 2;
                        }

                        vm.stateInit(state);
                    }
                })

            },
            clearTime() {
                clearInterval(this.timeState);
            },
            startTime(){
                this.timeState = setInterval(this.getState, 1000);
            }
        },
        watch : {
            isFocus(val){
                if(val){
                    this.startTime();
                }else {
                    this.clearTime();
                }
            }
        },
        created() {
            this.stateInit(this.status);
            this.startTime();
            // console.log(this.rowData);
            if (this.rowData.isNew === false) {
                // this.icons[5].show = false;
                this.icons.save.show = false;
            }
            this.rowL = Object.assign({}, this.rowData);
            globalBus.$on("saveAsModel", this.model_save);
        },
        destroyed() {
            this.clearTime();
            globalBus.$off("saveAsModel", this.model_save);
        }
    }
</script>

<style scoped>
    .flowButtons {
        position: absolute;
        left: 10px;
        top: 10px;
        overflow: hidden;
        z-index: 120;
    }

    .state_box {
        display: inline-block;
        vertical-align: middle;
        padding: 2px 10px;
    }

    .status_icon {
        font-size: 18px;
    }
</style>
