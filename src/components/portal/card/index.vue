<!--
    Exception 个人信息卡片
    @Author: zouwf
    @Date: 2019-12-10
-->
<template>
    <div class="l-card">
        <!--默认类型-->
        <div v-if="original"  class="l-card__default">
            <img class="l-card__default-img" :src="data.url" />
            <div class="l-card__default-content">
                <div class="l-card__default-content-text">
                    <span>{{ data.name }}</span>
                    <span>{{ data.sex }}</span>
                </div>
                <div class="l-card__default-content-text-id">{{ data.id }}</div>
            </div>
            <span class="l-card__default-label"
                  :class="{ 'is-active-notpass': data.status === '不通过',
                            'is-active-pass': data.status === '通过'}"
            >
                {{ data.status }}
            </span>
        </div>
        <!--左右类型-->
        <div v-if="about" class="l-card__about">
            <div class="l-card__about-img">
                <img :src="data.url" alt="">

            </div>
            <div class="l-card__about-right">
                <div class="l-card__about-right__content">
                    <h3>{{data.title}}</h3>
                    <p>嫌疑人：<em>{{data.name}}</em></p>
                    <p>案发时间：<em>{{data.time}}</em></p>
                    <p>案发地点：<em >{{data.address}}</em></p>
                </div>
                <el-checkbox
                        class="l-card__about-right-checkbox"
                        v-model="data.checked"
                ></el-checkbox>
            </div>
        </div>
        <!--上下类型-->
        <div v-if="upDown" class="l-card__up-down">
            <img :src="data.url" alt>
            <div class="l-card__up-down-text">
                <h2 class="dg-words-title-second">{{data.title}}</h2>
                <p class="dg-words-normal">{{data.text}}</p>
                <div class="l-card__up-down-text-footer">
                    <span>{{data.time}}</span>
                    <i class="el-icon-download"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "index",
        props: {
            //数据
            data:{
                type: Object,
                default:() =>{
                    return{
                        url:'/static/themes/images/test.png',
                        status:'不通过',
                        name:'风小谷',
                        sex:'女',
                        id:'36225454787894541',
                        checked:'',
                        title:'这是一条测试数据',
                        time:'2018-9-10 12:34:34',
                        address:'福建省厦门市集美区软件园三期B区201号楼',
                        text:'大夫回电话花都飞虎度杜大夫回电话花都飞虎度杜大夫回电话花都飞虎度杜'
                    }
                }
            }
        },
        data(){
            return{
                checked:'',
                //默认类型
                original:false,
                //左右类型
                about:false,
                //上下类型
                upDown:false
            }
        },
        created(){
            /**
             *
             * 控制类型显示
             *
             * */
            let {type} = this.$attrs;
            if(type === void(0)){
                this.original = true
            }else if(type === 'about') {
                this.about = true
            }else if(type === 'upDown'){
                this.upDown = true
            }
        }
    };
</script>