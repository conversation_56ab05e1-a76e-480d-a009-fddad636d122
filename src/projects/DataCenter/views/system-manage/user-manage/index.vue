<template>
    <div class="roles-manage ce-manage_list" v-loading="settings.loading">
        <div class="roles-header">
            <div class="ce-table_btns">
                <dg-button type="primary" v-if="rights.indexOf(addUserRight) > -1" @click="addUser">添加
                </dg-button>
            </div>
            <div class="ce-table__search">
                <el-input
                        size="mini"
                        placeholder="请输入登录账号/姓名搜索"
                        v-model.trim="searchVal"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'searchVal')"
                        @keyup.native.enter="isMock ? searchTableData() : changePage(1)"
                >
                    <i class="el-icon-search el-input__icon poi" slot="suffix"
                       @click="isMock ? searchTableData() : changePage(1)"></i>
                </el-input>
            </div>
        </div>
        <div class="roles-content">
            <common-table
                    :data="tableData"
                    :columns="tableHead"
                    :max-height="tableBodyH"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100 ce-table-h__auto"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ?  ()=>{} : changePage($event)"
                    @change-size="changeSize"
            >
               <template  slot="enableState" slot-scope="{row}">
                    {{ row.enableState | state }}
                </template>
               <template slot="operate" slot-scope="{row}">
                    <span class="r-c-action" v-for="(opt , inx) in operator" :key="inx"
                            v-if="opt.condition(row , rights)">
                        <dg-button type="text" v-html="opt.label" :title="opt.label"
                                    @click="opt.clickFn(row)"></dg-button>
                    </span>
                </template>
            </common-table>
        </div>
        <!-- 用户信息新增弹窗begin -->
        <dg-dialog :title="userForm.title"
                   v-loading="userForm.loading"
                   width="700px"
                   :visible.sync="userForm.visible"
                   :close-on-click-modal="false"
                   @closed="clearFormData"
                   @open="reNewData"
        >
            <div slot="title"><span class="el-dialog__title">{{userForm.title}}</span> <help-document :code="userForm.functionCode"></help-document></div>
            <div class="user-info-dig" ref="userForm">
                <div class="u-i-d-top" ref="f_body" v-show="showF">
                    <h3>{{ userformTitle }}</h3>
                    <el-form
                            ref="form"
                            size="small"
                            :model="currentData"
                            status-icon
                            label-width="100px"
                            class="demo-ruleForm"
                            :hide-required-asterisk="userForm.state === 'detail'"
                            :rules="rules"
                    >
                        <el-form-item label="登录账号" prop="account"
                                      :rules="userForm.state === 'add'? account : editAccount">
                            <el-input ref="account" class="el-input--suffix" maxlength="100"
                                      :title="currentData.account"
                                      v-model.trim="currentData.account"
                                      v-input-filter="currentData.account"
                                      :disabled="userForm.state === 'detail' || userForm.state === 'edit'"
                                      placeholder="请输入登录账号(字母、数字、下划线，限100个字符以内)"
                                      @change="setTipShow(true)"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="姓名" prop="name">
                            <el-input ref="name" class="el-input--suffix" v-model.trim="currentData.name"
                                      v-input-filter="currentData.name"
                                      :title="currentData.name"
                                      maxlength="100" :disabled="userForm.state === 'detail'"
                                      placeholder="请输入姓名(限100字符以内)"></el-input>
                        </el-form-item>
                        <el-form-item label="所属用户组" prop="fromAuth">
                            <ce-select-drop
                                    class="expect-select"
                                    ref="tree"
                                    :props="defaultProps"
                                    v-model="currentData.fromAuth"
                                    :filterNodeMethod="filterNode"
                                    :data="fromAuthList"
                                    filterable
                                    :disabled="userForm.state === 'detail'"
                            ></ce-select-drop>
                        </el-form-item>
                        <el-form-item label="身份证号" prop="certificateNumber">
                            <el-input v-model.trim="currentData.certificateNumber"
                                      maxlength="18" :title="currentData.certificateNumber"
                                      :disabled="userForm.state === 'detail'"
                                      :placeholder="userForm.state === 'detail' ? '': '请输入身份证号'"></el-input>
                        </el-form-item>
                        <el-form-item label="电子邮箱" prop="email">
                            <el-input v-model.trim="currentData.email"
                                      v-input-filter="currentData.email"
                                      maxlength="100" :title="currentData.email"
                                      :disabled="userForm.state === 'detail'"
                                      :placeholder="userForm.state === 'detail' ? '': '请输入电子邮箱(限100字符以内)'"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话" prop="phone">
                            <el-input v-model.trim="currentData.phone"
                                      v-input-filter="currentData.phone"
                                      maxlength="13" :title="currentData.phone"
                                      :disabled="userForm.state === 'detail'"
                                      :placeholder="userForm.state === 'detail' ? '': '请输入联系电话'"></el-input>
                        </el-form-item>
                        <el-form-item label="状态" prop="status">
                            <dg-select
                                    v-model="currentData.status"
                                    :data="statusList"
                                    :disabled="userForm.state === 'detail'"
                            ></dg-select>
                        </el-form-item>
                        <div class="u-i-d-bottom">
                            <h3>{{ userformTitle1 }}</h3>
                            <el-form-item prop="roleVal" label-width="0">
                                <div class="u-i-d-b-checkbox">
                                    <!-- <dg-checkbox-group v-model="currentData.roleVal"  :disabled="userForm.state === 'detail'" :data="roleData" row></dg-checkbox-group>-->
                                    <el-checkbox-group v-model="currentData.roleVal"
                                                       :disabled="userForm.state === 'detail'">
                                        <el-checkbox v-for="role in roleData" :label="role.value" :key="role.value">
                                            <span class="role_checklabel" :title="role.label">{{ role.label }}</span>
                                        </el-checkbox>
                                    </el-checkbox-group>
                                </div>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>

            </div>
            <span slot="footer" class="dialog-footer">
                <dg-button v-if="userForm.state !== 'detail'" @click="userForm.visible = false">取 消</dg-button>
                <dg-button type="primary" :disabled="userForm.loading" v-if="userForm.state === 'add'"
                           @click="addUserSubmit(true)">确 定</dg-button>
                <dg-button type="primary" :disabled="userForm.loading" v-else-if="userForm.state === 'edit'"
                           @click="addUserSubmit(false)">确 定</dg-button>
                <dg-button v-else @click="userForm.visible = false">关 闭</dg-button>
            </span>
        </dg-dialog>
        <!-- 用户信息新增弹窗end -->
    </div>
</template>
<script lang="js" src="./user-manage.js"></script>
<style lang="less" scoped src="./user-manage.less"></style>
