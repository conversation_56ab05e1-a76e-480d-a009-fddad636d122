<!--返回结果页面-->
<template>
    <div class="height100">
        <el-form label-width="110px" label-position="right">
            <el-form-item v-for="(item , k) in resFormList" :key="k" :label="`${item.label}: `">
                {{ resForm[k] || ' - ' | formTxt }}
            </el-form-item>
        </el-form>
        <div class="api-right-page">
            <div class="api-right-btn-group">
                <el-tooltip v-if="isError" effect="dark" :content="resForm.code">
                    <el-button type="text" @click="showError">{{ errorBtnTxt }}</el-button>
                </el-tooltip>
            </div>
            <ce-tab-page class="api-right-tab" v-model="showStyle" :tabPanes="tabData">
                <div slot="page" slot-scope="{classN}" :class="[classN , 'api-right-respond']">
                    <el-input
                            class="api-right-respond_input"
                            v-if="showStyle === 'text'"
                            type="textarea"
                            resize="none"
                            readonly
                            v-model="resForm.response">
                    </el-input>
                    <common-table
                            :data="data"
                            :columns="tHeadData"
                            :paging-type="'client'"
                            :pagination-props="paginationProps"
                            height="calc(100% - 45px)"
                            class="width100"
                            row-key="tableIndex"
                            default-expand-all
                            :border="false"
                            v-else-if="['数据查询'].includes(row.type)"
                    >
                        <template slot="type-expand" slot-scope="{row}">
                            <common-table
                                    :data="row.data"
                                    :columns="row.head"
                                    :border="false"
                                    :pagination="false"
                            >
                            </common-table>
                        </template>
                    </common-table>
                    <!-- 数据查询表格展示 -->
                    <common-table
                            :data="data"
                            :columns="tHeadData"
                            :paging-type="'client'"
                            :pagination-props="paginationProps"
                            height="calc(100% - 45px)"
                            class="width100"
                            :border="false"
                            v-else-if="['信息核查'].includes(row.type) || isSub"
                    >
                    </common-table>
                    <common-table
                            :data="data"
                            :columns="tHeadData"
                            :pagination="pageFlag"
                            :paging-type="'server'"
                            :pagination-props="paginationProps"
                            :pagination-total="totalCount"
                            @change-current="changePage($event) "
                            @change-size="changeSize"
                            height="calc(100% - 45px)"
                            class="width100"
                            :border="false"
                            v-else
                    >
                    </common-table>
                </div>
            </ce-tab-page>

        </div>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import CeTabPage from "dc-front-plugin/src/components/CeTabPage/CeTabPage";

export default {
    name: "RespondPage",
    mixins: [commonMixins],
    filters: {
        formTxt(val) {
            switch (val) {
                case 'success' :
                    return '请求成功';
                case 'fail' :
                    return '请求失败';
                default:
                    return val;
            }
        }
    },
    components: {
        CeTabPage,
    },
    props: {
        row: Object
    },
    computed: {
        isError() {
            return this.resForm.result === 'fail';
        }
    },
    data() {
        return {
            errorBtnTxt: '查看错误码',
            tHeadData: [],
            data: [
                {
                    tableCode: 'cccddee',
                    data : [
                        {name : '123'}
                    ],
                    head : [
                        {
                            prop : 'name',
                            label :'name'
                        }
                    ]
                }
            ],
            pageFlag: false,
            totalCount: 0,
            textInfo: '',
            showStyle: 'text',
            tabData: [
                {
                    value: "text",
                    label: "返回结果(JSON)"
                }, {
                    value: "table",
                    label: "返回结果(表格)"
                }
            ],
            resFormList: {
                result: {
                    label: '请求结果'
                }, useTime: {
                    label: '请求耗时(ms)'
                },
            },
            resForm: {
                result: "",
                useTime: "",
                response: "",
                code: ""
            },
            reqParamsVo: {},
            isSub: false,
        }
    },
    methods: {
        showError() {
        },
        changePage(inx = 1) {
            const vm = this;
            let services = this.$services('apiTest');
            this.totalCount = 0;
            this.paginationProps.currentPage = inx;
            if (this.reqParamsVo.pageSize !== undefined) this.reqParamsVo.pageSize = this.paginationProps.pageSize;
            if (this.reqParamsVo.pageIndex !== undefined) this.reqParamsVo.pageIndex = this.paginationProps.currentPage;
            services.testSingleService({params: this.reqParamsVo}).then(res => {
                if (res.data.code === 0) {
                    let result = res.data.data;
                    vm.setResult(result)
                }
            })
        },
        setResult(result) {
            const vm = this;
            let parInfo = result.data || "";
            vm.setModelResultData(parInfo);
            vm.resForm.useTime = result.usedTime;
            vm.resForm.result = result.result;
            vm.resForm.code = result.code;
        },
        setModelResultData(res) {
            const vm = this;
            vm.data = [];
            vm.tHeadData = [];
            let result = res ? JSON.parse(res) : '';
            vm.resForm.response = res ? JSON.stringify(JSON.parse(res), null, 4) : '';
            if (result.requestId) vm.$emit('getAsyncInfo', result.requestId);
            if (['数据查询'].includes(vm.row.type)) vm.setMutiTable(result);
            else vm.setDefTable(result);
        },
        setDefTable(result) {
            const vm = this;
            vm.data = result.operatorDataSet || [];
            for (const key in vm.data[0]) {
                vm.tHeadData.push({
                    prop: key,
                    label: key
                })
            }
        },
        setMutiTable(result) {
            const vm = this;
            this.tHeadData = [
                {
                    type: 'expand',
                    prop: 'expand'
                }, {
                    prop: 'tableCode',
                    label: '表名'
                },{
                    prop : 'condition',
                    label : '条件'
                }
            ];
            vm.data = [];
            let data = result.operatorDataSet || [];
            let i = 0;
            data.forEach(it => {
                for(let k in it){
                    for(let ta in it[k]){
                        let item = {};
                        item.condition = k;
                        item.tableIndex = `${ta}${i}`;
                        i++;
                        item.tableCode = ta;
                        item.head = [];
                        item.data = [];
                        if(it[k][ta].length){
                            for(let key in it[k][ta][0]){
                                item.head.push({
                                    prop : key ,
                                    label : key
                                })
                            }
                            item.data = it[k][ta];
                        }
                        vm.data.push(item);
                    }
                }
            })
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage();
        },
        setReqParams(vo) {
            this.reqParamsVo = vo;
            this.changePage();
        },
        setTestVo(vo, res) {
            this.reqParamsVo = vo;
            this.setResult(res);
        },
        setSubParams(vo) {
            this.isSub = true;
            this.reqParamsVo = vo;
            this.getSubscribe();
        },
        getSubscribe() {
            const vm = this, {reqParamsVo} = vm;
            let ser = vm.$services("serviceManage");
            ser.serviceSubscribe(reqParamsVo).then(res => {
                vm.setSubResultData(res);
            })
        },
        setSubResultData(res) {
            const vm = this;
            vm.data = [];
            vm.tHeadData = [];
            vm.resForm.code = res.status;
            vm.resForm.useTime = "";
            vm.resForm.result = res.data.code === 0 ? 'success' : 'fail';
            vm.resForm.response = JSON.stringify(res.data, null, 4);
            vm.data = res.data && res.data.data ? [res.data.data] : [];
            for (const key in vm.data[0]) {
                vm.tHeadData.push({
                    prop: key,
                    label: key
                })
            }
        },

    },
}
</script>

<style scoped lang="less" src="./testPage.less"></style>
