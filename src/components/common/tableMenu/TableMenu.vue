<template>
    <div class="TableMenu">
        <div class="r">
            <slot name="append"></slot>
        </div>
        <div class="l">
            <el-form :inline="true" size="small" class="ce-form__m0" @submit.native.prevent>
                <slot name="prepend"></slot>
            </el-form>
        </div>
    </div>
</template>

<script>
    export default {
        name: "TableMenu"
    }
</script>

<style scoped>
    .TableMenu {
        min-height: 34px;
        margin-bottom: 10px;
        overflow: hidden;
    }
</style>