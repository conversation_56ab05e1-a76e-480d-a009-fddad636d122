<template>
    <div class="cornTimeData">
        <el-form ref="form" size="mini">
            <el-form-item>
                <el-radio v-model="timeType" label="wildcard/*">每{{tabData.label}}允许的通配符[ , - * {{tabData.wildcard}} /]
                </el-radio>
            </el-form-item>
            <el-form-item v-if="tabData.noAssign">
                <el-radio v-model="timeType" label="wildcard/?">不指定</el-radio>
            </el-form-item>

            <el-form-item v-if="tabData.cycleStart">
                <el-radio v-model="timeType" :label="dataRange">
                    <span class="txt_left_padding">周期从</span><span class="txt_left_padding" v-if="tabData.label === '周'">{{tabData.label}}</span>
                    <el-input-number style="width: 100px;" v-model="num1" controls-position="right"
                                     :min="tabData.cycleStart"
                                     :max="tabData.rangeMax - 1"></el-input-number>
                    <span class="txt_padding">-</span>
                    <el-input-number style="width: 100px;" v-model="num2" controls-position="right" :min="num1 + 1"
                                     :max="tabData.rangeMax"></el-input-number>
                    <span v-if="tabData.label !== '周'" class="txt_padding">{{tabData.label}}</span>
                </el-radio>
            </el-form-item>
            <el-form-item v-if="tabData.distance">
                <el-radio v-model="timeType" :label="dataDistance">
                    <span class="txt_left_padding">从</span>
                    <el-input-number style="width: 90px;" v-model="startTime" controls-position="right"
                                     :min="tabData.rangeMin"
                                     :max="tabData.rangeMax"></el-input-number>
                    <span class="txt_padding">{{tabData.label}}开始,每</span>
                    <el-input-number style="width: 90px;" v-model="distance" controls-position="right"
                                     :min="tabData.rangeMin"
                                     :max="tabData.rangeMax"></el-input-number>
                    <span class="txt_padding">{{tabData.label}}执行一次</span>
                </el-radio>
            </el-form-item>
            <el-form-item v-if="tabData.length">
                <el-radio v-model="timeType" :label="dataSort">
                    <span class="txt_left_padding">第</span>
                    <el-input-number style="width: 90px;" v-model="startTime" controls-position="right"
                                     :min="tabData.rangeMin"
                                     :max="tabData.length"></el-input-number>
                    <span class="txt_padding">{{tabData.label}} 的星期</span>
                    <el-input-number style="width: 90px;" v-model="sortVal" controls-position="right"
                                     :min="tabData.rangeMin"
                                     :max="tabData.rangeMax"></el-input-number>
                </el-radio>
            </el-form-item>
            <el-form-item v-if="tabData.nearWorkDay">
                <el-radio v-model="timeType" :label="dataNearWorkDay">
                    <span class="txt_left_padding">每月</span>
                    <el-input-number style="width: 90px;" v-model="workDay" controls-position="right"
                                     :min="tabData.rangeMin"
                                     :max="tabData.rangeMax"></el-input-number>
                    <span class="txt_padding">号最近的那个工作日</span>
                </el-radio>
            </el-form-item>

            <el-form-item v-if="tabData.lastDay">
                <el-radio v-model="timeType" label="wildcard/L">本月最后一天</el-radio>
            </el-form-item>
            <el-form-item v-if="tabData.lastWeek">
                <el-radio v-model="timeType" :label="dataLast">
                    <span class="txt_left_padding">本月最后一个星期</span>
                    <el-input-number style="width: 90px;" v-model="weekday" controls-position="right"
                                     :min="tabData.rangeMin"
                                     :max="tabData.rangeMax"></el-input-number>
                </el-radio>
            </el-form-item>
            <el-form-item v-if="tabData.rangeMax">
                <el-radio v-model="timeType" :label="dataAssign">
                    <span class="txt_left_padding">指定({{tabData.label}})</span>
                    <el-checkbox @change="assignChange" :indeterminate="isIndeterminate" v-model="checkAll" class="txt_left_padding">全选</el-checkbox>
                    <el-checkbox-group class="checkGroup" v-model="checkData" @change="checkChange">
                        <el-checkbox v-for="(check , i ) in checkList" :key="i" :label="parseInt(check)">{{check}}</el-checkbox>
                    </el-checkbox-group>
                </el-radio>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
    export default {
        name: "CornTimeData",
        props: {
            tabData: Object ,
            inx : Number
        },
        computed: {
            dataRange() {
                return 'range/' + this.num1 + ',' + this.num2;
            },
            dataDistance() {
                return 'distance/' + this.startTime + ',' + this.distance;
            },
            dataAssign() {
                return 'assign/' + this.checkData.join(',');
            },
            dataNearWorkDay() {
                return 'workDay/' + this.workDay;
            },
            dataSort(){
                return 'sort/' + this.startTime + ',' + this.sortVal ;
            },
            dataLast(){
                return 'last/' + this.weekday;
            }
        },
        watch: {
            num1(val) {
                if (this.num2 <= val) {
                    this.num2 = this.num1 + 1;
                }
            },
            timeType(val){
                this.$emit('getTimeType', this.inx , val);
            }
            
        },
        data() {
            return {
                label_width: '110px',
                timeType: 'wildcard/*',
                num1: 1,
                num2: 2,
                startTime: 0,
                distance: 0,
                checkData: [],
                checkList: [],
                workDay: 0 ,
                sortVal : 1 ,
                weekday :1 ,
                isIndeterminate : false ,
                checkAll : false
            }
        },
        methods: {
            createList() {
                if (this.tabData.rangeMax) {
                    for (let i = this.tabData.rangeMin; i <= this.tabData.rangeMax; i++) {
                        let data = i < 10 ? '0' + i : '' + i;
                        this.checkList.push(data);
                    }
                }
            },
            checkChange(value) {
                this.timeType = this.dataAssign;
                let checkedCount = value.length;
                this.checkAll = checkedCount === this.checkList.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkList.length;
            },
            assignChange(val) {
                this.checkData = val ? this.checkList : [];
                this.isIndeterminate = false;
                this.timeType = this.dataAssign;
            }
            
        },
        created() {
            this.createList();
        }
    }
</script>

<style scoped>
    .txt_padding {
        padding: 0 8px;
    }

    .txt_left_padding {
        padding-right: 8px;
    }

    .checkGroup {
        display: inherit;
        white-space: normal;
        padding: 10px 5px 0 5px;
        line-height: 24px;
    }
</style>