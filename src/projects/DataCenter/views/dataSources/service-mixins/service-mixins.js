export const servicesMixins = {
    data(){
        const {isMock} = this;
        let listApi = require("@/projects/DataCenter/services/dataSource-services/listApi") ,
            listMock = isMock? require("@/projects/DataCenter/mock/dataSource-mock/listMock"):{},
            treeApi = require("@/projects/DataCenter/services/dataSource-services/treeApi") ,
            treeMockApi = isMock? require("@/projects/DataCenter/mock/dataSource-mock/treeMock"):{};

        return {
            listApi ,
            listMock ,
            treeApi,
            treeMockApi
        }
    }
}
