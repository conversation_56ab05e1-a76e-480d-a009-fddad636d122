<template>
    <div class="chartCard" :class="{'pt30': showDrill}">
        <div :style="{'height' : showDrill ? drillHeight : height}">
            <ve-pie
                    v-loading="loading"
                    :width="width"
                    :height="height"
                    :data="chartData"
                    :settings="chartSettings"
                    :extend="extend"
                    :judge-width="true"
                    :data-empty="data_empty"
                    :events="chartEvents"
                    ref="chart"


            >
            </ve-pie>
            <!-- :colors="colors"-->
        </div>
        <div v-if="showDrill" class="chartTip"
             @click="backToDims"
             :title="dimsName"><i class="el-icon-arrow-left"></i>{{dimsName}}
        </div>
    </div>
</template>
<script>
    import 'v-charts/lib/style.css'
    import {globalBus} from "@/api/globalBus";
    import {chartMxins} from "./chart-mixins/chart-mixins";
    import numerify from 'numerify'

    export default {
        name: "PieCard",
        mixins: [chartMxins],
        data() {
            return {
                extend: {
                    series: {},
                    title: {
                        show: true,
                        top: 'top',
                        left: 'center',
                        textStyle: {
                            color: '#666'
                        }
                    },
                    tooltip: {
                        show: true,
                        trigger: 'item',
                        appendToBody: true
                    },
                    legend: {
                        top: 26,
                        left: 'center',
                    },
                },
              defaultLegend : {
                color_type :"custom",
                color_theme :"default",
                color: [],
                radius_o: 50,
                radius_i: 0,
                out_radius_o: 80,
                out_radius_i: 65,
                radius_o_type: "abs",
                radius_i_type: "abs",
                out_radius_o_type: "abs",
                out_radius_i_type: "abs",
                label_show: true,
                label_pos: "outside",
                orient: "horizontal",
                legend_pos: "top",
                measure: "",
                label_cont: "dimension,percent",
                top_type: "abs",
                top: 0,
                seriesName: [],
                bottom_type: "abs",
                bottom: 0,
                left_type: "abs",
                left: 0,
                right_type: "abs",
                right: 0,
              }
            }
        }
        ,
        methods: {
            getNodeData() {
                globalBus.$on('setPiePanel', this.setHistogramPanel);
            },
            setChartExtend(settings) {
                this.setTitle(settings.title);
                this.setAlias(settings.fieldSetting);
                this.setLegend(settings.legend);
                this.setTooltip();
            },
            getDefaultLegend(legend , nodeData){
              const vm = this;
              if(legend.label_cont === undefined || legend.radius_i === undefined){
                vm.defaultLegend.seriesName = [];
                nodeData.yData.forEach(item => {
                  vm.defaultLegend.seriesName.push(item.label);
                })
                vm.defaultLegend.measure = nodeData.xData.length && nodeData.xData[0].label;
                return Object.assign(legend , this.defaultLegend);
              }else {
                return legend;
              }
            },
            setLegend(legend) {
                const vm = this;
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                let de_legend = vm.getDefaultLegend(legend , nodeData);
                vm.setColor(de_legend.color, de_legend.color_theme, de_legend.color_type);
                vm.setDataLabel(de_legend.label_show, de_legend.label_pos, nodeData);
                vm.legendAuto(de_legend);
                vm.setSeriesM(de_legend, nodeData);
                vm.setLabelCont(de_legend.label_cont, de_legend.seriesName, de_legend.measure, nodeData);
                vm.setRadius(de_legend, de_legend.radius_i, de_legend.radius_o, de_legend.out_radius_i, de_legend.out_radius_o, nodeData);
            },
            setTooltip() {
                const node = this.node, vm = this;
                this.extend.tooltip.formatter = function (params) {
                    let {beforeData} = JSON.parse(vm.node.widget),
                        nodeData = JSON.parse(beforeData);
                    let yField = nodeData.yData.filter(item => item.label === params.seriesName);
                    let funcLabel = nodeData.xData[0].func.name;
                    let xLabel = nodeData.xData[0].alias || nodeData.xData[0].label;
                    let xField = vm.chartData.rows[0][nodeData.xData[0].data.code];
                    let yLabel = yField[0].alias || yField[0].label;
                    if (vm.chartData && vm.chartData.dimsCodes) {
                       if(vm.node.code === "RingPieChartWidget"){
                           let inx = params.seriesIndex === 0 ? 1 : 0;
                           yLabel = vm.chartData.dimsCodes[inx];
                       }else {
                           yLabel = vm.chartData.dimsCodes[0];
                       }
                    }
                    let result;
                    let paramsVal = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== "none" ? numerify(params.value , vm.format[nodeData.xData[0].data.numberFormat]) : params.value;
                    result = `<div>\
                                    <div class="f14 mb5">${yLabel} :</div>\
                                    <span class="dib vm " style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                     <span>${params.name} : ${paramsVal}(${xLabel})</span>\
                                     <span>[${params.percent}%] (${funcLabel})  </span>
                                </div>`;
                    return result;
                };
            },
            /**
             * 设置数据标签
             * @param conts 显示的内容
             * @param seriesName 图表系列名
             * @param measure 度量
             * @param nodeData 图表数据
             * d_cont 维度内容 ， m_cont 度量内容 p_cont 百分比 cont1 数据名 ，cont2 数据值
             * */
            setLabelCont(conts, seriesName, measure, nodeData) {
                const vm = this, {chartData} = this;
                let d_cont = "", m_cont = "", p_cont = "";
                let condition = vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill;
                let seriesN;
                if (condition) {
                    vm.extend.series[0].name = seriesName[1];
                    vm.extend.series[1].name = seriesName[0];
                    seriesN = seriesName[1];
                } else {
                    if (Array.isArray(seriesName)) {
                        vm.extend.series.name = seriesName[0];
                        seriesN = seriesName[0];
                    } else {
                        vm.extend.series.name = seriesName;
                        seriesN = seriesName;
                    }
                }
                let labelM = vm.chartSettings.labelMap,
                    d_n = labelM[seriesN] ? labelM[seriesN] : seriesN,
                    m_n = labelM[measure] ? labelM[measure] : measure;
                if (vm.node.code !== 'RingPieChartWidget') {
                    if(vm.filterDimsName) d_n = vm.filterDimsName;
                } else {
                    if(vm.filterDimsName && vm.chartData.dimsCodes.indexOf(d_n) === -1) d_n = vm.filterDimsName;
                }

                if (conts && conts.length) {
                    conts.split(",").forEach(item => {
                        if (item === "dimension") {
                            d_cont = `(${d_n})`;
                        } else if (item === "measure") {
                            m_cont = `(${m_n})`;
                        } else if (item === "percent") {
                            p_cont = "({d}%)";
                        }
                    });
                }
                if (condition) {
                    vm.extend.series[0].label.formatter = function (params) {
                        let value = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== "none" ? numerify(params.data.value , vm.format[nodeData.xData[0].data.numberFormat])  : params.data.value;
                        let percent = p_cont ? ' ('+ params.percent + "%)" : p_cont;
                        return params.name + d_cont + ':' + value + m_cont + percent;
                    }
                } else {
                    vm.extend.series.label.formatter = function (params) {
                        let value = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== "none" ? numerify(params.data.value , vm.format[nodeData.xData[0].data.numberFormat])  : params.data.value;
                        let percent = p_cont ? ' ('+ params.percent + "%)" : p_cont;
                        return params.name + d_cont + ':' + value + m_cont + percent;
                    }
                }

            },
            setSeriesM(legend, nodeData) {
                const vm = this;
                let top = legend.top_type === "abs" ? legend.top : legend.top + '%',
                    bottom = legend.bottom_type === "abs" ? legend.bottom : legend.bottom + '%',
                    left = legend.left_type === "abs" ? legend.left : legend.left + '%',
                    right = legend.right_type === "abs" ? legend.right : legend.right + '%';
                if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                    vm.extend.series[0].top = top;
                    vm.extend.series[0].bottom = bottom;
                    vm.extend.series[0].left = left;
                    vm.extend.series[0].right = right;
                    vm.extend.series[1].top = top;
                    vm.extend.series[1].bottom = bottom;
                    vm.extend.series[1].left = left;
                    vm.extend.series[1].right = right;
                } else {
                    vm.extend.series.top = top;
                    vm.extend.series.bottom = bottom;
                    vm.extend.series.left = left;
                    vm.extend.series.right = right;
                }
                this.tipWidth = legend.left_type === "abs" ? legend.left + 100 + 'px' : legend.left + '% + 100px';
            },
            setRadius(legend, ins, out, o_ins, o_out, nodeData) {
                const vm = this;
                let ins_v = legend.radius_i_type === "abs" ? ins : ins + '%',
                    out_v = legend.radius_o_type === "abs" ? out : out + '%',
                    o_ins_v = legend.out_radius_i_type === "abs" ? o_ins : o_ins + '%',
                    o_out_v = legend.out_radius_o_type === "abs" ? o_out : o_out + '%';
                if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                    vm.extend.series[0].radius = [o_ins_v, o_out_v];
                    if (vm.extend.series[1]) {
                        vm.extend.series[1].radius = [ins_v, out_v];
                    }
                } else {
                    vm.extend.series.radius = [ins_v, out_v];
                }
            },
            setDataLabel(show, pos, nodeData) {
                const vm = this;
                if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                    vm.extend.series[0].label.show = show;
                    vm.extend.series[0].label.position = pos; //top inside insideBottom
                } else {
                    vm.extend.series.label.show = show;
                    vm.extend.series.label.position = pos; //top inside insideBottom
                }

            },
            setRingPieData(data) {
                const vm = this;
                let out_data = [], in_data = [];
                vm.chartSettings.level = [];
                data.rows.forEach(item => {
                    in_data.push({
                        name: item.name,
                        value: item.value
                    });
                    for (let k in item.child) {
                        out_data.push({
                            name: k,
                            value: item.child[k]
                        });
                    }

                });
                vm.extend.series = [];
                vm.extend.series[0] = {
                    name: "",
                    type: 'pie',
                    radius: ['40%', '55%'],
                    center: ['50%', '55%'],
                    animationThreshold: 500,
                    avoidLabelOverlap: false,
                    minShowLabelAngle: 8,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: out_data
                };
                vm.extend.series[1] = {
                    name: "",
                    type: 'pie',
                    animationThreshold: 500,
                    avoidLabelOverlap: false,
                    minShowLabelAngle: 8,
                    radius: [0, '35%'],
                    center: ['50%', '55%'],
                    label: {
                        normal: {
                            position: 'inner'
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data: in_data
                };
            },
            setRowsData(datas) {
                let rows = [];
                datas.rows.forEach(item => {
                    let list = {};
                    list[datas.columns.dims[0]] = item.name;
                    list[datas.columns.dims[1]] = item.child;
                    list[datas.columns.measures] = item.value;
                    rows.push(list);
                });
                return rows;
            },
            async getMessage(msg) {
                const vm = this;
                let result = JSON.parse(msg.data), data = result.data;
                if (vm.socket) vm.socket.close();
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                if (result.status === 0 && data) {
                    if (vm.node.code === 'RingPieChartWidget' && nodeData.yData.length > 1 && !vm.showDrill) {
                        vm.setRingPieData(data);
                        vm.chartData = {dimsCodes : data.dimsCodes, dims: data.columns.dims, rows: await vm.setRowsData(data)};
                    } else {
                        vm.chartData = data;
                        delete vm.chartSettings.level;
                        vm.extend.series = {
                            name: data.columns[1],
                            type: "pie",
                            radius: [0, 0],
                            center: ['50%', '55%'],
                            animationThreshold: 500,
                            avoidLabelOverlap: false,
                            minShowLabelAngle: 8,
                            label: {
                                show: true,
                                position: 'outside'
                            },
                        };
                    }
                    vm.setChartExtend(nodeData.style);
                    vm.set_digital_format(nodeData.xData);
                    vm.data_empty = false; //没有数据为true
                    vm.loading = false;
                } else {
                    vm.data_empty = true; //没有数据为true
                    vm.loading = false;
                    vm.chartData = {};
                }
            },
        }

    }
</script>

<style scoped lang="less" src="./css/chart.less"></style>
