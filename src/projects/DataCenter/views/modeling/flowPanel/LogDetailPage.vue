<template>
    <div class="logDetailPanel" v-loading="loading">
        <div class="ce-log__content" ref="logContent">
            <ul class="ce-log__wrap">
                <pre class="ce-log__item ce-log__info" v-if="logList" v-html="logList"></pre>
                <li v-if="!logList.length" class="ce-log__item ce-log__nodata">{{noLog}}</li>
            </ul>
        </div>
    </div>
</template>

<script>

    export default {
        name: "LogDetailPanel",
        props: {
            row: Object,
        },
        data() {
            return {
                noLog: '暂无数据',
                logList: "", //日志内容
                isNowrap: false,
                loading: false,
            }
        },
        methods: {
            initLogList() {
                const vm = this, {$services, row, isTypeData } = vm;
                vm.loading = true;
                $services("modeling").getTransJobDetail({
                    detailId: row.taskId,
                    settings: vm,
                }).then((res) => {
                    const result = res.data.data;
                    if(res.data.status === 0 && result) {
                        vm.logList = isTypeData(result , "String") ?
                            result.replace(/\n /g,'<br/> ') :
                            JSON.stringify(result, null, 2);
                    }
                })
            },
        },
        created() {
            this.initLogList();
        }
    }
</script>

<style scoped>
    .logDetailPanel {
        vertical-align: middle;
    }

    .ce-log__content {
        margin-top: 10px;
        height: 450px;
        overflow: auto;
        background: #333;
    }

    .ce-log__wrap {
        padding: 12px;
    }

    .ce-log__item {
        padding: 4px 2px;
        white-space: pre-wrap;
    }

    .ce-log__item:hover {
        /*background: #606669;*/
    }

    .ce-log__nowrap {
        white-space: nowrap;
    }

    .ce-log__error {
        color: #f56c6c;
    }

    .ce-log__warn {
        color: #e6a23c;
    }

    .ce-log__info {
        color: #fff;
    }


    .ce-log__trace {
        color: #e1f3d8;
    }

    .ce-log__nodata {
        color: #fff;
        text-align: center;
        padding: 20px;
    }
</style>
