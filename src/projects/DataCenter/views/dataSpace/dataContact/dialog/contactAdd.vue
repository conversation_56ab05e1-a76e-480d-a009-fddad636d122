<template>
    <common-dialog custom-class="addCustomBody" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="clearD"
                   top="10vh"
                   v-loading="settings.loading"
    >
        <div slot="title"><span class="el-dialog__title">{{ title }}</span>
            <help-document :code="'dataConnectionAddDataSource'"></help-document>
        </div>
        <div class="add-data" v-if="reNew">
            <main class="add-data__main">
                <div class="steps">
                    <div :class="['steps__item', active === 1 && 'is-active']">
                        <span class="dg-iconp icon-f-circle-check iconCheck" v-show="active === 2"></span>
                        <span class="steps__number" v-show="active === 1">1</span>
                        <span class="steps__text">配置数据源信息</span>
                    </div>
                    <div :class="['steps__item', active === 2 && 'is-active']">
                        <span class="steps__number">2</span>
                        <span class="steps__text">选择数据表（可选）</span>
                    </div>
                </div>
                <div class="add-data__wrap">
                    <template ref="formAdd">
                        <div v-show="active === 1" class="firstStyle">

                            <Choice ref="choice" :isAdd="true" :keyId="dataSourceType"
                                    @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion1"
                                    :zkVersionList="zkVersionList1" @serachIp="serachIp">
                                <el-form slot="customChoice">
                                    <el-form-item label-width="120px" label="保存到目录:" required>
                                        <dg-tree-drop
                                                class="expect-select"
                                                ref="select_drop"
                                                :props="defaultProps"
                                                v-model="catalogInfo.data.id"
                                                :data="nodeList"
                                                :tree-props="treeBind"
                                                filterable
                                                :filterNodeMethod="filterNode"
                                                :default-expanded-keys="defaultExpandedKeys"
                                                @current-change="changeCatalog"
                                        >
                                        </dg-tree-drop>
                                    </el-form-item>
                                    <!-- <el-form-item label-width="120px" label="数据源类型:" required>
                                        <el-select size="mini" v-model="dataSourceType" readonly
                                                   :disabled="saveId?true:false">
                                            <el-option v-for="(item , index) in dataSourcechoices" :key="index"
                                                       :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                    </el-form-item> -->
                                    <!--<el-form-item label-width="120px" label="连接类型:" required v-if="lineTypes.length > 0">-->
                                    <!--<el-select size="mini" v-model="lineType" readonly :disabled="saveId?true:false">-->
                                    <!--<el-option v-for="(item, index) in lineTypes" :key ="index" :label="item.label" :value="item.value"></el-option>-->
                                    <!--</el-select>-->
                                    <!--</el-form-item>-->
                                </el-form>
                            </Choice>
                        </div>
                    </template>
                    <template>
                        <data-table ref="dataTable" :treeNode="selectInfo" v-if="active === 2"
                                    @close="clearD"></data-table>
                    </template>
                </div>
            </main>
        </div>
        <div slot="footer">
            <el-button @click="clearD" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button type="primary" @click="testConnect" size="mini">{{ btnTestTxt }}</el-button>
            <el-button v-if="active === 1" @click="handleNext" size="mini" :disabled="!saveId">下一步</el-button>
            <el-button v-if="active === 2" @click="lastStep" size="mini">上一步</el-button>
            <el-button @click="saveConnect" type="primary" size="mini">{{ btnSaveTxt }}</el-button>
        </div>
    </common-dialog>
</template>
<script>
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import Choice from "../choice/Choice";
import SignedSource from "../dialog/signed-source";
import {mapGetters} from "vuex"
import dataTable from "./dataTable";
import {treeMethods} from "@/api/treeNameCheck/treeName"

export default {
    name: "contactAdd",
    mixins: [commonMixins, servicesMixins],
    computed: {
        ...mapGetters(["userInfo"])
    },
    components: {Choice, SignedSource, dataTable},
    props: ['selectTreeNode', 'showPage', 'tabActive' ],
    watch: {
        dataSourceType(val) {
            this.lineTypes = [];
            this.lineTypes.push({
                label: 'jdbc',
                value: 'jdbc',
            })
            this.lineType = "jdbc";
            // for(let i = 0; i < this.jdbcTeam.length; i++) {
            //     if(val === this.jdbcTeam[i]) {
            //         this.lineTypes = []
            //         this.lineType = "";
            //     }
            // }
            this.getList();
        }
    },
    data() {
        return {
            treeBind: {
                "default-expand-all": true,
                nodeKey: "id",
                isFrozenRoot: true,
            },
            defaultProps: {
                value: 'id',
                label: 'name',
                children: 'children',
                title: 'name',
            },
            catalogInfo: {},
            nodeData: [],
            reNew: false,
            title: '添加数据源',
            width: '800px',
            btnCancelTxt: '取消',
            btnSaveTxt: '保存',
            btnTestTxt: '测试连接',
            visible: false,
            active: 1,
            machineVoList: [],
            zkVersionList1: [],
            dataVersion1: [],
            jdbcTeam: ['zookeeper', 'Elasticsearch', 'hbase'],
            dataSourceType: "GREENPLUM",
            lineType: "jdbc",
            lineTypes: [
                {
                    label: 'jdbc',
                    value: 'jdbc',
                }
            ],
            dataSetInfo: {
                hostIP: "",
                dbSchemaName: "",
                port: "",
                dbName: "",
                userName: "",
                type: "",
                dataSourceName: "",
            },
            iframeData: {},
            data: [
                {
                    label: "v2.1.0",
                    value: 1
                },
                {
                    label: "v2.1.2",
                    value: 2
                }
            ],
            rules: {
                databaseType: [{required: true, message: "请输入数据库类型", trigger: "change"}],
                databaseName: [{required: true, message: "请输入数据库名称", trigger: "blur"}],
                userName: [{required: true, message: "请输入用户名", trigger: "blur"}],
                ip: [{required: true, message: "请输入主机IP", trigger: "blur"}],
                databaseExample: [{required: true, message: "请输入数据库实例", trigger: "blur"}],
                port: [{required: true, message: "请输入端口号", trigger: "blur"}],
                version: [{required: true, message: "请选择版本", trigger: "change"}]
            },
            isSave: false,
            selectInfo: {
                instanceType: '',
                catalogId: '',
                id: '',
                schemaId: '',
                instanceId: ''
            },
            saveId: '',
            nodeList: [],
            typeList: ['hbase', 'Elasticsearch', 'ELASTICSEARCH', 'hive', 'Hive'],
            defaultExpandedKeys: [],
            dataSourcechoices: [],
        };
    },
    methods: {
        ...treeMethods.methods,
        show(dataType) {
            const vm = this;
            if(dataType)this.dataSourceType = dataType;//上一步选择的数据源类型
            vm.reNew = true;
            vm.visible = true;
            vm.active = 1;
            this.selectTreeNode && this.selectTreeNode.id ? vm.catalogInfo = this.selectTreeNode : vm.catalogInfo = {data: {id: null}};
            vm.defaultExpandedKeys = [];
            this.getTree();
        },
        /**
         * 设置 树节点数据
         * @param data
         * @param level
         * @return {*}
         */
        setTreeNode(data, level) {
            const vm = this, {dirLevel} = vm;
            let child_l = level + 1;
            return data.map(item => {
                let disabled = dirLevel !== undefined ? level >= dirLevel : false;
                let children = item.children && item.children.length && !disabled ? vm.setTreeNode(item.children, child_l) : [];
                let child = [];
                children.forEach(n => {
                    if (!n.schemaId)
                        child.push(n)
                })
                return {
                    id: item.id,
                    label: item.name,
                    name: item.name,
                    //icon ,
                    children: child,
                    isParent: true,
                    pId: item.pId === "-1" ? "0" : item.pId,
                    dirType: item.dirType,
                    operateTime: item.operateTime,
                    schemaId: item.schemaId,
                    level,
                    isActive: false
                }
            });
        },
        getTree() {
            const vm = this, {dataSpaceServices, dataSpaceMock, settings, dirId} = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            settings.loading = true;
            vm.nodeList = [];
            if (this.tabActive === 'myData') {
                services.queryDirTree('', '', '', settings).then(res => {
                    if (res.data.status === 0) {
                        if (!res.data.data || res.data.data.length === 0) return;
                        let result = res.data.data;
                        vm.nodeList = vm.setTreeNode(result, 1);
                        vm.nodeList = vm.treeSortByWay(vm.nodeList, 'operateTime' , 'sortInTime' , false)
                        vm.catalogInfo = vm.selectTreeNode && vm.selectTreeNode.id && vm.selectTreeNode.level !== 1 ? vm.selectTreeNode :
                            vm.selectTreeNode.childNodes && vm.selectTreeNode.childNodes.length ?
                                vm.selectTreeNode.childNodes[0] : {data: {id: ""}};
                        vm.getSelectNode();
                    }
                })
            } else {
                vm.sourceDatasetTree = [];
                services.getDataSourceTree(settings, vm.isLogic).then(res => {
                    if (res.data.status === 0) {
                        if (!res.data.data || res.data.data.length === 0) return;
                        vm.nodeList = vm.setTreeNode(res.data.data, 1);
                        vm.getSelectNode();
                    }
                });
            }
        },
        getSelectNode() {
            const vm = this;
            if (vm.catalogInfo && vm.catalogInfo.data.id) {
                vm.defaultExpandedKeys = [vm.catalogInfo.data.id];
                vm.$nextTick(() => {
                    vm.$refs.select_drop ? vm.$refs.select_drop.setCurrentKey(vm.catalogInfo.data.id) : '';
                })
            }
        },
        clearD() {
            this.dataSourceType = "GREENPLUM";
            this.lineType = "jdbc";
            this.active = 1;
            this.reNew = false;
            this.visible = false;
            if (this.saveId) {
                this.$emit("refresh");
            }
            this.saveId = "";
        },
        changeCatalog(data, nodes) {
            this.catalogInfo = nodes;
            // this.catalogInfo=this.catalogInfo;
            // if(nodes.level!=1)
            //     this.catalogInfo=nodes;
            // else{
            //     this.$nextTick(()=>{
            //         this.$refs.select_drop.setCurrentKey(this.catalogInfo.data.id);
            //     })
            // }
        },
        //下一步
        async handleNext() {
            const vm = this;
            if (vm.saveId) {
                await vm.saveConnect();
                vm.active = 2;
            } else
                vm.active = 2;
        },
        //上一步
        lastStep() {
            this.$refs.choice.changeDataSet(this.$refs.choice.getResult())
            this.active = 1;
        },
        serachIp(val) {
            const vm = this, {dataSpaceServices, dataSpaceMock} = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            services.serchMachine(val).then(res => {
                if (res.data.status === 0) {
                    vm.machineVoList = [];
                    if (res.data.data.id !== null && res.data.data.id !== undefined) {
                        vm.machineVoList.push({
                            machineId: res.data.id,
                            ip: res.data.ipAddress,
                            name: res.data.machineName || res.data.ipAddress,
                            port: "",
                            isMaster: "1"
                        })
                    }
                }
            })
        },
        saveSource() {
            const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
            let service = vm.getServices(dataSpaceServices, dataSpaceMock),
                    result = vm.$refs.signed.getResult();
            this.$refs.signed.validate(valid => {
                if (valid) {
                    let busiDirId = vm.treeParentId(result.treeNode.parent);
                    let classifyId = result.nodeData.id;
                    let sourceData = {
                        classifyId: result.nodeData.id,
                        dbType: "",
                        domainDbType: result.baseType,
                        name: result.name,
                        otherElementId: result.schemaId,
                        softwareId: result.schemaId,
                        dataSourceName: result.dataSourceName
                    };
                    vm.signSource(service, classifyId, busiDirId, sourceData);
                }
            })
        },
        testConnect() {
            const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            let dataSourceVO = JSON.parse(JSON.stringify(vm.dataSetInfo));
            dataSourceVO.connType = vm.lineType;
            dataSourceVO.dbType = vm.dataSourceType;
            settings.loading = true;
            services.checkConnect(dataSourceVO, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data === false) vm.$message.error("连接失败");
                    else vm.$message.success("连接成功");
                }
            })
        },
        async checkConnectRepeat() {
            const vm = this, {$services, catalogInfo, dataSetInfo} = vm;
            let service = $services("dataContact");
            let params = {classifyId: catalogInfo.data.id, dataSourceName: dataSetInfo.dbName};
            return await service.checkConnectRepeat(params).then(res => {
                if (res.data.code === 0) {
                    let result = res.data.data;
                    if (!result) vm.$message.error("数据源名称已存在!")
                    return result;
                }
            }).catch(() => {
                return false;
            })
        },
        async saveConnect() {
            if (!this.catalogInfo.data.id)
                return this.$message.warning('请先选择目录！');
            if (this.catalogInfo.level === 1)
                return this.$message.warning('当前目录是第一层级目录，第一层级目录不支持新增数据源！');
            if (this.$refs.choice.valiatorRule() === true) {
                const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
                let services = vm.getServices(dataSpaceServices, dataSpaceMock);
                let dataSourceVO = JSON.parse(JSON.stringify(vm.dataSetInfo));
                if (vm.machineVoList.length > 0) vm.machineVoList.port = dataSourceVO.port;
                dataSourceVO.machineVoList = vm.machineVoList;
                dataSourceVO.connType = vm.lineType;
                dataSourceVO.dbType = vm.dataSourceType;
                dataSourceVO.clusterName = dataSourceVO.port;
                //settings.loading = true;
                if (vm.active === 1) {
                    if (vm.saveId) {//修改
                        dataSourceVO.id = vm.saveId;
                        services.updateDataSet(dataSourceVO, settings).then(res => {
                            if (res.data.status === 0) {
                                if (!vm.isSave) {
                                    vm.$message.success("保存成功");
                                }
                            }
                        })
                    } else {//新增
                        let res = await vm.checkConnectRepeat();
                        if (!res) return;
                        dataSourceVO.classifyId = this.catalogInfo.data.id;
                        services.saveConnect(dataSourceVO).then(res => {
                            let info = res.data.data || {};
                            if (res.data.status === 0) {
                                vm.$refs.choice.changeDataSet(this.$refs.choice.getResult());
                                vm.selectInfo.schemaId = info.schemaId;
                                vm.selectInfo.catalogId = info.catalogId;
                                vm.selectInfo.instanceId = info.instanceId;
                                vm.saveId = info.catalogId || info.instanceId;
                                info.catalogId &&  vm.saveDataSet(info.catalogId);
                                info.instanceId && vm.saveDataSet(info.instanceId);
                                // if (info.catalogId !== null && info.catalogId !== undefined) {
                                //     vm.saveDataSet(info.catalogId);
                                // } else if (info.instanceId !== null && info.instanceId !== undefined) {
                                //     vm.saveDataSet(info.instanceId);
                                // }
                            }
                        })
                    }
                } else {
                    this.$refs.dataTable.save();
                    //this.clearD();
                }
            }
        },
        getList() {
            const vm = this, {dataSpaceServices, dataSpaceMock} = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            let type = vm.dataSourceType.toUpperCase() == 'ELASTICSEARCH' ? "Elasticsearch" : vm.dataSourceType
            services.getdataSourceList(type).then(res => {
                if (res.data.status === 0) {
                    if(type.toUpperCase() === 'KAFKA'){
                        res.data.data.versionList = res.data.data.versionList.filter(n=>n.id !== '4028b8855c3e5aef015c3e5b903801c1')
                    }

                    vm.dataVersion1 = res.data.data.versionList || [];
                    vm.zkVersionList1 = res.data.data.zkVersionList || [];
                }
            })
        },
        changeDataSetInfo(val) {
            this.dataSetInfo = val;
        },
        receiveMessage(e) {
            this.saveDataSet(e.data.data);
        },
        treeParentId(tree) {
            if (tree.level == 0) {
                return null;
            } else if (tree.level == 1) {
                return tree.data.id;
            } else {
                return this.treeParentId(tree.parent)
            }
        },
        async saveDataSet(data) {
            if (!data) return;
            let dataSet = await this.getDataSetInfo(data);
            if (dataSet === null) {
                return;
            }
            this.selectInfo.instanceType = dataSet.dbType;
            window.removeEventListener('message', this.receiveMessage);
            let busiDirId = this.treeParentId(this.catalogInfo.parent);
            let classifyId = this.catalogInfo.data.id;

            let sourceData = {
                classifyId: this.catalogInfo.data.id,
                dbType: "",
                domainDbType: dataSet.dbType,
                name: dataSet.name,
                otherElementId: dataSet.schemaId,
                softwareId: dataSet.id,
                dataSourceName: dataSet.name

            };
            const vm = this, {dataSpaceServices, dataSpaceMock} = this;
            let service = vm.getServices(dataSpaceServices, dataSpaceMock);
            vm.signSource(service, classifyId, busiDirId, sourceData);
        },
        signSource(service, classifyId, busiDirId, sourceData) {
            const vm = this, {settings} = this;
            let isAddExistDataSource = vm.showPage === "sign" ? false : true;
            settings.loading = true;
            service.saveDWDBInstance(classifyId, busiDirId, sourceData, '', isAddExistDataSource).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.selectInfo.id = result.id;
                    result.catalogId = sourceData.softwareId;

                    for (let i = 0; i < vm.typeList.length; i++) {
                        if (sourceData.domainDbType === vm.typeList[i]) {
                            delete result.catalogId
                            vm.selectInfo.schemaId = sourceData.otherElementId;
                            //result.schemaId = sourceData.otherElementId;
                            break;
                        }
                    }
                    let dataSetAuthVo = {
                        dataObjectVos: [{name: result.name, code: result.id}],
                        roleVos: [],
                        userVos: [{id: vm.userInfo.id}]
                    };
                    service.dataSetAuthRegister(dataSetAuthVo).then(res => {
                        if (res.data.status === 0) {
                            service.addDataSetAuth(dataSetAuthVo, settings).then(res => {
                                if (res.data.status === 0) {
                                    vm.isSave = true;
                                    let msg = "数据源添加成功！";
                                    if (result.msg !== null && result.msg !== "") {
                                        msg += result.msg;
                                    }
                                    vm.$message.success(msg);
                                    if (vm.showPage == 'signed') {
                                        vm.clearD();
                                    }
                                }
                            })
                        }
                    })

                } else {
                    settings.loading = false;
                }
            })
        },
        async getDataSetInfo(data) {
            let resData = null;
            const vm = this, {dataSpaceServices, dataSpaceMock} = this;
            let service = vm.getServices(dataSpaceServices, dataSpaceMock);
            await service.getDataSetInfo(data).then(res => {
                if (res.data.status === 0) {
                    resData = res.data.data;
                }
            });
            return resData;

        },
        //获取数据源类型
        allType() {
            const vm = this, {dataSpaceServices, dataSpaceMock} = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            services.queryAllTypes().then(res => {
                if (res.data.status === 0) {
                    vm.dataSourcechoices = [];
                    Object.keys(res.data.data).forEach(n => {
                        if (n.toUpperCase() != "HBASE" && n != '') {
                            vm.dataSourcechoices.push({label: n, value: res.data.data[n]})
                        }
                    })
                    vm.dataSourceType = vm.dataSourcechoices.length ? vm.dataSourcechoices[0].value : "";
                    vm.$emit("updateAllTypes", vm.dataSourcechoices);
                }
            })

        },
    },
    created() {
        this.getList();
        this.allType();
    },
    destroyed() {
        window.removeEventListener('message', this.receiveMessage);
    }
};
</script>
<style lang="less" scoped>
/deep/ .addCustomBody {
    height: 75%;

    .el-dialog__body {
        height: calc(100% - 140px);
        padding-bottom: 0;
    }

    @media screen and (max-width: 1365px) {
        .el-dialog__body {
            height: calc(100% - 110px);
            padding-bottom: 0;
        }
    }
}

.add-data {
    height: 100%;

    &__main {
        height: 100%;

        .iconCheck {
            font-size: 32px;
            height: 32px;
            line-height: 32px;
            color: #1890ff;
        }
    }

    &__wrap {
        height: calc(100% - 50px);
        padding-top: 14px;

        .firstStyle {
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }
    }

    .selectSource {
        //height:50%;
    }
}

.steps {
    display: flex;
    align-items: center;

    &__item {
        display: flex;
        align-items: center;

        &.is-active {
            .steps__number {
                color: #fff;
                background-color: #1890ff;
                border-color: #1890ff;
            }

            .steps__text {
                color: rgba(0, 0, 0, 0.85);
                font-weight: bold;
            }
        }

        &:not(:last-child) {
            &::after {
                content: "";
                display: inline-block;
                width: 77px;
                height: 1px;
                background-color: rgba(0, 0, 0, 0.15);
                margin: 0 16px;
            }
        }
    }

    &__number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 1px solid rgba(0, 0, 0, 0.15);
        margin-right: 14px;
    }

    &__text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
    }
}
</style>
