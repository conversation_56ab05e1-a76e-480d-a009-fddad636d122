<template>
    <div class="cond-filter__content">
        <!-- <el-button type="text" class="filter-box__add" @click="handleClickAddGroup">
            <i class="el-icon-circle-plus-outline"></i><span>新增</span>
        </el-button> -->
        <div class="cond-filter__form" v-if="isAdd">
            <label class="is-required">设置过滤条件：</label>
            <el-button type="primary" size="small" @click="addFilter">添加条件</el-button>
        </div>
        <div class="filter-box__content" v-else>
            <div class="filter-group" v-for="(item, idx) in conditions" :key="idx">
                <div class="filter-group__logic" v-if="idx == 0">
                    <span class="logic-select--thin">全部</span>
                </div>
                <div class="filter-group__logic" v-if="idx !== 0">
                    <dg-select class="logic-select logic-select--thin" v-model="item.connectionType" :data="radioList">
                    </dg-select>
                </div>

                <div class="filter-group__main">
                    <div class="filter-group__header">
                        <span>满足以下</span>
                        <dg-select
                                class="logic-select"
                                v-model="item.relation"
                                :data="childrenLogics"
                        ></dg-select>
                        <span>条件</span>
                        <div class="filter-group__btns icon-group">
                            <el-link
                                    :underline="false"
                                    title="删除"
                                    @click="handleDeleteGroup(item, idx)"
                            >
                                <i class="el-icon-delete"></i>
                            </el-link>
                            <el-link :underline="false" title="新增" @click="handleClickAddGroup">
                                <i class="el-icon-circle-plus-outline"></i>
                            </el-link>
                        </div>
                    </div>

                    <div class="filter-item" v-for="(child, index) in item.children" :key="index">
                        <dg-select
                                class="filter-item__field"
                                v-model="child.field"
                                :data="filterList"
                                placeholder="请选择字段"
                                @change="fieldSelected(arguments , idx,index)"
                        ></dg-select>
                        <dg-select class="filter-item__operator" v-model="child.operator"
                                   @change="operatorChange(idx,index)"
                                   :data="child.format === 'TEXT' ? txt_option : child.format === 'NUMBER'?  num_option :   time_option "
                        >
                        </dg-select>

                        <div v-if="child.operator !== conTxt.isNull && child.operator !== conTxt.norNull">
                            <dg-select class="filter-item__operator" v-model="child.variable"
                                       v-if="transInputByType(child)"
                                       :data="variableList"
                                       @change="variableChange(idx,index)"
                            >
                            </dg-select>
                            <div v-if="transInputByType(child)" style="display: inline-flex">
                                <dg-select
                                        v-if="child.variable ==='true'"
                                        class="filter-item__value"
                                        v-model="child.inputVal"
                                        filterable
                                        :data="valueColumns"
                                        @change="filterChange(idx,index)"
                                ></dg-select>
                                <el-input style="width:300px" v-else v-model.trim="child.inputVal" :maxlength="100" @input="changeVal($event)" :placeholder="conTxt.i_placeholder"></el-input>

                            </div>
                            <el-date-picker
                                    v-if="!transInputByType(child)"
                                    v-model="child.time"
                                    type="datetime"
                                    placeholder="选择日期时间">
                            </el-date-picker>

                            <!--<template v-if="child.format === 'TEXT'">-->
                                <!--<el-input v-model.trim="child.inputVal" :maxlength="100" :disabled="disabled" @input="changeVal($event)" :placeholder="conTxt.i_placeholder"></el-input>-->
                            <!--</template>-->
                            <!--<template v-else-if="child.format === 'NUMBER'">-->
                                <!--<template-->
                                        <!--v-if="child.operator === conTxt.in || child.operator === conTxt.norIn"-->
                                <!--&gt;-->
                                    <!--<div class="inStyle">-->
                                        <!--<div class="number-select-left number-select">-->
                                            <!--<el-input-->
                                                    <!--:disabled="disabled"-->
                                                    <!--:placeholder="conTxt.n_placeholder"-->
                                                    <!--@input="changeVal($event)"-->
                                                    <!--v-model.trim="child.spaceVal1"-->
                                                    <!--class="input-with-select"-->
                                            <!--&gt;-->
                                                <!--<dg-select-->
                                                        <!--class="w60"-->
                                                        <!--:disabled="disabled"-->
                                                        <!--v-model="child.select1"-->
                                                        <!--slot="append"-->
                                                        <!--:data="logic_options"-->
                                                <!--&gt;</dg-select>-->
                                            <!--</el-input>-->
                                        <!--</div>-->
                                        <!--<div class="number-select number-select-left lh28 mr10 ml10" >{{numTip}}</div>-->
                                        <!--<div class="number-select">-->
                                            <!--<el-input-->
                                                    <!--:disabled="disabled"-->
                                                    <!--v-model.trim="child.spaceVal2"-->
                                                    <!--@input="changeVal($event)"-->
                                                    <!--:placeholder="conTxt.n_placeholder"-->
                                                    <!--class="input-with-select"-->
                                            <!--&gt;-->
                                                <!--<dg-select-->
                                                        <!--class="w60"-->
                                                        <!--:disabled="disabled"-->
                                                        <!--v-model="child.select2"-->
                                                        <!--slot="prepend"-->
                                                        <!--:data="logic_options1"-->
                                                <!--&gt;</dg-select>-->
                                            <!--</el-input>-->
                                        <!--</div>-->
                                    <!--</div>-->

                                <!--</template>-->
                                <!--<template v-else>-->
                                    <!--<el-input-number v-model="child.spaceVal" @change="numChecked($event , child)" :disabled="disabled"></el-input-number>-->
                                <!--</template>-->
                            <!--</template>-->
                            <!--<template v-else>-->
                                <!--<template v-if="child.operator === conTxt.in || child.operator === conTxt.norIn">-->
                                    <!--<dg-date-picker-->
                                            <!--v-if="child.reNew"-->
                                            <!--v-model="child.timeSpace"-->
                                            <!--:disabled="disabled"-->
                                            <!--unlink-panels-->
                                            <!--type="daterange"-->
                                            <!--range-separator="至"-->
                                            <!--start-placeholder="开始日期"-->
                                            <!--end-placeholder="结束日期"-->
                                            <!--:value-format="dateFormat"-->
                                    <!--&gt;</dg-date-picker>-->
                                <!--</template>-->
                                <!--<template v-else>-->
                                    <!--<dg-date-picker-->
                                            <!--v-if="child.reNew"-->
                                            <!--:disabled="disabled"-->
                                            <!--unlink-panels-->
                                            <!--v-model="child.time"-->
                                            <!--type="date"-->
                                            <!--placeholder="选择日期"-->
                                            <!--:value-format="dateFormat"-->
                                    <!--&gt;</dg-date-picker>-->
                                <!--</template>-->
                            <!--</template>-->
                        </div>
                        <!--<el-input-->
                                <!--class="filter-item__value"-->
                                <!--v-model="child.value"-->
                                <!--placeholder="请输入值"-->
                        <!--&gt;</el-input>-->
                        <div class="filter-item__btns icon-group">
                            <el-link :underline="false" title="新增" @click="handleAddChild(item.children,idx)">
                                <i class="el-icon-circle-plus-outline"></i>
                            </el-link>
                            <el-link
                                    v-if="item.children.length !== 1"
                                    :underline="false"
                                    title="删除"
                                    @click="handleDeleteChild(item.children, child, index)"
                            >
                                <i class="el-icon-close"></i>
                            </el-link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "filterInfo",
        props:{
            echoCondition:Array,
            filterList:Array,
        },
        data(){
            return{
                conditions: [],
                radioList: [
                    { label: "且", value: "and" },
                    { label: "或", value: "or" }
                ],
                childrenLogics: [
                    { label: "全部", value: "and" },
                    { label: "部分", value: "or" }
                ],
                isAdd: true,
                variableList:[
                    { label:'常量' , value:"false" },
                    { label:'字段' , value:"true" },
                ],
                num_option : [
                    {value: "大于",},
                    {value: "小于"},
                    {value: "等于"},
                    {value: "不等于"},
                    {value: "大于等于"},
                    {value: "小于等于"},
                    {value: "不为空"},
                    {value: "为空"},
                ],
                time_option :  [
                    {value: "大于",},
                    {value: "小于"},
                    {value: "等于"},
                    { value: "不等于"},
                    {value: "大于等于"},
                    {value: "小于等于"},
                    {value: "不为空"},
                    {value: "为空"},
                ],
                txt_option : [
                    {value: "大于",},
                    {value: "小于"},
                    {value: "等于"},
                    {value: "不等于"},
                    {value: "大于等于"},
                    {value: "小于等于"},
                    {value: "包含"},
                    {value: "不包含"},
                    {value: "不为空"},
                    {value: "为空"},
                    {value: "属于"},
                    {value: "不属于"},
                    {value: "开头是"},
                    {value: "开头不是"},
                    {value: "结尾是"},
                    {value: "结尾不是"}
                ],
                conTxt: {
                    addFilter: "添加过滤条件" ,
                    isNull : "为空",
                    norNull : "不为空",
                    i_placeholder : "请输入内容(限100个字符)" ,
                    in : "介于" ,
                    norIn : "不介于",
                    n_placeholder : "请输入数值"
                },
                valueColumns:[],
                logic_options: [
                    {
                        label: ">",
                        value: "<"
                    },
                    {
                        label: "≥",
                        value: "<="
                    },
                    {
                        label: "<",
                        value: ">"
                    },
                    {
                        label: "≤",
                        value: ">="
                    },
                    {
                        label: "=",
                        value: "="
                    },
                    {
                        label: "≠",
                        value: "!="
                    }
                ],
                logic_options1 : [
                    {
                        label: ">",
                        value: ">"
                    },
                    {
                        label: "≥",
                        value: ">="
                    },
                    {
                        label: "<",
                        value: "<"
                    },
                    {
                        label: "≤",
                        value: "<="
                    },
                    {
                        label: "=",
                        value: "="
                    },
                    {
                        label: "≠",
                        value: "!="
                    }
                ],
                disabled : false ,
                numTip : "值",
                dateFormat : "yyyy-MM-dd",
            }
        },
        watch:{
            "echoCondition":function(){
                if(this.echoCondition&&this.echoCondition.length){
                    this.setFilterInfo();
                }
                else{
                    this.isAdd=true;
                }
            },
            "filterList":function(){
                this.setCurrentValueColumns();
            }
        },
        methods:{
            setCurrentValueColumns(format){
                this.valueColumns = format ? this.filterList.filter( n=> n.format == format ) : this.filterList;
            },
            setFilterInfo(){
                const vm=this;
                vm.isAdd=false;
                vm.conditions=[]
                for (let i = 0; i < vm.echoCondition.length; i++) {
                    let children = [];
                    vm.echoCondition[i].filterFieldInfo.forEach(n => {
                        let row = {
                            id : "",
                            fieldName:'',
                            fieldCode:'',
                            operator:'',
                            startValue:'',
                            startOperator:'',
                            endVale:'',
                            endOperator:'',
                            inputValue:'' ,
                            format : n.format,
                            reNew:true,
                        };
                        row.name = n.fieldName;
                        row.field  = n.fieldCode;
                        row.operator = n.operator;
                        row.id =n.id;
                        row.variable = n.variable ? n.variable.toString() : 'false';
                        switch (n.format) {
                            case 'TEXT' :
                                row.inputVal = n.inputValue;
                                break;
                            case 'NUMBER':
                                row.spaceVal = n.inputValue;
                                row.select1 = n.startOperator;
                                row.select2 = n.endOperator;
                                row.spaceVal1 = n.startValue;
                                row.spaceVal2 = n.endValue;
                                break;
                            default:
                                row.timeSpace=[];
                                row.time = n.inputValue;
                                row.timeSpace[0] = n.startValue?n.startValue:"";
                                row.timeSpace[1] = n.endValue?n.endValue:"";
                                break;
                        }

                        children.push(row);
                    });
                    vm.conditions.push({
                        relation:vm.echoCondition[i].groupType,
                        connectionType: i != 0 ? vm.echoCondition[i-1].connectionType : 'and',
                        children : children
                    });
                }

            },
            refreshFilter(){
                this.conditions=[];
                this.isAdd=true;
            },
            addFilter(){
                this.conditions.push(
                    {
                        relation:'and',
                        connectionType:'and',
                        children: [
                            {
                                id:'',
                                field: "",
                                operator: "",
                                value: "",
                                reNew:true,
                                format:"TEXT",
                                variable:''
                            }
                        ]
                    }
                )
                this.isAdd=false;
            },
            fieldSelected(params, index, inx) {
                let data= params[1];
                const vm  = this;
                vm.conditions[index].children[inx].format = data.format;
                vm.conditions[index].children[inx].name = data.name;
                vm.conditions[index].children[inx].operator = "";
                vm.conditions[index].children[inx].label = data.label;
                vm.conditions[index].children[inx].id = data.id;
                vm.conditions[index].children[inx].variable = "false";
                vm.conditions[index].children[inx].inputVal = "";
                vm.initLogic(data.format ,index , inx);
                vm.setCurrentValueColumns(data.format);
            },
            initLogic(format ,index, inx  ){
                const vm  = this;
                switch (format) {
                    case 'TEXT' :
                        vm.conditions[index].children[inx].operator = vm.txt_option[0].value;
                        break;
                    case 'NUMBER' :
                        vm.conditions[index].children[inx].operator = vm.num_option[0].value;
                        break;
                    default:
                        vm.conditions[index].children[inx].operator = vm.time_option[0].value;
                        break;
                }
            },
            transInputByType(child){
                if (child.format === 'TEXT' || child.format === 'NUMBER') {
                    return true;
                }else {
                    return false;
                }
            },
            operatorChange(index ,inx){
                const vm  = this;
                vm.resetValue(index,inx);
            },
            variableChange(index ,inx){
                const vm = this;
                let row = vm.conditions[index].children[inx];
                row.inputVal = "";
            },
            filterChange(){
                this.$forceUpdate()
            },
            resetValue(index,inx){
                const vm = this;
                let row = vm.conditions[index].children[inx];
                row.inputVal = "";
                row.spaceVal = "";
                row.spaceVal1 = "";
                row.spaceVal2 = "";
                row.time = "";
                row.timeSpace = "";
                row.reNew = true;
            },

            validate(){
                const vm = this, {conditions} = this;
                let result = true;
                for(let i = 0;i < conditions.length; i++){
                    let children = conditions[i].children;
                    for(let j = 0; j < children.length; j++){
                        let ins = children[j];
                        if(!ins.id) {
                            result=false;
                            vm.$message.warning("请选择过滤条件!");
                            break;
                        }
                        switch (ins.format) {
                            case 'TEXT' :
                            case 'NUMBER' :
                                if(ins.operator !== "为空" && ins.operator !== "不为空" ){
                                    result = ins.inputVal !== "" && typeof(ins.inputVal) !== "undefined";
                                }
                                break;
                            // case 'NUMBER' :
                            //     if(ins.operator !== "为空" && ins.operator !== "不为空"){
                            //         if(ins.operator === "介于" || ins.operator === "不介于"){
                            //             result = ins.spaceVal1 !== "" && ins.spaceVal2 !== ""&&typeof(ins.spaceVal1) !== "undefined"&&typeof(ins.spaceVal2) !== "undefined";
                            //         }else {
                            //             result = ins.spaceVal !== ""&& typeof(ins.spaceVal) !== "undefined";
                            //         }
                            //     }
                            //     break;
                            default:
                                if(ins.operator !== "为空" && ins.operator !== "不为空" ){
                                    if(ins.operator === "介于" || ins.operator === "不介于"){
                                        result = ins.timeSpace[0] && ins.timeSpace[1];
                                    }else {
                                        result = ins.time !== ""&& typeof(ins.time) !== "undefined";
                                    }
                                }
                                break;
                        }
                        if(!result) {
                            vm.$message.warning("过滤条件结果值不能为空!");
                            break;
                        }
                    }
                    if(!result) break;
                }
                return result;
            },
            numChecked(val , item){
                if(!val){
                    item.spaceVal = 0;
                }
            },
            changeVal(e){
                this.$forceUpdate()
            },
            /**
             * 添加子条件
             * @param children
             */
            handleAddChild(children,index) {
                const row = {
                    field:"",
                    name: "",
                    operator: "",
                    format : 'TEXT' ,
                    inputVal: "",
                    select1: ">",
                    select2: "<",
                    spaceVal: "",
                    spaceVal1: "",
                    spaceVal2: "",
                    time: "",
                    timeSpace: "",
                    reNew :true
                };
                this.$set(children, children.length, row);
                let info={ label: "", value: "" ,format:"TEXT"}
                this.fieldSelected(['' , info] ,index , children.length-1);
            },

            /**
             * 添加条件组
             **/
            handleClickAddGroup() {
                const vm = this;
                vm.conditions.push({
                    relation:'and',
                    connectionType:'and',
                    children: [
                        {
                            id:'',
                            field: "",
                            operator: "",
                            value: "",
                            reNew:true,
                            format:"TEXT",
                            variable:"",
                        }
                    ]
                });
            },

            /**
             * 删除条件组
             **/
            handleDeleteGroup(item, idx) {
                const vm = this;
                vm.conditions.splice(idx, 1);
                if(!vm.conditions.length) vm.isAdd=true;
                vm.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            },


            /**
             * 删除子条件
             * @param children
             * @param child 要删除的项
             * @param index 要删除的下标
             */
            handleDeleteChild(children, child, index) {
                children.splice(index, 1);
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }
        }
    }
</script>

<style lang="less" scoped>
    .cond-filter {
        &__content {
            padding: 0.875rem 1.875rem;
            position: relative;
            height: 100%;
        }
        .el-card {
            flex-shrink: 0;
            height: auto;
            box-sizing: border-box;
        }
        .el-card + .el-card {
            margin-top: 14px;
        }
        &__wrap {
            &-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            &-actions {
                display: flex;
                align-items: center;
            }
            &-table{
                height:100%;
            }
            &-reflash {
                text-align: center;
                //line-height: 30px;
                //width: 30px;
                //height: 30px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                border-radius: 2px;
                margin-left: 4px;
                cursor: pointer;
                i {
                    color: rgba(0, 0, 0, 0.45);
                }
                &:hover {
                    i {
                        color: #0088ff;
                    }
                }
            }

            &-list {
                padding-top: 14px;
            }
        }
    }
    // 图标按钮组
    .icon-group {
        display: flex;
        align-items: center;
        .el-link {
            line-height: 1;
            margin-right: 4px;
        }
        &:before {
            content: "";
        }
    }

    .filter-group {
        color: #0088ff;
        display: flex;
        margin-bottom: 24px;

        &__logic {
            flex: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 16px;
            padding-top: 4px;
            &:after {
                flex: 1;
                content: "";
                width: 2px;
                background-color: rgba(#0088ff, 0.12);
            }
        }
        &__main {
            flex: 1;
        }
        &__header {
            display: flex;
            align-items: center;
            .logic-select {
                margin: 0 12px;
            }
        }

        &__btns {
            margin-left: 14px;
        }
        &:not(:hover) &__btns {
            display: none;
        }

        .filter-item {
            margin-top: 14px;
        }
    }

    .filter-item {
        display: flex;
        .inStyle{
            display: inline-flex;
        }

        &__field,
        &__value {
            flex: none;
            width: 300px;
        }
        &__operator {
            flex: none;
            //min-width:84px;
            width: 130px;
            margin: 0 6px;
        }
        &__btns {
            margin-left: 14px;
            .el-link {
                font-size: 16px;
            }
        }
        &:not(:hover) &__btns {
            display: none;
        }
    }

    // 逻辑选择器样式
    .logic-select {
        width: 84px;
        /deep/.el-input__inner {
            border-color: #0088ff;
            color: #0088ff;
        }
        /deep/.el-input__icon {
            color: #0088ff;
        }

        &--thin {
            width: 42px;
            height: 24px;

            /deep/.el-input__inner {
                padding: 0 12px 0 6px;
                background-color: rgba(#0088ff, 0.12);
                border: none;
                height: 24px;
                line-height:24px;
            }
            /deep/ .el-input__suffix {
                right: 4px;
                height: 24px;
            }
            /deep/.el-input__icon {
                width: 12px;
                height: 24px;
                line-height:24px;
            }
        }
    }
</style>
