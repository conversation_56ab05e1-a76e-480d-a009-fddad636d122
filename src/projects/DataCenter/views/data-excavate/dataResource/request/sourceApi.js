/**
 * 数据资源 数据视图 接口
 * @Author: wangjt
 * @Date: 2020-05-14
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/dataModeling/";

/*
*  数据视图
 * @param
 * @returns {Promise<*>}
* */
export async function getOriginDataListByView( params ) {
    return await reqUtil.post({
        url: root + 'getOriginDataListByView',
    }, params );
}
/*
*  数据视图
 * @param
 * @returns {Promise<*>}
* */
export async function getOriginDataList( params ) {
    return await reqUtil.post({
        url: root + 'getOriginDataList',
    }, params );
}