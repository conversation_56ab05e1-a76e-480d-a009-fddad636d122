<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/4/18
-->
<template>
    <ce-list-table
            ref="table"
            :filter-text.sync="filterText"
            :t-head-data="tHeadData"
            :table-data="tableData"
            @changePage="changePage"
            :operate-icons="operateIcons"
            :filter-placeholder="filterPlaceholder"
    >
        <template slot="search">
            <el-form class="list_form" inline>
                <el-form-item v-for="(item, k) in queryForm" :key="k" :label="item.label">
                    <dg-select v-model="item.value" v-if="item.type==='select'"
                               clearable
                               :data="item.data()"
                               @change="queryList()"
                               :placeholder="selPlaceholder(item.label)"
                    ></dg-select>
                </el-form-item>
            </el-form>
        </template>
        <template #status="{row}">
            <div :style="{'color' : auditStateForKey(row.audit_state , 'color')}">
                <i :class="auditStateForKey(row.audit_state , 'icon')"></i>
                <span class="ml5">{{ auditStateForKey(row.audit_state , 'label') }}</span>
            </div>
        </template>
        <template #auditResult="{row}">
            <span >{{auditResult(row.audit_result_state)}}</span>
        </template>
    </ce-list-table>
</template>

<script>
import marketUse from "@store/modules/market/market-constant"
import CeListTable from "dc-front-plugin/src/components/CeListTable/CeListTable.vue"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";

CeListTable.mixins = [...CeListTable.mixins, commonMixins];
export default {
    name: "approvalMessage",
    mixins: [marketUse, commonMixins],
    components: {CeListTable},
    computed: {
        selPlaceholder() {
            return (text) => `请选择${text}`
        },
        auditStateForKey() {
            return (val , k) => {
                let state = {};
                for (let it of this.m_market_audit_state) {
                    state[it.value] = it[k];
                }
                return state[val] || "";
            }
        },
        auditResult(){
            return (val)=> {
                let state = {};
                for(let it of this.m_market_audit_result ){
                    state[it.value] = it.label;
                }
                return state[val] || "";
            }
        },
        ...mapGetters(["userInfo"]),
    },
    data() {
        return {
            filterText: '',
            tHeadData: [
                {
                    label: '资源类型',
                    prop: 'resourceType',
                    align: 'center',
                }, {
                    label: '资源名称',
                    prop: 'resource_name',
                    align: 'center',
                }, {
                    label: '申请人',
                    prop: 'apply_user_name',
                    align: 'center',
                }, {
                    label: '申请时间',
                    prop: 'apply_time',
                    align: 'center',
                },
                {
                    label: '审批状态',
                    prop: 'status',
                    align: 'center',
                }, {
                    label: '审批结果',
                    prop: 'auditResult',
                    align: 'center',
                },

                {
                    label: '操作',
                    prop: 'operate',
                    align: 'center',
                    width: 256,
                },
            ],
            tableData: [],
            operateIcons: [
                {
                    name: '资源详情',
                    clickFn: this.detail,
                    show: (row) => row.audit_state !== "1",
                    condition: (row, right) => true,
                }, {
                    name: '审批详情',
                    clickFn: this.approvalDetail,
                    show: (row) => this.m_market_has_audit(row.audit_state), // 已审批
                    condition: (row, right) => true,
                },
                {
                    name: '审批',
                    clickFn: this.approval,
                    show: (row) => !this.m_market_has_audit(row.audit_state) && this.userInfo && row.audit_user_id === this.userInfo.id, // 未审批
                    condition: (row, right) => true,
                }
            ],
            filterPlaceholder: '请输入资源名称搜索',
            queryForm: {
                auditState: {
                    label: '审批状态',
                    type: 'select',
                    data: () => this.m_market_audit_state,
                    value: "",
                },
            }
        }
    },
    methods: {
        changePage(inx, size) {
            const vm = this, {queryForm, filterText, settings} = this;
            const params = {
                type: 'AUDIT', // 审批列表
                applyType: "",
                resourceName: filterText,
                auditState: queryForm.auditState.value,
                pageNum: inx,
                pageSize: size,
            };
            settings.loading = true;
            vm.tableData = [];
            this.$services('market').resourceApplyList(params, settings).then(res => {
                if (res.data.status === 0) {
                    const result = res.data.data;
                    vm.tableData = result.dataList.map(li => {
                        let rType = vm.m_market_get_model_types().find(i => i.value === li.resource_type);
                        li.resourceType = rType && rType.label;
                        return li;
                    });
                    if (vm.$refs.table) vm.$refs.table.total = result.totalCount;
                }
            })
        },
        // 资源详情
        detail(row) {
            let routeData = this.$router.resolve({path: '/market/modelMarket', query: {id: row.model_id}});
            window.open(routeData.href, '_blank');
        },
        // 审批详情
        approvalDetail(row) {
            let layer = this.$dgLayer({
                title : '审批详情',
                content : require("../dialog/approvalPage.vue"),
                maxmin:false,
                move:false,
                area:["800px","640px"],
                props:{
                    row,
                    type : 'detail'
                },
            })
        },
        // 审批
        approval(row) {
            const vm = this;
            let layer = this.$dgLayer({
                title : '审批',
                content : require("../dialog/approvalPage.vue"),
                maxmin:false,
                move:false,
                area:["800px","640px"],
                props:{
                    row,
                },
                on : {
                    submit(){
                        vm.queryList();
                        layer.close(layer.dialogIndex);
                    }
                }
            })
        },
        queryList() {
            this.$refs.table.changePage();
        },

    },
    mounted() {
        this.queryList();
        // this.tableData = [{}]
    }
}
</script>

<style scoped lang="less">

</style>
