<template>
    <div class="roles-manage ce-manage_list">

        <div>
            <div class="a_style mb8">总线服务信息</div>
            <el-form class="ce-form__m0 mb10" label-width="120px" inline>
                <el-form-item label="总线版本:" required>
                    <el-select placeholder="请选择:" v-model="bus_version">
                        <el-option
                                v-for="item in busVersionOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="HTTP地址:" required>
                    <el-input placeholder="请输入HTTP地址:" style="width:300px" v-model="http_address"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="savaBusInfo()" v-if="rights.indexOf(saveGroupRight) > -1">保存
                    </el-button>
                    <help-document code="accessBusManagementSaved"></help-document>
                </el-form-item>
            </el-form>
            <div class="r-h-search">
                <div class="a_style">请求服务信息列表</div>
                <div>
                    <dg-button type="primary" @click="addNew"
                               v-if="rights.indexOf(addGroupRight) > -1">新增服务
                    </dg-button>
                    <el-input
                            size="mini"
                            placeholder="请输入服务方名称搜索"
                            v-model.trim="searchVal"
                            v-input-limit:trim
                            class="search-style ml10"
                            @input="inputFilterSpecial($event , 'searchVal')"
                            @keyup.enter.native="isMock ? searchTableData() : changePage(1)"
                    >
                        <i class="el-icon-search el-input__icon poi"
                           slot="suffix" @click="isMock ? searchTableData() : changePage(1)"></i>
                    </el-input>
                </div>
                <!-- <el-button type="primary" icon="el-icon-search" @click="changePage(1)"></el-button> -->

            </div>
        </div>
        <div class="roles-content" v-loading="settings.loading">
            <common-table
                    :data="tableData"
                    :columns="tableHead"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100 ce-table-h__auto"
                    :max-height="tableBodyH"
                    :border="false"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
                    :row-key="getRowKeys"
                    :expand-row-keys="expands"
            >
                <template slot="type-expand" scope="scope">
                    <a class="a_style">报文信息列表</a>
                    <el-table :data="scope.row.rowInterfaceInfo" border>
                        <el-table-column prop="name" label="接口名称" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="serviceType" label="服务类型"></el-table-column>
                        <el-table-column label="操作" :resizable="false" width="180" align="center">
                            <template slot-scope="secondScope">
                                    <span class="r-c-action" v-for="(opt , inx) in interfaceOperator" :key="inx"
                                          v-if="opt.condition(secondScope , rights)">
                                        <dg-button type="text" v-html="opt.label"
                                                   :title="opt.label"
                                                   @click="opt.clickFn(secondScope.$index, secondScope.row, scope)"></dg-button>
                                    </span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="操作">
                            <template slot-scope="scope">
                                <span>
                                    <dg-button
                                            type="text"
                                            @click="deleteInterface(scope.$index, scope.row)"
                                            size="medium"
                                            icon="el-icon-delete"
                                    ></dg-button>
                                </span>
                            </template>
                        </el-table-column> -->
                    </el-table>

                </template>


                <template slot="operate" slot-scope="{row}">
                        <span class="r-c-action" v-for="(opt , inx) in operator" :key="inx"
                              v-if="opt.condition(row , rights)">
                            <dg-button type="text" v-html="opt.label" :title="opt.label"
                                       @click="opt.clickFn(row)"></dg-button>
                        </span>
                </template>
            </common-table>
        </div>

        <twoDialog ref="twoDialogS" :title="title" @changePage="changePage(1)" :rowData="rowData"
                   :functionCode="functionCode"
                   :serviceDetailShow="serviceDetailShow"
                   :interfaceDetailShow="interfaceDetailShow" :submitShow="submitShow" :busId="busId"
                   @closeDialog="closeDialog"></twoDialog>
    </div>
</template>
<script lang="js" src="./bus-manage.js"></script>
<style lang="less" scoped src="./bus-manage.less"></style>
