<template>
    <div>
        <div class="pb10">
            <span>执行状态：</span>
            <el-select class="ce-form-select" v-model="state" @change="changePage(1)" filterable placeholder="请选择">
                <el-option v-for="item in stateOptions" @change="changePage(1)" :key="item.value" :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
            <span class="pl20">运行时间：</span>
            <el-date-picker v-model="time" type="datetimerange" unlink-panels @change="changePage(1)"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </div>

        <common-table height="calc(75vh - 100px)" :data="tableData" :columns="tHeadData"
            :paging-type="isMock ? 'client' : 'server'" :pagination-props="paginationProps" :pagination-total="total"
            v-loading="settings.loading" @change-current="isMock ? () => { } : changePage($event)"
            @change-size="changeSize" overflow="hidden">
            <template slot="operate" slot-scope="{row , $index}">
                <el-button type="text" class="model_status" v-for="(col, inx) in operateIcons" :key="inx"
                    :title="col.tip" v-html="col.tip" v-if="col.show(isSparkTask)"
                    @click="col.clickFn(row, $index)"></el-button>
            </template>
            <template slot="executeInfo" slot-scope="{row , $index}">
                <el-popover v-if="Array.isArray(row.executeInfo) && row.executeInfo.length > 1" placement="left"
                    width="900" popper-class="ce-code_panel" trigger="click">
                    <div class="ce-code_panel_h">
                        <dg-scrollbar>
                            <span>{{ row.executeInfo }}</span>
                        </dg-scrollbar>
                    </div>
                    <span slot="reference" class="ce-link_text">{{ row.executeInfo }}</span>
                </el-popover>
                <span v-else>{{ row.executeInfo }}</span>
            </template>
        </common-table>

        <!--        查看历史任务日志-->
        <HistoryTask ref="historyTask" />
        <HistoryExecute ref="historyExecute" />
        <TaskLogDetail ref="logDetail" />
        <HistoryTaskDetail ref="hisDetail" />
        <indicatorsInfo ref="indicators"></indicatorsInfo>
    </div>
</template>

<script>
import { commonMixins } from "@/api/commonMethods/common-mixins"
import { common } from "@/api/commonMethods/common";
import indicatorsInfo from "./indicatorsInfo"
import HistoryTask from "./HistoryTask"
import HistoryExecute from "./HistoryExecute"
import TaskLogDetail from './TaskLogDetail'
import HistoryTaskDetail from "./HistoryTaskDetail";
import { mapGetters } from "vuex";
export default {
    name: "runHistory",
    mixins: [commonMixins, common],
    components: {
        HistoryTask,
        HistoryExecute,
        TaskLogDetail,
        HistoryTaskDetail,
        indicatorsInfo
    },
    props: {
        type: String
    },
    computed: {
        ...mapGetters(["isCollectionLog"]),
    },
    data() {
        return {
            //运行状态
            state: "",
            //运行时间
            time: [],
            stateOptions: [
                {
                    label: "全部",
                    value: ""
                },
                {
                    label: "执行成功",
                    value: "SUCCESS",
                },
                {
                    label: "执行失败",
                    value: "ERROR",
                },
                {
                    label: "执行中",
                    value: "RUNNING",
                }, {
                    label: "执行取消",
                    value: "CANCEL",
                },
            ],
            timeVal: "",
            timeTip: "运行时间",
            statusVal: "",
            statusOpt: [
                { label: '全部', value: "" }
            ],
            statusTip: "运行状态",
            active: true,
            // 分页
            currentPage: 1,
            total: 0,
            pageSize: 10,
            openDialog: false,
            isSparkTask: false,
            rowData: {},
            tableData: [],
            HistoryTableData: [],
            tHeadData: [
                {
                    prop: "taskName",
                    label: "模型名称",
                    width: "140",
                    align: "left"
                }, /*{
                        prop: "taskType",
                        label: "任务类型",
                        width: "",
                        align: "center"
                    },*/
                {
                    prop: "startTime",
                    label: "开始时间",
                    width: "180",
                    align: "center"
                },
                {
                    prop: "endTime",
                    label: "结束时间",
                    width: "180",
                    align: "center"
                },
                {
                    prop: "runTime",
                    label: "运行耗时",
                    align: "center"
                }, {
                    prop: "executeResult",
                    label: "运行状态",
                    align: "center"
                },
                /*{
                    prop: "executeResult",
                    label: "执行结果",
                    width: "",
                    align: "center"
                },*/
                {
                    prop: "executor_service",
                    label: "所用引擎实例",
                    width: "130",
                    align: "center",
                    resizable: false,
                },
                    /*{
                        prop: "executeInfo",
                        label: "异常信息",
                        width: "",
                        align: "center",
                        resizable: false,
                    },*/ {
                    prop: "operate",
                    label: "操作",
                    align: "center",
                    width: '240',
                    resizable: false
                }

            ],
            operateIcons: [
                // 超级魔方 暂时注释{icon: "&#xe660;", tip: "spark详情", show: ()=>  !this.rowData.isEditParam, clickFn: this.historyTask},
                { icon: "&#xe660;", tip: "flink详情", show: () => this.rowData.isEditParam, clickFn: this.flinkTask },
                /*  {icon: "&#xe72c;", tip: "查看历史执行日志",show : (isSparkTask)=> !isSparkTask, clickFn: this.historyExecute},*/
                // {icon: "&#xe76b;", tip: "日志详情", show:()=> true, clickFn: this.logDetails},
                //超级魔方 暂时注释 {icon: "&#xe660;", tip: "日志详情",show :()=>  this.isCollectionLog, clickFn: this.historyLogger},
                { icon: "&#xe660;", tip: "日志详情", show: () => !this.rowData.isEditParam, clickFn: this.showLogDetail },
                { icon: "&#xe660;", tip: "查看指标", show: () => this.rowData.isEditParam, clickFn: this.lookIndicators },
                { icon: "&#xe65f;", tip: "删除", show: () => this.type !== 'detail', clickFn: this.deleteLog },
            ],
            nodes: [],
        };
    },
    methods: {
        showLogDetail(row) {
            this.$dgLayer({
                title: "日志详情",
                content: require("@views/modeling/flowPanel/LogDetailPage.vue"),
                area: ["1000px", "560px"],
                maxmin: false,
                move: false,
                noneBtnField: true,
                props: {
                    row,
                }
            })
        },
        lookIndicators(row) {
            this.$refs.indicators.show(row, this.nodes);
        },
        historyLogger(row) {
            this.$refs.hisDetail.getDetail(row.taskId);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        insertStr(soure, start, newStr) {
            return soure.slice(0, start) + newStr + soure.slice(start);
        },
        show(rowData, nodes) {
            this.rowData = rowData;
            this.nodes = nodes;
            this.changePage(1);
        },
        //查看历史任务日志
        historyTask(row) {
            const _this = this, { settings } = this;
            let services = _this.$services("modeling");
            settings.loading = true;
            services.searchLog(row.taskId, settings).then(res => {
                if (res.data.data.length === 0) {
                    if (row.executeResult === "执行成功") {
                        this.$message.warning("查询不到执行日志，或是执行日志已过期！");
                    } else {
                        this.$message.error("任务解析异常，未提交到引擎执行，暂无执行日志！");
                    }

                } else {
                    if (res.data.status === 0) {
                        _this.HistoryTableData = [];
                        res.data.data.forEach(item => {
                            let execStatus = item.status;
                            switch (item.status) {
                                case 'SUCCEEDED':
                                    execStatus = '执行成功';
                                    break;
                                case 'RUNNING':
                                    execStatus = '执行中';
                                    break;
                                case 'FAILED':
                                    execStatus = '执行失败';
                                    break;
                                case 'UNKNOWN':
                                    execStatus = '未知';
                                    break;
                                default:
                                    execStatus = '执行失败';
                                    break;
                            }
                            let tableNode = {

                                groupId: item.groupId,
                                name: item.jobId,
                                status: execStatus,
                                description: item.description === "None" ? "" : item.description,
                                submissionTime: item.submissionTime,
                                completionTime: item.completionTime,
                                jobId: item.jobId
                            };
                            _this.HistoryTableData.push(tableNode)
                        })
                        this.$refs.historyTask.show(row, _this.HistoryTableData);
                    }
                }

            }).catch(err => {
                console.log(err);
                _this.$message.error("服务器异常，请联系管理员！")
            })
        },
        //查看历史任务日志
        flinkTask(row) {
            const _this = this, { settings } = this;
            let services = _this.$services("modeling");
            settings.loading = true;
            let data = {
                jobId: row.id,
                applicationName: row.task_string,
                settings
            }
            services.queryLogUrlByJobId(data).then(res => {
                window.open(res.data.data.data, '_blank')
            })
        },
        // 查看任务执行日志
        historyExecute(row) {
            // console.log(row);
            this.$refs.historyExecute.show(row);
        },
        // 日志详情
        logDetails(row) {
            this.$refs.logDetail.show(row);
        },
        formatDate: function (value) {
            let date = new Date(value);
            let y = date.getFullYear();
            let MM = date.getMonth() + 1;
            MM = MM < 10 ? ('0' + MM) : MM;
            let d = date.getDate();
            d = d < 10 ? ('0' + d) : d;
            let h = date.getHours();
            h = h < 10 ? ('0' + h) : h;
            let m = date.getMinutes();
            m = m < 10 ? ('0' + m) : m;
            let s = date.getSeconds();
            s = s < 10 ? ('0' + s) : s;
            return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
        },


        // 删除日志
        deleteLog(row, index) {
            const _this = this, { settings } = this;
            let services = _this.$services("modeling");
            this.confirm('提示', '确认删除日志？', () => {
                settings.loading = true;
                services.deleteLog(row.taskId, settings).then(res => {
                    if (res.data.status === 0) {
                        _this.$message({
                            type: 'success',
                            message: "删除成功！",
                        });
                        _this.changePage(this.currentPage);
                    } else {
                        _this.$message.error(res.data.msg);
                    }
                }).catch(err => {
                    _this.$message.error("服务器发生异常，请联系管理员")
                })
            })
        },
        changePage(index) {
            // this.currentPage = index;
            // console.log(this.rowData);
            const vm = this, { settings } = this;
            let services = vm.$services("modeling");
            vm.paginationProps.currentPage = index;
            let data = {
                pageSize: vm.paginationProps.pageSize,
                pageIndex: index,
                sortField: "name",
                sortOrder: "desc",
                state: vm.state.toLowerCase(),
                startTime: vm.time ? vm.time[0] ? new Date(vm.time[0]).getTime().toString() : "" : "",
                endTime: vm.time ? vm.time[1] ? new Date(vm.time[1]).getTime().toString() : "" : "",
                transMetaId: vm.rowData.transId,
            };
            settings.loading = true;
            services.changeLogPage(data, settings).then(res => {
                //console.log(res.data);
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.total = result.totalCount;
                    vm.isSparkTask = result.staInfo.isSparkTask;
                    vm.tableData = [];
                    if (result.dataList && result.dataList.length) {
                        result.dataList.forEach(item => {
                            /* let execStatus = item.EXEC_STATUS;
                             switch (item.EXEC_STATUS) {
                                 case 'SUCCESS':
                                     execStatus = '执行成功';
                                     break;
                                 case 'EXECING':
                                     execStatus = '执行中';
                                     break;
                                 case 'ALLOT':
                                     execStatus = '分配中';
                                     break;
                                 case 'SCHEDULE':
                                     execStatus = '调度中';
                                     break;
                                 case 'WAITING':
                                     execStatus = '等待';
                                     break;
                                 case 'EXECUTED':
                                     execStatus = '已执行';
                                     break;
                                 case 'APPLY':
                                     execStatus = '准备中';
                                     break;
                                 case 'SCHEDULE_SUCCESS':
                                     execStatus = '等待分配';
                                     break;
                                 default:
                                     execStatus = '执行错误';
                                     break;
                             }*/

                            /*let taskType = '';
                            switch (item.TASK_TYPE) {
                                case 'SINGLE':
                                    taskType = '单机';
                                    break;
                                case 'SPARK':
                                    taskType = 'spark';
                                    break;
                                default:
                                    taskType = '分布式';
                                    break;
                            }*/
                            //let startTime = this.insertStr(this.insertStr(this.insertStr(this.insertStr(this.insertStr(item.START_TIME, 4, "-"), 7, "-"), 10, "  "), 14, ":"), 17, ":00");
                            //let endTime = this.insertStr(this.insertStr(this.insertStr(this.insertStr(this.insertStr(item.END_TIME, 4, "-"), 7, "-"), 10, "  "), 14, ":"), 17, ":00");
                            let startTime = this.formatDate(item.start_time)
                            let endTime = this.formatDate(item.end_time).indexOf("1970") === 0 ? "" : this.formatDate(item.end_time)
                            let execStatus = item.execute_status;
                            let jobDeteil = item.job_detail;
                            switch (item.execute_status.toUpperCase()) {
                                case 'SUCCESS':
                                    execStatus = '执行成功';
                                    jobDeteil = '无';
                                    break;
                                case 'ERROR':
                                    execStatus = '执行失败';
                                    jobDeteil = item.job_detail;
                                    break;
                                case 'RUNNING':
                                case 'ONGOING':
                                    execStatus = '执行中';
                                    jobDeteil = '无';
                                    break;
                                case 'CANCEL':
                                    execStatus = '执行取消';
                                    jobDeteil = '无';
                                    break;
                                default:
                                    execStatus = '执行失败';
                                    break;
                            }
                            let taskNode = {
                                id: item.id,
                                task_string: item.task_string,
                                taskId: item.id,
                                taskName: item.trans_name,
                                /*taskType: taskType,*/
                                startTime: startTime,
                                endTime: endTime,
                                executeResult: execStatus,
                                executeInfo: jobDeteil,
                                runTime: item.runTime,
                                executor_service: vm.rowData.isEditParam ? item.task_string : item.executor_service,
                            };
                            vm.tableData.push(taskNode);
                        })
                    }
                }
            })
        },
        clearD() {
            this.openDialog = false;
        },
    },
}
</script>

<style scoped></style>
