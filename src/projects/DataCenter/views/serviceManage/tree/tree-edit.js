export const TreeEdit = {
    data() {
        return {}
    },
    methods: {
        /**
         * 树点击事件
         * @param name
         * @param id
         * @param node
         */
        nodeClick(name , id , node) {
            this.$refs.treeDir.nodeClick(name , id , node);
        },
        /**
         * 树过滤
         * @param val
         */
        filterEditTree(val) {
            this.$refs.editTree.setFilterText(val);
        },
        /**
         * 新建目录
         * @param name
         * @param parent
         */
        addTreeDir(name, parent) {
            const vm = this, {settings} = vm;
            let services = vm.$services("serviceManage");
            settings.loading = true;
            let par = {
                parentId: parent.id,
                name:name
            }
            services.newServiceClassify(par ,settings).then(res => {
                if (res.data.status === 0) {
                    vm.successAddTip(name);
                    let nodeId = res.data.data;
                    vm.$refs.dataTree.addTreeNode(name, parent, nodeId);
                    vm.$refs.treeDir.stop();
                }
            })
        },
        /**
         * 移动目录
         * @param curNode
         * @param parentNode
         */
        moveTreeDir(curNode, parentNode) {
            const vm = this, {settings} = vm;
            let services = vm.$services("serviceManage");
            settings.loading = true;
            let par = {
                id: curNode.id,
                parentId: parentNode.id,
                name:curNode.name
            }
            services.editServiceClassify(par ,settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("移动成功!");
                    vm.$refs.dataTree.moveTreeDir(curNode, parentNode)
                    vm.$refs.treeDir.stop();
                }
            })
        },
        /**
         * 重新搜索
         */
        reSearch(){
            let {search} = this.$refs.treeCard;
            this.$refs.dataTree.filterTreeNode(search);
        }
    }
}
