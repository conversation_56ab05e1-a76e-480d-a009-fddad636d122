import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"

export default {
    name: "treePage" ,
    mixins : [treeMethods ,commonMixins],
    data() {
        return {
            filterText: '',
            data: [],
            defaultProps : {
                value : "id" ,
                label : "label" ,
                children : "children"
            },
        }
    },
    props :{
        services :Object  ,
        rowData : Object
    },
    methods: {
        nodeClick(v,i){
            this.$emit('nodeClick',v.label,v.id,i);
        },
        initTree(){
            const vm = this, {settings , rowData} = this;
            let {services} = vm.services;
            settings.loading = true;
            services.queryDataSetTree(false , settings).then(res => {
                if(res.data.status === 0){
                    vm.data = res.data.data[0].children;
                    vm.$nextTick(()=>{
                        if(vm.$refs.tree){
                            vm.$refs.tree.setCurrentKey(rowData.parentid);
                        }
                    });
                }
            })
        }
    }

}