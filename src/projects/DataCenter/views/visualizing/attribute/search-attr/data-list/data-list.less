.ce-attr_cont {
  height: calc(100% - 20px);
  padding: 10px;
  overflow: auto;
}

.ce-attr_button {
  position: absolute;
  bottom: 0;
  width: calc(100% - 20px);
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #ddd;
}

.ce-chart_item {
  display: inline-block;
  padding: 0 10px;
}


.ce-box-card .el-form-item:last-child {
  margin-bottom: 0;
}

.ce-list_box {
  border: 1px solid #ddd;
  width: 416px;
}

.ce-list_header {
  background: #e7eaf0;
  color: #656567;
  line-height: 34px;
  padding: 0 14px;
}

.ce-list_cont {
  padding: 10px;
  overflow: hidden;
}

.ce-list_item {
  float: left;
  min-width: 80px;
  text-align: center;
  color: #666;
  border: #ddd 1px solid;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  margin: 0 6px 6px 0;
  box-sizing: border-box;
}

.ce-list_item:hover, .ce-list_item.active_item {
  color: #fff;
  background-color: #409EFF;
  border-color: #409EFF;
}
.ce-enum_cont {
  width: 300px;
  border: 1px solid #ddd;
  border-left: none;
  padding: 10px;
  position: relative;
  padding-bottom : 40px;
}
.ce-list_outer {
  display: flex;
}
.attr_btn {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

/deep/.el-table.ce-filter_table .cell {
  padding: 0 5px;
}
.is-require::before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}