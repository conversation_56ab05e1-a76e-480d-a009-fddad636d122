<!--
    拖拽插件，支持拖拽多个
    slot ： listItem 建议自定义时，使用内联标签 如 span , em , i
    props :
        data 选项数组
        mode 布局模型 //horizontal , vertical
        checkAllTxt 全选 名称
        multiple 是否多选 默认是
        showCheckAll 是否显示 全选的复选框 默认不显示
        dragItemClassName //拖拽自定义dom item样式
        focusClassName 选中的样式类名
        setDragItemDom 自定义dom的方法 @return {jQuery|HTMLElement}
        cursorAt 拖拽时鼠标的位置
    事件 ：checkAllChange 选择的项改变时触发事件
          dragItems 拖拽开始事件
          dragEnd 拖拽结束事件
-->

<template>
    <div class="ce-draggable" v-click-out="clearFocus">
        <slot name="checkAll"></slot>
        <div class="ce-draggable_setting" v-if="showCheckAll">
            <el-checkbox class="ce-check-all" :indeterminate="isIndeterminate" v-model="checkAll"
                         @change="handleCheckAllChange">{{ checkAllTxt }}
            </el-checkbox>
        </div>
        <div class="ce-draggable_cont" :class="{'ce-draggable_mul' : showCheckAll}"
             ref="list">
            <dg-scrollbar noresize>
                <ul>
                    <li class="ce-draggable_item ce-draggable_li"
                        :class="{'ce-vertical_check' : mode === 'vertical' , 'ce-horizontal_check' : mode === 'horizontal'}"
                        v-for="(item , k) in data"
                        :key="k"
                        @mousedown="setDragItem($event , item , k , 'checkAllItems')"
                        @click="triggerItem($event , item , k)"
                        :data-value="item.value">
                        <slot name="listItem" :item="item"
                              :$index="k"
                              :className="isCheckedData(item , checkAllItems) ? focusClassName : ''"
                        >
                            <span :title="item.label">{{ item.label }}</span>
                        </slot>
                    </li>
                </ul>
                <empty v-show="!data.length"></empty>
            </dg-scrollbar>
        </div>
    </div>
</template>

<script>
import * as $ from "jquery"
import 'jquery-ui-dist/jquery-ui'
import 'jquery-ui-dist/jquery-ui.min.css'

export default {
    name: "CeDraggable",
    props: {
        data: {
            type: Array,
            default: () => []
        },
        mode: {
            type: String,
            default: "vertical",//horizontal , vertical
        },
        checkAllTxt: {
            type: String,
            default: "全选"
        },
        multiple: {
            type: Boolean,
            default: true
        },
        showCheckAll: {
            type: Boolean,
            default: false
        },
        dragItemClassName: { //拖拽 显示 item dom 样式
            type: String,
            default: ""
        },
        focusClassName: {
            type: String,
            default: "active"
        },
        setDragItemDom: Function,
        cursorAt: {
            type: Object,
            default: () => {
                return {left: 0, top: 0};
            }
        }
    },
    data() {
        return {
            isIndeterminate: false,
            checkAll: false,
            checkAllItems: [],
            shiftStart: null,
        }
    },
    watch: {
        checkAllItems: {
            handler(val) {
                let checkAll = val.length === this.data.length;
                this.$emit("checkAllChange", this.isIndeterminate, checkAll);
            },
            deep: true
        }
    },
    methods: {
        /**
         * 清空选择
         */
        clearFocus() {
            this.checkAllItems = [];
            this.checkAll = false;
            this.isIndeterminate = false;
            this.shiftStart = null;
        },
        /**
         *
         * @return {boolean}
         */
        isCheckedData(item, data) {
            return data.map(fo => fo.value).includes(item.value);
        },
        setDragItem(e, item, inx) {
            const vm = this;
            let {checkAllItems} = vm;
            let itemIds = checkAllItems.map(it => it.value);
            if (e.ctrlKey || e.shiftKey) return;
            if (!checkAllItems.length || !itemIds.includes(item.value)) {
                vm.checkAllItems = [];
                vm.shiftStart = {...item, inx};
                vm.checkAllItems = [item];
            }
        },
        triggerItem(e, item, inx) {
            const vm = this;
            if (vm.multiple) {
                if (e.ctrlKey) {
                    vm.ctrlClickForField(item, inx);
                    return;
                } else if (e.shiftKey) {
                    vm.shiftClickForField(item, inx);
                    return;
                }
            }
            vm.shiftStart = {...item, inx};
            vm.checkAllItems = [item];
            vm.checkedItemsChange(vm.checkAllItems);
        },
        ctrlClickForField(item, inx) {
            const vm = this, {checkAllItems, isCheckedData} = vm;
            vm.shiftStart = {...item, inx};
            if (isCheckedData(item, checkAllItems)) {
                vm.checkAllItems = checkAllItems.filter(fo => fo.value !== item.value);
            } else {
                vm.checkAllItems.push(item);
            }
            vm.checkedItemsChange(vm.checkAllItems);
        },
        shiftClickForField(item, inx) {
            const vm = this, {shiftStart, data} = vm;
            if (!shiftStart) {
                vm.shiftStart = {...item, inx};
                vm.checkAllItems.push(item);
            } else {
                vm.checkAllItems = data.filter((da, i) => {
                    let staI = shiftStart.inx, endI = inx;
                    return staI > endI ? (i >= endI && i <= staI) : (i <= endI && i >= staI);
                });
            }
            vm.checkedItemsChange(vm.checkAllItems);
        },
        /**
         * 全选
         * @param val
         */
        handleCheckAllChange(val) {
            const vm = this, {data} = vm;
            vm.checkAllItems = val ? data : [];
            vm.isIndeterminate = false;
            vm.checkAll = val;
        },
        /**
         * 单个改变时触发
         * @param val
         */
        checkedItemsChange(val) {
            let checkedCount = val.length;
            this.checkAll = checkedCount === this.data.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.data.length;
        },
        /**
         * 绑定拖拽事件
         */
        bindDraggable() {
            const vm = this, {getCheckedDom, cursorAt} = vm;
            $(vm.$refs.list).draggable({
                addClasses: false,
                appendTo: "body",
                cursor: "move",
                zIndex: 9999,
                cursorAt,
                helper: function () {
                    return getCheckedDom();
                },
                start: function (e) {
                    let {checkAllItems} = vm;
                    vm.$emit("dragItems", checkAllItems);
                },
                stop: function (e) {
                    vm.$emit("dragEnd");
                },
            })

        },
        /**
         * 构建 拖拽显示的DOM
         * @return {jQuery|HTMLElement}
         */
        getCheckedDom() {
            const vm = this, {checkAllItems, dragItemClassName, setChildDom, setDragItemDom} = vm;
            let dom = document.createElement('div');
            if (checkAllItems) dom.className = "p5 bgwh ce-shadow";
            return setDragItemDom ? setDragItemDom(checkAllItems, dom, dragItemClassName) :
                    setChildDom(checkAllItems, dom, dragItemClassName);
        },
        /**
         * 设置单个DOM
         * @param checkedItems
         * @param dom
         * @param dragItemClassName
         * @return {jQuery|HTMLElement|*}
         */
        setChildDom(checkedItems, dom, dragItemClassName) {
            checkedItems.forEach((item, i) => {
                if (i > 5) return;
                let li = document.createElement('li');
                li.className = `ell w200 lh24 g5 ${dragItemClassName}`;
                if (i < 5) {
                    li.innerText = item.label;
                } else {
                    li.innerText = '...';
                }
                dom.appendChild(li);
            });
            return $(dom);
        },
    },
    mounted() {
        this.bindDraggable()
    }
}
</script>

<style scoped lang="less">
.ce {


    &-check-all {
        line-height: 40px;
    }

    &-draggable {
        height: 100%;
        overflow: visible;

        &_item {
            display: inline-block;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
            cursor: move;
        }

        &_li:hover {

        }

        &_settings {

        }

        &_cont {
            height: 100%;
            overflow: hidden;
        }

        &_mul {
            height: calc(100% - 40px);
        }
    }

    &-vertical_check {
        display: block;
        margin: 0;
    }

    &-horizontal_check {
        display: inline-block;
        max-width: 200px;
        width: auto;
        margin: 0 6px 6px 0;
    }

}
</style>
<style>
.ce-shadow {
    box-shadow: 0 1px 3px 3px rgba(0, 0, 0, .1);
}
</style>
