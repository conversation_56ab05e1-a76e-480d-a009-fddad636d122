<template>
<div style="height:100vh"></div>
</template>

<script>
    import secret from "@/api/secret"
    import * as loginServices from "../../../services/login/login-services"
    import * as loginMock from "../../../mock/login/login-mock"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    export default {
        name: "modelView",
        mixins : [commonMixins],
        methods:{
            getView(){
                const vm = this;
                let urlQuery = JSON.parse(JSON.stringify(vm.$route.query));
                if (!urlQuery.transId) {
                    this.$router.push({name:'error'})
                    return
                }
                urlQuery.isNew = false;
                urlQuery.caseId = urlQuery.transId;
                urlQuery.isEditParam = true; //是否四系跳转
                let layer = this.$dgLayer({
                    content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                    skin: "new-model",
                    maxmin: true,
                    noneBtnField: true,
                    props: {
                        dirId: "-1",
                        isCase: true,
                        isLook: true,
                    },
                    area: ["100%", "100%"],
                    on: {
                        close() {
                            //window.top.postMessage('','*');
                            layer.close(layer.dialogIndex);
                            window.close();
                        }
                    },
                });
                layer.$children[0].addTab(urlQuery, false);
            },
            login() {
                const vm = this;
                let password = secret.encrypt('654321');
                vm.$axios.get(`/login?objCode=dc_super&password=${password}`).then(res => {
                    if(res.data.status === 0) {
                        vm.getRight(res.data.data);
                    }
                })
            },
            getRight(userInfo){
                const vm = this;
                let services = vm.getServices(loginServices , loginMock);
                services.queryUserFunction().then(res =>{
                    if(res.data.status === 0){
                        let result = res.data.data;
                        if(result.length){
                            vm.$store.dispatch("login" , userInfo );
                            localStorage.setItem("userInfo" ,JSON.stringify(userInfo) );
                            localStorage.setItem("userRight" ,JSON.stringify(result) );
                            services.getUserRole(userInfo.id).then(res=>{
                                if(res.data.status === 0){
                                    localStorage.setItem("userRole" , JSON.stringify(res.data.data));
                                }
                            });
                            vm.$store.dispatch("setRight" , result );
                            services.updataLoginNumber(userInfo.id);
                            vm.getView()
                        }else {
                            vm.$message.warning("无系统访问权限");
                        }
                    }
                })
            }
        },
        mounted(){
            this.login();
        }
    }
</script>

<style scoped>

</style>
