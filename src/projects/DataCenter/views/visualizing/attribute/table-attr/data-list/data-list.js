import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";
export default {
    name: "dataList" ,
    props: {
        node: Object,
        dir_dataSetId : String
    },
    mixins : [attrMixins],
    data(){
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show: ()=> true,
            }
        };
        return {
            showDrill : false,
            axisList : {
                xAxis : {
                    show: ()=> false,
                },
                yAxis: {
                    show: ()=> true,
                    label: '列',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度/度量字段到此处(不限个数)',
                    options: [],
                    multipleLimit: 0,
                    changeFn:
                    this.changeFn,
                    rule: [
                        {required: true, message: '请添加列', trigger: 'blur'}
                    ],
                },
                ...formList
            },
            query : null
        }
    },
    computed: {
        chartData() {
            return {
                xAxis: [],
                xData: [],
                yAxis: this.axisList.yAxis.value,
                yData: this.axisList.yAxis.options,
                filterData: this.filterSelects,
                datasetId: this.datasetId,
                classifierStatId: this.classifierStatId,
                dataset: this.dataset ,
                filterList : this.query,
            };
        }
    },
    methods : {
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            /*if (this.node.code !== 'TableChartWidget') {
                if (this.axisList[inx].prop === 'xAxis' && this.dragTreeNode.indexType === "DIMENSION") {
                    this.$message.warning('请拖入维度输入框');
                    return;
                } else if (this.axisList[inx].prop === 'yAxis' && this.dragTreeNode.indexType === "MEASURE") {
                    this.$message.warning('请拖入度量输入框');
                    return;
                }
            }*/
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx , this.dragTreeNode.indexType , 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        initValue(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let {beforeData , query} = JSON.parse(vm.node.widget),nodeData = JSON.parse(beforeData);
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId :true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];
                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                //查询
                vm.query  = [];
                //钻取
                // vm.axisList.drilling.list = nodeData.drill ? JSON.parse(JSON.stringify(nodeData.drill)) : [];
                //隐藏下拉
                vm.dropDownHide([[] , vm.axisList.yAxis.options]);
                vm.inputData();
                vm.addAxisName("","", vm.axisList);
            })
        },
        validate(){
            if (this.chartData.yAxis.length === 0) {
                this.$message.info('请添加维度!');
                return;
            }
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setTablePanel', this.node.id);
        },
    }
}
