<template>
    <div v-loading="settings.loading">
        <el-form class="attrForm" :rules="rules" label-position="right" label-width="160px">
            <el-form-item label="选择文件：" prop="files">
                <el-upload
                    action="FAKEURL"
                    :accept="'.xlsx,.csv,.xls'"
                    ref="uploadfile"
                    :show-file-list="false"
                    :on-change="handleBefore"
                    :auto-upload="false"
                >
                    <el-button type="primary" size="mini">文件上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传excel,csv文件<span
                        v-if="hasLimit">，且文件不超过{{ limitNum }}MB</span>。
                    </div>
                </el-upload>
            </el-form-item>
            <el-form-item label="文件名称：" required>
                <el-input v-model="pluginMeta.fileReName" placeholder="请输入文件名称"
                          maxlength="50"></el-input>
                <p class="el-upload__tip"> 名称只能由中英文、数字及下划线、斜线、反斜线、竖线、小括号、中括号组成，不超过50个字符。
                </p>
            </el-form-item>
            <el-form-item label="csv分割符:" v-if="pluginMeta.fileType === 'csv'" required>
                <el-select v-model="pluginMeta.separator">
                    <el-option v-for="(item , inx) in csvSeparator" :key="inx" :value="item" :label="item"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="选择Sheet页:" prop="sheet" v-show="pluginMeta.fileType === 'excel'">
                <el-row>
                    <el-select v-model="pluginMeta.sheetName" placeholder="请选择">
                        <el-option
                            v-for="item in excelSheetNames"
                            :key="item.excelSheetName"
                            :label="item.excelSheetName"
                            :value="item.excelSheetName">
                        </el-option>
                    </el-select>
                </el-row>
                <!--                        <el-checkbox v-model="pluginMeta.useHeader">首行为字段名</el-checkbox>-->
            </el-form-item>

            <el-form-item label="首行为字段名:" v-show="pluginMeta.fileType === 'excel'" :required="true">
                <el-radio v-model="pluginMeta.useHeader" :label="true">是</el-radio>
                <el-radio v-model="pluginMeta.useHeader" :label="false">否</el-radio>
            </el-form-item>

            <el-form-item label="数据更新方式:" prop="files">
                <div>
                    <el-radio v-model="pluginMeta.dataUpdateWay"
                              v-for="item in fileDataUpdateWays"
                              :key="item.value"
                              :label="item.value">{{ item.label }}
                    </el-radio>
                </div>
                <div class="el-upload__tip"> 覆盖数据：用新的数据对原数据进行覆盖 ;<br/><br/> 追加数据：在原数据的基础上增加新的数据。
                </div>
            </el-form-item>

        </el-form>
        <common-dialog
            class="params-dialog"
            :title="title"
            :width="width"
            :visible.sync="visible"
            @closed="clear_data"
            v-loading="loading"
        >
            <fileUploadDialog ref="dialog" v-if="showParams" :sourceData="sourceData" :file="files"
                              :pluginMetaC="pluginMeta" :csvSeparator="csvSeparator" @updateData="updateData"
                              @cancel="cancel"></fileUploadDialog>
            <div slot="footer">
                <el-button @click="canceled">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </common-dialog>
        <preView ref="preview"></preView>
    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import fileUploadDialog from "./FileUploadDialog";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {judgeNull, isTypeData} from "dc-front-plugin/src/api/global_func_index";

export default {
    name: "FileUpLoadPlugin",
    mixins: [pluginMixins, servicesMixins],
    props: {
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    components: {
        fileUploadDialog,
        preView
    },
    data() {
        return {
            excelSheetNames: [],
            title: "文件上传",
            width: "650px",
            files: null,
            rules: {
                files: [
                    {
                        required: true,
                        message: "请上传Excel文件",
                        trigger: 'blur'
                    }
                ],
                sheet: [
                    {
                        required: true,
                        message: "请选择sheet页",
                        trigger: 'blur'
                    }
                ]
            },
            pluginMeta: {
                localPath: "",
                HdfsPath: "",
                fileReName: "",
                fileType: "",
                sheetName: "",
                startRow: "1",
                startColumn: "A",
                separator: "",
                useHeader: true,
                dataUpdateWay: "DATACOVERAGE"
            },
            fileDataUpdateWays: [],
            csvSeparator: [],
            hasLimit: false,
            limitNum: 0,
        }
    },
    methods: {
        getLimit() {
            const {$fileUploadLimit} = this;
            let hasLimit = judgeNull($fileUploadLimit) && (isTypeData($fileUploadLimit, 'Number') || isTypeData($fileUploadLimit, 'Boolean')),
                limit = hasLimit ? +$fileUploadLimit : 0,
                dType = typeof $fileUploadLimit;
            if (!hasLimit) throw new Error(`类型错误:fileUploadLimit 为 ${dType} ,请设置为 number 或 boolean`);
            return {hasLimit: hasLimit && !!$fileUploadLimit, limit};
        },
        handleBefore(file) {
            // if(file.size /1024/1024 > 10) {
            //     this.$message.warning("请不要上传大于10MB的文件");
            //     return;
            // }
            const vm = this, {pluginServices, pluginMock, hasLimit, limitNum} = this;
            if (hasLimit && file.size / 1024 / 1024 > limitNum) {
                this.$message.warning(`请不要上传大于${limitNum}MB的文件`);
                return;
            }
            let services = vm.getServices(pluginServices, pluginMock);
            let files = file.name.split(".");
            if (files[1] !== 'csv' && files[1] !== 'xlsx' && files[1] !== 'xls') {
                vm.$message.warning("文件格式错误，请重新选择文件上传");
                return;
            } else {
                let copyPlugin = {
                    localPath: "",
                    HdfsPath: "",
                    fileReName: "",
                    fileType: "",
                    sheetName: "",
                    startRow: "1",
                    startColumn: "A",
                    separator: "",
                    useHeader: true,
                    dataUpdateWay: "DATACOVERAGE"
                }
                if (files[1] === 'csv') copyPlugin.fileType = "csv";
                else copyPlugin.fileType = "excel";
                copyPlugin.fileReName = file.name;
                vm.files = file;
                let fileUpload = new FormData();
                if (vm.files !== null) {
                    fileUpload.append("file", vm.files.raw);
                } else {
                    fileUpload.append("file", null);
                }
                let fileSuffix = copyPlugin.fileReName.split(".")[1];
                if (copyPlugin.fileType === 'csv' || vm.files === null) {
                    copyPlugin.separator = vm.csvSeparator.includes(',') ? ',' : vm.csvSeparator[0];
                } else {
                    services.getExcelSheets(fileSuffix, fileUpload).then(result => {
                        if (result.data.status === 0) {
                            vm.excelSheetNames = result.data.data;
                            vm.excelSheetNames = vm.excelSheetNames.sort(this.compareed('sheetNo'))
                            for (let i = 0; i < vm.excelSheetNames.length; i++) {
                                if (vm.excelSheetNames[i].sheetNo === 0) {
                                    copyPlugin.sheetName = vm.excelSheetNames[i].excelSheetName;
                                }
                            }
                        }
                    })
                }
                if (this.pluginMeta.useHeader !== true) copyPlugin.useHeader = this.pluginMeta.useHeader;
                if (this.pluginMeta.dataUpdateWay !== 'DATACOVERAGE') copyPlugin.dataUpdateWay = this.pluginMeta.dataUpdateWay;
                this.pluginMeta = copyPlugin;

            }
        },
        validator() {
            let reg = /^[a-zA-Z0-9\\/|()[\]_\u4e00-\u9fa5]+$/;
            let files = this.pluginMeta.fileReName.split(".");
            if (files[1] === undefined) {
                return false;
            }
            if (!reg.test(files[0])) {
                return false;
            }
            return true;
        },
        canceled() {
            this.$refs.dialog.cancel();
        },
        handleSubmit() {
            this.$refs.dialog.handleSubmit();
        },
        compareed(property) {
            return function (a, b) {
                let value1 = a[property];
                let value2 = b[property];
                return value1 - value2;
            }
        },
        cancel() {
            this.clear_data();
            this.close();
        },
        judgeU(data) {
            if (data === undefined || data === null || data === "") {
                return true;
            } else {
                return false;
            }
        },
        save(saveName, showTip) {
            if (this.judgeU(this.files) && this.judgeU(this.pluginMeta.fileType)) {
                this.$message.warning("请上传文件");
                return;
            }
            if (this.pluginMeta.fileType === "csv" && (this.pluginMeta.separator === "" || this.pluginMeta.separator === undefined)) {
                this.$message.warning("请输入csv文件的分割符");
                return;
            }
            if (this.validator() == false) {
                this.$message.warning("请输入正确的文件名");
                return;
            }
            const vm = this, {pluginServices, pluginMock, loadParams} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            let file = new FormData();
            if (this.files !== null) {
                file.append("file", this.files.raw);
            } else {
                file.append("file", null);
            }
            if (vm.pluginMeta.fileType === 'csv') {
                vm.pluginMeta.useHeader = true;
            }
            let copy = JSON.parse(JSON.stringify(this.pluginMeta));
            delete copy.sheetNames;
            file.append("fileInputMeta", JSON.stringify(copy));
            loadParams.loading = true
            services.cicadaSaveFileInputPlugin(vm.sourceData.id, file, loadParams).then(async result => {
                if (result.data.status === 0) {
                    const previewRes = await vm.getPreviewData();
                    vm.fileInputUpdatePlugin(previewRes, file.get('fileInputMeta'));
                    if (saveName) saveName();
                    vm.$emit("setPreviewAble");
                    showTip && this.$message.success("保存成功");
                }
            })
        },
        /**
         * 在调用文本上传插件更新
         * @param colJson
         * @param fileInputMeta
         */
        fileInputUpdatePlugin(colJson = '', fileInputMeta) {
            const {services} = this;
            const tranStepId = this.sourceData.id;
            let params = new FormData();
            params.set('tranStepId', tranStepId);
            params.set('colJson', colJson);
            params.set('fileInputMeta', fileInputMeta);
            services.fileInputUpdatePlugin(params);
        },
        /**
         * 获取 预览的字段数据
         * @returns {*}
         */
        getPreviewData() {
            const {services} = this;
            const previewStepId = this.sourceData.id,
                {transId} = this.rowData,
                selectSize = '100000',
                limitSize = 100;
            return services.pluginPreview(transId, previewStepId, selectSize, limitSize, null, false, Date.now().toString()).then(res => {
                if (res.data.status === 0) {
                    let data = JSON.parse(res.data.data);
                    return JSON.stringify((data?.schema?.fields || []).map(item => {
                        return {
                            columnName: item.code,
                            columnZhName: item.name,
                            columnType: item.type,
                        }
                    }))
                } else {
                    return JSON.stringify([]);
                }

            })
        },
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        updateData(data) {
            if (this.pluginMeta.useHeader !== true) data.pluginMeta.useHeader = this.pluginMeta.useHeader;
            if (this.pluginMeta.dataUpdateWay !== 'DATACOVERAGE') data.pluginMeta.dataUpdateWay = this.pluginMeta.dataUpdateWay;
            this.pluginMeta = data.pluginMeta;
            if (data.pluginMeta.fileType !== 'csv') {
                this.excelSheetNames = data.excelSheetNames;
                this.excelSheetNames = this.excelSheetNames.sort(this.compareed('sheetNo'))
                for (let i = 0; i < this.excelSheetNames.length; i++) {
                    if (this.excelSheetNames[i].sheetNo === 0) {
                        this.pluginMeta.sheetName = this.excelSheetNames[i].excelSheetName;
                    }
                }
            }

            this.files = data.file;
            this.clear_data();
            this.close();
        },
        setFileDataUpdateWays(data) {
            for (let i in data) {
                let temp = {
                    label: data[i],
                    value: i
                }
                this.fileDataUpdateWays.push(temp);
            }
            this.fileDataUpdateWays.reverse()
        },
        judgeNull(obj, data) {
            if (obj === "" || obj === null || obj === undefined) {
                return data;
            } else {
                return obj;
            }
        }
    },
    created() {
        const vm = this, {pluginServices, pluginMock, loadParams} = this;
        loadParams.loading = true;
        let services = vm.getServices(pluginServices, pluginMock);
        services.getCicadaFileInputPluginPage(vm.sourceData.id, loadParams).then(result => {
            if (result.data.status === 0) {
                vm.setFileDataUpdateWays(result.data.data.fileDataUpdateWays); // 设置给更新路径
                vm.pluginMeta = result.data.data.fileInputMeta;
                vm.excelSheetNames = result.data.data.fileInputMeta.sheetNames;
                vm.pluginMeta.dataUpdateWay = vm.judgeNull(vm.pluginMeta.dataUpdateWay, "DATACOVERAGE");
                vm.csvSeparator = result.data.data.csvSeparator;
                if (result.data.data.fileInputMeta.sheetNames !== null) {
                    vm.excelSheetNames = vm.excelSheetNames.sort(vm.compareed('sheetNo'))
                }
            }
        })
        let {hasLimit, limit} = this.getLimit();
        this.hasLimit = hasLimit;
        this.limitNum = limit;
    }

}
</script>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped lang="less">
.files {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    word-break: break-all;

}

.row_check {
    padding-left: 60px;
}
</style>

