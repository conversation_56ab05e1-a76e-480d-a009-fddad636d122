<template>
    <div class="ce-union">
        <el-form size="mini" class="ce-form mb10" label-position="right" label-width="120px">
            <el-form-item
                    v-for="(attr , i ) in attrsList"
                    :key="i"
                    
            >
                <div slot="label" class="ce-label_title"> <span> *</span>{{attr.label + ' :'}}</div>
                <span class="formTip" v-if="attr.tip"><i class="el-icon-info"></i>{{attr.tip}}</span>
                <div class="select_right">
                   
                    <el-button-group  v-if="attr.type === 'result'">
                        <el-button type="primary" @click="submitAutoUnion">{{ dropName  }}</el-button>
                        <el-popover
                                popper-class="ce-map_pop"
                                width="136"
                                trigger="hover">
                            <ul class="ce-map">
                                <li class="ce-map_item" v-for="opt in dropDownItems" :key="opt.value"
                                    @click="handleCommand(opt.value)"
                                    :class="{'isActive':dropValue === opt.value}">{{ opt.label }}
                                </li>
                            </ul>
                            <el-button class="ce-map_btn" type="primary" slot="reference"
                                       icon="el-icon-arrow-down"></el-button>
                        </el-popover>

                    </el-button-group>
                </div>
                <template v-if="attr.type === 'data_obj'">
                    <el-row class="ce-cont_pd">
                        <el-col >
                            <el-radio v-model="pluginMeta.repeat" label="UNION">去重</el-radio>
                            <el-radio v-model="pluginMeta.repeat" label="UNION ALL">不去重</el-radio>
                        </el-col>
                    </el-row>
                </template>
              
            </el-form-item>
        </el-form>
        <common-table height="calc(100% - 86px)"
                    :columns="resultColumns"
                    :pagination="false"
                    :data="result">
            <template slot="header-opers">
                <el-button type="text" title="添加" class="el-icon-plus" @click="addUnion"></el-button>
            </template>
            <template slot="header-belongTableColCodeOne">
                <p class="ce-header_cell" :title="resultColumns[2].label">{{resultColumns[2].label}}</p>
            </template>
            <template slot="header-belongTableColCodeTwo">
                <p class="ce-header_cell" :title="resultColumns[3].label">{{resultColumns[3].label}}</p>
            </template>
            <template
                    slot="opers"
                    slot-scope="scope"
            >
                <el-popconfirm
                        title="确定删除吗？"
                        @confirm="handleClickDelete(scope.row, scope.$index)"
                >
                    <el-button type="text"
                                slot="reference"
                                > 删除
                    </el-button>
                </el-popconfirm>
            </template>
          
            <el-input slot="colCode"
                        slot-scope="scope"
                        v-model.trim="scope.row.colCode"
                        @input="sout(scope.row.colCode, scope.$index)"
            ></el-input>
            <el-input slot="colName"
                        slot-scope="{ row }"
                        v-model.trim="row.colName"
            >
            </el-input>
            <el-select slot="belongTableColCodeOne"
                        slot-scope="scope"
                       filterable
                        v-model="scope.row.belongTableColCodeOne"
            >
                <el-option v-for="item in leftTable.fields"
                        :key="item.value"
                        :label="item.colName"
                        :value="item.colCode"
                >
                </el-option>
            </el-select>
            <el-select slot="belongTableColCodeTwo"
                        slot-scope="{ row }"
                       filterable
                        v-model="row.belongTableColCodeTwo">
                <el-option v-for="item in rightTable.fields"
                        :key="item.value"
                        :label="item.colName"
                        :value="item.colCode"
                >
                </el-option>
            </el-select>
        </common-table>
        <preView ref="preview"></preView>

    </div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import UnionDialog from "./UnionDialog";
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    export default {
        name: "CicadaUnionJoin",
        mixins : [pluginMixins , servicesMixins],
        components: {
            UnionDialog,
            preView
        },
        props: {
            sourceData: Object,
            rowData : Object,
            loadParams: Object,
            hideMsg : Boolean
        },
         computed:{
            dropName(){
                const vm = this , {dropDownItems , dropValue} = vm;
                return dropDownItems.find(item => item.value === dropValue).label;
            }
        },
        data() {
            return {
                dropDownItems: [{label: "按字段名合并" ,value : "byCode"}, {label:"按字段中文名合并" , value : "byName"}],
                resultColumns: [
                    {prop: 'colCode', label: '合并结果字段名'},
                    {prop: 'colName', label: '合并结果字段中文名'},
                    {prop: 'belongTableColCodeOne', label: '合并来源表:'},
                    {prop: 'belongTableColCodeTwo', label: '合并来源表:'},
                    {prop: 'opers', label: '操作',align: 'center', width: 70},
                ],
                aggData:[],
                btnActive: -2,
                leftSelected:[],
                rightSelected:[],
                title: "添加输出字段",
                attrsList: [
                    {
                        label: ' 设置合并方式',
                        type: 'data_obj',
                        value: '',
                    },
                    {
                        label: ' 设置合并结果',
                        type: 'result',
                        value: '',
                        // tip : "若来源表出现重名字段,请修改字段别名,否则默认只保留一个字段"
                    }
                ],
                tableInfo: { //左右表
                    left : {
                        tableName : ''
                    },
                    right : {
                        tableName : ''
                    }
                },
                joinFieldList: [  //连接字段
                    //{ left: '', right: '' }
                ],
                leftTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                rightTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                result: [],
                pluginMeta: {},
                dropValue:"byCode"
            }
        },
        methods: {
            handleClickDelete(row, index) {
                this.result.splice(index, 1);
            },
            submitAutoUnion() {
                this.$dgConfirm('此操作将覆盖之前的配置, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.autoUnion();
                });
            },
            autoUnion() {
                this.result = [];
                for(let i = 0; i < this.leftTable.fields.length; i++) {
                    for(let j = 0; j < this.rightTable.fields.length; j++) {
                        if(this.dropValue === 'byName' ? this.leftTable.fields[i].colName === this.rightTable.fields[j].colName : this.leftTable.fields[i].colCode === this.rightTable.fields[j].colCode ) {
                            this.result.push({
                                objId: this.tableInfo.left.tableId,
                                objCode: this.tableInfo.left.tableCode,
                                replaceFieldName: this.leftTable.fields[i].colCode,
                                newFieldName: this.leftTable.fields[i].colCode,
                                colCode: this.leftTable.fields[i].colCode,
                                colName: this.leftTable.fields[i].colName,
                                valType: this.leftTable.fields[i].type,
                                length: this.leftTable.fields[i].length,
                                precision: this.leftTable.fields[i].precsn,
                                columnZhName: this.leftTable.fields[i].colName,
                                belongTableColCodeOne: this.leftTable.fields[i].colCode,
                                belongTableColCodeTwo: this.rightTable.fields[j].colCode
                            })
                            break;
                        }
                    }
                }
                if(this.result.length === 0) {
                    this.$message.warning("目前暂无可合并字段");
                }else {
                    // this.$message.success("合并成功");
                }
            },
            addUnion() {
                this.result.push({
                    colCode: "",
                    colName: "",
                    objId: this.tableInfo.left.tableId,
                    objCode: this.tableInfo.left.tableCode,
                    replaceFieldName: "",
                    newFieldName: "",
                    valType: "",
                    length: "",
                    precision: "",
                    columnZhName: "",
                    belongTableColCodeOne: "",
                    belongTableColCodeTwo:""
                })
            },
            sout(val, index) {
                let codeReg = new RegExp("^[a-zA-Z0-9_]+$");
                let len = val.length;
                let str = '';
                for(let i = 0; i < len; i++){
                    if(codeReg.test(val[i])){
                        str+=val[i];
                    }
                }
                this.result[index].colCode = str;
            },
            handleCommand(value) {
                this.dropValue = value;
                this.autoUnion();
            },
            canceled() {
                this.$refs.dialog.cancel();
            },
            submit() {
                this.$refs.dialog.submit();
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            save(saveName,showTip) {
                if(this.tableInfo.left.tableName === "") {
                    this.$message.warning("输入源必须为两个");
                    return;
                }
                if(this.result.length === 0) {
                    this.$message.warning("请选择输出字段");
                    return;
                }
                for(let i = 0; i < this.result.length; i++) {
                    if(this.result[i].colCode === ""
                        || this.result[i].colName === ""
                        || this.result[i].belongTableColCodeOne === ""
                        || this.result[i].belongTableColCodeTwo === ""
                    ) {
                        this.$message.warning("请不要留有空字段");
                        return;
                    }
                    else {
                        for(let j = i + 1; j < this.result.length; j++) {
                            if(this.result[i].colCode === this.result[j].colCode) {
                                this.$message.warning("存在相同的字段名" + this.result[j].colCode+",请修改配置");
                                return;
                            }
                            if(this.result[i].colName === this.result[j].colName) {
                                this.$message.warning("存在相同的字段中文名" + this.result[j].colName+",请修改配置");
                                return;
                            }
                        }
                        this.result[i].replaceFieldName = this.result[i].colCode;
                        this.result[i].newFieldName = this.result[i].colCode;
                        this.result[i].replaceFieldName = this.result[i].colCode;
                        this.result[i].columnZhName = this.result[i].colName;
                    }
                }
                const vm = this, {pluginServices, pluginMock, loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                loadParams.loading = true;
                vm.pluginMeta.cicadaAggregatorFieldExps = this.result;
                services.saveCicadaUnionJoinPlugin(vm.sourceData.id, vm.pluginMeta ,loadParams).then(result => {
                    if(result.data.status === 0){
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                        showTip && vm.$message.success("保存成功！");
                    }
                })
            },
            cancel() {
                this.clear_data();
                this.close();
            },
            updateOutput(data) {
                this.result = data;
                this.clear_data();
                this.close();
            },
            showTip() {
                this.$message.success("提示")
            },
            initData() {   //初始化
                const vm = this, {pluginServices, pluginMock, settings, loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                loadParams.loading = true;
                services.cicadaUnionJoinPluginPage(vm.sourceData.id, loadParams).then(result => {
                    if (result.data.status === 0) {
                        let leftSelCol = [];
                        let rightSelCol = [];
                        vm.result = [];
                        result.data.data.cicadaAggregatorFieldExps.forEach(item => {
                            let cp = {
                                objId: item.objId,
                                colCode: item.replaceFieldName,
                                replaceFieldName: item.replaceFieldName,
                                newFieldName: item.replaceFieldName,
                                colName: item.columnZhName,
                                valType: item.valType,
                                length: item.length,
                                precision: item.precision,
                                columnZhName: item.columnZhName,
                                belongTableColCodeTwo: item.belongTableColCodeTwo,
                                belongTableColCodeOne: item.belongTableColCodeOne
                            }
                            vm.result.push(cp);
                        })
                        result.data.data.columns.leftTableColumns.forEach(leftCol => {
                            let item = {
                                colCode: leftCol.columnName,
                                colName: leftCol.columnZhName === "" ? leftCol.columnName : leftCol.columnZhName,
                                id: leftCol.dataColumnId,
                                type: leftCol.columnType,
                                length: leftCol.length,
                                precsn: leftCol.precsn
                            };
                            vm.leftTable.fields.push(item);
                            if (leftSelCol.includes(leftCol.columnName) || leftSelCol.includes(leftCol.columnZhName)) {
                                vm.leftSelected.push(item);
                            }
                        });
                        result.data.data.columns.rightTableColumns.forEach(rightCol => {
                            let item = {
                                colCode: rightCol.columnName,
                                colName: rightCol.columnZhName === "" ? rightCol.columnName : rightCol.columnZhName,
                                id: rightCol.dataColumnId,
                                type: rightCol.columnType,
                                length: rightCol.length,
                                precsn: rightCol.precsn
                            };
                            vm.rightTable.fields.push(item);

                            if (rightSelCol.includes(rightCol.columnName) || rightSelCol.includes(rightCol.columnZhName)) {
                                vm.rightSelected.push(item);
                            }
                        });
                        // 设置表名
                        vm.tableInfo = {
                            left: {
                                tableName: result.data.data.pluginMeta.leftStepName,
                                tableCode: result.data.data.pluginMeta.leftStepCode,
                                tableId: result.data.data.pluginMeta.leftStepId
                            },
                            right: {
                                tableName: result.data.data.pluginMeta.rightStepName,
                                tableCode: result.data.data.pluginMeta.rightStepCode,
                                tableId: result.data.data.pluginMeta.rightStepId
                            }
                        };
                        vm.resultColumns[2].label = vm.resultColumns[2].label + vm.tableInfo.left.tableName;
                        vm.resultColumns[3].label = vm.resultColumns[3].label + vm.tableInfo.right.tableName;
                        vm.pluginMeta = result.data.data.pluginMeta;
                        if(vm.pluginMeta.repeat === "" || vm.pluginMeta.repeat === undefined) {
                            vm.pluginMeta.repeat = "UNION"
                        }
                    }
                })
            }
        },
        created() {
            if(!this.hideMsg) this.initData();
            // this.isSeniorShow = {
            //     partitionShow: false,
            //     fieldInput: false,
            //     fieldOutPut: true,
            //     isCopyOrDispense: false,
            //     isSampleExpr: false,
            //     isThread: false,
            //     isException: false,
            //     notTransition:false,
            // }
        }
    }
</script>

<style lang="less" scoped>
    .over-dialog /deep/ .dg-dialog .dg-dialog__body {
        overflow: hidden;
        position: relative;

    }

    .mainButton {
        width: 100%;
    }
    .ce-label_title {
        float: left;
        color: rgba(0,0,0,0.65);
        font-size: 14px;
        > span {
            color: #F56C6C;
        }
    }
    .ce-cont_pd {
        
       
    }
    .select_right {
        display: flex;
        justify-content: flex-end;
    
    }
    .ce-union {
        overflow: hidden;
        height: 100%;
    }
.ce-map {
    
    &_item {
        padding: 0 10px;
        line-height: 30px;
        color: #555;
        cursor: pointer;
        margin: 6px 0;

        &.isActive {
        background-color: rgba(24, 144, 255, .12);
        color: #1890ff;
        }

        &:hover {
        background-color: rgba(24, 144, 255, .08);
        }
    }

    &_btn {
        width: 30px;
        min-width: auto;
        padding: 0;
        vertical-align: top;
    }

}

</style>
<style lang="less" scoped>
    .ce-header_cell {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
</style>
