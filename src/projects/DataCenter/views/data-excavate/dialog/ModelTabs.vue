<template>
    <div class="modelContainer" v-loading="loading">
        <div class="ce-edit-btn">
            <el-button size="mini" type="primary" @click="openSave" v-if="isTemplate && !saved">保存为模板</el-button>
            <el-button size="mini" type="primary" @click="addTab">新建</el-button>
            <el-button size="mini" type="primary" @click="openModel">打开</el-button>
            <el-button size="mini" type="primary" @click="backTo">返回</el-button>
        </div>
        <el-tabs
                class="ce-editTabs"
                :class="{'ce-editModel': isTemplate && !saved}"
                v-if="editableTabs.length" v-model="editableTabsValue" type="border-card" closable
                @tab-click="tabClick"
                @tab-remove="removeTab">
            <el-tab-pane
                    v-for="item in editableTabs"
                    :key="item.id"
                    :label="item.label"
                    :name="item.id"
            >
                <ModelPage :treeData="treeData" :rowData="item.rowData" :isTemplate="isTemplate" :tabId="item.id" :focusTab="editableTabsValue" />
            </el-tab-pane>
        </el-tabs>
        <OpenModel ref="openM" @openPage="addTab"/>
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    import ModelPage from '../ModelPage'
    import OpenModel from './OpenModel'
    import {common} from "@/api/commonMethods/common"

    export default {
        name: "ModelContainer",
        components: {
            ModelPage,
            OpenModel
        },
        mixins: [common],
        props: {
            treeData: Array,
        },

        data() {
            return {
                editableTabsValue: '1',
                editableTabs: [],
                tabIndex: 0,
                newId: '',
                isTemplate: false,
                loading: false,
                modelsName: [],
                saved: false
            }
        },
        methods: {
            tabClick(item) {
                this.isTemplate = item.$children[0].rowData.isTemplate;
                this.saved = item.$children[0].rowData.saved;
            },
            openSave() {
                const vm = this;
                globalBus.$emit("saveAsModel", null, true, vm.editableTabsValue);
            },
            setSaved(transId) {
                const vm = this;
                this.saved = true;
                this.editableTabs.forEach(tab => {
                    if (transId === tab.id) {
                        tab.rowData.saved = true;
                    }
                });
            },
            onListenFun() {
                globalBus.$on("modelSaveSuccess", this.setSaved);
                globalBus.$on('changeName', this.change);
            },
            offListenFun() {
                globalBus.$off("modelSaveSuccess", this.setSaved);
                globalBus.$off('changeName', this.change);
            },
            modelValidate(value, instance, done, tip) {
                const vm = this;
                if (!value) {
                    instance.editorErrorMessage = tip + '不能为空!';
                    return;
                }
                let result = vm.modelsName.filter(item => {
                    return item.name === value;
                });
                if (result.length > 0) {
                    instance.editorErrorMessage = tip + "已存在";
                } else {
                    done();
                }
            },
            saveModel() {
                const vm = this;
                vm.loading = true;
                vm.$emit("openNewModel");
                this.$emit('closeTab');
                setTimeout(() => {
                    vm.loading = false;
                    vm.removeTab(vm.editableTabsValue);
                }, 500)
            },
            reName(newName, transId) {
                if (this.editableTabs.length > 0) {
                    this.editableTabs.forEach(item => {
                        if (transId === item.id) {
                            item.label = newName;
                        }
                    })
                }
            },
            change(name, id) {
                this.editableTabs.forEach(item => {
                    if (item.id === id) {
                        item.label = name;
                    }
                })
            },
            openModel() {
                this.$refs.openM.open({}, 'dataCenter', 'open');
            },
            removeTab(targetName) {
                const vm = this;
                let tabs = this.editableTabs;
                let activeName = this.editableTabsValue;
                if (activeName === targetName) {
                    tabs.forEach((tab, index) => {
                        if (tab.id === targetName) {
                            let nextTab = tabs[index + 1] || tabs[index - 1];
                            if (nextTab) {
                                activeName = nextTab.id;
                            }
                        }
                    });
                }
                this.editableTabsValue = activeName;
                this.editableTabs = tabs.filter(tab => tab.id !== targetName);
                this.editableTabs.forEach(tab => {
                    if (activeName === tab.id) {
                        vm.isTemplate = tab.rowData.isTemplate;
                        vm.saved = tab.rowData.saved;
                    }
                });
                if (this.editableTabs.length === 0) {
                    this.$emit('closeTab');
                }
            },
            addNewTrans(rowData, transName) {
                const _this = this;
                this.$axios.get("/dataMining/createTrans?transName=" + transName).then(res => {
                    //console.log("获取新创建的id：", res,res.data.status,res.data.data);
                    if (res.data.status === 0) {
                        _this.showFlow(rowData, res.data.data, transName);
                    } else {
                        _this.$message.error(res.data.msg);
                    }
                }).catch(err => {
                    console.log(err);
                    this.$message.error("服务器发生异常，请联系管理员");
                });
            },
            addTab(rowData, isTemplate=false, dirId) {
                let newTransName = 'newTrans_' + new Date().getTime();
                this.isTemplate = isTemplate;
                rowData.isTemplate = isTemplate;
                rowData.saved = this.saved = false;
                if (rowData.transId === undefined) {
                    rowData.dirId = dirId;
                    this.addNewTrans(rowData, newTransName);
                } else {
                    this.showFlow(rowData, rowData.transId, newTransName);
                }
            },

            showFlow(rowData, new_trans_id, newTransName) {
                let tabLabel;
                tabLabel = rowData.modelName ? rowData.modelName : newTransName;
                let rowD = rowData.modelName ? rowData : {
                    dirParentId: "",
                    modelName: newTransName,
                    modelSpec: "",
                    modelType: "",
                    releaseDate: "",
                    status: "",
                    transId: new_trans_id,
                    isNew: true,
                    ...rowData
                };
                let newTab = {
                    id: new_trans_id,
                    label: tabLabel,
                    rowData: rowD,
                };
                let hasTab = false;
                hasTab = this.hasTabs(newTab, hasTab);//
                if (hasTab || this.editableTabs.length === 0) {
                    this.editableTabs.push(newTab);
                    this.editableTabsValue = new_trans_id;
                }
            },

            hasTabs(newTab, hasTab) {
                let tabs = this.editableTabs;
                for (let i = 0; i < tabs.length; i++) {
                    if (tabs[i].id === newTab.id) {
                        this.editableTabsValue = newTab.id;
                        hasTab = false;
                        return;
                    } else {
                        hasTab = true;
                    }
                }
                return hasTab;
            },
            backTo() {
                this.$emit('backTo');
            }
        },
        created() {
            this.onListenFun();
        },
        destroyed() {
            this.offListenFun();
        }
    }
</script>

<style scoped lang="less">
    .modelContainer {
        position: fixed;
        left: 0;
        top: 4rem;
        width: 100%;
        height: calc(100% - 50px);
        overflow: hidden;
        background: #f5f5f5;
        z-index: 30;
    }

    .ce-edit-btn {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 30;
        padding: 0 10px;
        height: 40px;
        line-height: 38px;
    }
</style>
