<template>
    <div class="settingSenior" v-loading="settings.loading">
        <el-collapse v-model="activeCollapse" @change="collapseChange">
            <!--            分区方式-->
            <el-collapse-item v-if="partitionShow" title="分区方式" name="1">
                <div>
                    <el-row class="ce-row-item">
                        <el-col :span="4">
                            <span>分区类型:</span>
                        </el-col>
                        <el-col :span="10">
                            <el-select size="mini" v-model="partitionType" @change="changePartitionType">
                                <el-option v-for="item in partition"
                                           :label="item.label" :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                    <!--                    无分区-->
                    <div class="mb10" v-show="isShowDefTable">
                        <div class="mb5 tr">
                            <el-button
                                    type="primary"
                                    size="mini"
                                    @click="addDefaultTable"
                                    plain
                            >添加
                            </el-button>
                        </div>

                        <common-table
                                :data="defaultPartitionData"
                                size="mini"
                                class="width100"
                                :columns="tRegionHead"
                                height="244px"
                                :pagination="false"
                        >
                            <template slot="isExecute" slot-scope="{row , $idnex}">
                                <el-checkbox v-model="row.isExecute"></el-checkbox>
                            </template>
                            <template slot="repartitionExecute" slot-scope="{row , $idnex}">
                                <el-checkbox v-model="row.repartitionExecute"></el-checkbox>
                            </template>
                            <template slot="start"  slot-scope="{row , $idnex}">
                                <el-input size="mini" :title="row.start" v-model="row.start"></el-input>
                            </template>
                            <template slot="end"  slot-scope="{row , $idnex}">
                                <el-input size="mini" :title="row.end" v-model="row.end"></el-input>
                            </template>
                            <template slot="incrementTime"  slot-scope="{row , $idnex}">
                                <el-input size="mini" :title="row.incrementTime" v-model="row.incrementTime"></el-input>
                            </template>
                            <template slot="operate" slot-scope="{row , $index}">
                                <i
                                        class="icon ce_link model_status"
                                        v-for="(col , inx) in operateIcons"
                                        :key="inx"
                                        :title="col.tip"
                                        v-html="col.icon"
                                        @click="col.clickFn(row,$index , 'defaultPartitionData')"
                                ></i>
                            </template>
                        </common-table>
                    </div>
                    <!--                    分区类型----业务分区-->
                    <div class="partitionClass" v-show="isShowBusiTable" style="margin-top: 10px">
                        <el-row class="ce-row-item">
                            <el-col :span="4">
                                <span>分区选择:</span>
                            </el-col>
                            <el-col :span="10">
                                <el-select size="mini" v-model="partitionSelect" @change="changePartitionType">
                                    <el-option v-for="item in partitionSelectData"
                                               :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-col>
                        </el-row>
                        <el-button style="margin-top: 5px"
                                   type="primary"
                                   size="mini"
                                   @click="synchInfo"
                                   class="btn_new_model"
                                   plain
                        >同步分区信息
                        </el-button>

                        <el-table
                                :data="partitionTableData"
                                border
                                style="width: 100%"
                                size="mini"
                                height="244px"
                        >
                            <el-table-column v-for="(item,i) in tPartitionHead"
                                             :label="item.label"
                                             :prop="item.prop"
                                             :width="item.width"
                                             align="center"

                            >
                                <template slot-scope="scope">
                                    <label v-if="item.type==='tableCell'">{{scope.row[item.prop]}}</label>
                                    <!--                    输入框-->
                                    <el-input size="mini" v-if="item.type === 'input'"
                                              :title="scope.row[item.prop]"
                                              v-model="scope.row[item.prop]"
                                    ></el-input>
                                    <el-checkbox v-if="item.type==='checkbox'"
                                                 v-model="scope.row[item.prop]"></el-checkbox>
                                    <!--                    开关-->
                                    <el-switch size="mini" v-if="item.type === 'switch'"
                                               v-model="scope.row[item.prop]"
                                               active-color="#13ce66"
                                               inactive-color="#EAE3E3"
                                               active-text="是"
                                               inactive-text="否"
                                    ></el-switch>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--                    分区类型----region分区-->
                    <div class="partitionClass" v-show="isShowRegionPartition">
                        <el-button
                                type="primary"
                                size="mini"
                                @click="getPreSplit"
                                class="btn_new_model"
                                plain
                        >获取预分区
                        </el-button>
                        <common-table
                                :data="regionData"
                                size="mini"
                                class="width100"
                                :columns="tRegionHead"
                                height="244px"
                                :pagination="false"
                        >
                            <template slot="isExecute" slot-scope="{row , $idnex}">
                                <el-checkbox v-model="row.isExecute"></el-checkbox>
                            </template>
                            <template slot="repartitionExecute" slot-scope="{row , $idnex}">
                                <el-checkbox v-model="row.repartitionExecute"></el-checkbox>
                            </template>
                            <template slot="start"  slot-scope="{row , $idnex}">
                                <el-input size="mini" :title="row.start" v-model="row.start"></el-input>
                            </template>
                            <template slot="end"  slot-scope="{row , $idnex}">
                                <el-input size="mini" :title="row.end" v-model="row.end"></el-input>
                            </template>
                            <template slot="incrementTime"  slot-scope="{row , $idnex}">
                                <el-input size="mini" :title="row.incrementTime" v-model="row.incrementTime"></el-input>
                            </template>
                            <template slot="operate" slot-scope="{row , $index}">
                                <i
                                        class="icon ce_link model_status"
                                        v-for="(col , inx) in operateIcons"
                                        :key="inx"
                                        :title="col.tip"
                                        v-html="col.icon"
                                        @click="col.clickFn(row,$index , 'regionData')"
                                ></i>
                            </template>
                        </common-table>


                    </div>
                    <!--                    分区类型----时间分区-->
                    <div class="partitionClass" v-show="isShowTimePartition">
                        <div>
                            <el-form size="mini" v-model="timeFormData" label-width="120px">
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="时间分区字段:" label-width="100px">
                                            <el-row :gutter="2">
                                                <el-col :span="10">
                                                    <el-select placeholder="列簇" v-show="dbType === 'kv'"
                                                               :title="timeFormData.timePartitionField"
                                                               v-model="timeFormData.timePartitionField">
                                                        <el-option v-for="optPart in timePartitionOption"
                                                                   :key="optPart.value"
                                                                   :value="optPart.value"
                                                                   :label="optPart.label"
                                                        ></el-option>
                                                    </el-select>
                                                </el-col>
                                                <el-col :span="10">
                                                    <el-input v-model.trim="timeFormData.partitionFieldInput"></el-input>
                                                </el-col>
                                            </el-row>

                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="时间存储格式:" label-width="100px">
                                            <el-select :title="timeFormData.timeStorageFormat"
                                                       v-model="timeFormData.timeStorageFormat">
                                                <el-option v-for="optPart in timeStorageOption"
                                                           :key="optPart.value"
                                                           :value="optPart.value"
                                                           :label="optPart.label"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="时间分区类型:" label-width="100px">
                                            <el-select :title="timeFormData.timePartitionType"
                                                       v-model="timeFormData.timePartitionType">
                                                <el-option v-for="optPart in partitionTypeOption"
                                                           :key="optPart.value"
                                                           :value="optPart.value"
                                                           :label="optPart.label"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="开始时间:" label-width="80px">
                                            <el-date-picker style="width: 130px"
                                                            v-model="timeFormData.startTime"
                                                            type="date"
                                                            placeholder="开始时间">
                                            </el-date-picker>
                                            <!--                                        <el-input size="mini"></el-input>-->
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="结束时间:" label-width="80px">
                                            <el-date-picker style="width: 130px"
                                                            v-model="timeFormData.endTime"
                                                            type="date"
                                                            placeholder="结束时间">
                                            </el-date-picker>
                                            <!--                                        <el-input size="mini"></el-input>-->
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <div class="mb10 tr">
                            <el-button
                                    v-for="(btn , inx) in timePartitionButton"
                                    :key="inx"
                                    size="mini"
                                    :type="btn.type"
                                    @click="btn.clickFn"
                            >{{btn.name}}
                            </el-button>
                        </div>
                        <div>
                            <common-table
                                    :data="timeData"
                                    size="mini"
                                    class="width100"
                                    :columns="tRegionHead"
                                    height="244px"
                                    :pagination="false"
                            >
                                <template slot="isExecute" slot-scope="{row , $idnex}">
                                    <el-checkbox v-model="row.isExecute"></el-checkbox>
                                </template>
                                <template slot="repartitionExecute" slot-scope="{row , $idnex}">
                                    <el-checkbox v-model="row.repartitionExecute"></el-checkbox>
                                </template>
                                <template slot="start"  slot-scope="{row , $idnex}">
                                    <el-input size="mini" :title="row.startTime" v-model="row.startTime"></el-input>
                                </template>
                                <template slot="end"  slot-scope="{row , $idnex}">
                                    <el-input size="mini" :title="row.endTime" v-model="row.endTime"></el-input>
                                </template>
                                <template slot="incrementTime"  slot-scope="{row , $idnex}">
                                    <el-input size="mini" :title="row.incrementTime" v-model="row.incrementTime"></el-input>
                                </template>
                                <template slot="operate" slot-scope="{row , $index}">
                                    <i
                                            class="icon ce_link model_status"
                                            v-for="(col , inx) in operateIcons"
                                            :key="inx"
                                            :title="col.tip"
                                            v-html="col.icon"
                                            @click="col.clickFn(row,$index , 'timeData')"
                                    ></i>
                                </template>
                            </common-table>

                        </div>
                    </div>

                </div>
                <div class="tr">
                    <el-button size="mini" @click="savePartition('1')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--            字段输入-->
            <el-collapse-item v-if="fieldInput" name="2">
                <div>
                    <el-table
                            :data="fieldInputData"
                            border
                            style="width: 100%"
                            size="mini"
                            height="244px"
                    >
                        <el-table-column v-for="(item,i) in tFieldInputHead"
                                         :label="item.label"
                                         :prop="item.prop"
                                         :width="item.width"
                                         align="center"

                        >
                            <template slot-scope="scope">
                                <label v-if="item.type==='tableCell'">{{scope.row[item.prop]}}</label>
                                <!--                    输入框-->
                                <el-input size="mini" v-if="item.type === 'input'"
                                          :title="scope.row[item.prop]"
                                          v-model="scope.row[item.prop]"
                                ></el-input>
                                <!--                    多选框-->
                                <el-checkbox v-if="item.type==='checkbox'" v-model="scope.row[item.prop]"></el-checkbox>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="tr">
                    <el-button size="mini" @click="saveFieldInput('2')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--            字段输出-->
            <el-collapse-item title="字段输出" name="fieldOutPut" v-if="fieldOutPut">
                <div>
                    <common-table
                            :data="fieldTableData"
                            :columns="tFieldHead"
                            class="width100"
                            height="244px"
                            :pagination="false"
                    >
                        <template slot="transition"  slot-scope="{row}">
                            <el-input size="mini"
                                      :title="row.transition"
                                      v-model="row.transition"
                                      @blur="changeField(row)"
                                      disabled
                            >
                                <el-button class="ce-btn_default" :disabled="isShow.notTransition" style="padding: 0;" slot="append"
                                           icon="el-icon-edit"
                                           @click="editFn(row)"></el-button>
                            </el-input>
                        </template>
                        <template slot="type"  slot-scope="{row}">
                            <dg-select v-model="row.type" size="mini"
                                       :title="row.type"
                                       :data="typeOptions"
                                       @change="changeField(row)"
                                       disabled
                            >
                            </dg-select>
                        </template>
                        <template slot="index" slot-scope="{row , $index}">
                            <el-input size="mini"
                                      :title="row.index"
                                      v-model.trim.number="row.index"
                                      @blur="changeField(row)"
                                      @input="checkRowkey(row)"
                            ></el-input>
                        </template>
                    </common-table>
                </div>
                <!--                <div class="tr">-->
                <!--                    <el-button size="mini" @click="saveFieldOutput('3')" align="center" type="primary" plain>-->
                <!--                        {{checkBtn}}-->
                <!--                    </el-button>-->
                <!--                </div>-->
            </el-collapse-item>
            <!--字段变更-->
            <el-collapse-item title="字段动态传参" name="9" v-if="fieldsChange">
                <common-table
                        class="selectn"
                        :data="fieldChData"
                        :columns="fieldsHead"
                        :pagination="false"
                >
                    <template slot="field" slot-scope="scope">
                        <el-select v-model="scope.row.field" size="mini">
                            <el-option v-for="opt in fieldTableData"
                                       :key="opt.fieldName"
                                       :value="opt.fieldName"
                                       :label="opt.fieldName"
                            ></el-option>
                        </el-select>
                    </template>
                    <template slot="value" slot-scope="scope">
                        <el-input placeholder="常量或sys_time()" v-model.trim="scope.row.value" size="mini"></el-input>
                    </template>
                    <template slot="header-operate" slot-scope="scope">
                        <i class="ce_link icon" @click="addField">&#xe6e1;</i>
                    </template>
                    <template slot="operate" slot-scope="scope">
                        <i class="ce_link icon" @click="deleteField(scope.$index)">&#xe756;</i>
                    </template>
                </common-table>
                <div class="tr mt5">
                    <el-button size="mini" @click="saveFieldCh('9')" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--            分发机制-->
            <el-collapse-item title="分发机制" name="4" v-if="isCopyOrDispense">
                <div>
                    <el-radio-group v-model="copyOrDispense">
                        <el-radio :label="1">复制</el-radio>
                        <el-radio :label="2">分发</el-radio>
                    </el-radio-group>
                </div>
                <div class="tr">
                    <el-button size="mini" @click="saveIsCopy('4')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--            线程数-->
            <el-collapse-item title="线程数" name="5" v-if="isThread">
                <div class="ce-row-box">
                    <el-row class="ce-row-item">
                        <el-col :span="4">线程数:</el-col>
                        <el-col :span="10">
                            <el-input size="mini" v-model="threadCount"></el-input>
                        </el-col>
                    </el-row>
                </div>
                <div class="tr">
                    <el-button size="mini" @click="saveThreadCnt('5')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--            异常处理-->
            <el-collapse-item title="异常处理" name="6" v-if="isException">
                <div class="ce-row-box">
                    <el-row class="ce-row-item">
                        <el-col :span="4">异常处理:</el-col>
                        <el-col :span="10">
                            <el-select size="mini" v-model="exceptionMode">
                                <el-option v-for="item in optException"
                                           :label="item.name"
                                           :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                <div class="tr">
                    <el-button size="mini" @click="saveExceptionMode('6')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--            抽样设置-->
            <el-collapse-item title="抽样设置" name="7" v-if="isSampleExpr">
                <div class="ce-row-box">
                    <el-row class="ce-row-item">
                        <el-col :span="6">抽样表达式:</el-col>
                        <el-col :span="10">
                            <el-input size="mini" v-model="sampleExpr"></el-input>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">是否数据去重:</el-col>
                        <el-col :span="10">
                            <el-switch
                                    v-model="sampleDistinct"
                                    active-color="#13ce66"
                                    inactive-color="#DBDBDB"
                                    active-text="是"
                                    inactive-text="否"
                            >
                            </el-switch>
                        </el-col>
                    </el-row>
                </div>
                <div class="tr">
                    <el-button size="mini" @click="saveSampling('7')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--是否排序-->
            <el-collapse-item title="排序字段" name="8" v-if="isShow.isSort">
                <el-row :gutter="10">
                        <el-col :span="12">
                        <el-select size="mini" v-model="sortNode" placeholder="请选择排序字段">
                            <el-option
                                    v-for="opt in sortNodeOptions"
                                    :label="opt.label"
                                    :value="opt.value"
                            ></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="12">
                        <el-select size="mini" v-model="sort">
                            <el-option
                                    v-for="opt in sortOpt"
                                    :label="opt.label"
                                    :value="opt.value"
                            ></el-option>
                        </el-select>
                    </el-col>
                </el-row>
                <div class="tr">
                    <el-button size="mini" @click="saveSort('8')" align="center" type="primary" plain>
                        {{checkBtn}}
                    </el-button>
                </div>
            </el-collapse-item>
            <!--格式设置-->
            <el-collapse-item title="格式设置" name="8" v-if="fileUpload" v-show="fileType ==='excel' ">
                <div v-show="excelSheetNames.length > 0 && fileType ==='excel' ">
                    <div>
                        <span style="margin-left: 15px">Sheet页:</span>
                        <el-select v-model="sheetName" placeholder="请选择" style="margin-left: 10px" >
                            <el-option
                              v-for="item in excelSheetNames"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div style="margin-top:10px">
                        <span style="margin-left: 25px">起始行:</span>
                        <el-input :disabled="disabledFile" v-model="startRow" @keyup.native="startRow=startRow.replace(/[^0-9]/g,'')"  @blur="changeOutputFiled" placeholder="请输入内容" style="width: 46%;margin-left: 10px"></el-input>
                    </div>
                    <div style="margin-top:10px">
                        <span style="margin-left: 25px"> 起始列:</span>
                        <el-input  v-model="startColumn" @keyup.native="startColumn=startColumn.replace(/[^a-zA-Z]/g,'').toUpperCase()" @blur="changeOutputFiled" style="width: 46%;margin-left: 10px" ></el-input>
                    </div>
                    <div style="margin-top:10px;margin-left: 70px">
                        <el-checkbox v-model="useHeader">首行为字段名称</el-checkbox>
                    </div>
                </div>
            </el-collapse-item>
            <!--分区字段-->
            <el-collapse-item title="分区字段" name="" v-show="partitionNode">
                <el-row :gutter="5">
                    <el-col class="ce-normal_item ce-normal_head" :span="7">
                        <span>分区字段: </span>
                    </el-col>
                    <el-col :span="17" class="ce-normal_head">
                        <el-select
                            class="mr10"
                            size="mini"
                            v-model="partitionFieldName"
                            placeholder="请选择"
                            filterable
                            multiple
                            clearable
                            collapse-tags
                        >
                            <el-option
                                v-for="item in columns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-row>
            </el-collapse-item>

        </el-collapse>
        <PlugSetting ref="setting" @saveFn="saveFn"/><!--组件配置弹窗-->
    </div>
</template>

<script>
    import PlugSetting from '../ruleEditing/PlugSetting'
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";
    import {partitionNode} from "@/projects/DataCenter/views/plugin/component/partitionNode";

    export default {
        name: "settingSenior",
        mixins: [commonMixins, servicesMixins ,partitionNode],
        props: {
            sourceData: Object,
            isShow: Object,
            dbType: String,
        },
        components: {
            PlugSetting
        },
        watch: {
          startRow(val) {
                this.$emit("editStartRow", val);
          },
          sheetName(val) {
              this.changeOutputFiled();
              this.$emit("editSheetName", val);
          },
          startColumn(val) {
               this.$emit("editStartColumn", val);
           },
           useHeader(val) {
          	 this.startRow = "1";
          	 this.disabledFile = !this.disabledFile;
						 this.changeOutputFiled();
	           this.$emit("editUseHeader", val);
           }
        },
        data() {
            return {
                disabledFile: true,
                partitionShow: true,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: true,
                isSampleExpr: true,
                isThread: true,
                isException: true,
                fieldsChange : false , //字段变更
                fildUpload: false,

                defaultPartitionData: [],
                fieldChData : [],
                fieldsHead : [
                    {
                        label: "序号",
                        type: "index",
                        align : "center",
                        width: "70"
                    },
                    {
                        prop : "field" ,
                        label :"字段名称" ,
                        align : "center"
                    },{
                        prop : "value" ,
                        label : "参数值",
                        align : "center"
                    }, {
                        prop : "operate",
                        align : "center",
                        width : "50"
                    }
                ],
                checkBtn: '确定',
                activeCollapse: [],
                //分区方式
                partition: [],
                partitionType: "",
                isShowBusiTable: false,
                isShowDefTable: true,
                isShowTimePartition: false,
                isShowRegionPartition: false,

                partitionSelect: "",
                partitionSelectData: [
                    {
                        label: "分区1",
                        value: "分区1"
                    }
                ],
                tPartitionHead: [
                    {
                        prop: "num",
                        label: "序号",
                        type: "tableCell",
                    },
                    {
                        prop: "isExecute",
                        label: "是否执行",
                        type: "checkbox",
                    },
                    {
                        prop: "repetitiveExecute",
                        label: "重复执行",
                        type: "checkbox",
                    },
                    {
                        prop: "code",
                        label: "code",
                        type: "tableCell",
                    },
                    {
                        prop: "expression",
                        label: "表达式",
                        type: "input",
                        width: "100"
                    },
                    {
                        prop: "incrementalTime",
                        label: "增量时间",
                        type: "input",
                        width: "100"
                    },

                ],
                partitionTableData: [],

                //字段输出
                fieldTableData: [],
                tFieldHead: [],
                typeOptions :[],
                // 分发机制
                copyOrDispense: 1,
                //线程数
                threadCount: "",
                // 异常处理
                exceptionMode: "",
                optException: [
                    {
                        name: "中断异常数据",
                        value: "0",
                    }, {
                        name: "丢弃异常数据",
                        value: "1",
                    }
                    // , {
                    //     name: "分离异常数据",
                    //     value: "2",
                    // }
                ],
                // 抽样设置
                sampleDistinct: false,
                sampleExpr: "",

                //字段输入
                fieldInputData: [],
                tFieldInputHead: [
                    {
                        prop: "fieldInputNum",
                        label: "序号",
                        type: "tableCell",
                    },
                    {
                        prop: "inputFieldName",
                        label: "字段名称",
                        type: "tableCell",
                    },
                    {
                        prop: "inputType",
                        label: "类型",
                        type: "tableCell",
                    }, {
                        prop: "inputLength",
                        label: "长度",
                        type: "tableCell",
                    }, {
                        prop: "inputPrecision",
                        label: "精度",
                        type: "tableCell",
                    },
                    {
                        prop: "inputExpression",
                        label: "表达式",
                        type: "input",
                        width: "100"
                    },
                    {
                        prop: "isGreenLight",
                        label: "是否放行",
                        type: "checkbox",
                    },
                ],


                //region分区的数据
                regionData: [
                    {
                        regionNum: "1",
                        isExecute: false,
                        repartitionExecute: true,
                        partitionType: "固定",
                        start: "",
                        startCompare: "",
                        partitionField: "",
                        endCompare: "",
                        end: "",
                        executeStatus: "",
                        incrementTime: "",
                    }
                ],//表格绑定的数据
                tRegionHead: [
                    {
                        label: "序号",
                        type: "index",
                        width: "70",
                        align : "center"
                    },
                    {
                        prop: "isExecute",
                        label: "是否执行",
                        align : "center" ,
                        minWidth : "90"
                    },
                    {
                        prop: "repartitionExecute",
                        label: "重复执行",
                        align : "center" ,
                        minWidth : "100"
                    },
                    {
                        prop: "partitionType",
                        label: "分区类型",
                        align : "center" ,
                        minWidth : "100"
                    },
                    {
                        prop: "start",
                        label: "开始",
                        align : "center" ,
                        minWidth : "140"
                    },
                    {
                        prop: "startCompare",
                        label: "开始比较",
                        align : "center" ,
                        minWidth : "100"
                    }, {
                        prop: "partitionField",
                        label: "分区字段",
                        align : "center" ,
                        minWidth : "100"
                    },
                    {
                        prop: "endCompare",
                        label: "结束比较",
                        align : "center" ,
                        minWidth : "100"
                    },
                    {
                        prop: "end",
                        label: "结束",
                        align : "center" ,
                        minWidth : "140"
                    },
                    {
                        prop: "executeStatus",
                        label: "执行状态",
                        align : "center" ,
                        minWidth : "100"
                    },
                    {
                        prop: "incrementTime",
                        label: "增量时间",
                        align : "center" ,
                        minWidth : "100"
                    },
                    {
                        prop : "operate" ,
                        label :"操作" ,
                        align : "center"
                    }
                ],//表格单元的页面内容
                operateIcons: [
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteDefault}
                ],
                //时间分区的数据
                timePartitionOption: [],//时间分区字段
                timeStorageOption: [
                    {
                        label: "yyyy/MM/dd",
                        value: "yyyy/MM/dd",
                    }, {
                        label: "yyyy年MM月dd日 HH时mm分ss秒",
                        value: "yyyy年MM月dd日 hh时mm分ss秒",
                    }, {
                        label: "yyyy-MM-dd HH:mm:ss",
                        value: "yyyy-MM-dd hh:mm:ss",
                    }, {
                        label: "yyyyMMdd",
                        value: "yyyyMMdd",
                    }, {
                        label: "yyyyMMddHHmmss",
                        value: "yyyyMMddhhmmss",
                    }, {
                        label: "yyyy-MM-dd",
                        value: "yyyy-MM-dd",
                    },{
                        label: "second_timestamp",
                        value: "second_timestamp",
                    }, {
                        label: "millisecond_timestamp",
                        value: "millisecond_timestamp",
                    },{
                        label: "UTC",
                        value: "UTC",
                    }
                ],//时间存储格式
                partitionTypeOption: [
                    {
                        label: "按年",
                        value: "year",
                    }, {
                        label: "按月",
                        value: "month",
                    }, {
                        label: "按天",
                        value: "day",
                    }
                ],//时间分区类型

                timeFormData: {
                    timePartitionField: "",
                    timeStorageFormat: "yyyy/MM/dd",
                    timePartitionType: "year",
                    startTime: "",
                    endTime: "",
                    partitionFieldInput: "",
                }, //时间分区表单数据
                timeData: [],//时间分区表格数据
                //时间分区删除表格删除操作
                timePartitionButton: [
                    {
                        name: '生成固定分区',
                        clickFn: this.fixation,
                        type: 'primary'
                    }, {
                        name: '生成动态分区',
                        clickFn: this.dynamic,
                        type: 'primary',
                    }
                ],
                partitionFlag: "",
                fixationPartitionNum: 0,
                // 分区信息
                transPartitionMeta: {},
                sortNode: '',
                sortNodeOptions: [],
                sort: 'NONE',
                sortOpt: [
                    {
                        label: '无排序',
                        value: 'NONE'
                    }, {
                        label: '升序',
                        value: 'ASC'
                    }, {
                        label: '降序',
                        value: 'DESC'
                    },

                ],
                rowData: '',
                useHeader: true,
                startRow: "1",
                startColumn: "A",
                excelSheetNames: [],
                sheetName: "",
                fileType: "",
            }
        },
        methods: {
        	  csvFlash() {
							const vm = this, {pluginServices, pluginMock,settings} = this;
							let services = vm.getServices(pluginServices, pluginMock);
							this.getOutputFields(services);
	          },
            changeOutputFiled() {
                const vm = this, {pluginServices, pluginMock,settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                if(vm.startRow === "") vm.startRow = "1";
                if(vm.startColumn === "") vm.startColumn = "A";
                settings.loading = true;
                services.getFiledWithRealtime(vm.sourceData.id, vm.sheetName, vm.startRow, vm.startColumn, vm.useHeader,settings).then(res => {
                    if(res.data.status === 0) {
                        let columns = res.data.data;
                        let fieldTableDatacopy = [];
                        for(let i = 0; i < columns.length; i++) {
                            let d = {
                                fieldNum : i+1,
                                fieldName : columns[i].columnName,
                                type: columns[i].columnType
                            }
                            fieldTableDatacopy.push(d);
                        }
                        vm.fieldTableData = fieldTableDatacopy;
                    }
                    else if(res.data.status === 1 && (res.data.msg==='首行默认为表头,文件内容只有一行不能读取！'|| res.data.msg==='文件内容为空！')) {
                        vm.sheetName = vm.excelSheetNames[0].value;
                    }
                })
            },
            setSheetName(val) {
                if(this.sheetName === "" || this.sheetName === undefined){
                    for(let i = 0; i < val.length; i++) {
                        if(val[i].index === 0) {
                            this.sheetName = val[i].value;
                        }
                    }
                }
                this.excelSheetNames = val;
            },
        	//格式设置
            setTypeConfig(pluginMeta) {
                this.startRow = pluginMeta.startRow;
                this.sheetName = pluginMeta.sheetName;
                this.startColumn = pluginMeta.startColumn;
                this.useHeader = pluginMeta.useHeader;
                this.fileType = pluginMeta.fileType;
                for(let key in pluginMeta.sheetNames) {
                    let sheet = {
                        value:  pluginMeta.sheetNames[key].excelSheetName,
                        label:  pluginMeta.sheetNames[key].excelSheetName
                    }
                    this.excelSheetNames.push(sheet);
                }
            },
            //折叠面板改变
            collapseChange(val){
                const vm = this;
                const {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                if(val.indexOf("fieldOutPut") > -1 && vm.fileUpload === false){
                    vm.getOutputFields(services);
                }
            },
            //保存后，关闭折叠面板
            closeCollapse(){
                this.activeCollapse = [];
            },
            checkRowkey(row){
                const vm = this;
                let reg = new RegExp(/^[0-9]\d*$/),msg = "" ,  val = row.index;
                if (!reg.test(val)) {
                    msg = "行键索引限输入整数!";
                    row.index = isNaN(parseInt(val)) ? "" : parseInt(val);
                }
                if (msg) {
                    vm.$message.info(msg);
                }
            },
            deleteField(index){
                this.fieldChData.splice(index , 1);
            },
            addField(){
                this.fieldChData.push({
                    field :"" ,
                    value :""
                })
            },
            saveFn() {
                for (let i = 0; i < this.fieldTableData.length; i++) {
                    if (this.fieldTableData[i].fieldName === this.rowData.columnCode) {
                        this.fieldTableData[i].transition = 'RULE_EXPRESSION:' + this.rowData.serviceOrgId;
                        break;
                    }
                }
            },
            editFn(row) {
                let rowData = {
                    id:row.id,
                    columnCode:row.fieldName,
                    columnName:row.fieldName,
                    type:row.type,
                    serviceOrgId:(row.transition === '' || row.transition === null) ? '' : row.transition.split(":")[1],
                };
                this.rowData = rowData;
                this.$refs.setting.openSetting(this.sourceData, this.rowData);
            },
            //下拉框选择事件
            changeField(row , index) {
                this.saveOutputColumn(row.fieldName, row.length, row.precision, row.type, row.transition, row.index)
            },
            // 获取输出字段
            getOutputFields(services){
                const vm = this;
                vm.fieldTableData = [];
                services.showOutputColumn(vm.stepId).then(res => {
                    if (res.data.status === 0){
                        let columns = res.data.data;
                        vm.fieldTableData = columns.map((d , i)=>{
                            d.fieldNum = i+1;
                            d.fieldName = d.name;
                            d.transition = d.filterExpress;
                            d.precision = d.precsn;
                            return d;
                        });
                    }
                })
            },
            initValue() {
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let _this = this;
                this.stepId = this.sourceData.id;
                /*if (this.sourceData.keyWord === 'fullTextInput') {
                    this.dbType = 'fulltext';
                }*/
                // console.log('dbtype = ' + this.dbType);
                services.advanceConfig(vm.stepId).then(res => {
                    if (res.data.status === 0) {
                        let attr = res.data.data;
                        _this.exceptionMode = attr.exceptionMode;
                        _this.threadCount = attr.threadCount;
                        _this.copyOrDispense = attr.isCopy ? 1 : 2;
                        _this.sampleDistinct = (!(attr.sampleDistinct === null || attr.sampleDistinct === "false"));
                        _this.sampleExpr = attr.sampleExpr === "null" ? "" : attr.sampleExpr;
                    }
                });
                // 获取输出字段
                if (this.fieldOutPut) {
                    vm.getOutputFields(services);
                }
                //获取变更字段
                if(this.fieldsChange){
                    this.getDynamic();
                }
                // 获取分区信息
                if (this.partitionShow) {
                    let url = "/kvInput/kvPartitionPage?tranStepId=" + this.stepId;
                    if (this.dbType === 'fulltext') {
                        url = "/plugin/fulltext/input/fulltextPartitionPage?tranStepId=" + this.stepId;
                    } else if (this.dbType === 'sql') {
                        url = "/plugin/sql/partitions?transStepId=" + this.stepId;
                    }
                    services.partitionPage(url).then(res => {
                        _this.transPartitionMeta = res.data.data.partitionVo;

                        _this.partitionType = _this.transPartitionMeta.partitionMode;
                        _this.changePartitionType(_this.partitionType);
                        if (this.dbType === 'kv') {
                            res.data.data.columnFamilies.forEach(columnFamily => {
                                _this.timePartitionOption.push({
                                    label: columnFamily,
                                    value: columnFamily
                                })
                            });
                        }

                        //无分区
                        if ("DEF" === _this.partitionType) {
                            _this.transPartitionMeta.ranges.forEach((range, index) => {
                                let partitionCol = _this.transPartitionMeta.timePartitionCol;
                                if (_this.dbType === 'kv' && _this.transPartitionMeta.timePartitionCF != null && _this.transPartitionMeta.timePartitionCol != null) {
                                    partitionCol = _this.transPartitionMeta.timePartitionCF + ":" + _this.transPartitionMeta.timePartitionCol;
                                }

                                _this.defaultPartitionData.push({
                                    isExecute: range.execFlag === "1",
                                    repartitionExecute: range.cycleExtra === "1",
                                    partitionType: "固定",
                                    start: range.startVal,
                                    startCompare: "<=",
                                    partitionField: partitionCol,
                                    endCompare: "<",
                                    end: range.endVal,
                                    executeStatus: range.execStatus,
                                    incrementTime: range.incrementStamp,
                                })
                            });
                        }
                        //时间分区
                        if ("DATE" === _this.partitionType) {

                            _this.timeFormData.timePartitionField = _this.transPartitionMeta.timePartitionCF;
                            _this.timeFormData.partitionFieldInput = _this.transPartitionMeta.timePartitionCol;
                            _this.timeFormData.timeStorageFormat = _this.transPartitionMeta.dateFormat;

                            _this.timeFormData.timePartitionType = _this.transPartitionMeta.datePartitionType;

                            let partitionCol = _this.transPartitionMeta.timePartitionCol;
                            if (_this.dbType === 'kv' && _this.transPartitionMeta.timePartitionCF != null && _this.transPartitionMeta.timePartitionCol != null) {
                                partitionCol = _this.transPartitionMeta.timePartitionCF + ":" + _this.transPartitionMeta.timePartitionCol;
                            }

                            _this.transPartitionMeta.ranges.forEach((range, index) => {
                                _this.timeData.push({
                                    endCompare: range.endComparison,
                                    endTime: range.endVal,
                                    executeStatus: range.execStatus,
                                    incrementTime: range.incrementStamp,
                                    isExecute: range.execFlag === "1",
                                    partitionField: partitionCol,
                                    partitionType: range.rangeType === "fixed" ? "固定" : "动态",
                                    regionNum: index,
                                    repartitionExecute: range.cycleExtra === "1",
                                    startCompare: range.startComparison,
                                    startTime: range.startVal
                                });
                            });
                        }
                        //region分区
                        if ("REGION" === _this.partitionType) {

                        }
                    })
                }

                if (this.isShowBusiTable) {
                    //初始化分区
                    for (let i = 0; i < 10; i++) {//静态渲染10行数据
                        let num = {
                            num: "序号" + i,
                            isExecute: false,
                            repetitiveExecute: false,
                            code: "code" + i,
                            expression: "表达式" + i,
                            incrementalTime: "增量时间" + i,
                        };
                        this.partitionTableData.push(num);
                    }
                }
            },
            //分区类型
            changePartitionType(value) {
                if (value === "BUSI") {
                    this.isShowBusiTable = true;
                    this.isShowRegionPartition = false;
                    this.isShowTimePartition = false;
                    this.isShowDefTable = false;
                } else if (value === "REGION") {
                    this.isShowBusiTable = false;
                    this.isShowRegionPartition = true;
                    this.isShowTimePartition = false;
                    this.isShowDefTable = false;
                } else if (value === "DATE") {
                    this.isShowBusiTable = false;
                    this.isShowRegionPartition = false;
                    this.isShowTimePartition = true;
                    this.isShowDefTable = false;
                } else {
                    this.isShowBusiTable = false;
                    this.isShowRegionPartition = false;
                    this.isShowTimePartition = false;
                    this.isShowDefTable = true;
                }
            },

            //同步分区信息
            synchInfo() {

            },
            //字段输入
            saveFieldInput(name) {
                this.closeFold(name);//折叠面板
            },
            //字段输出
            saveFieldOutput(name) {
                let vm = this;
                this.saveOutputColumn("CSD", 6, 0, "String", "filter express", "")
                vm = vm.closeFold(name);//折叠面板
            },
            saveFieldTable() {
                let vm = this;
                vm.fieldTableData.forEach(i => {
                    vm.saveOutputColumn(i.fieldName, i.length, i.precision, i.type, i.transition, i.index)
                });
            },
            saveOutputColumn(field, length, precsn, valType, filterExpress, uniqueValue) {
                // console.log(field,length,precsn,valType,filterExpress,uniqueValue);
                if (filterExpress === null) {
                    filterExpress = "";
                }
                if (uniqueValue === null) {
                    uniqueValue = "";
                }
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                services.modifyColumn(vm.stepId , field , length ,precsn ,valType , uniqueValue , filterExpress);
            },
            saveAdvancedCfg() {
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let isCopy = vm.copyOrDispense === 1 ? "true" : "false";
                services.saveAdvanceConfig(vm.stepId ,isCopy ,vm.threadCount ,vm.sampleExpr ,vm.sampleDistinct , vm.exceptionMode )
                .then(res => {
                    if (res.data.status === 0) {
                        vm.$message.info("成功保存！")
                    }
                })
            },
            getDynamicParamJson(){
                const vm = this;
                let result = {};
                vm.fieldChData.forEach( item =>{
                    result[item.field] = {value : item.value};
                });
                return JSON.stringify(result);
            },
            async saveDynamic(){
                const vm = this;
                const {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let dynamicParamJson = await vm.getDynamicParamJson() , url;
                if (vm.dbType === 'fulltext') {
                    url = '/plugin/fulltext/input/saveDynamic';
                } else if (vm.dbType === 'sql') {
                    url = '/plugin/sql/saveDynamic';
                }else {
                    url = "/kvInput/saveDynamic"
                }
                services.saveDynamic(url , this.stepId , dynamicParamJson);
            },
            getDynamic(){
                if(this.dbType === undefined) return;
                const vm  = this; let url;
                const {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                if (vm.dbType === 'fulltext') {
                    url = '/plugin/fulltext/input/getDynamic';
                } else if (vm.dbType === 'sql') {
                    url = '/plugin/sql/getDynamic';
                }else {
                    url = "/kvInput/getDynamic"
                }
                services.getDynamic(url , vm.stepId).then(res =>{
                    if(res.data.status === 0){
                        let result = res.data.data;
                        if(result !== null){
                            vm.setDynamic(JSON.parse(result))
                        }
                    }
                })
            },
            setDynamic(dynamic){
                const vm = this;
                vm.fieldChData = [];
                Object.keys(dynamic).forEach(item =>{
                    vm.fieldChData.push({
                        field : item ,
                        value : dynamic[item].value
                    })
                })
            },
            saveFieldCh(name){ //保存字段变更
                this.saveDynamic();
                this.closeFold(name);
            },
            saveIsCopy(name) { // 保存分发、复制
                this.saveAdvancedCfg();
                this.closeFold(name);//折叠面板
            },
            saveExceptionMode(name) { // 保存异常模式
                this.saveAdvancedCfg();
                this.closeFold(name);//折叠面板
            },
            saveThreadCnt(name) { // 保存线程数量
                this.saveAdvancedCfg();
                this.closeFold(name);//折叠面板
            },
            saveSampling(name) { // 保存抽样设置
                this.saveAdvancedCfg();
                this.closeFold(name);//折叠面板
            },
            saveSort(name) {//保存排序字段
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                if (vm.sourceData.keyWord === 'fullTextInput') {
                    services.fulltext_saveSort(vm.sourceData.id , vm.sortNode ,vm.sort ).then(res => {
                        if (res.data.status === 0) {
                            vm.$message.success("保存成功!");
                        }
                    });
                }
                vm.closeFold(name);
            },
            savePartition(name) { //分区方式
                const vm  = this; let url;
                const {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                vm.transPartitionMeta.partitionMode = vm.partitionType;
                if (vm.dbType === 'kv') {
                    vm.transPartitionMeta.timePartitionCF = vm.timeFormData.timePartitionField;
                }
                vm.transPartitionMeta.timePartitionCol = vm.timeFormData.partitionFieldInput;
                vm.transPartitionMeta.dateFormat = vm.timeFormData.timeStorageFormat;
                vm.transPartitionMeta.datePartitionType = vm.timeFormData.timePartitionType;

                vm.transPartitionMeta.ranges = [];
                // 构建ranges
                vm.timeData.forEach(item => {
                    vm.transPartitionMeta.ranges.push({
                        execFlag: item.isExecute ? 1 : 0,
                        cycleExtra: item.repartitionExecute ? 1 : 0,
                        rangeType: item.partitionType === "动态" ? "dynamic" : "fixed",
                        rangeValue: "",
                        startVal: item.startTime,
                        code: item.startTime,
                        startComparison: item.startCompare,
                        endComparison: item.endCompare,
                        endVal: item.endTime,
                        incrementStamp: item.incrementTime,
                        execStatus: ""
                    });
                });

                url = "/kvInput/savePartition?tranStepId=" + vm.stepId;
                if (vm.dbType === 'fulltext') {
                    url = '/plugin/fulltext/input/savePartition?tranStepId=' + vm.stepId;
                } else if (vm.dbType === 'sql') {
                    url = '/plugin/sql/partition/save?tranStepId=' + vm.stepId;
                }
                services.savePartition(url, vm.transPartitionMeta).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("成功保存！")
                    }
                })
                vm.closeFold(name);//折叠面板
            },
            closeFold(name) {
                this.activeCollapse = this.activeCollapse.filter(col => {
                    return col !== name;
                });
            },
            /*
                region分区
            */
            //获取预分区事件
            getPreSplit() {

            },
            /*
                时间分区
             */
            dynamic() {//生成动态分区
                if (!this.checkTime()) {
                    return;
                }
                let startDate = new Date(this.timeFormData.startTime);
                let fmtDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDate);

                let num = this.timeData.length + 1;
                if (this.fixationPartitionNum === 1 && this.partitionFlag === 'fixation') {
                    this.$message.warning("只允许有一个动态分区！");
                    return;
                }
                let partitonField = this.timeFormData.timePartitionField;
                if (this.dbType === "kv") {
                    partitonField = partitonField + ":" + this.timeFormData.partitionFieldInput;
                } else {
                    partitonField = this.timeFormData.partitionFieldInput;
                }
                this.partitionFlag = 'fixation';
                this.timeData.push({
                    regionNum: num,
                    isExecute: true,
                    repartitionExecute: "",
                    partitionType: "动态",
                    startTime: fmtDate,
                    startCompare: "<=",
                    partitionField: partitonField,
                    endCompare: "<",
                    endTime: "",
                    executeStatus: "",
                    incrementTime: "",
                });
                this.fixationPartitionNum = 1;


            },
            fixation() {//生成固定分区
                this.partitionFlag = "dynamic";
                if (!this.checkTime()) {
                    return;
                }
                let allDate = [];//返回的所有的处理后获得的日期
                if (this.timeFormData.timePartitionType === "year") {//按年
                    allDate = this.increaseYear();
                } else if (this.timeFormData.timePartitionType === "month") {//按月
                    allDate = this.increaseMonth();
                } else {//按天
                    allDate = this.increaseDay();
                }
                this.timeData = [];
                this.fixationPartitionNum = 0;
                // let index = this.timeData.length+1;
                let partitonField = this.timeFormData.timePartitionField;
                if (this.dbType === 'kv') {
                    partitonField = partitonField + ":" + this.timeFormData.partitionFieldInput;
                } else {
                    partitonField = this.timeFormData.partitionFieldInput;
                }

                allDate.forEach((item, i) => {
                    let endTime = item.endTime;
                    this.timeData.push({
                        regionNum: i + 1,
                        isExecute: true,
                        repartitionExecute: "",
                        partitionType: "固定",
                        startTime: item.startTime,
                        startCompare: "<=",
                        partitionField: partitonField,
                        endCompare: "<",
                        endTime: endTime,
                        executeStatus: "",
                        incrementTime: "",
                    });
                })
            },
            //日期按日增长
            increaseDay() {
                let dateArr = [];
                let startDay = new Date(this.timeFormData.startTime);
                let endDay = new Date(this.timeFormData.endTime);
                let dayCount = (endDay - startDay) / (24 * 60 * 60 * 1000) + 1;

                let tempDate = new Date(startDay.getTime());
                for (let i = 0; i < dayCount; i++) {
                    if (i !== 0) {
                        let dayTime = startDay.getDate() + 1;
                        startDay.setDate(dayTime);
                    }
                    tempDate.setDate(tempDate.getDate() + 1);

                    let fmtStartDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDay);//格式化开始时间
                    let fmtEndDate = this.dateFmt(this.timeFormData.timeStorageFormat, tempDate);//格式话每年的结束时间
                    dateArr.push({
                        "startTime": fmtStartDate,
                        "endTime": fmtEndDate
                    });//将开始时间和结束时间添加到返回的数组中
                }
                return dateArr;
            },
            //日期按月增长
            increaseMonth() {
                let dateArr = [];
                let startYear = new Date(this.timeFormData.startTime).getFullYear();//获取开始时间的年份
                let endYear = new Date(this.timeFormData.endTime).getFullYear();//获取结束时间的年份
                let startMonth = new Date(this.timeFormData.startTime).getMonth();
                let endMonth = new Date(this.timeFormData.endTime).getMonth();
                let startDate = new Date(this.timeFormData.startTime);//开始时间

                //获取两个日期相差几月
                let monthCount = 0;
                if (endYear === startYear) {
                    monthCount = endMonth - startMonth + 1;
                } else if ((endYear - startYear) === 1) {
                    monthCount = (12 - startMonth) + endMonth + 1;
                } else {
                    monthCount = (endYear - startYear - 1) * 12 + (12 - startMonth) + endMonth + 1;
                }

                let tempDate = new Date(startDate.getTime());
                for (let i = 0; i < monthCount; i++) {
                    // let endDate = null;

                    if (i !== 0) {//非第一个月
                        startDate.setMonth(startDate.getMonth() + 1);
                        startDate.setDate(1);
                        // endDate = new Date(startDate.getTime());
                    }
                    if (i === (monthCount - 1)) {
                        tempDate = new Date(this.timeFormData.endTime);
                    } else {
                        tempDate.setMonth(startDate.getMonth() + 1);
                        tempDate.setDate(1);
                    }
                    // endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, startDate.getDate());
                    // endDate.setDate(0);
                    let fmtStartDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDate);//格式化开始时间
                    let fmtEndDate = this.dateFmt(this.timeFormData.timeStorageFormat, tempDate);//格式话每年的结束时间
                    dateArr.push({
                        "startTime": fmtStartDate,
                        "endTime": fmtEndDate
                    });//将开始时间和结束时间添加到返回的数组中
                }
                return dateArr;
            },
            //日期按年增长
            increaseYear() {
                let dateArr = [];
                let startYear = new Date(this.timeFormData.startTime).getFullYear();//获取开始时间的年份
                let endYear = new Date(this.timeFormData.endTime).getFullYear();//获取结束时间的年份
                let startDate = new Date(this.timeFormData.startTime);//开始时间
                //循环获取开始时间到结束时间的素所有日期
                let tempDate = new Date(startDate.getTime());
                for (let i = 0; i <= (endYear - startYear); i++) {
                    if (i !== 0) {//非开始时间的那一年
                        startDate.setFullYear(startDate.getFullYear() + 1);
                        startDate.setMonth(0);
                        startDate.setDate(1);
                    }
                    if (i === (endYear - startYear)) {
                        tempDate = new Date(this.timeFormData.endTime);
                    } else {
                        tempDate.setFullYear(tempDate.getFullYear() + 1);
                        tempDate.setMonth(0);
                        tempDate.setDate(1);
                    }

                    let fmtStartDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDate);//格式化开始时间
                    let fmtEndDate = this.dateFmt(this.timeFormData.timeStorageFormat, tempDate);//格式话每年的结束时间
                    dateArr.push({
                        "startTime": fmtStartDate,
                        "endTime": fmtEndDate
                    });//将开始时间和结束时间添加到返回的数组中
                }
                return dateArr;
            },

            //格式化日期
            dateFmt(fmt, date) {
                let mat = fmt;
                if (fmt.indexOf("timestamp") !== -1 || fmt === "UTC") {
                    fmt = "yyyy-MM-dd hh:mm:ss";
                }
                let allDate = {
                    "M+": date.getMonth() + 1,
                    "d+": date.getDate(),
                    "h+": date.getHours(),
                    "m+": date.getMinutes(),
                    "s+": date.getSeconds(),
                    "q+": Math.floor((date.getMonth() + 3) / 3),
                    "S": date.getMilliseconds()
                };
                if (/(y+)/.test(fmt))
                    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                for (let k in allDate)
                    if (new RegExp("(" + k + ")").test(fmt))
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (allDate[k]) : (("00" + allDate[k]).substr(("" + allDate[k]).length)));
                if (mat.indexOf("timestamp") !== -1) {
                    let timestamp = new Date(fmt).getTime();
                    if (mat.indexOf("millisecond") !== -1) {
                        return timestamp;
                    } else {
                        return timestamp / 1000;
                    }
                } else if (mat === "UTC") {
                    return new Date(fmt).toISOString()
                } else {
                    return fmt;
                }
            },
            checkTime() {
                if (this.timeFormData.startTime === '') {
                    this.$message.warning("请选择开始时间！");
                    return false;
                }
                if (this.timeFormData.endTime === '') {
                    this.$message.warning("请选择结束时间！");
                    return false;
                }
                let startTime = new Date(this.timeFormData.startTime).getTime();
                let endTime = new Date(this.timeFormData.endTime).getTime();
                if (startTime > endTime) {
                    this.$message.warning("开始时间不能不能大于结束时间！");
                    return false;
                }
                return true;
            },
            addDefaultTable() {
                this.defaultPartitionData.push({
                    regionNum: this.defaultPartitionData.length + 1,
                    isExecute: false,
                    repartitionExecute: true,
                    partitionType: "固定",
                    start: "",
                    startCompare: "<=",
                    partitionField: "",
                    endCompare: "<",
                    end: "",
                    executeStatus: "ddd",
                    incrementTime: "",
                })
            },
            deleteDefault(row, index ,targetTable) {
                // this.defaultPartitionData.splice(index, 1);
                this[targetTable].splice(index, 1);
            },
            //表格删除事件
            deleteOperate(row, index) {
                this.timeData.splice(index, 1);
            },
            initFullText() {
                const vm = this , {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                // 初始化全文库输入的分区类型
                vm.partition = [
                    {
                        label: "无分区",
                        value: "DEF"
                    },
                    {
                        label: "时间分区",
                        value: "DATE"
                    }
                ];

                // 初始化输入字段，作为排序字段的选项
                services.fulltext_columns(vm.sourceData.id).then(res => {
                    if (res.data.status === 0) {
                        res.data.data.forEach(c => {
                            this.sortNodeOptions.push({
                                label: c.label,
                                value: c.code
                            })
                        })
                    }
                });
                services.fulltext_getSort(vm.sourceData.id).then(res => {
                    if (res.data.status === 0) {
                        this.sortNode = res.data.data.sortColumn;
                        this.sort = res.data.data.sortType
                    }
                });
            },
            setFold() {
                this.partitionShow = this.isShow.partitionShow;
                this.fieldInput = this.isShow.fieldInput;
                this.fieldOutPut = this.isShow.fieldOutPut;
                this.isCopyOrDispense = this.isShow.isCopyOrDispense;
                this.isSampleExpr = this.isShow.isSampleExpr;
                this.isThread = this.isShow.isThread;
                this.isException = this.isShow.isException;
                this.fieldsChange = !!this.isShow.fieldsChange;
                this.fileUpload = this.isShow.fileUpload;
                this.partitionNode = !!this.isShow.partitionNode;
            },
            setFieldData() {
                if (this.isShow.fieldOutPut) {
                    this.tFieldHead = [
                        {
                            prop: "fieldNum",
                            label: "序号",
                            align : "center" ,
                            width : "70"
                        }, {
                            prop: "fieldName",
                            label: "字段名称",
                            align : "center",
                            width : "120"
                        }, {
                            prop: "transition",
                            label: "转换表达式",
                            width: "130",
                            align : "center"
                        }, {
                            prop: "type",
                            label: "类型",
                            width: "140",
                            align : "center"
                        }, {
                            prop: "index",
                            label: "行键索引",
                            align : "center",
                            width : "120"

                        }, {
                            prop: "length",
                            label: "长度",
                            align : "center"
                        }, {
                            prop: "precision",
                            label: "精度",
                            align : "center"
                        },
                    ];
                    this.typeOptions = [
                        {
                            label: "Short",
                            value: "Short"
                        }, {
                            label: "Integer",
                            value: "Integer"
                        }, {
                            label: "Long",
                            value: "Long"
                        }, {
                            label: "Float",
                            value: "Float"
                        }, {
                            label: "Double",
                            value: "Double"
                        }, {
                            label: "Date",
                            value: "Date"
                        }, {
                            label: "String",
                            value: "String"
                        }, {
                            label: "Text",
                            value: "Text"
                        }, {
                            label: "BigDecimal",
                            value: "BigDecimal"
                        }, {
                            label: "BigInteger",
                            value: "BigInteger"
                        }, {
                            label: "Blob",
                            value: "Blob"
                        }, {
                            label: "Boolean",
                            value: "Boolean"
                        }, {
                            label: "Time",
                            value: "Time"
                        }, {
                            label: "Timestamp",
                            value: "Timestamp"
                        }
                    ]
                }
            },
        },
        created() {
            if (this.sourceData.keyWord === 'standardSqlInput') {
                this.partition = [
                    {
                        label: "无分区",
                        value: "DEF"
                    },
                    {
                        label: "时间分区",
                        value: "DATE"
                    },
                   /* {
                        label: "业务分区",
                        value: "BUSI"
                    }*/
                ]
            } else if (this.sourceData.keyWord === 'fullTextInput') {

                this.initFullText();

            }else if(this.sourceData.keyWord ==='dataSetPlugin'){
                this.partition = [
                    {
                        label: "无分区",
                        value: "DEF"
                    },
                    {
                        label: "时间分区",
                        value: "DATE"
                    }
                ];
            } else {
                this.partition = [
                    {
                        label: "无分区",
                        value: "DEF"
                    },
                    {
                        label: "时间分区",
                        value: "DATE"
                    }, /*{
                        label: "region分区",
                        value: "REGION"
                    }*/
                ];
            }
            this.setFieldData();
            this.setFold();
            this.initValue()
        }
    }
</script>

<style scoped>
    .btn_new_model {
        float: right;
        margin-bottom: 5px;
        margin-top: -27px;
        margin-left: 3px;
    }

    .partitionClass {
        margin-top: 30px;
    }
    .ce-row-box {
        padding: 10px;
    }

</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
