<template>
    <div class="ce-cond">
        <el-form class="ce-form__m0 pb10" label-position="right" label-width="140px">
            <el-form-item label="分区类型:">
                <dg-radio-group v-model="partitionType"
                                :data="partition"
                                call-off
                                @change="changePartitionType"></dg-radio-group>
                <div class="ce-form_tip">{{ partTip }}</div>
            </el-form-item>
        </el-form>
        <!--                    分区类型----时间分区-->
        <div class="ce-cond_cont">
            <time-partition v-show="isShowTimePartition" ref="timePartition" :time-columns="timeColumns" :isIncrement="isIncrement"></time-partition>
            <busi-partition v-show="isBusiParition" ref="busiParttion" :isIncrement="isIncrement"/>
        </div>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import timePartition from "@/projects/DataCenter/views/plugin_3/metaDataInput/standardSql/timePartition";
import busiPartition from "@/projects/DataCenter/views/plugin_3/metaDataInput/standardSql/busiPartition";

export default {
    name: "conditionPage",
    components: {
        timePartition,
        busiPartition,
    },
    mixins: [commonMixins, servicesMixins],
    props: {
        sourceData: Object,
        dbType: String,
        timeColumns: {
            type: Array,
            default: () => []
        },
        isIncrement: {
            type: Boolean,
            default: false,
        },
        rowData:Object,
    },
    data() {
        return {
            emptyTxt: "暂无数据",
            stepId: "",
            partTip: "默认无分区，支持系统按时间分区多线程进行并行读取，能够极大的提高大数据量情况下的数据读取性能。",
            activeCollapse: [],
            partition: [],
            partitionType: "",
            isShowDefTable: true,
            isShowTimePartition: false,
            checkBtn: '确定',
            isBusiParition: false,
        }
    },
    methods: {
        /**
         * //分区类型
         * @param value
         */
        changePartitionType(value) {
            this.isShowTimePartition = value === "DATE";
            this.isBusiParition = value === "BUSI";
            this.$emit('showTimePartition', this.isShowTimePartition, this.isBusiParition)
        },
        async savePartition() { //分区方式
            const vm = this;
            let url;
            const {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            let transPartitionMeta = {};
            let {timeFormData, timeData} = vm.$refs.timePartition;

            transPartitionMeta.partitionMode = vm.partitionType;
            transPartitionMeta.dateFormat = timeFormData.timeStorageFormat;
            transPartitionMeta.timePartitionCol = timeFormData.timePartitionField;
            transPartitionMeta.datePartitionType = timeFormData.timePartitionType;
            transPartitionMeta.ranges = [];
            // 构建ranges
            timeData.forEach(item => {
                transPartitionMeta.ranges.push({
                    execFlag: item.isExecute ? 1 : 0,
                    startVal: item.startTime,
                    code: item.startTime,
                    startComparison: item.startCompare,
                    endComparison: item.endCompare,
                    endVal: item.endTime,
                    incrementStamp: vm.isIncrement ? item.incrementStamp : '',
                });
            });
            url = "/kvInput/savePartition?tranStepId=" + vm.stepId;
            if (vm.dbType === 'fulltext') {
                url = '/plugin/fulltext/input/savePartition?tranStepId=' + vm.stepId;
            } else if (vm.dbType === 'sql') {
                url = '/plugin/sql/partition/save?tranStepId=' + vm.stepId;
            }
            if(this.partitionType === 'DEF') {
                transPartitionMeta.ranges = [];
                transPartitionMeta.timePartitionCol = "";
            }
            await services.savePartition(url, transPartitionMeta);
        },
        /**
         * 初始化
         */
        initValue() {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            vm.stepId = vm.sourceData.id;
            // 获取分区信息
            let url;
            if (this.dbType === 'fulltext') {
                url = "/cicada/plugin/fulltext/input/fulltextPartitionPage?tranStepId=" + this.stepId;
            } else if (this.dbType === 'sql') {
                url = "/cicada/plugin/sql/partitions?transStepId=" + this.stepId;
            } else {
                url = "/cicada/kvInput/kvPartitionPage?tranStepId=" + vm.stepId;
            }
            services.partitionPage(url).then(res => {
                let transPartitionMeta = res.data.data.partitionVo;
                if(this.rowData.isEditParam){//4系跳转
                    vm.partitionType = transPartitionMeta.datePartitionType === 'business' ? 'BUSI' : (transPartitionMeta.datePartitionType === 'time' ? 'DATE' : 'DEF')
                }
                else{
                    vm.partitionType = transPartitionMeta.partitionMode;
                }
                vm.changePartitionType(vm.partitionType);
                //时间分区
                if ("DATE" === vm.partitionType) {
                    let timeStorageFormat = transPartitionMeta.dateFormat || "";
                    let timePartitionType = transPartitionMeta.datePartitionType || "";
                    let timePartitionField = transPartitionMeta.timePartitionCol || "";
                    vm.$refs.timePartition.setTimeForm(
                            timeStorageFormat,
                            timePartitionType,
                            timePartitionField);
                    transPartitionMeta.ranges.forEach((range) => {
                        vm.$refs.timePartition.addTable({
                            endCompare: range.endComparison,
                            endTime: range.endVal,
                            isExecute: range.execFlag === "1",
                            startCompare: range.startComparison,
                            startTime: range.startVal ,
                            incrementTime: range.incrementStamp,
                        });
                    });
                }
                else if("BUSI" === vm.partitionType){
                    vm.$refs.busiParttion.setTableData(transPartitionMeta.ranges)
                }
            })

        },
        init() {
            this.initValue();
            this.partition = [
                {
                    label: "无分区",
                    value: "DEF"
                },
                {
                    label: "时间分区",
                    value: "DATE"
                }
            ];
            if(this.rowData.isEditParam){
                this.partition.push({label: "业务分区", value: "BUSI"})
            }
        },
        validate(){
            if(this.partitionType === 'DATE') return this.$refs.timePartition.validate();
            return true;
        },
        checkTime() {
            if (this.timeFormData.startTime === '') {
                this.$message.warning("请选择开始时间！");
                return false;
            }
            if (this.timeFormData.endTime === '') {
                this.$message.warning("请选择结束时间！");
                return false;
            }
            let startTime = new Date(this.timeFormData.startTime).getTime();
            let endTime = new Date(this.timeFormData.endTime).getTime();
            if (startTime > endTime) {
                this.$message.warning("开始时间不能不能大于结束时间！");
                return false;
            }
            return true;
        },
        //日期按日增长
        increaseDay() {
            let dateArr = [];
            let startDay = new Date(this.timeFormData.startTime);
            let endDay = new Date(this.timeFormData.endTime);
            let dayCount = (endDay - startDay) / (24 * 60 * 60 * 1000) + 1;

            let tempDate = new Date(startDay.getTime());
            for (let i = 0; i < dayCount; i++) {
                if (i !== 0) {
                    let dayTime = startDay.getDate() + 1;
                    startDay.setDate(dayTime);
                }
                tempDate.setDate(tempDate.getDate() + 1);

                let fmtStartDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDay);//格式化开始时间
                let fmtEndDate = this.dateFmt(this.timeFormData.timeStorageFormat, tempDate);//格式话每年的结束时间
                dateArr.push({
                    "startTime": fmtStartDate,
                    "endTime": fmtEndDate
                });//将开始时间和结束时间添加到返回的数组中
            }
            return dateArr;
        },
        //日期按月增长
        increaseMonth() {
            let dateArr = [];
            let startYear = new Date(this.timeFormData.startTime).getFullYear();//获取开始时间的年份
            let endYear = new Date(this.timeFormData.endTime).getFullYear();//获取结束时间的年份
            let startMonth = new Date(this.timeFormData.startTime).getMonth();
            let endMonth = new Date(this.timeFormData.endTime).getMonth();
            let startDate = new Date(this.timeFormData.startTime);//开始时间

            //获取两个日期相差几月
            let monthCount = 0;
            if (endYear === startYear) {
                monthCount = endMonth - startMonth + 1;
            } else if ((endYear - startYear) === 1) {
                monthCount = (12 - startMonth) + endMonth + 1;
            } else {
                monthCount = (endYear - startYear - 1) * 12 + (12 - startMonth) + endMonth + 1;
            }

            let tempDate = new Date(startDate.getTime());
            for (let i = 0; i < monthCount; i++) {
                // let endDate = null;

                if (i !== 0) {//非第一个月
                    startDate.setMonth(startDate.getMonth() + 1);
                    startDate.setDate(1);
                    // endDate = new Date(startDate.getTime());
                }
                if (i === (monthCount - 1)) {
                    tempDate = new Date(this.timeFormData.endTime);
                } else {
                    tempDate.setMonth(startDate.getMonth() + 1);
                    tempDate.setDate(1);
                }
                // endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, startDate.getDate());
                // endDate.setDate(0);
                let fmtStartDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDate);//格式化开始时间
                let fmtEndDate = this.dateFmt(this.timeFormData.timeStorageFormat, tempDate);//格式话每年的结束时间
                dateArr.push({
                    "startTime": fmtStartDate,
                    "endTime": fmtEndDate
                });//将开始时间和结束时间添加到返回的数组中
            }
            return dateArr;
        },
        //日期按年增长
        increaseYear() {
            let dateArr = [];
            let startYear = new Date(this.timeFormData.startTime).getFullYear();//获取开始时间的年份
            let endYear = new Date(this.timeFormData.endTime).getFullYear();//获取结束时间的年份
            let startDate = new Date(this.timeFormData.startTime);//开始时间
            //循环获取开始时间到结束时间的素所有日期
            let tempDate = new Date(startDate.getTime());
            for (let i = 0; i <= (endYear - startYear); i++) {
                if (i !== 0) {//非开始时间的那一年
                    startDate.setFullYear(startDate.getFullYear() + 1);
                    startDate.setMonth(0);
                    startDate.setDate(1);
                }
                if (i === (endYear - startYear)) {
                    tempDate = new Date(this.timeFormData.endTime);
                } else {
                    tempDate.setFullYear(tempDate.getFullYear() + 1);
                    tempDate.setMonth(0);
                    tempDate.setDate(1);
                }

                let fmtStartDate = this.dateFmt(this.timeFormData.timeStorageFormat, startDate);//格式化开始时间
                let fmtEndDate = this.dateFmt(this.timeFormData.timeStorageFormat, tempDate);//格式话每年的结束时间
                dateArr.push({
                    "startTime": fmtStartDate,
                    "endTime": fmtEndDate
                });//将开始时间和结束时间添加到返回的数组中
            }
            return dateArr;
        },
        //格式化日期
        dateFmt(fmt, date) {
            let mat = fmt;
            if (fmt.indexOf("timestamp") !== -1 || fmt === "UTC") {
                fmt = "yyyy-MM-dd hh:mm:ss";
            }
            let allDate = {
                "M+": date.getMonth() + 1,
                "d+": date.getDate(),
                "h+": date.getHours(),
                "m+": date.getMinutes(),
                "s+": date.getSeconds(),
                "q+": Math.floor((date.getMonth() + 3) / 3),
                "S": date.getMilliseconds()
            };
            if (/(y+)/.test(fmt))
                fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (let k in allDate)
                if (new RegExp("(" + k + ")").test(fmt))
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (allDate[k]) : (("00" + allDate[k]).substr(("" + allDate[k]).length)));
            if (mat.indexOf("timestamp") !== -1) {
                let timestamp = new Date(fmt).getTime();
                if (mat.indexOf("millisecond") !== -1) {
                    return timestamp;
                } else {
                    return timestamp / 1000;
                }
            } else if (mat === "UTC") {
                return new Date(fmt).toISOString()
            } else {
                return fmt;
            }
        },
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less" src="../../css/attrForm.less"></style>
<style scoped lang="less">
.ce-cond {
    height: 100%;
    overflow: hidden;
    &_cont {
        height: calc(100% - 38px - 2rem);
    }

}
</style>
