import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../service-mixins/service-mixins";
import DataPreview from "../../../dataSources/dialog/PlanDataPreviewDialog"
import Issue from "../../../dataset-manage/dialog/Issue/IssuePage"
import * as dataset from "../../../dataset-manage/service-mixins/service-mixins";
export default {
    name: "previewResult",
    mixins: [commonMixins, servicesMixins , dataset.servicesMixins , common],
    props : {
        row : Object
    },
    components : {DataPreview , Issue},
    data(){
        return {
            tableData :[] ,
            tableHead : [
                {
                    prop: "code",
                    label: "名称",
                    minWidth: 160,
                    align: "left"
                } , {
                    prop: "userName",
                    label: "创建人",
                    align: "center"
                },
                 {
                    prop: "releaseDate",
                    label: "最后编辑时间",
                    minWidth: 180,
                    align: "center"
                }, {
                    prop: 'operate',
                    label: '操作',
                    minWidth: 140,
                    align: "center"
                }
            ],
            maxH : "296px" ,
            operateIcon: [
               /* {
                    icon: "&#xe6cd;",
                    tip: "创建仪表盘",
                    clickFn: this.visualizing,
                    condition: () => true
                },*/{
                    icon: "&#xe886;",
                    tip: "服务发布",
                    clickFn: this.serveIssue,
                    condition: () => true
                },{
                    icon: "&#xe790;",
                    tip: "数据预览",
                    clickFn: this.preview,
                    condition: () => true
                },/*{
                    icon: "<i class='el-icon-share'></i>",
                    tip: "分享",
                    clickFn: ()=> {},
                    condition: (right) => true
                },{
                    icon: "&#xe856;",
                    tip: "移交",
                    clickFn: ()=> {},
                    condition: (right) => true
                },*/
            ],
            isShowIssue :false ,
            rowData :{}
        }
    },
    methods :{
        visualizing(row){
            const vm = this;
            vm.confirm("提示" , "确认使用此数据集创建仪表盘?" , ()=> {
                this.$router.push({name : "dashboard" , params : {datasetId : row.id , rowList : row }})
            })
        },
        closeServiceIssue() {
            this.isShowIssue = false;
        },
        preview(row){
            this.$refs.preview.show({
                name : row.code ,
                code : row.code,
                id : row.id,
                dbType : "RdbDataObj"
            });
        },
        serveIssue(row){
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            services.isIssue(row.id).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data) {
                        vm.$message.warning("此数据集已经发布服务，请勿重新操作");
                    } else {
                        vm.rowData = row;
                        vm.isShowIssue = true;
                    }
                }
            })
        },
        init(){
            const vm = this, {modelingServices, modelingMock , row} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.tableData = [];
            services.getDataSets(row.transId).then(res => {
                if(res.data.status === 0){
                    let result =  res.data.data;
                    result.forEach(item =>{
                        vm.tableData.push({
                            id : item.id,
                            code : item.code ,
                            userName : row.userName ,
                            releaseDate : row.releaseDate ,
                            isModel : true ,
                            db_type : "RdbDataObj"
                        })
                    })
                }else  {
                    vm.$message.error("暂无输出数据集");
                }
            })
        }
    },
    created() {
        this.init();
    }
}
