/**
 * 数仓规划 树接口
 * @Author: wangjt
 * @Date: 2020-04-28
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'

const root = "/manage/dataWarehouse/";
/*
*  查询数仓 树
 * @param id , pId , keyword
 * @returns {Promise<*>}
* */
export async function queryDirTree(id = "" , pId = "" , keyword = "" , settings ) {
    return await reqUtil.get({
        url: root + 'queryDirTree',
        settings :settings
    }, {id , pId , keyword} );
}

/*
*  移动树节点
 * @param id , ownerId
 * @returns {Promise<*>}
* */

export async function moveDataOrigin(id , ownerId  ) {
    return await reqUtil.get({
        url: root + 'moveDataOrigin',
    }, {id , ownerId} );
}
/*
*  新建子仓库 或 同级仓库
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addTreeNode( {pId  , dirName , dirCode } , settings ) {
    return await reqUtil.post({
        url: root + 'addTreeNode',
        settings
    }, {pId , dirName , dirCode } );
}
/*
*  新建子层次 或 同级层次
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function saveDWLevel( {pId  , levelName , levelCode } ,settings ) {
    return await reqUtil.post({
        url: root + 'saveDWLevel',
        settings
    }, {pId , levelName , levelCode } );
}
/*
*  新建仓库
 * @param  dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addBusiDir( { dirName , dirType } , settings ) {
    return await reqUtil.post({
        url: root + 'addBusiDir',
        settings
    }, { dirName , dirType } );
}
/*
*  新建业务库库
 * @param  busiId , dbType , catalogId , catalogName , selectId ,selectName , customName
 * @returns {Promise<*>}
* */

export async function saveBusi( busiId , dbType , catalogId , catalogName ,selectId , selectName , levelNum , customName , settings) {
    return await reqUtil.get({
        url: root + 'saveBusi',
        settings
    }, { busiId , dbType , catalogId , catalogName , selectId ,selectName ,levelNum , customName} );
}

/*
*  重命名
 * @param  id , dirId , type , classifyName , classifyId
 * @returns {Promise<*>}
* */

export async function renameDWInstance( { id , dirId , type , classifyName , classifyId } ) {
    return await reqUtil.post({
        url: root + 'renameDWInstance',
    }, { id , dirId , type , classifyName , classifyId } );
}
/*
*  数据集重命名
 * @param  dataId , newName , dirId
 * @returns {Promise<*>}
* */

export async function renameDataSet( { dataId , newName , dirId } ) {
    return await reqUtil.post({
        url: root + 'renameDataSet',
    }, { dataId ,newName , dirId } );
}
/*
*  业务库重命名
 * @param newName , id , catalogType
 * @returns {Promise<*>}
* */

export async function updateCatalogName( { newName , id , catalogType } ) {
    return await reqUtil.post({
        url: root + 'updateCatalogName',
    }, { newName , id , catalogType } );
}
/*
*  删除
 * @param deleteType
 * @returns {Promise<*>}
* */

export async function dataWarehouse( deleteType ) {
    return await reqUtil.get({
        url: root + deleteType,
    } );
}
/*
*  添加业务库
 * @param deleteType
 * @returns {Promise<*>}
* */

export async function getResTree( softwares ) {
    return await reqUtil.get({
        url: root + 'getResTree',
    } , {softwares});
}

const f_root = "/metadata/datasource/"
export async function getdataSourceList( dbType ) {
    return await reqUtil.get({
        url: f_root + 'list?dbType='+dbType,
    } );
}

export async function serchMachine( ip ) {
    return await reqUtil.get({
        url: f_root + 'machine?ip='+ip,
    } );
}
//检查链接
export async function checkConnect( dataSourceVO ) {
    return await reqUtil.post({
        url: f_root + 'check',
    },dataSourceVO);
}
//保存
export async function saveConnect( dataSourceVO,settings ) {
    return await reqUtil.post({
        url: f_root + 'save',
        settings
    },dataSourceVO);
}
//查看数据源信息
export async function edit( instanceId, settings ) {
    return await reqUtil.get({
        url: f_root + 'edit?instanceId='+instanceId,
        settings
    });
}
//更新数据源
export async function updateDataSet( dataSourceVO, settings ) {
    return await reqUtil.post({
        url: f_root + 'update',
        settings
    },dataSourceVO);
}
