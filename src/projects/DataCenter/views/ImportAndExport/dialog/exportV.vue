<template>
    <div class="excelImport" v-loading="loading">
        <el-form ref="form"  :model="form" :rules="rules" label-width="160px" label-position="right">
            <template v-for="(k , i) in layoutData">
                <el-form-item  :key="i" :prop="i"
                               v-if="k.type && k.show()"
                               :label="k.label ?`${k.label}:`:''">

                    <template v-if="k.type === 'radioGroup'">
                        <dg-radio-group type="button" call-off v-model="form[i]" :data="k.data" @change="radioChange(i)"></dg-radio-group>
                    </template>
                    <template v-else-if="k.type === 'select' ">
                        <dg-select v-model="form[i]" :data="k.data"></dg-select>
                    </template>
                    <template v-else-if="k.type === 'checkGroup' ">
                        <el-checkbox-group v-model="form[i]" @change="exportContentTypeChange(i, $event)">
                            <el-checkbox v-for="city in k.data" :label="city.label" :key="city.value" :disabled="city.disabled">{{city.label}}</el-checkbox>
                        </el-checkbox-group>
                    </template>
                    <template v-else-if="k.type === 'treeSelect' ">
                        <ce-select-drop
                                style="width:20%"
                                ref="modelTree"
                                :props="defaultProps"
                                v-model="form[i]"
                                multiple
                                visible-type="leaf"
                                :data="modelTreeData"
                                :filterNodeMethod="filterNode"
                                filterable
                        ></ce-select-drop>
                    </template>
                    <template v-else-if="k.type === 'classifyTreeSelect' ">
                        <dg-tree-drop
                                style="width:20%"
                                ref="tree"
                                :props="classifyProps"
                                v-model="form.classifyId"
                                multiple
                                :data="classifyTreeData"
                                :filterNodeMethod="classifyFilterNode"
                                filterable
                        ></dg-tree-drop>
                    </template>
                    <template v-else-if="k.type ==='button' ">
                        <el-button type="primary"  @click="submit">导出</el-button>
                    </template>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
    import {mapGetters} from "vuex"
    import {commonMixins} from "@/api/commonMethods/common-mixins";
    import {treeMethods} from "@/api/treeNameCheck/treeName";

    export default {
        name: "exportV",
        mixins:[commonMixins],
        computed: {
            ...mapGetters(["userRight", "userInfo"]),
        },
        props: {

        },
        data() {
            return {
                loading : false,
                classifyProps : {
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    pId : "pId",
                },
                defaultProps :{
                    value: 'id',
                    label: 'label',
                    children: 'children',
                },
                form: {
                    style: 'MODEL',
                    area: 'ALL',
                    text: ['仅导出模型','导出模型依赖的资源定义'],
                    modelOrMulu : "",
                    exportContentType : "",
                    classifyId : "",
                },
                layoutData: {
                    style: {
                        label: '导出资源',
                        type: 'radioGroup',
                        show: () => true,
                        data: [
                            {label: "模型", value: "MODEL"},
                            // {label: "作业", value: "JOB_WORK"},
                        ]
                    },
                    area: {
                        label: '导出范围',
                        type: 'radioGroup',
                        show: () => true,
                        data: [
                            {label: "包含全部", value: "ALL"},
                            {label: "指定模型", value: "SPECIFIED_MODEL"},
                            {label: "指定目录", value: "SPECIFIED_CLASSIFY"},
                        ]
                    },
                    modelOrMulu: {
                        label:  '指定模型',
                        type : 'treeSelect',
                        show: () => this.form.area === 'SPECIFIED_MODEL' && this.form.area !== 'ALL' ,
                    },
                    classifyId : {
                        label:  '指定目录',
                        type : 'classifyTreeSelect',
                        show: () => this.form.area === 'SPECIFIED_CLASSIFY' && this.form.area !== 'ALL' ,
                    },
                    text: {
                        label: '导出内容',
                        type: 'checkGroup',
                        show: () => true,
                        data: [{
                                label : "仅导出模型",
                                value : "仅导出模型",
                                disabled :true,
                            }, {
                                label : "导出模型依赖的资源定义",
                                value : "导出模型依赖的资源定义",
                                disabled :false,
                            }, {
                                label : "导出模型关联的资源定义",
                                value : "导出模型关联的资源定义",
                                disabled :false,
                            }],
                    },
                    button : {
                        label :'',
                        type : 'button',
                        show : () =>true
                    }

                },
                contentInfo : {
                    '仅导出模型' : 'MODEL',
                    '导出模型依赖的资源定义' : 'MODEL_RESOURCE',
                    '导出模型关联的资源定义' : 'MODEL_RESOURCE_API',
                },
                rules: {
                    style: [
                        {required: true, message: '', trigger: ['blur', 'change']}
                    ],
                    modelOrMulu : [{required : true, message: '请选择',}],
                    classifyId : [{required : true, message: '请选择',}],
                    area: [
                        {required: true, message: '', trigger: ['blur', 'change']}
                    ],
                    text: [
                        {required: true, message: '请选择导出内容', trigger: ['blur', 'change']}
                    ],
                },
                classifyTreeData : [],
                modelTreeData : [],
                modelOrClassifyTreeInfoData : [],
            }
        },
        methods: {
            filterNode(...arg) {
                return treeMethods.methods.filterNode(...arg, this.defaultProps);
            } ,
            classifyFilterNode(...arg) {
                return treeMethods.methods.filterNode(...arg, this.classifyProps);
            } ,
            radioChange(key) {
                if(key !=='area') return;
                this.form.modelOrMulu = "";
            },
            exportContentTypeChange(key, e) {
                this.layoutData.text.data.forEach(item =>{
                    item.disabled = false;
                })
                e.forEach(element => {
                    this.form.exportContentType = element;
                    if (element === '导出模型依赖的资源定义'){
                        this.layoutData.text.data[0].disabled = true;
                        if(!e.find(item => item === '仅导出模型')) this.form.text.push('仅导出模型');
                    } else if (element === '导出模型关联的资源定义'){
                        this.layoutData.text.data[0].disabled = true;
                        this.layoutData.text.data[1].disabled = true;
                        if(!e.find(item => item === '仅导出模型')) this.form.text.push('仅导出模型');
                        if(!e.find(item => item === '导出模型依赖的资源定义')) this.form.text.push('导出模型依赖的资源定义');
                    }
                });
            },
            tabChange() {

            },
            checkChange() {

            },
            getClassifyTreeInfo(){
                const vm = this;
                let services = this.$services('modeling');
                vm.classifyTreeData = [];
                services.queryTransTree('' , '' , 'TRANS_DIR_MF' ).then( async res => {
                    vm.classifyTreeData = res.data.data;
                });
            },
            getModelTreeInfo() {
                const vm = this;
                let services = vm.$services("modeling");
                services.queryModelServiceSourceTree({transClassify :''}).then(async res=>{
                    vm.modelTreeData = vm.filterModeling(res.data.data);
                })
            },
            filterModeling(data){
                const vm = this;
                if (data !== null) {
                    return data.filter(item => {
                        item.filterName = item.label;
                        if(item.children && item.children.length !== 0){
                            item.children = vm.filterModeling(item.children);
                        }
                        return (item.elementOrDir === '1'  && item.children && item.children.length !== 0) || item.elementOrDir === '0';
                    })
                }
            },
            submit(){
                const vm = this, {userInfo} = this;
                if (this.form.area === 'SPECIFIED_CLASSIFY' && this.form.classifyId === '' || 
                    this.form.area === 'SPECIFIED_MODEL' && this.form.modelOrMulu === '') {
                    this.$message.warning('请填写完整信息');
                    return;
                }
                let fId = this.classifyTreeData[0].id;
                let classifyIds = this.form.classifyId.split(','), modelIds = this.$refs.modelTree && this.$refs.modelTree.length>0&& this.$refs.modelTree[0].$refs.tree.getCheckedNodes(true), modelList = [];
                if(modelIds) {
                    modelIds.forEach(item => {
                        modelList.push(item.id);
                    })
                }
                if (this.form.text.length === 0) {
                    this.$message.warning('请选择导出内容');
                    return;
                }
                let params = {
                    "exportResourceType": this.form.style,
                    "exportScopeType":this.form.area,
                    "specifiedModelTransIdList": this.form.area === 'SPECIFIED_MODEL' ? modelList : [""],
                    "specifiedTransClassifyIdList": this.form.area === 'ALL' ? [fId] : this.form.area === 'SPECIFIED_CLASSIFY' ? classifyIds : [""] ,
                    "exportContentType":this.contentInfo[this.form.exportContentType],
                    "userId":userInfo.id
                } , services = this.$services("modeling");
                vm.loading = true;
                this.$axios.post("/dataTransImportExport/exportDataCenterTrans",params, {
                    responseType: 'arraybuffer'
                }).then(res => {
                    vm.loading = false;
                    var blob = new Blob([res.data], {type: 'application/octet-binary'});
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'dataTrans.model';
                    link.click();
                    window.URL.revokeObjectURL(url); //释放掉blob对象
                })
                this.$emit('submit' , this.fileList, vm);
                if(!this.isBusinessMete) this.close();
            },
            close(){
                this.$emit('close')
            }
        },
        created() {
            this.getClassifyTreeInfo();
            this.getModelTreeInfo();
        },
        activated() {
            this.getClassifyTreeInfo();
            this.getModelTreeInfo();
        }
    }
</script>

<style scoped lang="less">
    .tip-red {
        color: #F56C6C;
        padding: 0 2px;
    }
    .import-file {
        color: rgba(0,0,0,.45);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:hover {
            color: #1890FF;
        }
    }
</style>
