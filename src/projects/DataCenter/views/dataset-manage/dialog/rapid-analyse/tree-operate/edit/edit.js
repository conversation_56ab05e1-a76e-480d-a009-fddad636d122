import editPage from "./page/index.vue"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../../service-mixins/service-mixins";
import {mapGetters} from "vuex";

export default {
    name: "eidt" ,
    mixins : [commonMixins ,servicesMixins],
    components : {
        editPage
    },
    computed : {
        ...mapGetters(["userInfo"])
    },
    props : {
        datasetId :String
    },
    data(){
        return {
            title : "编辑" ,
            width :"628px" ,
            visible : false ,
            reNew : false ,
            nodeData :{}
        }
    },
    methods : {
        clearData(){
            this.reNew = false;
        },
        show(data){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.nodeData = data;
            vm.settings.loading = false;
            if(data.step){
                vm.title = "编辑(轨迹步骤)"
            }else {
                vm.title = "编辑"
            }
        },
        submit(){
            const vm = this , {datasetServices , datasetMock , datasetId ,userInfo,settings ,nodeData} = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let form_data = vm.$refs.editPage.form;
            vm.$refs.editPage.validate(valid => {
                if(valid){
                    vm.settings.loading = true;
                    let data = {
                        code : "" ,
                        columnCode : form_data.physics ,
                        columnId :form_data.id ,
                        comment : form_data.memo ,
                        columnAlias : form_data.columnAlias ,
                        dataSetId : datasetId ,
                        showColumnName : form_data.name ,
                        indexType : nodeData.indexType ,
                        userId : userInfo.id ,
                        metaComment :  nodeData.memo ,
                        metaShowColumnName : nodeData.name ,
                        metaColumnAlias :nodeData.columnAlias
                    };

                    if(nodeData.step === true){
                        // vm.editStep(services ,data , vm , nodeData , settings);
                        let params = {
                            dataSetId: data.dataSetId ,
                            stepId : nodeData.stepId ,
                            editColumnStep : data
                        };
                        services.editDataStep(params ,settings ).then(res =>{
                            if(res.data.status === 0){
                                vm.$emit("reNewColumn");
                                vm.visible = false;
                            }
                        })
                    }else {
                        // vm.editColumn(services , data , vm , form_data , settings);
                        services.editDataSetColumn(data , settings).then(res => {
                            if(res.data.status === 0){
                                vm.$emit("setColumn" , form_data);
                                vm.visible = false;
                            }
                        })
                    }

                }
            })
        },

    }
}