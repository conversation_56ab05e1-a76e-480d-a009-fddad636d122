import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

export default {
    name: "styleList",
    components: {
        chartStyle
    },
    mixins: [attrStyle],
    props: {
        node: Object,
        dir_dataSetId: String
    },
    data() {
        return {
            activeTabs: ["title", "legend", "xAxis", "yAxis", "fieldSetting"],
            styleList: {
                title: {
                    label: "标题内容",
                    settingForm: {
                        show: {
                            label: "显示标题",
                            type: "checkbox",
                        },
                        custom_style: {
                            label: "字体样式",
                            type: "sty_radio"
                        },
                        fontFamily: {
                            type: "select_font",
                            isLine: true
                        },
                        fontSize: {
                            type: "select_size",
                            isLine: true
                        },
                        fontWeight: {
                            type: "select_w",
                            isLine: true
                        },
                        fontStyle: {
                            type: "checkBtn",
                            isLine: true
                        },
                        color: {
                            type: "font_color",
                            isLine: true
                        },
                        align: {
                            type: "font_align",
                            isLine: true
                        },
                        text: {
                            type: "form_input",
                            pos: "right",
                            label: "标题名称",
                            maxlength: 30,
                            width: "76px",
                            placeholder: "请輸入标题名称(限30个字符)"
                        },
                    },
                    form: {
                        show: true,
                        custom_style: "default",
                        fontFamily: "sans-serif",
                        fontSize: "12px",
                        fontWeight: "normal",
                        fontStyle: [],
                        color: "#666",
                        text: "",
                        align: "center"
                    }
                },
                legend: {
                    label: "图表样式",
                    settingForm: {
                        radius_o: {
                            title: "半径",
                            label: "外半径",
                            type: "slider",
                            width: "100px",
                            pos: "right",
                            step: 1,
                            min: 0,
                        },
                        radius_i: {
                            label: "内半径",
                            type: "slider",
                            width: "100px",
                            pos: "right",
                            step: 1,
                            min: 0,
                        },
                        out_radius_o: {},
                        out_radius_i: {},
                        label_show: {
                            label: "显示数据标签",
                            type: "checkbox",
                        },
                        label_pos: {
                            label: "数据标签显示位置",
                            type: "form_select",
                            width: "140px",
                            pos: "right",
                            options: [
                                {value: "outside", label: "外部"},
                                {value: "inside", label: "内部"}
                            ]
                        },
                        label_cont: {
                            label: "数据标签内容",
                            type: "form_checkbox",
                            width: "140px",
                            pos: "right",
                            options: [
                                {label: "维度", value: "dimension"},
                                {label: "度量", value: "measure"},
                                {label: "百分比", value: "percent"}
                            ]
                        },

                        legend_pos: {
                            title: "图例设置",
                            type: "radio_btns",
                            align: "center",
                            options: [
                                {
                                    label: '上方',
                                    value: 'top'
                                }, {
                                    label: '下方',
                                    value: 'bottom'
                                }, {
                                    label: '右侧',
                                    value: 'right'
                                }, {
                                    label: '左侧',
                                    value: 'left'
                                }, {
                                    label: '无',
                                    value: 'none'
                                }
                            ]
                        },
                        orient: {
                            label: "图例列表布局",
                            type: "form_select",
                            width: "140px",
                            pos: "right",
                            options: [
                                {value: "horizontal", label: "水平"},
                                {value: "vertical", label: "垂直"},
                            ]
                        },
                        color: {
                            label: "配色方案",
                            type: "legend_color",
                            width: "90px"
                        },
                        top: {
                            label: "上边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        },
                        bottom: {
                            label: "下边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        },
                        left: {
                            label: "左边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        },
                        right: {
                            label: "右边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        }
                    },
                    form: {
                        color_type :"custom",
                        color_theme :"default",
                        color: [],
                        radius_o: 50,
                        radius_i: 0,
                        out_radius_o: 80,
                        out_radius_i: 65,
                        radius_o_type: "abs",
                        radius_i_type: "abs",
                        out_radius_o_type: "abs",
                        out_radius_i_type: "abs",
                        label_show: true,
                        label_pos: "outside",
                        orient: "horizontal",
                        legend_pos: "top",
                        seriesName: [],
                        measure: "",
                        label_cont: "dimension,percent",
                        top_type: "abs",
                        top: 0,
                        bottom_type: "abs",
                        bottom: 0,
                        left_type: "abs",
                        left: 0,
                        right_type: "abs",
                        right: 0,
                    }
                },
                fieldSetting: {
                    label: "系列设置",
                    settingForm: {
                       /* fields: {
                            label: "请选择字段",
                            type: "field_select",
                            width: "120px",
                            pos: "right"
                        },*/
                        alias: {
                            label: "别名",
                            type: "field_alias",
                            maxlength: 100,
                            width: "120px",
                            pos: "right",
                            placeholder: "请输入别名(限100个字符)"
                        }
                    },
                    form: {
                        options: [],
                    }
                }
            },
            emitStyle: {}
        }
    },
    methods: {
        getAllStyle() {
            const {emitStyle} = this;
            let styles = {};
            for (let k in emitStyle) {
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        setLegendData() {
            const vm = this;
            if (vm.emitStyle && vm.emitStyle.legend) {
                // let {radius_o, radius_i, out_radius_o, out_radius_i, radius_o_type, radius_i_type, out_radius_o_type, out_radius_i_type} = JSON.parse(JSON.stringify(vm.emitStyle.legend));
                vm.styleList.legend.form = JSON.parse(JSON.stringify(vm.emitStyle.legend));
            }
        },
        setRingPieSettings() {
            const vm = this;
            if (vm.node && vm.node.code === 'RingPieChartWidget') {
                vm.styleList.legend.settingForm.radius_o.title = "内圆半径";
                vm.styleList.legend.settingForm.out_radius_o = {
                    title: "外圆半径",
                    label: "外半径",
                    type: "slider",
                    width: "100px",
                    pos: "right",
                    step: 1,
                    min: 0,
                };
                vm.styleList.legend.settingForm.out_radius_i = {
                    label: "内半径",
                    type: "slider",
                    width: "100px",
                    pos: "right",
                    step: 1,
                    min: 0,
                };
                vm.setLegendData();
            }
        },
        setNormalPieSettings() {
            const vm = this;
            if (vm.node && vm.node.code === 'RingPieChartWidget') {
                vm.styleList.legend.settingForm.radius_o.title = "半径";
                vm.styleList.legend.settingForm.out_radius_o = {};
                vm.styleList.legend.settingForm.out_radius_i = {};
                vm.setLegendData();
            }
        },

        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData);
                vm.setRingPieSettings();
                let {title, legend, fieldSetting} = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                vm.setPieDefaultLegend(legend);
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form;
                vm.node.setData = false;
            })

        }
    },

}
