<template>
    <div class="index">
        <el-collapse v-model="activeTabs">
            <el-collapse-item v-for="(sty , key) in styleList" :key="key" :name="key">
                <template v-if="sty.show">
                    <div class="pl15" slot="title"><i class="el-icon-arrow-right"></i> {{ sty.label }}</div>
                    <chart-style v-on="$listeners" :settingForm="sty.settingForm"
                                 :formList="sty.form" v-bind="$attrs"
                                 @styleRenew="styleRenew($event , key)"></chart-style>
                </template>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script src="./style-list.js"></script>
<style scoped lang="less" src="./style-list.less"></style>
