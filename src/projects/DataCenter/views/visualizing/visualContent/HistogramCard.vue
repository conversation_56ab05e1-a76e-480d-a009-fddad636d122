<template>
  <div class="chartCard" :class="{'pt30': showDrill}">
    <div :style="{'height' : showDrill ? drillHeight : height}">
      <ve-chart
          v-loading="loading"
          :width="width"
          :height="height"
          :data="chartData"
          :settings="chartSettings"
          :extend="extend"
          :judge-width="true"
          :data-empty="data_empty"
          :events="chartEvents"
          ref="chart"
      ></ve-chart>
    </div>
    <div v-if="showDrill" class="chartTip"
         @click="backToDims"
         :title="dimsName"><i class="el-icon-arrow-left"></i>{{ dimsName }}
    </div>
  </div>
</template>

<script>
import 'v-charts/lib/style.css'
import {chartMxins} from "./chart-mixins/chart-mixins";
import {globalBus} from "@/api/globalBus";
import numerify from 'numerify'

export default {
  name: "HistogramCard",
  mixins: [chartMxins],
  data() {
    return {
      chartSettings: {type: "histogram"},
      extend: {
        title: {
          show: true,
          top: 'top',
          left: 'center',
          textStyle: {
            color: '#666'
          },

        },
        tooltip: {
          show: true,
          trigger: 'axis',
          appendToBody: true
        },
        grid: {
          top: 66,
          bottom: 50,
          right: 40,
          left: 40
        },
        legend: {},
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            filterMode: "filter",
            // maxValueSpan: 400
          }, {
            show: false,
            filterMode: "filter",
            start: 0,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '100%',
            height: 16,
            showDetail: false,
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }, {
            type: 'slider',
            show: true,
            yAxisIndex: 0,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '100%',
            width: "16",
            showDetail: false,
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            start: 0,
            left: "10"
          }],
        series: {
          type: 'bar',
          // barMinHeight: 10,
          barMinWidth: 5,
          barMaxWidth: 60,
          barGap: "50%",
          barCategoryGap: "50%",
          large: true,
          largeThreshold: 300,
          label: {
            show: true,
            position: 'top'
          }
        },
        xAxis: {
          nameLocation: 'end',
          nameTextStyle: {
            align: 'left',
            verticalAlign: 'bottom'
          },
          nameRotate: 0,
          axisLine: {
            lineStyle: {}
          },
          axisLabel: {
            interval: 0,
          },
          position: 'bottom'

        },
        yAxis: {
          nameLocation: 'end',
          nameRotate: 0,
          axisLine: {
            lineStyle: {}
          },
          axisLabel: {
            interval: 0,
          },
          position: 'left'
        }
      }
    }
  },
  methods: {
    getNodeData() {
      globalBus.$on('setHistogramPanel', this.setHistogramPanel);
    },
    setChartExtend(settings) {
      const vm = this;
      let transverseCode = ['TransverseStackBarChartWidget', 'TransverseBarChartWidget'];
      vm.setTitle(settings.title);
      if (transverseCode.indexOf(vm.node.code) > -1) {
        vm.setTransLegend(settings.legend);
        vm.setAxis(settings.xAxis, 'xAxis');
        vm.setAxis(settings.yAxis, 'yAxis');
        vm.setAlias(settings.fieldSetting);
        vm.setTooltip();
      } else {
        vm.setLegend(settings.legend);
        vm.setAxis(settings.xAxis, 'xAxis');
        vm.setAxis(settings.yAxis, 'yAxis');
        vm.setAlias(settings.fieldSetting);
        vm.setTooltip();
      }

    },
    setTransLegend(legend) {
      const vm = this;
      vm.setColor(legend.color, legend.color_theme, legend.color_type);
      vm.setTransDataLabel(legend.label_show, legend.label_pos);
      vm.setGrid(legend);
      vm.legendAuto(legend);
      vm.setDataZoom(legend);
      // vm.setDataZoomAxis();
    },
    setTransDataLabel(show, pos) {
      this.extend.series.label.show = show;
      let labelPos = pos === 'outside' ? 'right' : pos === 'insideTop' ? 'insideRight' : pos === 'insideBottom' ? 'insideLeft' : pos;
      this.extend.series.label.position = labelPos;
    },
    setDataZoomAxis() {
      this.extend.dataZoom[0].orient = "vertical";
      this.extend.dataZoom[1].orient = "vertical";
      this.extend.dataZoom[1].bottom = "20";
      this.extend.dataZoom[1].left = "10";
      this.extend.dataZoom[1].top = "20";
      this.extend.dataZoom[1].height = "auto";
      this.extend.dataZoom[1].width = 16;

      this.extend.dataZoom[2].width = "80%";
      this.extend.dataZoom[2].height = 16;
      this.extend.dataZoom[2].bottom = "10";
      this.extend.dataZoom[2].left = "center";
      this.extend.dataZoom[2].orient = "horizontal";
      delete this.extend.dataZoom[2].yAxisIndex;
    },
    /**
     * 设置数据格式
     * @param measure
     */
    set_digital_format(measure) {
      const node = this.node, vm = this;
      measure.forEach(item => {
        if (item.data.numberFormat && item.data.numberFormat !== "none") {
          if (node.code === "BarChartWidget" || node.code === "StackBarChartWidget") {
            vm.extend.yAxis.axisLabel.formatter = function (v) {
              return numerify(v, vm.format[item.data.numberFormat])
            };
          } else if (node.code === "TransverseBarChartWidget" || node.code === "TransverseStackBarChartWidget") {
            vm.extend.xAxis.axisLabel.formatter = function (v) {
              return numerify(v, vm.format[item.data.numberFormat])
            };
          }
          vm.extend.series.label.formatter = function (v) {
            return numerify(v.value, vm.format[item.data.numberFormat]);
          }
        }
      });
    },
    /**
     * tooltip
     */
    setTooltip() {
      const node = this.node, vm = this;
      vm.setTooltipTrigger();
      this.extend.tooltip.formatter = function (param) {
        let params = Array.isArray(param) ? param[0] : param;
        let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
        let yField = nodeData.yData.filter((item, i) => vm.yData.indexOf(item.data.code) > -1 && vm.yData.indexOf(item.data.code) === i);
        let xField = nodeData.xData.filter((item, i) => vm.xData.indexOf(item.data.code) > -1 && vm.xData.indexOf(item.data.code) === i);
        if (nodeData.xData.length === 0 || nodeData.yData.length === 0) {
          vm.data_empty = true;
          vm.showDrill = false;
          return;
        }
        if (xField.length !== nodeData.xData.length || yField.length !== nodeData.yData.length || yField.length !== vm.yData.length || xField.length !== vm.xData.length) {
          vm.preview();
          return;
        }
        if (vm.drillLevel > 0 && (!nodeData.drill[vm.drillLevel - 1] || !nodeData.drill[vm.drillLevel - 1].options.length)) {
          vm.showDrill = false;
          vm.drillLevel = 0;
          vm.preview();
          return;
        }
        let fast = nodeData.xData[0].fastCount && nodeData.xData[0].fastCount.value && nodeData.xData[0].fastCount.value !== "none" ?
            '-' + nodeData.xData[0].fastCount.name : "";
        let funcLabel = nodeData.xData[0].func.name + fast;
        let yLength = nodeData.yData.length - 1;
        let yLabel = nodeData.xData[0].alias || nodeData.xData[0].label,
            xLabel = "(" + (nodeData.yData[yLength].alias || nodeData.yData[yLength].label) + ")",
            xAxis = "(" + (nodeData.yData[0].alias || nodeData.yData[0].label) + ")";
        let result = '';
        let paramsVal;
        if (vm.chartData && vm.chartData.dimsCodes && vm.chartData.dimsCodes.length) {
          let dimInx = vm.chartData.dimsCodes.length;
          if (dimInx === 1) {
            xLabel = `(${vm.chartData.dimsCodes[0]})`;
          } else {
            xAxis = `(${vm.chartData.dimsCodes[0]})`;
            xLabel = `(${vm.chartData.dimsCodes[1]})`;
          }
        }
        if (vm.drillLevel > 0) {
          xLabel = "(" + nodeData.drill[vm.drillLevel - 1].options[0].label + ")";
        }
        if (node.code === "BarChartWidget" || node.code === "StackBarChartWidget" || node.code === "TransverseBarChartWidget" || node.code === "TransverseStackBarChartWidget") {
          paramsVal = nodeData.xData[0].data.numberFormat && nodeData.xData[0].data.numberFormat !== 'none' ? numerify(params.value, vm.format[nodeData.xData[0].data.numberFormat]) : params.value;
        }
        if (params.componentType !== "markPoint") {
          if (nodeData.yAxis.length === 1 || vm.drillLevel > 0) {
            result = `<div>\
                                        <div class="f16 mb5">${params.name + xLabel} :</div>\
                                        <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                        <span>${params.seriesName || params.name} : ${paramsVal} [${funcLabel}]</span>
                                    </div>`;
          } else if (nodeData.yAxis.length === 2) {
            result = `<div>\
                                        <div class="f16 mb5">${params.name + xAxis} :</div>\
                                        <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                        <span>${params.seriesName || params.name} ${xLabel} : ${paramsVal + "[" + yLabel + "-" + funcLabel + "]"}</span>
                                    </div>`;
          }
        }
        return result;
      }
    },
    setTooltipTrigger() {
      const vm = this;
      let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
      let yLen = nodeData.yData.length;
      if(yLen === 1){
        vm.extend.tooltip.trigger = "axis";
      }else {
        vm.extend.tooltip.trigger = "item";
      }

    }
  },


}
</script>

<style scoped lang="less" src="./css/chart.less"></style>
