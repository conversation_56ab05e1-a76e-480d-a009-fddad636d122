<template>
    <div class="codeFormPage fixed-size-form">
        <el-form ref="form" :model="codeForm" label-position="right" label-width="120px">
            <el-form-item
                    v-for="(item , k) in layoutForm"
                    :key="k"
                    :label="`${item.label}:`"
                    :prop="k"
                    :rules="item.rules"
                    size="mini"
            >
                <el-input v-if="item.type === 'input'"
                          size="mini"
                          v-model.trim="codeForm[k]"
                          :maxlength="31"
                          :placeholder="item.placeholder"
                          v-input-limit:[item.limit]></el-input>
                <dg-select v-if="item.type === 'select'"
                           :placeholder="item.placeholder"
                           v-model="codeForm[k]"
                           @change="chartChange"
                           :data="item.option"></dg-select>
                <ce-select-drop
                        v-if="item.type === 'selectTree'"
                        ref="pos_tree"
                        :placeholder="item.placeholder"
                        :props="defaultProps"
                        filterable
                        check-leaf
                        check-strictly
                        visible-type="leaf"
                        :filterNodeMethod="filterNode"
                        v-model="codeForm[k]"
                        :data="item.option"
                        @current-change="selectTreeChange"
                ></ce-select-drop>
                <common-table
                        v-if="item.type === 'table'"
                        :data="codeData"
                        :columns="codeHead"
                        height="313px"
                        :pagination="false"
                        class="ce-form-table_float-tip"
                >
                    <template slot="header-operate">
                        <el-button type="text" title="新增" icon="el-icon-circle-plus" @click="addCode"></el-button>
                    </template>
                    <template slot="operate" slot-scope="{row , $index}">
                        <el-button
                                v-if="row.code === '' && row.value === ''"
                                type="text"
                                @click="deleteCode(row , $index)"
                        >{{ btnDeleteTxt }}
                        </el-button>
                        <el-popconfirm
                                v-else
                                size="mini"
                                title="确认删除?"
                                @confirm="deleteCode(row ,$index)">
                            <el-button slot="reference" type="text">{{ btnDeleteTxt }}</el-button>
                        </el-popconfirm>
                    </template>
                    <template slot="code" slot-scope="{row ,$index}">
                        <el-form-item prop="`code`" :rules="[
                                ...checkEmpty($index , '请输入code' , codeData , 'code') ,
                                ...checkSameName($index , '请勿输入相同code' , codeData , 'code')]">
                            <el-input v-if="row.isEdited"
                                      v-model.trim="row.code"
                                      :ref="`input_key${$index}`"
                                      :maxlength="20"
                                      size="mini"
                                      placeholder="请输入code"
                            ></el-input>
                            <span v-else>{{ row.code }}</span>
                        </el-form-item>
                    </template>
                    <template slot="value" slot-scope="{row , $index}">
                        <el-form-item prop="value" :rules="[
                                ...checkEmpty($index , '请输入码值' , codeData , 'value')]">
                            <el-input v-model.trim="row.value"
                                      :maxlength="20"
                                      size="mini"
                                      :ref="`input_val${$index}`"
                                      placeholder="请输入码值"
                            ></el-input>
                        </el-form-item>
                    </template>
                </common-table>
            </el-form-item>
        </el-form>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import {treeMethods} from "@/api/treeNameCheck/treeName";
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "codeFormPage",
    mixins: [commonMixins],
    props: {
        layoutForm: Object,
        form: Object,
        getTargetTable: Function,
        initBase: Function,
        getCodeData : Function

    },
    data() {
        return {
            codeForm: this.form,
            loading: false,
            codeHead: [
                {
                    type: "index",
                    label: "#",
                    width: "50px"
                },
                {
                    prop: 'code',
                    label: 'code'
                }, {
                    prop: 'value',
                    label: "码值"
                }, {
                    prop: 'operate',
                    width: '90px',
                    align: "center"
                }
            ],
            codeData: [],
            defaultProps: {
                value: 'id',
                label: "label",
                children: "children",
            },
        }
    },
    watch: {
        codeData: {
            handler(val) {
                if (val && val.length) {
                    this.codeForm.enumeration = JSON.stringify(val);
                } else {
                    this.codeForm.enumeration = "";
                }
                this.$refs.form.validateField("enumeration");
            },
            deep: true
        }
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        async chartChange(val , data){
            let res = this.getCodeData(data.id);

        },
        async selectTreeChange(data, node) {
            let res = await this.getTargetTable(data.id);
            if (res.data.status !== 0) return;
            let result = res.data.data;
            this.layoutForm.chart.option = result.map(item => {
                return {
                    name: item.name,
                    label: item.name || item.code,
                    code: item.code,
                    value: item.id,
                };
            })

        },
        init() {
            const vm = this;
            let p = new Promise((resolve, reject) => {
                if (vm.initBase) vm.initBase(resolve);
            });
            p.then((data) => {
                vm.layoutForm.base.option = data;
            });
        },
        submit() {
            const vm = this;
            vm.$refs.form.validate(valid => {
                if (valid) {
                    let form = vm.transformData();
                    vm.$emit("submit" , form);
                    vm.close();
                }
            })

        },
        close() {
            this.$emit("close");
        },
        transformData(){
            let form = JSON.parse(JSON.stringify(this.form));
            form.enumeration = JSON.parse(form.enumeration);
            return form;
        },

        deleteCode(row, inx) {
            this.codeData.splice(inx, 1);
        },
        addCode() {
            const vm = this;
            vm.codeData.push({
                code: "",
                value: "",
                isEdited : true
            });
            vm.$nextTick(() => {
                vm.$refs[`input_key${(vm.codeData.length - 1)}`][0].focus();
            });
        },
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.ce-edit_box {
    min-height: 2rem;
    line-height: 2rem;
    border-radius: 2px;
    overflow: hidden;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.ce-edit_box:hover {
    cursor: pointer;
}
</style>
