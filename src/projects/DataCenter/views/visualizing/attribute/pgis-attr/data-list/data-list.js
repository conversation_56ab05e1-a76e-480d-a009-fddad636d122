import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";

export default {
    name: "dataList",
    props: {
        node: Object,
        dir_dataSetId: String
    },
    mixins: [attrMixins],
    data() {
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show: () => true,
            }
        };
        return {
            showDrill:false,
            axisList: {
                xAxis: {
                    label: '度量',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动度量字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: () => true,
                    rule: []
                },
                yAxis: {
                    label: '维度',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: () => true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ]
                },
                zAxis: {
                    label: '经纬度',
                    type: 'select',
                    value: [],
                    prop: 'zAxis',
                    placeholder: '拖动字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: () => true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ]
                },
                uAxis: {
                    label: '纬度',
                    type: 'select',
                    value: [],
                    prop: 'uAxis',
                    placeholder: '拖动字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: (axis, axisList) => axisList.markType.value !== "polygon",
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ]
                },
                markType: {
                    label: "地图标记",
                    type: 'mark_radio',
                    prop: 'markType',
                    value: "polygon",
                    circle_radius: 200,
                    show: () => true,
                    options: [
                        {
                            label: "点",
                            value: "icon"
                        },
                        {
                            label: "圈",
                            value: "circle",
                            custom: true,
                            custom_t: "半径",
                            unit: "米"
                        }, {
                            label: "绘图",
                            value: "polygon"
                        }
                    ]
                },
                ...formList,
            },
            query: null,
            storeMark : "polygon"
        }
    },
    computed: {
        chartData() {
            const vm = this;
            return {
                xAxis: vm.axisList.xAxis.value,
                xData: vm.axisList.xAxis.options,
                yAxis: vm.axisList.yAxis.value,
                yData: vm.axisList.yAxis.options,
                zAxis: vm.axisList.zAxis.value,
                zData: vm.axisList.zAxis.options,
                uAxis: vm.axisList.uAxis.value,
                uData: vm.axisList.uAxis.options,
                filterData: vm.filterSelects,
                datasetId: vm.datasetId,
                classifierStatId: vm.classifierStatId,
                dataset: vm.dataset,
                filterList: vm.query,
                markType : vm.axisList.markType.value,
                circle_radius:vm.axisList.markType.circle_radius
            };
        }
    },
    methods: {
        markChange(val) {
            const vm = this , {storeMark} = vm;
            if(storeMark === "polygon" || val === "polygon"){
                vm.axisList.zAxis.value = [];
                vm.axisList.zAxis.options = [];
                vm.axisList.uAxis.value = [];
                vm.axisList.uAxis.options = [];
            }
            vm.storeMark = val;
            vm.setDimensionList();
        },
        setDimensionList() {
            const vm = this, markType = vm.axisList.markType.value;
            if (markType === 'polygon') {
                vm.setPolygonList();
            } else {
                vm.setPointList();
            }
        },
        setPolygonList() {
            this.axisList.zAxis.label = "经纬度";
        },
        setPointList() {
            this.axisList.zAxis.label = "经度";
        },
        resetList(datasetId, dataset, isNew) { //重置数据
            const vm = this;
            if (isNew) {
                vm.axisList.xAxis.value = [];
                vm.axisList.xAxis.options = [];
                vm.axisList.yAxis.value = [];
                vm.axisList.yAxis.options = [];
                vm.axisList.zAxis.value = [];
                vm.axisList.zAxis.options = [];
                vm.axisList.uAxis.value = [];
                vm.axisList.uAxis.options = [];
                vm.filterSelects = [];
                vm.chartAttr = [];
                if (vm.axisList.drilling) {
                    vm.axisList.drilling.list = [];
                }
            }
            //设置 datasetId 、tableCode
            vm.datasetId = datasetId;
            // vm.classifierStatId = datasetId;
            vm.$nextTick(() => {
                let getDataset;
                if (!dataset) {
                    getDataset = vm.$refs.nodeTree.getDataSet();
                } else {
                    getDataset = dataset;
                }
                getDataset && getDataset.classifierStatId ? vm.classifierStatId = getDataset.classifierStatId : true;
                getDataset ? vm.dataset = vm.copyArrayObj(getDataset) : true;
            })

        },
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            /*if (this.node.code !== 'TableChartWidget') {
                if (this.axisList[inx].prop === 'xAxis' && this.dragTreeNode.indexType === "DIMENSION") {
                    this.$message.warning('请拖入度量输入框');
                    return;
                } else if (this.axisList[inx].prop === 'yAxis' && this.dragTreeNode.indexType === "MEASURE") {
                    this.$message.warning('请拖入维度输入框');
                    return;
                }
            }*/
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx, this.dragTreeNode.indexType, 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId : true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.xAxis.options = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.zAxis.options = nodeData.zData ? JSON.parse(JSON.stringify(nodeData.zData)) : [];
                vm.axisList.uAxis.options = nodeData.uData ? JSON.parse(JSON.stringify(nodeData.uData)) : [];
                vm.axisList.xAxis.value = nodeData.xAxis ? nodeData.xAxis : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];
                vm.axisList.zAxis.value = nodeData.zAxis ? nodeData.zAxis : [];
                vm.axisList.uAxis.value = nodeData.uAxis ? nodeData.uAxis : [];
                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                nodeData.markType ? vm.storeMark = vm.axisList.markType.value = nodeData.markType : true;
                nodeData.circle_radius ? vm.axisList.markType.circle_radius = nodeData.circle_radius : true;

                vm.query = [];
                //经纬度
                vm.setDimensionList();

                //隐藏下拉
                vm.dropDownHide([vm.axisList.xAxis.options, vm.axisList.yAxis.options, vm.axisList.zAxis.options]);
                vm.inputData();
                vm.addAxisName("", "", vm.axisList);
            })
        },
        validate() {
            const markType = this.axisList.markType.value;
            if (this.chartData.yAxis.length === 0) {
                this.$message.info('请添加维度!');
                return;
            }
            if (markType === 'polygon') {
                if (this.chartData.zAxis.length === 0) {
                    this.$message.info('请添加经纬度!');
                    return;
                }
            } else {
                if (this.chartData.zAxis.length === 0) {
                    this.$message.info('请添加经度!');
                    return;
                }else if (this.chartData.uAxis.length === 0) {
                    this.$message.info('请添加纬度!');
                    return;
                }
            }
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setPgisPanel', this.node.id);
        },
    }
}
