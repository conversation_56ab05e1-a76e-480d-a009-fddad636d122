<template>
    <div class="externalFile">
        <div class="filter-b">
        <el-input placeholder="请输入资源名称搜索" v-model="filterText" size="small" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
    </div>
        <div class="btn-box">
            <el-button size="mini" type="text" @click="importF">导入文件</el-button>
        </div>
        <div class="tree-cont selectn">
            <el-tree
                    :data="treeData"
                    node-key="id"
                    default-expand-all
                    :expand-on-click-node="false"
            >
                    <span class="custom-tree-node" slot-scope="{ node, data }">

                        <span class="node-label" v-if="node.level > 1" :title="node.label" draggable="true"
                              @dragstart="addNode( {label : node.label ,icon : '#iconshuju_huaban' , type : 'source'})"
                        >{{ node.label }}</span><!--配置固定图标-->
                        <span v-else >{{ node.label }}</span>
                        <span v-if="node.level > 1">
                              <el-button
                                      title="删除文件"
                                      icon="el-icon-remove-outline"
                                      type="text"
                                      size="medium"
                                      @click="() => remove(node, data)">
                              </el-button>
                        </span>
                    </span>
            </el-tree>
        </div>
        <ImportFile ref="importF" />
    </div>
</template>

<script>
    import { globalBus } from '@/api/globalBus';
    import ImportFile from './ImportFile'
    export default {
        name: "ExternalFile",
        components : {
            ImportFile
        },
        data() {
            return {
                treeData: [],
                filterText: ''
            }
        },
        methods: {
            initTree() {
                // this.$axios.get("/dataModeling/externalFileObj?condition=&hasAbroad=false")
                //     .then(res =>{
                //         console.log("获取外部文件返回结果：");
                //         console.log(res);
                //     }).catch(err =>{
                //     console.error(err);
                // })
                // this.treeData = [
                //     {
                //         id: '1',
                //         label: '文件',
                //         children: [
                //             {
                //                 id: '1_1',
                //                 label: 'CSV',
                //             },
                //             {
                //                 id: '1_2',
                //                 label: 'JSON'
                //             }, {
                //                 id: '1_3',
                //                 label: 'XML'
                //             }, {
                //                 id: '1_4',
                //                 label: 'FTP'
                //             }
                //         ]
                //     }
                // ]
            },
            addNode(plug){
                globalBus.$emit('plugNode', plug);
            },
            append(data) {

            },
            importF(){
                this.$refs.importF.openDialog();
            },
            remove(node, data) {
                const parent = node.parent;
                const children = parent.data.children || parent.data;
                const index = children.findIndex(d => d.id === data.id);
                this.$confirm('此操作将永久删除该文件, 是否继续?', '删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    children.splice(index, 1);
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });


            }
        },
        created() {
            this.initTree();
        }
    }
</script>

<style scoped>
    .externalFile {
        height: 100%;
    }

    .btn-box {
        padding: 1px 4px;
        text-align: right;
    }

    .tree-cont {
        height: calc(100% - 72px);
        overflow: auto;
    }
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }
    .node-label {
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>