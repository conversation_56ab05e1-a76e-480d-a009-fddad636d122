<!--
    @Describe: 分析结果输出
    @Author: wangjt
    @Date: 2024/8/22
-->
<template>
    <div class="ce-plunin_cont">
        <el-tabs v-model="activeName">
            <el-tab-pane label="基本设置" name="baseConfig"></el-tab-pane>
            <el-tab-pane label="字段映射" name="mappingConfig"></el-tab-pane>
        </el-tabs>
        <div class="ce-zipper_cont" v-show="activeName === 'baseConfig'">
            <el-form :model="plug_data"
                     ref="form"
                     label-position="right"
                     label-width="160px"
                     label-suffix=":"
                     class="attrForm"
                     :disabled="dataFusionFlag"
            >
                <template v-for="(attr , k ) in attrsList">
                    <el-form-item
                            :key="k"
                            :label="attr.label"
                            :prop="k"
                            :rules="attr.rule"
                            :label-width="attr.width"
                            v-if="attr.show(isGreenPlum)"
                    >
                        <el-button v-if="attr.type === 'settingBtn'" @click="attr.clickFn(attr.bindKey)" type="text">高级设置</el-button>
                        <el-row v-if="attr.type ==='input'">
                            <el-col :span="24">
                                <el-input
                                        :placeholder="attr.placeholder"
                                        v-model.number.trim="plug_data[k]"
                                        v-input-limit:number
                                >
                                    <span v-if="attr.suffix" slot="suffix">{{ attr.suffix }}</span>
                                </el-input>
                            </el-col>
                        </el-row>
                        <el-row v-if="attr.type ==='inputDis'">
                            <el-col :span="24">
                                <el-input
                                        :placeholder="attr.placeholder"
                                        v-model="plug_data[k]"
                                        readonly
                                >
                                </el-input>
                            </el-col>
                        </el-row>
                        <el-row v-if="attr.type ==='select-tree'">
                            <el-col :span="24">
                                <dg-tree-drop
                                        ref="selectTree"
                                        v-model="plug_data[k]"
                                        :props="defaultProps"
                                        @current-change="selectTreeChange"
                                        :data="attr.option"
                                        :filterNodeMethod="filterNode"
                                        disabled
                                        filterable
                                        check-leaf
                                        check-strictly
                                        :tree-props="{
                                        'default-expanded-keys' : [plug_data[k]]
                                    }"
                                        :placeholder="attr.placeholder"
                                ></dg-tree-drop>
                            </el-col>
                        </el-row>

                        <el-row v-if="attr.type === 'switch'">
                            <el-col :span="24">
                                <el-switch
                                        v-model="plug_data[k]"
                                        :active-text="attr.activeTxt"
                                        :inactive-text="attr.inactiveTxt"
                                >
                                </el-switch>
                            </el-col>
                        </el-row>
                        <el-row v-if="attr.type === 'select'">
                            <el-col :span="20">
                                <el-select
                                        v-model="plug_data[k]"
                                        :placeholder="attr.placeholder"
                                        filterable
                                        @change="choseTargetTable"
                                >
                                    <el-option
                                            v-for="item in attr.option"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="4">
                                <div class="ce-table_btn">
                                    <el-button class="width100" type="primary" @click="showAddTarget"
                                               :disabled="!!rowData.caseId || isCreating">{{
                                        newChart
                                        }}
                                    </el-button>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row v-if="attr.type === 'radioCustom'">
                            <div>
                                <el-radio :disabled="isDeleteTable" v-model="plug_data[k]"
                                          v-for="(radio , inx) in attr.radioOptions"
                                          :key="inx" :label="radio.value">
                        <span v-if="radio.type">{{ radio.label }}

                        </span>
                                    <span v-else>{{ radio.label }}</span>
                                </el-radio>
                            </div>
                        </el-row>
                        <div v-if="attr.type === 'radio'">
                            <dg-radio-group v-model="plug_data[k]"
                                            :data="!isOracle ? options : [...options,{value: 'update',label: '插入或更新'}]"
                                            call-off></dg-radio-group>
                            <div class="tipStyle">{{ tipList[plug_data[k]] }}</div>
                        </div>

                    </el-form-item>
                </template>
            </el-form>
        </div>
        <div class="ce-zipper_cont" v-show="activeName === 'mappingConfig'">
            <params-page ref="paramsPage"
                         v-if="!loadParams.loading"
                         :isMappingColumn="true"
                         :columnsOptions="upColumns"
                         :numList="jsonNumList"
                         :plug_data="plug_data"
                         :isShowPg="isShowPg"
            >
                <div slot="header" class="ce-map_header">
                    <el-input
                            class="ce-map_inp"
                            size="mini"
                            placeholder="请输入字段名搜索"
                            v-model.trim="inputValueTable"
                    >
                        <i class="el-icon-search el-input__icon poi"
                           slot="suffix"></i>
                    </el-input>
                    <el-button-group>
                        <el-button type="primary" :disabled="dataFusionFlag"
                                   style="margin-right:8px;"
                                   @click="syncColumn">同步目标字段
                        </el-button>
                        <el-button type="primary" :disabled="dataFusionFlag" @click="outMappingColumn">{{ mapLabel }}
                        </el-button>
                        <el-popover
                                popper-class="ce-map_pop"
                                width="136"
                                :disabled="dataFusionFlag"
                                trigger="hover">
                            <ul class="ce-map">
                                <li class="ce-map_item" v-for="opt in wayOption" :key="opt.value"
                                    @click="wayChange(opt.value)"
                                    :class="{'isActive':mappingWay === opt.value}">{{ opt.label }}
                                </li>
                            </ul>
                            <el-button class="ce-map_btn" :disabled="dataFusionFlag" type="primary" slot="reference"
                                       icon="el-icon-arrow-down"></el-button>
                        </el-popover>
                    </el-button-group>
                </div>
            </params-page>
        </div>

        <PreviewDialog ref="preview"></PreviewDialog>
    </div>
</template>

<script>
import PreviewDialog from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {treeMethods} from "@/api/treeNameCheck/treeName"
import * as listApi from "@/projects/DataCenter/views/dataSources/service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins"
import {outputMixins} from "@/api/commonMethods/output";
import paramsPage from "@/projects/DataCenter/views/plugin_3/metaDataOutput/components/params-page";
import {MappingMixins} from "@/projects/DataCenter/views/plugin_3/metaDataOutput/components/mapping-mixins";

export default {
    name: "analysisResultLibraryOutPutMeta",
    props: {
        sourceData: Object,
        loadParams: Object,
        rowData: Object,
    },
    mixins: [commonMixins, listApi.servicesMixins, servicesMixins, outputMixins, MappingMixins],
    components: {
        PreviewDialog,
        paramsPage
    },
    data() {
        return {
            listForm: {
                name: {
                    label: "表中文名",
                    limit: "fieldName",
                    placeholder: "请输入表中文名(限50个字符)",
                    maxlength: 50,
                    rules: [
                        {required: true, message: "请输入表中文名(限50个字符)", trigger: ['blur', 'change']}
                    ]
                },
                code: {
                    label: "表名",
                    limit: "chartCode",
                    placeholder: "请输入表名(限30个字符)",
                    maxlength: 30,
                    rules: [
                        {required: true, message: "请输入表名(限30个字符)", trigger: ['blur', 'change']}
                    ]
                }
            },
            plug_data: {
                dataBase: "",
                batchLimit: 1000,
                isClearTable: false,
                isCreateTable: false,
                isNeglect: false,
                targetTable: "",
                dataUpdateWay: "overwrite",
                tableName: "",//表中文名
            },
            //是否是配置映射字段
            isMappingColumn: false,
            //数据更新方式
            options: [
                {
                    value: 'overwrite',
                    label: '覆盖数据'
                },
                {
                    value: 'append',
                    label: '追加数据'
                },
            ],
            defaultProps: {
                value: 'id',
                label: "label",
                children: "children",
            },
            isShowPg: false,
            selectDbType: "",
            dbType: ["ORACLE", "GBASE", "MYSQL", "POSTGRESQL", "LIBRA", "HWMPP", "GREENPLUM", "HIVE", "TBASE", 'VERTICA'],
            activeName: 'baseConfig',
            monitorShow: false,
            attrsList: {
                dataBase: {
                    label: '数据源',
                    type: 'select-tree',
                    option: [],
                    placeholder: '请选择数据库连接',
                    rule: [
                        {required: true, message: '请选择数据库连接', trigger: 'change'},
                    ],
                    show: () => true
                },
                targetTable: {
                    label: '目标表',
                    type: 'select',
                    option: [],
                    placeholder: '请选择目标表',
                    rule: [
                        {required: true, message: '请选择目标表', trigger: 'change'},
                    ],
                    show: () => true
                },
                tableName: {
                    label: "目标表中文名",
                    type: "inputDis",
                    placeholder: "中文名根据表名自动填充",
                    rule: [
                        {required: true, message: '请选择目标表', trigger: 'change'},
                    ],
                    show: () => true
                },
                monitorSetting: {
                    type: "settingBtn",
                    width: '20px',
                    bindKey: "monitorShow",
                    show: () => true,
                    clickFn:(key) => {
                        this[key] = !this[key];
                    }
                },
                isCreateTable: {
                    label: '是否建表',
                    type: 'switch',
                    activeTxt: '是',
                    inactiveTxt: '否',
                    rule: [],
                    show: () => false
                },
                isClearTable: {
                    label: '是否清空表',
                    type: 'switch',
                    activeTxt: '是',
                    inactiveTxt: '否',
                    rule: [],
                    show: () => false
                },
                batchLimit: {
                    label: '批量提交数量',
                    type: 'input',
                    placeholder: '请输入提交数量',
                    rule: [
                        {required: true, message: '请输入提交数量', trigger: 'change'},
                        {validator: this.checkedCommon, message: "请输入0或正整数", reg: /^\d*$/, trigger: "change"}
                    ],
                    show: (isGreenPlum) => this.monitorShow && isGreenPlum === false,
                },
                dataUpdateWay: {
                    label: "数据更新方式",
                    type: "radio",
                    rule: [
                        {required: true, message: '', trigger: 'change'},
                    ],
                    show: () => this.monitorShow
                },
                isNeglect: {
                    label: '是否忽略插入异常',
                    type: 'switch',
                    activeTxt: '是',
                    inactiveTxt: '否',
                    rule: [],
                    show: (isGreenPlum) => this.monitorShow && isGreenPlum === false,
                },
            },
            dataBaseExample: "",

            //表格数据
            numList: [],

            //目标表选择后的数据
            dataSetId: "",
            schemaId: "",
            tableId: "",
            tableName: "",
            isDeleteTable: false,
            targetColumns: [],
            sourceColumns: [],
            savedExprs: [],
            isGreenPlum: false,
            modelElementId: [], //主键
            isNumList: false,
            isFullText: false,
            isOracle: false,
            isHive: false,
            tipList: {
                'overwrite': '覆盖数据：用新的数据对原数据进行覆盖。',
                'append': '追加数据：在原数据的基础上增加新的数据。',
                'update': '更新数据：对原有的数据进行更新，对不存在的数据进行插入。',
            },
            dataFusionFlag: false, //数据融合 生成的方案
            isShowModel: false,//是否显示模式
        }
    },
    methods: {

        /**
         * 保存新建表
         * */
        saveNewChart(form, list, layer) {
            const vm = this, { userInfo, $services} = this;
            let dw_service = $services("dataSource"), services = $services("plugin3");
            vm.tableName = form.code;
            vm.setCreateTable(true);
            let numList = list;
            if (vm.validateList(numList)) {
                layer.$children[0].loading = false;
                return
            }
            let {
                targetColumns,
                sourceColumns,
                targetType,
                pkNames,
                targetZhName,
                targetColumnLength,
                targetColumnType
            } = vm.setAllColumns(numList, true);
            if (vm.columnsValidate(targetColumns, sourceColumns, targetType)) return;
            if (vm.isShowModel && vm.plug_data.isCreateTable) {
                vm.tableName = vm.tableModel + "." + form.code;
            }
            let tableZhName = form.name;
            let y = vm.setSaveVo(targetColumns, sourceColumns, targetType, pkNames, targetZhName, tableZhName, targetColumnLength, targetColumnType);
            services.analysisResultSave(y).then(res => {
                if (res.data.status === 0) {
                    if (vm.plug_data.isCreateTable) {
                        let result = res.data.data;
                        vm.tableId = result;
                        let dataSetVos = [{name: result, code: result}];
                        let dataSetAuthVo = {
                            dataObjectVos: dataSetVos,
                            roleVos: [],
                            userVos: [{id: userInfo.id}],
                            dwId: vm.plug_data.dataBase
                        };
                        dw_service.dataSetAuthRegister(dataSetAuthVo).then(res => {
                            if (res.data.status === 0) {
                                dw_service.addDataSetAuth(dataSetAuthVo).then(async res => {
                                    if (res.data.status === 0) {
                                        vm.closeAddChart(layer);
                                        await vm.getTargetTable(vm.dataSetId, '', vm.tableId);
                                        vm.$message.success("新建成功!");
                                        vm.choseTargetTable(vm.tableId);
                                    }
                                })
                            } else {
                                layer.$children[0].loading = false;
                            }
                        });
                    }
                } else {
                    layer.$children[0].loading = false;
                }
            })
        },
        /**
         *  配置映射字段方法
         * */
        async outMappingColumn(val) {
            const vm = this, {loadParams} = vm;
            if (typeof val === "string") vm.mappingWay = val;
            vm.numList = [];
            vm.inputValueTable = "";
            loadParams.loading = true;
            let numList = await vm.importField();
            if (!numList) {
                loadParams.loading = false;
                return;
            }
            vm.checkNone(numList);
            vm.numList = numList;
            vm.sortNumList();
            vm.filterTableMethod(vm.inputValueTable);
            loadParams.loading = false;
        },
        /**
         * 新建表 选择输出字段
         * */
        async outColumn() {
            let numList = await this.importField();
            if (!numList) return;
            numList.forEach(item => {
                item.isSave = true;
            })
            return numList;
        },
        filterNode: treeMethods.methods.filterNode,
        changeUpperName(val) {
            const vm = this;
            if (vm.selectDbType === 'ORACLE') vm.plug_data.targetTable = vm.plug_data.targetTable.toUpperCase();
        },
        /**
         * 获取来源 和目标 字段
         * */
        async initMappings(stepId, isChange) {
            await this.getTarget(stepId);
            await this.getSource(stepId);
            await this.refreshMapping(this.targetColumns, this.sourceColumns, isChange);
        },
        async getTarget(stepId) {
            const vm = this, {$services, tableId} = this;
            let services = $services("plugin3");
            await services.analysisResultColumns(stepId, tableId).then(res => {
                if (res.data.status === 0) {
                    vm.targetColumns = res.data.data;
                }
            })
        },
        async getSource(stepId) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            await services.sqlOutMeta(stepId).then(res => {
                if (res.data.status === 0) {
                    vm.sourceColumns = res.data.data;
                    if (vm.upColumns.length) return;
                    vm.upColumns = res.data.data.map(col => {
                        return {
                            ...col,
                            label: col.columnZhName || col.columnName,
                            value: col.columnName,
                            type: col.columnType,
                        }
                    });
                }
            })
        },
        checkGbaseTable() {
            const vm = this;
            let reg = new RegExp(/[A-Z]/);
            return reg.test(vm.tableName);
        },
        validateList(numList) {
            const vm = this;
            let res = false;
            if (numList.length === 0) {
                vm.$message.info('字段信息为空!');
                return res = true;
            }
            if (vm.selectDbType === 'GBASE' && vm.checkGbaseTable()) {
                vm.$message.warning("gbase表名不允许输入大写字母！");
                return res = true;
            }

            if (vm.plug_data.batchLimit === '' || vm.plug_data.batchLimit === undefined) {
                return res = true;
            }
            if (vm.plug_data.batchLimit === '') {
                vm.$message.info('请输入批量提交数量');
                return res = true;
            }
            return res;
        },
        setTableData() {
            const vm = this;
            if (vm.plug_data.isCreateTable) {
                vm.tableName = vm.plug_data.targetTable;
            } else {
                vm.attrsList.targetTable.option.forEach(item => {
                    if (item.value === vm.tableName) {
                        vm.tableName = item.label;
                    }
                })
            }
        },
        setAllColumns(numList, isSaveField) {
            const vm = this;
            let targetColumns = [], sourceColumns = [],
                targetType = [], pkNames = [],
                targetZhName = [], targetColumnType = [], targetColumnLength = [];
            for (let i = 0; i < numList.length; i++) {
                let list = numList[i];
                let out = vm.plug_data.isCreateTable ? list.outputField : list.outputCode || list.outputField;
                let tar = list.targetField;
                let type = list.outputType;
                let zhName = list.outputColumnZhName;
                let isSave = list.isSave;
                if (isSaveField) {
                    if (list.primaryKey && isSave) pkNames.push(tar);
                    isSave && targetColumns.push(out);
                    isSave && sourceColumns.push(tar);
                    isSave && targetType.push(type);
                    isSave && targetZhName.push(zhName);
                    isSave && targetColumnType.push(list.targetColumnType);
                    isSave && targetColumnLength.push(list.targetColumnLength);

                } else {
                    if (list.primaryKey) pkNames.push(tar);
                    targetColumns.push(out);
                    sourceColumns.push(tar);
                    targetType.push(type);
                    targetZhName.push(zhName);
                }

            }
            return {
                targetColumns,
                sourceColumns,
                targetType,
                pkNames,
                targetZhName,
                targetColumnLength,
                targetColumnType
            };
        },
        columnsValidate(targetColumns, sourceColumns, targetType) {
            let res = false;
            const vm = this;
            let source = sourceColumns.filter(sou => sou !== "");
            if (targetColumns.length === 0 || sourceColumns.length === 0 || targetType.length === 0 || source.length === 0) {
                vm.$message.info("至少需要配置一个映射！");
                return res = true;
            }
            return res;
        },
        setSaveVo(targetColumns, sourceColumns, targetType, pkNames, targetZhName, tableZhName, targetColumnLength, targetColumnType) {
            const vm = this;
            let y = new FormData(), {isCreateTable, dataUpdateWay, batchLimit, isNeglect, isClearTable} = vm.plug_data;
            y.append('updateTableType', isCreateTable ? "append" : dataUpdateWay);
            y.append('stepId', vm.stepId);
            y.append('schemaId', vm.schemaId);
            y.append('tableId', isCreateTable ? "" : vm.tableId);
            y.append('tableName', vm.tableName);
            y.append('batchLimit', batchLimit);
            y.append('ignoreInsertError', isNeglect);
            y.append('isDeleteTable', isClearTable);
            y.append('sourceColumns', sourceColumns);
            y.append('targetType', targetType);
            y.append('targetColumns', targetColumns);
            y.append('targetColumnZhNames', targetZhName);
            y.append('dataSetId', vm.dataSetId);
            y.append('isCreateTable', isCreateTable);
            y.append('pkNames', pkNames);
            y.append('modelElementId', pkNames);
            y.append('tableZhName', tableZhName);
            y.append('targetColumnLength', targetColumnLength);
            y.append('targetColumnType', targetColumnType);
            return y;
        },
        save(saveName, showTip) {
            const vm = this, {loadParams,$services} = this;
            let services = $services("plugin3");
            vm.$refs.form.validate((valid) => {
                if (valid) {
                    let numList = vm.numList; //vm.$refs.paramsPage.numList;
                    if (vm.validateList(numList)) return;
                    let {targetColumns, sourceColumns, targetType, pkNames, targetZhName} = vm.setAllColumns(numList);
                    if (vm.columnsValidate(targetColumns, sourceColumns, targetType)) return;
                    if (vm.isShowPg && vm.plug_data.isCreateTable) vm.tableName = "public." + vm.plug_data.tableName;
                    let y = vm.setSaveVo(targetColumns, sourceColumns, targetType, pkNames, targetZhName, vm.plug_data.tableName);
                    loadParams.loading = true;
                    services.analysisResultSave(y, loadParams).then(res => {
                        if (res.data.status === 0) {
                            showTip && vm.$message.success("保存成功!");
                            if (saveName) saveName();
                            vm.$emit("setPreviewAble");
                        }
                    })
                }
            });
        },
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        async initData() {
            const vm = this, {listApi, listMock, loadParams, rights} = this;
            let dw_service = vm.getServices(listApi, listMock);
            vm.stepId = vm.sourceData.id;
            loadParams.loading = true;
            let res = await dw_service.querySuperAllDirTree();
            if (res.data.code !== 0) return;
            vm.attrsList.dataBase.option = vm.resTreeDataDispose(res.data.data);
            vm.initView();

        },
        // 获取分析结果 库 id
        async getAnalysisResult(hasAttribute){
            const vm = this, {$services} = vm;
            await $services("dataContact").getAnalysisResult().then(res => {
                if(res.data.code === 0 ) {
                    const result = res.data.data;
                    vm.tableModel = result.schema;
                    if(result?.id && hasAttribute) {
                        vm.dataSetId = result.id;
                        let node = vm.$refs.selectTree[0].getNode(result.id);
                        vm.selectTreeChange(result.id , node);
                    }
                }
            })
        },
        setDbType(val) {
            const vm = this;
            vm.selectDbType = val;
            vm.isShowPg = vm.selectDbType === 'hwmpp' || vm.selectDbType === 'POSTGRESQL';
            vm.isGreenPlum = vm.selectDbType === 'GREENPLUM';
            vm.isOracle = vm.selectDbType.toUpperCase() === 'ORACLE';
            vm.isHive = vm.selectDbType.toUpperCase() === 'HIVE';
            vm.isShowModel = ['GREENPLUM', 'TBASE', 'POSTGRESQL', 'HWMPP', 'LIBRA', 'VERTICA','GBASE'].includes(vm.selectDbType.toUpperCase());
        },
        /**
         * 初始化
         */
        initView() {
            const vm = this, {$services, loadParams} = this;
            let services = $services("plugin3");
            loadParams.loading = true;
            services.analysisResultView(vm.stepId, loadParams).then(async res => {
                if (res.data.status === 0) {
                    vm.upColumns = res.data.data.upstreamColumns.map(e => {
                        return {
                            label: e.columnZhName || e.columnName,
                            value: e.columnName,
                            type: e.columnType,
                            ...e
                        }
                    });
                    let attrs = res.data.data.attrs;
                    if (attrs.dbType) vm.setDbType(attrs.dbType);
                    const hasAttribute = Object.getOwnPropertyNames(attrs).length === 0;
                    vm.getAnalysisResult(hasAttribute);
                    if (hasAttribute) {
                        return;
                    } else {
                        vm.$emit("setPreviewAble");
                    }
                    await vm.getTargetTable(attrs.dataSetId, attrs.tableName);
                    if (attrs.isCreateTable !== 'true') vm.plug_data.dataUpdateWay = attrs.updateTableType;
                    vm.schemaId = attrs.schemaId;
                    vm.dataSetId = attrs.dataSetId;
                    vm.dataBaseExample = attrs.schemaName;
                    vm.plug_data.dataBase = vm.dataSetId;
                    vm.$refs.selectTree[0] && vm.$refs.selectTree[0].setCurrentKey(attrs.dataSetId);
                    vm.tableName = attrs.tableName;
                    vm.plug_data.isCreateTable = false;
                    vm.plug_data.isClearTable = attrs.isDeleteTable === 'true';
                    vm.plug_data.batchLimit = attrs.batchLimit;
                    vm.plug_data.isNeglect = attrs.ignoreInsertError === 'true';
                    vm.plug_data.tableName = attrs.tableZhName;
                    vm.savedExprs = res.data.data.exprs;
                    vm.modelElementId = attrs.modelElementId ? attrs.modelElementId.split(";") : [];
                    if (attrs.dataFusionFlag === "true") { //融合回显
                        vm.dataFusionFlag = true;
                        vm.plug_data.dataBase = attrs.schemaName;
                        vm.plug_data.targetTable = attrs.tableName;
                        vm.plug_data.tableName = attrs.tableZhName;
                        vm.tableId = attrs.tableId;
                    }
                    vm.initMappings(vm.stepId);
                }
            })
        },
        selectTreeChange(val, value) {
            const _this = this;
            this.numList = [];
            this.selectDbType = value.data.instanceType;
            _this.isGreenPlum = this.selectDbType === 'GREENPLUM';
            _this.isOracle = this.selectDbType.toUpperCase() === 'ORACLE';
            _this.isHive = this.selectDbType.toUpperCase() === 'HIVE';
            _this.isShowModel = ['GREENPLUM', 'TBASE', 'POSTGRESQL', 'HWMPP', 'LIBRA', 'VERTICA'].includes(this.selectDbType.toUpperCase());
            if (_this.isGreenPlum) {
                this.setCreateTable(false)
                if (this.plug_data.batchLimit === "") this.plug_data.batchLimit = 1000;
            }
            _this.isShowPg = _this.selectDbType === 'hwmpp' || _this.selectDbType === 'POSTGRESQL';
            if (value.data.level !== 3) {
                this.plug_data.dataBase = "";
                this.plug_data.targetTable = "";
                this.$message.warning("该目录下没有关系库数据源！");
                return;
            }
            this.dataBaseExample = value.parent.data.label;
            this.dataSetId = value.data.id;
            this.schemaId = value.data.schemaId;
            this.getTargetTable(this.dataSetId);

            this.plug_data.dataBase = value.data.id;
            this.plug_data.targetTable = "";
            this.plug_data.tableName = "";
        },
        /**
         * //获取目标表数据
         * @param instanceId 库id
         * @param content
         * */
        async getTargetTable(instanceId, content = "", chartId = "") {
            const vm = this, {listApi, listMock} = this;
            let dw_service = vm.getServices(listApi, listMock);
            vm.attrsList.targetTable.option = [];
            await dw_service.querySuperDataBaseTableAll(instanceId).then(res => {
                if (res.data.status === 0) {
                    let data = res.data.data;
                    data.forEach(item => {
                        let tableNode = {
                            name: item.name || item.code,
                            label: item.name ? `${item.code}(${item.name})` : item.code,
                            code: item.code,
                            value: item.id,
                        };
                        if (content && content.toLowerCase() === item.code.toLowerCase()) {
                            vm.tableId = item.id;
                            vm.plug_data.targetTable = item.id;
                            vm.plug_data.tableName = item.name;
                        } else if (chartId && chartId === item.id) {
                            vm.tableId = item.id;
                            vm.plug_data.targetTable = item.id;
                            vm.plug_data.tableName = item.name;
                        }
                        vm.attrsList.targetTable.option.push(tableNode);
                    })
                }
            })
        },
        async refreshMapping(targetColumns, sourceColumns, isChange) {
            const vm = this, {savedExprs} = vm;
            vm.numList = [];
            if (typeof savedExprs != 'undefined' && !isChange) {
                // 如果已经配置过，那么只显示配置过的字段
                vm.numList = vm.setNumListCom(
                    savedExprs,
                    sourceColumns,
                    "targetColumnCode",
                    "targetType",
                    "targetColumnZhName",
                    "sourceColumnCode",
                    vm.modelElementId,
                    "sourceColumnCode",
                    "columnName",
                    "columnType",
                    "columnZhName"
                );
                vm.sortNumList();
                vm.filterTableMethod("");
            }
        },
        /**
         *
         * */
        async getCreateTableList(numList) {
            const vm = this, {pluginServices, pluginMock, stepId, schemaId} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            await services.analysisResultCreateTableMeta(stepId, schemaId).then(res => {
                if (res.data.status === 0) {
                    let temp = [], result = res.data.data;
                    result.forEach(col => {
                        temp.push(col.outputColumnName);
                    });
                    result.forEach(col => {
                        let num = {
                            ...col,
                            outputField: col.inputColumnName,
                            outputType: col.inputColumnType,
                            primaryKey: false,
                            targetField: col.outputColumnName,
                            targetType: col.outputColumnType === "FLOAT8" ? "FLOAT4" : col.outputColumnType,
                            outputColumnZhName: col.inputColumnZhName || col.inputColumnName,
                            targetColumnZhName: col.inputColumnZhName || col.outputColumnName,
                            isSave: true,
                        };
                        numList.push(num);
                    });
                }
            })
            return numList;
        },
        /**
         * 映射字段
         * */
        mappingField(numList) {
            const vm = this, {targetColumns, sourceColumns, mappingWay} = vm;
            if (typeof targetColumns != 'undefined') {
                if (mappingWay === "byCode") {
                    numList = vm.setNumListCom(
                        targetColumns,
                        sourceColumns,
                        "code",
                        "type",
                        "name",
                        "columnName",
                        "pk",
                        "code",
                        "columnName",
                        "columnType",
                        "columnZhName"
                    );
                } else {
                    numList = vm.setNumListCom(
                        targetColumns,
                        sourceColumns,
                        "code",
                        "type",
                        "name",
                        "columnName",
                        "pk",
                        "name",
                        "columnZhName",
                        "columnType",
                        "columnZhName"
                    );
                }
            }
            return numList;
        },
        /**
         * 匹配字段导入
         * @return {Promise<[]>}
         */
        async importField() {
            const vm = this, {schemaId, $message, tableId} = vm;
            if (schemaId === null || schemaId === '' || schemaId === undefined) {
                $message.warning("请配置数据源!");
                return;
            }
            let numList = [];
            if (vm.plug_data.isCreateTable === true) {
                numList = await vm.getCreateTableList(numList);
            } else {
                if (tableId === null || tableId === '' || vm.plug_data.targetTable === '' || tableId === undefined) {
                    $message.warning("请选择目标表!");
                    return;
                }
                numList = vm.mappingField(numList);
            }
            return numList;
        },
        async choseTargetTable(value) {
            const vm = this, {loadParams} = vm;
            for (const item of this.attrsList.targetTable.option) {
                if (item.value === value) {
                    vm.tableId = item.value;
                    vm.tableName = item.code;
                    vm.plug_data.tableName = item.name;
                    loadParams.loading = true;
                    await vm.initMappings(vm.stepId, true);
                    vm.outMappingColumn();
                }
            }
        },
        //同步目标字段
        syncColumn() {
            const vm = this;
            let list = [...vm.numList];
            vm.numList = [];
            vm.targetColumns.forEach(n => {
                let info = list.find(p => n.code === p.outputCode);
                if (info) {
                    vm.numList.push(info);
                } else {
                    let obj = {
                        outputCode: n.code,
                        outputColumnZhName: n.name || n.code,
                        outputType: n.type,
                        primaryKey: n.pk,
                        targetField: '',
                        targetName: '',
                    }
                    vm.numList.push(obj);
                }
            })
            vm.loadParams.loading = true;
            vm.sortNumList();
            vm.filterTableMethod(vm.inputValueTable);
            vm.loadParams.loading = false;
        }
    },
    created() {
        this.initData();
    }
}
</script>
<style>
.ce-map_pop.el-popover {
    min-width: auto;
}

.tipStyle {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}
</style>
<style src="../../css/plugin.less" scoped lang="less"></style>
<style lang="less" scoped src="../../css/attrForm.less"></style>

