<template>
    <div class="attrText">
        <el-form size="mini" :model="textSettingVal" :inline="true">
            <el-form-item v-for="(set , index) in textSettingOpt" :key="index" :label="set.label + ' :'">
                <el-select
                        class="fontSelect"
                        v-if="set.type === 'select'"
                        filterable
                        allow-create
                        @visible-change="visibleChange(index)"
                        @change="set.changeFn(textSettingVal[index] , index)"
                        v-model="textSettingVal[index]"
                        @focus="inputData"
                >
                    <el-option v-for="opt in set.options"
                               :key="opt.value"
                               :value="opt.value"
                               :label="opt.label"
                    >
                    </el-option>
                </el-select>
                <el-color-picker class="l" @change="inputData" v-if="set.type === 'color'"
                                 v-model="textSettingVal.color"></el-color-picker>
                <el-radio-group @change="inputData" v-if="set.type ==='align'" v-model="textSettingVal.textAlign">
                    <el-radio-button v-for="opt in set.option" :key="opt.value" :label="opt.value">{{opt.label}}
                    </el-radio-button>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-input
                type="textarea"
                :rows="10"
                placeholder="请输入内容(限500个字符)"
                v-model="textarea"
                resize="none"
                @focus="inputData"
                maxLength="500"
        ></el-input>
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus"

    export default {
        name: "AttrText",
        data() {
            let sizeOptions = [
                {
                    label: '12px',
                    value: '12px'
                },
                {
                    label: '14px',
                    value: '14px'
                },
                {
                    label: '16px',
                    value: '16px'
                },
                {
                    label: '18px',
                    value: '18px'
                },
                {
                    label: '20px',
                    value: '20px'
                }, {
                    label: '24px',
                    value: '24px'
                },
                {
                    label: '30px',
                    value: '30px'
                }
            ], lineOpt = [
                {
                    label: '1倍',
                    value: '1倍'
                }, {
                    label: '1.5倍',
                    value: '1.5倍'
                }, {
                    label: '2倍',
                    value: '2倍'
                },
            ];
            return {
                textarea: "",
                textSettingVal: {
                    fontSize: '12px',
                    color: '#555',
                    textAlign: 'left',
                    lineHeight: '24px' ,
                    lineMg : "1倍"
                },
                textSettingOpt: {
                    fontSize: {
                        type: 'select',
                        label: '字号',
                        options : sizeOptions ,
                        changeFn : this.sizeChange
                    },
                    fontColor: {
                        type: 'color',
                        label: '字体颜色'
                    },
                    lineMg: {
                        type: 'select',
                        label: '行距',
                        options : lineOpt ,
                        changeFn : this.lineChange
                    },
                    textAlign: {
                        type: 'align',
                        label: '文本对齐',
                        option: [
                            {
                                label: '左对齐',
                                value: 'left'
                            }, {
                                label: '居中',
                                value: 'center'
                            }, {
                                label: '右对齐',
                                value: 'right'
                            }
                        ],
                    }

                },
                editNode: null,
                setData: true,
                unused: '',
                storeSize: '' ,
                lineStore : ""
            }
        },

        watch: {
            resultData: {
                handler(val) {
                    if (this.editNode && this.setData) {
                        globalBus.$emit('nodeDataRenew', val, this.editNode.id);
                        globalBus.$emit('setTextPanel', this.editNode.id , val);
                    }
                },
                deep: true
            }
        },
        computed: {
            resultData() {
                return {
                    unused: this.unused,
                    fontSize: this.textSettingVal.fontSize,
                    color: this.textSettingVal.color,
                    textAlign: this.textSettingVal.textAlign,
                    lineHeight: this.textSettingVal.lineHeight,
                    textarea: this.textarea
                }
            },
        },
        methods: {
            visibleChange(key) {
                if(key === "fontSize"){
                    this.storeSize = this.textSettingVal[key];
                }else if(key === "lineMg"){
                    this.lineStore = this.textSettingVal.lineMg;
                }

            },
            sizeChange(val, key) {
                let value = val.trim();
                let size = parseInt(value);
                let {textSettingVal} = this;
                if (isNaN(size)) {
                    this.$message.info('请输入数字');
                    this.textSettingVal[key] = this.storeSize;
                } else {
                    if (size < 12) {
                        this.$message.info('谷歌最小字体12px,请输入12及以上数字！');
                        this.textSettingVal[key] = this.storeSize;
                    } else {
                        this.textSettingVal[key] = size + 'px';
                        this.storeSize = size + 'px';
                    }
                }
                textSettingVal.lineHeight = parseInt(textSettingVal.fontSize) * parseFloat(this.lineStore) + 'px' ;
            },
            lineChange(val , key){
                let value = val.trim();
                let size = parseFloat(value);
                let {textSettingVal} = this;
                if (isNaN(size)) {
                    this.$message.info('请输入数字');
                    this.textSettingVal[key] = this.lineStore;

                } else {
                    if (size < 1) {
                        this.$message.info('行距最小设置为1倍！');
                        this.textSettingVal[key] = this.lineStore;
                    } else {
                        this.textSettingVal[key] = size + "倍";
                        this.lineStore = size + "倍";
                    }
                }
                textSettingVal.lineHeight = parseInt(textSettingVal.fontSize) * parseFloat(this.lineStore) + 'px' ;
            },
            setVisualPanel() {
                globalBus.$on('editVisualPanel', this.getVisualId);
            },
            getVisualId(node) {
                this.editNode = node;
                this.initValue(node);
            },
            inputData() {
                this.setData = true;
            },
            initValue(node) {
                this.$nextTick(() => {
                    const vm = this;
                    this.setData = false;
                    if (node.widget) {
                        let {beforeData } = JSON.parse( node.widget),nodeData = JSON.parse(beforeData);
                        vm.textSettingVal.fontSize = nodeData.fontSize ? nodeData.fontSize : '12px';
                        this.textSettingVal.color = nodeData.color ? nodeData.color : '#555';
                        this.textSettingVal.textAlign = nodeData.textAlign ? nodeData.textAlign : 'left';
                        this.textSettingVal.lineHeight = nodeData.lineHeight ? nodeData.lineHeight : '24px';
                        this.textarea = nodeData.textarea ? nodeData.textarea : '';
                    }
                    this.setData = true;
                    this.unused = '0';
                })
            }
        },
        created() {
            this.setVisualPanel();
        },
        destroyed() {
            globalBus.$off('editVisualPanel', this.getVisualId);
        }
    }
</script>

<style scoped>
    .attrText {
        padding: 10px;
    }

    .fontSelect {
        width: 160px;
    }
</style>