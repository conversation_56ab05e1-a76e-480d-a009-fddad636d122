<template>
    <el-form ref="form" size="mini" :model="source" class="BaseChart"
             :rules="rules"
             label-width="50px"
             label-position="right">
        <el-form-item :label="base+' :'" prop="sourceId" >
            <tree-select
                    v-model="source.sourceId"
                    :props="props"
                    :data="baseData"
                    filterable
                    clearable
                    check-leaf
                    check-strictly
                    placeholder="库名"
                    @change="baseChange"
            ></tree-select>
        </el-form-item>
        <el-form-item :label="chart+' :'" prop="tableId" >
            <tree-select
                    v-model="source.tableId"
                    :props="props1"
                    :data="tableData"
                    filterable
                    clearable
                    check-leaf
                    check-strictly
                    placeholder="表名"
                    @change="chartChange"
            ></tree-select>
        </el-form-item>
    </el-form>
</template>

<script>
    import treeSelect from "ui-component-v4/lib/tree-select"
    import {inputView} from "./tableApi"
    export default {
        name: "Base<PERSON><PERSON>",
        components : {
            treeSelect
        },
        props : {
            baseData : Array
        },
        data (){
            return {
                rules : {
                    sourceId : [
                        {required : true , message : "请选择库" , trigger : "change"}
                    ],
                    tableId : [
                        {required : true , message : "请选择表" , trigger : "change"}
                    ]
                },
                source : {
                    sourceId : "" ,
                    schemaId : "" ,
                    tableId : "" ,
                    tableName :""
                },
                base : "库" ,
                chart : "表" ,
                props: {
                    value: 'id',
                    label: 'label',
                    children: 'children'
                },
                props1: {
                    value: 'id',
                    label: 'name',
                    children: 'children'
                },
               tableData :[]
            }
        },
        methods :{
            baseChange(val , node){
                const vm = this ;let {source} = this;
                source.schemaId = node.schemaId;
                vm.$emit("selectChange",node.schemaId , "schemaId");
                source.tableId = "";
                vm.getTableData(val);
            },
            getTableData(dwId){
                const vm = this;
                inputView(dwId).then(res=>{
                    if(res.data.status === 0){
                        let result = res.data.data;
                        result.forEach(re =>{
                            if(re.name === null) re.name = re.code;
                        });
                        vm.tableData = result;
                    }
                })
            },
            chartChange(val , node){
                const vm = this ;let {source} = this;
                source.tableName = node.code;
                vm.$emit("selectChange",node.code , "tableName");
                vm.$emit("selectChange",val , "tableId");
            },
            validate(...args){
                return this.$refs.form.validate(...args);
            }
        }
    }
</script>

<style scoped>

</style>