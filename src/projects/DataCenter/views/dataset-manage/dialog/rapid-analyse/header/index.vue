<template>
    <page-header>
        <template slot="title">
            <label class="header-name">{{title_txt}}</label>
            <div class="d-s-h-l-input">
                <div class="name_txt" v-if="showTxt" @click="toEdit" :title="anaName">{{anaName}}</div>
                <el-input ref="name_input" :maxlength="maxlength" @blur="toRead" v-model.trim="anaName"
                          v-input-filter="anaName"
                          v-else></el-input>
            </div>
        </template>
        <template slot="buttons">
            <span class="separator">
                <el-popover
                        placement="bottom"
                        width="290"
                        trigger="click"
                        v-model="popVis"
                        @show="init"
                >
                    <div class="ce-time_h">
                        <dg-scrollbar v-loading="settings.loading">
                        <dg-time-line
                                v-if="history.length > 0"
                                class="time-line-box"
                                :data="history"
                                :round-background="roundBackground"
                                text-type="box"
                                user-defined
                        >
                            <template slot-scope="{data }">
                                <div class="history-box"
                                     @click="linkStep.indexOf(data.code) > -1 && data.status === 0? editHistory(data , false) : ()=>''"
                                     :class="{'ce-detail_link' : linkStep.indexOf(data.code) > -1 && data.status === 0}">
                                    <span :class="{'ce-error_color' : data.status === 1}">{{data.name}}</span>
                                    <span>
                                        <dg-button
                                                type="text"
                                                @click.stop="editHistory(data , true)"
                                                icon="el-icon-edit"
                                                v-if="has_edit.indexOf(data.code) > -1 && data.status === 0"
                                        ></dg-button>
                                        <dg-button
                                                type="text"
                                                @click.stop="deleteHistory(data)"
                                                icon="el-icon-delete"
                                        ></dg-button>
                                    </span>
                                </div>

                            </template>
                        </dg-time-line>
                         <div v-else class="ce-data-none p20">{{noData}}</div>
                    </dg-scrollbar>
                    </div>

                    <dg-button
                            slot="reference"
                            type="text"
                            icon="el-icon-s-operation"
                    >{{his_btn_txt}}</dg-button>
                </el-popover>
            </span>
            <span class="separator">
                <dg-button
                        type="text"
                        icon="el-icon-folder-checked"
                        @click="showSaveOther"
                >{{save_other_btn_txt}}</dg-button>
            </span>
            <span class="separator">
                <dg-button type="text" @click="saveDataset" icon="el-icon-document-checked">{{save_btn_txt}}</dg-button>
            </span>
            <span>
                <dg-button type="text" @click="backTo" icon="el-icon-back">{{back_btn_txt}}</dg-button>
            </span>
        </template>
    </page-header>
</template>

<script src="./header.js"></script>
<style scoped lang="less" src="./header.less"></style>