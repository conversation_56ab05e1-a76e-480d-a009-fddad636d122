<!--
 可复用分页表格
 页面表格 带分页 查询，等通用页面，仅限传数据展示，不可带逻辑业务
    插件高度100%；需要上层标签有高度。
 props :
    operateIcons 数组Array 操作的按钮数据  ，如 编辑，删除等
        {
            name: "删除",  显示名称
            clickFn: this.deleteB, 调用的事件
            show: (row) => true, 除了权限外，控制显示的逻辑 row 本行的数据
            condition: (right) => true,  right 所有权限  如 condition: (right) => right.indexOf($right["processModelingSaseSettingPage"]) > -1
        },
    tableData : 数组Array 表格数据
    tHeadData : 数组Array 表头数据
    filterText : 字符String 过滤输入的值 如：  :filter-text.sync="filterText" 可同步更新上层变量 filterText
    filterPlaceholder 字符String 过输入框的占位描述
    loadParams 对象Object 控制加载动画参数
 事件绑定 :
    changePage 分页事件
 插槽:
    button 左侧的按钮 或者其他( 显示于 表格顶部左侧)
    search 右侧 (显示于表格顶部右侧 ，搜索框左侧) 内容
    search_input 可自定义 搜索框
    自定义 表格 列 ，表头 同common-table
    操作列 为 operate 固定，不可修改
 计算变量:
 tableRef : 可获取到原表格组件对象 ，调用 组件开放的方法 Table Methods=> 如 sort , doLayout , clearSelection 等
 -->
<template>
    <div class="listTable" v-loading="loadParams.loading">
        <div class="listTable_top">
            <div class="listTable_btns">
                <slot name="button"></slot>
            </div>
            <div class="listTable_search">
                <slot name="search"></slot>
                <slot name="search_input">
                    <el-input
                            class="listTable_inp"
                            :placeholder="filterPlaceholder"
                            v-model.trim="inputValue"
                            @input="inputFilterSpecial($event , 'inputValue')"
                            @keyup.enter.native="searchTable"
                            v-input-limit:trim
                    >
                        <i
                                class="el-icon-search el-input__icon poi"
                                slot="suffix"
                                @click="searchTable">
                        </i>
                    </el-input>
                </slot>
            </div>
        </div>
        <div class="listTable_table" v-if="!loadParams.loading">
            <common-table
                    ref="table"
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    :max-height="tableBodyH"
                    v-bind="{
                        ...tableAttrs,
                        ...$attrs
                    }"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event)"
                    @change-size="changeSize"
            >
                <slot
                        v-for="(column , index) in tHeadData"
                        :slot="`header-${column.prop}`"
                        :name="`header-${column.prop}`"
                        slot-scope="{$index}"
                        :column="column"
                        :prop="column.prop"
                        :index="index"
                        :$index="$index"
                >{{ column.label }}
                </slot>
                <template
                        v-for="(column , index) in tHeadData"
                        :slot="column.prop"
                        slot-scope="{row,$index}"
                        v-if="column.prop !== 'operate'">
                    <slot :name="column.prop"
                          :row="row"
                          :prop="column.prop"
                          :value="row[column.prop]"
                          :column="column"
                          :index="index"
                          :$index="$index"
                    >{{ row[column.prop] }}
                    </slot>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                        <span class="r-c-action"
                              v-for="(item,index) in hasRightOptIcon"
                              :key="index"
                              v-if="item.show(row) && index < 3"
                        >
                            <el-button type="text"
                                       :class="item.class"
                                       @click="item.clickFn( row , $index)"
                                       :title="item.name"
                            >{{ item.name }}</el-button>
                        </span>
                    <dir-edit-action v-if="countHasRightMenu > 3"
                                     placement="bottom"
                                     @command="menuCommand($event , row , $index)" :data="hasRightOptIcon.slice(3)"
                                     :node="row"
                                     :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>{{ moreTxt }}</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
            </common-table>
        </div>
    </div>
</template>

<script>

import {commonMixins} from "@/api/commonMethods/common-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import {mapGetters} from "vuex";
import {coustTableH} from "@/api/commonMethods/count-table-h";

export default {
    name: "CeListTable",
    mixins: [commonMixins, listMixins, coustTableH],
    computed: {
        inputValue: {
            get() {
                return this.filterText;
            },
            set(val) {
                this.$emit('update:filterText', val)
            }
        },
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                rights = this.userRight.map(r => r.funcCode);
            }
            return rights;
        },
        countHasRightMenu() {
            const vm = this;
            let len = 0;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        },
        tableRef(){
            return  this.$refs.table.$refs.table;
        }
    },
    props: {
        operateIcons: {
            type: Array,
            default: () => []
        },
        tableData: {
            type: Array,
            default: () => []
        },
        tHeadData: {
            type: Array,
            default: () => []
        },
        filterText: {
            type: String,
            default: ""
        },
        filterPlaceholder: {
            type: String,
            default: "请输入名称搜索"
        },
        loadParams: {
            type: Object,
            default: () => {
                return {loading: false};
            }
        }
    },
    data() {
        return {
            tableAttrs: {
                'border': false,
            },
        }
    },
    methods: {
        changePage(inx = 1) {
            this.$emit("changePage", inx);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage();
        },
        searchTable() {
            this.changePage();
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
    }
}
</script>

<style scoped lang="less">
.listTable {
    height: 100%;

    &_top {
        display: flex;
        justify-content: space-between;
    }

    &_btns, &_search {
        display: flex;
    }

    &_inp {
        width: 20rem;
    }

    &_table {
        padding: 15px 0;
        height: calc(100% - 60px);
    }
}
</style>
