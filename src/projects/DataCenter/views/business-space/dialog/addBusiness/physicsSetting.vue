<template>
    <div class="physicsSetting">
        <el-form
                ref="form"
                class="height100"
                :model="{ list: tableData }"
                @submit.native.prevent>
            <common-table
                    :data="tableData"
                    :columns="tableHead"
                    max-height="318px"
                    :pagination="false"
            >
                <template slot="businessName" slot-scope="{row , $index}">
                    <el-form-item>
                        {{row.businessName}}
                    </el-form-item>
                </template>
                <template slot="ip" slot-scope="{row, $index}">
                    <el-form-item :prop="`list.${$index}.ip`"
                                  :rules="rules.ip">
                        <el-input v-model.trim="row.ip" v-input-limit:custom :ce-custom-reg="ipReg"
                                  placeholder="请输入IP地址"></el-input>
                    </el-form-item>
                </template>
                <template slot="port" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.port`"
                                  :rules="rules.port">
                    <el-input v-model.trim="row.port" v-input-limit:number placeholder="请输入端口号"></el-input>
                    </el-form-item>
                </template>
                <template slot="baseType" slot-scope="{row, $index}">
                    <el-form-item :prop="`list.${$index}.baseType`"
                                  :rules="rules.baseType">
                    <dg-select class="pct100" v-model="row.baseType" :data="bases" placeholder="请选择库类型" filterable></dg-select>
                    </el-form-item>
                </template>
            </common-table>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "physicsSetting",
    data() {
        return {
            ipReg: /[^\d\.]/g,
            tableData: [],
            rules: {
                ip: [
                    {required:true , message : "请输入IP地址" , trigger: ['blur' , 'change']},
                    {
                        validator: this.checkedCommon,
                        reg: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
                        message: "ip不符合规则",
                        trigger: 'blur'
                    }
                ],
                port :[
                    {required:true , message : "请输入端口号" , trigger: ['blur' , 'change']},
                ],
                baseType :[
                    {required:true , message : "请选择库类型" , trigger: ['blur' , 'change']},
                ],
            },
            tableHead: [
                {
                    label: "#",
                    type: "index"
                }, {
                    prop: "businessName",
                    label: "业务数据",
                }, {
                    prop: "ip",
                    label: "IP地址"
                }, {
                    prop: "port",
                    label: "端口"
                }, {
                    prop: "baseType",
                    label: "库类型"
                }
            ],
            bases: [
                {
                    label: "数据库",
                    value: "base"
                }
            ]
        }
    }
}
</script>

<style scoped>

</style>
