<template>
    <div style="padding-left:20px;">
        <div class="wordPadding">
            <p>
                <span class="wordSize wordStyle">基本信息</span>
            </p>
        </div>
        <div style="">
            <el-form ref="form" :model="form" label-width="100px" label-position="left" class="ce-form__m0">
                <el-form-item label="API名称：">
                    <span class="wordStyle">
                        {{form.interfaceChineseName}}
                    </span>
                </el-form-item>
                <el-form-item label="API类型：">
                    <span class="wordStyle">
                        {{form.serviceType}} 
                    </span>
                </el-form-item>
                <el-form-item label="调用地址：">
                    <span class="wordStyle">
                        {{form.requestPath}}
                    </span>
                </el-form-item>
                <el-form-item label="请求方法：">
                    <span class="wordStyle">
                        {{form.requestMethod}}
                    </span>
                </el-form-item>
                <el-form-item label="版本号：">
                    <span class="wordStyle">
                        {{form.interfaceVersion}}
                    </span>
                </el-form-item>
                <el-form-item label="API描述：">
                    <span class="wordStyle" v-if="form.memo">
                        {{form.memo}}
                    </span>
                    <span v-else class="wordStyle">
                        --
                    </span>
                </el-form-item>
            </el-form>            
        </div>

        <el-divider></el-divider>

        <div class="wordPadding">
            <p>
                <span class="wordSize wordStyle">请求参数</span>
            </p>
        </div>
        <div class="tableClass">
            <common-table
                    :data="requestTableData"
                    :columns="requestTHeadData"
                    class="width100"
                    :paging-type="'client'"
                    :border="false"
                    :pagination="false"
                    row-key="paramId"
            >
            </common-table>
        </div>

        <!-- <el-divider></el-divider> -->

        <div class="wordPadding">
            <p>
                <span class="wordSize wordStyle">返回参数</span>
            </p>
        </div>
        <div class="tableClass">
            <common-table
                    :data="responseTableData"
                    :columns="responseTHeadData"
                    class="width100"
                    :paging-type="'client'"
                    :border="false"
                    :pagination="false"
                    row-key="paramId"
            >
            </common-table>
        </div>
    </div>
</template>

<script src="./detail.js">
</script>

<style scoped lang="less" src="./detail.less"></style>