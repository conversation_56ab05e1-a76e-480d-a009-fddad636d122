<template>
    <div class="timing">
        <common-dialog custom-class="timing"
                width="725px"
                :title="title"
                :visible.sync="visible"
                @closed="clearData"
        >
            <params v-if="reNew" />
            <span slot="footer">
                <dg-button @click="visible = false">{{ btnCancelTxt }}</dg-button>
                <dg-button type="primary" @click="save">{{ saveBtnTxt }}</dg-button>
            </span>
        </common-dialog>
    </div>
</template>

<script>
import {dialog} from "@/api/commonMethods/dialog-mixins";
import params from "./params"
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "modelDialog",
    mixins :[dialog,commonMixins],
    components : {params},
    data() {
        return {
            width: "1000px",
            pageLoad: false,

        }
    },
    methods: {
        show() {
            this.title = "选择作业";
            this.visible = true;
            this.reNew = true;
        },
        save(){

        }
    }
}
</script>

<style scoped lang="less">

</style>
