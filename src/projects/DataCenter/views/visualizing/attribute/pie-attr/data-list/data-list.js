import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";
export default {
    name: "dataList" ,
    props: {
        node: Object,
        dir_dataSetId : String
    },
    mixins : [attrMixins],
    data(){
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show: ()=> true,
            }
        };
        return {
            axisList : {
                drilling : {
                    label : "钻取/维度" ,
                    type: 'drill_select',
                    list : [],
                    show : (axis)=> axis.list.length
                },
                xAxis: {
                    label: '扇区占比 /度量',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动度量字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: ()=> true,
                    rule: [
                        {required: true, message: '请添加度量', trigger: 'blur'}
                    ]
                },
                yAxis: {
                    label: '扇区标签/维度',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: ()=> true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ]
                },
                ...formList
            },
            query : null
        }
    },
    computed: {
        chartData() {
            return {
                xAxis: this.axisList.xAxis.value,
                xData: this.axisList.xAxis.options,
                yAxis: this.axisList.yAxis.value,
                yData: this.axisList.yAxis.options,
                filterData: this.filterSelects,
                datasetId: this.datasetId,
                classifierStatId: this.classifierStatId,
                dataset: this.dataset ,
                filterList : this.query,
                drill :this.axisList.drilling.list
            };
        }
    },
    methods : {
        addFilter() {
            this.filterSelects.push({
                value: [],
                options: [],
                data: {},
                filterResult: {},
            });
        },
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            /*if (this.node.code !== 'TableChartWidget') {
                if (this.axisList[inx].prop === 'xAxis' && this.dragTreeNode.indexType === "DIMENSION") {
                    this.$message.warning('请拖入维度输入框');
                    return;
                } else if (this.axisList[inx].prop === 'yAxis' && this.dragTreeNode.indexType === "MEASURE") {
                    this.$message.warning('请拖入度量输入框');
                    return;
                }
            }*/
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx , this.dragTreeNode.indexType , 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        nodeFilter( data){//字段过滤
            this.inputData();
            let filterD = {
                value: [data.data.id],
                options: [
                    {
                        value :data.data.id ,
                        label : data.label
                    }
                ],
                data: data.data,
                filterResult: {},
            } , inx = this.filterSelects.length;
            this.$refs.filter_dialog.open(filterD , inx , true);
        },
        dataFormat( data , item ,index){ //数据格式
            //console.log(data , item , index);
            this.inputData();
            this.$refs.format_dialog.show(data , item , index);
        },
        duplicateFun( data , item , index){//去重
            this.inputData();
            //console.log( data ,item , index);
            let duplicate =  item.options[index]['duplicateF'].value ;
            if( duplicate === '' || !duplicate ){
                item.options[index]['duplicateF'].value = true;
                item.options[index]['duplicateF'].name = '<span style="font-family: cursive;">√</span>';
            }else {
                item.options[index]['duplicateF'].value = false;
            }
            item.options[index].visible = false;
        },
        initValue(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let {beforeData , query} = JSON.parse(vm.node.widget),nodeData = JSON.parse(beforeData);
                if(vm.node.code === "RingPieChartWidget"){
                    vm.axisList.yAxis.multipleLimit = 2;
                }
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId :true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.xAxis.options = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.xAxis.value = nodeData.xAxis ? nodeData.xAxis : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];
                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                //查询
                vm.query  = [];
                //钻取
                vm.axisList.drilling.list = nodeData.drill ? JSON.parse(JSON.stringify(nodeData.drill)) : [];
                //隐藏下拉
                vm.dropDownHide([vm.axisList.xAxis.options , vm.axisList.yAxis.options]);
                vm.inputData();
                vm.addAxisName("","", vm.axisList);
            })
        },
        validate(){
            if (this.chartData.xAxis.length === 0 && this.node.code !== 'TableChartWidget') {
                this.$message.info('请添加度量!');
                return;
            } else if (this.chartData.yAxis.length === 0) {
                this.$message.info('请添加维度!');
                return;
            }
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setPiePanel', this.node.id);
        },
    }
}
