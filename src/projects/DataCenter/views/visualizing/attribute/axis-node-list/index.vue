<template>
    <ul class="ce-node_box AxisNodeList"
        ref="parentNode"
        @drop="checkMove"
    >
        <span class="ce-placeholder" v-if="axis.value.length === 0">{{axis.placeholder}}</span>
        <ce-sortable v-else :data="axis.options" id-key="value">
            <div slot="listItem"
                 slot-scope="{item , $index , id}" :key="id"
                 class="ce-node_drag-list">
                <li :title="item.data.label+funcName(item)" class="ce-node_item"><!--这里取值 item.data(字段数据) 这样当数据集字段数据更新后，仪表盘的图表字段也会更新-->
                    <span>{{item.data.label}}{{funcName(item)}}</span>
                    <el-popover
                            popper-class="ce-popover_notp"
                            trigger="click"
                            v-model="item.visible"
                            @show="changeFastCount"
                    >
                        <DropdownOpt :data="item"
                                     @changeVal="changeVal(arguments , axis , $index)"
                                     @duplicateF="duplicateFun($event , axis , $index)"
                                     @filter="nodeFilter($event , axis , $index)"
                                     @changeFastCount="changeFastCount()"
                        />
                        <i class="el-icon-caret-bottom ce-node_option poi" v-if="!hiddenOpt" slot="reference"></i>
                    </el-popover>
                    <dg-button v-if="keyVal === 'yAxis' && showDrill" type="text" title="钻取" icon="el-icon-bottom-right"
                               @click="showDrilling(axis , $index)"></dg-button>
                    <i class="el-icon-close ce-node_close poi" @click="deleteNode(keyVal , $index)"></i>
                </li>
                <em class="el-icon-arrow-right ce-node_arrow"
                    v-if="keyVal ==='xAxis' && axis.options.length > 0 && $index < axis.options.length -1"></em>
            </div>
        </ce-sortable>

    </ul>
</template>

<script src="./axis-node-list.js"></script>
<style scoped lang="less" src="./axis-node-list.less"></style>
