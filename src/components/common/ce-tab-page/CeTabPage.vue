<template>
    <div class="ce-tab-page">
        <el-tabs class="ce-tab-page_tab" v-bind="$attrs" v-on="$listeners">
            <el-tab-pane v-for="(tab , inx) in tabPanes" :key="inx" :label="tab.label" :name="tab.value">
            </el-tab-pane>
        </el-tabs>
        <keep-alive>
            <slot name="page" classN="ce-tab-page_cont"></slot>
        </keep-alive>
    </div>
</template>

<script>
export default {
    name: "CeTabPage" ,
    props : {
        tabPanes : {
            type : Array ,
            default: ()=>[]
        }
    },
}
</script>

<style scoped lang="less">
.ce-tab-page {
    height: calc(100% - 12px);
    background: #fff;
    border-radius: 2px;
    box-sizing: border-box;
    padding: 10px 0;
    &_tab {
        &.el-tabs--top /deep/ .el-tabs__item.is-bottom:nth-child(2),
        &.el-tabs--top /deep/ .el-tabs__item.is-top:nth-child(2) {
            padding-left: 30px;
        }
    }
    &_cont {
        height: calc(100% - 54px);
        padding: 10px 28px;
        box-sizing: border-box;
    }
}
</style>
