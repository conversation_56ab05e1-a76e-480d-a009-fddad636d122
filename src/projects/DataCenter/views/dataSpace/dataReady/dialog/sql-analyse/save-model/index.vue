<template>
    <common-dialog custom-class="newModel"
                   :title="title" :width="width" :visible.sync="visible" @closed="clearData">
       <new-model-name v-if="reNew" ref="newName" :load-params="settings" :label="label"></new-model-name>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button type="primary" :disabled="settings.loading" @click="submit">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>

<script src="./new-model.js"></script>
