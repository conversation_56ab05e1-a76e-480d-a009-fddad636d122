<template>
    <div class="height100">
        <code-editor v-model="value" language="x-sql" read-only></code-editor>
    </div>

</template>

<script>
import codeEditor from "@/components/common/codeEditor/codeEditor";
export default {
    name: "SqlPage",
    components: {
        codeEditor
    },
    props: {
        value: String,
    },
    data() {
        return {}
    },
    methods: {},
    mounted() {
        this.triggerEvent('resize' , 300);
    }
}
</script>

<style scoped>

</style>
