<!--
    通用目录树 编辑 组件
    使用 ： add-tree-dir 直接使用不用注册组件
    <add-tree-dir ref="treeDir" @addTreeDir="addTreeDir" @moveTreeDir="moveTreeDir" @filterData="filterEditTree" >
        <tree slot="tree" ref="editTree" slot-scope="scope" v-bind="scope" @nodeClick="nodeClick" />
    </add-tree-dir>

    树的点击事件需绑定
    nodeClick ： nodeClick(name , id , node) {
        this.$refs.treeDir.nodeClick(name , id , node);
    },
    filterData ：  目录树的过滤
    filterEditTree(val) {
        this.$refs.editTree.setFilterText(val);
    },
    addTreeDir (name 目录名称 , parentData 挂载父级目录数据) ： 添加目录 执行接口事件

    moveTreeDir (currentMoveNode 当前节点数据 , parentData 移动到的父级目录数据) ： 移动目录 接口事件

   接口执行结束后，stop方法关闭弹窗
-->
<template>
    <common-dialog custom-class="addTreeDir" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="clearData"
    >
        <el-form v-if="reNew" :model="form" label-width="100px" label-position="right">
            <el-form-item :label="formList.name.label" prop="name"
                          :rules="[{ required: true, message: '请输入目录名称', trigger: ['blur','change'] }]">
                <div class="content__name">
                    <el-input v-model.trim="form.name" maxlength="25"
                              :disabled="type === 'move'"
                              v-input-limit:custom
                              :ce-custom-reg="specialReg"
                              :ce-custom-msg="limitMessage"
                              :placeholder="formList.name.placeholder"></el-input>
                    <p>{{ formList.name.tip }}</p>
                </div>
            </el-form-item>
            <el-form-item :label="formList.pos.label" required>
                <p class="node-path">{{nodePath}}</p>
                <div class="content__wrap">
                    <!--目录树-->
                    <el-input placeholder="请输入名称" v-model.trim="filterText" v-input-limit:trim size="medium" class="ce-search">
                        <i slot="suffix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <div class="ce-tree selectn">
                        <slot name="tree" class="content__tree" :curId="curId" :dirLevel="level" hasSave :dirId="dirId" editDir ></slot>
                    </div>
                </div>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="stop" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button @click="save" type="primary" size="mini">{{ btnCheckTxt }}</el-button>
        </div>
    </common-dialog>
</template>

<script>
import {listMixins} from "@/api/commonMethods/list-mixins";
import {dialog} from "@/api/commonMethods/dialog-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "addTreeDir",
    mixins : [listMixins ,dialog , commonMixins],
    data() {
        return {
            level : 2 ,
            filterText:"",
            form: {
                name: "",
                pos: "",
            },
            formList : {
                name : {
                    label: '目录名称 :',
                    placeholder: '请输入目录名称',
                    tip : "名称最大不超过25字符，只允许除/、\\、<、>等特殊字符以外。"
                },
                pos : {
                    label : "上级目录 :"
                },
            },
            dirId : "" ,
            parentData : {} ,
            type : "" ,
            currentMoveNode : {} ,//移动点的Id
            curId : "" , //移动当前节点Id
            nodePath: ''
        }
    },
    watch: {
        filterText(val) {
            this.$emit("filterData" , val);
        }
    },
    methods : {
        clearData(){
            this.reNew = false;
            this.filterText = "";
        },
        /**
         * 重置值
         * */
        reSetForm(){
            this.dirId = "";
            this.curId = "";
            this.form = {
                name: "",
                pos: "",
            };
            this.currentMoveNode = {};
            this.nodePath = '';
        },
        /**
         * 弹窗显示
         * @param level 限制层级
         * @param data 父节点数据
         * @param type 新建 、移动
         * */
        show(data , type="new",level=5){
            this.visible = true;
            this.type = type;
            this.level = level;
            this.title = type === 'move' ? "移动目录到" : "新建目录";
            this.reSetForm();
            if(type === 'move'){
                this.form.name = data.name;
                this.form.pos = data.pId;
                this.dirId = data.pId;
                this.currentMoveNode = data;
                this.curId = data.id;
            }
            this.reNew = true;
            if(data && data.id && type !== 'move'){
                this.dirId = data.id;
                this.form.pos = data.id;
                this.parentData = data;
            }
        },
        nodeClick(name , id , node , props){
            let treeProp = props || {
                value: "id",
                label: "label",
                children: "children"
            };
            this.form.pos = id;
            this.parentData = node.data;
            this.handleSetPath(node , treeProp)
        },
        save(){
            const {type} = this;
            if(type === 'new' ){
                this.addTreeDir();
            }else if(type === 'move'){
                this.moveTreeDir();
            }
        },
        /**
         * 移动目录
         */
        moveTreeDir(){
            const vm = this,{parentData ,currentMoveNode , dirId} = vm, { pos } = vm.form;
            if(dirId === pos){
                vm.visible = false;
                return ;
            }
            vm.$emit("moveTreeDir" , currentMoveNode , parentData);
        },
        /**
         * 新增目录
         */
        addTreeDir(){
            const vm = this,{parentData} = vm, {name , pos } = vm.form;
            if (pos === "") {
                vm.$message.warning("请选择存放路径！");
                return ;
            } else if (name === "") {
                vm.$message.warning("请输入目录名称！");
                return ;
            }
            vm.$emit("addTreeDir" , name , parentData);

        },
        stop(){
            this.visible = false;
        },
        /**
         * 设置当前选中节点路径
         * @param node
         * @param treeProp
         */
        handleSetPath(node , treeProp){
            this.nodePath = '';
            let path = [];
            while(node.level >= 1){
                const label = node.data[treeProp.label];
                path.unshift(label)
                node = node.parent;
            }
            this.nodePath = path.join('/')
        }
    }
}
</script>

<style scoped lang="less">
.content {
    &__name {
        p {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 21px;
            margin-top: 4px;
        }
    }
    &__tree {
        float: none;
        margin:0;
        border: none;
        height: 100%;
        width: auto;
        padding: 0;
    }
    &__wrap {
        padding: 14px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        box-sizing: border-box;
        height: 250px;
    }
}
.ce-tree {
    height: calc(100% - 32px);
    overflow: auto;
    box-sizing: border-box;
    padding-top: 10px;
}
.node-path{
    line-height: 2rem;
    color: rgba(0, 0, 0, 0.45);
}
</style>
