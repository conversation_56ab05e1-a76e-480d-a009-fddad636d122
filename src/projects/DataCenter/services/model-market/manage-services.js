/**
 * 模型市场管理 接口
 * @Author: qzx
 * @Date: 2021-10-09
 * @Project cicada-dataCenter-webui
 */

import * as reqUtil from '@/api/reqUtil'

const root = "/supermarkmodel/mymodel/", root1 = "/supermarkmodel/modelmark/", root2 = "/supermarkmodel/mainpage/";

/*
*  查询待发布模型列表
 * @param
 * @returns {Promise<*>}
* */

export async function unPublishModelList(data, settings) {
    return await reqUtil.post({
        url: root + "unPublishModelList",
        settings: settings
    }, data)
}

/*
*  查询已发布模型列表
 * @param
 * @returns {Promise<*>}
* */

export async function publishedModelList(data, settings) {
    return await reqUtil.post({
        url: root + "publishedModelList",
        settings: settings
    }, data)
}

//发布模型
export async function publishModel(data, settings) {
    return await reqUtil.post({
        url: root + "publishModel",
        settings: settings
    }, data)
}

//编辑模型
export async function updateMarkModel(data, settings) {
    return await reqUtil.post({
        url: root + "updateMarkModel",
        settings: settings
    }, data)
}

//编辑时获取模型信息
export async function initUpdateMarkModel(data, settings) {
    return await reqUtil.post({
        url: root + "initUpdateMarkModel",
        settings: settings
    }, data)
}

//查询标签
export async function queryModelLabelList(data, settings) {
    return await reqUtil.post({
        url: root + "queryModelLabelList",
        settings: settings
    }, data)
}

//新增标签
export async function addModelLabel(data, settings) {
    return await reqUtil.post({
        url: root + "addModelLabel",
        settings: settings
    }, data)
}

//模型上下架
export async function updateModelState(data, settings) {
    return await reqUtil.post({
        url: root + "updateModelState",
        settings: settings
    }, data)
}

// 模型启停
export async function updateEnabledState(data, settings) {
    return await reqUtil.post({
        url: root + "updateEnabledState",
        settings: settings
    }, data)
}

//模型删除
export async function deleteModel(data, settings) {
    return await reqUtil.post({
        url: root + "deleteModel",
        settings: settings
    }, data)
}

//获取模型分类信息
export async function initPublishModel(data, settings) {
    return await reqUtil.post({
        url: root + "initPublishModel",
        settings: settings
    }, data)
}


//模型详情
export async function viewMarkModelDetail(data, settings) {
    return await reqUtil.post({
        url: root1 + "viewMarkModelDetail",
        settings: settings
    }, data)
}

//同类型推荐查询
export async function sameTypeModelList(data, settings) {
    return await reqUtil.post({
        url: root1 + "sameTypeModelList",
        settings: settings
    }, data)
}

//已关注用户列表
export async function focusedUserlList(data, settings) {
    return await reqUtil.post({
        url: root1 + "focusedUserlList",
        settings: settings
    }, data)
}

//关注或取消关注模型
export async function focusOrUnFocusModel(data, settings) {
    return await reqUtil.post({
        url: root1 + "focusOrUnFocusModel",
        settings: settings
    }, data)
}

//查询累计评价列表
export async function queryModelEvaluationList(data, settings) {
    return await reqUtil.post({
        url: root1 + "queryModelEvaluationList",
        settings: settings
    }, data)
}

//评价模型
export async function evaluationModel(data, settings) {
    return await reqUtil.post({
        url: root1 + "evaluationModel",
        settings: settings
    }, data)
}

//评价详情
export async function modelEvaluationDetail(data, settings) {
    return await reqUtil.post({
        url: root1 + "modelEvaluationDetail",
        settings: settings
    }, data)
}

//主页

//模型主页信息初始化 -- 日志
export async function initMainPageModel(settings) {
    return await reqUtil.post({
        url: root2 + "initMainPageModel",
        settings: settings
    })
}

//模型信息统计
export async function modelCountInfo(settings) {
    return await reqUtil.post({
        url: root2 + "modelCountInfo",
        settings: settings
    })
}

//查询模型排行榜列表
export async function modelRankingList(data, settings) {
    return await reqUtil.post({
        url: root2 + "modelRankingList",
        settings: settings
    }, data)
}

//查询模型用户排行上架榜列表
export async function modelUserPublishRankingList(data, settings) {
    return await reqUtil.post({
        url: root2 + "modelUserPublishRankingList",
        settings: settings
    }, data)
}


/**
 * 模型市场卡片列表
 * @param params
 * {
    "objType":"cs", //传code
    "appType":"",
    "policeType":"",
    "searchType":"label",//label：模型标签,name:模型名称
    "searchCond":"试",
    "sortType":"browse",//focus:关注 browse：浏览 score：评分  publishTime：发布时间
    "sort":"desc",  //asc:升序 desc:降序
    "pageNum":1,
    "pageSize":2
}
 * @param settings
 * @return {Promise<*>}
 */
export async function queryModelMarketPage(params, settings) {
    return reqUtil.post({
        url: root1 + "queryModelMarketPage",
        settings
    }, params)
}


/**
 * 我的关注 - 列表
 * @param {*} params
 * @param {*} settings
 */
export async function myFocusModelPage(params, settings) {
    return reqUtil.post({
        url: root + "myFocusModelPage",
        settings
    }, params)
}

// 申请 流程管理 接口
const root4 = '/supermarkmodel/modelResource/';

/**
 * 模型申请
 * @param params
 * @param settings
 * @returns {Promise<*>}
 */
export async function resourceApply(params, settings) {
    return reqUtil.post({
        url: root4 + 'resourceApply',
        settings,
    }, params)
}

/**
 * 申请或审批列表
 * @param params
 * @param settings
 * @returns {Promise<*>}
 */
export async function resourceApplyList(params, settings) {
    return reqUtil.post({
        url: root4 + 'resourceApplyList',
        settings,
    }, params)
}

/**
 * 审批
 * @param params
 * @param settings
 * @returns {Promise<*>}
 */
export async function audit(params, settings) {
    return reqUtil.post({
        url: root4 + 'audit',
        settings,
    }, params)
}

/**
 * 审批详情
 * @param params
 * @param settings
 * @returns {Promise<*>}
 */
export async function viewAuditDetail(params, settings) {
    return reqUtil.post({
        url: root4 + 'viewAuditDetail',
        settings,
    }, params)
}

/**
 * 审批确认
 * @param params
 * @param settings
 * @returns {Promise<*>}
 */
export async function auditResultConfirm(params, settings) {
    return reqUtil.post({
        url: root4 + 'auditResultConfirm',
        settings,
    }, params)
}

