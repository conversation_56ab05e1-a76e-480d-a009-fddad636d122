<template>
    <div class="classificationCounting ce-plug_cont" v-loading="loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont" v-show="activeName === 'setting_base'">
            <div class="ce-plug-btn_box">
                <el-button type="primary"
                           @click="add"
                           plain
                >{{ addbtn }}
                </el-button>
            </div>
            <div class="ce-plug_attr">
                <div class="ce-list_cont mr-8">
                    <el-form size="mini"
                             ref="form"
                             class="ce-form__m0 height100"
                             :model="{ list: markingTimeDataForm }"
                             @submit.native.prevent
                    >
                        <el-table
                            height="100%"
                            class="h_auto-table"
                            cell-class-name="ce-cell_p0"
                            border
                            :data="markingTimeDataForm"
                            :show-header="status"
                            size="mini">
                            <el-table-column type="index" align="center" width="40px"></el-table-column>
                            <el-table-column align="center">
                                <template slot-scope="{row , $index}">
                                    <el-row class="ce-row-item" :gutter="7">
                                        <el-col class="ce-common_item is-require" :span="9">
                                            <span>输入字段:</span>
                                        </el-col>
                                        <el-col :span="15">
                                            <el-form-item :prop="`list.${$index}.columnValue`"
                                                          :rules="rules.columnValue">
                                                <el-select size="mini"
                                                           v-model="row.columnValue"
                                                           placeholder="请选择"
                                                           filterable
                                                >
                                                    <el-option
                                                        v-for="item in columns"
                                                        :key="item.code"
                                                        :label="item.name"
                                                        :value="item.code">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row class="ce-row-item" :gutter="7">
                                        <el-col class="ce-common_item is-require" :span="9">
                                            <span>时间间隔: </span>
                                        </el-col>
                                        <el-col :span="15" class="ce-normal_right">
                                            <el-input-number size="mini" class="timeInputNumber" :min="1" :max="60"
                                                             controls-position="right" v-model="row.thresholdValue"
                                            ></el-input-number>
                                            <el-select v-model="row.timeFormatValue" placeholder="" class="timeSelect">
                                                <el-option
                                                v-for="item in timeOptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                                </el-option>
                                            </el-select>
                                            <!-- <span class="p8">{{ minTip }}</span> -->
                                        </el-col>
                                    </el-row>
                                    <el-row class="ce-row-item" :gutter="7">
                                        <el-col class="ce-common_item is-require" :span="9">
                                            <span>输出字段: </span>
                                        </el-col>
                                        <el-col :span="15">
                                            <el-form-item :prop="`list.${$index}.normalizedFields`"
                                                          :rules="rules.normalizedFields">
                                                <el-input size="mini" v-model="row.normalizedFields"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </template>
                            </el-table-column>
                            <el-table-column width="50px" align="center">
                                <template slot-scope="{row , $index}">
                                    <el-popconfirm
                                        size="mini"
                                        v-for="(col , inx) in operateIcons"
                                        title="确认删除?"
                                        @confirm="col.clickFn(row,$index)"
                                    >
                                        <i
                                            slot="reference"
                                            class="icon ce_link model_status"
                                            :key="inx"
                                            :title="col.tip"
                                            v-html="col.icon"
                                        ></i>
                                    </el-popconfirm>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                </div>
            </div>
            <div class="attr_btn">
                <el-button
                    v-for="(btn , inx) in buttons"
                    :key="inx"
                    size="mini"
                    :type="btn.type"
                    @click="btn.clickFn"
                >{{ btn.name }}
                </el-button>
            </div>
        </div>
        <div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
import PlugSetting from '../ruleEditing/PlugSetting'
import SettingSenior from '../component/SettingSenior'
import PreView from "../component/PreView"
import {pluginMixins} from "../component/plugin-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins"

export default {
    name: "MarkingTime",
    components: {
        PlugSetting,
        SettingSenior,
        PreView
    },
    mixins: [pluginMixins, commonMixins, servicesMixins],
    props: {
        sourceData: Object,
        row: Object
    },
    data() {
        return {
            exps: [],
            addbtn: "添加",
            // timeFormatValue: "mins",
            timeOptions: [{
                value: 'hour',
                label: '时'
            }, {
                value: 'mins',
                label: '分'
            }, {
                value: 'seconds',
                label: '秒'
            }],
            minTip: "分",
            loading: false,
            dataTypes: ['Float', 'Double', 'BigDecimal'],
            isSeniorShow: {},
            activeName: 'setting_base',
            tab_panel: [
                {
                    label: '基本设置',
                    name: 'setting_base'
                }, {
                    label: '高级设置',
                    name: 'setting_senior'
                }
            ],
            buttons: [
                {
                    name: '保存',
                    clickFn: this.saveFn,
                    type: 'primary'
                }, {
                    name: '预览',
                    clickFn: this.previewFn,
                    type: '',
                }
            ],
            status: false,
            rules: {
                columnValue: [
                    {required: true, message: '请选择输入字段', trigger: 'change'},
                ],
                normalizedFields: [
                    {required: true, message: '请输入输出字段', trigger: ['blur', 'change']},
                    {
                        validator: this.checkedCommon,
                        trigger: ['blur', 'change'],
                        reg: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                        message: "限字母、数字、下划线(非数字开头)"
                    },
                ]
            },
            //分组统计
            markingTimeDataForm: [],
            inputType: [],
            rowData: '',
            groupFieldName: [],
            columns: [],
            operateIcons: [
                {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteClassificationData}
            ],
        }
    },
    methods: {
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        selectDataType(val) {
            val.hasPrecision = this.checkDataType(val.outputTypeValue);
        },
        init() {
            const vm = this, {services , sourceData} = vm;
            vm.loading = true;
            services.markingTimeView(sourceData.id , vm).then(res => {
                if (res.data.status === 0) {
                    let pluginMeta = res.data.data;
                    //初始化输入字段
                    let columns = pluginMeta.column && pluginMeta.column.filter(col => {
                        col.name = col.columnZhName || col.columnName;
                        col.code = col.columnName ;
                        return col.columnType === "Date" || col.columnType === "Timestamp";
                    });
                    vm.columns = columns ? columns : [];
                    //初始化数据
                    if(pluginMeta.pluginMeta.markingTimeGroupInfo.length === 0) vm.add();
                    pluginMeta.pluginMeta.markingTimeGroupInfo.forEach(item => {
                        vm.markingTimeDataForm.push(
                            {
                                thresholdValue: item.thresholdValue, //阈值
                                columnValue: item.inputFieldCode,//输入字段
                                normalizedFields: item.normalizedFieldCode,//归一化字段
                                precisionValue: 0,
                                timeFormatValue: item.unit,
                            }
                        );
                    })
                }
            })
        },
        // 分组统计删除操作
        deleteClassificationData(row, index) {
            this.markingTimeDataForm.splice(index, 1);
        },
        saveFn() {
            const vm = this , {services , sourceData} = vm;
            vm.resetSenior();
            let pluginMeta = {};
            if (vm.markingTimeDataForm.length === 0) {
                vm.$message.error("请添加配置信息！");
                return;
            }
            vm.validate(valid => {
                if (valid) {
                    pluginMeta['markingTimeGroupInfo'] = vm.getTimeGroup();
                    vm.loading = true;
                    services.saveMarkingTime(sourceData.id , pluginMeta , vm).then(res => {
                        if (res.data.status === 0) vm.$message.success("保存成功");
                    })
                }
            })
        },
        getTimeGroup(){
            let result = [];
            const vm = this, {markingTimeDataForm , columns} = vm;
            markingTimeDataForm.forEach(item =>{
                let column = columns.find(col => item.columnValue === col.code);
                result.push({
                    'inputFieldName': column.name,
                    'inputFieldCode': column.code,
                    'thresholdValue': item.thresholdValue,
                    'normalizedFieldName': item.normalizedFields,
                    'normalizedFieldCode': item.normalizedFields,
                    'unit' : item.timeFormatValue,
                })
            })
            return result;
        },
        initSettingSenior() {
            this.$refs.settingSenior.initValue();
        },
        saveColumn() {
            this.$refs.settingSenior.saveFieldTable();
        },
        checkDataType(val) {
            return this.dataTypes.indexOf(val) > -1;
        },
        previewFn() {
            this.$refs.preview.show(this.row, this.sourceData.id);
        },
        add() {
            this.markingTimeDataForm.push(
                {
                    thresholdValue: 1, //阈值
                    columnValue: "",//输入字段
                    normalizedFields: "",//归一化字段
                    precisionValue: 0,
                    timeFormatValue: "mins"
                }
            );
        }
    },
    created() {
        this.isSeniorShow = {
            partitionShow: false,
            fieldInput: false,
            fieldOutPut: true,
            isCopyOrDispense: false,
            isSampleExpr: false,
            isThread: false,
            isException: false,
            notTransition: false,
        };
        this.init();
    }
}
</script>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped>
.countingRadio {
    width: 100%;
    text-align: center;
}

.countingContent {
    margin-top: 10px;
}

.countingSelect {
    height: 5px;
}

.btn_new_model {
    float: right;
    margin-bottom: 5px;
    margin-top: -27px;
    margin-left: 3px;
}

.timeInputNumber {
    width: 50px;
}

.timeSelect {
    padding-left:10px;
    width:86px;
}
</style>
