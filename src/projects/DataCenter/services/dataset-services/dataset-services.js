/**
 * 数据集管理 接口
 * @Author: wangjt
 * @Date: 2020-07-03
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/dataSetOperation/";

/*
* 初始化树
*@params
* @returns {Promise<*>}
* */

export async function queryDataSetTree(hasDataObj ,settings,currentDataSetId) {
   return await reqUtil.get({
       url : root + "queryDataSetTree",
       settings : settings
   },{hasDataObj,currentDataSetId})
}

/*
*  获取数据集（源数据集和自定义数据集 合并的树）
 * @param
 * @returns {Promise<*>}
* */

export async function datasetList(settings,currentDataSetId) {
    return await reqUtil.get({
        url : "/dataSet/list" ,
        settings : settings
    },{currentDataSetId})
}
/*
* 添加树节点
*@params
* @returns {Promise<*>}
* */

export async function addDataSetTreeNode(busiId , name) {
   return await reqUtil.get({
       url : root + "addDataSetTreeNode"
   }, {busiId , name})
}
/*
* 树节点重命名
*@params
* @returns {Promise<*>}
* */

export async function reNameDataSetTreeNode(classifyId , name , oldName) {
   return await reqUtil.get({
       url : root + "reNameDataSetTreeNode"
   }, {classifyId , name , oldName})
}
/*
* 树节点删除
*@params
* @returns {Promise<*>}
* */

export async function deleteDataSetTreeNode(nodeId ) {
   return await reqUtil.get({
       url : root + "deleteDataSetTreeNode"
   }, {nodeId})
}
/*
* 授权数据集
*@params
* @returns {Promise<*>}
* */

export async function accreditLogicDataObj(dataObjIds ,dataSetTreeNodeId ,settings  ) {
   return await reqUtil.post({
       url : root + "accreditLogicDataObj",
       settings : settings
   }, {dataObjIds , dataSetTreeNodeId})
}
/*
* 校验是否有数据集
*@params
* @returns {Promise<*>}
* */

export async function checkHasDataSet(busiId ) {
   return await reqUtil.get({
       url : root + "checkHasDataSet"
   }, { busiId  })
}

/*
* 删除数据集
*@params
* @returns {Promise<*>}
* */

export async function deleteDataSet(id ) {
   return await reqUtil.get({
       url : root + "deleteDataSet"
   }, { id })
}

/**
 * 删除输入验证
 * @param params
 * @return {Promise<*>}
 */
export async function deleteOneFieldCheck(params ) {
    return await reqUtil.post({
        url : "/pluginManage/deleteOneFieldCheck" ,
    },params )
}

/*
* 获取数据集字段
*@params
* @returns {Promise<*>}
* */

export async function getDataSetColumn(id ) {
   return await reqUtil.get({
       url : root + "getDataSetColumn"
   }, { id })
}
/*
* 获取数据集分页
*@params id ,name , page , pageSize ,
* @returns {Promise<*>}
* */

export async function getDataSetPage(params , settings ) {
   return await reqUtil.get({
       url : root + "getDataSetPage",
       settings : settings
   }, params)
}
/*
* 数据集详情
*@params
* @returns {Promise<*>}
* */

export async function queryDataSetById(id ) {
   return await reqUtil.get({
       url : root + "queryDataSetById"
   }, { id })
}
/*
* 更新数据集
*@params authType , code , dataType ,id , isFast ,name, nameSpace , search_sql ,userId
* @returns {Promise<*>}
* */

export async function updateDataSet( params) {
   return await reqUtil.get({
       url : root + "updateDataSet"
   }, params )
}

const root1 = "/editDataSet/";

/*
* 字段新增操作
*@params code , columnCode , columnName ,dataSetId , dataType ,exp, markInfo , userId
* @returns {Promise<*>}
* */

export async function addDataSetColumn( params, settings) {
    return await reqUtil.post({
        url : root1 + "addDataSetColumn",
        settings : settings
    }, params )
}
/*
* 维度和度量的转换和字段类型的转换
*@params
* {
  "code": "string",
  "columnId": "string",
  "dataSetAlias": "string",
  "dataSetId": "string",
  "indexType": "string",
  "userId": "string"
}
* @returns {Promise<*>}
* */

export async function changeIndexType( params) {
    return await reqUtil.post({
        url : root1 + "indexType"
    }, params )
}
/*
* 快速分析 新建数据集
*@params
* @returns {Promise<*>}
* */

export async function createLogicDataSet(params ,settings) {
    return await reqUtil.get({
        url : root1 + "createLogicDataSet",
        settings : settings
    }, params)
}
/*
*字段编辑操作
*@params code , columnCode , columnId , columnName , comment , dataSetId, showColumnName , userId
* {
  "code": "string",
  "columnCode": "string",
  "columnId": "string",
  "comment": "string",
  "dataSetAlias": "string",
  "dataSetId": "string",
  "showColumnName": "string",
  "userId": "string"
}
* @returns {Promise<*>}
* */

export async function editDataSetColumn(params , settings) {
    return await reqUtil.post({
        url : root1 + "editDataSetColumn" ,
        settings :settings
    }, params )
}
/*
*获取编辑字段信息
*@params columnId
* @returns {Promise<*>}
* */

export async function getDataSetColumnInfo(columnId) {
    return await reqUtil.get({
        url : root1 + "getDataSetColumnInfo"
    }, {columnId} )
}
/*
*获取步骤
*@params id
* @returns {Promise<*>}
* */

export async function getDataSetStepRelation(id , settings) {
    return await reqUtil.get({
        url : root1 + "getDataSetStepRelation",
        settings : settings
    }, {id} )
}
/*
*添加数据集到面板
*@params id
* @returns {Promise<*>}
* */

export async function getLogicDataColumn(addDataSetId , currentDataSetId , settings) {
    return await reqUtil.get({
        url : root1 + "getLogicDataColumn",
        settings : settings
    }, {addDataSetId , currentDataSetId} )
}
/*
*获取同步字段
*@params syncColumnVo
* @returns {Promise<*>}
* */

export async function getSyncLogicDataColumn(logicDataObjId) {
    return await reqUtil.get({
        url : root1 + "getSyncLogicDataColumn"
    }, {logicDataObjId} )
}
/*
*数据集预览
*@params maps {dataSetId:数据集id,fields：[]字段的名称}
* @returns {Promise<*>}
* */

export async function previewLogicDataSet(previewVo , settings) {
    return await reqUtil.post({
        url : root1 + "previewLogicDataSet" ,
        settings : settings
    }, previewVo  )
}
/*
* 保存
*@params columns  , logicDataSetId , metaDataObjIds , name
* @returns {Promise<*>}
* */

export async function save(saveDataSetVo ,settings ) {
    return await reqUtil.post({
        url : root1 + "save",
        settings :settings
    }, saveDataSetVo )
}

/*
* 另保存
*@params columns  , logicDataSetId , metaDataObjIds , name
* @returns {Promise<*>}
* */

export async function saveAs (saveDataSetVo ,settings ) {
    return await reqUtil.post({
        url : root1 + "saveAs",
        settings :settings
    }, saveDataSetVo )
}

/*
* 保存同步新增数据集
*@params
* @returns {Promise<*>}
* */

export async function saveLogicDataColumn(columnList ) {
    return await reqUtil.get({
        url : root1 + "saveLogicDataColumn"
    }, {columnList} )
}
const sql_root = "/quickSql/";

/*
* sql 字段分析
*@params
* @returns {Promise<*>}
* */

export async function sqlColumns( sql ,schemaId , settings) {
    return await reqUtil.post({
        url : sql_root + "columns",
        settings
    },{sql,schemaId})
}
/*
* sql 即席SQL分析保存、新建
* @data classifyId 目录节点ID , dataObjCode , dataObjName , schemaId , sql, dsTypeClassifyId
* @returns {Promise<*>}
* */

export async function sqlSave(data,settings) {
    return await reqUtil.post({
        url : sql_root + "save",
        settings
    }, data)
}

/**
 * sql 修改
 * @param dataObjId
 * @param dataObjName
 * @param sql
 * @param classifyId
 * @param settings
 * @return {Promise<*>}
 */
export  async function sqlUpdate(dataObjId , dataObjName ,sql,classifyId, settings){
    return await reqUtil.post({
        url : sql_root + "update" ,
        settings
    },{dataObjId , dataObjName ,sql ,classifyId})
}
/*
* sql 预览
*@params
* @returns {Promise<*>}
* */

export async function sqlPreview(page ,pageSize , sql , schemaId , settings ) {
    return await reqUtil.post({
        url : sql_root + "preview",
        settings
    }, {page ,pageSize , sql , schemaId } )
}
/*
* sql spark注册表信息
*@params
* @returns {Promise<*>}
* */

export async function registered(settings,schemaId) {
    return await reqUtil.get({
        url : sql_root + "registered",
        settings : settings
    },{schemaId})
}
/*
* sql 保存
*@params
* @returns {Promise<*>}
* */

/*export async function sqlSave( dataObjName , dataObjId , sql ,settings) {
    return await reqUtil.get({
        url : sql_root + `save`,
        settings : settings
    },{dataObjName ,dataObjId , sql})
}*/

/*
* sql 回显
*@params
* @returns {Promise<*>}
* */

export async function sqlQuery(dataObjId,settings) {
    return await reqUtil.get({
        url : sql_root + "query",
        settings
    },{dataObjId })
}

/*
* 获取函数
*@params
* @returns {Promise<*>}
* */

export async function getFunction(condition="" ) {
    return await reqUtil.get({
        url : root1 + "getFunction"
    }, {condition} )
}
/*
* 查询字段
*@params
* @returns {Promise<*>}
* */

export async function getLogicColumn(condition="" , id , settings) {
    return await reqUtil.get({
        url : root1 + "getLogicColumn",
        settings : settings
    }, {condition ,id} )
}
/*
* 删除字段
*@params {
  "code": "string",
  "columnId": "string",
  "dataSetAlias": "string",
  "dataSetId": "string",
  "name": "string",
  "userId": "string"
}
* @returns {Promise<*>}
* */

export async function deleteColumn(params ) {
    return await reqUtil.post({
        url : root1 + "deleteColumn"
    }, params )
}
/*
* 设置过滤条件
*@params
* {
  "code": "string",
  "condition": "string",
  "dataSetAlias": "string",
  "dataSetId": "string",
  "userId": "string"
}
* @returns {Promise<*>}
* */

export async function filterCondition(params,settings ) {
    return await reqUtil.post({
        url : root1 + "filter",
        settings : settings
    }, params )
}
/*
* 数据格式转换
*@params
* {
  "code": "string",
  "columnId": "string",
  "dataSetAlias": "string",
  "dataSetId": "string",
  "format": "string",
  "userId": "string"
}
* @returns {Promise<*>}
* */

export async function format(params ) {
    return await reqUtil.post({
        url : root1 + "format"
    }, params )
}
/*
* 获取步骤详情
*@params
*
* @returns {Promise<*>}
* */

export async function getDataSetStepInfo(id ) {
    return await reqUtil.get({
        url : root1 + "getDataSetStepInfo"
    }, {id} )
}
/*
* 左右关联
*@params
*
* @returns {Promise<*>}
* */

export async function join(params ,settings) {
    return await reqUtil.post({
        url : root1 + "join",
        settings : settings
    }, params )
}
/*
* 数字格式
*@params
*
* @returns {Promise<*>}
* */

export async function numberFormat(params ) {
    return await reqUtil.post({
        url : root1 + "numberFormat"
    }, params )
}
/*
* 同步数据结构
*@params
*
* @returns {Promise<*>}
* */

export async function syncColumn( params ,settings ) {
    return await reqUtil.post({
        url : root1 + "syncColumn" ,
        settings : settings
    } , params)
}
/*
* 上下合并
*@params
* @returns {Promise<*>}
* */

export async function union(params, settings) {
    return await reqUtil.post({
        url : root1 + "union" ,
        settings : settings
    },params)
}
/*
* 删除步骤
*@params
* @returns {Promise<*>}
* */

export async function deleteDataStep(id , dataSetId) {
    return await reqUtil.post({
        url : root1 + `deleteDataStep?id=${id}&dataSetId=${dataSetId}`
    })
}
/*
* 编辑步骤
*@params
* @returns {Promise<*>}
* */

export async function editDataStep(params , settings) {
    return await reqUtil.post({
        url : root1 + "editDataStep" ,
        settings : settings
    },params)
}
/*
*  发布
 * @param sourceId
 * @returns {Promise<*>}
* */
export async function isIssue( sourceId ) {
    return await reqUtil.get({
        url: '/publish/' + 'isIssue'
    }, {sourceId});
}

/*
*  发布服务
 * @param param
 * @returns {Promise<*>}
* */
export async function createService( param ) {
    return await reqUtil.post({
        url:  '/publish/createService'
    } , param);
}

/*
*  详情
 * @param pageSize , pageIndex ,name , sortField , sortOrder, objId , dbType
 * @returns {Promise<*>}
* */
export async function queryElasticsColumns( {pageSize , pageIndex ,name , sortField , sortOrder, objId , dbType }) {
    return await reqUtil.post({
        url: '/manage/dataWarehouse/queryElasticsColumns'
    }, {pageSize , pageIndex ,name , sortField , sortOrder, objId , dbType});
}



const dw_root = "/manage/dataWarehouse/";

/*
*  选择服务
 * @param param
 * @returns {Promise<*>}
* */
export async function checkServiceName( name , code ) {
    return await reqUtil.get({
        url: dw_root+  'checkServiceName'
    } , {name , code});
}
/*
*  测试服务
 * @param param
 * @returns {Promise<*>}
* */
export async function testService( param ) {
    return await reqUtil.post({
        url:  '/publish/testService'
    } , param);
}

/*
*  移动
 * @param param
 * @returns {Promise<*>}
* */
export async function moveLogicDataSet( dataSetID , treeNodeId ) {
    return await reqUtil.get({
        url: root +  'moveLogicDataSet'
    } , {dataSetID , treeNodeId});
}

/*
*  极速表
 * @param param
 * @returns {Promise<*>}
* */
export async function changeIsFast( dataSetId , ifFase ) {
    return await reqUtil.get({
        url: root +  'changeIsFast'
    } , {dataSetId , ifFase});
}

/*
*  左右关联 ，条件过滤回显
 * @param param type:join 左右关联,filter 过滤
 * @returns {Promise<*>}
* */
export async function getJoinAndFilter( dataSetId , type ) {
    return await reqUtil.get({
        url: root1 +  'getJoinAndFilter'
    } , {dataSetId , type});
}
/*
*  校验是否被可视化使用
 * @param param type:join 左右关联,filter 过滤
 * @returns {Promise<*>}
* */
export async function checkDataSetUserInVisual( id ) {
    return await reqUtil.get({
        url: root +  'checkDataSetUserInVisual'
    } , {id});
}

/*
*  快速建模 字段保存
 * @param param
 * @returns {Promise<*>}
* */
export async function columnsSave( columns, transId, dataObjId, customDataSet, fetchSize, settings) {
    return await reqUtil.post({
        url:  '/cicada/plugin/sql/save/saveFilterColumn?transStepId=' + transId +"&dataObjId=" + dataObjId 
                + "&customDataSet=" + customDataSet + "&fetchSize=" + fetchSize,
        settings
    } , columns);
}

/**
 * 快速建模 回显
 * @param {*} columns 
 * @param {*} transId 
 * @param {*} dataObjId 
 * @param {*} customDataSet 
 * @param {*} fetchSize 
 */
export async function columnsInit( transId, dataObjId, customDataSet, logicDataSetId, settings) {
    return await reqUtil.post({
        url:  '/cicada/plugin/sql/showModelColumns?transStepId=' + transId +"&dataObjId=" + dataObjId 
                + "&customDataSet=" + customDataSet + "&logicDataSetId=" + logicDataSetId,
        settings
    } );
}

