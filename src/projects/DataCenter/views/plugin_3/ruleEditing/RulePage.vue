<template>
    <div class="ce-func_page">
        <div class="mb10" v-if="isGlobal">
            <span class="ce-is-required pr5">{{ globalTip }}</span>
            <el-input class="ce-variable-name" v-model="funName"/>
        </div>
        <div class="ce-rule_func" :data-plugin="isGlobal">
            <div class="ce-func_tree">
                <div class="ce-func_title">
                    <ce-tab-page v-if="!isGlobal" class="ce-func_tab" @tab-click="tabClick" v-model="tabVal"
                                 :tabPanes="pageOpt"/>
                    <span v-else>{{ funcTitle }}</span>
                </div>

                <div class="ce-func-cont" v-show="tabVal === 'func'">
                    <rule-tree ref="ruleTree"
                               @setAllUdf="setAllUdf"
                               @setDescription="treeForDescription"
                               @inputExpress="inputExpress"
                               :isGlobal="isGlobal"
                               :beforeChange="udfBeforeChange"
                               @dblInputExpress="dblInputExpress"
                               @inputFinish="inputFinish"/>
                </div>
                <div class="ce-func_com" v-show="tabVal === 'common'">
                    <div class="ce-func_search">
                        <el-input placeholder="请输入名称搜索" v-model="filterVal" size="small" class="ce-search">
                            <i slot="suffix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                    </div>
                    <ul class="ce-func_list" v-if="commonList.length">
                        <li class="ce-func_item" v-for="(li, inx) in commonList" :key="inx" @click="commonFn(li, inx)"
                            :class="{ 'ce-func_active': inx === activeInx }">{{ li.label }}
                        </li>
                    </ul>
                    <empty class="ce-empty" v-else/>
                </div>
            </div>
            <!-- -->
            <div class="ce-func_edit">
                <div class="ce-edit_cont">
                    <div class="ce-collapse ce-collapse_top" :style="{ 'flex': showFunc ? 1 : 0 }" v-if="!isGlobal">
                        <div class="ce-func_title ce-collapse_title" @click="showCollapse('showFunc')">
                            <span>{{ func_desc }}</span>
                            <i :class="{ 'el-icon-arrow-up': showFunc, 'el-icon-arrow-down': !showFunc }"></i>
                        </div>
                        <div class="ce-collapse_item" v-show="showFunc">
                            <udf-description :desc="desc" :is-standard="isStandard" key="standard"
                                             v-show="tabVal === 'func'"/>
                            <udf-description :desc="comDesc" key="common" v-show="tabVal === 'common'"/>
                        </div>
                    </div>

                    <div class="ce-func_title" :border-top="isGlobal ? false : showFunc">
                        <div class="ce-func_text">
                            {{ isStandard || isFunctionalOrCommon('common') ? paramTitle : editTitle }}
                        </div>
                        <dg-radio-group v-model="typeVal" @change="inputTypeChange" :data="typeOpt" call-off
                                        v-if="!isGlobal && isFunctionalOrCommon('func')"/>
                        <!--<span>{{ editCode }}</span>-->
                        <el-popover v-if="!isStandard && isFunctionalOrCommon('func')" class="ce-field-popover"
                                    placement="bottom" ref="popover"
                                    width="280"
                                    trigger="click">
                            <div class="conds__pop">
                                <el-input v-model="filterText" suffix-icon="el-icon-search"
                                          placeholder="搜索"></el-input>
                                <div class="conds__checkboxs">
                                    <dg-tree :data="columns" ref="tree" node-key="id" highlight-current
                                             :props="defaultProps" :expand-on-click-node="true"
                                             :filter-node-method="filterNode"
                                             @node-click="checkField">
                                    <span class="custom-tree-node ce-rule_field" slot-scope="{ node, data }">
                                        <span class="node-label el-tree-node__label" :title="data.label">{{
                                                data.label
                                            }}</span>
                                        <span class="el-tree-node__label ce-field_type">{{ data.type }}</span>
                                    </span>
                                    </dg-tree>
                                </div>
                            </div>
                            <el-button slot="reference"
                                       type="text"
                                       icon="el-icon-circle-plus"
                                       v-if="!isGlobal">{{ field }}
                            </el-button>
                        </el-popover>
                    </div>
                    <template v-if="isStandard || isFunctionalOrCommon('common')">
                        <div class="ce-editor ce-params-panel">
                            <standard-page v-model="paramsData" ref="paramsForm" key="params"
                                           v-if="isFunctionalOrCommon('func')"/>
                            <standard-page v-model="formList" ref="commonForm" key="common"
                                           v-if="isFunctionalOrCommon('common')"/>
                        </div>
                        <div class="ce-collapse" :style="{ 'flex': showDebug ? 1 : 0 }" v-if="!isGlobal">
                            <div class="ce-collapse_title" @click="showCollapse('showDebug')">
                                <span>{{ express_tip }}</span>
                                <i :class="{ 'el-icon-arrow-up': !showDebug, 'el-icon-arrow-down': showDebug }"></i>
                            </div>
                            <div class="ce-collapse_item pl10" v-show="showDebug">
                                <div class="ce-collapse_debug">
                                    <div class="b">{{ result }}</div>
                                    <span v-if="debugResult">{{ debug_label }}</span>
                                    <span>{{ debugResult }}</span>
                                </div>
                                <div class="ce-collapse_btns">
                                    <div class="l" v-if="showRe"><span>{{ result_t }}</span>{{ debug_result }}</div>
                                    <el-button type="primary" size="mini" @click="getStandardDebugResult">{{
                                            debug_run
                                        }}
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="ce-editor" @drop="drop">
                            <code-editor ref="editor" @lastCursorPos="lastCursorPos" :input-columns="inputColumns"
                                         v-model="funcCode" @change="codeChange" language="x-sql"/>
                        </div>
                        <div class="ce-collapse" :style="{ 'flex': showDebug ? 1 : 0 }" v-if="!isGlobal">
                            <div class="ce-collapse_title" @click="showDebugPanel">
                                <span>{{ debug_tip }}</span>
                                <i :class="{ 'el-icon-arrow-up': !showDebug, 'el-icon-arrow-down': showDebug }"></i>
                            </div>
                            <div class="ce-collapse_item" v-show="showDebug">
                                <div class="ce-collapse_debug">
                                    <dg-scrollbar>
                                        <el-form inline>
                                            <el-form-item v-for="(item, key) in debug_data" :key="key"
                                                          :label="key + ':'">
                                                <el-input v-model="debug_data[key]"></el-input>
                                            </el-form-item>
                                        </el-form>
                                    </dg-scrollbar>
                                </div>
                                <div class="ce-collapse_btns">
                                    <div class="l" v-if="showRe"><span>{{ result_t }}</span>{{ debug_result }}</div>
                                    <!--<el-button type="text" size="mini">{{ debug_help }}</el-button>-->
                                    <!--<el-button type="primary" size="mini" @click="getDebugField">{{
                                            debug_get
                                        }}
                                    </el-button>-->
                                    <el-button type="primary" size="mini" @click="getDebugResult">{{
                                            debug_run
                                        }}
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <span v-footer v-if="!rowData.isEditParam && !isGlobal">
            <el-button @click="close">{{ btnCancelTxt }}</el-button>
            <el-button @click="validate" type="primary">{{ btnCheckTxt }}</el-button>
        </span>
    </div>
</template>

<script>
import RuleTree from "@/projects/DataCenter/views/plugin_3/ruleEditing/RuleTree";
import codeEditor from "@/components/common/codeEditor/codeEditor";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {treeMethods} from "@/api/treeNameCheck/treeName";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import CeTabPage from "dc-front-plugin/src/components/CeTabPage/CeTabPage.vue";
import standardPage from "@/projects/DataCenter/views/plugin_3/ruleEditing/standard-page.vue";
import udfDescription from "@/projects/DataCenter/views/plugin_3/ruleEditing/udf-description.vue";
import {debounce} from "lodash";

export default {
    name: "RulePage",
    mixins: [commonMixins, servicesMixins],
    props: {
        inputColumns: Array,
        isGlobal: {
            type: Boolean,
            default: false,
        },
        rowData: Object,
    },
    components: {
        RuleTree,
        codeEditor,
        CeTabPage,
        standardPage,
        udfDescription,
    },
    computed: {
        columns() {
            return this.inputColumns;
        },
        // 标准输入
        isStandard() {
            return this.typeVal === "standard";
        },
        // 是函数 或 常用方法
        isFunctionalOrCommon() {
            return (val = 'func') => this.tabVal === val;
        },
        // 表达式 拼 参数 结果
        result() {
            let res = this.isFunctionalOrCommon('func') ? this.usedOperatorForS.expression : this.usedOperator.expression;
            const data = this.isFunctionalOrCommon('func') ? this.paramsData : this.formList;
            for (let item of data) {
                if (item.value === '') continue;
                const value = item.source === 'custom' && ['string', 'text', 'varchar'].includes(item.dataType.toLowerCase()) ? `"${item.value}"` : item.value;
                res = res.replaceAll(`:${item.code}`, value);
            }
            return res;
        },
        // 表达式 拼 调试参数 结果
        debugResult() {
            let res = this.isFunctionalOrCommon('func') ? this.usedOperatorForS.expression : this.usedOperator.expression;
            const data = this.isFunctionalOrCommon('func') ? this.paramsData : this.formList;
            for (let item of data) {
                if (item.debugValue === '' || item.debugValue === undefined) continue;
                const value = ['string', 'text', 'varchar'].includes(item.dataType.toLowerCase()) ? `"${item.debugValue}"` : item.debugValue;
                res = res.replaceAll(`:${item.code}`, value);
            }
            return res;
        }
    },
    provide() {
        return {
            'isCustom': () => !this.isStandard,
            'prevColumns': () => this.inputColumns,
            'timeFuncOption': () => [
                {
                    type: 'Timestamp',
                    label: "当前时间戳",
                    value: "current_timestamp()",
                    columnCode: "current_timestamp()",
                }
            ]
        }
    },
    data() {
        return {
            defaultProps: treeMethods.data().defaultProps,
            globalTip: "名称 :",
            funName: "",
            tabTip: "选择配置类型 :",
            tabVal: "func",
            pageOpt: [
                {label: "函数", value: "func"},
                {label: "常用", value: "common"},
            ],
            funcTitle: "函数",
            editTitle: "编辑表达式",
            editCode: "+ - * / ( )",
            field: "字段",
            func_tip: "函数释义",
            debug_tip: "调试 ( Ctrl:函数显示提示;  Alt:入参显示提示; )",
            debug_label: "调试表达式: ",
            showFunc: false,
            showDebug: false,
            funcCode: "",
            desc: {},
            commonPlugins: [],
            commonList: [],
            activeInx: 0,
            filterText: "",
            filterVal: "",
            checkList: [],
            fields: [],
            debug_get: "获取字段",
            debug_run: "调试表达式",
            debug_help: "帮助文档",
            storeExp: "",
            canDrop: false,
            cursorToken: null,
            rowIndex: "",
            debug_data: {},
            debug_result: "",
            showRe: false,
            result_t: "输出 : ",
            formLay: {},
            formList: [],
            tip: '',
            rules: {},
            usedOperator: {},
            curRow: {},
            comDesc: {},
            isExit: true,
            // 函数定义
            func_desc: '定义',
            showDesc: true,
            typeOpt: [
                {label: '标准输入', value: 'standard'},
                {label: '自定义输入', value: 'custom'},
            ],
            typeVal: 'standard',
            paramTitle: '参数',
            paramsData: [],
            allUdf: [], // 全部算子
            sourceOpt: [
                {label: '自定义', value: 'custom'},
                {label: '字段', value: 'field'},
            ], // 值来源类型
            express_tip: "表达式结果",
            checkConditionColumn: [{required: true, message: '请设置参数', trigger: ['blur', 'change']}],
            usedOperatorForS: {}, // 标准输入 算子数据
        }
    },
    watch: {
        filterText: treeMethods.watch.filterText,
        filterVal(val) {
            this.filterCommon(val);
        },
    },
    methods: {
        /**
         * 算子 切换之前
         */
        async udfBeforeChange(dataKey = 'paramsData', tip) {
            const hasEdit = this.checkParamsHasValue(this[dataKey]);
            let res = true;
            if (hasEdit) {
                res = await this.confirmChangeUdf(tip);
            }
            return res;
        },
        /**
         * 自定义算子 输入改变
         */
        codeChange: debounce(function () {
            if (this.showDebug && this.funcCode) this.getDebugField();
            else this.debug_data = {}
        }, 500),
        async inputTypeChange() {
            this.clearDebugResult();
            if (this.isStandard) {
                if(this.desc.id) this.$refs.ruleTree?.udfClick({...this.desc, version: true});
            } else {
                let res = await this.udfBeforeChange('paramsData', '已设置算子参数值，未保存，切换到 "自定义输入" 将清空参数值，是否继续？');
                if (res) {
                    this.paramsData = [];
                } else {
                    this.typeVal = 'standard';
                }

            }
        },
        showDebugPanel() {
            this.showCollapse('showDebug');
            this.codeChange();
        },
        tabClick({name}) {
            if (name === 'common') {
                this.$refs.ruleTree.filterText = "";
            } else if (name === 'func') {
                this.filterVal = "";
            }
            this.clearDebugResult();
        },
        close() {
            this.$emit("close");
        },
        filterCommon(val = "") {
            const vm = this;
            vm.commonList = vm.commonPlugins.filter(plug => {
                if (val) {
                    return plug.label.includes(val);
                } else {
                    return true;
                }
            })
        },
        // 解析表达式参数
        expressionParser(code = '') {
            let regex = /(?<=\()(.+?)(?=\))/g;
            let result = code.match(regex);
            return result && result.length ? result[0].split(',') : [];
        },
        // 清空 debug 结果
        clearDebugResult() {
            this.showRe = false;
            this.debug_result = "";
        },
        /**
         * 获取调试字段参数
         * */
        async getDebugField() {
            const vm = this, {services, funcCode} = vm;
            vm.debug_data = {};
            vm.showRe = false;
            if (!funcCode.trim()) {
                vm.$message.warning("表达式为空");
                return;
            }
            const {data} = await services.expressionParser(funcCode);
            if (data.status === 0) {
                let result = data.data;
                if (!result.length) vm.$message.warning("暂无字段");
                for (let item of result) {
                    vm.$set(vm.debug_data, item, "");
                }
            }
        },
        /**
         * 调试
         * */
        async getDebugResult() {
            const vm = this, {funcCode, debug_data} = vm;
            let sql = funcCode, canRun = true;
            if (!sql.trim()) {
                vm.$message.warning("表达式为空");
                return;
            }
            if (sql.includes(':') && Object.keys(debug_data).length === 0) {
                vm.$message.warning("请先获取字段");
                return;
            }

            for (let key in debug_data) {
                let val = debug_data[key].replace(/[\\"']/g, "");
                if (val === "") canRun = false;
                sql = sql.replaceAll(':' + key, `'${val}'`);
            }
            if (!canRun) {
                vm.$message.warning("字段不能为空");
                return;
            }
            await vm.getExpressionDebug(sql);
        },
        getStandardDebugResult() {
            const vm = this;
            const data = this.isFunctionalOrCommon('func') ? this.paramsData : this.formList;
            for (let row of data) {
                if (row.debugValue === "" || row.debugValue === undefined) return vm.$message.warning(`请输入'${row.name}(${row.code})'调试值`);
            }
            vm.getExpressionDebug(vm.debugResult);
        },
        async getExpressionDebug(sql) {
            const vm = this, {services} = vm;
            vm.showRe = false;
            const {data} = await services.expressionDebug(sql);
            if (data.status === 0) {
                let result = JSON.parse(data.data);
                vm.showRe = true;
                vm.debug_result = result.data.length && result.data[0].col;
            } else {
                vm.showError(data)
            }
        },
        showError(res) {
            if (!this.isExit) return;
            const head = res.msg || "";
            let content = res.detailErrorMsg || res.msg;
            this.$dgLayer({
                title: "错误详情",
                type: 1,
                move: false,
                content: `
                <div class="cred f16 b">${head}</div>
                <div>${content}</div>
                `,
                maxmin: false,
                noneBtnField: true,
                area: ['1200px', '500px']
            })
        },
        /**
         * 初始化
         * @param row
         * @param index
         * */
        async init(row, index) {
            const vm = this;
            if (this.isGlobal) this.typeVal = 'custom'; // 全局参数 默认为自定义输入表达式
            if (row.ifCustomInput !== undefined && row.ifCustomInput !== null) this.typeVal = row.ifCustomInput === 'true' ? 'custom' : 'standard';
            else this.typeVal = 'custom';
            await this.$nextTick();
            this.rowIndex = index;
            if (row.isCommonUseOperator) {
                this.tabVal = 'common';
                this.curRow = row;
            } else {
                vm.treeOutput({
                    standard: () => {
                        vm.backStandardParams(row);
                    },
                    custom: () => {
                        vm.tabVal = 'func';
                        vm.funName = row.varValue || "";
                        vm.funcCode = row.expressionScript;
                        setTimeout(()=>{
                            vm.inputCode(vm.funcCode);
                        },300)
                    }
                })

            }
            this.getCommonUseOperator();
        },
        async backStandardParams(row) {
            if (!row.usedOperatorId) return this.$refs.ruleTree?.setDefKey();
            const operator = await this.getUdfOperator(row.usedOperatorId);
            if (!operator) return;
            this.$refs.ruleTree?.setCurrentKey(row.usedOperatorId);
            this.usedOperatorForS = operator;
            this.setDescription(operator);
            this.paramsData = operator.udfParameterVos.map(u => {
                let params = JSON.parse(row.commonUseOperatorParams);
                let par = params.find(p => p.paramCode === u.code);
                return {
                    ...u,
                    value: par?.paramValue || "",
                    source: par?.source || "custom",
                    debugValue: par?.debugValue || "",
                }
            });
        },
        /**
         * 获取输入最后的位置
         * */
        lastCursorPos(token) {
            this.cursorToken = token;
        },
        /**
         * 过滤树方法
         * */
        filterNode: treeMethods.methods.filterNode,
        /**
         * 设置全部的算子表达式
         * @param data
         * */
        setAllUdf(data) {
            this.allUdf = data;
        },
        setEditorUdf(data) {
            this.$refs.editor.setAllUdf(data);
        },
        /**
         * 输入结束
         * */
        inputFinish() {
            this.canDrop = false;
        },
        /**
         * 双击输入表达式
         * @param data
         * */
        dblInputExpress(data) {
            const {expression} = data;
            const vm = this;
            vm.treeOutput({
                standard: () => {

                },
                custom: async () => {
                    await vm.inputCode(expression);
                    vm.getFirstParamsSelection(expression);
                }
            })
        },
        /**
         * 表达式第一个参数
         * */
        firstParamsStr(exp) {
            let first = -1, str = "";
            if (exp.indexOf(':a') > -1) {
                first = exp.indexOf(':a');
                str = ":a";
            } else if (exp.indexOf(':url') > -1) {
                first = exp.indexOf(':url');
                str = ":url";
            } else if (exp.indexOf(':str') > -1) {
                first = exp.indexOf(':str');
                str = ":str";
            } else if (exp.indexOf(':k1') > -1) {
                first = exp.indexOf(':k1');
                str = ":k1";
            } else if (exp.indexOf(':val') > -1) {
                first = exp.indexOf(':val');
                str = ":val";
            } else if (exp.indexOf(':separator') > -1) {
                first = exp.indexOf(':separator');
                str = ":separator";
            } else if (exp.indexOf(':h') > -1) {
                first = exp.indexOf(':h');
                str = ":h";
            } else if (exp.indexOf(':lat') > -1) {
                first = exp.indexOf(':lat');
                str = ":lat";
            } else if (exp.indexOf(':partitionFieldA') > -1) {
                first = exp.indexOf(':partitionFieldA');
                str = ":partitionFieldA";
            } else if (exp.indexOf(':partitionField') > -1) {
                first = exp.indexOf(':partitionField');
                str = ":partitionField";
            } else if (exp.indexOf(':timestamp0') > -1) {
                first = exp.indexOf(':timestamp0');
                str = ":timestamp0";
            } else if (exp.indexOf(':t') > -1) {
                first = exp.indexOf(':t');
                str = ":t";
            } else if (exp.indexOf(':inputStartDate') > -1) {
                first = exp.indexOf(':inputStartDate');
                str = ":inputStartDate";
            } else if (exp.indexOf(':date') > -1) {
                first = exp.indexOf(':date');
                str = ":date";
            }
            return {str, first};
        },
        /**
         * 获取第一个参数的位置 并选中
         * 第一个参数 :a
         * */
        getFirstParamsSelection(exp) {
            const vm = this;
            let cursorToken = vm.$refs.editor.getCursor();
            let {first, str} = vm.firstParamsStr(exp);
            let curP = cursorToken.ch - exp.length + first;
            if (first > -1) {
                let start = {line: cursorToken.line, ch: curP},
                    end = {line: cursorToken.line, ch: curP + str.length};
                this.$refs.editor.setSelection(start, end);
            } else {
                this.$refs.editor.setSelection(cursorToken, cursorToken);
            }
        },
        // 不同输入类型，算子树事件的输出判断
        treeOutput(state = {}) {
            const funState = Object.assign({
                standard: () => {
                },
                custom: () => {
                }
            }, state);
            const {typeVal} = this;
            return funState[typeVal] && funState[typeVal]();
        },
        /**
         * 表达式输入
         * @param expression
         * */
        inputExpress({expression}) {
            const vm = this;
            this.treeOutput({
                custom: () => {
                    vm.storeExp = expression;
                    vm.canDrop = true;
                }
            })

        },
        /**
         * 调试 ，函数释义切换
         * @param curKey
         * @param otherKey
         */
        showCollapse(curKey, otherKey) {
            this[curKey] = !this[curKey];
            if (this[curKey]) this[otherKey] = false;
        },
        /**
         * 常用算子点击显示对应内容表单
         * @param data
         * @param inx
         * */
        async commonFn(data, inx) {
            let res = await this.udfBeforeChange('formList');
            if (res) {
                this.activeInx = inx;
                this.getCommonUdfPage(data, true);
            }
        },
        /**
         * 输入内容
         * */
        async inputCode(code) {
            const vm = this;
            vm.treeOutput({
                standard: () => {

                },
                custom: async () => {
                    let {cursorToken} = this;
                    if (!cursorToken) {
                        cursorToken = vm.$refs.editor?.getCursor();
                        vm.lastCursorPos(cursorToken);
                    }
                    if (code) {
                        let selections = vm.$refs.editor.coder.getSelection();
                        if (selections) {
                            await vm.$refs.editor.coder.replaceSelection(code, 'around');
                        } else {
                            await vm.$refs.editor.setValByPos(cursorToken, code);
                        }
                    }
                    vm.$refs.editor.coder.focus();
                }
            })
        },
        /**
         * 选择字段输入
         */
        checkField(node) {
            const vm = this;
            let code = node && node.columnCode || "";
            vm.inputCode(code);
            this.$refs.popover.doClose();
        },
        /**
         * 校验
         */
        validate() {
            const vm = this, {rowIndex, usedOperator, paramsData} = vm;
            let params = {};
            if (this.tabVal === 'common') {
                vm.$refs.commonForm?.validate(valid => {
                    if (valid) {
                        params.isCommonUseOperator = true;
                        params.usedOperatorId = usedOperator.id;
                        params.expressionScript = usedOperator.expression;
                        params.ifCustomInput = 'false';
                        let par = vm.formList.map(udf => {
                            return {
                                paramId: udf.udfId,
                                paramName: udf.name,
                                paramCode: udf.code,
                                paramValue: udf.value,
                                source: udf.source,
                                debugValue: udf.debugValue,
                                paramValueType: udf.dataType,
                            };
                        });
                        params.commonUseOperatorParams = JSON.stringify(par);
                        vm.$emit("setRowList", params, rowIndex, usedOperator);
                        vm.close();
                    }
                });
            } else {
                if (this.isGlobal && this.funName === '') {
                    this.$message("请输入名称");
                    return;
                }
                params.name = this.funName;
                params.isCommonUseOperator = false;
                vm.treeOutput({
                    standard: () => {
                        vm.$refs.paramsForm.validate(valid => {
                            if (valid) {
                                params.expressionScript = vm.usedOperatorForS.expression;
                                params.usedOperatorId = vm.usedOperatorForS.id;
                                let par = paramsData.map(udf => {
                                    return {
                                        paramId: udf.udfId,
                                        paramName: udf.name,
                                        paramCode: udf.code,
                                        paramValue: udf.value,
                                        source: udf.source,
                                        debugValue: udf.debugValue,
                                        paramValueType: udf.dataType,
                                    };
                                });
                                params.ifCustomInput = 'false'; // 界面化参数 false
                                params.commonUseOperatorParams = JSON.stringify(par);
                                vm.$emit("setRowList", params, rowIndex,vm.usedOperatorForS);
                                vm.close();
                            }
                        })
                    },
                    custom: () => {
                        params.ifCustomInput = 'true';
                        params.expressionScript = vm.funcCode;
                        vm.$emit("setRowList", params, rowIndex);
                        vm.close();
                    }
                })


            }
        },
        /**
         * 拖拽算子放置
         * */
        drop(e) {
            const vm = this, {storeExp, canDrop} = vm;
            if (!canDrop) return;
            vm.dblInputExpress({expression: storeExp});
        },

        /**
         * 设置描述
         */
        setDescription(data) {
            if (data.id) {
                this.showFunc = true;
                this.showDebug = false;
                this.desc = {
                    id: data.id,
                    tip: data.classifyName,
                    msg: data.memo,
                    exampleCont: data.example,
                    params: data.udfParameterVos,
                    returnType: data.returnType,
                    name: data.name,
                    expression: data.expression,
                }
            } else {
                this.showFunc = false;
                this.desc = data;
            }
        },
        /**
         * 算子树单击
         * @param data
         */
        treeForDescription(data) {
            const vm = this;
            vm.treeOutput({
                standard: async () => {
                    const {udfParameterVos} = data;
                    this.usedOperatorForS = data;
                    this.paramsData = (udfParameterVos || []).map(u => {
                        return {
                            ...u,
                            source: "custom",
                            value: "",
                            debugValue: "",
                        }
                    });
                    this.setDescription(data);
                    this.$refs.paramsForm.resetFields();
                },
                custom: () => {
                    vm.setDescription(data);
                }
            })
        },
        /**
         * 检查 参数 数据是否已经填写数据
         * @param data
         */
        checkParamsHasValue(data = []) {
            for (let row of data) {
                if (row?.value) return true;
            }
            return false;
        },
        /**
         * 确认切换 udf
         */
        confirmChangeUdf(tip = '已设置算子参数值，未保存，切换算子将清空参数值，是否继续？', title = '提示', type = 'warning') {
            const vm = this;
            return new Promise((resolve) => {
                vm.$dgConfirm(tip, title, {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    closeOnClickModal: false,
                    type: type
                }).then(() => {
                    resolve(true);
                }).catch(() => {
                    resolve()
                });
            })

        },
        /**
         * 获取常用算子
         */
        getCommonUseOperator() {
            const vm = this, {services, curRow} = this;
            vm.commonPlugins = [];
            services.queryUdfModel(process.env.VUE_APP_SECRET, 'commonUseOperator').then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.commonPlugins = result.map((item, inx) => {
                        if (curRow.usedOperatorId === item.id) vm.activeInx = inx;
                        item.label = item.name;
                        return item;
                    })
                    if (result && result.length) {
                        vm.getCommonUdfPage(result[vm.activeInx]);
                        vm.filterCommon();
                    }
                }
            })

        },
        /**
         * 常用算子页面
         */
        getCommonUdfPage(data, isReset) {
            const vm = this;
            vm.getUdfOperator(data.id).then(res => {
                if (res) {
                    vm.usedOperator = res;
                    vm.setComDesc(vm.usedOperator);
                    vm.setOperatorPage(vm.usedOperator, isReset);
                }
            })
        },
        getUdfOperator(id) {
            const vm = this, {services} = vm;
            return services.getUdfOperatorById(id).then(res => {
                if (res.data.status === 0) return res.data.data;
            })
        },
        /**
         * 设置常用说明
         */
        setComDesc(data) {
            if (data.id) {
                this.comDesc = {
                    tip: data.classifyName,
                    msg: data.memo,
                    exampleCont: data.example,
                    params: data.udfParameterVos,
                    returnType: data.returnType,
                    name: data.name,
                    expression: data.expression,
                }
            } else {
                this.comDesc = data;
            }
        },
        /**
         * 添加当前时间戳选项
         * @param type
         * @param option
         */
        addCurTimeOpt(type, option) {
            if (type === 'Date' || type === 'Timestamp') {
                option.push({
                    label: "当前时间戳",
                    value: "current_timestamp()"
                })
            }
        },
        /**
         * 配置常用算子 展示表单内容，  验证规则 ，参数
         * @param data
         * @param isReset
         */
        setOperatorPage(data, isReset = false) {
            const vm = this, {curRow} = vm;
            // vm.tip = data.memo;
            vm.formList = [];
            if (data.udfParameterVos.length) {
                vm.formList = data.udfParameterVos.map(u => {
                    let params = curRow.commonUseOperatorParams ? JSON.parse(curRow.commonUseOperatorParams) : [];
                    let par = isReset ? null : params.find(p => p.paramCode === u.code);
                    return {
                        ...u,
                        source: par?.source || "custom",
                        value: par?.paramValue || "",
                        debugValue: par?.debugValue || "",
                    }
                });
                vm.$refs.commonForm?.resetFields();
            }
            vm.showCommon = true;
        }
    },
    beforeDestroy() {
        this.isExit = false;
    }
}
</script>

<style scoped lang="less">
.ce-empty {
    height: 60px;
}

.ce-rule_func {
    border: 1px solid #D9D9D9;
    border-radius: 2px;
    height: 100%;
    display: flex;
    overflow: hidden;

    &[data-plugin=true] {
        height: calc(100% - 10px - 2rem);
    }
}

@titleHeight: 40px;
@treeWidth: 240px;
.ce-func {
    &_page {
        height: 100%;
    }

    &_search {
        padding: 10px 12px 8px;
    }

    &_list {
        height: calc(100% - 18px - 2rem);
        overflow: auto;
    }

    &_item {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 36px;
        padding: 0 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-left: 4px solid rgba(0, 0, 0, 0);
        border-radius: 2px 0 0 2px;
        cursor: pointer;
    }

    &_active {
        border-color: #0088FF;
        background: #fff;
        color: #0088FF;
    }

    &_tree {
        width: @treeWidth;
        height: 100%;
        border-right: 1px solid rgba(0, 0, 0, 0.12);
    }

    &_title {
        background: rgba(0, 0, 0, 0.02);
        height: @titleHeight;
        min-height: @titleHeight;
        flex-basis: 0;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0 14px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
        box-sizing: border-box;

        &[border-top=true] {
            border-top: 1px solid rgba(0, 0, 0, 0.12);
        }
    }

    &-cont {
        padding: 10px 12px;
        height: calc(100% - 20px - @titleHeight);
    }

    &_edit {
        flex: 1;
        max-width: calc(100% - @treeWidth);
    }

    &_tab {
        &.ce-tab-page {
            height: 100%;
            padding: 0;
            margin: auto;

            /deep/ .el-tabs__nav-wrap::after {
                height: 0;
            }

            /deep/ .el-tabs__header {
                margin: 0;
            }

            /deep/ .el-tabs__nav-wrap:first-child {
                padding: 0;
            }
        }

    }

    &_text {
        width: 120px;
    }
}


.ce-variable-name {
    width: 200px;
}


.ce-func_com {
    height: calc(100% - @titleHeight);
    background: rgba(0, 0, 0, 0.02);
}


.ce-field-popover {
    margin-left: auto;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    letter-spacing: 10px;
    font-weight: normal;
}

@editorH: 34%;
.ce-editor {
    min-height: calc(@editorH - @titleHeight);
    flex: 1;
    transition: 500ms;
    position: relative;
    z-index: 10;
}

.ce-params-panel {
    //min-height: 50%;
}

.ce-standard {
    //flex:1;
    //display: flex;

}

/deep/ .el-table tr:last-child td {
    border-bottom: 1px solid rgba(0, 0, 0, .15);
}

/deep/ .el-table::before {
    height: 0;
}

.ce-edit_cont {
    display: flex;
    flex-direction: column;
    align-content: stretch;
    height: 100%;
}

@collapseTitle: 36px;
.ce-collapse {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    transition: 500ms;
    min-height: calc(@collapseTitle);
    //max-height: calc( @editorH - @titleHeight - @collapseTitle);

    &_top {
        border: none;
        min-height: calc(@titleHeight);
    }

    &_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 36px;
        padding: 0 12px;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.85);
    }

    &_detail {
        height: calc(30% - 40px);
    }

    &_item {
        height: calc(100% - @titleHeight);
        overflow: hidden;
    }

    &_debug {
        height: calc(100% - 1.75rem);
        padding-left: 18px;
        overflow: auto;
        word-wrap: break-word;
    }

    &_btns {
        padding: 0 14px;
        text-align: right;
    }
}

.ce-common_dec {
    padding: 0 10px;
}


.conds {
    min-height: 100%;
    display: flex;
    flex-direction: column;

    /deep/ .el-card__header {
        padding: 0 !important;
    }

    .comm__header {
        padding: 1rem 1.75rem;
    }

    &__nodata {
        padding: 20px 0;
    }

    &__checkall {
        padding: 8px 0;
    }

    &__checkboxs {
        padding: 1rem 0;
        max-height: 200px;
        overflow: auto;

        /deep/ .el-checkbox {
            display: block;
            margin-bottom: 1.125rem;
        }
    }

    &__btns {
        border-top: 0.0625rem solid #ccc;
        padding-top: 0.875rem;
        text-align: right;
    }

    &__conds {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        flex-wrap: wrap;

        &-item {
            display: flex;
            align-items: center;
            margin-right: 1.25rem;
            margin-bottom: 1.25rem;

            span {
                margin-right: 0.4375rem;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.65);
            }
        }
    }

    &__wrap {
        &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        &-actions {
            display: flex;
            align-items: center;
        }

        &-reflash {
            text-align: center;
            //line-height: 1.875rem;
            //width: 1.875rem;
            //height: 1.875rem;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.125rem;
            margin-left: 0.25rem;
            cursor: pointer;

            i {
                color: rgba(0, 0, 0, 0.45);
            }

            &:hover {
                i {
                    color: #0088ff;
                }
            }
        }

        &-list {
            padding-top: 0.875rem;
        }
    }

    &__item {
        display: flex;
    }

    &__fs {
        width: 100%;

        &-header {
            width: 100%;
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            color: #0088ff;
            justify-content: space-between;
        }

        &-content {
            padding: 0.875rem 0;
        }
    }
}

.ce-rule_field {
    display: flex;
    justify-content: space-between;
    color: #333;
}

.ce-field_type {
    justify-content: flex-end;
    color: #777;
    max-width: 90px;
}

.node-label {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}
</style>
