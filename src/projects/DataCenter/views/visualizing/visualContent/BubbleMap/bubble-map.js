import 'v-charts/lib/style.css'
import {globalBus} from "@/api/globalBus";
import {chartMxins} from "../chart-mixins/chart-mixins";
import {mapMixins} from "../chart-mixins/map-mixins";

export default {
    name: "BubbleMap",
    mixins: [chartMxins, mapMixins],
    data() {
        return {
            chartSettings: {
                aspectScale: 1,
                roam: true,
                zoom: 1.1,
                scaleLimit: {min: 1, max: 6},
                positionJsonLink: '',
                mapOrigin: "",
                mapGrid: {
                    left: 'center',
                    top: '50px',
                },
                labelMap: {}
            },
            extend: {
                title: {
                    show: true,
                    text: "2212"
                },
                legend: {
                    top: 26,
                    left: 'center'
                },
                visualMap: {},
                tooltip: {},
                series: [],
            },
            code: '',
            chartEvents: {
                click: this.chartClickFn,
                legendselectchanged: this.legendSelectChange
            },
            bubble_min: 0,
            bubble_max: 0
        }
    },
    methods: {
        legendSelectChange(params) {
            // this.extend.legend.selected = params.selected;
        },
        setSymbolSize(val) {
            const vm = this;
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);
            let size_data = 0, res;
            val.forEach((it, i) => {
                if (i > 1 && i < nodeData.xAxis.length + 2) {
                    size_data += it;
                }
            });
            res = size_data === 0 ? 0 : ((size_data / vm.bubble_max) * 35 + 5).toFixed(2);
            return res;
        },
        getMapGrid(nodeData) {
            let {legend} = nodeData.style;
            let top = legend.top_type === "abs" ? legend.top : legend.top + '%',
                bottom = legend.bottom_type === "abs" ? legend.bottom : legend.bottom + '%',
                left = legend.left_type === "abs" ? legend.left : legend.left + '%',
                right = legend.right_type === "abs" ? legend.right : legend.right + '%';
            return {top, bottom, left, right};
        },
        mapSetting(map, hasData, nodeData) {
            const vm = this;
            let mapN = "map" + vm.id;
            vm.$echarts.registerMap(mapN, map);
            let p = vm.$echarts.getMap(mapN).geoJson.features;
            let mapData = [], allMapDatas = {};
            let {top, bottom, left, right} = vm.getMapGrid(nodeData);
            vm.chartSettings.mapOrigin = map;
            // vm.chartSettings.position = "province";
            vm.extend.series = [];
            let columns = vm.chartData.columns.slice(1, nodeData.xAxis.length + 1);
            let min = 0, max = 0;
            vm.chartData.rows.forEach(row => {
                p.forEach(item => {
                    if (row['位置'] === item.properties.name) {
                        let measureVals = [];
                        let addD = 0;
                        for (let k in row) {
                            if (k !== '位置') {
                                if (!allMapDatas[k]) allMapDatas[k] = [];
                                measureVals.push(row[k]);
                                let mapD = {
                                    name: item.properties.name,
                                    value: row[k],
                                    visualMap: false
                                };
                                allMapDatas[k].push(mapD);
                                if (columns.indexOf(k) > -1) {
                                    addD += row[k];
                                }
                            }
                        }
                        if (addD > max) {
                            max = addD;
                        }
                        if (addD < min) {
                            min = addD;
                        }
                        let lnglat = item.properties.cp || item.properties.center || [item.properties.longitude, item.properties.latitude];
                        let area = {
                            name: item.properties.name,
                            value: [...lnglat, ...measureVals],
                            visualMap: true
                        };
                        mapData.push(area);
                    }
                });
            });
            vm.bubble_max = max;
            vm.bubble_min = min;
            vm.extend.series.push({
                type: 'effectScatter',
                coordinateSystem: 'geo',
                showEffectOn: 'render',
                symbolSize: vm.setSymbolSize,
                show: false,
                rippleEffect: {
                    period: 10,
                    scale: 4,
                    brushType: 'fill'
                },
                hoverAnimation: true,
                label: {
                    normal: {
                        formatter: function (params) {
                            return params.name;
                        },
                        color: "#555",
                        show: nodeData.style.legend.label_show
                    },
                },
                itemStyle: {
                    normal: {
                        color: '#ffff',
                        shadowBlur: 10,
                        shadowColor: '#333'
                    }
                },
                data: mapData
            });
            vm.extend.geo = {
                map: mapN,
                aspectScale: .75, //长宽比
                zoom: 1.1,
                roam: true,
                animation: false,
                scaleLimit: {min: 1, max: 6},
                label: {
                    normal: {
                        formatter: function (params) {
                            return params.name;
                        },
                        color: "#555",
                        show: nodeData.style.legend.label_show
                    },
                    emphasis: {
                        show: nodeData.style.legend.label_show
                    }
                },
                itemStyle: {
                    normal: {
                        areaColor: nodeData.style.legend.item_color,
                        borderColor: nodeData.style.legend.line_color,
                    }
                },
                left,
                right,
                bottom,
                top
            };
            for (let n in allMapDatas) {
                let series = {
                    name: n,
                    type: 'map',
                    map: mapN,
                    aspectScale: 0.75, //长宽比
                    showLegendSymbol: false, // 存在legend时显示
                    roam: true,
                    geoIndex: 0,
                    animation: false,
                    zoom: 1.1,
                    scaleLimit: {min: 1, max: 6},
                    data: allMapDatas[n],
                };
                vm.extend.series.push(series);
            }
            setTimeout(() => {
                vm.loading = false;
                vm.data_empty = hasData !== 0;
            }, 300)
        },
        getNodeData() {
            globalBus.$on('setBubbleMapPanel', this.setHistogramPanel);
        },
        setChartExtend(settings, nodeData) {
            this.setTitle(settings.title);
            this.setLegend(settings.legend, nodeData);
            this.setTooltip();
        },
        setColor(color, theme, type) {
            const vm = this;
            if (type === "theme") {
                vm.extend.color = vm.themes[theme];
            } else if (type === "custom") {
                if (color && color.length > 0) {
                    vm.extend.color = color;
                } else {
                    vm.extend.color = [...vm.colors];
                }
            }

        },
        getColorVal(color, i) {
            if (color[i] === undefined) {
                return this.getColorVal(color, i - color.length);
            } else {
                return color[i];
            }
        },
        setTooltip() {
            const vm = this;
            let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
            let measure = [...nodeData.xData , ...nodeData.kData];
            let options = nodeData.style.fieldSetting?.options||[];
            // 利用reduce方法遍历数组,reduce第一个参数是遍历需要执行的函数，第二个参数是item的初始值
            let obj = {};
            measure = measure.reduce((item, next) =>{
                obj[next.value] ? '' : obj[next.value] = item.push(next);
                return item;
            }, []);
            vm.setMapTooltip(measure,options);
        },
        setLegend(legend, nodeData) {
            const vm = this;
            vm.setColor(legend.color, legend.color_theme, legend.color_type);
            vm.setVisualMap(legend, nodeData);
            vm.legendAuto(legend,nodeData);
        },
        legendAuto(legend,nodeData) {
            const vm = this;
            let show_l = legend.legend_pos !== 'none';
            let left = '', top = '', zoomBottom;
            if (legend.legend_pos !== 'none') {
                if (legend.legend_pos === 'left') {
                    left = 'left';
                    top = 'middle';
                    zoomBottom = 6;
                } else if (legend.legend_pos === 'right') {
                    left = 'right';
                    top = 'middle';
                    zoomBottom = 6;
                } else if (legend.legend_pos === 'top') {
                    left = 'center';
                    top = 26;
                    zoomBottom = 6;
                } else if (legend.legend_pos === 'bottom') {
                    left = 'center';
                    top = 'bottom';
                    zoomBottom = 30;
                }
            } else {
                zoomBottom = 6;
            }
            if (vm.extend.dataZoom) {
                vm.extend.dataZoom[1].bottom = zoomBottom;
            }
            const columns  = nodeData.style.fieldSetting?.options||[];
            let legend_data = vm.chartData.columns.slice(1);
            vm.extend.legend = {
                type: 'scroll',
                orient: legend.orient,
                show: show_l,
                left: left,
                top: top,
                align: 'auto',
                height: "80%",
                width: "80%",
                data: legend_data,
                formatter:(o)=>{
                    let item = columns.find(c => c.label === o);
                    return item?.alias || item?.label || o;
                }
            };
        },
        forChartData(chartData, allKeys) {
            let max = 200, min = 0;
            if (chartData.rows) {
                chartData.rows.forEach(chart => {
                    let data = 0, condiion;
                    for (let k in chart) {
                        condiion = allKeys ? (k !== '位置' && allKeys.indexOf(k) > -1) : k !== '位置';
                        if (condiion) {
                            data += chart[k];
                        }
                    }
                    if (data > max) {
                        max = data;
                    }
                    if (data < min) {
                        min = data;
                    }
                });
            }
            return {max, min};
        },
        getDataMinMax(chartData, params, name) {
            const vm = this;
            let res;
            if (!params) {
                res = vm.forChartData(chartData, [name]);
            } else {
                let allKeys = [];
                for (let i in params.selected) {
                    if (params.selected[i] === true && i === name) {
                        allKeys.push(i);
                    }
                }
                res = vm.forChartData(chartData, allKeys);
            }
            let {min, max} = res;
            if (min < 0) {
                min -= Math.abs(Math.ceil(max / 100));
            }
            if (max > 200) {
                max += Math.abs(Math.ceil(max / 100));
            }
            if (max < 200) max = 200;
            return {min, max}
        },
        setDataMinMax(params) {
            const vm = this, {chartData} = this;
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);
            let {legend} = nodeData.style, res;
            let name = nodeData.kData[0].label;
            res = vm.getDataMinMax(chartData, params, name);
            let {min, max} = res;
            if (!legend.minAuto) {
                vm.extend.visualMap.min = legend.min;
            } else {
                vm.extend.visualMap.min = min;
            }
            if (!legend.maxAuto) {
                vm.extend.visualMap.max = legend.max;
            } else {
                vm.extend.visualMap.max = max;
            }
        },
        setVisualMap(legend, nodeData) {
            const vm = this, {node, themes} = this;
            let {rangeColor, rangeColor_theme, rangeColor_type, bubbles_color} = legend;
            let min = 0, max = 200, splitNum = 5;
            if (legend.splitNumber !== undefined) {
                splitNum = legend.splitNumber;
            }
            if (nodeData.kAxis.length) {
                let xlen = nodeData.xAxis.indexOf(nodeData.kAxis[0]) > -1 ? nodeData.xAxis.length - 1 : nodeData.xAxis.length;
                let dim_len = xlen + 2;
                vm.extend.visualMap = {
                    type: "piecewise",
                    min: min,
                    max: max,
                    left: 'left',
                    top: 'bottom',
                    splitNumber: splitNum,
                    show: true,
                    dimension: dim_len,
                    calculable: true,
                    seriesIndex: [0]
                };
                vm.setDataMinMax();
                if (rangeColor_type === "theme") {
                    vm.extend.visualMap.inRange = {
                        color: themes[rangeColor_theme]
                    }
                } else if (rangeColor_type === "custom") {
                    if (rangeColor.length) {
                        vm.extend.visualMap.inRange = {
                            color: rangeColor
                        }
                    } else {
                        delete vm.extend.visualMap.inRange
                    }
                }
            } else {
                vm.extend.visualMap = {
                    show: false
                };
                vm.extend.visualMap.inRange = {
                    color: bubbles_color
                }
            }

        },
        setHistogramPanel(id) {
            const vm = this, {node, isView} = this;
            if (node.id !== id && id !== undefined) return;
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);
            vm.code = nodeData.style && nodeData.style.funcSettings.mapCode && nodeData.style.funcSettings.mapCode.length ? nodeData.style.funcSettings.mapCode.slice(-1)[0] : 'china';
            vm.filterDimsName = "";
            if (nodeData.filterList && nodeData.filterList.length > 0) {
                let dims = null;
                let query = nodeData.filterList.filter(list => {
                    if (list.dim) dims = list.dim;
                    return list.filter && list.filter.addVal !== "";
                });
                widget.query = JSON.stringify(query);
                let dimsIndex = widget.widgetDataset.widgetDatasetDims.length;
                if (dims) {
                    dims.index = dimsIndex;
                    widget.widgetDataset.widgetDatasetDims.push(dims);
                    vm.filterDimsName = dims.filedCode;
                }
            }
            widget.mapCode = vm.code;
            vm.loading = true;
            vm.data_empty = true;
            vm.chartSettings.mapOrigin = null;
            /*vm.send(JSON.stringify({
                code: node.code,
                data: widget,
                type: isView
            }));*/
            vm.getChartData({
                code: vm.node.code,
                data: widget,
                type: vm.isView
            });
        },
        async getMessage(msg) {
            const vm = this;
            if (vm.socket) vm.socket.close();
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);
            let result = JSON.parse(msg.data), data = result.data;
            if (result.status === 0 && data) {
                vm.chartData = data;
                await vm.initMap(result.status, nodeData);
                vm.setChartExtend(nodeData.style, nodeData);
                vm.loading = false;
                vm.data_empty = false;
            } else {
                vm.loading = false;
            }
        },
    }
}
