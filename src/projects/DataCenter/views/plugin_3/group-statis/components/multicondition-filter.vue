<!--
 * @Author: zhengtp
 * @Date: 2021-06-17 09:47:43
 * @Description: 多条件检索过滤
 * @FilePath: \04代码\src\pages\model-space\data-model\models\rapid-analysis\model\group-statis\components\multicondition-filter.vue
-->
<template>
    <div class="multicondition-filter" :style="{'height' : tableHeight ? '100%' : ''}">
        <div class="multicondition-filter__aside"
             :class="{'isRecycling dg-iconp icon-delete-b' : isRecycling}"
             @dragover="handleListDragover($event)"
             @dragleave="cancelRecycle"
             @drop="listDrop($event)" v-loading="settings.loading">
            <div class="multicondition-filter__check">
                <el-input
                        class="ce-filter_inp"
                        v-model="filter"
                        @input="fieldFilter"
                        suffix-icon="el-icon-search"
                        v-input-limit:trim
                        placeholder="搜索"
                        clearable
                ></el-input>
            </div>
            <div class="multicondition-filter__aside-main">
                <div class="field-list">
                    <ce-drag-list :data="fields"
                                  ref="dragList"
                                  drag-item-class-name="list-item_tobody"
                                  :cursor-at="cursorAt"
                                  :setDragItemDom="setDragItemDom"
                                  @checkAllChange="fieldCheckAllChange"
                                  @dragEnd="dragEnd"
                                  @dragItems="dragItems">
                        <el-checkbox class="ce-drag_check" slot="checkAll" v-model="checkAll"
                                     :indeterminate="isIndeterminate" title="全选" @change="checkAllField"></el-checkbox>
                        <template slot="listItem" slot-scope="{item , $index , className }">
                            <div
                                    class="field-list__item list-item"
                                    :class="className" :title="item.label"
                                    :style="{
                                        backgroundColor: getLightColor(item.suffix.color)
                                    }">
                                    <span class="list-item__suffix"
                                          :style="{
                                        color: item.suffix.color
                                    }"
                                    >{{ item.suffix.label }}</span>
                                <span class="list-item__label">{{ item.label }}</span>
                            </div>
                        </template>
                    </ce-drag-list>
                </div>
            </div>
        </div>
        <div class="multicondition-filter__main">
            <div
                    class="field-card mb-14"
                    :style="{'height' : tableHeight ? (checked ? 'calc((100% - 6px - 1.75rem)/3)' : 'calc(50% - 2px - .4375rem)') : ''}"
            >
                <div class="field-card__header"><span class="mr10">分组</span> <el-checkbox v-model="checked">分组过滤</el-checkbox></div>
                <div class="field-card__main">
                    <ce-drop-cont ref="dropperGroup" hover-class="ce-dropper_hover" @drop="dropField($event , 'group')">
                        <dg-scrollbar ref="parentNode" class="scoller-view">
                            <ce-sortable :data="groupFilterList" ghostClass="ce-sort_ghost">
                                <cond-select
                                        slot="listItem"
                                        slot-scope="{item , id , $index}"
                                        class="field-item-top"
                                        draggable
                                        @dragstart.native="nodeDragStart(item , $index ,'groupFilterList' ,'columnOptions' )"
                                        @dragend.native="nodeDragEnd"
                                        @rename="rename"
                                        :key="id"
                                        :options="getOptions('group', item)"
                                        :field="item"
                                        :groupList="groupFilterList[$index]"
                                        @del="del('group', $index, id)"
                                        @getIntervalGroup="
                                val => {
                                    getIntervalGroup(val, 'group', $index)
                                }"
                                        @value-change="
                                val => {
                                    handleValueChange(val, 'group', $index);
                                }
                            "
                                ></cond-select>
                            </ce-sortable>
                            <div v-if="!groupFilterList.length" class="gray-tip">
                                请从左侧列表选择数据
                            </div>
                        </dg-scrollbar>
                    </ce-drop-cont>
                </div>

            </div>

            <div
                    class="field-card"
                    :style="{'height' : tableHeight ? (checked ? 'calc((100% - 6px - 1.75rem)/3)' : 'calc(50% - 2px - .4375rem)') : ''}">
                <div class="field-card__header">统计</div>
                <div class="field-card__main">
                    <ce-drop-cont ref="dropperStatic" hover-class="ce-dropper_hover"
                                  @drop="dropField($event , 'static')">
                        <dg-scrollbar class="scoller-view">
                            <ce-sortable :data="statisFilterList" ghostClass="ce-sort_ghost">
                                <cond-select
                                        slot="listItem"
                                        slot-scope="{item , id , $index}"
                                        :key="id"
                                        class="field-item-bottom"
                                        draggable
                                        @dragstart.native="nodeDragStart(item , $index ,'statisFilterList' ,'statisColumnOptions' )"
                                        @dragend.native="nodeDragEnd"
                                        @rename="rename"
                                        :options="getOptions('statis', item)"
                                        :statisFilterList="statisFilterList"
                                        :field="item"
                                        @del="del('statis', $index, id)"
                                        @getQuantile="
                                                    val => {
                                                        getQuantile(val, 'statis', $index)
                                                    }"
                                        @value-change="
                                                    val => {
                                                        handleValueChange(val, 'statis', $index);
                                                    }
                                                "
                                ></cond-select>
                            </ce-sortable>
                            <div v-if="!statisFilterList.length" class="gray-tip">
                                请从左侧列表选择数据
                            </div>
                        </dg-scrollbar>
                    </ce-drop-cont>
                </div>
            </div>

            <div  class="field-card" v-if="checked" :style="{'height' : tableHeight && checked ? 'calc((100% - 6px - 1.75rem)/3)' : '0'}">
                <div class="field-card__header">分组过滤</div>
                <div class="field-card__main textarea_info">
                    <el-input
                            type="textarea"
                            class="textareaInput"
                            resize="none"
                            :rows="3"
                            placeholder="sql语句"
                            @change="save"
                            v-model="groupFilterInfo">
                    </el-input>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import condSelect from "./cond-select";
import {common} from "@/api/commonMethods/common"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins"
import CeDragList from "@/components/common/ce-drag-list/CeDragList";
import CeDropCont from "@/components/common/ce-drag-list/CeDropCont";
import {fieldDragMethod} from "@/projects/DataCenter/views/plugin_3/group-statis/components/fieldDragMethod";
import CeSortable from "@/components/common/ce-drag-list/CeSortable";

export default {
    name: "multicondition-filter",
    mixins: [common, commonMixins, servicesMixins, fieldDragMethod],
    components: {
        CeSortable,
        condSelect,
        CeDragList,
        CeDropCont
    },
    props: {
        tableHeight: {
            type: Boolean,
            default: false
        },
        sourceData: Object,
        rowData: Object,
        saveVo: Object,
        isRapidAnalysis: Boolean,
        loadParams : Object,
    },
    data() {
        return {
            draging: null,
            target: null,
            //移动起始label值
            dragingText: "",
            // 过滤关键词
            filter: "",

            // 左侧字段列表
            fields: [
                {
                    label: "身份证号",
                    value: "sfzh",
                    type: "string",
                    suffix: {
                        label: "Ab",
                        color: "#0088FF"
                    }
                },
                {
                    label: "人数",
                    value: "rs",
                    type: "number",

                    suffix: {
                        label: "#",
                        color: "#52C41A"
                    }
                },
                {
                    label: "入住时间",
                    value: "rzsj",
                    type: "date",

                    suffix: {
                        label: "Dt",
                        color: "#FAAD14"
                    }
                },
                {
                    label: "入住金额",
                    value: "rzje",
                    type: "number",

                    suffix: {
                        label: "#",
                        color: "#52C41A"
                    }
                },
                {
                    label: "退房时间",
                    value: "tfsj",
                    type: "date",

                    suffix: {
                        label: "Dt",
                        color: "#FAAD14"
                    }
                }
            ],

            // 预设选项配置
            operation: [
                {
                    label: "重命名",
                    value: "rename"
                },
                {
                    label: "删除",
                    value: "del"
                }
            ],
            // 选项-分组
            group: {
                String: [
                    {
                        label: "相同值为一组",
                        value: "some"
                    }
                ],
                date: [
                    {
                        label: "相同值为一组",
                        value: "some"
                    },
                    {
                        label: "年月日",
                        value: "nyr"
                    },
                    {
                        label: "年周数",
                        value: "nzs"
                    },
                    {
                        label: "年月",
                        value: "ny"
                    },
                    {
                        label: "年季度",
                        value: "njd"
                    },
                    {
                        label: "年",
                        value: "n"
                    }
                ],
                number: [
                    {
                        label: "相同值为一组",
                        value: "nyr"
                    },
                    /*{
                        label: "区间分组",
                        value: "nzs"
                    }*/
                ]
            },
            // 选项-统计
            statis: {
                String: [
                    {
                        label: "记录个数",
                        value: "jlgs"
                    },
                    {
                        label: "去重计数",
                        value: "qcjs"
                    },

                    {
                        label: "字符串拼接",
                        value: "zfcpj"
                    }
                ],
                date: [
                    {
                        label: "记录个数",
                        value: "jlgs"
                    },
                    {
                        label: "去重计数",
                        value: "qcjs"
                    },
                    /*{
                        label: "最早时间",
                        value: "zzsj"
                    },
                    {
                        label: "最晚时间",
                        value: "zwsj"
                    }*/
                ],
                number: [
                    {
                        label: "求和",
                        value: "qh"
                    },
                    {
                        label: "平均值",
                        value: "pjz"
                    },
                    {
                        label: "中位数",
                        value: "zws"
                    },
                    {
                        label: "最大值",
                        value: "zdz"
                    },
                    {
                        label: "最小值",
                        value: "zxz"
                    },
                    {
                        label: "记录个数",
                        value: "jlgs"
                    }
                ]
            },

            //  右侧 分组 统计 托放入结果值
            groupFilterList: [],
            statisFilterList: [],

            // 拖拽放置目标容器
            dropTarget: "",
            //重名
            newName: "",
            groupI: 0,
            renameGroupName: "",
            statisI: 0,
            renameStatisName: "",

            //字段数据类型
            dataDateType: [],
            dataNumberType: [],
            dataStringType: [],
            //分位数
            quantile: "",
            //左侧原始数据
            originalFields: [],

            //是否拉到左侧列表触发回收
            isRecycling: false,
            columnOptions: {},
            //统计字段
            statisColumnOptions: {},

            //初始的分组字段函数
            groupFuncList: [],
            //初始的统计字段函数
            statisFuncList: [],
            //需要经过特殊处理的时间函数
            specialDateList: ['year', 'quarter', 'weekofyear', 'yearAndMonthAndDay', 'yearAndMonth'],
            checked: false,
            groupFilterInfo:'',
        };
    },
    methods: {
        listDragStart(event) {
            this.draging = event.target.parentNode;
            this.dragingText = event.target.innerText.split("\n")[0];
            this.target = null;
            this.isRecycling = false;
        },
        movePos(fieldData, fromInx, targetInx) {
            if (fromInx < 0 || targetInx < 0) return;
            const temp = fieldData[targetInx];
            this.$set(fieldData, targetInx, fieldData[fromInx]);
            this.$set(fieldData, fromInx, temp);
        },
        listDragEnd() {
            /*if (this.isRecycling) {
                let dragInx = this._index(this.dragingText);
                this.groupFilterList.splice(dragInx, 1);
            }
            if (!this.target) return;
            if (this.target.className === "cond-select field-item-top" && this.target !== this.draging) {
                let dragInx = this._index(this.dragingText),
                        targetInx = this._index(this.target.innerText.split("\n")[0]),
                        values = this.groupFilterList;
                this.movePos(values, dragInx, targetInx);
            }*/
        },
        listStatisDragEnd() {
            if (this.isRecycling) {
                let dragInx = this._statisIndex(this.dragingText);
                this.statisFilterList.splice(dragInx, 1);
            }
            if (!this.target) return;
            if (this.target.className === "cond-select field-item-bottom" && this.target !== this.draging) {
                let dragInx = this._statisIndex(this.dragingText),
                        targetInx = this._statisIndex(this.target.innerText.split("\n")[0]),
                        values = this.statisFilterList;
                this.movePos(values, dragInx, targetInx);
            }
        },
        /**
         * 分组字段位置
         */
        _index(text) {
            for (let index = 0; index < this.groupFilterList.length; index++) {
                if (this.groupFilterList[index].label === text) {
                    return index;
                }
            }
        },
        /**
         * 统计字段位置
         */
        _statisIndex(text) {
            for (let index = 0; index < this.statisFilterList.length; index++) {
                if (this.statisFilterList[index].label === text) {
                    return index;
                }
            }
        },
        del(card, index, id) {
            if (card === "group") {
                let list = this.groupFilterList[index];
                if (this.columnOptions[list.value]) {
                    this.columnOptions[list.value].forEach(item => {
                        if (item.value === list.optionVal) item.show = false;
                    })
                }

                // this.groupFilterList.splice(index, 1);
                this.groupFilterList = this.groupFilterList.filter(item => item.id !== id);
            } else {
                let list = this.statisFilterList[index];
                if (this.statisColumnOptions[list.value]) {
                    this.statisColumnOptions[list.value].forEach(item => {
                        if (item.value === list.optionVal) item.show = false;
                    })
                }
                // this.statisFilterList.splice(index, 1);
                this.statisFilterList = this.statisFilterList.filter(item => item.id !== id);
            }
        },
        /**
         * @description: 字段操作列表
         * @param {*} card
         * @param {*} field
         * @return {*}
         */
        getOptions(card, field) {
            let type = field.type;
            if (this.dataNumberType.indexOf(type) >= 0) {
                type = "number";
            } else if (this.dataStringType.indexOf(type) >= 0) {
                type = "string";
            } else {
                type = "date";
            }
            if (card === 'group') {
                if (Object.keys(this.columnOptions).length === 0 || !this.columnOptions[field.value]) return this.operation;
                this.columnOptions[field.value].forEach(item => {
                    item.show = false;
                })
                this.groupFilterList.forEach(item => {
                    if (this.columnOptions[item.value] && this.columnOptions[item.value].length > 0) {
                        this.columnOptions[item.value].forEach(element => {
                            if (item.optionVal === element.value) element.show = true;
                        })
                    }
                })
                const result = this.columnOptions[field.value];
                return result.concat(...this.operation);
            } else {
                if (Object.keys(this.statisColumnOptions).length === 0 || !this.statisColumnOptions[field.value]) return this.operation;
                this.statisColumnOptions[field.value].forEach(item => {
                    item.show = false;
                })
                this.statisFilterList.forEach(item => {
                    this.statisColumnOptions[item.value].forEach(element => {
                        if (item.optionVal === element.value) element.show = true;
                    })
                })
                const result = this.statisColumnOptions[field.value];
                return result.concat(...this.operation);
            }

            // const list = this[card][type];
            // return list.concat(...this.operation);
        },
        /**
         * @description: 过滤
         * @param {*}
         * @return {*}
         */

        fieldFilter(value) {
            const vm = this;
            let searchResult = []
            vm.fields.forEach(item => {
                if (item.label.toUpperCase().indexOf(value) >= 0 || item.label.toLowerCase().indexOf(value) >= 0) {
                    searchResult.push(item);
                }
            })
            vm.fields = searchResult;
            if (value === "") vm.fields = vm.originalFields;
        },
        /**
         * @description: hex颜色转rgb颜色数组
         * @param {*} str
         * @return {*}
         */
        hexToRgb(str) {
            let reg = /^\#?[0-9A-F]{6}$/i;
            if (!reg.test(str)) return alert("错误的hex格式");
            str = str.replace("#", "");
            // match得到查询数组
            let hxs = str.match(/../g);
            hxs = hxs.map(v => parseInt(v, 16));
            return hxs;
        },
        /**
         * @description: 获取减淡背景色
         * @param {*} hxs
         * @return {*}
         */
        getLightColor(str) {
            const hxs = this.hexToRgb(str);
            return `rgba(${hxs[0]}, ${hxs[1]}, ${hxs[2]}, 0.15)`;
        },
        /**
         * @description: 处理拖拽字段
         * @param {*}
         * @return {*}
         */

        handleDragStart(e, field) {
            e.dataTransfer.dropEffect = "move";
            e.dataTransfer.setData("drag_field", JSON.stringify(field));
        },

        /** 放置目标区  **/
        /**
         * @description:
         * @param {*}
         * @return {*}
         */
        handleDragEnter(dropTarget) {
            // this.dropTarget = dropTarget;
        },
        handleDrop(e, dropTarget) {
            this.dropTarget = "";
            let target = e.target;
            if (target.nodeName === "DIV") {
                this.target = target.parentNode;
            } else { //
                this.target = target.parentNode.parentNode;
            }
            let field = e.dataTransfer.getData("drag_field");
            if (field !== "") {
                field = JSON.parse(field);
                field.optionVal = "";
                let isSelectable = false;
                if (dropTarget === "group") {
                    this.columnOptions[field.value].forEach(item => {
                        if (!item.show) isSelectable = true;
                    })
                    if (!isSelectable) {
                        this.$message.warning("请勿选择重复");
                        return;
                    }
                    let flag = false; //String和number类型分组字段是否拉重复（现在不允许拉重复）
                    if (field.type === 'String') {
                        this.groupFilterList.forEach(item => {
                            if (item.label === field.label) {
                                this.$message.warning("分组字段重复");
                                flag = true;
                            }
                        })
                    }
                    if (flag) return;
                    // this.newName = field.label;
                    // this.rename(field.label);
                    // field.label = this.newName;
                    field.id = new Date().getTime();
                    this.groupFilterList.push(field);
                } else {
                    let row_numberFlag = false; //判断该字段是否已经选过组内排名
                    this.statisColumnOptions[field.value].forEach(item => {
                        if (!item.show) isSelectable = true;
                        if (item.show && item.value === 'row_number') row_numberFlag = true;
                    })
                    if (!isSelectable) {
                        this.$message.warning("请勿选择重复");
                        return;
                    }
                    if (row_numberFlag) {
                        this.$message.warning("已选过该字段组内排名函数，请勿重复选择该字段");
                        return;
                    }
                    // this.newName = field.label;
                    // this.rename(field.label);
                    // field.label = this.newName;
                    field.id = new Date().getTime();
                    this.statisFilterList.push(field);
                }
            }
        },
        rename(fieldLabel, id) {
            let gList = JSON.parse(JSON.stringify(this.groupFilterList)),
                    sList = JSON.parse(JSON.stringify(this.statisFilterList));
            let allList = gList.concat(sList);
            allList.forEach(item => {
                if (fieldLabel === item.label) {
                    let i = item.label.split("-")[1] === undefined ? 0 : item.label.split("-")[1];
                    i++;
                    let newLabel = fieldLabel.split("-")[0] + "-" + i;
                    this.newName = newLabel;
                    this.rename(newLabel);
                }
            })
            this.groupFilterList.forEach(element => {
                if (element.id === id) element.label = this.newName;
            });
        },
        handleDragover(e, dropTarget) {
            e.preventDefault();
            if (dropTarget !== this.dropTarget) {
                this.dropTarget = dropTarget;
            }
        },
        /**
         * 左侧树是否可放置
         */
        handleListDragover(e) {
            if(this.preDelData){
                this.isRecycling = true;
                e.preventDefault();
            }

        },
        /**
         * 左侧树放置时间
         * 回收字段
         */
        listDrop(e, list, option) {
            // this.isRecycling = true;
            this.listRecycling();
        },
        handleDragLeave() {
            this.dropTarget = "";
        },
        handleValueChange(val, card, index) {
            if (card === "group") {
                this.groupFilterList[index].optionVal = val;
            } else {
                this.statisFilterList[index].optionVal = val;
            }
        },
        getIntervalGroup(val, card, index) {
            this.groupFilterList[index].intervalGroup = val;
            this.save();
        },
        init() {
            const vm = this, {pluginServices, pluginMock , loadParams} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            vm.fields = [];
            loadParams.loading = true;
            services.groupInit(this.sourceData.id, loadParams).then(res => {
                if (res.data.status == 0) {
                    let result = res.data.data;
                    this.setStatis(result.exps)
                    this.setGroup(result.groupEnums);
                    this.getDataType(result.types);
                    this.listPreview(result.pluginMeta.groupFieldExps, "groupFilterList");
                    this.listPreview(result.cicadaAggregatorFieldExps, "statisFilterList");
                    vm.groupFilterInfo = result.pluginMeta.havingCondition;
                    vm.checked =  vm.groupFilterInfo ? true :false
                    result.columns.forEach(i => {
                        let column = {
                            label: i.columnZhName ? i.columnZhName : i.columnName,
                            value: i.columnName,
                            type: i.columnType,
                            length: i.length,
                            //字段标记
                            suffix: this.dataNumberType.indexOf(i.columnType) > -1 ? {
                                label: "#",
                                color: "#52C41A"
                            } : this.dataStringType.indexOf(i.columnType) > -1 ? {
                                label: "Ab",
                                color: "#0088FF"
                            } : {
                                label: "Dt",
                                color: "#FAAD14"
                            }
                        }
                        vm.fields.push(column);
                        vm.originalFields.push(column);
                    });
                    this.setGroup(result.groupEnums);
                    this.setStatis(result.exps);
                    this.listPreview(result.cicadaAggregatorFieldExps, "statisFilterList");
                    if (this.statisFilterList.length > 0 && this.isRapidAnalysis) this.$emit("isSave", true);
                    if (this.saveVo && this.saveVo.cicadaReduceFieldExps && this.saveVo.cicadaReduceFieldExps.length > 0 && !this.isRapidAnalysis) {
                        this.listPreview(this.saveVo.groupFieldExps, "groupFilterList");
                        this.listPreview(this.saveVo.cicadaReduceFieldExps, "statisFilterList");
                    }
                }
            });
            if (!this.isRapidAnalysis) this.save();
        },
        /**
         * 分组统计字段集合回显
         */
        listPreview(result, list) {
            this[list] = [];
            const vm = this;
            let i = 0;
            result.forEach(item => {
                let type = item.valType;
                if (list === 'statisFilterList') {
                    vm.fields.forEach(i => {
                        if (i.value === item.mergeColumn) type = i.type;
                    })
                }
                let intervalGroupObject = {};
                if (list !== 'statisFilterList' && item.sectionExpression) {
                    intervalGroupObject.sectionExpression = JSON.parse(item.sectionExpression);
                    intervalGroupObject.otherValueSec = item.otherValueSec;
                    intervalGroupObject.otherValueName = item.otherValueName;
                }
                i++;
                let object = {
                    label: item.replaceFieldName.split("_")[1] ? item.replaceFieldName.split("_")[1] || item.mergeColumn : item.replaceFieldName || item.mergeColumn,
                    value: item.mergeColumn,
                    type: type,
                    length: item.length,
                    optionVal: item.mergeExpression,
                    sectionExpression: list !== 'statisFilterList' && item.sectionExpression ? JSON.parse(item.sectionExpression) : "",
                    otherValueSec: list !== 'statisFilterList' ? item.otherValueSec : "",
                    otherValueName: list !== 'statisFilterList' ? item.otherValueName : "",
                    quantileValue: list === 'statisFilterList' ? item.quantileValue : 0,
                    intervalGroup: intervalGroupObject,
                    id:`${list}_${i}`,
                }
                this[list].push(object);
            })
        },
        /**
         * 获取统计字段函数
         */
        setStatis(result) {
            this.statis = {
                string: [],
                date: [],
                number: []
            };
            this.statisFuncList = result;
            result.forEach(item => {
                let object = {
                    label: item.label,
                    value: item.code
                };
                if (item.code === 'sum') {
                    this.statis.number.push(object);
                } else if (item.code === 'count' || item.code === 'count_distinct') {
                    this.statis.string.push(object);
                    this.statis.date.push(object);
                    this.statis.number.push(object);
                } else if (item.code === 'concat_ws') {
                    this.statis.string.push(object);
                } else if (item.code === 'min_time' || item.code === 'max_time') {
                    this.statis.date.push(object);
                } else {
                    this.statis.number.push(object);
                }
            })
            this.fields.forEach(item => {
                let code = item.value;
                if (this.dataNumberType.indexOf(item.type) >= 0) {
                    this.statisColumnOptions[code] = JSON.parse(JSON.stringify(this.statis.number));
                } else if (this.dataStringType.indexOf(item.type) >= 0) {
                    this.statisColumnOptions[code] = JSON.parse(JSON.stringify(this.statis.string));
                } else {
                    this.statisColumnOptions[code] = JSON.parse(JSON.stringify(this.statis.date));
                }
            })
        },
        /**
         * 获取分组字段函数
         */
        setGroup(result) {
            this.group = {
                string: [],
                date: [{
                    label: "相同值为一组",
                    value: "sameValueGroup"
                }],
                number: []
            };
            this.groupFuncList = result;
            result.forEach(item => {
                let object = {
                    label: item.label,
                    value: item.code
                };
                if (item.code === 'sameValueGroup') {
                    this.group.string.push(object);
                    this.group.number[0] = object;
                } else if (item.code === 'intervalGroup') {
                    this.group.number[1] = object;
                } else {
                    this.group.date.push(object);
                }
            })
            this.fields.forEach(item => {
                let code = item.value;
                if (this.dataNumberType.indexOf(item.type) >= 0) {
                    this.columnOptions[code] = JSON.parse(JSON.stringify(this.group.number));
                } else if (this.dataStringType.indexOf(item.type) >= 0) {
                    this.columnOptions[code] = JSON.parse(JSON.stringify(this.group.string));
                } else {
                    this.columnOptions[code] = JSON.parse(JSON.stringify(this.group.date));
                }
            })
        },
        /**
         * 获取字段数据类型
         */
        getDataType(result) {
            this.dataDateType = [],
                    this.dataNumberType = [],
                    this.dataStringType = [],
                    result.forEach(item => {
                        if (item.label === '数值') {
                            this.dataNumberType.push(item.code);
                        } else if (item.label === '字符') {
                            this.dataStringType.push(item.code);
                        } else {
                            this.dataDateType.push(item.code);
                        }
                    })
        },
        getQuantile(val, card, index) {
            this.statisFilterList[index].quantileValue = val;
            this.save();
        },
        save() {
            const vm = this;
            let groupName = [], groupInfo = [], statisInfo = [];
            this.groupFilterList.forEach((item, i) => {
                let funcName;
                this.groupFuncList.forEach(e =>{
                    if (item.optionVal === e.code) funcName = e.label;
                })
                groupName.push(item.value);
                let object = {
                    length: this.specialDateList.includes(item.optionVal) ? item.length === 0 ? 32 : item.length : item.length,
                    mergeColumn: item.value,
                    mergeExpression: item.optionVal,
                    newFieldName: item.optionVal === 'sameValueGroup' ? item.value : (item.optionVal + "_" + item.value),
                    precision: 0,
                    // replaceFieldName: funcName + "_" + item.label,
                    replaceFieldName: item.optionVal === 'intervalGroup' && item.intervalGroup ? '区间分组_' + item.label : funcName + "_" + item.label,
                    valType: item.optionVal === 'intervalGroup' || this.specialDateList.includes(item.optionVal) ? 'String' : item.type,
                    sectionExpression: item.optionVal === 'intervalGroup' && item.intervalGroup ? JSON.stringify(item.intervalGroup.sectionExpression) : "",
                    otherValueSec: item.optionVal === 'intervalGroup' && item.intervalGroup ? item.intervalGroup.otherValueSec : "",
                    otherValueName: item.optionVal === 'intervalGroup' && item.intervalGroup ? item.intervalGroup.otherValueName : "",
                    sortNo: i+1,
                };
                groupInfo.push(object);
            })
            this.statisFilterList.forEach((item, i) => {
                let flag = item.optionVal === 'sum' || item.optionVal === 'sum_distinct' || item.optionVal === 'count_distinct' || item.optionVal === 'count';
                let funcName;
                this.statisFuncList.forEach(e => {
                    if (item.optionVal === e.code) funcName = e.label;
                })
                let object = {
                    length: flag ? "32" : item.length,
                    mergeColumn: item.value,
                    mergeExpression: item.optionVal,
                    newFieldName: item.optionVal + "_" + item.value,
                    precision: 0,
                    replaceFieldName: funcName + "_" + item.label,
                    valType: flag ? "Integer" : item.type,
                    quantileValue: item.optionVal === 'quantile' ? item.quantileValue : "",
                    sortNo: i+1,
                };
                statisInfo.push(object);
            })
            let vo = {
                flatMapExpression: null,
                groupFieldExps: groupInfo,
                groupFieldName: JSON.stringify(groupName),
                cicadaReduceFieldExps: statisInfo,
                havingCondition: vm.checked ? vm.groupFilterInfo : '',
            };
            this.$emit('getVo', vo);
        }
    },
    created() {
        this.init();
    },
    watch: {
        groupFilterList: {
            handler(value) {
                this.save();
            },
            deep: true
        },
        statisFilterList: {
            handler(value) {
                this.save();
            },
            deep: true
        },
    },
};
</script>
<style lang="less">
.list-item_tobody {
    width: 13.375rem;
    box-sizing: border-box;
    padding: 0.4375rem 0.875rem;
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid transparent;
    transition: border 150ms;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:not(:last-child) {
        margin-bottom: 5px;
    }

    > .list-item__label {
        color: rgba(0, 0, 0, 0.85);
    }
}
</style>
<style lang="less" scoped>

.ce-filter_inp {
    margin-left: 30px;
}

.ce-drag_check {
    position: absolute;
    top: calc(1.875rem - 10px);
}

.ce-dropper_hover {
    background: rgba(0, 136, 255, 0.1);
}

.scoller-view {
    /deep/ .el-scrollbar__view {
        padding: 8px;
    }
}

.multicondition-filter {
    display: flex;
    height: 18.5rem;

    &__check {
        display: flex;
        justify-content: space-between;
        align-items: center;

        > .el-checkbox {
            margin-right: 8px;
        }
    }

    &__aside {
        padding: 0.875rem;
        width: 13.375rem;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
        position: relative;

        &-main {
            height: calc(100% - 2rem);
            padding-top: 0.625rem;
            box-sizing: border-box;
        }
    }

    &__main {
        flex: 1;
        margin-left: 0.875rem;
    }

    .field-list {
        //margin-top: 0.625rem;
        height: 100%;

        &__item {
            margin-bottom: 0.625rem;
            cursor: move;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .list-item {
        width: 13.375rem;
        box-sizing: border-box;
        padding: 0.4375rem 0.875rem;
        background: rgba(0, 0, 0, 0.02);
        border: 1px solid transparent;
        transition: border 150ms;

        &:hover, &.active {
            border: 1px solid #0088ff;
        }


        &__suffix {
            display: inline-block;
            width: 1.5rem;
            height: 0.875rem;
            line-height: 0.875rem;
            text-align: center;
            margin: 0 0.4375rem 0 0;
        }

        &__label {
            color: rgba(0, 0, 0, 0.85);
        }
    }

    .field-card {
        height: 8.7125rem;
        border: 1px solid #d9d9d9;
        border-radius: 2px;

        &:not(:last-child) {
            margin-bottom: 0.875rem;
        }

        &__header {
            background: #fafafa;
            line-height: 2.5rem;
            padding-left: 1rem;
            color: #333;
            border-bottom: 1px solid #d9d9d9;
        }

        &__main {
            padding: calc(0.875rem - 8px);
            height: calc(100% - 2.5625rem);
            box-sizing: border-box;
        }

        &.is-droping {
            background: rgba(color #0088ff, alpha 0.04);
        }
    }

    .field-item {
        margin: 0 0.875rem 0.875rem 0;
    }

    .field-item-top {
        margin: 0 0.875rem 0.875rem 0;
    }

    .field-item-bottom {
        margin: 0 0.875rem 0.875rem 0;
    }

    .gray-tip {
        color: rgba(0, 0, 0, 0.25);
        font-size: 0.875rem;
        line-height: 2;
    }
}

.ce-sort_ghost {
    background: none;
    border: 1px dashed #aaa;
    cursor: move;
    /deep/ * {
        opacity: .2;
    }
}
.isRecycling {
    &::before {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50% , -50%);
        color: #999;
        font-size: 40px;
        z-index: 12;
    }
    &::after {
        content: '';
        display: block;
        position: absolute;
        background: rgba(255,255,255,.8);
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 10;
    }
}

.textareaInput {
    height: 100%;
    overflow: hidden;
    /deep/ .el-textarea__inner{
        border: none;
        height: 100%;
    }
}

.multicondition-filter {
    /deep/ .textarea_info{
        padding: 0;
    }
}
</style>
