<template>
    <div class="TableManage">
        <ce-list-table
                :border="false"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                :operate-icons="operateIcons"
                @changePage="changePage"
                :filter-placeholder="filterPlaceholder"
        >
            <template slot="button">
                <el-button type="primary" @click="customData" >{{customTxt}}</el-button>
                <el-button @click="baseMapping" >{{baseTxt}}</el-button>
            </template>
        </ce-list-table>
    </div>
</template>

<script>

export default {
    name: "TableManage",
    data(){
        return {
            filterPlaceholder:'请输入编码/码值搜索',
            customTxt:"自定义码表",
            baseTxt:"库映射",
            filterText : "" ,
            tHeadData:[
                {
                    prop: "name" ,
                    label : "编码中文名称",
                    minWidth:"140px"
                },{
                    prop: "code" ,
                    label : "英文名称",
                    minWidth:"140px"
                },{
                    prop: "enumeration" ,
                    label : "枚举值",
                    minWidth:"100px"
                },{
                    prop: "coder" ,
                    label : "编码",
                    minWidth:"100px"
                }, {
                    prop: "operate",
                    label: "操作",
                    width: "120px"
                }
            ],
            tableData:[],
            operateIcons : [
                {
                    name: "编辑",
                    clickFn: this.edit,
                    show: () => true,
                    condition: (right) => true
                },{
                    name: "删除",
                    clickFn: this.deleteM,
                    show: () => true,
                    condition: (right) => true
                },
            ] ,
        }
    },
    methods : {
        /**
         * 编辑
         */
        edit (row ,inx){} ,
        /**
         * 删除
         */
        deleteM (row ,inx){} ,
        changePage(inx=1){

        },
        customData(){
            const vm = this;
            let layer = vm.$dgLayer({
                title : "自定义码表" ,
                content : require("./customPage.vue"),
                maxmin:false,
                move:false,
                area : ['668px' ,'590px'],
                on : {
                    submit(form){
                        console.log(form)
                    }
                }
            })
        },
        baseMapping(){
            const vm = this;
            let layer = vm.$dgLayer({
                title :"库映射码表" ,
                content : require("./mappingPage.vue"),
                maxmin:false,
                move:false,
                area : ['668px' ,'680px'],
                on : {
                    submit(form){

                    }
                }
            })
        }
    }
}
</script>

<style scoped>
    .TableManage {

    }
</style>
