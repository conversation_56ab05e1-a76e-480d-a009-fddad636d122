import {commonMixins} from "@/api/commonMethods/common-mixins";
export const servicesMixins = {
    mixins:[commonMixins],
    data(){
        const {isMock} = this;
        let modelingServices = require("@/projects/DataCenter/services/modeling-services/modeling-services") ,
            modelingMock = isMock ? require("@/projects/DataCenter/mock/modeling-mock/modeling-mock"):{},
            rapidAnalysisServices = require("@/projects/DataCenter/services/modeling-services/rapid-analysis-services"),
            rapidAnalysisMock = isMock ? require("@/projects/DataCenter/mock/modeling-mock/rapid-analysis-mock"):{};
        return {
            modelingServices ,
            modelingMock ,
            rapidAnalysisServices,
            rapidAnalysisMock,
            services : this.getServices(modelingServices , modelingMock)
        }
    }
}
