<template>
    <div>
        <el-form  size="mini" label-position="top">
            <el-form-item
                    v-for="(attr , i ) in attrsList"
                    :key="i"
            >
                <div slot="label" class="ce-label_title">  <span style="color: red"> *</span> {{attr.label}}
                  <i class="el-icon-question" v-if="attr.label == '关联条件'" @click="showTip"></i>
                </div>
                <span class="formTip" v-if="attr.tip"><i class="el-icon-info"></i>{{attr.tip}}</span>
                <template v-if="attr.type === 'data_obj'">
                    <el-row :gutter="10" class="ce-cont_pd" style="margin-bottom: 10px">
                        <el-col >
                            <el-radio v-model="pluginMeta.condition" label="AND">满足全部条件</el-radio>
                            <el-radio v-model="pluginMeta.condition" label="OR">满足任意条件</el-radio>
                        </el-col>
                    </el-row>
                    <el-row class="ce-cont_pd mb10 " >
                        <el-col :span="12" >
                            <div class="tabelName" :title="tableInfo.left.tableName">来源表：{{tableInfo.left.tableName}}</div>
                        </el-col>
                        <el-col :span="12">
                            <div class="tabelName" :title="tableInfo.right.tableName">关联表：{{tableInfo.right.tableName}}</div>
                        </el-col>
                    </el-row>
                    <el-row v-for="(joinField, joinIndex) in joinFieldList"
                            :key="joinIndex"
                            class="ce-cont_pd mb10"
                            :gutter="10">
                        <el-col :span="10">
                            <el-select v-model="joinField.left.code"
                                       filterable
                                       placeholder="请选择"
                                       @change="joinFieldChange(joinField.left.code , 'left' , joinIndex)"
                            >
                                <el-option
                                        v-for="field in leftTable.fields"
                                        :key="field.colCode"
                                        :label="field.colName"
                                        :value="field.colCode">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="1.5">=</el-col>
                        <el-col :span="10">
                            <el-select v-model="joinField.right.code"
                                       filterable
                                       placeholder="请选择"
                                       @change="joinFieldChange(joinField.right.code , 'right' ,joinIndex)"
                            >
                                <el-option
                                        v-for="field in rightTable.fields"
                                        :key="field.colCode"
                                        :label="field.colName"
                                        :value="field.colCode">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="2">
<!--                            <i v-if="joinIndex === 0"-->
<!--                               class="icon icon__btn fl"-->
<!--                               title="新增"-->
<!--                               @click.stop="handleAddJoinField"-->
<!--                            >&#xe6e1;</i>-->
                            <i
                               class="el-icon-close"
                               title="删除"
                               @click.stop="handleDeleteJoinField(joinIndex)"
                            ></i>
                        </el-col>
                    </el-row>
                    <el-row  class="ce-cont_pd">
                        <i class="el-icon-circle-plus-outline" @click="handleAddJoinField" style="color: #0088FF;">添加关联条件</i>
                    </el-row>
                </template>
                <div v-if="attr.type === 'result'"
                     class="ce-cont_pd">
                    <el-button type="primary" class="mainButton" @click="show()">选择输出字段</el-button>
                </div>
            </el-form-item>
        </el-form>
        <common-dialog

                class="params-dialog"
                :title="title"
                :width="width"
                :visible.sync="visible"
                @closed="clear_data"
                v-loading="loading"
        >
            <operationDialogParam   ref="dialog" v-if="showParams" :tableInfo="tableInfo" :leftTable="leftTable"
                                  :rightTableC="rightTable" :leftSelected="leftSelected"
                                  :rightSelected="rightSelected" @updateOutput="updateOutput" @cancel="cancel" :btnActive="btnActive"></operationDialogParam>
            <div slot="footer"  >
                <el-button @click="canceled">取 消</el-button>
                <el-button type="primary" @click="submit">确定</el-button>
            </div>

        </common-dialog>

        <preView ref="preview"></preView>
    </div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import operationDialogParam from "./operationDialogParam";
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    export default {
        name: "cicadaInnerJoinPlugin",
        mixins : [pluginMixins , servicesMixins],
        components: {
            operationDialogParam,
            preView
        },
        props: {
            sourceData: Object,
            rowData : Object,
            loadParams: Object,
        },
        data() {
            return {
                btnActive: -1,
                leftSelected:[],
                rightSelected:[],
                title: "添加输出字段",
                attrsList: [ //名字列表
                    {
                        label: '关联条件',
                        type: 'data_obj',
                        value: ''
                    },
                    {
                        label: '选择输出字段',
                        type: 'result',
                        value: '',
                        // tip : "若来源表出现重名字段,请修改字段别名,否则默认只保留一个字段"
                    }
                ],
                tableInfo: { //左右表
                    left : {
                        tableName : ''
                    },
                    right : {
                        tableName : ''
                    }
                },
                joinFieldList: [  //连接字段
                    //{ left: '', right: '' }
                ],
                leftTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                rightTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                result: [],
                pluginMeta: {}
            }
        },
        methods: {
            canceled() {
                this.$refs.dialog.cancel();
            },
            submit() {
                this.$refs.dialog.submit();
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            save(saveName,showTip) {
                if(this.tableInfo.left.tableName === "") {
                    this.$message.warning("输入源必须为两个");
                    return;
                }
                if(this.joinFieldList.length === 0) {
                    this.$message.warning("请选择连接字段");
                    return;
                }
                if(this.result.length === 0) {
                    this.$message.warning("请选择输出字段");
                    return;
                }
                const vm = this, {pluginServices, pluginMock, loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                for(let i = 0; i < vm.result.length; i++) {
                    for(let j = i + 1; j < vm.result.length; j++) {
                        if(vm.result[i].colCode === vm.result[j].colCode) {
                            vm.$message.warning(`存在相同的字段名 "${vm.result[i].colCode}",请修改配置`);
                            return;
                        }
                        if(vm.result[i].colName === vm.result[j].colName) {
                            vm.$message.warning(`存在相同的字段中文名 "${vm.result[i].colName}",请修改配置`);
                            return;
                        }

                        // if(vm.result[i].colCode === vm.result[j].colCode) {
                        //     vm.result[i].replace = vm.result[i].belong === 'left' ? vm.result[i].colCode : vm.result[i].colCode + "1"
                        //     vm.result[j].replace = vm.result[j].belong === 'left' ? vm.result[j].colCode : vm.result[j].colCode + "1"
                        // }
                        // if(vm.result[i].colName === vm.result[i].colName) {
                        //     vm.result[i].colName = vm.result[i].belong === 'left' ? vm.result[i].colName : vm.result[i].colName + "1"
                        //     vm.result[j].colName = vm.result[j].belong === 'left' ? vm.result[j].colName : vm.result[j].colName + "1"
                        // }
                    }
                }
                loadParams.loading = true;
                vm.pluginMeta.cicadaAggregatorFieldExps = [];
                vm.result.forEach(item => {
                    vm.pluginMeta.cicadaAggregatorFieldExps.push({
                        objId: item.tableId,
                        objCode: item.tableCode,
                        replaceFieldName: item.colCode,
                        newFieldName: item.colCode,
                        valType: item.type,
                        length: item.length,
                        precision: item.precsn,
                        columnZhName: item.colName
                    })
                });

                vm.pluginMeta.cicadaAggregatorJoinColumns = [];
                vm.joinFieldList.forEach((value, index) => {
                    vm.pluginMeta.cicadaAggregatorJoinColumns.push({
                        index:index,
                        leftCol:value.left.code,
                        leftId:value.left.id,
                        rightCol:value.right.code,
                        rightId:value.right.id,
                    })
                });
                services.saveCicadaInnerJoinPlugin(vm.sourceData.id, vm.pluginMeta ,loadParams).then(result => {
                    if(result.data.status === 0){
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                        showTip && vm.$message.success("保存成功！");
                    }
                })
            },
            cancel() {
              this.clear_data();
              this.close();
            },
            updateOutput(data) {
                const that = this;
                for(let i = 0; i < data.left.length; i ++) {
                    for(let j = 0; j < that.leftTable.fields.length; j++) {
                        if(that.leftTable.fields[j].colCode === data.left[i].colCode) {
                            that.leftTable.fields[j].tempColName = JSON.parse(JSON.stringify(data.left[i].colName));
                            break;
                        }
                    }
                }
                for(let i = 0; i < data.right.length; i ++) {
                    for(let j = 0; j < that.rightTable.fields.length; j++) {
                        if(that.rightTable.fields[j].colCode === data.right[i].colCode) {
                            that.rightTable.fields[j].tempColName = JSON.parse(JSON.stringify(data.right[i].colName));
                            break;
                        }
                    }
                }
                this.leftSelected = data.left;
                this.rightSelected = data.right;
                this.result = data.result;
                // this.leftTable.fields = data.leftT.fields;
                // this.rightTable.fields = data.rightT.fields;
                this.clear_data();
                this.close();
            },
            showTip() {
                this.$message.success("提示")
            },
            joinFieldChange(val, side, inx) {
                let id;
                let code;
                this[side + 'Table'].fields.forEach(data => {
                    if (data.colCode === val) {
                        id = data.id;
                        code = data.colCode;
                    }
                });
                this.joinFieldList[inx][side].id = id;
                this.joinFieldList[inx][side].code = code;
            },
            handleAddJoinField() { //连接字段
                const {leftTable, rightTable, joinFieldList} = this;
                let item = {left: {code: '', id: ''}, right: {code: '', id: ''}};
                if (leftTable.fields && leftTable.fields.length > 0) {
                    // item.left = leftTable.fields[0].colCode;
                    item.left = {
                        code: leftTable.fields[0].colCode,
                        id: leftTable.fields[0].id,
                    }
                }
                if (rightTable.fields && rightTable.fields.length > 0) {
                    // item.right = rightTable.fields[0].colCode;
                    item.right = {
                        code: rightTable.fields[0].colCode,
                        id: rightTable.fields[0].id,
                    }
                }
                joinFieldList.push(item)
            },
            handleDeleteJoinField(index) {//删除连接字段
                const {joinFieldList} = this;
                joinFieldList.splice(index, 1);
            },
            initData() {   //初始化
                const vm = this, {pluginServices, pluginMock, settings, loadParams} = this;
                loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                services.cicadainnerJoinPluginPage(vm.sourceData.id, loadParams).then(result => {
                    if (result.data.status === 0) {
                        let leftSelCol = [];
                        let rightSelCol = [];
                        result.data.data.cicadaAggregatorFieldExps.forEach(exp => {
                            if (exp.objCode.includes("_left")) {
                                leftSelCol.push({expName:exp.newFieldName, zhName: exp.columnZhName});
                            }
                            if (exp.objCode.includes("_right")) {
                                rightSelCol.push({expName:exp.newFieldName, zhName: exp.columnZhName});
                            }
                        });

                        result.data.data.columns.leftTableColumns.forEach(leftCol => {
                            let item = {
                                colCode: leftCol.columnName,
                                colName: leftCol.columnZhName === "" ? leftCol.columnName : leftCol.columnZhName,
                                id: leftCol.dataColumnId,
                                type: leftCol.columnType,
                                length: leftCol.length,
                                precsn: leftCol.precsn,
                                tempColName: leftCol.columnZhName === "" ? leftCol.columnName : leftCol.columnZhName,
                            };

                            let itemCopy = JSON.parse(JSON.stringify(item));
                            leftSelCol.forEach(temp => {
                                if(temp.expName === leftCol.columnName|| temp.expName === leftCol.columnZhName) {
                                    item.tempColName = temp.zhName;
                                    itemCopy.colName = temp.zhName;
                                    vm.leftSelected.push(itemCopy)
                                }
                            })
                            vm.leftTable.fields.push(item);
                        });
                        result.data.data.columns.rightTableColumns.forEach(rightCol => {
                            let item = {
                                colCode: rightCol.columnName,
                                colName: rightCol.columnZhName === "" ? rightCol.columnName : rightCol.columnZhName,
                                id: rightCol.dataColumnId,
                                type: rightCol.columnType,
                                length: rightCol.length,
                                precsn: rightCol.precsn,
                                tempColName: rightCol.columnZhName === "" ? rightCol.columnName : rightCol.columnZhName
                            };
                            let itemCopy = JSON.parse(JSON.stringify(item));
                            rightSelCol.forEach(temp => {
                                if(temp.expName === rightCol.columnName || temp.expName === rightCol.columnZhName) {
                                    item.tempColName = temp.zhName;
                                    itemCopy.colName = temp.zhName;
                                    vm.rightSelected.push(itemCopy);
                                }
                            })
                            vm.rightTable.fields.push(item);
                        });
                        // 设置表名
                        vm.tableInfo = {
                            left: {
                                tableName: result.data.data.pluginMeta.leftStepName,
                                tableCode: result.data.data.pluginMeta.leftStepCode,
                                tableId: result.data.data.pluginMeta.leftStepId
                            },
                            right: {
                                tableName: result.data.data.pluginMeta.rightStepName,
                                tableCode: result.data.data.pluginMeta.rightStepCode,
                                tableId: result.data.data.pluginMeta.rightStepId
                            }
                        };
                        vm.pluginMeta = result.data.data.pluginMeta;
                        // 初始化链接字段
                        vm.handleAddJoinField();
                        vm.pluginMeta.cicadaAggregatorJoinColumns.forEach((item, index) => {
                            if (index !== 0) {
                                vm.handleAddJoinField();
                            }
                            vm.joinFieldChange(item.leftCol, 'left', index);
                            vm.joinFieldChange(item.rightCol, 'right', index);
                        });


                        if (vm.tableInfo.left && vm.tableInfo.right) {
                            vm.leftSelected.forEach(item => {
                                vm.result.push({belongName: '来源表：' + vm.tableInfo.left.tableName, belong:'left', row: item,
                                    tableCode: this.tableInfo.left.tableCode,
                                    tableId: this.tableInfo.left.tableId,
                                    tableName: this.tableInfo.left.tableName,
                                    colCode: item.colCode,
                                    colName: item.colName,
                                    id: item.id,
                                    length: item.length,
                                    precsn: item.precsn,
                                    tempColName: item.tempColName,
                                    type: item.type});
                            });
                            vm.rightSelected.forEach(item => {
                                vm.result.push({belongName: '关联表：' + vm.tableInfo.right.tableName, belong:'right', row: item,
                                    tableCode: this.tableInfo.right.tableCode,
                                    tableId: this.tableInfo.right.tableId,
                                    tableName: this.tableInfo.right.tableName,
                                    colCode: item.colCode,
                                    colName: item.colName,
                                    id: item.id,
                                    length: item.length,
                                    precsn: item.precsn,
                                    tempColName: item.tempColName,
                                    type: item.type});
                            });
                        }

                        if(vm.pluginMeta.condition === "") {
                            vm.pluginMeta.condition = "AND"
                        }
                    }
                })
            }
        },
        computed:{
            plug_data() {

            },
            // result() {
            //     const {leftTable, rightTable, tableInfo} = this;
            //     let result = [];
            //     if (tableInfo.left && tableInfo.right) {
            //         leftTable.checked.forEach(item => {
            //             result.push(Object.assign({belongName: '左表：' + tableInfo.left.tableName, belong:'left', row: item}, tableInfo.left, item));
            //         });
            //         rightTable.checked.forEach(item => {
            //             result.push(Object.assign({belongName: '右表：' + tableInfo.right.tableName, belong:'right', row: item}, tableInfo.right, item));
            //         });
            //     }
            //     return result;
            // }
        },
        created() {
            this.initData();
            // this.isSeniorShow = {
            //     partitionShow: false,
            //     fieldInput: false,
            //     fieldOutPut: true,
            //     isCopyOrDispense: false,
            //     isSampleExpr: false,
            //     isThread: false,
            //     isException: false,
            //     notTransition:false,
            // }
        }
    }
</script>

<style scoped>
    .mainButton {
        width: 100%
    }

    .ce-label_title {
        font-size: 14px;
        color: rgba(0,0,0,0.65);
    }
    .ce-cont_pd {
        padding: 0 10px;
        font-size: 14px;
        color: rgba(0,0,0,0.85);
    }
    .tabelName {
        overflow:hidden;
        width: 110px;
        height: 21px;
        white-space: nowrap;
        text-align: left;
        text-overflow:ellipsis;
        color: rgba(0,0,0,0.45);
    }
</style>
