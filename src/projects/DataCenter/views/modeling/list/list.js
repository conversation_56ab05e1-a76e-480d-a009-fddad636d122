import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters,mapState} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {
    transList
} from "@/projects/DataCenter/views/modeling/dialog/rapid-analysis/model/model-panel/trans-list-mixins";
import Timing from "@/projects/DataCenter/views/modeling/flowPanel/timeing-page"
import createApi from "@/projects/DataCenter/views/serviceManage/dialog/createAPI"

export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common, listMixins, coustTableH, transList],
    props: {
        isCase: {
            type: Boolean,
            default: false,
        },
        renewListLoad: Function,
    },
    components: {
        Timing,
        createApi
    },
    watch: {
        "settings.loading": {
            handler(val) {
                this.renewListLoad(val);
            },
            deep: true
        }
    },
    computed: {
        ...mapGetters(["userRight","userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        },
        // 是其他用户
        isOtherUser(){
            return this.currentUserId !== this.userInfo?.id;
        },
        tHeadData(){
            const operate = this.isOtherUser ? [] : [
                {
                    prop: 'operate',
                    label: '操作',
                    minWidth: 230,
                    align: "center",
                    resizable: false
                }
            ];
            return [
                {
                    prop: "modelName",
                    label: "名称",
                    minWidth: 160,
                    align: "left"
                },
                {
                    prop: "path",
                    label: "所属目录",
                    minWidth: 120,
                    resizable: false
                },
                {
                    prop: "userName",
                    label: "创建人",
                    minWidth: 120,
                    align: "center"
                },
                {
                    prop: "editorAndDate",
                    label: "更新时间",
                    minWidth: 140,
                    align: "center",
                    sortable: false,
                    resizable: false
                },
                {
                    prop: "scheduleType",
                    label: "状态",
                    minWidth: 90,
                    align: "center",
                    resizable: false
                },
                {
                    prop: "end_time",
                    label: "最近运行时间",
                    minWidth: 160,
                    resizable: false,
                    align: "center",
                },
                ...operate,
            ];
        },
        ...mapState({
            // 他人空间目录 Id
            otherUserCatalogId: (state) => state.plans.otherUserCatalogId,
        }),
    },
    data() {
        return {
            rowData: null,
            modelTip: '提供全程可视化的模型搭建，通过拖拽的交互方式，采用引导式及“搭积木”般的画图式界面帮助用户实现数据资源、组件的快速组合，快速完成数据分析模型的搭建。',
            analyseTip: "提供实时在线分析能力，适用于快速搭建小数据量的数据碰撞和数据统计场景的业务模型，建模结果不落地存储。",
            inputValueTable: "",
            buttonTxt: "流程建模",
            tableData: [],
            // 版本、开发商
            customHeadData: [
                {
                    prop: "version",
                    label: "版本",
                    align: "center",
                    minWidth: 120
                },
                {
                    prop: "productionfirm",
                    label: "开发商",
                    align: "center",
                    minWidth: 120
                },
            ],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe650;",
                    name: "运行",
                    clickFn: this.carryOut,
                    show: (row) => !(['running', 'ongoing'].includes(row.execute_status.toLowerCase())),
                    condition: (right) => right.indexOf($right["processModelingStartJob"]) > -1
                },
                {
                    icon: "&#xe650;",
                    name: "停止",
                    clickFn: this.model_stop,
                    show: (row) => ['running', 'ongoing'].includes(row.execute_status.toLowerCase()),
                    condition: (right) => right.indexOf($right["processModelingStartJob"]) > -1
                },
                {
                    icon: "&#xe6c0;",
                    name: "编辑",
                    clickFn: this.editModel,
                    show: () => true,
                    condition: (right) => right.indexOf($right["processModelingResultReuseObj"]) > -1
                },
                {
                    icon: "&#xe605;",
                    name: "重命名",
                    clickFn: this.reName,
                    show: () => true,
                    condition: (right) => right.indexOf($right["processModelingUpdateTransName"]) > -1
                },
                // {
                //     icon: "&#xe656;",
                //     name: "生成API",
                //     clickFn: this.createAPI,
                //     show: () => true,
                //     condition: (right) => right.indexOf($right["modelServiceGenerateAPI"]) > -1
                // },
                {
                    icon: "&#xe656;",
                    name: "定时作业",
                    clickFn: this.setTiming,
                    show: () => true,
                    condition: (right) => right.indexOf($right["processModelingSaseSettingPage"]) > -1
                },
                /*{
                    icon: "<i class='el-icon-share'></i>",
                    name: "分享",
                    clickFn: this.share,
                    condition: (right) => true
                },*/
                /* {
                     name: "转建模方案",
                     clickFn: this.changeToModeling,
                     show: (right , row) =>row.taskgroup === "QUICK_SPARK",
                     condition: (right) => true
                 },*/
                {
                    icon: "&#xe6f4;",
                    name: "另存为",
                    clickFn: this.copySolution,
                    show: () => true,
                    condition: (right) => right.indexOf($right["processModelingCopyTrans"]) > -1
                },
                // {
                //     icon: "&#xe790;",
                //     name: "预览结果集",
                //     clickFn: this.previewResult,
                //     show: () => true,
                //     condition: (right) => right.indexOf($right["processModelingGetDataSets"]) > -1
                // },
                {
                    icon: "&#xe6bf;",
                    name: "移动到",
                    clickFn: this.moveTo,
                    show: () => true,
                    condition: (right) => right.indexOf($right["processModelingQueryTransTree"]) > -1
                },/*{
                    icon: "&#xe856;",
                    name: "移交",
                    clickFn: this.transfer,
                    condition: (right) => true
                },
                {
                    icon: "&#xe886;",
                    name: "发布",
                    clickFn: this.serveIssue,
                    condition: () => true
                },*/
                //分割线
                /*{
                    name: "divider",
                    show: () => true,
                    condition: (right) => (right.indexOf("processModelingCopyTrans") > -1||right.indexOf("processModelingGetDataSets") > -1||right.indexOf("processModelingQueryTransTree") > -1)&&right.indexOf("processModelingDeleteTrans") > -1
                },*/
                {
                    icon: "&#xe65f;",
                    name: "删除",
                    clickFn: this.deleteModel,
                    show: () => true,
                    condition: (right) => right.indexOf($right["processModelingDeleteTrans"]) > -1
                },
            ],
            dirId: "-1",
            dirType: "TRANS_DIR_MF",
            currentUserId: '',// 树节点传参过来的用户 Id
            addModelR: $right['processModelingSaveTempTrans'],
            analyseR: $right['processModelingRapidAnalysis'],
            lookOverR: $right['dataMiningLookOver'],
            analyseTxt: "快速分析",
            outputTxt: "导出模型",
            inputTxt: "导入模型",
            states: [
                {type: 'none', text: '暂无状态', icon: 'icon icon-state'},
                {type: 'success', text: '任务完成', icon: 'icon icon-state'},
                {type: 'error', text: '异常', icon: 'icon icon-state'},
                {type: 'ongoing', text: '执行中', icon: 'el-icon-loading'},
            ],
            statusList: [
                {label: '全部', value: ""},
                {label: '运行成功', value: 'SUCCESS'},
                {label: '运行失败', value: 'ERROR'},
                {label: '运行中', value: 'RUNNING'},
                {label: '未执行', value: 'FAILED'},
            ],
            state: "",
            intervalId: null,
            leavePage: false, //页面离开
        }
    },
    methods: {
        initPage() {
            const {tHeadData, customHeadData} = this;
            if (this.$globalConfig.showVersionAndFirm) {
                tHeadData.splice(tHeadData.length - 1, 0, ...customHeadData)
            }
        },
        operateIconShow(row) {
            return this.hasRightOptIcon.filter(li => li.show(row))
        },
        transStatus(val) {
            const vm = this, {states} = vm;
            let type = val ? (val.toUpperCase() === 'RUNNING' ? 'ongoing' : val) : 'none';
            let modelStatus = states.find(item => item.type === type.toLowerCase());
            let icon = modelStatus.icon + ' status-' + modelStatus.type, {text} = modelStatus;
            return {icon, text};
        },
        /**
         * 转建模
         * @param row
         * @param index
         */
        changeToModeling(row, index) {
            const vm = this;
            vm.confirm('转建模方案', `确定将快速分析 "${row.modelName}" 转为建模方案`, async () => {
                await vm.getTransList(row);
            })
        },
        async getTransList(rowData) {
            const vm = this, {services, settings} = this;
            settings.loading = true;
            const {data} = await services.loadTransPage(rowData.transId);
            if (data.status === 0) {
                const resD = data.data;
                let allNodes = [];
                let firstTransId = resD.firstTransId;
                vm.setLineList(resD);
                allNodes = await vm.setAllNodes(resD.transMetaVo.children);
                await vm.pushNodeByLine(resD.transMetaVo.hops, allNodes, firstTransId);
                let plugins = vm.setNodesPosition(allNodes);
                const result = await services.quickTransToProcessTrans(rowData.transId, plugins);
                if (result.data.status === 0) {
                    vm.$message.success("转换成功");
                }
                vm.changePage(1);
            }
        },
        /**
         * 生成API
         */
        createAPI(row) {
            const vm = this;
            vm.rowData = row;
            let services = vm.$services("modeling");
            //如果有数据集默认数据查询，如果没有默认模型分析
            services.getDataSets(row.transId).then(res => {
                if (res.data.status === 0) {
                    this.rowData.dataSetList = res.data.data || [];
                } else {
                    this.rowData.dataSetList = [];
                }
                this.$refs.createAPI.show();
            })
        },

        /**
         * 导入
         */
        inputModel() {
        },
        /**
         * 导出
         */
        outputModel() {
        },
        /**
         * 快速分析
         */
        rapidAnalysis() {
            const vm = this;
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/rapid-analysis/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    dirId: this.dirId
                },
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab({}, false, this.dirId);
        },
        serveIssue(row) {
            this.$emit("serveIssue", row)
        },
        share(row) {
            this.$emit("share", row);
        },
        transfer(row) {
        },
        previewResult(row) {
            this.$emit("previewResult", row);
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
        deleteInit(data) {
            this.currentUserId = data.currentUserId;
            this.dirId = data.id;
            this.dirType = data.name === "我的空间" ? "TRANS_DIR_MF" : data.dirType;
            this.changePage(1);
        },
        update() {
            this.changePage(this.currentPage);
        },
        // 定时刷新数据函数
        refreshTransStatus() {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            if (vm.leavePage) return;
            if (vm.intervalId) clearTimeout(vm.intervalId);
            let ids = vm.tableData.filter(p => p.execute_status && ['running', 'ongoing'].includes(p.execute_status.toLowerCase())).map(n => n.transId);
            if (!ids.length) return
            vm.intervalId = setTimeout(() => {
                services.refreshTransStatus({transIds: ids}).then(res => {
                    let resulet = res.data.data;
                    resulet.transStatusList && resulet.transStatusList.length && resulet.transStatusList.forEach(n => {
                        let info = vm.tableData.find(k => k.transId === n.transId);
                        if (info) {
                            info.execute_status = n.executeStatus;
                            info.end_time = n.endTime;
                        }
                    })
                    vm.refreshTransStatus();
                })
            }, 3000)
        },
        changePage(index) {
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                pageSize: vm.paginationProps.pageSize,
                pageNum: index,
                condition: vm.inputValueTable,
                dirId: vm.dirId,
                dirType: vm.dirType,
                state: vm.state,
                operatorId: vm.currentUserId,
            };
            vm.tableData = [];
            services.queryTransList(data, settings).then(res => {
                if (res.data.status === 0) {
                    let callbackData = res.data.data;
                    vm.tableData = [];
                    if (callbackData.data.length) {
                        callbackData.data.forEach(item => {

                            let data = {
                                ...item,
                                modelName: item.name,
                                userName: item.userName,
                                editorAndDate: item.releasetime,
                                releaseDate: item.releasetime,
                                modelType: item.classifyname,
                                modelSpec: item.memo,
                                statusCode: 0,//&#xe66b
                                transId: item.id,
                                dirParentId: item.parentid,
                                isNew: false,
                                execute_status: item.execute_status || 'none',
                                scheduleType: item.scheduleType === 'hand' ? '手工调度' : '自动调度',
                                end_time: item.end_time,
                                taskgroup: item.taskgroup,
                                path: item.path,
                                isReference: item.isReference,
                            };
                            vm.tableData.push(data);
                        });
                        vm.total = callbackData.totalCount;
                        vm.refreshTransStatus();
                    }
                    vm.$nextTick(() => {
                        vm.triggerEvent('resize', 500)
                    })
                }
            })

        },
        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        openNewModel() {
            // this.$emit('openNewModel' , this.dirId);
            const vm = this;
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: this.dirId
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab({}, false, this.dirId);
        },
        //停止
        model_stop(row) {
            const vm = this, {modelingServices, modelingMock} = vm;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.confirm("提示", '当前模型有任务正在运行中，是否停止运行？', () => {
                let data = {
                    transId: row.transId
                }
                services.taskStop(data).then(res => {
                    vm.changePage(vm.paginationProps.currentPage);
                })
            })
        },
        //操作
        carryOut(row) {
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let layer = vm.$dgLayer({
                title: '运行设置',
                content: require("@/components/common/schedulingChoice/index.vue"),
                props: {
                    value: '',
                },
                area: ["400px", "230px"],
                move: false,
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    },
                    run(engine) {
                        vm.confirm('提示', '此操作将执行任务, 是否继续?', () => {
                            settings.loading = true;
                            vm.$message.success("开始执行！");
                            services.runJob(row.transId, engine).then(res => {
                                settings.loading = false;
                                console.log(res.data.data);
                                vm.changePage(vm.paginationProps.currentPage);
                            }).catch(p => {
                                settings.loading = false;
                            })
                        })
                        layer.close(layer.dialogIndex);
                    }
                },
            })
        },
        startJob(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            services.startJob(row.transId).then(res => {
                if (res.data.status === 0) {
                    vm.$message({
                        type: 'success',
                        message: "开始执行",
                    })
                }
            })
        },
        editModel(row) {
            // if(row.isReference){
            //     return this.$message.warning("方案被服务引用，无法进行编辑")
            // }
            // this.$emit("modelEdit", row);
            let page;
            if (row.taskgroup === "QUICK_SPARK") {
                page = require("@/projects/DataCenter/views/modeling/dialog/rapid-analysis/index.vue");
            } else {
                page = require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue");
            }
            const vm = this;
            let layer = this.$dgLayer({
                content: page,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: this.dirId,
                    isCase: this.isCase,
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab(row, false);
        },
        /**
         * 定时运行
         * @param row
         */
        setTiming(row) {
            this.rowData = row;
            this.$refs.timing.show();
        },
        copySolution(row) {
            this.$emit("saveAs", row);
            /*  const vm = this, {modelingServices, modelingMock} = this;
             let services = vm.getServices(modelingServices, modelingMock);
             vm.confirm("复制", `确认复制 \'${row.modelName}\' 方案？`, () => {
                 services.copyTrans(row.transId, row.dirParentId).then(res => {
                     if (res.data.status === 0) {
                         vm.$message({
                             type: 'success',
                             message: "复制成功",
                         })
                         this.changePage(1);
                     }
                 })
             }) */

        },
        reName(row, index) {
            this.$emit("reName", row, index);
        },
        moveTo(row) {
            this.$emit("moveTo", row, "dataCenter", "move");
        },
        deleteModel(row) {
            if (row.isReference) {
                return this.$message.warning("方案被服务引用，无法删除")
            }
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);

            vm.confirm('提示', `此操作将删除 \"${row.modelName}\" 方案，是否继续?`, () => {
                services.deleteTrans(row.transId).then(res => {
                    if (res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!',
                        });
                        vm.changePage(1);
                    }
                })
            })
        },
        setDirType(value, id, node) {
            if (id === "0") {
                this.dirId = "-1";
                this.dirType = "TRANS_DIR_MF";
            } else if (
                value === "我的空间" && node.data.pId === "0" ||
                node.parent.data.id === this.otherUserCatalogId
            ) { // 我的空间，他人空间顶层目录
                this.dirId = id;
                this.dirType = "TRANS_DIR_MF";
            } else if (value === "标准模型") {
                this.dirId = id;
                this.dirType = node.data.dirType;
            } else { // 其他目录
                this.dirId = id;
                this.dirType = value;
            }
            this.currentUserId = node.data.currentUserId;
            this.changePage(1);
        },
        tDataChangeWithName(newName, index) {
            const vm = this;
            vm.tableData.forEach((tab, i) => {
                if (i === index) {
                    tab.modelName = newName
                }
            });
        },
        getJobStatus() {
            this.tableData.forEach((item, i) => {
                this.$axios.get("/transSchedule/jobStatus?transId=" + item.transId)
                    .then(res => {
                        // console.log("状态码：",res.data,this.tableData.length);
                        // item.statusCode = res.data.status;
                        if (res.data.status === 0) {
                            if (res.data.data.status === "none") {
                                item.statusCode = 0;
                            } else if (res.data.data.status === "success") {
                                item.statusCode = 1;
                            } else if (res.data.data.status === "execing") {
                                item.statusCode = 5;
                            } else {
                                item.statusCode = 2;
                            }
                        }

                    })
                    .catch(err => {
                        console.log(err);
                    })
            });
        },
        //模型详情
        detailShow(row) {
            if (this.rights.indexOf("dataMiningLookOver") === -1) {
                this.$message.warning("无功能访问权限");
                return;
            }
            if (!row) return
            const vm = this, {isOtherUser} = vm;
            let url = "";
            url = require("@/projects/DataCenter/views/modeling/dialog/details/index.vue");
            let layer = vm.$dgLayer({
                content: url,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    row: row,
                    isOtherUser,
                },
                on: {
                    close() {
                        //vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
        },
        clearTimer() {
            if (this.intervalId) {
                clearTimeout(this.intervalId);
            }
            this.intervalId = null;
            this.leavePage = true;
        }
    },
    created() {
        this.initPage();
        // this.changePage(1);
    },
    beforeDestroy() {
        this.clearTimer();
    }
}
