import {pinyin} from "@/api/pinyin"

export const treeMethods = {
    data() {
        return {
            selectedNode: {},
            selectedData: {},
            selectedParent: {},
            maxLength: 25,
            expandData: [],
            renameFn: null
        }
    },
    methods: {
        /**
         * 重命名 提交
         */
        submit() {
            const vm = this, {renameFn} = vm;
            let {name , oldName} = vm.$refs.rename.form;
            if(name === oldName){
                vm.$message.success("保存成功!");
                vm.visible = false;
                return;
            }
            vm.$refs.rename.validate(valid => {
                if (valid) {
                    renameFn && renameFn();
                    vm.visible = false;
                }
            })
        },
        /**
         * 重命名 弹窗
         * @param title
         * @param value
         * @param fn
         * @param tip
         * @param isNew
         * @param ruleMes
         */
        showReName(title, value, fn, tip, isNew = false, ruleMes = "名称最大不超过25个字符。只允许除/、\\、<、>等特殊字符以外。") {
            const vm = this, {maxLength, treeProps} = vm;
            vm.visible = true;
            vm.reNew = true;
            vm.title = title;
            let data = isNew || vm.selectedNode.level <= 1 ? vm.data : vm.selectedNode.parent.data[treeProps.children];
            let placeholder = `请输入${tip}(限${maxLength}个字符)`,
                curData =  isNew ?  {} : vm.selectedData;
            vm.renameFn = function () {
                return fn();
            };
            vm.$nextTick(() => {
                vm.$refs.rename.initVal({
                    placeholder,
                    label: tip,
                    ruleMes,
                    name: value,
                    maxLength,
                    data,
                    treeProps,
                    curData
                });
            })
        },
        /**
         * 设当前节点
         * @param object
         */
        setCurrentNode(object) {
            this.$refs.tree.setCurrentNode(object);
        },
        /**
         * 当前节点
         * @param key
         */
        setCurrentKey(key) {
            this.$refs.tree.setCurrentKey(key);
        },
        /**
         * 编辑菜单
         * @param node
         * @param object
         */
        showMenu(node, object) {
            this.selectedNode = node;
            this.selectedData = object;
            this.getRootTree(node);
        },
        /**
         *
         * @param data
         * @param id
         */
        hideTreeMenu(data, id) {
            data.forEach(item => {
                if (item.id !== id) item.isActive = false;
                if (item.children && item.children.length) this.hideTreeMenu(item.children, id);
            })
        },
        /**
         * 树 菜单获取
         * @param event
         * @param node
         * @param object
         */
        rightMenu(event, node, object) {
            this.hideTreeMenu(this.data, object.id);
        },
        /**
         * 菜单事件
         * @param command
         * @param node
         * @param data
         */
        menuCommand(command, node, data) {
            if (data.isActive !== undefined) data.isActive = false;
            this.showMenu(node, data);
            command.fn(data);
        },
        /**
         * 获取根目录
         * @param node
         */
        getRootTree(node) {
            while (node.level >= 1) {
                node = node.parent;
            }
            this.selectedParent = node.data;
        },
        /**
         * 获取操作节点数据
         * @return {{data: {}, children: *}}
         */
        getTreeNode() {
            let node = this.selectedNode,
                data = this.selectedData;
            const parent = node.parent;
            const children = parent.data.children || parent.data;
            return {data, children};
        },
        /**
         * 删除的confirm
         * @param msg
         * @param title
         * @param callback
         */
        confirm(msg, title, callback) {
            this.$dgAlert(msg + '?', title, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true,
            }).then(() => {
                callback();
            }).catch(() => {
            });

        },
        /**
         * 过滤方法
         * @param value
         * @param data
         * @param node
         * @param props
         * @return {boolean}
         */
        filterNode(value, data, node, props) {
            const vm = this;
            if (typeof value !== 'string' && value.trim() !== '') {
                return true;
            }
            if (node.childNodes.length > 0) { // 非叶子节点直接不展示，由叶子节点决定是否展示
                return false;
            } else {
                let defaultProps = props || vm.defaultProps;
                value = value.toLowerCase();
                while (node.level > 0) {
                    let label = node.data[defaultProps.label] || '';
                    if (label.toLowerCase().includes(value)) { // 编辑节点一定会展示
                        return true;
                    }
                    node = node.parent; // 本身没匹配，查找父级
                }
                return false;
            }
        },
        /**
         * 按字母排序 (升序)
         * @param data
         * @param key
         * @param way 排序方式 true 升 false 降
         * @return {*}
         */
        sortInLetter(data, key, way) {
            return data.sort(function (s, t) {
                let a = s[key] && pinyin.getCamelChars(s[key]).toLowerCase();
                let b = t[key] && pinyin.getCamelChars(t[key]).toLowerCase();
                if (a < b) return way ? -1 : 1;
                if (a > b) return way ? 1 : -1;
                return 0;
            })
        },
        /**
         * 时间排序
         * @param data
         * @param key
         * @param way 排序方式 true 升 false 降
         */
        sortInTime(data, key, way) {
            return data.sort(function (a, b) {
                return a[key] < b[key] ? way ? -1 : 1 : way ? 1 : -1
            });
        },
        /**
         * tree 各层按 规定方式 排序 (首字母 sortInLetter ，时间 sortInTime)
         * @param tree
         * @param key
         * @param sortFn
         * @param way
         * @return {*}
         */
        treeSortByWay(tree, key, sortFn, way) {
            const vm = this;
            let result = tree.map(item => {
                item.children = item.children && item.children.length ? vm.treeSortByWay(item.children, key, sortFn, way) : [];
                return item;
            })
            return vm[sortFn](result, key, way);
        },
        /**
         * 设置默认展开层级
         * @param {*} data
         */
        setExpandData(data) {
            this.expandData.push(...data.map(item => item.id))
        },
    }
};
