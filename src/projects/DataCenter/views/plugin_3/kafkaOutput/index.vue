<template>
    <div class="ovh" v-loading="settings.loading">
        <el-form ref="form" :model="baseForm" label-position="right" label-width="150px" :rules="baseRules">
            <el-form-item v-for="(item , k) in baseCon" :key="k" :prop="k" :label="item.label + ' :'" v-if="item.show(baseForm)">
                <div v-if="item.type === 'radio'">
                    <el-radio-group v-model="baseForm[k]">
                        <el-radio v-for="rad in radioOpt" :key="rad.value" :label="rad.value" :disabled="rad.disabled">{{rad.label}}</el-radio>
                    </el-radio-group>
                </div>
                <div v-if="item.type === 'select'">
                    <dg-select
                        v-model="baseForm[k]"
                        class="selectC"
                        :data="item.option"
                        output-format="String"
                        @change="handleChange"
                    />
                </div>
                <div v-if="item.type === 'input'">
                    <el-input
                        v-model="baseForm[k]"
                    />
                </div>
                <div v-if="item.type === 'mulcSelect'">
                    <dg-select
                        v-model="baseForm[k]"
                        class="selectC"
                        multiple
                        default-first-option
                        filterable
                        allow-create
                        :data="item.option"
                        output-format="String"
                        @change="handleChange"
                    />
                </div>
                <div v-if="item.type === 'kafkaTree'">
                    <dg-tree-drop
                            class="selectC"
                            clearable
                            check-strictly
                            ref="pos_tree"
                            :placeholder="item.placeholder"
                            :props="item.defaultProps"
                            filterable
                            :filterNodeMethod="filterNode"
                            v-model="baseForm[k]"
                            :data="item.option"
                            @node-click="selectTreeChange($event, k)"
                    ></dg-tree-drop>
                </div>

            </el-form-item>
        </el-form>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins"
    export default {
        name: "kafkaProduction",
        mixins: [common, commonMixins, servicesMixins, treeMethods],
        props: {
            sourceData: Object,
            rowData: Object,
            previewObj:Object,
        },
        components: {
            PreView,
        },
        data(){
            return {
                tHeadData : [],
                tableData : [],
                radioOpt : [
                    {
                        value : '0',
                        label : '不路由',
                        disabled : false,
                    },{
                        value : '1',
                        label : '路由',
                        disabled : false,
                    }
                ],
                baseForm: {
                    routingStrategyCode : "1",
                    topic : "type",
                    kafkaSourceId : "",
                    retries : "**********",
                    acks : "1",
                    kafkaSourceName : "",
                },
                baseCon: {
                    routingStrategyCode :{
                        type: 'radio',
                        label: "路由策略",
                        placeholder: "请输入路由字段",
                        option: [],
                        show :()=> true,
                    },
                    topic: {
                        type: 'input',
                        label: "路由字段",
                        placeholder: "请输入路由字段",
                        option: [],
                        show :()=> true,
                    },
                    kafkaSourceId: {
                        type: 'kafkaTree',
                        label: "kafka实例名称",
                        placeholder: "请选择kafka实例名称",
                        option: [
                            {value: "f1a9cb13ac7c439abd0fdc4de16e5ea3", id: "f1a9cb13ac7c439abd0fdc4de16e5ea3", label: "kafka测试"}
                        ],
                        defaultProps: {
                            children: "children",
                            label: "name",
                            value: "schemaId",
                            disabled : 'parent'
                        },
                        show :()=> true,
                    },
                    retries: {
                        type: 'input',
                        label: "消息失败重试次数",
                        placeholder: "请输入消息失败重试次数",
                        option: [],
                        show :()=> true,
                    },
                    acks: {
                        type: 'select',
                        label: "消息的确认模式",
                        placeholder: "请选择消息的确认模式",
                        option: [
                            {
                                value : '1',
                                label : '1',
                            }, {
                                value : '0',
                                label : '0',
                            }, {
                                value : '-1',
                                label : '-1',
                            },
                        ],
                        show :()=> true,
                    },
                },
                baseRules: {
                    kafkaSourceId: [
                        {required: true, message: "请选择kafka实例名称", trigger: ['blur', 'change']}
                    ],
                    retries: [
                        {required: true, message: "请输入消息失败重试次数", trigger: ['blur', 'change']}
                    ],
                    acks: [
                        {required: true, message: "请选择消息的确认模式", trigger: ['blur', 'change']}
                    ],
                },

            }
        },
        methods : {
            filterNode: treeMethods.methods.filterNode,
            selectTreeChange(val, e) {
                this.baseForm.kafkaSourceId = val.schemaId;
                this.baseForm.kafkaSourceName = val.name;
            },
            handleChange() {

            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            save(fn,showTip) {
                const vm = this, {settings} = vm;
                let services = this.$services("plugin3");
                vm.baseForm.stepId = vm.sourceData.id,
                vm.$refs.form.validate((valid) => {
                    if (valid) {
                        settings.loading = true;
                        services.kafkaOutputStepConfigSaveOrUpdate({settings ,...vm.baseForm}).then(res => {
                            showTip && vm.$message.success("保存成功");
                            vm.$emit("setPreviewAble");
                            fn && fn();
                            //vm.$emit('reloadCanvas') //刷新画布
                        })
                    }
                })
            },
            async getKafkaList() {
                const vm = this, {settings} = vm;
                let services = this.$services("plugin3");
                settings.loading = true;
                await services.getKafkaInstanceTree({ settings }).then(res => {
                    vm.baseCon.kafkaSourceId.option = res.data.data;
                })
            },
            async getAcksType() {
                const vm = this, {settings} = vm;
                let services = this.$services("plugin3");
                settings.loading = true;
                await services.getAcksType({ settings }).then(res => {
                    vm.baseCon.acks.option = res.data.data.map(item => {
                        item.value = item.code,
                        item.label = item.name;
                        return item;
                    });
                })
            },
            /**
             * 详情回显
             */
            getDetail() {
                const vm = this , {settings} = vm;
                settings.loading = true;
                let services = this.$services("plugin3");
                services.getKafkaOutputDetail({stepId: vm.sourceData.id, settings }).then(res => {
                    
                    if(res.data.data.kafkaSourceId) {
                        vm.baseForm = res.data.data;
                        vm.baseForm .acks = res.acks === null ? '1' : res.data.data.acks;
                        vm.baseForm .retries = res.retries === null ? '1' : res.data.data.retries;
                    }
                })
            },
            async init() {
                await this.getKafkaList();
                await this.getAcksType();
                await this.getDetail();
                this.previewObj.isPreviewAble = !(this.baseForm.routingStrategyCode === '1');
                // if(this.baseForm.routingStrategyCode === '1') this.$emit('setPreviewAble');
                // debugger
            }
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped lang="less">
    

</style>
