<template>
    
        <div v-loading="settings.loading">
            <el-form :inline="true" >
                    <el-form-item label="服务方名称:" required>
                        <el-input placeholder="请输入(限100个字符):" class="input_style" v-model="serviceName" :disabled="serviceDetailShow" v-input-limit:trim maxlength="100"></el-input>
                    </el-form-item>
            </el-form>
            <!-- 总线请求报文头配置 -->
            <div class="ce-form_box">
                    <span class="a_style">总线请求报文头配置</span>
                    <dg-button type="primary" icon="el-icon-plus" @click="add" v-if="!serviceDetailShow">添加</dg-button>
            </div>
            <el-table
                    :data="requestTableData"
                    border
                    :row-style="{height:'10px'}"
                    :cell-style="{padding:'5px 0'}"
                >
                    <el-table-column prop="code" label="字段名称" width="286px;">
                        <template slot-scope="scope">
                            <a v-if="!scope.row.isDelete"  style="color:black;">
                                {{ scope.row.code }}
                            </a>
                            <el-input v-else v-model="scope.row.code" :disabled="serviceDetailShow" @input="codeCheck(scope.$index, scope.row)">

                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="字段中文名称" width="170px">
                        <template slot-scope="scope">
                            <a v-if="!scope.row.isDelete"  style="color:black;">
                                {{ scope.row.name }}
                            </a>
                            <el-input v-else v-model="scope.row.name" :disabled="serviceDetailShow">

                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="columnType" :resizable="false"  label="字段类型" :span="4">
                        <template slot-scope="scope">
                            <a v-if="!scope.row.isDelete"  style="color:black;">
                                {{ scope.row.columnType }}
                            </a>
                            <el-input v-else v-model="scope.row.columnType" :disabled="serviceDetailShow">

                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isRequired" label="是否必填"  width="100px">
                        <template slot-scope="scope">
                            <el-checkbox v-model="scope.row.isRequired" :disabled="!scope.row.isDelete||serviceDetailShow">

                            </el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column prop="defaultValue" label="默认值" width="190px">
                        <template slot-scope="scope">
                        
                            <el-select
                                :disabled="serviceDetailShow"
                                v-model="scope.row.defaultValue" 
                                v-if="scope.row.code === requestName" 
                                filterable
                                allow-create>
                                <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <el-input v-model="scope.row.defaultValue" v-else-if="scope.row.code === idCardName" @blur="IDCardValite(scope.row.defaultValue)" :disabled="serviceDetailShow">

                            </el-input>
                            
                            <el-date-picker
                                v-model="scope.row.defaultValue" 
                                style="width:155px;"
                                v-else-if="scope.row.code === 'timestamp'"
                                type="date"
                                format="yyyy - MM - dd "
                                value-format="yyyyMMddHHmmss"
                                placeholder="选择日期">
                            </el-date-picker>
                            <el-input v-model="scope.row.defaultValue" v-else-if="scope.row.code === userNameCode || scope.row.code === department" :disabled="serviceDetailShow" >

                            </el-input>
                            <el-input v-model="scope.row.defaultValue" v-else :disabled="serviceDetailShow" @input="changeCode(scope.row)">

                            </el-input>
                        </template>
                        
                    </el-table-column>
                    <el-table-column prop="deleteRow" align="center" label="操作" v-if="!serviceDetailShow">
                        <template slot-scope="scope">
                            <span class="" v-if="scope.row.isDelete">
                                <dg-button
                                        title="删除"
                                        type="text"
                                        @click="deleteLast(scope.$index, scope.row)"
                                        size="medium"
                                        icon="el-icon-delete"
                                ></dg-button>
                            </span>
                        </template>
                    </el-table-column>
            </el-table>
            <!-- 总线响应报文头配置 -->
            <div class="a_style pb10 pt15">总线响应报文头配置</div>
            <el-table
                    :data="responseTableData"
                    border
                    :row-style="{height:'10px'}"
                    :cell-style="{padding:'5px 0'}"
                >
                    
                    <el-table-column prop="code" label="字段名称" :span="3"></el-table-column>
                    <el-table-column prop="name" label="字段中文名称" :span="3"></el-table-column>
                    <el-table-column prop="columnType" :resizable="false"  label="字段类型" :span="3"></el-table-column>
                    <el-table-column prop="isRequired" label="是否必填"  :span="3">
                        <template slot-scope="scope">
                            <el-checkbox v-model="scope.row.isRequired" :disabled="scope.row.isRequired">

                            </el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column prop="defaultValue" label="默认值" :span="3">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.defaultValue" :disabled="serviceDetailShow" @input="changeCode(scope.row)">

                            </el-input>
                        </template>
                        
                    </el-table-column>
            </el-table>

        </div>
        
        
</template>
<script lang="js" src="./add-service.js"></script>
<style lang="less" scoped src="./add-service.less"></style>
