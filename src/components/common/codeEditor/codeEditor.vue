<template>
    <div class="in-coder-panel">
        <textarea ref="textarea" :placeholder="placeholder"></textarea>
    </div>
</template>
<style lang="less">
.in-coder-panel {
    flex-grow: 1;
    display: flex;
    position: relative;
    height: 100%;
    .CodeMirror {
        flex-grow: 1;
        z-index: 1;
        height: 100%;
        font-size: 16px;
        .CodeMirror-code {
            line-height: 26px;
        }
        .CodeMirror-placeholder {
            color: #999;
        }
    }
}
.CodeMirror-hints {
    z-index: 9999;
    max-width:500px;
    li {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

</style>

<script>
// 引入全局实例
import CodeMirror from 'codemirror'

// 核心样式
import 'codemirror/lib/codemirror.css'
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/3024-day.css'
import "codemirror/addon/hint/show-hint.css";
import "codemirror/theme/idea.css";
import "codemirror/theme/dracula.css";
// 需要引入具体的语法高亮库才会有对应的语法高亮效果
// codemirror 官方其实支持通过 /addon/mode/loadmode.js 和 /mode/meta.js 来实现动态加载对应语法高亮库
// 但 vue 貌似没有无法在实例初始化后再动态加载对应 JS ，所以此处才把对应的 JS 提前引入
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/mode/css/css.js'
import 'codemirror/mode/xml/xml.js'
import 'codemirror/mode/clike/clike.js'
import 'codemirror/mode/markdown/markdown.js'
import 'codemirror/mode/python/python.js'
import 'codemirror/mode/r/r.js'
import 'codemirror/mode/shell/shell.js'
import 'codemirror/mode/sql/sql.js'
import 'codemirror/mode/swift/swift.js'
import 'codemirror/mode/vue/vue.js'
import 'codemirror/addon/display/placeholder'
require("codemirror/addon/hint/show-hint");
require("codemirror/addon/hint/sql-hint");

import "codemirror/addon/fold/foldgutter.css"
import "codemirror/addon/edit/matchbrackets"
import "codemirror/addon/fold/foldcode"
import "codemirror/addon/fold/foldgutter"
import "codemirror/addon/fold/brace-fold"
import "codemirror/addon/fold/comment-fold"

import "codemirror/addon/search/search"
import "codemirror/addon/search/searchcursor"
import "codemirror/addon/search/jump-to-line"
import {format} from "sql-formatter"
export default {
    name: "codeEditor",
    props: {
        // 外部传入的内容，用于实现双向绑定
        value: String,
        // 外部传入的语法类型
        language: {
            type: String,
            default: null
        },
        inputColumns : Array ,
        readOnly : {
            type : Boolean ,
            default: false
        },
        placeholder: {
            type:String ,
            default : ""
        }
    },
    data() {
        const vm = this;
        return {
// 内部真实的内容
            code: '',
            // 默认的语法类型
            mode: 'javascript',
            // 编辑器实例
            coder: null,
            // 默认配置
            options: {
                // 缩进格式
                tabSize: 2,
                // 主题，对应主题库 JS 需要提前引入
                theme: 'idea',
                // 显示行号
                lineNumbers: true,
                line: true,
                mode: {name: "text/x-mysql"},
                lineWrapping: 'scroll', // 文字过长时，是换行(wrap)还是滚动(scroll),默认是滚动
                showCursorWhenSelecting: true, // 文本选中时显示光标
                cursorHeight: 0.85, //光标高度，默认是1
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                matchBrackets: true, // 括号匹配
                smartIndent: true, // 智能缩进
                hintOptions: { completeSingle: false},
                autofocus:false,
                // 智能提示
                extraKeys: {
                    "Alt": (cm)=>{
                        cm.showHint({hint: vm.altOption});
                    },
                    "Ctrl": (cm)=>{
                        cm.showHint({hint: vm.ctrlOption});
                    },
                    // 支持切换的语法高亮类型，对应 JS 已经提前引入
                    // 使用的是 MIME-TYPE ，不过作为前缀的 text/ 在后面指定时写死了
                },

            },

            modes: [
                {
                    value: 'css',
                    label: 'CSS'
                }, {
                    value: 'javascript',
                    label: 'Javascript'
                }, {
                    value: 'html',
                    label: 'XML/HTML'
                }, {
                    value: 'x-java',
                    label: 'Java'
                }, {
                    value: 'x-objectivec',
                    label: 'Objective-C'
                }, {
                    value: 'x-python',
                    label: 'Python'
                }, {
                    value: 'x-rsrc',
                    label: 'R'
                }, {
                    value: 'x-sh',
                    label: 'Shell'
                }, {
                    value: 'x-sql',
                    label: 'SQL'
                }, {
                    value: 'x-swift',
                    label: 'Swift'
                }, {
                    value: 'x-vue',
                    label: 'Vue'
                }, {
                    value: 'markdown',
                    label: 'Markdown'
                },{
                    value : "application/json" ,
                    label: "application/json"
                }],
            udf_operator:[],
        }
    },
    mounted() {
        // 初始化
        this._initialize()
    },
    methods: {
        formatSql(){
            /*获取文本编辑器内容*/
            let sqlContent="";
            sqlContent=this.coder.getValue();
            /*将sql内容进行格式后放入编辑器中*/
            this.coder.setValue(format(sqlContent));
        },
        getCursor(){
            return this.coder.getCursor();
        },
        setAllUdf(data){
            this.udf_operator = data;
        },
        showOption(cm , list) {
            const cmInstance = cm;
            // 得到光标
            let cursor = cmInstance.getCursor();
            // 得到行内容
            let cursorLine = cmInstance.getLine(cursor.line);
            // 得到光标位置
            let end = cursor.ch;
            let start = end;
            const Two = `${cursorLine.charAt(start - 2)}${cursorLine.charAt(start - 1)}`;
            const One = `${cursorLine.charAt(start - 1)}`;
            // 得到光标标识
            let token = cmInstance.getTokenAt(cursor);
            return {
                list:list,
                from: {ch: end, line: cursor.line},
                to: {ch: token.end, line: cursor.line},
            };
        },

        ctrlOption(cm){
            const {udf_operator} = this;
            return this.showOption(cm,udf_operator);
        },
        altOption(cm){
            let list = this.inputColumns.map(item =>{
                return item.columnCode;
            });
            return this.showOption(cm,list);
        },
        setValue(value){
            this.coder.setValue(value);
        },
        setSelection(...arg){
            this.coder.setSelection(...arg);
        },
        /**
         * 根据位置输入 内容
         * @param pos1
         * @param val
         */
        setValByPos(pos1 , val){
            let pos = {};
            pos.line = pos1.line;
            pos.ch = pos1.ch;
            this.coder.replaceRange(val,pos);
        },
        // 初始化
        _initialize() {
            const vm = this;
            // 初始化编辑器实例，传入需要被实例化的文本域对象和默认配置
            this.coder = CodeMirror.fromTextArea(this.$refs.textarea, this.options);
            if(vm.readOnly){
                vm.coder.setOption("readOnly", true);
            }
            // 编辑器赋值
            this.coder.setValue(this.value || this.code);
            // 支持双向绑定
            this.coder.on('change', (coder, change) => {
                this.code = coder.getValue();
                if (this.$emit) {
                    this.$emit('input', this.code)
                }
                if (change.origin === "+input") {
                    let text = change.text && change.text[0].trim();
                    if(text === '') return ;

                    setTimeout(function () {
                        vm.coder.execCommand("autocomplete");
                    }, 20);
                }
            });
            this.coder.on("cursorActivity" , (coder) => {
                let cursor = coder.getCursor();
                this.$emit("lastCursorPos" , cursor);
            })
            this.coder.on("change" ,(coder, changeObj)=>{
                this.$emit("change", changeObj);
            })
            // 尝试从父容器获取语法类型
            if (this.language) {
                // 获取具体的语法类型对象
                let modeObj = this._getLanguage(this.language)

                // 判断父容器传入的语法是否被支持
                if (modeObj) {
                    this.mode = modeObj.label;
                    this.changeMode(this.mode);
                }
            }
        },
        // 获取当前语法类型
        _getLanguage(language) {
            // 在支持的语法类型列表中寻找传入的语法类型
            return this.modes.find((mode) => {
                // 所有的值都忽略大小写，方便比较
                let currentLanguage = language.toLowerCase()
                let currentLabel = mode.label.toLowerCase()
                let currentValue = mode.value.toLowerCase()

                // 由于真实值可能不规范，例如 java 的真实值是 x-java ，所以讲 value 和 label 同时和传入语法进行比较
                return currentLabel === currentLanguage || currentValue === currentLanguage
            })
        }
        ,
        // 更改模式
        changeMode(val) {
            // 修改编辑器的语法配置
            this.coder.setOption('mode', `text/${val}`)

            // 获取修改后的语法
            let label = this._getLanguage(val).label.toLowerCase()

            // 允许父容器通过以下函数监听当前的语法值
            this.$emit('language-change', label)
        },
    }
}
</script>

