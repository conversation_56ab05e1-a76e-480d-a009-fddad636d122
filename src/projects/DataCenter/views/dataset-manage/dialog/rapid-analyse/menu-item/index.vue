<template>
    <span class="menuItem">
        <el-submenu v-if="menu.children" :index="inx"  >
            <template slot="title">{{menu.name}}</template>
            <menu-item v-for="(item , i) in menu.children" :key="i" :inx="item.code" :menu="item"></menu-item>
        </el-submenu>
        <el-menu-item v-else  :index="inx"  @click="menu.clickFn(menu)" >{{menu.name}}</el-menu-item>
    </span>
</template>
<script src="./menu-item.js"></script>