
/**
 * 服务管理 接口
 * @Author: chenzt
 * @Date: 2020-09-07
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/dataSetOperation/";

/*
* 初始化树
*@params
* @returns {Promise<*>}
* */

export async function queryDataSetTree(hasDataObj ,settings,currentDataSetId) {
    return await reqUtil.get({
        url : root + "queryPublishDataSetTree",
        settings : settings
    },{hasDataObj,currentDataSetId})
}

/*
* 获取数据集字段
*@params
* @returns {Promise<*>}
* */

export async function getDataSetColumn(id, settings ) {
    return await reqUtil.get({
        url : root + "getDataSetColumn",
        settings
    }, { id })
}


/*
* 获取数据集字段-多表
*@params
* @returns {Promise<*>}
* */

export async function getDataSetColumnList(id, settings ) {
    return await reqUtil.get({
        url : root + "getDataSetColumnList",
        settings
    }, { id })
}

/*
* 获取主题集字段
*@params
* @returns {Promise<*>}
* */

export async function getDataSourceTree( settings ) {
    return await reqUtil.get({
        url : "/manage/dataWarehouse/getDataSourceTree?isLogic=true",
        settings
    })
}

/*
*  发布服务
 * @param param
 * @returns {Promise<*>}
* */
export async function createService( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/createService',
        settings
    } , param);
}

/*
*  列表查询
 * @param param
 * @returns {Promise<*>}
* */
export async function queryServicePage( param, settings ) {
    return await reqUtil.post({
        url:  '/publishManagement/queryServicePage',
        settings
    } , param);
}

/**
 * 服务详情
 * @param {*} param 
 * @param {*} settings 
 */ 
export async function queryServiceDetails( id, settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/queryServiceDetails?id=' + id,
        settings
    } );
}
/*
*  下拉类别查询
 * @param param
 * @returns {Promise<*>}
* */
export async function getQueryType(settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/getQueryType',
        settings
    } );
}
/**
 * 获取服务状态
 */
export async function getServiceStatus(settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/getServiceStatus',
        settings
    } );
}
const root1 = "/publish"

/**
 * 服务卸载
 */
export async function offlineServiceByServiceMetaId(id, settings ) {
    return await reqUtil.get({
        url:  '/publish/offlineServiceByServiceMetaId?serviceMetaId=' + id,
        settings
    } );
}
/**
 * 服务重新发布（启用）
 */
export async function redistributionService(id, settings ) {
    return await reqUtil.get({
        url:  '/publish/redistributionService?serviceMetaId=' + id,
        settings
    } );
}

/**
 * 服务停用
 */
export async function disableService(id, settings ) {
    return await reqUtil.get({
        url:  '/publish/disableService?serviceMetaId=' + id,
        settings
    } );
}

/**
 * 测试
 * @param {*} paramsVo 
 * @param {*} settings 
 */
export async function testService(paramsVo, settings ) {
    return await reqUtil.post({
        url:  '/publish/testService',
        settings
    }, paramsVo);
}

/**
 * 服务编辑
 * @param {*} paramConfigVo 
 * @param {*} settings 
 */
export async function editService(paramConfigVo, settings ) {
    return await reqUtil.post({
        url:  '/publish/editService',
        settings
    }, paramConfigVo);
}

/**
 * 生成API获取训练版本号
 * @param {*} id 
 * @param {*} settings 
 */
export async function getLogAndParams(id, settings ) {
    return await reqUtil.get({
        url:  '/ai/publish/getLogAndParams?scriptId=' + id,
        settings
    });
}
/**
 * 生成API中AI建模保存发布
 * @param {*} paramConfigVo 
 * @param {*} settings 
 */
export async function publishAiService(paramConfigVo, settings ) {
    return await reqUtil.post({
        url:  'ai/publish/publishAiService',
        settings
    }, paramConfigVo);
}

/**
 * 服务管理列表 - AI建模卸载
 * @param {*} id 
 * @param {*} settings 
 */
export async function unInstall(id, settings ) {
    return await reqUtil.get({
        url:  '/ai/publish/unInstall?serviceMetaId=' + id,
        settings
    });
}

/**
 * 服务管理列表 - AI建模下线
 * @param {*} id 
 * @param {*} settings 
 */
export async function offLine(id, settings ) {
    return await reqUtil.get({
        url:  '/ai/publish/offLine?serviceMetaId=' + id,
        settings
    });
}

/**
 * 服务管理列表 - AI建模上线
 * @param {*} id 
 * @param {*} settings 
 */
export async function aiExport(id, settings ) {
    return await reqUtil.get({
        url:  '/ai/publish/export?serviceMetaId=' + id,
        settings
    });
}

/**
 * 服务管理列表 - AI建模测试
 * @param {*} id 
 * @param {*} params 
 * @param {*} settings 
 */
export async function testAiService(id, params, settings ) {
    return await reqUtil.post({
        url:  '/ai/publish/testAiService?serviceId=' + id,
        settings
    }, params);
}

/**
 * 服务管理生成API - 获取版本号下拉树
 * @param {*} settings 
 */
export async function getLogIdTree( settings ) {
    return await reqUtil.get({
        url:  '/ai/publish/getLogIdTree',
        settings
    });
}

/**
 * AI建模编辑API
 * @param {*} params 
 * @param {*} settings 
 */
export async function updateAiService(params, settings ) {
    return await reqUtil.post({
        url:  '/ai/publish/updateAiService',
        settings
    }, params);
}

/**
*  服务左侧树-查询
 * @param param
 * @returns {Promise<*>}
* */
export async function queryServiceClassifyTree(settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/queryServiceClassifyTree',
        settings
    });
}

/**
*  服务左侧树-新增
 * @param param
 * @returns {Promise<*>}
* */
export async function newServiceClassify(param, settings ) {
    return await reqUtil.post({
        url:  '/publishManagement/newServiceClassify',
        settings
    },param);
}

/**
*  服务左侧树-修改
 * @param param
 * @returns {Promise<*>}
* */
export async function editServiceClassify(param, settings ) {
    return await reqUtil.post({
        url:  '/publishManagement/editServiceClassify',
        settings
    },param);
}

/**
*  服务左侧树-删除
 * @param param
 * @returns {Promise<*>}
* */
export async function deleteServiceClassify(classifyId, settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/deleteServiceClassify?classifyId=' + classifyId,
        settings
    });
}

/**
 *  服务列表--移动
 * @param param
 * @returns {Promise<*>}
 * */
export async function moveModelService(param, settings ) {
    return await reqUtil.post({
        url:  '/publishManagement/moveModelService',
        settings
    },param);
}

/**
 * 生成api--创建新方案
 * @param {*} param
 * @param {*} settings
 */
export async function createNewServiceTrans(param,settings) {
    return await reqUtil.get({
        url:  '/publish/createNewServiceTrans',
        settings
    },param);
}

/**
 *  服务--数据脱敏下拉框数据
 * @param param
 * @returns {Promise<*>}
 * */
export async function getDesenTypes(settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/getDesenTypes',
        settings
    });
}


/**
 *  服务发布---数据查询保存
 * @param param
 * @returns {Promise<*>}
 * */
export async function createDataQueryService( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/createDataQueryService',
        settings
    } , param);
}

/**
 *  服务发布---数据查询编辑
 * @param param
 * @returns {Promise<*>}
 * */
export async function editDataQueryService( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/editDataQueryService',
        settings
    } , param);
}

/**
 *  服务发布---信息核查保存
 * @param param
 * @returns {Promise<*>}
 * */
export async function createInformationVerificationService( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/createInformationVerificationService',
        settings
    } , param);
}

/**
 *  服务发布---信息核查编辑
 * @param param
 * @returns {Promise<*>}
 * */
export async function editInformationVerificationService( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/editInformationVerificationService',
        settings
    } , param);
}


/**
 *  服务发布---比对订阅，根据模型id获取业务数据集
 * @param param
 * @returns {Promise<*>}
 * */
export async function queryTransSteps( transId, settings ) {
    return await reqUtil.get({
        url:  '/publish/queryTransSteps?transId=' + transId,
        settings
    });
}

/**
 *  服务发布---比对订阅，订阅测试
 * @param param
 * @returns {Promise<*>}
 * */
export async function subscribe( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/subscribe',
        settings
    },param);
}

export async function serviceSubscribe( param, settings ) {
    return await reqUtil.post({
        url:  '/publish/serviceSubscribe',
        settings
    },param);
}

/**
 *  服务发布---数据查询、信息核查树（根据数据集id所在的数据源过滤）
 * @param param
 * @returns {Promise<*>}
 * */
export async function getPublishDataSetTree( param, settings ) {
    return await reqUtil.get({
        url:  '/manage/dataWarehouse/getPublishDataSetTree',
        settings
    },param);
}


/**
 *  服务发布---判断模型分析、数据碰撞、比对订阅在服务配置时是否回填（true）
 * @param param
 * @returns {Promise<*>}
 * */
export async function checkPlugin( param, settings ) {
    return await reqUtil.post({
        url:  '/publishManagement/checkPlugin',
        settings
    },param);
}

export async function getServiceOutput( settings ) {
    return await reqUtil.get({
        url:  '/publishManagement/getServiceOutput',
        settings
    });
}
/**
 * api列表-服务发布(查询、核查)
 * @param param
 * @returns {Promise<*>}
 * */
export async function publishServiceNoSaveMeta( id,settings ) {
    return await reqUtil.get({
        url:  '/publish/publishServiceNoSaveMeta',
        settings
    },{id});
}

/**
 * api列表-服务发布(模型)
 * @param param
 * @returns {Promise<*>}
 * */
export async function publishModelServiceCreateJar( id,settings ) {
    return await reqUtil.get({
        url:  '/publish/publishModelServiceCreateJar',
        settings
    },{id});
}

/**
 * 测试-数据查询、信息核查（多条查询）
 * @param {*} paramsVo
 * @param {*} settings
 */
export async function testQueryAndVercationService(paramsVo, settings ) {
    return await reqUtil.post({
        url:  '/publish/testQueryAndVercationService',
        settings
    }, paramsVo);
}


