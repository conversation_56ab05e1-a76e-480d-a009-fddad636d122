<template>
    <div class="ApiTestPage api-outer" v-loading="settings.loading">
        <div class="api-left">
            <div class="api-left-form">
                <ParamsList ref="params" v-on="$listeners" :loadParams="settings" :row="row" v-if="apiRow" :apiRow="apiRow" />
            </div>
            <el-button type="primary" @click="testFn">
                {{ testTxt }}
                <help-document code="testSingleService"></help-document>
            </el-button>
        </div>
        <div class="api-right">
            <RespondPage ref="respond" v-if="apiRow" :row="apiRow" @getAsyncInfo="getAsyncInfo"/>
        </div>
    </div>
</template>

<script>

import {commonMixins} from "@/api/commonMethods/common-mixins";
import RespondPage from "./RespondPage";
import ParamsList from "./ParamsList";
export default {
    name: "ApiTestPage",
    mixins: [commonMixins],
    components: {
        RespondPage,
        ParamsList,
    },
    props: {
        pageId: String,
        row : Object,
    },
    data() {
        return {
            testTxt: '开始测试',
            apiRow: null
        }
    },
    methods: {
        testFn() {
            let isSubscript = this.$refs.params.isSubscript;
            let testVo = this.$refs.params.setTestVo(true);
            if(!testVo) return;
            if(isSubscript) this.$refs.respond.setSubParams(testVo);
            else this.$refs.respond.setReqParams(testVo);
        },
        getAsyncInfo(id){
            this.$refs.params.getAsyncInfo(id);
        },
        getApiDetail() {
            const {pageId:id ,settings} = this;
            let services = this.$services('modeling');
            settings.loading = true;
            services.getServiceDetailById(id).then(res => {
                if(res.data.code === 0){
                    let result = res.data.data;
                    this.apiRow = {
                        type : result.apiType ,
                        async: result.async ,
                        id : result.servicePublishId ,
                        serviceMetaId : result.serviceMetaId ,
                        apiName : result.apiName
                    }
                }
            })
        }
    },
    created() {
        this.getApiDetail();
    }
}
</script>

<style scoped lang="less" src="./testPage.less"></style>
