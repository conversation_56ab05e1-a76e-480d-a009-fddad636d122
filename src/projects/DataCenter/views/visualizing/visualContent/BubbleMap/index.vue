<template>
    <div class="chartCard">
        <ve-map
                v-loading="loading"
                :width="width"
                :height="height"
                :settings="chartSettings"
                :extend="extend"
                :data-empty="data_empty"
                :judge-width="true"
                :events="chartEvents"
                ref="chart"
        ></ve-map>
    </div>
</template>

<script src="./bubble-map.js"></script>
<style scoped lang="less" src="../css/chart.less"></style>
<style>
    .v-charts-data-empty {
        background: #fff;
    }
</style>
<style scoped lang="less" src="./bubble-map.less"></style>