/**
 * 数据连接 接口
 * @Author: qiuzx
 * @Date: 2021-07-08
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'

const root = "/manage/dataWarehouse/", root1 = "/metadata/dataobject/",root2 = "/metadata/";

/*
*  查询树
 * @param
 * @returns {Promise<*>}
* */

export async function queryDirTree(id , pId , keyword , settings ) {
    return await reqUtil.get({
        url : root + "queryDirTree" ,
        settings : settings
    } , {id , pId , keyword})
}

/*
*  查询数据源类型
 * @param
 * @returns {Promise<*>}
* */
export async function queryAllTypes(settings ) {
    return await reqUtil.get({
        url : root + "queryAllTypes" ,
        settings : settings
    })
}
/*
*  查询列表
 * @param
 * @returns {Promise<*>}
* */

export async function queryDirTreeChidren( params ,settings ) {
    return await reqUtil.post({
        url : root + "queryDirTreeChidren" ,
        settings : settings
    } , params )
}


const f_root = "/metadata/datasource/"
export async function getdataSourceList( dbType ,settings) {
    return await reqUtil.get({
        url: f_root + 'list?dbType='+dbType,
        settings : settings
    } );
}

/**
 * 用于数据空间的数据仓库添加数据源的时候，
 * 保存数据源的时候先校验库名是否重复
 * @return {Promise<*>}
 */
export async function checkConnectRepeat({classifyId , dataSourceName , settings}){
    return await reqUtil.post({
        url : `/metadata/datasource/checkConnectRepeat?classifyId=${classifyId}&dataSourceName=${dataSourceName}`,
        settings
    })
}

/**
 * 数据源注册新的数据的接口替换成新的保存接口
 * 参数：(以原来的调用传参一致,只不过合并到一起传)
 * @return {Promise<*>}
 */
export async function addContactTableSave({dataBaseId ,dataObjectVO, dataSetAuthVo, settings}){
    return await reqUtil.post({
        url : '/metadata/dataobject/newSave',
        settings
    },{dataBaseId ,dataObjectVO , dataSetAuthVo })
}
/*
*  新增数据源信息
 * @param id
 * @returns {Promise<*>}
* */
export async function getDataSetInfo( id ,settings) {
    return await reqUtil.get({
        url: root + 'getDataSetInfo',
        settings : settings
    } , {id});
}

//新增数据源保存
export async function saveConnect( dataSourceVO,settings ) {
    return await reqUtil.post({
        url: f_root + 'save',
        settings : settings
    },dataSourceVO);
}

//查看数据源信息
export async function edit( instanceId, settings ) {
    return await reqUtil.get({
        url: f_root + 'edit?instanceId='+instanceId,
        settings : settings
    });
}
//更新数据源
export async function updateDataSet( dataSourceVO, settings ) {
    return await reqUtil.post({
        url: f_root + 'update',
        settings : settings
    },dataSourceVO);
}

/*
*  保存添加数据源
 * @param
 * @returns {Promise<*>}
* */
export async function saveDWDBInstance(classifyId , busiDirId , sourceData , settings ,isAddExistDataSource ,source) {
    return await reqUtil.post({
        url: root + 'saveDWDBInstance?classifyId=' + classifyId + "&busiDirId="+busiDirId + "&isAddExistDataSource=" + isAddExistDataSource + "&source=" +source ,
        settings : settings
    } , sourceData);
}

/*
*  验证用户名密码（大数据导入）
 * @param type
 * @returns {Promise<*>}
* */
export async function checkImportAuth( {catalogId,username,password, settings}) {
    return await reqUtil.post({
        url: '/metadata/datasource/checkImportAuth',
        settings : settings
    } , {catalogId,username,password});
}

/**
 * 条件过滤变量下拉获取接口
 * @param {*} param0 
 */
export async function queryTransVariable( transId , settings) {
    return await reqUtil.get({
        url: 'variableManage/queryTransVariable?transId=' + transId,
        settings : settings
    } );
}


/*
*  获取库实例
 * @param type
 * @returns {Promise<*>}
* */
export async function getDataSetByDataSetType( type, settings) {
    return await reqUtil.get({
        url:root + 'getDataSetByDataSetType',
        settings : settings
    } , {type});
}

/*
*  添加数据源授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function addDataSetAuth( dataSetAuthVo , settings ) {
    return await reqUtil.post({
        url: '/dataSetAuth/addDataSetAuth',
        settings : settings
    } , dataSetAuthVo);
}

/*
*  测试链接（最外层）
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function checkOut( instanceId , settings ) {
    return await reqUtil.get({
        url: '/metadata/datasource/checkOut',
        settings : settings
    } , {instanceId});
}
//检查链接
export async function checkConnect( dataSourceVO,settings ) {
    return await reqUtil.post({
        url: f_root + 'check',
        settings : settings
    },dataSourceVO);
}
/*
*  删除数据表
 * @param deleteType
 * @returns {Promise<*>}
* */

export async function dataObject( deleteType,settings ) {
    return await reqUtil.get({
        url: root1 + deleteType,
        settings : settings
    } );
}

/*
*  删除
 * @param deleteType
 * @returns {Promise<*>}
* */
export async function dataWarehouse( deleteType,settings ) {
    return await reqUtil.get({
        url: root + deleteType,
        settings : settings
    } );
}
/*
*  查询数仓 表格
 * @param id , pId , keyword
 * @returns {Promise<*>}
* */
export async function queryDataBaseTable( data,settings ) {
    return await reqUtil.post({
        url: root + 'queryDataBaseTable',
        settings : settings
    }, data );
}
/*
*  添加数据对象
 * @param id , pId , keyword
 * @returns {Promise<*>}
* */
const g_root = "/metadata/dataobject/"
export async function queryPendingRegisterDataObj( schemaId, dbType ,settings) {
    return await reqUtil.get({
        url: g_root + 'queryPendingRegisterDataObj',
        settings : settings
    } , {schemaId,dbType});
}

export async function saveDataObj( DataObjectVO, settings ) {
    return await reqUtil.post({
        url: g_root + 'save',
        settings : settings
    },DataObjectVO);
}

export async function getSchema( instanceId ,settings) {
    return await reqUtil.get({
        url: g_root + 'schema?instanceId='+instanceId,
        settings : settings
    } );
}

/*
*  添加数据对象数据
 * @param dataBaseId
 * @returns {Promise<*>}
* */
export async function saveTable( dataBaseId , values , settings) {
    return await reqUtil.post({
        url: root + 'saveTable?dataBaseId=' + dataBaseId ,
        settings : settings
    } , values);
}

/*
*  数据集注册
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function dataSetAuthRegister( dataSetAuthVo ,settings) {
    return await reqUtil.post({
        url: '/dataSetAuth/dataSetAuthRegister',
        settings : settings
    } , dataSetAuthVo);
}
/*
*  删除
 * @param tableId , dbType ,classifyId
 * @returns {Promise<*>}
* */
export async function deleteTable( data,settings ) {
    return await reqUtil.get({
        url:'/metadata/deleteTable',
        settings : settings
    }, data);
}
export async function serchMachine( ip ,settings) {
    return await reqUtil.get({
        url: f_root + 'machine?ip='+ip,
        settings : settings
    } );
}



/*
* 我的数据源新增目录
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addDataConnectTreeNode( data , settings ) {
    return await reqUtil.post({
        url: root + 'addDataConnectTreeNode',
        settings : settings
    }, data);
}

/*
*  新建子仓库 或 同级仓库
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addTreeNode( {pId  , dirName , dirCode } , settings ) {
    return await reqUtil.post({
        url: root + 'addTreeNode',
        settings : settings
    }, {pId , dirName , dirCode } );
}
/*
*  新建子层次 或 同级层次
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function saveDWLevel( {pId  , levelName , levelCode } ,settings ) {
    return await reqUtil.post({
        url: root + 'saveDWLevel',
        settings : settings
    }, {pId , levelName , levelCode } );
}
/*
*  重命名
 * @param  id , dirId , type , classifyName , classifyId
 * @returns {Promise<*>}
* */

export async function renameDWInstance( { id , dirId , type , classifyName , classifyId },settings ) {
    return await reqUtil.post({
        url: root + 'renameDWInstance',
        settings : settings
    }, { id , dirId , type , classifyName , classifyId } );
}

/*
*  数据集重命名
 * @param  dataId , newName , dirId
 * @returns {Promise<*>}
* */

export async function renameDataSet( { dataId , newName , dirId },settings ) {
    return await reqUtil.post({
        url: root + 'renameDataSet',
        settings : settings
    }, { dataId ,newName , dirId } );
}

/*
*  业务库重命名
 * @param newName , id , catalogType
 * @returns {Promise<*>}
* */

export async function updateCatalogName( { newName , id , catalogType } ) {
    return await reqUtil.post({
        url: root + 'updateCatalogName',
    }, { newName , id , catalogType } );
}


/*
*  新建仓库
 * @param  dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addBusiDir( { dirName , dirType } , settings ) {
    return await reqUtil.post({
        url: root + 'addBusiDir',
        settings
    }, { dirName , dirType } );
}








//以下為旧的

/*
*  新增子目录
 * @param classifyId , classifyName , dirType
 * @returns {Promise<*>}
* */

export async function createTransClassify(classifyId , classifyName , dirType ) {
    return await reqUtil.post({
        url : root + "createTransClassify"
    } , {classifyId , classifyName , dirType})
}

/*
*  删除子目录
 * @param
 * @returns {Promise<*>}
* */

export async function deleteTransClassify(transClassifyId ) {
    return await reqUtil.get({
        url : root + "deleteTransClassify"
    } , {transClassifyId})
}

/*
*  重命名子目录
 * @param
 * @returns {Promise<*>}
* */

export async function updateTransClassify(currClassifyId , transClassifyName ) {
    return await reqUtil.get({
        url : root + "updateTransClassify"
    } , {currClassifyId , transClassifyName})
}

/*
*  查询列表
 * @param
 * @returns {Promise<*>}
* */

export async function queryTransList( params ,settings ) {
    return await reqUtil.post({
        url : root + "queryTransList" ,
        settings : settings
    } , params )
}

/**
 * 目录移动
 * @param curryClassifyId
 * @param newParentClassifyId
 * @param dirType
 * @return {Promise<*>}
 */
export async function moveTreeDir(curryClassifyId , newParentClassifyId ,dirType){
    return await reqUtil.get({
        url : "/dataModeling/moveDirectory"
    },{curryClassifyId , newParentClassifyId ,dirType})
}

export async function findChangeColumn( dataObjId, dbType,settings ) {
    return await reqUtil.get({
        url: 'metadata/dataobject/column?dataObjId='+dataObjId+'&dbType=' +dbType,
        settings : settings
    });
}


export async function syncColumn(key, dataObjId, dbType, settings ) {
    return await reqUtil.get({
        url: root + 'sync?dataObjId='+dataObjId+'&dbType=' +dbType+'&key='+key,
        settings : settings
    });
}

export async function syncColumnsDcThree( params ,settings ) {
    return await reqUtil.get({
        url : root + "syncColumnsDcThree" ,
        settings : settings
    } , params)
}

export async function overlaySynchronization( params ,settings ) {
    return await reqUtil.get({
        url : root2 + "overlaySynchronization" ,
        settings : settings
    } , params)
}

/**
 * 数据源移动
 * @param dirId
 * @param dwbId
 * @return {Promise<*>}
 */
export async function moveDwbInstance( params ,settings ) {
    return await reqUtil.get({
        url : root2 + "moveDwbInstance" ,
        settings : settings
    } , params )
}
/**
 * 数据表预览
 * @param tableId
 * @param tableName
 * @param limit
 * @param dbType
 * @param isFile
 * @return {Promise<*>}
 */
export async function preview( params ,settings ) {
    return await reqUtil.post({
        url : root2 + "preview" ,
        settings : settings
    } , params )
}
/**
 * 数据表字段
 * @param dirId
 * @param dwbId
 * @return {Promise<*>}
 */
export async function getDataObjColumn( params ,settings ) {
    return await reqUtil.get({
        url : root2 + "getDataObjColumn" ,
        settings : settings
    } , params )
}

/**
 * 数据表字段
 * @param dirId
 * @param dwbId
 * @return {Promise<*>}
 */
export async function saveAs( params ,settings ) {
    return await reqUtil.get({
        url : root2 + "saveAs" ,
        settings : settings
    } , params )
}

export async function getDataSourceTree(settings,isLogic,dataSpace) {
    return await reqUtil.get({
        url: root2 + 'getDataSourceTree',
        settings : settings
    },{isLogic,dataSpace});
}
//从元数据导入 获取数据类型
export async function getHouseKeepTree(data,settings) {
    return await reqUtil.get({
        url: root2 + 'dataobject/getHouseKeepTree',
        settings : settings
    },data);
}
//从元数据导入 获取数据源
export async function findHouseKeepDataObjByType(data,settings) {
    return await reqUtil.get({
        url: root2 + 'dataobject/findHouseKeepDataObjByType',
        settings : settings
    },data);
}

//从元数据导入 获取数据表
export async function getDataTableById(data,settings) {
    return await reqUtil.get({
        url: root2 + 'getDataTableById',
        settings : settings
    },data);
}

/**
 * 获取回显分享用户角色
 * @param {*} id
 * @param {*} settings
 */
export async function getObjsByResource(id,settings) {
    return await reqUtil.get({
        url: 'firstPageShare/getObjsByResource?resourceId=' + id,
        settings : settings
    });
}

/**
 * 查询已授权用户，仪表盘使用

 * @param {*} params
 * @param {*} settings
 */
export async function getVisualizingAllUserAuth(id, settings) {
    return await reqUtil.post({
        url : "/dashboardAuth/getAllUserAuth",
        settings
    }, id)
}

/**
 * 查询已授权角色,仪表盘使用
 * @param {*} params
 * @param {*} settings
 */
export async function queryVisualizingAllRoleAuth(id, settings) {
    return await reqUtil.post({
        url : "/dashboardAuth/queryAllRoleAuth",
        settings
    }, id)
}
