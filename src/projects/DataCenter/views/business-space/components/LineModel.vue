<template>
    <div class="Line">
        <ul class="item">
            <li v-for="(node , i) in nodeList"
                :key="i"
                :class="'item-'+node.type"
                class="item-box">
                <slot :name="node.type"></slot>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    name: "LineModel",
    data() {
        return {
            nodeList: [
                {type: "left"},
                {type: "top",},
                {type: "right",},
            ],
        }
    },
};
</script>

<style scoped lang="less">
.arrow() {
    content: "";
    display: block;
    position: absolute;
    width: 36px;
    height: 18px;
    background: url("../../../assets/images/business/arrow-big.png") no-repeat center center;
}
.Line {
    padding: 10px 8px 0 0;
}
.item {
    display: grid;
    padding: 10px;
    grid-template-columns: 334px calc(100% - 334px);
    grid-template-rows: 512px 170px;
    &-box {

    }
    &-top {
        margin-bottom: 94px;
        position: relative;
        &::before {
            .arrow();
            bottom : -50px;
            left: 50%;
            margin-left: -18px;
            transform: rotate(90deg);
        }
    }

    &-left {
        max-width: 254px;
        grid-row: 1 / span 2;
        margin-right: 80px;
        position: relative;
        &::before {
            .arrow();
            right: -58px;
            top: 200px;
        }
        &::after {
            .arrow();
            right: -58px;
            bottom: 85px;
        }
    }

    &-right {

    }
}
</style>
