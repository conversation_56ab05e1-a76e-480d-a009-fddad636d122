export default {
    name: "detail" ,
    mixins : [],
    components : {},
    props :{
        detailInfo : Object
    },
    data(){
        return {
            form:{},
            requestTableData: [],
            requestTHeadData: [
                {
                    prop: "paramCode",
                    label: "参数名",
                    minWidth: 100,
                    align: "left"
                },
                {
                    prop: "paramLocation",
                    label: "参数位置",
                    minWidth: 160,
                    resizable: false
                },
                {
                    prop: "memo",
                    label: "参数描述",
                    width: 100,
                    align: "center"
                },
                {
                    prop: "type",
                    label: "参数类型",
                    minWidth: 160,
                    align: "center",
                    // sortable: true,
                    resizable: false
                }, 
                {
                    prop: "example",
                    label: "示例值",
                    width: 100,
                    align: "center"
                },
            ],
            responseTableData : [],
            responseTHeadData: [
                {
                    prop: "paramCode",
                    label: "参数名",
                    minWidth: 105,
                    align: "left"
                },
                {
                    prop: "memo",
                    label: "参数描述",
                    width: 135,
                    align: "center"
                },
                {
                    prop: "type",
                    label: "参数类型",
                    minWidth: 160,
                    align: "center",
                    // sortable: true,
                    resizable: false
                }, 
                // {
                //     prop: "example",
                //     label: "示例值",
                //     width: "",
                //     align: "center"
                // },
            ],
        }
    },
    methods : {

    },
    created(){
        this.form = this.detailInfo;
        this.requestTableData = this.detailInfo.paramList.map((item)=>{
            item.memo = item.memo || "--";
            return item;
        });
        this.responseTableData = this.detailInfo.ginsengList.map((item)=>{
            item.memo = item.memo || "--";
            return item;
        });
    }
}
