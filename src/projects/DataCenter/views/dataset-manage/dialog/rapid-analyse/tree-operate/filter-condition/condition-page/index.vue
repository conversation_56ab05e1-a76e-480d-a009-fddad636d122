<template>
    <div class="filter-condition" v-loading="settings.loading">
        <dg-button type="primary" icon="el-icon-edit" :disabled="disabled" @click="addBigRow">{{conTxt.addFilter}}</dg-button>
        <div class="f-c-content">
            <div v-for="(bigItem,index) in filter_condition" :key="index">
                <div class="f-c-c-item" v-if="bigItem.inside.length > 0">
                    <div class="f-c-c-i-row mb15">
                        <dg-radio v-for="lo in logicRadio" :disabled="disabled" :key="lo.value" v-model="bigItem.relation" :label="lo.value" call-off>{{lo.label}}</dg-radio>
                    </div>
                    <div class="f-c-c-i-row" v-for="(item,idx) in bigItem.inside" :key="idx">
                        <div class="f-c-c-i-select-one">
                            <dg-select v-model="item.id" :disabled="disabled" filterable :data="fieldOpts"
                                       @change="fieldSelected(arguments , index , idx)"></dg-select>
                        </div>
                        <div class="f-c-c-i-select-two">
                            <dg-select
                                    v-model="item.logic"
                                    :disabled="disabled"
                                    @change="logicChange(index , idx)"
                                    :data="item.format === 'TEXT' ? txt_option : item.format === 'NUMBER'?  num_option :   time_option "
                            ></dg-select>
                        </div>
                        <div class="f-c-c-i-other" v-if="item.logic !== conTxt.isNull && item.logic !== conTxt.norNull">
                            <template v-if="item.format === 'TEXT'">
                                <el-input v-model.trim="item.inputVal" :maxlength="100" :disabled="disabled" :placeholder="conTxt.i_placeholder"></el-input>
                            </template>
                            <template v-else-if="item.format === 'NUMBER'">
                                <template
                                        v-if="item.logic === conTxt.in || item.logic === conTxt.norIn"
                                >
                                    <div class="number-select-left number-select">
                                        <el-input
                                                :disabled="disabled"
                                                :placeholder="conTxt.n_placeholder"
                                                @input="item.spaceVal1 = item.spaceVal1.replace(/\D/g , '')"
                                                v-model.trim="item.spaceVal1"
                                                class="input-with-select"
                                        >
                                            <dg-select
                                                    class="w60"
                                                    :disabled="disabled"
                                                    v-model="item.select1"
                                                    slot="append"
                                                    :data="logic_options"
                                            ></dg-select>
                                        </el-input>
                                    </div>
                                    <div class="number-select number-select-left lh28">{{numTip}}</div>
                                    <div class="number-select">
                                        <el-input
                                                :disabled="disabled"
                                                v-model.trim="item.spaceVal2"
                                                @input="item.spaceVal2 = item.spaceVal2.replace(/\D/g , '')"
                                                :placeholder="conTxt.n_placeholder"
                                                class="input-with-select"
                                        >
                                            <dg-select
                                                    class="w60"
                                                    :disabled="disabled"
                                                    v-model="item.select2"
                                                    slot="prepend"
                                                    :data="logic_options1"
                                            ></dg-select>
                                        </el-input>
                                    </div>
                                </template>
                                <template v-else>
                                    <el-input-number v-model="item.spaceVal" @change="numChecked($event , item)" :disabled="disabled"></el-input-number>
                                </template>
                            </template>
                            <template v-else>
                                <template
                                        v-if="item.logic === conTxt.in || item.logic === conTxt.norIn"
                                >
                                    <dg-date-picker
                                            v-if="item.reNew"
                                            v-model="item.timeSpace"
                                            :disabled="disabled"
                                            unlink-panels
                                            type="daterange"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            :value-format="dateFormat"
                                    ></dg-date-picker>
                                </template>
                                <template v-else>
                                    <dg-date-picker
                                            v-if="item.reNew"
                                            :disabled="disabled"
                                            unlink-panels
                                            v-model="item.time"
                                            type="date"
                                            placeholder="选择日期"
                                            :value-format="dateFormat"
                                    ></dg-date-picker>
                                </template>
                            </template>
                        </div>
                        <div class="f-c-c-i-action">
                            <dg-button
                                    type="text"
                                    size="medium"
                                    icon="el-icon-circle-plus"
                                    :disabled="disabled"
                                    @click="addRow(bigItem.inside , index)"
                            ></dg-button>
                            <dg-button
                                    type="text"

                                    size="medium"
                                    icon="el-icon-delete"
                                    :disabled="disabled"
                                    @click="delBigRow(bigItem.inside,item)"
                            ></dg-button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script src="./condition-page.js"></script>

<style scoped lang="less" src="./condition-page.less"></style>