import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import syncPage from "./sync-page/index.vue"
import {servicesMixins} from "../../../../service-mixins/service-mixins";
import {mapGetters} from "vuex";

export default {
    name: "syncLogic",
    mixins : [ dialog , commonMixins , servicesMixins],
    components : {syncPage},
    computed : {
        ...mapGetters(["userInfo"])
    },
    data(){
        return {
            title : "同步数据集结构" ,
            width : "660px" ,
            datasetId : "",
            hideCheck :true
        }
    },
    methods : {
        show(datasetId){
            const vm = this;
            vm.title = "同步数据集结构";
            vm.datasetId = datasetId;
            vm.getSyncData();
        } ,
        showInfo(data , name){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.hideCheck = false;
            vm.title = "同步数据集结构(轨迹步骤)";
            vm.$nextTick(()=>{
                vm.$refs.syncPage.initHistory(data ,name, true);
            })
        },
        getSyncData(){
            const vm = this , {datasetServices , datasetMock ,datasetId } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            services.getSyncLogicDataColumn(datasetId).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    let columns_len = 0;
                    result.forEach(col => {
                        columns_len += col.newColumn.length;
                    });
                    if( columns_len > 0 ){
                        vm.visible = true;
                        vm.reNew = true;
                        vm.$nextTick(()=>{
                            vm.$refs.syncPage.init(result);
                        })
                    }else {
                        vm.$message.info("暂无字段同步");
                    }
                }
            })
        },
        submit(){
            const vm = this , {datasetServices , datasetMock ,datasetId ,settings , userInfo } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let allColumns = vm.$refs.syncPage.getSyncNodes();
            let columns_params = [];
            allColumns.forEach(item => {
                let measure = [] , dimension = [] ;
                item.columns.forEach(col => {
                    if(col.indexType === 'DIMENSION'){
                        dimension.push(col);
                    }else if(col.indexType === 'MEASURE'){
                        measure.push(col);
                    }
                });
                columns_params.push({measure , dimension , id : item.parentId})
            });
            let params = {
                columns : JSON.stringify(allColumns) ,
                dataSetId :datasetId ,
                userId : userInfo.id
            };
            settings.loading = true;
            services.syncColumn(params ,settings).then(res => {
                if(res.data.status === 0){
                    vm.$emit("appendColumns" ,columns_params);
                    vm.visible = false;
                }
            })

        }
    }
}