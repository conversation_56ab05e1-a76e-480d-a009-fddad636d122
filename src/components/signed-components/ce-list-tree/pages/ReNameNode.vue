<template>
    <el-form label-width="100px" :model="form" ref="form" label-position="right" @submit.native.prevent>
        <el-form-item :label="label" prop="name" :rules="rules">
            <el-input ref="inp" v-model="form.name" :placeholder="placeholder"
                      :maxlength="maxlength" v-input-limit:noSpecial
            ></el-input>
            <div class="g8 f14 lh30">{{ ruleMes }}</div>
        </el-form-item>
    </el-form>
</template>

<script>

export default {
    name: "ReNameNode",
    data() {
        return {
            form: {
                name: "",
                oldName: "",
            },
            label: "",
            placeholder: "",
            rules: [
                {required: true, message: '', trigger: ["blur", "change"]},
                {validator: this.hasTreeNode, trigger: 'change'}
            ],
            maxlength: 0,
            ruleMes: "",
            data: [],
            treeProps: null,
            curData : {}
        }
    },
    methods: {
        initVal({label, name, data, placeholder, maxLength, ruleMes, treeProps , curData}) {
            this.label = label;
            this.form.name = name;
            this.form.oldName = name;
            this.placeholder = placeholder;
            this.maxlength = maxLength;
            this.ruleMes = ruleMes;
            this.data = data;
            this.curData = curData;
            this.rules[0].message = label + '不能为空!';
            this.treeProps = treeProps;
            this.$nextTick(()=>{
                this.$refs.inp.select();
            })
        },
        validate(...arg) {
            return this.$refs.form.validate(...arg);
        },
        hasTreeNode(rule, val, cb) {
            const vm = this;
            let res = "";
            for (let i = 0; i < vm.data.length; i++) {
                let item = vm.data[i];
                if (item[vm.treeProps.label] === val && item.id !== vm.curData.id) {
                    res = `${vm.label}已存在`;
                    break;
                }
            }
            if(res)cb(new Error(res));
            else cb();
        }

    }

}
</script>

<style scoped>

</style>
