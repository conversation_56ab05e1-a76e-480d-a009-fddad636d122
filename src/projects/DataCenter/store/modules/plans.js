const plan = {
    state: {
        plans: [],
        planId: -1,
        planNodes: [],
        planLines: [],
        isCollectionLog: false,
        plansConfig: {},//方案配置
        outputColumns: {},//数据集输出字段
        plansCondition: {},//过滤条件
        editChange: {},//是否编辑配置
        cacheColumns: {},//缓存表字段
        pluginTree: [],// 插件树
        shareId: '6a155c5cdf9d5ddcbd8459f05b37186a',// 来自分享节点 id
        dataTypeList: [],// 数据集来源分类
        otherUserCatalogId: '6a155c5cdf9d5ddcbd8459f05ba71861',// 他人空间目录 id
        myCatalog:[] ,// 我的目录数据
    },
    mutations: {
        ADD_PLAN(state, plans) {
            state.plans = plans;
        },
        ADD_NODE(state, nodes) {
            const {plans, planId} = state;
            const fit = plans.find((item) => item.id === planId);
            if (fit) {
                fit.nodes = nodes;
                state.planNodes = nodes;
            }
        },
        ADD_LINE(state, lines) {
            const {plans, planId} = state;
            const fit = plans.find((item) => item.id === planId);
            if (fit) {
                fit.lines = lines;
                state.planLines = lines;
            }
        },
        SET_PLAN_ID(state, id) {
            state.planId = id;
            const {plans, planId} = state;
            const fit = plans.find((item) => item.id === planId);
            if (fit) {
                state.planLines = fit.lines;
                state.planNodes = fit.nodes;
            }
        },
        SET_NODE_CONFIG(state, node) {
            const {plans, planId} = state;
            const fit = plans.find((item) => item.id === planId);
            if (fit) {
                const index = fit.nodes.findIndex(item => item.index === node.index);
                fit.nodes[index] = node;
            }
        },
        SET_IS_COL(state, isCol) {
            state.isCollectionLog = isCol;
        },
        SET_PLANS_CONFIG(state, {id, config}) {
            state.plansConfig[id] = config;
        },
        CLEAR_PLANS(state) {
            state.plans = [];
            state.plansConfig = {};
            state.planNodes = [];
            state.planId = -1;
            state.planLines = [];
            state.isCollectionLog = false;
            state.outputColumns = {};
            state.plansCondition = {};
            state.editChange = {};
            state.cacheColumns = {};
            state.pluginTree = [];
        },
        SET_OUTPUT_COLUMNS(state, {id, columns}) {
            state.outputColumns[id] = columns;
        },
        SET_CONDITION(state, {id, condition}) {
            state.plansCondition[id] = condition;
        },
        SET_EDIT_CHANGE(state, {id, change}) {
            state.editChange[id] = change;
        },
        SET_CACHE_COLUMNS(state, {id, tableId, columns}) {
            if (!state.cacheColumns[id]) state.cacheColumns[id] = {};
            state.cacheColumns[id][tableId] = columns;
        },
        SET_PLUGIN_TREE(state, payload) {
            if (!state.pluginTree.length) state.pluginTree = payload;
        },
    },
    actions: {
        /**
         * 过滤其他、分享  目录
         */
        filterOtherCatalog({state},payload){
            state.myCatalog = (payload.data || []).filter(l => l.id !== state.shareId && l.id !== state.otherUserCatalogId);
        },
        /**
         * 获取来源类型 目录
         */
        getTypeList({state}, payload) {
            const {settings, $services} = payload;
            if (state.dataTypeList.length) {
                if (settings) settings.loading = false;
                return Promise.resolve(state.dataTypeList)
            }
            return $services("dataReady").queryDataSetTreeByDsType({
                    hasDataObj: false
                }, settings
            ).then(res => {
                if (res.data.code === 0) {
                    state.dataTypeList = res.data.data || [];
                }
                return state.dataTypeList;
            })
        },
        /**
         * 存 插件树
         * @param {*} param0
         * @param {*} payload
         */
        setPluginTree({commit}, payload) {
            commit("SET_PLUGIN_TREE", payload)
        },
        /**
         * 新增方案
         * @param {*} param0
         * @param {*} plans
         */
        addPlan({commit}, plans) {
            commit("ADD_PLAN", plans);
        },
        /**
         * 更新新增插件
         */
        addNode({commit}, nodes) {
            commit("ADD_NODE", nodes);
        },
        /**
         * 更新连线
         * @param commit
         * @param lines
         */
        addLine({commit}, lines) {
            commit("ADD_LINE", lines);
        },
        /**
         * 修改当前激活id
         */
        setPlanId({commit}, id) {
            commit("SET_PLAN_ID", id);
        },
        /**
         * 设置当前插件配置
         */
        setNodeConfig({commit}, node) {
            commit("SET_NODE_CONFIG", node);
        },
        setIsCol({commit}, isCol) {
            commit("SET_IS_COL", isCol);
        },
        /**
         * 设置方案配置数据
         * @param commit
         * @param id
         * @param config
         */
        setPlansConfig({commit}, {id, config}) {
            commit('SET_PLANS_CONFIG', {id, config});
        },
        /**
         * 清空方案数据
         * @param commit
         */
        clearPlans({commit}) {
            commit('CLEAR_PLANS');
        },
        /**
         * 设置输出字段
         * @param commit
         * @param id
         * @param columns
         */
        setOutputColumns({commit}, {id, columns}) {
            commit('SET_OUTPUT_COLUMNS', {id, columns});
        },
        /**
         * 设置过滤条件
         * @param commit
         * @param id
         * @param condition
         */
        setCondition({commit}, {id, condition}) {
            commit('SET_CONDITION', {id, condition});
        },
        /**
         * 编辑变化
         * @param commit
         * @param id
         * @param change
         * @param val
         */
        setEditChange({commit}, {id, change}) {
            commit('SET_EDIT_CHANGE', {id, change});
        },
        /**
         * 设置缓存字段
         * @param commit
         * @param id
         * @param tableId
         * @param columns
         */
        setCacheColumns({commit}, {id, tableId, columns}) {
            commit('SET_CACHE_COLUMNS', {id, tableId, columns})
        }
    },
};
export default plan;
