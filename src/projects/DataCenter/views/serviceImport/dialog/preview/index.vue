<template>
    <div style="background-color : white;height: 100%;padding-left:15px;">
        <el-tabs v-model="actTab">
            <el-tab-pane :label="apilTxt" name="api"></el-tab-pane>
            <el-tab-pane :label="modelTxt" name="model"></el-tab-pane>
            <el-tab-pane :label="testCaseTxt" name="testCase"></el-tab-pane>
            <el-tab-pane :label="dataSetTxt" name="dataSet"></el-tab-pane>
            <el-tab-pane :label="dataBaseTxt" name="dataBase"></el-tab-pane>
        </el-tabs>
        <div class="">
            <model :res="res" v-show="actTab === 'model'"/>
            <data-set :res="res" v-show="actTab === 'dataSet'"/>
            <data-base :res="res" v-show="actTab === 'dataBase'"/>
            <api :res="res" v-show="actTab === 'api'" ref="cont"/>
            <testCase :res="res" v-show="actTab === 'testCase'" />
        </div>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import api from './dialog/api.vue'
import model from './dialog/model.vue'
import dataSet from './dialog/dataSet.vue'
import dataBase from './dialog/dataBase.vue'
import testCase from './dialog/testCase.vue' 
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";

export default {
    name: "index",
    mixins:[commonMixins],
    components: {
        model,
        dataSet,
        dataBase,
        api,
        testCase,
    },
    props : {
        res : Object,
    },
    data(){
        return {
            testCaseTxt : "",
            apilTxt : "",
            modelTxt : "",
            dataSetTxt : "",
            dataBaseTxt : "",
            row : [],
            actTab : "api",
            tabs : [
            ]
        }
    },
    methods : {
        close(){
            this.$emit("close");
        },
        submit(){
            const vm = this;
            let result = this.$refs.cont.$refs.tree.getCheckedKeys(true);
            if (result.length === 0) {
                this.$message.warning("请勾选想要导入的方案");
                return;
            }
            vm.$emit('submit', result);
            vm.close();
        },
    },
    created() {
        const vm = this;
        let apiLength = vm.res.importType === 'TRANS'? this.res.transMetas.length : vm.res.importType === 'CASE' ? this.res.useCases.length : this.res.serviceApis.length;
        this.apilTxt = vm.res.importType === 'TRANS' ? '': 
                        vm.res.importType === 'CASE' ? '测试用例(' + this.res.useCases.length + ')' : 
                        'API(' + this.res.serviceApis.length + ')';
        this.dataBaseTxt = '数据库(' + this.res.dwInstances.length + ')';
        this.modelTxt = '模型(' + this.res.transMetas.length + ')';
        this.dataSetTxt = '数据集(' + this.res.logicInfos.length + ')';
        this.testCaseTxt = vm.res.importType === 'CASE' ? 'API(' + this.res.serviceApis.length + ')' : 
                            vm.res.importType === 'TRANS' ? '' : '测试用例(' + this.res.useCases.length + ')';
    }
}
</script>

<style scoped lang="less">
.manage {
    height: 100%;
    &-cont {
        height: calc(100% - 33px);
    }
    &-page {
        height: calc(100% - 14px);
        padding-top: 14px;
    }
}

</style>
