<template>
    <div class="addFeature fixed-size-form">
        <el-form ref="form" size="mini" :model="form" label-width="90px" label-position="right">
            <el-form-item
                    v-for="(item ,k) in layoutForm"
                    :key="k"
                    :label="`${item.label}:`"
                    :prop="k"
                    :rules="item.rules"
            >
                <el-input v-if="item.type==='input'" v-model="form[k]"
                          size="mini"
                          :placeholder="item.placeholder"
                          v-input-limit:[item.limit]
                ></el-input>
                <dg-select v-if="item.type==='select'"
                           size="mini"
                           v-model="form[k]"
                           :placeholder="item.placeholder"
                           :data="eleOption"
                ></dg-select>
            </el-form-item>
        </el-form>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "addFeature",
    mixins: [commonMixins],
    props: {
        eleOption: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            form: {
                name: "",
                code: "",
                businessEle: "",
            },
            layoutForm: {
                name: {
                    label: '特征名称',
                    type: "input",
                    limit: 'fieldName',
                    placeholder: "请输入特征名称",
                    rules: [
                        {required: true, message: "请输入特征名称", trigger: ['blur', 'change']}
                    ]
                },
                code: {
                    label: '英文名称',
                    type: "input",
                    limit: 'fieldCode',
                    placeholder: "请输入英文名称",
                    rules: [
                        {required: true, message: "请输入英文名称", trigger: ['blur', 'change']}
                    ]
                },
                businessEle: {
                    label: '关联业务元',
                    type: "select",
                    placeholder: "请选择业务元",
                    rules: []
                },
            }
        }
    },
    methods: {
        close() {
            this.$emit("close")
        },
        submit() {
            const vm = this;
            vm.$refs.form.validate((valid) => {
                if (valid) {
                    vm.$emit('submit', this.form);
                    vm.close();
                }
            })
        }
    }
}
</script>

<style scoped>

</style>
