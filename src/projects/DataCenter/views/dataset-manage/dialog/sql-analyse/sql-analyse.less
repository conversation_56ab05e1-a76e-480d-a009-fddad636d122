.sqlAnalyse {

}

@textH: 96px;
@tableMt: 10px;
@btnH: 30px;
.div_main {
  position: fixed;
  left: 0;
  top: 4rem;
  width: 100%;
  height: calc(100% - 4rem);
  overflow: hidden;
  background: #f5f5f5;
  z-index: 30;
}

.right {
  background: #fff;
  margin: 10px;
  padding: 10px;
  height: calc(100% - 88px);
  border: 1px solid #ddd;
}

.right_table {
  margin-top: 10px;
}

/*.div_main /deep/ .el-textarea__inner {
    max-height: 96px;
}*/

.div_bottom {
  position: absolute;
  left: 10px;
  bottom: @btnH;
}

.ta_selet_content {
  /*margin-top: 5px;*/
  height: @textH;
}

.el_select_value {
  /*float: left;*/
  display: inline-block;
  width: 100px;
  margin-left: 10px;
}

.btn {
  /*float: left;*/
  margin-left: 10px;
}

.getTableH {
  height: calc(100% - @textH - @btnH - @btnH);
}

.ce-panel_title-bg {
  overflow: hidden;
  background: #fafafa;
  //border-bottom: 1px solid #ddd;
}

.ce-panel_title {
  line-height: 40px;
  float: left;
  color: #555;
  font-size: 14px;
  padding: 0 16px;
  height: 40px;
}

.ce-buttons_group {
  float: right;
  padding: 5px 10px 0 0;
}

.ce-visual_title {
  border: none;
  outline: none;
  //border-bottom: 1px solid #ccc;
  background: none;
  color: #289bf1;
}

.name_txt {
  padding: 0 10px;
  font-weight: normal;
  font-size: 14px;
  max-width:calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 120px;
  cursor: pointer;
}
