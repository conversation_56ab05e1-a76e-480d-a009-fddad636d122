<template>
    <div class="dateZipperTablePlugin zipperPlugin">
        <el-tabs v-model="activeName">
            <el-tab-pane label="基本设置" name="baseConfig"></el-tab-pane>
            <el-tab-pane label="映射配置" name="mappingConfig"><!--参数配置弹窗内容页--></el-tab-pane>
        </el-tabs>
        <div class="ce-zipper_cont" v-if="activeName === 'baseConfig'">
            <el-form class="ce-zipper_list" label-position="right" label-width="160px">
                <el-form-item label="选择拉链表:" required>
                    <el-select
                            size="small"
                            v-model="zipperTableId"
                            placeholder="请选择字段"
                            filterable
                            @change="tableChange"
                    >
                        <el-option
                                v-for="item in tableInfo"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="源表排序字段:" required>
                    <el-select
                            size="small"
                            v-model="timeField"
                            placeholder="请选择字段"
                            filterable
                    >
                        <el-option
                                v-for="item in incColumns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="源表分组字段:" required>
                    <el-row>
                        <el-select
                                size="small"
                                v-model="groupField"
                                placeholder="请选择"
                                filterable
                                multiple
                        >
                            <el-option
                                    v-for="item in incColumns"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code">
                            </el-option>
                        </el-select>
                    </el-row>
                </el-form-item>
                <el-form-item label="源表状态字段:" required>
                    <el-select
                            size="small"
                            v-model="statField"
                            placeholder="请选择"
                            filterable
                            clearable
                    >
                        <el-option
                                v-for="item in incColumns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="拉链开始时间:" required>
                    <el-select
                            size="small"
                            v-model="startColumn"
                            placeholder="请选择"
                            filterable
                    >
                        <el-option
                                v-for="item in zipperColumns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="拉链结束时间:" required>
                    <el-select
                            size="small"
                            v-model="endColumn"
                            placeholder="请选择"
                            filterable
                    >
                        <el-option
                                v-for="item in zipperColumns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div class="ce-mapping" v-else-if="activeName === 'mappingConfig'">
            <el-row class="mb10">
                <el-col :span="5">
                    <el-input
                            @keyup.enter.native="searchAddColumn"
                            placeholder="请输入内容"
                            v-model="selectAddColumn">
                        <i slot="suffix" class="el-input__icon el-icon-search" @click="searchAddColumn"></i>
                    </el-input>
                </el-col>

                <div class="r ce-disable_for-btn">
                    <!--<el-button type="primary" size="small" @click="autoMapping">自动映射</el-button>-->
                    <el-dropdown @command="handleCommand" type="primary" size="small" class="mr5" >
                        <el-button type="primary">
                            {{dropValue}}<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-for="item in dropDownItems " :key="item.label" :command="item.label">{{item.label}}</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button type="primary" @click="addRuleEdit">手动添加</el-button>
                </div>
            </el-row>
            <el-table
                    :data="tableData"
                    size="small"
                    border
                    :height="tableData.length ? 'calc(100% - 38px)' : 'auto'"
            >
                <el-table-column v-for="(item , inx) in tHead"
                                 :key="inx"
                                 :label="item.label"
                                 :prop="item.prop"
                                 align="center"
                                 :show-overflow-tooltip="true"

                >
                    <template slot-scope="scope">
                        <label v-if="item.prop==='addColumn'" v-text="showZhName(scope.row[item.prop])"></label>
                        <label v-if="item.prop==='addType'">{{ scope.row[item.prop] }}</label>
                        <el-select
                                v-if="item.prop==='zipperColumn'"
                                size="small"
                                v-model="scope.row.zipperColumn"
                                placeholder="请选择"
                                filterable
                                clearable
                                @change="selectZipperColumn(scope.row.zipperColumn,scope.$index)"
                        >
                            <el-option
                                    v-for="item in zipperColumns"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code">
                            </el-option>
                        </el-select>
                        <label v-if="item.prop==='zipperType'">{{ scope.row[item.prop] }}</label>
                    </template>
                </el-table-column>

                <el-table-column
                        label="操作"
                        prop="operate"
                        width="70"
                        align="center"
                >
                    <template slot="header" slot-scope="scope">
                        <em class="icon ce-link_text f14 ml5" title="添加" @click="addRuleEdit">&#xe6e1;</em>
                    </template>
                    <template slot-scope="scope">
                        <el-popconfirm
                                title="确定删除吗？"
                                @confirm="deleteData(scope.row, scope.$index)"
                        >
                            <span
                                    slot="reference"
                                    class="ce-link_text"
                                    :title="btnDeleteTxt"
                                    v-html="btnDeleteTxt"
                            ><!--&#xe847;--></span>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import * as listApi from "@/projects/DataCenter/views/dataSources/service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {outputMixins} from "@/api/commonMethods/output";

export default {
    name: "dateZipperTablePlugin",
    mixins: [pluginMixins, servicesMixins, commonMixins, listApi.servicesMixins, outputMixins],
    components: {preView},
    props: {
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    data() {
        return {
            dropDownItems: [{label: "按字段名映射"}, {label:"按字段中文名映射"}],
            dropValue:"按字段名映射",
            handColumns: [],
            selectHandAddColumn: '',
            incColumnTableName: '',
            zipperTableName: '',
            showConfig: false,
            selectAddColumn: '',
            activeName: 'baseConfig',
            title: '添加解析字段',
            operateIcons: [
                {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteData}
            ],
            dropDownItems: [{label: "按字段名映射"}, {label:"按字段中文名映射"}],
            tHead: [
                {
                    prop: "addColumn",
                    label: "增量表字段别名",
                },
                {
                    prop: "addType",
                    label: "增量表字段类型",
                },
                {
                    prop: "zipperColumn",
                    label: "拉链表字段别名",
                },
                {
                    prop: "zipperType",
                    label: "拉链表字段类型",
                },

            ],
            statsDisable: true,
            disabled: true,
            addbtn: "添加",
            loading: false,
            dataTypes: ['Float', 'Double', 'BigDecimal'],
            isSeniorShow: {},


            status: false,

            dataDispose: "",//数据处理表达式输入框绑定的数据
            //分组统计
            classificationFormData: [],
            inputType: [],
            rowData1: '',

            businessField: '',
            timeField: '',
            groupField: [],
            statField: '',
            incrementField: '',
            zipperOrder: '',
            fillField: '',
            startDate: 'start_field',
            endDate: 'end_field',
            updataStat: '',
            columns: [],
            incColumns: [],
            zipperColumns: [],
            updataStatList: [
                {
                    name: "数据覆盖",
                    value: "dataCoverage"
                },
                {
                    name: "数据累积",
                    value: "dataAccumulation"
                }
            ],
            zipperDirection: [
                {
                    name: "升序",
                    value: "asc"
                },
                {
                    name: "降序",
                    value: "desc"
                }
            ],
            isAdd: [
                {
                    name: "true",
                    value: "true"
                },
                {
                    name: "false",
                    value: "false"
                }
            ],

            // realZipperTableCode: "",
            zipperTableId: "",
            incTableId: "",
            tableData: [],
            selectTreeOpt: [],
            tableTreeOpt: [],
            // databaseId: "",
            treeProps: {
                id: 'id',
                label: "label",
                children: "children",
            },
            defaultProps: {
                value: 'id',
                label: "label",
                children: "children",
            },
            startColumn: "",
            endColumn: "",
            tableInfo: [],
            oldZipperTableId: "",
            oldIncTableId: "",
            saveTableData: [],
            multipleSelection: []
        }
    },
    methods: {
        handleCommand(value) {
            this.dropValue = value;
            this.autoMapping();
        },
        save(...args) {
            this.saveFn(...args);
        },
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        init() {
            const vm = this, {pluginServices, pluginMock, loadParams} = vm;
            let services = vm.getServices(pluginServices, pluginMock);
            loadParams.loading = true;
            vm.stepId = vm.sourceData.id;
            services.queryDateZipperData(vm.stepId, vm.rowData.transId, loadParams).then(res => {
                if (res.data.status === 0) {
                    let inputColumn = res.data.data.inputColumn;
                    if (inputColumn !== null && inputColumn !== undefined) {
                        inputColumn.forEach(i => {
                            let column = {
                                name: i.columnZhName !== null ? i.columnZhName : i.columnName,
                                code: i.columnName,
                                type: i.columnType
                            };
                            vm.columns.push(column);
                        });
                    }
                    vm.tableInfo = res.data.data.tableInfo;
                    if (vm.tableInfo.length) {
                        this.incColumnTableName = vm.tableInfo[0].label;
                        this.zipperTableName = vm.tableInfo[1].label;
                    }
                    let pluginMeta = res.data.data.zipperTablePlugin;

                    vm.incTableId = pluginMeta.incTableId;

                    let tableIds = vm.tableInfo.map(n=> n.value);
                    let haveTableId = pluginMeta.zipperTableId != null && tableIds.includes(pluginMeta.zipperTableId);
                    if (vm.tableInfo.length >= 2) {

                        if(haveTableId){
                            vm.zipperTableId = pluginMeta.zipperTableId;
                        }
                        else {
                            vm.zipperTableId = vm.tableInfo[1].value;
                        }
                        //vm.zipperTableId = (pluginMeta.zipperTableId === null || vm.zipperTableIsChange()) ? vm.tableInfo[1].value : pluginMeta.zipperTableId;

                        vm.oldZipperTableId = (pluginMeta.zipperTableClassifierStatId === null || pluginMeta.zipperTableClassifierStatId === "") ? vm.tableInfo[0].classifierStatId : pluginMeta.zipperTableClassifierStatId;
                        vm.oldIncTableId = (pluginMeta.incTableClassifierStatId === null || pluginMeta.incTableClassifierStatId === "") ? vm.tableInfo[1].classifierStatId : pluginMeta.incTableClassifierStatId;
                    } else {
                        vm.zipperTableId = "";
                    }


                    if (inputColumn !== null && inputColumn !== undefined) {
                        inputColumn.forEach(i => {
                            let column = {
                                name: i.columnZhName !== null ? i.columnZhName : i.columnName,
                                code: i.columnName,
                                type: i.columnType
                            };

                            if (i.stepId === vm.zipperTableId) {
                                vm.zipperColumns.push(column);
                            } else {
                                vm.incColumns.push(column);
                            }
                        });
                    }


                    let isNullTableData = null !== pluginMeta.mappingColumnsJSON && '' !== pluginMeta.mappingColumnsJSON && undefined !== pluginMeta.mappingColumnsJSON;

                    if (isNullTableData && vm.tableIsChange() && haveTableId) {
                        vm.tableData = JSON.parse(pluginMeta.mappingColumnsJSON);
                    } else {
                        let flag = false;
                        for (let i = 0; i < vm.incColumns.length; i++) {
                            flag = false;
                            let inc = vm.incColumns[i];
                            for (let j = 0; j < vm.zipperColumns.length; j++) {
                                let zipper = vm.zipperColumns[j];
                                if (inc.code === zipper.code) {
                                    vm.tableData.push({
                                        "addColumn": inc.code,
                                        "addName": inc.name,
                                        "addType": inc.type,
                                        "zipperColumn": zipper.code,
                                        "zipperName": zipper.name,
                                        "zipperType": zipper.type,
                                    });
                                    flag = true;
                                    break;
                                }
                            }
                            if (!flag) {
                                vm.tableData.push({
                                    "addColumn": inc.code,
                                    "addType": inc.type,
                                    "addName": inc.name,
                                    "zipperColumn": '',
                                    "zipperType": '',
                                    "zipperName": '',
                                });
                            }
                        }
                    }


                    if (null !== pluginMeta.groupFieldJSON && '' !== pluginMeta.groupFieldJSON && undefined !== pluginMeta.groupFieldJSON) {
                        if (vm.tableIsChange() && haveTableId) {
                            vm.groupField = JSON.parse(pluginMeta.groupFieldJSON);
                        } else {
                            vm.groupField = [];
                        }
                    } else {
                        vm.groupField = [];
                    }

                    if (vm.tableIsChange() && haveTableId) {
                        vm.timeField = pluginMeta.timeField  || "";
                        vm.statField = pluginMeta.statField || "";
                        vm.startColumn = pluginMeta.startColumn || "";
                        vm.endColumn = pluginMeta.endColumn || "";
                    } else {
                        vm.timeField = "";
                        vm.statField = "";
                        vm.startColumn = "";
                        vm.endColumn = "";
                    }
                    vm.loadParams.loading = false;
                }
                vm.tableData.forEach(item => {
                    vm.saveTableData.push({
                        addColumn: item.addColumn,
                        addType: item.addType,
                        zipperColumn: item.zipperColumn,
                        zipperType: item.zipperType
                    });
                });
            })
        },
        initDBTree() {
            const vm = this, {listApi, listMock, settings} = this;
            let dw_service = vm.getServices(listApi, listMock);
            settings.loading = true;
            dw_service.queryDirTree().then(res => {
                // console.log("业务库数据：", res);
                vm.selectTreeOpt = vm.resTreeDataDispose(res.data.data);
            })
        },
        packageData(val) {
            const _this = this;
            let isHasDB = true;
            for (let i = val.length - 1; i >= 0; i--) {
                if (val[i].children.length > 0) {
                    let isHas = _this.packageData(val[i].children);
                    if (!isHas) {
                        val.splice(i, 1)
                    }
                } else {
                    val[i].children = [];
                }
            }
            if (val.length <= 0) {
                isHasDB = false;
            }
            return isHasDB;
        },
        tableChange(value) {
            let _this = this;
            let temp = _this.incColumnTableName;
            _this.incColumnTableName = _this.zipperTableName;
            _this.zipperTableName = temp;
            _this.tableData = [];
            _this.timeField = '';
            _this.groupField = [];
            _this.statField = '';
            _this.startColumn = '';
            _this.endColumn = '';

            let middleColumn = [];
            middleColumn = _this.incColumns;
            _this.incColumns = _this.zipperColumns;
            _this.zipperColumns = middleColumn;
            _this.incColumns.forEach(i => {
                _this.tableData.push({
                    "addColumn": i.code,
                    "addType": i.type,
                    "zipperColumn": "",
                    "zipperType": "",
                })
            });

            let middleClassifierStatId = "";
            middleClassifierStatId = _this.oldZipperTableId;
            _this.oldZipperTableId = _this.oldIncTableId;
            _this.oldIncTableId = middleClassifierStatId;
        },
        updataChange(updataStat) {
            if (updataStat == "dataCoverage") {
                this.statsDisable = false;
            } else {
                this.statField = null;
                this.statsDisable = true;
            }
        },
        saveRowData() {
            this.dataDispose = this.rowData1.serviceOrgId;
        },
        editFn(data) {
            let rowData = {
                columnCode: '数据处理表达式code',
                columnName: '数据处理表达式',
                serviceOrgId: data,
            };
            this.rowData1 = rowData;
            this.$refs.setting.openSetting(this.sourceData, this.rowData1);
        },
        selectDataType(val) {
            val.hasPrecision = this.checkDataType(val.outputTypeValue);
        },
        getStepIdByClassifierStatId(classifierStatId) {
            let _this = this;
            let stepId = '';
            _this.tableInfo.forEach(i => {
                if (classifierStatId === i.value) {
                    stepId = i.stepid;
                }
            });

            return stepId;
        },
        getIncTableIdClassifierStatId(zipperTableClassifierStatId) {
            let _this = this;
            let classifierStatId = '';
            _this.tableInfo.forEach(i => {
                if (zipperTableClassifierStatId !== i.value) {
                    classifierStatId = i.value;
                }
            });
            return classifierStatId;
        },
        zipperTableIsChange() {
            let _this = this;
            let tableId = [];
            _this.tableInfo.forEach(i => {
                tableId.push(i.value);
            });

            if (tableId.indexOf(_this.zipperTableId) === -1) {
                return true;
            }

            return false;
        },
        tableIsChange() {
            let _this = this;
            let classifierStatId = [];
            _this.tableInfo.forEach(i => {
                classifierStatId.push(i.classifierStatId);
            });

            if (classifierStatId.indexOf(_this.oldIncTableId) === -1) {
                return false;
            }

            if (classifierStatId.indexOf(_this.oldZipperTableId) === -1) {
                return false;
            }

            return true;
        },
        showZhName(code) {
            let zhName = code;
            for (let i = 0; i < this.incColumns.length; i++) {
                let item = this.incColumns[i];
                if (code === item.code) {
                    zhName = item.name;
                    break;
                }
            }
            return zhName;
        },
        isInColumn(columns, column) {
            for (let i = 0; i < columns.length; i++) {
                if (columns[i].code == column) {
                    return column;
                }
            }
            return '';
        },
        isALLInColumn(columns, columns2) {

            let inColumns = [];
            columns.forEach(i => {
                inColumns.push(i.code);
            });

            if (columns2.length === 0) {
                return false;
            }

            for (let j = 0; j < columns2.length; j++) {
                if (inColumns.indexOf(columns2[j]) == -1) {
                    return false;
                }
            }
            return true;
        },
        // 分组统计删除操作
        deleteClassificationData(row, index) {
            this.classificationFormData.splice(index, 1);
        },
        saveFn(saveName,showTip) {
            if (!this.saveEditDialog) {
                return;
            }
            let _this = this;
            let pluginMeta = {};

            if (_this.timeField === '' || _this.timeField === null) {
                _this.$message.warning("请先选择排序字段！");
                return;
            }

            if (this.groupField.length === 0) {
                _this.$message.warning("请配置源表分组字段！");
                return;
            }

            /*for (let i = 0; i < _this.tableData.length; i++) {
                let item = _this.tableData[i];
                if (item.zipperColumn === '' || item.zipperColumn === undefined || item.zipperColumn === null) {
                    _this.$message.warning('请配置对应拉链表字段！');
                    return;
                }
            }*/

            if (this.statField === '' || this.statField === null || this.statField === undefined) {
                _this.$message.warning('请配置源表状态字段！');
                return;
            }

            if (this.startColumn === '' || this.startColumn === null || this.startColumn === undefined) {
                _this.$message.warning('请配置拉链开始时间！');
                return;
            }

            if (this.endColumn === '' || this.endColumn === null || this.endColumn === undefined) {
                _this.$message.warning('请配置拉链结束时间！');
                return;
            }

            for (let i = 0; i < _this.groupField.length; i++) {
                let info = _this.tableData.find(p=>p.addColumn == _this.groupField[i]);
                if(info && info.zipperColumn === ""){
                    _this.$message.warning('源表分组字段必须有字段映射！');
                    return;
                }
            }

            pluginMeta["incTableClassifierStatId"] = _this.oldIncTableId;
            pluginMeta["zipperTableClassifierStatId"] = _this.oldZipperTableId;
            // pluginMeta["realZipperDataBaseId"] = _this.databaseId;
            pluginMeta["incTableId"] = _this.incTableId;
            pluginMeta["zipperTableId"] = _this.zipperTableId;
            // pluginMeta["realZipperTableCode"] = _this.realZipperTableCode;
            pluginMeta['mappingColumnsJSON'] = JSON.stringify(_this.tableData);
            pluginMeta['timeField'] = _this.timeField;
            pluginMeta['groupFieldJSON'] = JSON.stringify(_this.groupField);
            pluginMeta['statField'] = _this.statField;

            pluginMeta['startColumn'] = _this.startColumn;
            pluginMeta['endColumn'] = _this.endColumn;
            pluginMeta['startDataType'] = _this.getDataType(_this.startColumn);
            pluginMeta['endDataType'] = _this.getDataType(_this.endColumn);

            let inColumns = [];
            _this.incColumns.forEach(i => {
                inColumns.push(i.code);
            });
            let ziColumns = [];
            _this.zipperColumns.forEach(i => {
                ziColumns.push(i.code);
            });
            pluginMeta['inputColumns'] = JSON.stringify(inColumns);
            pluginMeta['zipperColumns'] = JSON.stringify(ziColumns);


            _this.loading = true;
            _this.loadParams.loading = true;
            _this.$axios.post("/cicada/zipperTable/saveDateZipperPlugin?tranStepId=" + _this.stepId, pluginMeta).then(res => {
                if (res.data.status === 1) {
                    _this.$message.error(res.data.msg);
                } else {
                    showTip && _this.$message.success("保存成功")
                    if (saveName) saveName();
                    _this.$emit("setPreviewAble");
                }
                _this.loading = false;
                _this.loadParams.loading = false;
            }).catch(err => {
                _this.loading = false;
                _this.loadParams.loading = false;
                _this.$message.error("服务器发生异常，请联系管理员")
            })

        },
        getDataType(field) {
            let _this = this;
            let type = 'String';
            for (let i = 0; i < _this.zipperColumns.length; i++) {
                if (field === _this.zipperColumns[i].code) {
                    type = _this.zipperColumns[i].type;
                    break;
                }
            }
            return type;
        },
        checkDataType(val) {
            if (this.dataTypes.indexOf(val) > -1) {
                return true;
            } else {
                return false;
            }
        },
        previewFn() {
            this.$refs.preview.show(this.rowData, this.sourceData.id);
        },
        selectTreeChange(val, value) {
            const _this = this;
            this.getTargetTable(value.data.id, "");
        },
        getTargetTable(instanceId, content) {
            const vm = this, {listApi, listMock} = this;
            let dw_service = vm.getServices(listApi, listMock);
            vm.tableTreeOpt = [];
            dw_service.queryDataBaseTableAll(instanceId).then(res => {
                if (res.data.status === 0) {
                    let data = res.data.data;
                    data.forEach(item => {
                        let tableNode = {
                            name: item.name,
                            label: item.code,
                            value: item.id,
                        };
                        vm.tableTreeOpt.push(tableNode);
                    })
                }
            })
        },
        addRuleEdit() {
            if (this.tableData.length == this.incColumns.length) {
                this.$message.warning("暂无未映射字段！");
                return;
            }

            let addColumns = [];
            this.tableData.forEach(i => {
                addColumns.push(i.addColumn);
            });

            for (let i = 0; i < this.incColumns.length; i++) {
                let flag = false;
                let inc = this.incColumns[i];
                if (addColumns.indexOf(this.incColumns[i].code) == -1) {
                    for (let j = 0; j < this.zipperColumns.length; j++) {
                        let zipper = this.zipperColumns[j];
                        if (inc.code === zipper.code) {
                            this.tableData.push({
                                addColumn: inc.code,
                                addType: inc.type,
                                addName: inc.name,
                                zipperColumn: zipper.code,
                                zipperType: zipper.type,
                                zipperName: zipper.name,
                            });
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        this.tableData.push({
                            addColumn: inc.code,
                            addType: inc.type,
                            addName:inc.name,
                            zipperColumn: '',
                            zipperType: '',
                            zipperName:''
                        });
                    }
                    break;
                }
            }
        },
        //删除操作
        deleteData(row, index) {

            this.tableData.splice(index, 1);
            this.$message({
                type: 'success',
                message: '删除成功!'
            });

        },
        selectZipperColumn(zipperColumn, index) {
            let zipperTy = '';
            for (let i = 0; i < this.zipperColumns.length; i++) {
                if (this.zipperColumns[i].code == zipperColumn) {
                    zipperTy = this.zipperColumns[i].type;
                    break;
                }
            }
            this.tableData[index].zipperType = zipperTy;
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        saveEditDialog() {
            let _this = this;
           /* for (let i = 0; i < _this.tableData.length; i++) {
                let item = _this.tableData[i];
                if (item.zipperColumn === '' || item.zipperColumn === undefined || item.zipperColumn === null) {
                    _this.$message.warning('请配置对应拉链表字段！');
                    return false;
                }
            }*/
            _this.saveTableData = [];
            _this.tableData.forEach(item => {
                _this.saveTableData.push({
                    addColumn: item.addColumn,
                    addType: item.addType,
                    addName: item.addName,
                    zipperColumn: item.zipperColumn,
                    zipperType: item.zipperType,
                    zipperName: item.zipperName,
                });
            });
            //this.close();
        },
        closeEditDialog() {
            this.tableData = [];
            this.saveTableData.forEach(item => {
                this.tableData.push({
                    addColumn: item.addColumn,
                    addType: item.addType,
                    addName: item.addName,
                    zipperColumn: item.zipperColumn,
                    zipperType: item.zipperType,
                    zipperName: item.zipperName,
                });
            });
            this.clear_data();
            this.close();
        },
        autoMapping() {
            this.$dgConfirm('此操作将覆盖之前的配置, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData.forEach(item => {
                    item.zipperColumn = "";
                    this.zipperColumns.forEach(zipper => {
                        if (this.dropValue === '按字段名映射' ? item.addColumn === zipper.code : item.addName === zipper.name) {
                            item.zipperColumn = zipper.code;
                            item.zipperType = zipper.type;
                        }
                    });
                });
                this.$message({
                    type: 'success',
                    message: '映射成功!'
                });
            });

        },
        searchAddColumn() {
            if (this.selectAddColumn === '' || this.selectAddColumn === undefined) {
                this.incColumns.forEach(i => {
                    this.tableData.push({
                        "addColumn": i.code,
                        "addType": i.type,
                        "addName": i.name,
                        "zipperColumn": "",
                        "zipperType": "",
                        "zipperName": "",
                    })
                });
            } else {
                this.tableData = [];
                for (let i = 0; i < this.incColumns.length; i++) {
                    let incColumn = this.incColumns[i];
                    if (incColumn.name.indexOf(this.selectAddColumn) >= 0) {
                        this.tableData.push({
                            "addColumn": incColumn.code,
                            "addType": incColumn.type,
                            "zipperColumn": "",
                            "zipperType": "",
                        })
                    }
                }
            }

        },
        handConfig() {
            this.handColumns = [];
            this.incColumns.forEach(item => {
                this.handColumns.push(item);
            });
            let tempSelect = [];
            this.tableData.forEach(item => {
                tempSelect.push({
                    name: item.addColumn,
                    type: item.addType,
                    code: item.addColumn,
                });
            });
            setTimeout(() => {
                for (let i = 0; i < this.handColumns.length; i++) {
                    for (let j = 0; j < tempSelect.length; j++) {
                        let handC = this.handColumns[i];
                        let multi = tempSelect[j];
                        if (handC.code === multi.code) {
                            this.$nextTick(() => {
                                this.$refs.multipleTable.toggleRowSelection(this.handColumns[i], true);
                            });
                            break;
                        }
                    }
                }

            }, 100);


            // this.$refs.multipleTable.toggleRowSelection(row);
        },
        handAddColumn() {
            //this.selectHandAddColumn;
            this.handColumns = [];
            this.incColumns.forEach(item => {
                if (item.name.indexOf(this.selectHandAddColumn) >= 0 || item.code.indexOf(this.selectHandAddColumn) >= 0) {
                    this.handColumns.push(item);
                }
            })
            let tempSelect = [];
            this.tableData.forEach(item => {
                tempSelect.push({
                    name: item.addColumn,
                    type: item.addType,
                    code: item.addColumn,
                });
            });
            setTimeout(() => {
                for (let i = 0; i < this.handColumns.length; i++) {
                    for (let j = 0; j < tempSelect.length; j++) {
                        let handC = this.handColumns[i];
                        let multi = tempSelect[j];
                        if (handC.code === multi.code) {
                            this.$nextTick(() => {
                                this.$refs.multipleTable.toggleRowSelection(this.handColumns[i], true);
                            });
                            break;
                        }
                    }
                }

            }, 100);
        },
        setConfigField() {
            this.tableData = [];
            this.multipleSelection.forEach(item => {
                this.tableData.push({
                    addColumn: item.code,
                    addType: item.type,
                    zipperColumn: '',
                    zipperType: '',
                });
            });
            this.close();
        },
        closeDia() {
            this.clear_data();
            this.close();
        }
    },
    created() {
        this.init();
        // this.initDBTree();
    }
}
</script>
<style src="../css/plugin.less" scoped lang="less"></style>
<style scoped lang="less">

.field-title {
    display: flex;
    align-items: center;
}

h3 {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
}
</style>
