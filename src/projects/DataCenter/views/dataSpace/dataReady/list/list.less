.d-s-r-top {
  display: flex;
  justify-content: flex-start;
  .d-s-r-t-serach {
    width: 272px;
  }
  .data-contact__search{
    width:500px;
    margin-left:auto;
  }

}
.d-s-r-table {
  padding: 15px 0;
  height: calc(100% - 60px);
  display: flex;
  overflow: hidden;
  align-items: center;
  .detailName{
    color:#0088FF;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
  }
  .r-c-action::after {
    content: "";
    display: inline-block;
    width: 1px;
    height: 12px;
    background-color: rgba(0, 0, 0, 0.15);
    margin: 0 4px 0 10px;
  }
  .r-c-action:last-child::after {
    width:0
  }
}

.list {
  height: 100%;
}
.roate {
  transform: rotate(40deg);
}
.actions {
  margin: 0 -12px;
  &__item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      //background: rgba($color: #0088ff, $alpha: 0.09);
      background: rgba(0, 136, 255, 0.09);
    }
    &-info {
      padding-left: 8px;
      div {
        color: rgba(0, 0, 0, 0.85);
        line-height: 1;
      }
      p {
        color: rgba(0, 0, 0, 0.44);
      }
    }
    &-icon {
      display: flex;
      align-items: flex-start;
      margin-top: -6px;
      i {
        font-size: 20px;
      }
    }
  }
}
