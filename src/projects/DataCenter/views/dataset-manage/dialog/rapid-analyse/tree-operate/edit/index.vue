<template>
    <common-dialog custom-class="edit"
                   v-loading="settings.loading"
                   :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <edit-page ref="editPage" v-if="reNew" :node="nodeData"></edit-page>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button v-if="nodeData.canEdit !== false" type="primary" @click="submit">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>
<script src="./edit.js"></script>
