import {commonMixins} from "@/api/commonMethods/common-mixins";

export const servicesMixins = {
    mixins: [commonMixins],
    data() {
        const {isMock} = this;
        let AIModelingServices = require("@/projects/DataCenter/services/AIModeling-services/AIModeling-services"),
            AIModelingMock = isMock ? require("@/projects/DataCenter/services/AIModeling-services/AIModeling-services") : {};
        return {
            AIModelingServices,
            AIModelingMock,
            services: this.getServices(AIModelingServices, AIModelingMock)
        }
    }
}
