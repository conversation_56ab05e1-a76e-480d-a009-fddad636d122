<template>
    <div class="resultReuse">
        <div class="filter-b">
            <em class="icon r ce_link lh30" @click="addVisualPanel">&#xe6e1;</em>
            <el-input placeholder="请输入资源名称搜索" v-model="filterText" size="small" class="ce-search">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
        </div>
        <!--        <ul class="list-tab">-->
        <!--            <li class="list-item"-->
        <!--                v-for="(item , index) in listTabs"-->
        <!--                :key="index"-->
        <!--                :class="{'active' : activeTab === index}"-->
        <!--                @click="sourceTabClick(index)"-->
        <!--            >{{item}}</li>-->
        <!--        </ul>-->
        <div class="tree-cont selectn">
            <el-tree
                    ref="tree"
                    :data="treeData"
                    node-key="id"
                    default-expand-all
                    :props="treeProps"
                    :filter-node-method="filterNode"
            >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span class="node-label" v-if="node.level > 2" :title="node.label" draggable="true"
                              @dragend="dragend"
                              @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data})"
                        >{{ node.label }}</span>
                        <span v-else>{{ node.label }}</span>
                    </span>
            </el-tree>
        </div>
        <common-dialog
                :title="newModel.title"
                :visible.sync="newModel.visible"
                width="30%"
                custom-class="ce-prompt"
                @closed="closeDialog"
        >
            <NewModelName v-if="newModel.reNew" ref="newName" :label="label"/>
            <div slot="footer">
                <el-button @click="newModel.visible = false" size="mini">取消</el-button>
                <el-button @click="newModeEvent" size="mini" type="primary">确定</el-button>
            </div>
        </common-dialog>
        <new-model style="z-index: 500"
                   ref="newModel_panel"
                   @closePlane="closeEditPanel"
                   v-if="showEditPanel"
                   :rowData="nodeData" ></new-model>
    </div>
</template>

<script>
    import {globalBus} from '@/api/globalBus';
    import CommonDialog from "@/components/common/dialog/CommonDialog"
    import NewModelName from "../beatLibraryModeling/NewModelName"
    import newModel from "../beatLibraryModeling/NewModel"
    export default {
        name: "ResultReuse",
        components: {
            CommonDialog,
            NewModelName,
            newModel
        },
        props: {
            general: Boolean
        },
        data() {
            return {
                treeData: [],
                filterText: '',
                // listTabs : ['全部','今日','一周内','一月内'],
                activeTab: 0,
                treeProps: {
                    label: 'name'
                },
                newModel: {
                    title: "",
                    visible: false,
                    reNew: false
                },
                label: {},
                showEditPanel :false ,
                nodeData : {}
            }
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        methods: {
            closeEditPanel(){
                this.showEditPanel = false;
                this.initTree();
            },
            newModeEvent(){
                const vm = this;
                let newName = this.$refs.newName.getFormData();
                if (newName.engName === "") {
                    return;
                }
                if (newName.engName.length >300) {
                    this.$message.warning("英文名长度不能超过300！");
                    return;
                }
                //保存
                let data ={
                    chName:newName.chName,
                    engName:newName.engName,
                    parentId:newName.savePath,
                };
                this.$axios.post("/manage/sql/saveModel",data).then(res => {
                    if (res.data.status === 0) {
                        if (res.data.data.length === 32) {
                            vm.$message.success("新建成功！");
                            vm.nodeData = {name: newName.chName, id: res.data.data};
                            vm.showEditPanel = true;
                            vm.newModel.visible = false;
                        } else {
                            vm.$message.warning(res.data.data);
                        }
                    }
                }).catch(err => {
                    console.log(err);
                    vm.$message.error("服务器异常，请联系管理员！");
                })
            },
            addVisualPanel() {
                const vm = this;
                let {newModel} = this;
                vm.label = {
                    engLabel: "英文名:",
                    chLabel: "中文名:",
                };
                newModel.title = "新建跨库模型";
                newModel.visible = true;
                newModel.reNew = true;
                vm.$nextTick(() => {
                    vm.$refs.newName.showSelect("model");
                })
            },
            closeDialog() {
                this.newModel.reNew = false;
            },
            dragend() {
                globalBus.$emit('dragend');
            },
            addNode(plug) {
                globalBus.$emit('plugNode', plug);
            },
            sourceTabClick(index) {
                // console.log("时间"+index);
                this.activeTab = index;
                this.initTree();
                // this.$axios.get("/dataModeling/resultReuseObj?condition="+this.filterText+"&dateType="+index)
                //     .then(res =>{
                //         console.log("结果复用返回结果：");
                //         console.log(res);
                //     }).catch(err =>{
                //     console.error(err);
                // })
                // switch (index) {
                //     case 0 :
                //         this.treeData = this.tree1 ;
                //         break;
                //     case 1 :
                //         this.treeData = this.tree2 ;
                //         break;
                //     default:
                //         break;
                // }

            },
            initTree() {
                //结果复用
                this.$axios.get("/dataModeling/resultReuseObj?condition=" + this.filterText + "&dateType=" + this.activeTab)
                    .then(res => {
                        this.treeData = res.data.data;

                        // this.treeData = [];
                        // let name = res.data.data[0].name;
                        // let result = {
                        //     name:name,
                        //     children:[],
                        // };
                        // res.data.data[0].children.forEach(item =>{
                        //     // console.log(item);
                        //     let pluginType = "";
                        //     if(item.emType === "DATASET_OBJ_FILEDB"){
                        //         pluginType = "parquetPlugin";
                        //     }else{
                        //         pluginType = "rdbTableInput";
                        //     }
                        //     let data ={
                        //         name:item.name,
                        //         type:"DS",
                        //         keyWord:pluginType,
                        //         classifierId:item.id,
                        //         //TODO 暂时写死
                        //         //classifierId:"a9c1a3080b8446b3b96c6c73846e72dc",//GBASE
                        //     };
                        //     result.children.push(data);
                        // });
                        // this.treeData.push(result);
                    }).catch(err => {
                    console.error(err);
                });
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
        },
        created() {
            this.initTree();
        }
    }
</script>

<style scoped>
    .resultReuse {
        height: 100%;
    }

    .list-tab {
        padding: 6px 0;
        overflow: hidden;
    }

    .list-item {
        font-size: 14px;
        float: left;
        margin: 0 6px;
        color: #666;
        cursor: pointer;
    }

    .list-item:hover, .list-item.active {
        color: #289bf1;
        border-bottom: 1px solid #289bf1;
    }

    .tree-cont {
        margin-top: 10px;
        height: calc(100% - 50px);
        overflow: auto;
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }

    .node-label {
        max-width: 166px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ce-search {
        width: 180px;
    }
</style>