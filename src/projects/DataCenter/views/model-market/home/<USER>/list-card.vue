<!--
排行榜 卡片
-->
<template>
    <div class="list-card poi" @click="detailsLook(data)">
        <div class="list-card_rank" v-if="index">
            <svg class="list-card_rank_icon" aria-hidden="true">
                <use :style="setIconColor" xlink:href="#icon-label"></use>
            </svg>
            <span>{{ index }}</span>
        </div>
        <div class="list-card_top">
            <div class="list-card_count">
                <div class="list-card_icon" :title="setName(data.type)">
                    <img class="list-card_img" v-if="data.logo && isBase64.test(data.logo)" :src="data.logo" alt="">
                    <svg class="list-card_ic" aria-hidden="true" v-else>
                        <use class="list-card_color" :xlink:href="setIcon(data.type)"></use>
                    </svg>
                </div>
                <div class="list-card_attr">
                    <p :title="data.name">{{ data.name }}</p>
                    <ul class="list-card_list">
                        <li :title="'关注:'+data.focuscount"><i class="dg-iconp icon-follow-b"></i>{{ data.focuscount }}</li>
                        <div class="list-card_divider"></div>
                        <li :title="'浏览:'+data.browsecount"><i class="dg-iconp icon-browse"></i>{{ data.browsecount }}</li>
                        <div class="list-card_divider"></div>
                        <li :title="'评分:'+data.avgscore"><i class="dg-iconp icon-focus"></i>{{ data.avgscore }}</li>
                    </ul>
                </div>
            </div>
            <div class="list-card_label" ref="label">
                <template v-if="data.label && data.label.length">
                    <div class="list-card_label_item" v-for="(lab , inx) in labels" :key="inx" :title="lab">
                        {{lab}}
                    </div>
                    <el-popover popper-class="ce-popover" placement="right" trigger="hover">
                        <div class="list-card_label_cont">
                            <div class="list-card_label_item list-card_label_lest" v-for="(lab , inx) in lestLabel" :key="inx" :title="lab">
                                {{lab}}
                            </div>
                        </div>
                        <div class="list-card_label_item list-card_label_more" slot="reference" v-if="labels.length < data.label.length">
                            {{ more }}{{ data.label.length - labels.length }}
                        </div>
                    </el-popover>
                </template>
                <div v-else class="list-card_label_no"> 无标签 </div>
            </div>
            <div class="list-card_dec" :title="data.introduction">{{ data.introduction }}</div>
        </div>
        <div class="list-card_footer">
            <div class="list-card_creator" :title="data.userName"><em class="dg-iconp icon-user"></em>{{ data.userName }}
            </div>
            <div class="list-card_creatTime" :title="data.publishtime"><em
                    class="dg-iconp icon-time-b"></em>{{ data.publishtime }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "list-card",
    props: {
        data: {
            type: Object,
            default: () => {
            }
        },
        index: Number
    },
    data() {
        return {
            isBase64:/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,
            more: "更多+",
            labels: [],
            lestLabel : []
        }
    },
    computed: {
        setIconColor() {
            const {index} = this;
            return index === 1 ? {fill: "#EA705E"} :
                    index === 2 ? {fill: "#F6C02E"} :
                            index === 3 ? {fill: "#BCD782"} : {fill: "#63727F" , "fill-opacity":"0.45"};
        },

    },
    methods: {
        setIcon(type ){
            let info = 'DASHBOARDS';
            switch (type) {
                case "DASHBOARDS":
                    info = "#icon-dashboard";
                    break;
                case "TRANS":
                    info = "#icon-model1";
                    break;
                case "SCRIPT":
                    info = "#icon-AImodel";
                    break;
                case "PORTAL":
                    info = "#icon-theme";
                    break;
                default:
                    info = "#icon-dashboard";
                    break;
            }
            return info;
        },
        setName(type){
            let name = type === 'DASHBOARDS' ? '仪表盘' :(type === 'TRANS' ? '数据模型' : (type === 'SCRIPT' ?  'AI模型' : '模型主题'));
            return name;
        },
        showLabel() {
            const vm = this, {data} = vm;
            let moreW = 76, //更多的宽度
                    labelW = 0;
            vm.labels = [];vm.lestLabel = [];
            vm.$nextTick(() => {
                let contW = vm.$refs.label.clientWidth;
                if (data.label && data.label.length) {
                    data.label.forEach((lab, i) => {
                        let itemW = lab.length * 12 + 20 > 140 ? 150 : lab.length * 12 + 30;
                        labelW += itemW;
                        if (labelW <= contW - moreW ) {
                            vm.labels.push(lab);
                        }else if(labelW <= contW && vm.labels.length === data.label.length) {
                            vm.labels.push(lab);
                        } else {
                            labelW -= itemW;
                            vm.lestLabel.push(lab);
                        }
                    })
                }
            })
        },
        //查看模型市场详情
        detailsLook(row){
            let routeData = this.$router.resolve({path: '/market/modelMarket', query: {id: row.id}});
            window.open(routeData.href, '_blank');
        },
    },
    mounted() {
        this.showLabel();
        window.addEventListener(
                "resize", this.showLabel, false
        );
    },
    destroyed() {
        window.removeEventListener(
                "resize", this.showLabel, false
        );
    }
}
</script>

<style scoped lang="less">
@textEll: {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.list-card {
    background: #FFFFFF;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 2px;
    position: relative;
    &:hover, &.active{
        border: 1px solid #0088FF;
        box-shadow: 0 5px 10px 0 rgba(24,144,255,0.30);
    }
    &_top {
        padding: 14px;
    }
    &_img {
        display: block;
        width: 36px;
        height: 36px;
    }
    &_icon {
        min-width: 52px;
        width: 52px;
        height: 52px;
        background: rgba(0, 136, 255, 0.09);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &_ic {
        width: 36px;
        height: 36px;
    }

    &_color {
        fill: rgba(0, 136, 255, 0.8);
    }

    &_count {
        display: flex;
    }

    &_attr {
        padding-left: 14px;
        flex: 1;
        width: calc(100% - 66px);
        > p {
            font-weight: bold;
            font-family: MicrosoftYaHei-Bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 22px;
            padding-bottom: 8px;
            width: calc(100% - 40px);
            @textEll();
        }
    }

    &_list {
        display: flex;
        justify-content: start;
        align-items: center;

        > li {
            padding-right: 8px;
            position: relative;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.24);
            max-width: calc(33.3% - 8px);
            @textEll();

            > i {
                font-size: 14px;
                padding-right: 6px;
            }
        }
    }

    &_divider {
        height: 12px;
        border-left: 1px solid #ddd;
        padding-left: 8px;
    }

    &_label {
        padding: 14px 0;
        height: 24px;
        display: flex;
        justify-content: start;

        &_item {
            border: 1px solid rgba(24, 144, 255, 0.60);
            border-radius: 12px;
            height: 24px;
            line-height: 22px;
            box-sizing: border-box;
            padding: 0 10px;
            font-size: 12px;
            color: #1890FF;
            max-width: 160px;
            margin-right: 10px;
            flex: none;
            @textEll();
        }
        &_cont {
            max-width:180px;
        }
        &_lest {
            display: inline-block;
        }
        &_lest + &_lest {
            margin-top: 8px;
        }
        &_more {
            margin-left: 4px;
            color: rgba(0, 0, 0, 0.45);
            border-color: #D9D9D9;
            cursor: pointer;
        }
        &_no{
            font-size: 12px;
            height: 24px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.45);
        }
    }

    &_dec {
        height: 42px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 21px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    &_footer {
        height: 42px;
        line-height: 42px;
        background: rgba(0, 0, 0, 0.02);
        padding: 0 14px;
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);

        em {
            margin-right: 6px;
        }
    }

    &_creator {
        @textEll();

    }

    &_creatTime {
        min-width: 140px;
        text-align: right;
    }

    &_rank {
        position: absolute;
        line-height: 24px;
        right: 10px;
        top: -4px;

        span {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -60%);
            color: #fff;
            font-size: 12px;
            line-height: 16px;
        }

        &_icon {
            color: #63727F45;
            width: 28px;
            height: 28px;
            display: block;
        }
    }
}
</style>
