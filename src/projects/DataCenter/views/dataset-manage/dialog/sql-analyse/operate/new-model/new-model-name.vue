<template>
    <div class="newModelName">
        <el-form :model="nameForm" v-loading="settings.loading" :rules="rules" size="small" ref="form">
            <el-form-item :label="label.chLabel" prop="chName" label-width="120px">
                <el-input v-model.trim="nameForm.chName" ref="name" v-input-limit:fieldName maxlength="100"
                          :placeholder="c_placeholder"></el-input>
            </el-form-item>
            <el-form-item :label="label.engLabel" prop="engName" label-width="120px" v-show="reType === 'model'">
                <el-input v-model.trim="nameForm.engName" v-input-limit:fieldCode @input="checkColumnCode"
                          maxlength="100" :placeholder="e_placeholder"></el-input>
            </el-form-item>
            <el-form-item label="数据源:" prop="dataObj" label-width="120px" v-show="isNew">
                <ce-select-drop
                        class="expect-select"
                        placeholder="请选择位置"
                        :props="defaultProps"
                        filterable
                        check-leaf
                        check-strictly
                        :tree-props="treeBind"
                        :filterNodeMethod="filterNode"
                        v-model="nameForm.dataObj"
                        @current-change="selectSchema"
                        :data="dataObjOpt"></ce-select-drop>
            </el-form-item>
            <el-form-item label="位置:" prop="savePath" label-width="120px" v-show="isNew">
                <ce-select-drop
                        class="expect-select"
                        placeholder="请选择位置"
                        ref="pos_tree"
                        :props="defaultProps"
                        filterable
                        :tree-props="treeBind"
                        :filterNodeMethod="filterNode"
                        v-model="nameForm.savePath"
                        :data="pathOpt"></ce-select-drop>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../../../../service-mixins/service-mixins";
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import * as source from "../../../../../dataSources/service-mixins/service-mixins"
    import {outputMixins} from "@/api/commonMethods/output";


    export default {
        name: "NewModelName",
        mixins: [commonMixins, servicesMixins, source.servicesMixins , outputMixins],
        props: {
            label: Object,
            treeNode: {
                type: Object,
                default: () => {
                    return {};
                }
            }
        },
        data() {
            return {
                nodeId: "",
                reType: "",
                isNew: true,
                pathOpt: [],
                dataObjOpt: [],
                dbType: ["GREENPLUM","ORACLE","POSTGRESQL"],
                nameForm:
                    {
                        chName: '',
                        engName: '',
                        savePath: '',
                        schemaId: '',
                        dataObj: '',
                    }
                ,
                e_placeholder: "请输入英文名(限100个字符)",
                c_placeholder: "请输入中文名(限100个字符)",
                rules: {
                    chName: [
                        {required: true, message: '请输入中文名', trigger: ['blur', 'change']},
                    ],
                    engName: [
                        {required: true, message: '请输入英文名', trigger: ['blur', 'change']}
                    ],
                    savePath: [
                        {required: true, message: '请选择位置', trigger: ['blur', 'change']},
                    ],
                    dataObj: [
                        {required: true, message: '请选择数据源', trigger: ['blur', 'change']},
                    ],
                },
                defaultProps: {
                    children: "children",
                    label: "label",
                    value: "id"
                },
                treeBind: {
                    "default-expand-all": false
                },
            }
        },
        methods: {
            addDisabled(val) {
                const vm = this;
                val.forEach(item => {
                    if (item.isParent) {
                        item.disabled = true;
                    }
                    if (item.children) {
                        vm.addDisabled(item.children);
                    }
                })

            },
            selectSchema(val, item) {
                this.nameForm.schemaId = val.schemaId;
            },
            checkColumnCode(e) {
                this.nameForm.engName = e.replace(/[\u4e00-\u9fa5]/ig, '');
            },
            filterNode: treeMethods.methods.filterNode,
            validate(...arg) {
                this.$refs.form.validate(...arg)
            },
            showSelect(type, nodeId) {
                this.reType = type;
                this.isNew = true;
                if (nodeId) this.nodeId = nodeId;
            },
            setSelect() {
                this.isNew = false;
            },
            getFormData() {
                return this.nameForm;
            },
            setName(row, type) {
                this.reType = type;
                this.isNew = false;
                this.nameForm.chName = row.name;
                this.nameForm.engName = row.code;

            },
            clearName() {
                this.nameForm.chName = "";
                this.nameForm.engName = "";
            },
            getDataSetAndSourceTree() {
                const vm = this, {datasetServices, datasetMock, settings, listApi, listMock} = this;
                vm.queryDataSetTree(vm,datasetServices, datasetMock, settings);
                vm.getDataSourceTree(vm,listApi, listMock, settings);
            },
            queryDataSetTree(vm, datasetServices, datasetMock, settings) {
                let services = vm.getServices(datasetServices, datasetMock);
                settings.loading = true;
                //数据集位置
                services.queryDataSetTree(false, settings).then(res => {
                    if (res.data.status === 0) {
                        vm.pathOpt = res.data.data[0].children.map(item => {
                            if (item.code === 'DATASET_DIR_MY') {
                                vm.nameForm.savePath = vm.nodeId || item.id;
                            }
                            return item;
                        });
                    }
                });
            },
            getDataSourceTree(vm, listApi, listMock, settings) {
                //数据源目录树
                let listServices = vm.getServices(listApi, listMock);
                let dataSpace = true;
                listServices.getDataSourceTree(settings, false, dataSpace).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.addDisabled(result);
                        vm.dataObjOpt = vm.resTreeDataDispose(result);
                    }
                })
            },
        },
        created() {
            this.getDataSetAndSourceTree();
        }
    }
</script>

<style scoped>

</style>
