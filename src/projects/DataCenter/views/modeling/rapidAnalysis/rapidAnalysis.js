import {servicesMixins} from "../service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi_3/index.vue"
import Tree from "./tree/index"
import List from "./list/index"
import NewModel from "../dialog/NewModel";
import ModelTabs from "../dialog/ModelTabs";
import RenameDailog from "../dialog/RenameDailog"
import OpenModel from '../dialog/OpenModel'
//import preview from "./dialog/preview"
import preview from "../dialog/preview"
import treeCard from "@/components/layout/tree-card"
import newTreeDir from "@/projects/DataCenter/views/modeling/dialog/newTreeDir"
import {TreeEdit} from "@/projects/DataCenter/views/modeling/tree/tree-edit";
//import saveModel from '@/projects/DataCenter/views/dataSpace/dialog/saveModel';
import saveModel from "@/projects/DataCenter/views/modeling/rapidAnalysis/dialog/saveModel";
import importAndExport from '@/projects/DataCenter/views/modeling/rapidAnalysis/dialog/importAndExport';

export default {
    name: "rapidAnalysis" ,
    mixins : [servicesMixins , commonMixins ,TreeEdit],
    components : {
        TreeAndList ,
        Tree ,
        List,
        NewModel,
        ModelTabs,
        RenameDailog,
        OpenModel,
        preview ,
        treeCard,
        newTreeDir,
        saveModel,
        importAndExport
    },
    data(){
        return {
            newModel: false, //新建模型
            modelTabs: false,
            treeData:[],
            cardName : "模型列表" ,
            showAddBox : false ,
            sortMenu : {
                time : {label : "按时间排序" , icon : "iconshijianpaixutubiao"} ,
                letter : {label : "按字母排序", icon : "iconsort"}
            },
            sortVal : "time",
            listLoad: false,
        }
    },
    methods : {
        /**
         * 更新列表加载状态
         * @param val
         */
        renewListLoad(val){
            this.listLoad = val;
        },
        /**
         * 显示移动弹窗
         * @param data
         * @param node
         */
        moveToDir(data,node){
            let level = data.children && data.children.length || node.level >= this.treeLimitLevel ? node.level -1 : this.treeLimitLevel-1;
            this.$refs.treeDir.show(data , 'move' , level );
        },
        /**
         * 排序树
         * @param val
         */
        setTreeSort(val){
            this.showAddBox = false;
            this.sortVal = val;
            this.$refs.dataTree.sortTreeData(val);
        },
        /**
         * 显示添加目录弹窗
         * @param data
         */
        showAddDialog(data){
            this.$refs.treeDir.show(data);
        },
        filterTree(val){
            this.$refs.dataTree.setFilterText(val);
        },
        serveIssue(row){
            this.$refs.issue.show(row)
        },
        shareFn(row){
            this.$refs.share.show(row);
        },
        previewResult(row){
            this.$refs.preview.show(row);
        },
        moveTo(row , type , operType , mType){
            this.$refs.move.open(row, type , operType , mType);
        },
        setTreeData(data) {
            this.treeData = data;
        },
        treeDir(value, id , node) {
            this.$refs.modelingCenterTable.setDirType(value, id , node);
        },
        initTable(data){
            this.$refs.modelingCenterTable.deleteInit(data);
        },
        openNewModel(dirId) {
            this.addModelTab(false,dirId);
            // this.newModel = true;
        },
        closeNewModel() {
            this.newModel = false;
        },
        openModelTabs() {
            this.modelTabs = true;
        },
        closeModelTabs() {
            this.modelTabs = false;
        },
        backTo() {
            //返回
            this.closeNewModel();
            this.closeModelTabs();
            this.$refs.modelingCenterTable.changePage(1);
        },
        addModelTab(isTemplate ,dirId) {
            const vm = this;
            this.openModelTabs();
            this.closeNewModel();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab({},!!isTemplate ,dirId);
            })
        },
        addModule(data){
            const vm = this;
            this.closeNewModel();
            this.openModelTabs();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab(data);
            })

        },
        update(){
            this.$refs.modelingCenterTable.changePage(1);
        },
        dialogRenameEmit(newName, index,transId) {
            // this.$refs.modelPage.reName(newName,transId);
            this.$refs.modelingCenterTable.tDataChangeWithName(newName, index);
        },
        rename(row, index ,type) {
            this.$refs.refRenameDialog.setName(row, index,type);
            this.$refs.refRenameDialog.show();
        },
        onTableItemEvent(row) {
            this.$refs.modelEditDailog.show(row);
        },
        modelEdit(rowData) {
            const vm = this;
            this.openModelTabs();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab(rowData , false);
            })
        },
        saveOther(row ,type){
            this.$refs.SaveModel.show(row,'',false, true ,false ,type)
        },
        setSave(value,id,type,row){
            this.$refs.modelingCenterTable.saveOtherSure(value,id,type,row);
        },
        importAndExport(type){
            this.$refs.importAndExport.show(type,this.$refs.dataTree.data)
        },
        impostAndExportSure(type,info){
            this.$refs.modelingCenterTable.impostAndExportSure(type,info);
        },
        closeImportAndExport(){
            this.$refs.importAndExport.close();
        },
        closeSave(){
            this.$refs.SaveModel.stop();
            this.$refs.modelingCenterTable.changePage(1);
        }
    },
    created(){
        let {rapidShowName} = this.$route.params;
        const vm = this;
        if (rapidShowName) {
            vm.$nextTick(()=>{
                this.$refs.modelingCenterTable.rapidAnalysis();
            })
        }
    }
}
