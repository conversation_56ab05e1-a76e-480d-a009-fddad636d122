<template>
    <div class="detailsRight">
        <div class="detailsRight_top">
            <p>荐• 同类其他模型</p>
            <div class="list-style" v-for="(item , index) in otherModel" :key="index">
                <div class="left-style">
                    <img :src="item.logo" v-if="isBase64.test(item.logo)">
                    <svg class="left-style_ic" aria-hidden="true" v-else>
                        <use class="list-card_color" :xlink:href="setIcon(item.type)"></use>
                    </svg>
                </div>
                <div class="left-right">
                    <div class="left-right_name overLineClamp1 poi" :title="item.name || '模型名称'" @click="lookDetails(item)">{{item.name || "模型名称"}}</div>
                    <div class="left-right_desc overLineClamp2" :title="item.introduction">{{item.introduction}}</div>
                </div>
            </div>
            <no-data v-if="otherModel.length === 0" :title="'暂无推荐'" top="80px"></no-data>

        </div>
        <div class="detailsRight_content">
            <p>已关注用户 <el-link :underline="false" @click="lookAll" v-if="userList.length">查看全部<i class="el-icon-arrow-right"></i></el-link></p>
            <div class="list-style" v-for="(item , index) in userList" :key="index">
                <div class="left-style bgTran">
                    <el-avatar icon="el-icon-user-solid"></el-avatar>
                </div>
                <div class="left-right">
                    <div class="left-right_name overLineClamp1" :title="item.username">{{item.username}}</div>
                    <div class="left-right_desc overLineClamp1">{{item.operatetime}}</div>
                </div>
            </div>
            <no-data v-if="userList.length === 0" :title="'暂无关注用户'" top="80px"></no-data>
        </div>
        <el-dialog title="已关注用户" :visible.sync="userFocusVisible" :top="'5vh'" custom-class="dialog_style">
            <common-table
                    :data="userDataInfo.dataList"
                    :columns="tableColumns"
                    :border="false"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    :pagination-total="userDataInfo.totalCount"
                    :max-height="tableBodyH"
                    @change-current="isMock ? ()=>{} : getList($event,pageSize) "
                    @change-size="changeSize"
            >
            </common-table>
        </el-dialog>
    </div>
</template>

<script>
    import {common} from "@/api/commonMethods/common";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import NoData from "@/components/no-data";
    import {coustTableH} from "@/api/commonMethods/count-table-h";
    export default {
        name: "detailsRight",
        props:{
            otherModel:Array,
            userList:Array,
            userDataInfo:Object,
            id:String,
        },
        mixins: [ coustTableH ,common ,commonMixins  ],
        components: {
            NoData,
        },
        data(){
            return{
                tableColumns:[
                    {
                        prop: "username",
                        label: "姓名",
                        align: "center"
                    },
                    {
                        prop: "operatetime",
                        label: "关注时间",
                        align: "center"
                    }
                ],
                pageSize:10,
                totalCount:0,
                userFocusVisible:false,
                isBase64:/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,
            }
        },
        methods:{
            setIcon(type ){
                let info = 'DASHBOARDS';
                switch (type) {
                    case "DASHBOARDS":
                        info = "#icon-dashboard";
                        break;
                    case "TRANS":
                        info = "#icon-model1";
                        break;
                    case "SCRIPT":
                        info = "#icon-AImodel";
                        break;
                    case "PORTAL":
                        info = "#icon-theme";
                        break;
                    default:
                        info = "#icon-dashboard";
                        break;
                }
                return info;
            },
            lookAll(){
                this.getList(1,this.pageSize);
                this.userFocusVisible = true;
            },
            //已关注用户列表
            getList(num,pageSize){
                this.paginationProps.currentPage = num;
                this.pageSize = pageSize;
                this.$emit('getUserList',num , pageSize ,'dialog')
            },
            lookDetails(item){
                let routeData = this.$router.resolve({path: '/market/modelMarket', query: {id: item.id}});
                window.open(routeData.href, '_blank');
            },
            changeSize(val){
                this.getList(1,val);
            },
        }
    }
</script>

<style scoped lang="less">
    .detailsRight{
        /deep/.dialog_style{
            .el-dialog__body{
                padding-top:0px;
            }
        }

        .overLineClamp2 {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .overLineClamp1 {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .list-card_color {
            fill: rgba(0, 136, 255, 0.8);
        }

        .list-style{
            display: flex;
            padding: 10px 0;
            justify-content: space-around;
            .bgTran{
                background: transparent;
            }
        }
        .left-style{
            text-align: center;
            width:44px;
            height:44px;
            margin-right:10px;
            align-items: center;
            display: flex;
            justify-content: center;
            background: rgba(0, 136, 255, 0.09);
            &_ic{
                width:32px;
                height:32px;
            }
            i{
                font-size: 44px;
                background: rgba(0, 136, 255, 0.09);
                border-radius: 2px;
                color: #0088FF;
            }
            img{
                width:44px;
                height:44px;
                border-radius: 2px;
            }
        }
        .left-right{
            flex:1;
            &_name{
                font-size: 14px;
                color: rgba(0,0,0,0.85);
            }
            &_desc{
                font-size: 14px;
                color: rgba(0,0,0,0.45);
                line-height: 21px;
            }
        }

        p{
            padding:0 0 10px 0;
            font-weight: bold;
            font-size: 16px;
            border-bottom: 1px solid rgba(0,0,0,0.07);
            justify-content: space-between;
            display: flex;
            span{
                color: rgba(0,0,0,0.65);
                font-size: 14px;
                cursor: pointer;
                font-weight:300;
            }
        }
        &_top{
            background: #fff;
            margin-bottom: 15px;
            padding:10px;
            min-height: 300px;
        }
        &_content{
            background: #fff;
            padding:10px;
            min-height: 300px;
        }
    }

</style>
