<template>
    <div style="width:100%;" v-loading="settings.loading">
        <span class="is-required ce-normal_item">配置打标信息: </span>
        <el-button style="width:100%;" type="primary" @click="chooseColumns">配置</el-button>
        <!-- <div class="attr_btn">
            <el-button
                    v-for="(btn , inx) in buttons"
                    :key="inx"
                    size="mini"
                    :type="btn.type"
                    @click="btn.clickFn"
            >{{btn.name}}
            </el-button>
        </div> -->
        <newDialog ref="newDialog" :sourceData="sourceData" :rowData="rowData" :dialogTitle="dialogTitle" @getRules="getRules" :previewLabelData="labelData"></newDialog>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import CommonTable from "@/components/common/CommonTable";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins"
    import newDialog from "./labelDialog"

    export default {
        name: "removeDuplicateData",
        props: {
            sourceData: Object,
            rowData: Object
        },
        mixins: [common, commonMixins, servicesMixins],
        components: {
            CommonTable,
            PreView,
            newDialog
        },
        data() {
            return {
                dialogTitle : "数据打标",
                columns:[],
                value:[],
                canSave: false,
                isSeniorShow: {},
                addTxt: '点击添加字段处理',
                labelData:[],
            }
        },
        methods: {
            chooseColumns(){
                const vm = this;
                vm.$nextTick(()=>{
                    this.$refs.newDialog.show();
                })
            },
            getRules(value){
                this.labelData = value;
            },
            save(saveName,showTip) { //保存后才能预览
                const vm = this, {pluginServices, pluginMock,} = this;
                if (vm.labelData.length <= 0) {
                    vm.$message.warning("请添加标签字段！");
                    return;
                }
                vm.labelData.forEach(item => {
                    if (item.fileName.trim() === "") {
                        vm.$message.warning("字段名不能为空！");
                        throw new Error("字段名不能为空");
                    }
                    if (item.fileType.trim() === "") {
                        vm.$message.warning("字段类型不能为空！");
                        throw new Error("字段类型不能为空");
                    }
                });
                let services = vm.getServices(pluginServices, pluginMock);
                services.saveLabelPluginPage(this.sourceData.id, {columnExps: vm.labelData}).then(res => {
                    if (res.data.status == 0) {
                        showTip && this.$message.success("保存成功！");
                        if(saveName) saveName();
                        vm.$emit("setPreviewAble");
                    } else {
                        this.$message.error("保存失败！");
                    }

                });
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            init(){
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                services.inintLabelPluginPage(this.sourceData.id, settings).then(res => {
                    if (res.data.status == 0) {
                        this.columns = res.data.data.column;
                        if (res.data.data.pluginMeta.columnExps !== undefined && res.data.data.pluginMeta.columnExps !== null) {
                            this.labelData = res.data.data.pluginMeta.columnExps;
                        }
                    }
                });
            }
        },
        created() {
            this.init();
            // this.getColumns();
        },
    }
</script>

<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped>
    .switch_width {
        width: 30px;
    }
    .ce-normal_item {
        padding-top: 10px;
        padding-left: 10px;
        text-align: left ;
    }
    .ce-normal_item.is-require::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }
    .select_class{
        padding-top: 10px;
        padding-left: 10px;
    }
    .ce-plug_attr1 {
        height: calc(100% - 270px) ;
        padding: 10px;
        box-sizing: border-box;
        overflow: auto;
    }
    .ce-add_b {
        line-height: 200px;
        border: 3px dashed #ddd;
        cursor: pointer;
        text-align: center;
        color: #999;
        font-size: 18px;
        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
    }
</style>
