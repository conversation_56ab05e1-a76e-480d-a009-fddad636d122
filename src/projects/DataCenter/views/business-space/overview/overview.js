//业务空间 预览
import ShareCard from "@/projects/DataCenter/views/home/<USER>/share/ShareCard";
import NoData from "@/components/no-data";
import CeCardItem from "@/components/common/ce-card-item/CeCardItem";

export default {
    name: "overview",
    components : {
        ShareCard,
        NoData,
        CeCardItem
    },
    data(){
        return {
            viewData :[
                {
                    name : "123",
                    iconTitle : "业务板块",
                    icon : "#icon-model1" ,
                    target : "用户" ,
                    time : "2021-11-05 10:12:14"
                }
            ]
        }
    },
    methods : {
        /**
         * 卡片点击
         */
        cardClick(row){
            this.$router.push({name:"businessSpaceDefinition" , params : {row}})
        }
    }
}
