<template>
    <div class="newModel">
        <div class="ce-search-outer">
            <el-button
                    size="mini"
                    type="primary"
                    @click="backTo"
            >返回
            </el-button>
            <el-button size="mini"
                       type="primary"
                       @click="newModel">
                新建模型
            </el-button>
            <el-button size="mini"
                       type="primary"
                       @click="addModules">
                创建模板
            </el-button>
            <el-upload
                    class="dib ml10"
                    :show-file-list="false"
                    :accept="'.model'"
                    :action="actionUrl"
                    :file-list="fileList"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
            >
                <el-button size="mini" type="primary">上传模板</el-button>
            </el-upload>
            <div class="r w250">
                <el-input
                        size="small"
                        placeholder="请输入模型名称搜索"
                        v-model.trim="searchVal"
                ></el-input>
            </div>
        </div>
        <ul class="ce-model-type">
            <li class="ce-model-item" v-for="(item , index ) in modelStore" :key="index" @click="modelSetting(item)">
                <div class="ce-touch" :title="item.name">
                    <div class="ce-model-box">
                        <div class="ce-menu">
                            <a :href="downloadUrl + item.name" :download="item.name + '.model'" target="_blank" class="icon" @click.stop>&#xe6f2;</a>
                            <em class="icon f14" @click.stop="removeConfirm(item , index)">&#xe614;</em>
                        </div>
                        <span class="ce-icon_font icon">{{item.icon}}</span>
                    </div>
                    <p class="ce-model-name">{{item.name}}</p>
                </div>
            </li>
            <li v-show="!modelStore.length" class="ce-data-none p30">暂无数据</li>
        </ul>
        <common-dialog
                :visible.sync="editM.visible"
                :title="editM.title"
                v-loading="loading"
        >
            <el-form v-loading="editM.loading" size="mini" label-width="180px" label-position="left">
                <el-form-item class="bbd" v-for="(li , key) in stepList"
                              :key="key">
                    <div slot="label" class="ell" :title="li.transName">{{li.transName}}</div>
                    <base-chart :ref="'base_'+key" :base-data="treeData" @selectChange="selectChange(arguments , li)" ></base-chart>
                    <!--<simple-search-tree
                            v-model="li.data"
                            :data="treeData"
                            :props="treeProps"
                            check-leaf
                            check-strictly
                            radio-type="all"
                            node-key="id"
                            :highlight-current="true"
                            :height="'160px'"
                            @node-click="setListData($event , li)"
                    ></simple-search-tree>-->
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button type="primary" size="mini" @click="openModel">确定</el-button>
                <el-button size="mini" @click="editM.visible = false">取消</el-button>
            </span>
        </common-dialog>
    </div>
</template>

<script>
    import {common} from "@/api/commonMethods/common"
    import CommonDialog from "@/components/common/dialog/CommonDialog"
    import SimpleSearchTree from "@/components/common/simpleTree/SimpleSearchTree"
    import * as modelingApi from "../request/modelingApi"
    import BaseChart from "@/components/common/baseChart/BaseChart"
    import * as treeApi from "../../../services/dataSource-services/treeApi"
    export default {
        name: "NewModel",
        mixins: [common],
        components: {
            CommonDialog,
            SimpleSearchTree,
            BaseChart
        },
        data() {
            const vm = this;
            let baseUrl = vm.$axios.defaults.baseURL;
            return {
                loading :false,
                searchVal: '',
                modelTemp: [],
                modelStore: [],
                actionUrl: baseUrl+"/template/upload",
                downloadUrl :baseUrl + "/template/download?modelName=" ,
                fileList: [],
                editM: {
                    visible: false,
                    name: '模板配置 >',
                    title: "",
                    modelName : "" ,
                    loading: false
                },
                stepList: [],
                treeData: [
                    {
                        id: '0',
                        label: '节点'
                    }, {
                        id: '1',
                        label: '节点2'
                    }
                ],
                treeProps: {
                    id: 'id',
                    label: 'label',
                    children: 'children',
                },
            }
        },
        watch: {
            searchVal(val) {
                let searchV = val.trim();
                this.createFilter(searchV);
            }
        },
        methods: {
            selectChange(args , item){
                item[args[1]] = args[0];
            },
            beforeUpload(file){
                const vm = this;
                let fileName = file.name;
                if(fileName.indexOf('.model') === -1){
                    vm.$message.warning("请选择.model模型文件");
                    return false;
                }
            },
            uploadSuccess(response, file, fileList){
                if(response.status === 0){
                    this.$message.success("上传成功");
                    this.modelInit();
                }else {
                    this.$message.error(response.data.msg)
                }
            },
            setListData(val, list) {
                list.data = val.id;
            },
            modelSetting(data) {
                const vm = this;
                let {editM} = vm;
                editM.visible = true;
                editM.modelName = data.name;
                editM.title = editM.name + data.name;
                editM.loading = true;
                vm.stepList = [];
                modelingApi.parse(data.name).then(res=>{
                    if(res.data.status === 0){
                        res.data.data.forEach(item =>{
                            vm.stepList.push({
                                tableId : "" ,
                                schemaId : "",
                                tableName : "",
                                transName : item
                            })
                        })
                    }
                    editM.loading = false;
                }).catch(err=>{editM.loading = false;})
            },
            removeModel(item, inx) {
                const vm = this;
                modelingApi.deleteM(item.name).then(res =>{
                    if(res.data.status === 0){
                        vm.modelTemp = vm.modelTemp.filter(m =>{
                            return m.name !== item.name;
                        });
                        vm.createFilter();
                        vm.$message.success("删除成功");
                    }else {
                        vm.$message.error(res.data.msg);
                    }
                })
            },
            removeConfirm(item, inx) {
                const vm = this;
                vm.confirm('删除', '是否删除模板', vm.removeModel, [item, inx]);
            },
            openModel() {
                const vm = this;
                let checked = false;
                let {stepList} = this;
                stepList.forEach((item , i) => {
                    if (item.schemaId === "" || item.tableId === "" || item.tableName === "") {
                        checked = true;
                    }
                    vm.$refs['base_'+i][0].validate(valid=>{});
                });
                if (!checked) {
                    vm.createTemplate(vm.editM.modelName , JSON.stringify(stepList));
                }
            },
            createTemplate(modelName , stepList){
                const vm = this;
                vm.loading = true;
                modelingApi.create( modelName , stepList).then(res=>{
                    if(res.data.status === 0){
                        let data = {
                            transId : res.data.data ,
                            isNew:true ,
                            modelName : vm.editM.modelName,
                            statusCode:0,
                        };
                        vm.$emit('addModule', data);
                        vm.editM.visible = false;
                    }
                    vm.loading = false;
                }).catch(err=>{
                    vm.loading = false;
                })
            },
            backTo() {
                this.$emit('backTo');
            },
            modelInit() {
                const vm = this;
                this.modelTemp = [];
                modelingApi.getList().then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        Object.keys(result).forEach(item => {
                            this.modelTemp.push({
                                name: item,
                                icon: item[0]
                            })
                        });
                        vm.createFilter();
                    }
                })
            },
            createFilter(val = "") {
                let model = this.modelTemp;
                this.modelStore = model.filter(mo => {
                    return mo.name.toLowerCase().indexOf(val.toLowerCase()) !== -1;
                });
                this.modelStore.sort(this.compare("name"));
            },
            compare(key){
                return function (a , b) {
                    let t1 = a[key] , t2 = b[key];
                    return t1.localeCompare(t2);
                }
            },
            newModel() {
                this.$emit('newModel');
            },
            addModules() {
                this.$emit('newModel', true);
            },
            initSourceTree() {
                const vm = this;
                treeApi.queryDirTree().then(res => {
                    if (res.data.status === 0) {
                        vm.treeData = res.data.data;
                    }
                });
            }

        },
        created() {
            this.modelInit();
            this.initSourceTree();
        }
    }
</script>

<style scoped>
    .newModel {
        position: fixed;
        left: 0;
        top: 4rem;
        width: 100%;
        height: calc(100% - 50px);
        overflow: hidden;
        background: #f5f5f5;
        z-index: 30;
    }

    .ce-search-outer {
        overflow: hidden;
        padding: 8px;
        border-bottom: 1px solid #ddd;
        background: #f8f8f8;
        width: 100%;
        box-sizing: border-box;
    }

    .ce-model-type {
        height: calc(100% - 50px);
        overflow: auto;
    }

    .ce-model-item {
        box-sizing: border-box;
        padding: 14px 0;
        text-align: center;
        float: left;
        font-size: 16px;
        line-height: 36px;
        color: #666;
        cursor: pointer;
    }

    .ce-touch {
        width: 150px;
        margin: auto;
        cursor: pointer;
    }

    .ce-model-box {
        height: 200px;
        border: 1px solid #ccc;
        background: #fff;
        line-height: 180px;
        box-sizing: border-box;
        position: relative;
    }

    .ce-model-box.active {
        border: 2px solid #289bf1;
    }

    .ce-model-icon {
        color: #153f76;
        font-size: 80px;
    }

    @media only screen and (max-width: 600px) {
        .ce-model-item {
            width: 50%;
        }
    }

    @media only screen and (min-width: 600px) and (max-width: 800px) {
        .ce-model-item {
            width: 33.3%;
        }
    }

    @media only screen and (min-width: 800px) and (max-width: 1360px) {
        .ce-model-item {
            width: 25%;
        }
    }

    @media only screen and (min-width: 1360px) and (max-width: 1600px) {
        .ce-model-item {
            width: 20%;
        }
    }

    @media only screen and (min-width: 1600px) and (max-width: 1920px) {
        .ce-model-item {
            width: 16.6%;
        }
    }

    @media screen and (min-width: 1920px) {
        .ce-model-item {
            width: 14.2%;
        }
    }

    .ce-model-name {
        padding: 0 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ce-touch:hover > .ce-model-box {
        border: 2px solid #289bf1;
    }

    .ce-touch:hover > .ce-model-box .icon {
        color: #289bf1;
    }

    .ce-icon_font {
        font-size: 50px;
        color: #999;
    }

    .ce-model-box:hover .ce-menu {
        display: block;
    }

    .ce-menu {
        line-height: 24px;
        position: absolute;
        right: 0;
        top: 0;
        color: #888;
        padding: 5px;
        display: none;
    }

    .ce-menu > .icon {
        padding: 0 5px;
    }
</style>