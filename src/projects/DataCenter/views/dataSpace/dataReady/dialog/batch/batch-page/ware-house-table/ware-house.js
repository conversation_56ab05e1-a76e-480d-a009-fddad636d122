import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import CommonTable from "@/components/common/dg-common-table/CommonTable"
import {roleData} from "../../../mixins/roleData";
import {treeMethods} from "@/api/treeNameCheck/treeName";
export default {
    name: "warehouse-table",
    inheritAttrs: false,
    mixins : [commonMixins , servicesMixins, roleData]  ,
    components : {
        CommonTable
    },
    props: {
        columns: Array,
        dwId : String,
        treeData:Array,
        tabActive:String
    },
    data() {
        return {
            selDataSetId:'',
            dataSourceList:this.treeData,
            defaultPropsDialog: {
                value: 'id',
                label: 'name',
                children: 'children',
                title: 'name',
            },
            treeBindDialog : {
                "default-expand-all" : true ,
            },
            defaultExpandedKeys:[],
            roleValue: "role",
            //所有条件
            list: [
                {
                    label: "角色",
                    value: "role"
                }, {
                    label: "用户",
                    value: "user"
                }
            ],
            s_value : "accept",
            //权限配置弹窗双向绑定值
            userVal: "",
            //权限配置弹窗树数据
            userData: [],
            searchVal: "" ,
            defaultProps: {
                value: 'id',
                label: 'objName',
                children: 'children'
            },
            //表格默认属性
            defaultAttrs: {
                border: true,
                pagination: false
            },
            tableData :[] ,
            treeBind : {
                "default-expand-all" : true ,
                "check-on-click-node" : true ,
                "expand-on-click-node" : false
            },
            dataSetVo : [] ,
            cancel_tabledata : [],
            data: []
        };
    },
    methods : {
        filterNode : treeMethods.methods.filterNode ,
        searchList(  text , data){
            const vm = this;
            let list = [];
            if(data){
                vm.tableData = list = data.map(li => {
                    li.auth = li.auth === true;
                    li.system = "数据中心";
                    return li;
                });
            }
            else{
                vm.tableData = [];
            }

            if(text !== ""){
                vm.tableData = list.filter(item =>{
                    return  item.name && item.name.toLowerCase().indexOf(text.toLowerCase()) > -1 ;
                })
            }

        },
        searchData(val){
            this.searchList( val , this.data);
        },
        checkEdit(dwId){
            this.setFun(dwId);
        },
        setFun(dwId){
            const vm = this , { dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices( dataReadyServices, dataReadyMock );
            if(vm.userVal.length === 0){
                vm.$message.warning("请选择被分享的对象！");
               return ;
            }
            if(vm.dataSetVo.length === 0){
                vm.$message.warning("请选择被分享的资源！");
                return ;
            }
            let roleVos = [] , userVos = [];
            let user_role_val = [];
            user_role_val = vm.getSelectVal(vm.userVal);
            if(vm.roleValue === 'user'){
                user_role_val.forEach(user => {
                    userVos.push({id : user})
                });
            }else {
                user_role_val.forEach(user => {
                    roleVos.push({roleId : user})
                });
            }
            let dataSetAuthVo = {
                dataObjectVos : vm.dataSetVo ,
                roleVos : roleVos ,
                userVos : userVos,
                dwId:"",
            };
            vm.$emit("loading");
            services.dataSetDCThreeAuthRegister(dataSetAuthVo).then(res => {
                if(res.data.status === 0){
                    services.dataDCThreeSetsAuth(dataSetAuthVo).then(res => {
                        if(res.data.status === 0){
                            vm.$message.success("分享成功");
                            vm.$emit("close");
                        }
                    })
                }
            })


        },
        cancelFun(){
            const vm = this , { dataReadyServices, dataReadyMock } = this;
            let services = vm.getServices( dataReadyServices, dataReadyMock );
            if(vm.userVal.length === 0){
                vm.$message.warning("授权人不能为空");
                return ;
            }
            if(vm.dataSetVo.length === 0){
                vm.$message.warning("请选择数据对象");
                return ;
            }
            let roleVos = [] , userVos = [];
            let user_role_val = [];
            user_role_val = vm.getSelectVal(vm.userVal);
            if(vm.roleValue === 'user'){
                user_role_val.forEach(user => {
                    userVos.push({id : user})
                });
            }else {
                user_role_val.forEach(user => {
                    roleVos.push({roleId : user})
                });
            }
            let dataSetAuthVo = {
                dataObjectVos : vm.dataSetVo ,
                roleVos : roleVos ,
                userVos : userVos
            };
            vm.$emit("loading");
            services.cancelDCThreeDataSetsAuth(dataSetAuthVo).then(res => {
                if(res.data.status === 0){
                    vm.$message.success("取消授权成功");
                    vm.$emit("close");
                }
            })
        },
        async getListData(val){
            const vm = this , { dataReadyServices, dataReadyMock } = this;
            let services = vm.getServices( dataReadyServices, dataReadyMock );
            vm.tableData = [];
            vm.cancel_tabledata = [];
            if(val.length === 0){
                return;
            }
            let user_role_val = [];
            user_role_val = await vm.getSelectVal(vm.userVal );
            services.queryDataSetTableAllByUserOrRole(vm.dwId ,user_role_val ).then(res => {
                if(res.data.status === 0){
                    vm.cancel_tabledata = res.data.data;
                    vm.searchData(vm.searchVal);
                }
            });
        },
        selectionChange(val){
            const vm = this;
            vm.dataSetVo = [];
            val.forEach(set => {
                vm.dataSetVo.push({
                    name : set.name ,
                    code : set.id
                })
            })
        },
        changeCatalog(n){
            const vm = this , { dataReadyServices, dataReadyMock,settings } = this;
            let services = vm.getServices( dataReadyServices, dataReadyMock)
            settings.loading = true;
            if(vm.tabActive === 'myData'){
                services.getDataSetForJurisdiction(n.id , settings).then(res =>{
                    if(res.data.status === 0){
                        vm.data = res.data.data.dataList;
                    }
                })
            }
            else{
                let params = {
                    dwDbId: n.id,
                    name: "",
                    pageIndex: 1,
                    dbType: "",
                    pageSize: -1,
                    sortField: "name",
                    sortOrder: "desc",
                };
                settings.loading = true;
                vm.tableData = [];
                services.queryDatasetBaseTableC(params,settings).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.data = result.dataList;
                    }
                })
            }
        }
    } ,
    watch : {
        data : {
            handler(val){
                this.searchList( "" , val);
            },
            deep : true
        }
    },
    computed: {
        attrs() {
            let { $attrs } = this;
            return Object.assign({}, this.defaultAttrs, $attrs);
        },
    },
    created(){
        this.getAuthorizer(this.roleValue);
        this.searchList(  "" , this.data);
        if(this.dwId){
            this.selDataSetId = this.dwId;
            this.changeCatalog({ id : this.dwId });
            this.$nextTick(()=>{
                this.$refs.select_drop ? this.$refs.select_drop.setCurrentKey(this.dwId) : "";
            })
        }
    }
};
