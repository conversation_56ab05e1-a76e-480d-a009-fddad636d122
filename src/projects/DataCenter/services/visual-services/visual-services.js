/**
 * 可视化 接口
 * @Author: wangjt
 * @Date: 2020-07-14
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/dashboardGroup/" , root1 = "/dashboard/" , rootW = "/widget/";

/*
*  查询 可视化 树
 * @param
 * @returns {Promise<*>}
* */

export async function queryTree( settings ) {
    return await reqUtil.get({
        url : root + "list" ,
        settings : settings
    })
}
/*
*  新增子目录
 * @param
 * @returns {Promise<*>}
* */

export async function addOrUpdate( name , id , parentId) {
    return await reqUtil.post({
        url : root + "addOrUpdate" ,
    }, {name , id , parentId})
}
/*
*  删除子目录
 * @param
 * @returns {Promise<*>}
* */

export async function deleteById( id ) {
    return await reqUtil.post({
        url : root + "deleteById?id="+id ,
    })
}
/*
*  查询可视化列表 带分页
 * @param
 * @returns {Promise<*>}
* */

export async function getListByGroupId( params , settings ) {
    return await reqUtil.post({
        url : root1 + "getListByGroupId" ,
        settings : settings
    } ,params)
}
/*
*  查询可视化列表 没有分页
 * @param
 * @returns {Promise<*>}
* */

export async function getDashboardAllByGroupId( groupId , settings ) {
    return await reqUtil.post({
        url : root1 + "getDashboardAllByGroupId" ,
        settings : settings
    },{groupId})
}


/*
*  复制 另存为
 * @param
 * groupId是目录id
 * {"id":"ae2fab70d129457e9ac60015bf95d7e4","groupId":"a038153c05b642ba9e6af7576926a464","dashboardName":"复制12"}
 * @returns {Promise<*>}
* */

export async function copy( params , settings ) {
    return await reqUtil.post({
        url : root1 + "copy" ,
        settings
    } ,params)
}
/*
*  删除
 * @param
 * @returns {Promise<*>}
* */

export async function deleteFn( id  ) {
    return await reqUtil.post({
        url : root1 + "delete" ,
    } ,{id})
}
/*
*  获取数据集
 * @param
 *  ignoreType (过滤多个库的话用英文分号隔开)
 * @returns {Promise<*>}
* */

export async function datasetList(ignoreType, settings) {
    return await reqUtil.get({
        url : "/dataSet/list" ,
        settings : settings
    },{ignoreType})
}

/*
*  获取数据集(新的，重新封装两个tab页的数据集)
 * @param
 * @returns {Promise<*>}
* */

export async function datasetListWithTwoTab(settings) {
    return await reqUtil.get({
        url : "/dataSet/datasetListWithTwoTab" ,
        settings : settings
    })
}
/*
*  保存仪表盘
 * @param
 * @returns {Promise<*>}
* */

export async function saveOrUpdate(data , settings) {
    return await reqUtil.post({
        url :root1 + "saveOrUpdate" ,
        settings : settings
    },{data})
}
/*
*  移动仪表盘
 * @param
 * @returns {Promise<*>}
* */

export async function updateGroup(id , groupId , settings) {
    return await reqUtil.post({
        url :root1 + "updateGroup" ,
        settings : settings
    },{id , groupId})
}
/*
*  仪表盘初始化
 * @param
 * @returns {Promise<*>}
* */

export async function getDashboard(id ,settings) {
    return await reqUtil.get({
        url :root1 + "getDashboard" ,
        settings : settings
    },{id})
}

/**
 * 分享 预览
 * @param id
 * @param userId
 * @param state
 * @param settings
 * @return {Promise<*>}
 */
export async function getPreviewDataByUserId(id ,userId ,state, settings) {
    return await reqUtil.get({
        url :root1 + "getPreVieWDataByUserId" ,
        settings : settings
    },{id,userId,state})
}

/**
 * 分享 通过用户
 * @param id
 * @param userCode
 * @param passWord
 * @param settings
 * @return {Promise<*>}
 */
export async function getPreVieWDataByUserCodeAndPassWord(id ,userCode ,passWord, settings) {
    return await reqUtil.get({
        url :root1 + "getPreVieWDataByUserCodeAndPassWord" ,
        settings : settings
    },{id,userCode,passWord})
}

/*
*  联动初始化数据
 * @param
 * @returns {Promise<*>}
* */

export async function getLinkageDashboard(id ,settings) {
    return await reqUtil.get({
        url :root1 + "getLinkageDashboard" ,
        settings : settings
    },{id})
}
/*
*  查看sql
 * @param
 * @returns {Promise<*>}
* */

export async function getSql(params) {
    return await reqUtil.post({
        url :rootW + "getSQL"
    },params)
}
/*
*  初始化id
 * @param
 * @returns {Promise<*>}
* */

export async function initWidget() {
    return await reqUtil.get({
        url :root1 + "initWidget"
    })
}
/*
*  初始化可视化插件
 * @param
 * @returns {Promise<*>}
* */

export async function getNoChart() {
    return await reqUtil.get({
        url : "/widgetMeta/getNoChart"
    })
}
export async function getChartGroup() {
    return await reqUtil.get({
        url : "/widgetCategories/getChartGroup"
    })
}

/*
* 获取数据字段
* @params
* @returns {Promise<*>}
* */

export async function getDatasetFields(dataSetId , settings) {
    return await reqUtil.get({
        url : "/dataSet/getDataSetFields",
        settings
    },{dataSetId})
}

/*
* 加载图表
* @params
* @returns {Promise<*>}
* */

export async function loadChart(data , settings) {
    return await reqUtil.post({
        url :rootW +  "loading",
        settings
    },data)
}
/*
* 请求快速计算
* @params
* @returns {Promise<*>}
* */

export async function requestFastCount() {
    return await reqUtil.post({
        url :rootW + "getFunctionList",
    })
}
const root_s = "/standermb/";

/**
 * 获取 码表list
 * @returns {Promise<*>}
 */
export async function allMbEnumTreeList() {
    return await reqUtil.get({
        url : root_s + "allMbEnumTreeList"
    })
}

/**
 * 获取 码表对应选项
 * @param id
 * @returns {Promise<*>}
 */
export async function getStanderMbCodeTreeList(id) {
    return await reqUtil.get({
        url : root_s + "getStanderMbCodeTreeList"
    },{id})
}

/**
 * 分局派出所警务区 联动查询
 * @returns {Promise<*>}
 * @param id
 * @param level
 */
export async function linkagePoliceStation(level , id="" ) {
    return await reqUtil.post({
        url : root_s + "linkagePoliceStation"
    } , {id , level})
}

/**
 * 校验数据集
 * @returns {Promise<*>}
 */
export async function isHaveDataSet() {
    return await reqUtil.get({
        url : "/dataSet/isHaveDataSet"
    })
}

/**
 * 时间颗粒度 获取
 * @return {Promise<*>}
 */
export async function getDateGranularity(){
    return await reqUtil.get({
        url :rootW + "getDateGranularity"
    })
}

/**
 * 业务关系图下拉框 获取
 * @return {Promise<*>}
 */
export async function getRelationTypes(settings){
    return await reqUtil.get({
        url :"widget/getRelationTypes",
        settings
    })
}

/**
 * 主题门户-仪表盘-获取开关
 * @param {*} settings
 */
export async function getOpenOn(settings){
    return await reqUtil.get({
        url :"dashboard/getOpenOn",
        settings
    })
}


/**
 * 首页仪表盘跳转
 * @param {*} id
 * @param {*} settings
 */
export async function getPreVieWDataByUserId(id, settings) {
    return await reqUtil.get({
        url : "/dashboard/getPreVieWDataByUserId?id=" + id +"&state=false",
        settings
    })
}

/**
 * 获取表格导出数量
 */
export async function getDownloadSize() {
    return await reqUtil.get({
        url :rootW + "getDownloadSize",
    })
}

/**
 * 表格导出
 */
export async function downloadExcel (param, settings) {
    return await reqUtil.post({
        url :rootW + "downloadExcel ",
        settings
    },param)
}
