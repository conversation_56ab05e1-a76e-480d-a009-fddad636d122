<!--业务实体-->
<template>
    <div class="businessEntity entity">
        <div class="entity_left">
            <div class="entity_left-btns">
                <el-button v-for="(btn , i) in buttons"
                           :key="i"
                           @click="btn.clickFn"
                           type="text"
                >
                    <em :class="btn.icon"></em>
                    <span>{{ btn.label }}</span>
                </el-button>
            </div>
            <div class="entity_left-tree">
                <ce-list-tree
                        ref="tree"
                        :search-style="{
                            'padding' : '18px 20px 18px 0'
                        }"
                        :treeProps="treeProps"
                        :treeMenus="treeMenus"
                        @nodeLink="nodeLink"
                >
                </ce-list-tree>
            </div>
        </div>
        <div class="entity_right">
            <ce-list-table
                    ref="table"
                    :filter-text.sync="filterText"
                    :t-head-data="tHeadData"
                    :table-data="tableData"
                    @changePage="changePage"
                    :operate-icons="operateIcons"
                    :filter-placeholder="filterPlaceholder"
            >
                <template slot="button">
                    <el-button type="primary" @click="addData">{{ dataInput }}</el-button>
                </template>
            </ce-list-table>
        </div>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "businessEntity",
    mixins: [commonMixins],
    data() {
        return {
            filterPlaceholder: "请输入特征搜索",
            dataInput: "新建特征",
            filterText: "",
            tHeadData: [
                {
                    prop: "name",
                    label: "业务实体",
                    minWidth: '140px'
                }, {
                    prop: "feature",
                    label: "特征",
                    minWidth: '140px'
                }, {
                    prop: "businessEle",
                    label: "关联业务元",
                    minWidth: '120px'
                }, {
                    prop: "datatype",
                    label: "数据类型",
                    minWidth: '100px'
                }, {
                    prop: "dataLen",
                    label: "数据长度",
                    minWidth: '100px'
                }, {
                    prop: "meteringType",
                    label: "计量类型",
                    minWidth: '100px'
                }, {
                    prop: "businessType",
                    label: "业务类型",
                    minWidth: '100px'
                }, {
                    prop: "coder",
                    label: "关联码表",
                    minWidth: '100px'
                }, {
                    prop: "staticType",
                    label: "统计类型",
                    minWidth: '100px'
                }, {
                    prop: "operate",
                    label: "操作",
                    width: '120px',
                    fixed: 'right',
                    align: "center"
                }
            ],
            tableData: [],
            operateIcons: [
                {
                    name: "编辑",
                    clickFn: this.edit,
                    show: () => true,
                    condition: (right) => true
                }, {
                    name: "删除",
                    clickFn: this.deleteF,
                    show: () => true,
                    condition: (right) => true
                },
            ],
            treeMenus: [
                {
                    name: '编辑实体',
                    icon: 'el-icon-edit-outline',
                    fn: this.editDir,
                    show: () => true
                },
                //分割线
                {
                    name: "divider",
                    show: () => true
                },
                {
                    name: '删除',
                    icon: 'el-icon-delete',
                    fnName: 'delete',
                    show: () => true
                }
            ],
            buttons: [
                {
                    label: "新建",
                    clickFn: this.addFile,
                    icon: "el-icon-folder-add",
                }, {
                    label: "批量导入",
                    clickFn: this.inputFile,
                    icon: "icon icon-download",
                }, {
                    label: "导出",
                    clickFn: this.output,
                    icon: "icon icon-export",
                },
            ],
            treeData: [],
            services: this.$services("modeling"),
            treeProps: {
                label: "name",
                children: "children"
            }
        }
    },
    methods: {
        addFile() {
            const vm=this;
            let layer = vm.$dgLayer({
                title : "定义实体" ,
                content : require("./addEntity/addEntity.vue"),
                maxmin:false,
                move:false,
                area : ["604px" , "292px"],
                on : {
                    submit(){}
                }
            })
        },
        /**
         * 编辑实体
         */
        editDir(row){
            const vm=this;
            let layer = vm.$dgLayer({
                title : "编辑实体" ,
                content : require("./addEntity/addEntity.vue"),
                maxmin:false,
                move:false,
                area : ["604px" , "292px"],
                props:{
                    //回填数据 data
                },
                on : {
                    submit(){}
                }
            })
        },
        inputFile() {
        },
        output() {
        },
        changePage() {

        },
        addData() {

        },

        edit(row, inx) {
        },
        deleteF(row, inx) {
        },
        getTreeMethod(...arg) {
            const vm = this, {services} = vm;
            return services.queryTransTree(...arg);
        },
        addChildDir(...arg) {
            const vm = this, {services} = vm;
            return services.createTransClassify(...arg);
        },
        moveDir(...arg){

        },
        /**
         * 树接口配置
         * @param name
         * @return {function(...[*]): *}
         */
        setTreeInterface(name){
            const vm = this, {services} = vm;
            return function (...arg){
                return services[name](...arg);
            }
        },
        /**
         * 树点击触发事件
         * @param name
         * @param id
         * @param node
         */
        nodeLink(name , id ,node){

        }
    },
    created() {

    }
}
</script>

<style scoped lang="less">
.entity {
    height: 100%;
    width: 100%;
    display: flex;

    &_left {
        height: 100%;
        border-right: 1px solid rgba(0, 0, 0, 0.15);
        min-width: 307px;

        &-btns {
            height: 2rem;

            .el-button--text {
                color: rgba(0, 0, 0, 0.85);
                margin-right: 16px;
            }

            em {
                color: rgba(0, 0, 0, 0.45);
                margin-right: 10px;
            }

            span {
                margin: 0;
            }
        }

        &-tree {
            height: calc(100% - 2rem);
        }
    }

    &_right {
        flex: 1;
        height: 100%;
        max-width: calc(100% - 308px);
        padding-left: 24px;
        box-sizing: border-box;
    }

}
</style>
