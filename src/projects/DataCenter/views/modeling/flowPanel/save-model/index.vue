<template>
    <common-dialog custom-class="save" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="clearD"
                   v-loading="settings.loading"
    >
        <el-form :model="form" label-width="100px" label-position="right" label-suffix=" :">
            <el-form-item :label="formList.name.label" prop="transName"
                          :rules="[{ required: true, message: '请输入模型名称', trigger: ['blur','change'] }]">
                <div class="content__name">
                    <el-input v-model.trim="form.transName" maxlength="50" v-input-limit:trim
                              @input="inputFilterSpecial($event , 'form' ,'transName')"
                              :placeholder="formList.name.placeholder"></el-input>
                    <p>{{ formList.name.tip }}</p>
                </div>
            </el-form-item>
            <el-form-item :label="formList.pos.label" required>
                <div class="content__wrap">
                    <!--目录树-->
                    <DataTreeVue class="content__tree" ref="tree" v-if="openDialog" @nodeClick="treeDir"
                                 :dirId="classifyId" hasSave/>
                </div>
            </el-form-item>
            <el-form-item :label="formList.description.label">
                <el-input v-model="form.description" type="textarea"
                          :placeholder="formList.description.placeholder"
                          maxlength="255"
                          rows="3"
                          resize="none"
                          show-word-limit></el-input>
            </el-form-item>

            <el-form-item v-for="(item, k) in customList" :key="k"
                          :prop="k"
                          :label="item.label"
                          v-if="item.show()">
                <el-input v-if="item.type === 'input'"
                          v-model="form[k]"
                          v-input-limit:[item.limit]
                          :placeholder="item.placeholder"
                          :maxlength="item.maxlength"
                          show-word-limit></el-input>
                <el-input v-if="item.type === 'numberInput'"
                          v-model.number="form[k]"
                          v-input-limit:[item.limit]
                          :placeholder="item.placeholder"
                          :maxlength="item.maxlength"
                          @input="item.onInput(k)"
                          ></el-input>
            </el-form-item>

        </el-form>
        <div slot="footer">
            <el-button v-show="active" @click="stop" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button @click="save" type="primary" size="mini">{{ btnCheckTxt }}</el-button>
        </div>
    </common-dialog>
</template>

<script>
import {globalBus} from "@/api/globalBus";
import DataTreeVue from "@/projects/DataCenter/views/modeling/dialog/ModelTree";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";

export default {
    name: "saveModel",
    mixins: [commonMixins, servicesMixins, listMixins],
    components: {DataTreeVue},
    data() {
        const { $globalConfig } = this;
        const version = $globalConfig.showVersionAndFirm ? "": undefined ,
            productionFirm = $globalConfig.showVersionAndFirm ? "": undefined;
        return {
            visible: false,
            width: '800px',
            active: true,
            title: "",
            classifyId: "",
            form: {
                instance: "",
                mode: "",
                transName: "",
                processMode: "",
                description: "",
                version ,
                productionFirm,
            },
            formList: {
                name: {
                    label: '模型名称',
                    placeholder: '请输入模型名称',
                    tip: "名称最大不超过50字符，只允许除/、\\、<、>等特殊字符以外。"
                },
                pos: {
                    label: "保存到目录"
                },
                description: {
                    label: "模型描述",
                    placeholder: "请输入对模型进行描述的一段说明"
                },

            },
            customList: {
                version: {
                    label: '版本',
                    placeholder: "请输入版本",
                    maxlength: 4,
                    type: "numberInput",
                    limit: "number",
                    show: ()=> $globalConfig.showVersionAndFirm,
                    onInput: (k) => {
                        // 限制 1000
                        if(this.form[k] && this.form[k] > 1000 ) {
                            this.form[k] = 1000;
                            this.$message.warning("版本最大限制为 1000");
                        }
                    }
                },
                productionFirm: {
                    label: "开发商",
                    placeholder: "请输入开发商",
                    maxlength: 30,
                    type: "input",
                    limit: "trim",
                    show: ()=> $globalConfig.showVersionAndFirm,
                }
            },
            /*formList: [
                {
                    label: '运行实例 :',
                    placeholder: '请选择运行实例'
                }, {
                    label: '运行模式 :',
                    placeholder: '请选择运行模式'
                }, {
                    label: '名称 :',
                    placeholder: '请输入模型名称(限30个字符)'
                }, {
                    label: '处理模式 :',
                    placeholder: '请选择处理模式'
                },
            ],*/
            optProcessModeData: [
                {
                    value: "0",
                    label: "单机"
                },
                {
                    value: "1",
                    label: "分布式"
                },
                {
                    value: "3",
                    label: "Spark"
                },
            ],
            optInstanceData: [
                {
                    value: "运行实例",
                    label: "运行实例"
                }
            ],
            optSolveModeData: [
                {
                    value: "",
                    label: ""
                }
            ],
            rowData: {},
            isModel: false,
            openDialog: false,
            saveOther: false
        }
    },
    methods: {
        initSetting(saveOther) {
            if(this.$globalConfig.showVersionAndFirm) {
                this.form.version = saveOther ? this.rowData.version : "";
                this.form.productionFirm = saveOther ? this.rowData.productionfirm : "";
            }
        },
        treeDir(label, value) {
            this.classifyId = value;
        },
        /**
         *  @param rowData
         * @param saveInstance
         * @param saveOther
         * @param title
         * @param isModel
         * */
        show(rowData, saveInstance ,  title="保存模型" , saveOther=false , isModel=false,) {
            this.form.instance = saveInstance;
            this.rowData = rowData;
            this.initSetting(saveOther);
            this.form.transName = rowData.modelName;
            this.initSelectValue();
            this.visible = true;
            this.openDialog = true;
            this.isModel = isModel;
            this.title = isModel ? "保存为模板" : title ? title : "模型另存为";
            if (saveOther) {
                this.saveOther = saveOther;
            }
            if (rowData.dirId && rowData.dirId !== "-1") {
                this.classifyId = rowData.dirId;
            } else if (rowData.dirParentId) {
                this.classifyId = rowData.dirParentId;
            }
        },
        /**
         * 保存接口
         */
        async saveFn(services, settings) {
            const vm = this;
            let data = {
                transName: this.form.transName,
                runMode: this.form.mode,
                transId: this.rowData.transId,
                instanceCode: this.form.instance,
                handleMode: this.form.processMode,
                classifyId: this.classifyId,
                dirType: "TRANS_DIR_MF",
                memo: this.form.description,
                version: this.form.version,
                productionFirm: this.form.productionFirm,
            };
            await services.updateDataMiningTrans(data, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data !== "该名称已存在！") {
                        // vm.activeJob();
                        globalBus.$emit('changeName', vm.form.transName, vm.rowData.transId);
                        vm.visible = false;
                        if (vm.isModel) {
                            services.save(vm.rowData.transId).then(res => {
                                if (res.data.status === 0) {
                                    globalBus.$emit("modelSaveSuccess", vm.rowData.transId);
                                }
                            });//保存模板
                        }
                        vm.$emit("modelSaveSuccess", vm.rowData.transId);
                        vm.$message.success("保存成功！");
                    } else {
                        vm.$message.warning(res.data.data);
                    }
                }
            })
        },
        /**
         * 另存为接口
         * @param transId
         * @param classifyId
         * @param transName
         * @param memo
         */
        saveOtherFn(services, settings) {
            const vm = this, {rowData, classifyId, form} = vm, {transId} = rowData,
                {transName, description, version, productionFirm} = form;
            const params = {
                transId,
                classifyId,
                transName,
                memo: description,
                version,
                productionFirm: productionFirm,
            };
            services.modelSaveOther(params, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data !== "该名称已存在！") {
                        // globalBus.$emit('changeName', vm.form.transName, vm.rowData.transId);
                        vm.visible = false;
                        const {id , name} = res.data.data;
                        vm.$message.success("保存成功！");
                        let model = {
                            dirParentId: "",
                            modelName: name,
                            modelSpec: "",
                            modelType: "",
                            releaseDate: "",
                            status: "",
                            transId: id,
                            isNew: false
                        };
                        vm.$emit("openTab" , model);
                        vm.$emit("saveSuccess");
                    }
                }
            })
        },
        save() {
            const vm = this, {modelingServices, modelingMock, settings, saveOther} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            if (vm.classifyId === "") {
                vm.$message.warning("请选择方案所在目录！")
            } else if (vm.form.transName === "") {
                vm.$message.warning("请输入模型名称！")
            } else if (vm.form.instance === "") {
                vm.$message.warning("请选择方案运行实例！")
            } else {
                // String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId
                settings.loading = true;
                if (saveOther) {
                    vm.saveOtherFn(services, settings);
                } else {
                    vm.saveFn(services, settings);
                }
            }
        },//transSchedule
        activeJob() {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            services.activeJob(vm.rowData.transId).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("激活成功！");
                }
            })
        },
        clearD() {
            this.openDialog = false;
        },
        stop() {
            this.visible = false;
        },
        initSelectValue() { // 没有值的时候初始化第一条数据， 有值再从接口读取值
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            services.baseSettingPage(vm.rowData.transId).then(res => {
                if (res.data.status === 0) {
                    //处理模式
                    vm.form.processMode = res.data.data.mode;
                    vm.optSolveModeData = [];
                    res.data.data.handleModes.forEach((item, i) => {
                        if (i < 2) {
                            let model = {
                                value: item.code,
                                label: item.name,
                            };
                            vm.optSolveModeData.push(model);
                        }
                    });
                    //运行模式
                    vm.form.mode = res.data.data.distributed;
                    //运行实例
                    vm.optInstanceData = [];
                    res.data.data.instanceList.forEach(item => {
                        let instance = {
                            value: item,
                            label: item,
                        };
                        vm.optInstanceData.push(instance);
                    });
                }

                vm.form.instance = vm.optInstanceData[0].value;
            })
        },
    },
}
</script>

<style scoped lang="less">
.content {
    &__name {
        p {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 21px;
            margin-top: 4px;
        }
    }

    &__tree.dataTree {
        float: none;
        margin: 0;
        border: none;
        height: 100%;
        width: 100%;
        padding: 0;
    }

    &__wrap {
        padding: 14px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        box-sizing: border-box;
        height: 250px;
    }
}
</style>
