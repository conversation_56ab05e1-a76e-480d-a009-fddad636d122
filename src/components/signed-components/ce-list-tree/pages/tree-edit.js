export const TreeEdit = {
    data() {
        return {}
    },
    methods: {
        /**
         * 树点击事件
         * @param data
         * @param node
         */
        nodeClick(data, node) {
            const vm = this, {treeProps} = vm;
            let name = data[treeProps.label], id = data.id;
            vm.$refs.treeDir.nodeClick(name, id, node, treeProps);
        },
        /**
         * 弹窗 树过滤
         * @param val
         */
        filterEditTree(val) {
            this.$refs.editTree.filter(val);
        },
        /**
         * 新建子目录
         * @param name
         * @param parent
         */
        addTreeDir(name, parent) {
            const vm = this, {addChildDirInterface, settings} = vm;
            settings.loading = true;
            addChildDirInterface(parent.id, name, "TRANS_DIR_MF", settings).then(res => {
                if (res.data.status === 0) {
                    vm.successAddTip(name);
                    let nodeId = res.data.data;
                    vm.addTreeNode(name, parent, nodeId);
                    vm.$refs.treeDir.stop();
                }
            })
        },
        /**
         * 移动目录
         * @param curNode
         * @param parentNode
         */
        moveTreeDir(curNode, parentNode) {
            const vm = this, { moveDirInterface , settings} = vm;
            settings.loading = true;
            moveDirInterface(curNode.id, parentNode.id, 'TRANS_DIR_MF', settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("移动成功!");
                    vm.moveTreeNode(curNode, parentNode)
                    vm.$refs.treeDir.stop();
                }
            })
        },
        /**
         * 重新搜索
         */
        reSearch() {
            this.search = "";
            this.$refs.tree.filter("");
        },
        /**
         * 删除
         * @param node
         */
        deleteTreeDir(node) {
            const vm = this, {deleteNodeInterface} = vm;
            let currentNode = vm.$refs.tree.getCurrentNode();
            const index = node.children.findIndex(d => d.id === node.data.id);
            deleteNodeInterface(node.data.id).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("删除成功!");
                    node.children.splice(index, 1);
                    if (currentNode.id === node.data.id) {
                        let parent = vm.selectedNode.parent;
                        vm.$refs.tree.setCurrentKey(parent.data.id);
                        vm.nodeLink(parent.data, parent.node);
                    }
                }
            })
        },
        /**
         * 新建一级目录
         * @param name
         */
        addFirstTree(name) {
            const vm = this, {addDirInterface, settings , showSort} = vm;
            settings.loading = true;
            addDirInterface(name).then(res => {
                if (res.data.code === 0) {
                    let id = res.data.data;
                    vm.successAddTip(name, 'success', '新建目录');
                    vm.expandData = [id];
                    let newTreeNode = vm.newTreeNode({id, label: name, pId: "0"});
                    newTreeNode.level = 1;
                    vm.data.unshift(newTreeNode);
                    if(showSort) vm.sortTreeData(vm.sortVal);
                }
            })
        },
        /**
         * 重命名
         * @param value
         */
        editTree(value) {
            const vm = this, {renameInterface , showSort} = this;
            let node = vm.selectedData;
            renameInterface(node.id, value).then(res => {
                if (res.data.status === 0) {
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.name = value;
                    if(showSort) vm.sortTreeData(vm.sortVal);
                }
            })
        },
        /**
         * 添加子目录
         * @param value
         * @param parent
         * @param id
         */
        addTreeNode(value, parent, id) {
            const vm = this , {showSort} = vm;
            let pId = parent && parent.id;
            let parentData = parent,
                parentN = vm.$refs.tree.getNode(parentData),
                level = parent.level + 1;
            let newTreeNode = vm.newTreeNode({id, label: value, pId});
            newTreeNode.level = level;
            parentN.data.children.unshift(newTreeNode);
            vm.expandData.push(newTreeNode.id);
            vm.$nextTick(() => {
                vm.$refs.tree.setCurrentKey(newTreeNode.id);
                if(showSort) vm.sortTreeData(vm.sortVal);
                let curData = vm.$refs.tree.getCurrentNode();
                let curNode = vm.$refs.tree.getNode(curData);
                vm.nodeLink(curData, curNode);
            });
        },
        /**
         * 移动节点
         * @param curNode
         * @param parentNode
         */
        moveTreeNode(curNode , parentNode){
            const vm = this;
            let moveToNode = curNode;
            moveToNode.pId = parentNode.id;
            this.$refs.tree.remove(curNode);
            let parent = this.$refs.tree.getNode(parentNode);
            this.$refs.tree.append(moveToNode , parent);
            let currentId = curNode.id;
            this.expandData.push(currentId);
            vm.$nextTick(()=>{
                if(vm.$refs.tree){
                    vm.$refs.tree.setCurrentKey(currentId);
                    vm.nodeLink(moveToNode ,parent);
                }
            });
        },
        /**
         * 新 默认节点
         * @param data
         * @return {{}}
         */
        newTreeNode(data) {
            const vm = this;
            let {treeDefault, treeProps} = vm;
            let res = vm.setPropsKey(data, treeDefault);
            for (let k in treeProps) {
                let val = treeProps[k];
                if (data[k]) {
                    res[val] = JSON.parse(JSON.stringify(data[k]));
                }
            }
            return res;
        }
    }
}
