<!--
 * @Author: linxp
 * @Date: 2021-06-15 13:41:26
 * @LastEditTime: 2021-06-15 14:19:00
 * @LastEditors: Please set LastEditors
 * @Description: 选择字段
 * @FilePath: \trunk\src\pages\model-space\data-model\models\rapid-analysis\model\field-choose\index.vue
-->
<template>
    <div class="field-choose" v-loading="settings.loading">
        <el-card style="height: 100%" shadow="never" :body-style="{ padding: 0 }">
            <div class="comm__header" slot="header">
                <span>选择字段</span>
            </div>
            <div class="field-choose__content" v-if="choose">
                <div class="field-choose__tree">
                    <h3>数据集列表</h3>
                    <el-input
                            suffix-icon="el-icon-search"
                            style="margin-bottom: 14px"
                            placeholder="搜索"
                            v-model.trim="filterText"
                            v-input-limit:trim
                    >
                    </el-input>
                    <div class="field-choose__tree-wrap">
                        <div class="tree-type">
                            <dg-select v-model="treeType" :data="typeOpt" @change="treeTypeChange"></dg-select>
                        </div>
                        <div class="tree-content">
                            <dg-tree
                                class="filter-tree"
                                :data="data"
                                node-key="id"
                                :props="defaultProps"
                                :default-expanded-keys="expandData"
                                :filter-node-method="filterNode"
                                @node-click="nodeClick"
                                ref="tree"
                        >
                            <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }" :title="data.label">
                                <span class="node-label"
                                      :title="data.label"
                                      :class="{
                                            'el-icon-folder' : !node.expanded && !data.dbType ,
                                            'el-icon-folder-opened' : node.expanded && !data.dbType,
                                            'el-icon-document' : data.dbType
                                          }"
                                >{{ data.label }}</span>
                            </span>
                        </dg-tree>
                        </div>
                    </div>
                </div>
                <div class="field-choose__wrap">
                    <no-data title="暂未选择字段，请从左侧列表选择数据集并选择字段。" top="0"></no-data>
                </div>
            </div>
            <Field
                    @back="backToTree"
                    :id="id"
                    :config="config"
                    :plans="plans"
                    v-on="$listeners"
                    :loadingParams="settings"
                    v-else
            />
        </el-card>
    </div>
</template>
<script>
import Field from "./field";

import {treeMethods} from "@/api/treeNameCheck/treeName";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/dataset-manage/service-mixins/service-mixins";
import * as modelServives from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import * as pluginServices from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins"
import noData from '@/components/no-data'
import {
    getName,
    getDescription,
} from "@/assets/data/model-plugin/plugin-name-description";
import {mapState} from "vuex";
import {DatasetMixins} from "@views/dataSpace/dataReady/dialog/dataset-tree-panel/dataset-mixins"
export default {
    name: "field-choose",
    mixins: [
        treeMethods,
        commonMixins,
        servicesMixins,
        modelServives.servicesMixins,
        pluginServices.servicesMixins,
        DatasetMixins,
    ],
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
    },
    props: {
        config : Object,
        plans : Array,
    },
    computed: {
        ...mapState({
            planId: (state) => state.plans.planId,
            plan:(state) => state.plans.plans.find(pl => pl.id === state.plans.planId),
        }),
    },
    components: {
        Field, noData
    },
    data() {
        return {
            choose: true,
            filterText: "",
            data: [],
            defaultProps: {
                children: "children",
                label: "label",
            },
            id: -1,
            transId: '',
        };
    },
    methods: {
        backToTree(id) {
            const vm = this, {modelingServices, modelingMock, pluginServices, pluginMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let plugServices = vm.getServices(pluginServices, pluginMock);
            vm.choose = true;
            vm.filterText = '';
            vm.deleteNodeReq(services, vm.transId);
            vm.setPluginDec(plugServices ,'' ,'' , false );
        },
        /**
         * 点击节点，初始化插件
         */
        nodeClick(data) {
            // this.tableName = data.label;
            if (data.belongType) {
                this.settings.loading = true;
                this.addPluginTranStep(data, data.label, 0, 0, this.planId);
            }
        },
        /**
         * 添加插件获取transId
         */
        addPluginTranStep(nodeMenu, name, x, y, parentTransId) {
            const vm = this,
                    {modelingServices, modelingMock, pluginServices, pluginMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let pluginKeyWord = "";
            let {dbType, id} = nodeMenu;
            dbType = String(dbType).toLocaleLowerCase();

            //输入插件走的逻辑
            if (dbType === "") {
                this.$message.error("数据集信息不完整！");
                return;
            }
            if (dbType === "FILE") {
                this.$message.error("暂不支持文件！");
                return;
            }

            if (dbType === "elasticsearch") {
                pluginKeyWord = "cicadaFullTextInput";
            } else if (dbType === "hbase") {
                pluginKeyWord = "cicadaKVInput";
            } else {
                pluginKeyWord = "cicadaStandardSqlInput";
            }
            let plugServices = vm.getServices(pluginServices, pluginMock);
            services.addPluginTranStep(pluginKeyWord, name, x, y, parentTransId)
                    .then(async (result) => {
                        let nodeId = result.data.data;
                        if (result.data.status === 0) {
                            let node = {
                                left: x + "px",
                                top: y + "px",
                                id: nodeId,
                                name: name,
                                code: name,
                                keyWord: pluginKeyWord,
                                tableId: id,
                                type: dbType, //需要type 判断是数据资源还是组件
                                label: getName()[pluginKeyWord] || "",
                                description: getDescription()[pluginKeyWord] || "",
                                status: true,
                            };
                            await vm.initPluginFn(nodeId, node, services);
                            vm.setPluginDec(plugServices, nodeId, name ,true ,services);

                        }
                    });
        },
        setPluginDec(plugServices, nodeId, name , hasInput=true , services) {
            let steps = this.plan.nodes , markTip = 'isFistTrans';
            let inputMemo = "" , nextMemo = "";
            hasInput ? inputMemo = markTip : nextMemo = markTip;
            if(nodeId)plugServices.savePluginDec(nodeId, name, inputMemo);
            if(steps && steps[1]) plugServices.savePluginDec(steps[1].config.id , steps[1].config.name , nextMemo);
            if(steps && steps[1] && nodeId) this.setSourceLink(services , steps , nodeId);
        },
        setSourceLink(services , steps , nodeId){
            let fromN = steps[0].config, toN = steps[1].config;
            if(nodeId && steps[1]){
                services.addTransHop(fromN.name, toN.name, fromN.id, toN.id, this.planId);
            }
        },
        /**
         * 初始化插件
         */
        async initPluginFn(transId, node, services) {
            const vm = this;
            let path = "";
            let {type, tableId} = node;
            let params = {
                tranStepId: transId,
                dataObjId: tableId,
                customDataSet: true,
            };
            let root = "/cicada";
            type = String(type).toLocaleLowerCase();
            if (type === "hbase") {
                path = `${root}/kvInput/`;
            } else if (type === "elasticsearch") {
                path = `${root}/plugin/fulltext/input/`;
            } else {
                path = `${root}/plugin/sql/`;
                params.transStepId = transId;
            }
            await vm.initPluginReq(vm, services, params, path, transId, node);
        },
        /**
         * 输入插件初始化
         * @param vm
         * @param services
         * @param params
         * @param path
         * @param transId
         * @param node 插件的数据
         */
        async initPluginReq(vm, services, params, path, transId, node) {
            await services.initPlug(params, path, vm.settings).then((res) => {
                if (res.data.status === 0) {
                    vm.$message.success("数据集初始化完成!");
                    vm.id = node.tableId;
                    vm.choose = false;
                    vm.$emit("get-config", {...node});
                    vm.transId = transId;
                } else {
                    vm.$message.error("初始化插件失败!");
                    vm.deleteNodeReq(services, transId, false); //执行下删除插件的接口
                }
            });
        },

        /**
         * 初始化树
         */
        initTree() {
            this.treeTypeChange();
        },
        /**
         * 过滤空数据
         * @param data
         * @returns {*}
         */
        filterChildren(data) {
            const vm = this;
            return data.filter(child => {
                if (child.children && child.children.length) child.children = vm.filterChildren(child.children);
                return child.children && child.children.length || child.belongType;
            })
        },
        /**
         * 按目录获取数据集
         */
        getDatasetByCatalog(){
            const vm = this, {settings:loadParams, $services} = this;
            loadParams.loading = true;
            vm.data = [];
            $services("dataReady").queryDataSetTree(true, loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result && result.length) {
                        vm.data = vm.filterChildren(result[0].children);
                    }
                }
            });
        },
        /**
         * 按来源分类获取数据集
         */
        getDatasetBySource(){
            const vm = this, {settings:loadParams, $services} = this;
            loadParams.loading = true;
            vm.data = [];
            $services("dataReady").queryDataSetTreeByDsType({
                hasDataObj: true
            }, loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result && result.length) {
                        vm.data = vm.filterChildren(result);
                        vm.setExpandData(vm.data,'expandData');
                    }
                }
            });
        },
        /**
         * 设置默认展开层级
         * @param {*} data
         * @param key
         */
        setExpandData(data, key) {
            this[key].push(...data.map(item => item.id))
        },
        /**
         * 删除插件
         */
        async deleteNodeReq(services, transId) {
            const vm = this;
            let stepIdArray = [];
            this.plans.forEach(plan => {
                if (plan.id === vm.planId){
                    plan.nodes && plan.nodes.forEach(node => {
                        stepIdArray.push(node.config.id);
                    });
                }
            });
            let params = {
                index : 0,
                stepIdArray : stepIdArray
            }
            services.deleteInputCheck(params).then(async res => {
                if (res.data.status === 0){
                    let errArray = res.data.data;
                    vm.$emit('getErrArray',errArray);
                }
            })
            const {data} = await services.deleteTransStep(transId, vm.planId);
            if (data.status === 0) {
                vm.$message.success("成功删除");
                vm.$emit("get-config", {});
            }

        },

        /**
         * 插件初始化
         */
        initPlugin() {
            const vm = this , {config} = vm;
            let services = vm.$services("plugin3");
            if (config && config.id) {
                // this.tableName = this.config.name;
                vm.transId = this.config.id;
                if (config.keyWord === 'cicadaStandardSqlInput') {
                    vm.sqlInput(config, services, vm);
                } else if (config.keyWord === 'cicadaFullTextInput') {
                    vm.fullTxtInput(config, services, vm);
                } else if (config.keyWord === 'cicadaKVInput') {
                    vm.kvInput(config, services, vm);
                }
            }
        },
        /**
         * 关系库输入
         * @param config
         * @param services
         * @param vm
         */
        sqlInput(config, services, vm) {
            services.sqlView(config.id).then(res => {
                if (res.data.status === 0) {
                    let resData = res.data.data;
                    vm.id = resData.logicObjId ? resData.logicObjId : "";
                    vm.choose = false;
                }
            })
        },
        /**
         * 全文库输入
         * @param config
         * @param services
         * @param vm
         */
        fullTxtInput(config, services, vm) {
            services.fulltext_view(config).then(res => {
                if (res.data.status === 0) {
                    let res_d = res.data.data;
                    vm.id = res_d.pluginMeta.tableId;
                    vm.choose = false;
                }
            })
        },
        /**
         * kv库输入
         * @param config
         * @param services
         * @param vm
         */
        kvInput(config, services, vm) {
            services.kvInputPage("", config.id).then(res => {
                if (res.data.status === 0) {
                    let res_d = res.data.data;
                    vm.id = res_d.pluginMeta.tableId;
                    vm.choose = false;
                }
            })
        }
    },
    created() {
        this.initPlugin();
    }
};
</script>
<style lang="less" scoped>
.field-choose {
    height: 100%;

    /deep/ .el-card__header {
        padding: 0;
        font-size: 16px;
        line-height: 16px;
    }

    .comm__header {
        display: flex;
        justify-content: space-between;
        padding: 16px 28px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    }

    /deep/ .el-card__body {
        height:calc(100% - 50px);
    }

    &__content {
        display: flex;
        height: 100%;
    }

    &__tree {
        width: 276px;
        padding: 18px 13px 18px 30px;
        box-sizing: border-box;
        border-right: 1px solid rgba(0, 0, 0, 0.09);
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        &-wrap {
            margin: 0 -13px 0 0;
            overflow: auto;
            padding: 0 13px 13px 0;
            height: 100%;

            /deep/ .el-tree-node__label {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
            }
            /deep/.custom-tree-node{
                align-items: center;
                display: inline-flex;
            }
        }

        h3 {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bold;
            line-height: 30px;
        }
    }

    &__wrap {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -20%;

    p {
        margin-top: 18px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
}
.tree-type {
    padding-bottom: 10px;
}
.tree-content {
    height:calc(100% - 2rem - 10px);
    overflow: auto;
}

</style>
