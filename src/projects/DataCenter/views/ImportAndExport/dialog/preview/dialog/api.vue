<template>
    <div class="height100">
        <dg-tree
                v-model="vals"
                :data="data"
                node-key="id"
                :props="defaultProps"
                @node-click="handleNodeClick"
                show-checkbox
                ref="tree"
        ></dg-tree>
    </div>
</template>

<script>
    export default {
        props : {
            res : Object,
        },
        data() {
            return {
                vals: '',
                data: [],
                defaultProps: {
                    children: 'children',
                    label: 'name'
                }
            };
        },
        methods: {
            handleNodeClick(data) {
                console.log(data);
            },
            init() {
                const vm = this;
                let treeData  = vm.treeData(vm.res.transClassify);
                vm.res.transClassifyRelation.forEach(element => {
                    vm.setTree(treeData, element.busiClassifyId, element.elementId);
                });
                let treeInfo = [{
                    children : treeData,
                    id : "1",
                    name : "全选",
                }];
                vm.data = JSON.parse(JSON.stringify(treeData));
            },
            treeData(data) {
                let cloneData = JSON.parse(JSON.stringify(data));
                return cloneData.filter(father => {
                    let branchArr = cloneData.filter(child => child.parentBc && father.id == child.parentBc.id);
                    branchArr.length > 0 ? father.children = branchArr : '';
                    return father.parentBc == null;
                })
            },
            setTree(data, busiClassifyId, elementId) {
                const vm = this;
                data.forEach(item => {
                    if (item.id === busiClassifyId) {
                        if(!item.children) item.children = [];
                        let childrenInfo = vm.res.transMetas.filter(e => e.id === elementId);
                        item.children.push(childrenInfo[0]);
                    }
                    if (item.children && item.children.length > 0) vm.setTree(item.children, busiClassifyId, elementId)
                })
            },
        },
        created() {
            this.init();
        }
    };
</script>