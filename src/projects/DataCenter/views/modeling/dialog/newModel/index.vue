<!--
    linxp
    2021.06.09
 -->
<template>
    <div class="new-model">
        <!-- 头部菜单区域 -->
        <header class="new-model__header">
            <div class="new-model__tabs analysis-tab">
                <div class="new-model__add" :style="{ 'left': addLeft + 'px' }" v-if="!isCase && !isDetails">
                    <el-popover ref="popover" popper-class="ce-popover" placement="top-start" v-model="showAddBox"
                        trigger="click" width="90" :visible-arrow="false" :offset="-50">
                        <div class="new-model__add-box">
                            <div @click="addTab({}, false, dirId)">{{ addModelTxt }}</div>
                            <div @click="openModel('DEF')">{{ openModelTxt }}</div>
                        </div>
                        <i slot="reference" :title="newAndOpenTip" class="dg-iconp icon-l-plus"></i>
                    </el-popover>
                </div>
                <model-tab v-if="editableTabs.length" v-model="editableTabsValue" :editable-tabs="editableTabs"
                    @tab-click="tabClick" @tab-remove="removeTab">
                    <model-panel slot="tabCont" slot-scope="{item}" :rowData="item.rowData" :isTemplate="isTemplate"
                        :componentType="componentType" @setLoading="setLoading" ref="modelPanel" :tabId="item.id"
                        :modelParams="modelParams" :focusTab="editableTabsValue" @openTab="addTab" :isCase="isCase" />
                </model-tab>
            </div>
            <div class="new-model__menus" :title="changeTabTip" ref="icon" @click="showMore = !showMore">
                <i class="dg-iconp icon-menu"></i>
            </div>
            <div class="new-model__more" v-show="showMore">
                <div class="new-model__item" v-for="(menu, idx) in editableTabs" :key="idx" :title="menu.label"
                    :class="{ 'active': menu.id === editableTabsValue }" @click="checkTab(menu)">{{ menu.label }}
                </div>
            </div>
            <span class="new-model__actions">
                <help-document :code="'processModelingSaveTempTrans'" class="mr-3"></help-document>
                <i :class="['dg-iconp', !isFull ? 'icon-maximize' : 'icon-minimum']"
                    :title="isFull ? closeFullScreenTip : fullScreenTip" @click="handleFull"></i>
                <i class="dg-iconp icon-l-close" :title="closeTip" @click="handleClose"></i>
            </span>
        </header>
        <OpenModel ref="openM" @openPage="addTab" />
        <SaveModel ref="SaveModel" v-on="$listeners" @modelSaveSuccess="modelSaveSuccess" />
    </div>
</template>
<script>

import ModelPanel from "@/projects/DataCenter/views/modeling/dialog/newModel/model/model-panel"
import { ModelMixins } from "@/components/flowPanel/mixins/model-mixins";
import ModelTab from "@/projects/DataCenter/views/modeling/dialog/model-tab"
import { tabMixins } from "@/projects/DataCenter/views/modeling/dialog/model-tab/model-tab-mixins";
import { common } from "@/api/commonMethods/common";
import { mapActions } from "vuex";
import SaveModel from "@/projects/DataCenter/views/modeling/flowPanel/save-model";


export default {
    name: "new-model",
    mixins: [ModelMixins, common, tabMixins],
    components: {
        ModelPanel,
        ModelTab,
        SaveModel,
    },
    props: {
        dirId: String,
        isLook: {
            type: Boolean,
            default: false,
        },
        isCase: {
            type: Boolean,
            default: false,
        },
        isDetails: {  //是否从详情进入
            type: Boolean,
            default: false,
        },
        componentType: String,
        container: {
            type: String,
            default: () => {
                return 'flowContainer'
            }
        },
    },
    watch: {
        "editableTabs.length": {
            handler(len) {
                let data = this.editableTabs;
                this.setPlanId(this.editableTabsValue);
                this.addPlan(data);
            },
            deep: true,
        },
        editableTabsValue(val) {
            this.setPlanId(val);
        },
    },
    data() {
        return {
            showAddBox: false,
            isFull: false,
            menus: [],
            showMore: false,
            tabI: 0,
        };
    },
    beforeDestroy() {
        this.clearPlans();
    },
    methods: {
        ...mapActions(["addPlan", "setPlanId", "clearPlans"]),
        /**
         * 运行时loading
         * */
        setLoading(flag) {
            this.modelParams.loading = flag;
        },
        /**
         * 保存成功修改tab
         * */
        change(name, id) {
            this.triggerEvent('resize', 300);
            this.editableTabs.forEach(item => {
                if (item.id === id) {
                    item.label = name;
                    item.rowData.isNew = false;
                    if (item.rowData && item.rowData.modelName) {
                        item.rowData.modelName = name;
                    }
                }
            })
        },
        /**
         * 关闭tab提示
         * */
        removeTab(targetName) {
            const vm = this, { editableTabs } = vm;
            let tabData = editableTabs.find(tab => tab.id === targetName);
            let { isNew } = tabData.rowData;
            if (isNew) {
                // vm.confirm("提示" , "该模型未保存，是否放弃修改？" , ()=>{
                //     vm.closeTab(targetName);
                // })
                this.$dgConfirm('该模型未保存，是否放弃修改？', '提示', {
                    confirmButtonText: '保存并退出',
                    cancelButtonText: '取消编辑',
                    closeOnClickModal: false,
                    showClose: false,
                    type: 'warning'
                }).then(() => {
                    let node = tabData.nodes, rowData = tabData.rowData;
                    vm.model_save(node, rowData, editableTabs, vm.tabI);
                }).catch(() => {
                    vm.closeTab(tabData.id);
                });
            } else {
                vm.closeTab(targetName);
            }
        },
        /**
         * 关闭tab
         * */
        closeTab(targetName) {
            const vm = this;
            let tabs = this.editableTabs;
            vm.isAdd = false;
            vm.planIndex = tabs.findIndex(item => item.id === targetName)
            let activeName = vm.editableTabsValue;
            if (activeName === targetName) {
                tabs.forEach((tab, index) => {
                    if (tab.id === targetName) {
                        let nextTab = tabs[index + 1] || tabs[index - 1];
                        if (nextTab) {
                            activeName = nextTab.id;
                        }
                    }
                });
            }
            vm.editableTabsValue = activeName;
            vm.editableTabs = tabs.filter(tab => tab.id !== targetName);
            if (vm.editableTabs.length === 0) {
                vm.$emit('closeTab');
                vm.$emit("close", tabs[0] && !(tabs[0].rowData.isNew) ? tabs[0].id : '');
            }
        },
        /**
         * 判断是否有输入
         * @return {*}
         */
        checkHasDataSet(nodes) {
            const vm = this;
            // if(!vm.checkHasInputAndOut(nodes)){
            //     vm.$message.warning("请配置输入输出插件");
            //     return false;
            // }
            let hasDataset = nodes.some(item => item.type !== 'plug' || item.keyWord === 'cicadaServiceInputMeta' || item.keyWord === "cicadaFileInputMeta" || item.keyWord === "cicadaMetaServiceInput");
            if (!hasDataset) vm.$message.warning("请配置数据集或输入插件");
            return hasDataset;
        },
        /**
         * 判断是否有服务输入输出
         * @return {*}
         */
        checkHasInputAndOut(nodes) {
            const vm = this;
            let id = '';
            let hasInputAndOut = nodes.some(item => item.keyWord === "cicadaMetaServiceOutput" || item.keyWord === "cicadaServiceInputMeta") || nodes.some(item => item.keyWord === "cicadaMetaServiceInput");
            if (hasInputAndOut) {
                id = nodes.find(item => item.keyWord === "cicadaMetaServiceInput").id
            }
            return id;
        },
        model_save(node, rowData, allTabsInfo, i) {
            if (i > allTabsInfo.length) {
                return;
            }
            const vm = this;
            if (!vm.checkHasDataSet(node)) return;
            if (node.filter(item => item.keyWord === "cicadaMetaServiceOutput").length > 1) {
                return this.$message.warning('方案只能有一个API响应结果插件')
            }
            vm.$refs.SaveModel.show(rowData, 'ETLInstance');
        },
        modelSaveSuccess(tranceId) {
            const vm = this, { editableTabs } = vm;
            if (tranceId) vm.closeTab(tranceId);
            if (vm.editableTabs.length === 0) return;//判断是否还有tab页
            // vm.closeTab(vm.editableTabs[vm.tabI].id);
            vm.tabCloseCheck(editableTabs);
        },
        /**
         * 关闭窗口操作
         */
        tabCloseCheck(editableTabs) {
            const vm = this;
            if (editableTabs[0].rowData.isNew) {
                this.$dgConfirm('该模型未保存，是否放弃修改？', '提示', {
                    confirmButtonText: '保存并退出',
                    cancelButtonText: '取消编辑',
                    closeOnClickModal: false,
                    showClose: false,
                    type: 'warning'
                }).then(() => {
                    let node = editableTabs[0].nodes, rowData = editableTabs[0].rowData;
                    vm.model_save(node, rowData, editableTabs, vm.tabI);
                }).catch(() => {
                    vm.closeTab(editableTabs[0].id);
                    vm.modelSaveSuccess();
                });
            } else {
                vm.loading = true;
                setTimeout(() => {
                    vm.loading = false;
                    // vm.$message.success("保存成功");
                    vm.modelSaveSuccess(editableTabs[0].id);
                }, 300)
            }
        },
        /**
         * 关闭弹窗
         */
        handleClose() {
            const vm = this, { editableTabs } = vm;
            if (vm.isLook) {
                vm.$emit("close");
                return
            }
            vm.tabCloseCheck(editableTabs);
        },
        /**
         * 全屏
         */
        handleFull() {
            this.isFull = !this.isFull;
            if (
                (document.fullScreenElement !== undefined && document.fullScreenElement === null) ||
                (document.msFullscreenElement !== undefined && document.msFullscreenElement === null) ||
                (document.mozFullScreen !== undefined && !document.mozFullScreen) ||
                (document.webkitIsFullScreen !== undefined && !document.webkitIsFullScreen)
            ) {
                if (document.documentElement.requestFullScreen) {
                    document.documentElement.requestFullScreen();
                } else if (document.documentElement.mozRequestFullScreen) {
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.webkitRequestFullScreen) {
                    document.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            } else {
                if (document.cancelFullScreen) {
                    document.cancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        },
    },
    mounted() {
        // 点击外部关闭更多消息
        const vm = this;
        document.addEventListener("click", (e) => {
            const dom = document.getElementsByClassName("new-model__more")[0];
            const target = vm.$refs.icon;
            if (!target || target.contains(e.target)) {
                return;
            }
            if (!dom.contains(e.target)) {
                this.showMore = false;
            }
        });
    },
};
</script>
<style lang="less" scoped>
.menu-active {
    background: rgba(0, 136, 255, 0.08);
    border-radius: 2px;
    font-size: 14px;
    color: #0088ff;
}

.new-model {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    &__header {
        width: 100%;
        display: block;
        align-items: center;
        background-color: #ffffff;
        //padding: 0 16px;
        height: 40px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        padding-top: 5px;
        position: relative;
    }

    &__actions {
        position: absolute;
        right: 20px;
        top: 14px;

        i {
            font-size: 16px;
            color: #000000;
            margin: 0 4px;
            cursor: pointer;
        }
    }

    &__menus {
        position: absolute;
        padding-right: 20px;
        cursor: pointer;
        user-select: none;
        left: 10px;
        top: 14px;
        background: #fff;
        z-index: 10;

        &::before {
            content: "";
            position: absolute;
            top: 50%;
            margin-top: -9px;
            right: -1px;
            height: 18px;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.09);
        }
    }

    &__more {
        position: absolute;
        top: 46px;
        left: 0;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
        border-radius: 2px;
        max-width: 260px;
        max-height: 300px;
        z-index: 20;
        overflow: auto;
        overflow-x: hidden;

        div.new-model__item {
            position: relative;
            line-height: 36px;
            cursor: pointer;
            padding: 0 20px;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &:hover,
            &.active {
                color: #0088ff;
            }
        }
    }

    &__tabs {
        align-items: center;
        height: 100%;
        display: block;
    }

    &__add {
        padding: 0 14px;
        cursor: pointer;
        line-height: 40px;
        position: absolute;
        z-index: 1;
        transition: 200ms;
        top: 4px;

        &-box {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            text-align: center;

            div {
                line-height: 36px;
                cursor: pointer;

                &:hover {
                    color: #0088ff;
                }
            }
        }

        i {
            font-size: 16px;
        }
    }

}
</style>
<style>
.ce-popover.el-popover {
    min-width: auto;
}

.node--primary svg {
    fill: #1890ff
}

.node--info svg {
    fill: #faad14;
}

.node--success svg {
    fill: #52c41a;
}

.node--chart svg {
    fill: #6D87F1;
}
</style>
