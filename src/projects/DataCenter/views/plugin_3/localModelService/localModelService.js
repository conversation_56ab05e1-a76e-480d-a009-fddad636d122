

import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin/service-mixins/service-mixins";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"

export default {
    name: "localModelService",
    mixins : [pluginMixins , servicesMixins],
    components: {preView},
    props:{
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    data(){
        return {
            leftLabel : "服务输入参集映射：",
            inputValueTable : "",
            wayOption: [
                {label: "按字段名", value: "byCode"},
                {label: "按字段中文名", value: "byName"}
            ],
            mappingTxt: "自动映射",
            tableData: [],
            tHeadData: [
                {
                    prop: "requestField",
                    label: "请求字段",
                    minWidth: 120,
                    align: "left"
                },
                {
                    prop: "fieldMemo",
                    label: "字段描述",
                    minWidth: 100,
                    resizable: false
                },
                {
                    prop: "dataType",
                    label: "数据类型",
                    minWidth: 90,
                    align: "center"
                },
                {
                    prop: "must",
                    label: "是否必填",
                    minWidth: 100,
                    align: "center",
                },
                {
                    prop: "inputParam",
                    label: "入参字段",
                    minWidth: 130,
                    align: "center",
                },
                {
                    prop: "fieldChName",
                    label: "字段中文",
                    minWidth: 130,
                    align: "center",
                },
            ],
            options : [],
            codeOptions : [],
            zhNameOptions : [],
            mappingWay: "byCode",
            total: 0,
            originalData : [],
            serviceId :"",
            serviceClassify : "",
            serviceType : "",
            paramInfoData : [],
            lastIndex : 0,//上一次点击的下标
            inputStepId : "",
        }
    },
    computed : {
        mapLabel(){
            return `${this.mappingTxt} (${this.wayOption.find(opt => opt.value === this.mappingWay).label})`;
        },
    },
    watch: {
        inputValueTable(val) {
            this.filterTableMethod(val);
        }
    },
    methods: {
        /**
         * 输入字段名变更事件
         * @param {} row 
         * @param {*} val 
         */
        codeChange(row, val) {
            if (val === '') {
                row.fieldChName = '';
                return;
            }
            row.fieldChName = this.codeOptions.find(item => item.value === val).name;
        },
        paramChange(item, i) {
            const vm = this;
            vm.setParamJson();
            vm.lastIndex = i;
            if(item.inputStepId) {
                let inputColumn = vm.options.find(e => e.value === item.inputStepId);
                vm.structureChange("", inputColumn);
            }
            vm.tableData = JSON.parse(item.cicadaModelServiceInputJson);
        },
        setParamJson() {
            this.paramInfoData[this.lastIndex].cicadaModelServiceInputJson = JSON.stringify(this.tableData);
        },
        /**
         * 左侧结构下拉框变更事件
         */
        structureChange(value, e) {
            this.codeOptions = [];
            e.inputColumn.forEach(item =>{
                this.codeOptions.push({
                    value : item.columnName,
                    label : item.columnName,
                    name : item.columnZhName
                })
                this.zhNameOptions.push({
                    label : item.columnZhName,
                    value : item.columnZhName,
                    code : item.columnName,
                });
            })
        },
        filterTableMethod(text) {
            this.tableData = text ? this.originalData.filter(list =>
                    list.requestField.indexOf(text) > -1 )
                : [...this.originalData];
        },
        wayChange(val) {
            if (this.mappingWay !== val) this.outMappingColumn(val);
        },
        async init(){
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = this.$services("plugin3"); 
            settings.loading = true;
            this.tableData = [];
            this.originalData = [];
            this.codeOptions = [];
            await services.queryLocalModelServiceData(vm.sourceData.id, vm.sourceData.keyWord, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data.pluginMeta.cicadaModelServiceInputSets[0].inputStepId) {
                        let firstParamInfo = res.data.data.inputColumnMaps.filter(item => item.inputStepId === res.data.data.pluginMeta.cicadaModelServiceInputSets[0].inputStepId);
                        if (firstParamInfo.length > 0) {
                            firstParamInfo[0].inputColumns.forEach(element =>{
                                vm.codeOptions.push({
                                    label : element.columnName,
                                    value : element.columnName,
                                    name : element.columnZhName
                                });
                                vm.zhNameOptions.push({
                                    label : element.columnZhName,
                                    value : element.columnZhName,
                                    code : element.columnName,
                                });
                            })
                        }
                    }
                    vm.paramInfoData = res.data.data.pluginMeta.cicadaModelServiceInputSets;
                    vm.tableData = JSON.parse(res.data.data.pluginMeta.cicadaModelServiceInputSets[0].cicadaModelServiceInputJson);
                    vm.originalData = JSON.parse(res.data.data.pluginMeta.cicadaModelServiceInputSets[0].cicadaModelServiceInputJson);
                    res.data.data.inputColumnMaps.forEach(element => {
                        this.options.push({
                            label : element.inputStepName,
                            value : element.inputStepId,
                            inputColumn : element.inputColumns,
                        });
                        vm.inputStepId = element.inputStepId;
                    });
                    vm.structureChange("",this.options[0]);
                    vm.serviceId = res.data.data.pluginMeta.serviceId;
                    vm.serviceClassify = res.data.data.pluginMeta.serviceClassify;
                    vm.serviceType = res.data.data.pluginMeta.serviceType;
                }
            })
            if (!this.tableData[0].inputParam) this.outMappingColumn();
        },
        outMappingColumn(val){
            const vm = this;
            if(typeof val === "string") vm.mappingWay = val;
            this.mappingField();
        },
        mappingField(numList) {
            const vm = this, { mappingWay} = vm;
            if (mappingWay === "byCode") {
                this.tableData.forEach(item =>{
                    item.inputParam = "";
                    item.fieldChName = "";
                    this.codeOptions.forEach(code=>{
                        if (code.value.toLowerCase() === item.requestField.toLowerCase()) {
                            item.inputParam   = code.value;
                            item.fieldChName = this.zhNameOptions.find((i) => i.code === code.value).value;
                        }
                    })
                })
            } else {
                this.tableData.forEach(item =>{
                    item.inputParam   = "";
                    item.fieldChName = "";
                    this.zhNameOptions.forEach(code=>{
                        if (code.value && code.value.toLowerCase() === item.fieldMemo.toLowerCase()) {
                            item.fieldChName= code.value;
                            item.inputParam   = this.codeOptions.find((i) => i.name === code.value).value;
                        }
                    })
                })
            }
            return numList;
        },
        save(fn,showTip) {
            const vm = this, { settings} = this;
            let services = this.$services("plugin3"); 
            let allChoose = false;
            vm.tableData.forEach(item => {
                if (item.must) {
                    if (item.inputParam ==='' || item.fieldChName === '') {
                        allChoose = true;
                    }
                }
            })
            vm.paramInfoData.forEach(item =>{
                item.inputStepId = vm.inputStepId;
            })
            if (allChoose) {
                this.$message('必填项请填写完整信息');
                return;
            }
            vm.setParamJson();
            let params = {
                cicadaModelServiceInputSets : vm.paramInfoData,
                serviceId : this.serviceId,
                serviceClassify : this.serviceClassify,
                serviceType : this.serviceType,
                pluginCode : vm.sourceData.keyWord
            };
            settings.loading = true;
            services.saveModelServicePlugin(this.sourceData.id, params, settings).then(res => {
                if (res.data.status === 0) {
                    showTip && this.$message.success("保存成功");
                    vm.$emit("setPreviewAble");
                    fn && fn();
                }
            })
        },
        view(){
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = this.$services("plugin3"); 
            settings.loading = true;
            services.saveModelServicePlugin(this.sourceData.id, params, settings).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("保存成功");
                }
            })
        },
        preview() {
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.preview.show(this.rowData, this.sourceData);
            });
            
        },
    },
    mounted() {
        this.init();
    },
}
