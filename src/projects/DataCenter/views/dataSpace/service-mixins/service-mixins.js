import {commonMixins} from "@/api/commonMethods/common-mixins";

export const servicesMixins = {
    mixins: [commonMixins],
    data() {
        const {isMock} = this;
        let dataSpaceServices = require("@/projects/DataCenter/services/dataSpace-services/dataSpace-services"),
            dataSpaceMock = isMock ? require("@/projects/DataCenter/mock/dataSpace-mock/dataSpace-mock") : {},//数据连接
            dataReadyServices = require("@/projects/DataCenter/services/dataSpace-services/dataReady-services"),
            dataReadyMock = isMock ? require("@/projects/DataCenter/mock/dataSpace-mock/dataReady-mock") : {},//数据准备
            datasetServices = require("@/projects/DataCenter/services/dataset-services/dataset-services"), //sql分析
            datasetMock = isMock ? require("@/projects/DataCenter/mock/dataset-mock/dataset-mock") : {},
            listApi = require("@/projects/DataCenter/services/dataSource-services/listApi"),//数据源
            listMock = isMock ? require("@/projects/DataCenter/mock/dataSource-mock/listMock") : {};
        return {
            dataSpaceServices ,
            dataSpaceMock ,
            dataReadyServices,
            dataReadyMock,
            datasetServices ,
            datasetMock ,
            listApi ,
            listMock,
            services : this.getServices(dataSpaceServices , dataSpaceMock)
        }
    }
}
