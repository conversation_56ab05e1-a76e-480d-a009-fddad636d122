import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"

export default {
    name: "list" ,
    mixins: [commonMixins, servicesMixins, common],
    props : {
        treeNode : Object
    },
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        }
    },
    data(){
        return {
            filterTxt : "",
            buttonTxt :"新建数据模型" ,
            addModelRight : $right["SQLModelingSaveModel"] ,
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "中文名称",
                    width: "",
                    align: "left"
                },
                {
                    prop: "code",
                    label: "英文名称",
                    width: "",
                    align: "left"
                },
                {
                    prop: "creator",
                    label: "创建人",
                    width: "",
                    align: "center"
                },
                {
                    prop: "time",
                    label: "最后编辑时间",
                    width: "190",
                    align: "center"
                },{
                    prop: 'operate',
                    label: '操作',
                    minWidth: 120,
                    align: "center"

                }
            ],
            operateIcons: [
                {icon: "&#xe6c0;", tip: "编辑", clickFn: this.editPanel , condition : (right) => right.indexOf($right["SQLModelingSqlEditPage"]) > -1},
                {icon: "&#xe607;", tip: "重命名", clickFn: this.renamePanel , condition : (right) => right.indexOf($right["SQLModelingUpdateSqlModelName"]) > -1},
                {icon: "&#xe6bf;", tip: "移动", clickFn: this.moveTo , condition : (right) => right.indexOf($right["SQLModelingMoveModel"]) > -1},
                {icon: "&#xe65f;", tip: "删除", clickFn: this.deletePanel , condition : (right) => right.indexOf($right["SQLModelingDelSqlObj"]) > -1},
            ],
        }
    },
    methods : {
        editPanel(){},
        renamePanel(){},
        moveTo(){},
        deletePanel(){},
        menuCommand(command) {
            command.m_icon.clickFn(command.row, command.$index);
        },
        searchData(){
            this.changePage(1);
        },
        addVisualPanel() {
            this.$emit("addNewModel");
        },
        changePage(index){
            const vm = this , {datasetServices , datasetMock ,settings } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let treeId = vm.treeNode.id;
            if (vm.treeNode.parentId === "-1" || vm.treeNode.parentId === undefined) {
                treeId = "";
            }
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                pageSize: vm.paginationProps.pageSize,
                pageNum: index,
                sortField: "name",
                sortOrder: "desc",
                condition: vm.filterText,
                parentId: treeId,
            };

        },
        changeSize (){
            this.changePage(1);
        }
    },
    created(){
        this.changePage(1);
    }
}