import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common";
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {servicesMixins} from "../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import codeEditor from "./codeEditor";

export default {
    name: "operatorManage",
    components: {UserFormPop, codeEditor},
    mixins: [commonMixins, servicesMixins, common,listMixins,coustTableH],
    filters: {
        state(val) {
            if (val === "0") {
                return "启用";
            } else if (val === "1") {
                return "禁用";
            }
        }
    },
    computed: {
        ...mapGetters(["userInfo", "userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    data() {
        return {
            langTypeOption : [
                {
                    value : "java",
                    label : "java",
                }, {
                    value : "scala",
                    label : "scala",
                }
            ],//函数逻辑代码下拉框
            //角色管理搜索
            searchVal: "",
            //编辑弹窗控制显示与否
            editVisible: false,
            //角色信息编辑
            userformTitle: "基本信息",
            // 角色管理表格数据
            tableData: [],
            addUserRight: $right["udfOperatorManageCreateUdf"],
            tableHead : [
                {
                    prop: "name",
                    label: "算子名称",
                    minWidth: "160",
                    align: "left"
                },
                {
                    prop : "classifyName" ,
                    label :"所属分类",
                    resizable :false

                },{
                    prop : "operate" ,
                    label : "操作",
                    resizable :false,
                    width : 200,
                    align: "center"
                }
            ],
            //操作栏
            operator: [
                {
                    label: '编辑',
                    clickFn: this.edit,
                    condition: (row, right) => row.objCode !== this.$userCode && right.indexOf($right["udfOperatorManageEditUdf"]) > -1,
                    icon: "&#xe6ad;"
                }, /*{
                    label: '权限管理',
                    // clickFn: this.rowDetail,
                    condition: (row, right) => right.indexOf($right["udfOperatorManageAuth"]) > -1,
                    icon: "&#xe6c5;"
                },*/ {
                    label: '删除',
                    clickFn: this.deleteRow,
                    condition: (row, right) => row.objCode !== this.$userCode && right.indexOf($right["udfOperatorManageDeleteUdf"]) > -1,
                    icon: "&#xe847;"
                }
            ],
            //分页
            paginationProps: {
                currentPage: 1,
                pageSizes: [10, 20],
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            total: 0,
            statusList: [
                {
                    label: "启用",
                    value: "0"
                },
                {
                    label: "禁用",
                    value: "1"
                }
            ],
            classifyList: [],
            defaultProps: {
                value: 'id',
                label: 'name',
                children: 'children'
            },
            rules: {
                example: [
                    {required: true, message: "示例不能为空", trigger: ["blur", "change"]},
                    {
                        validator: this.checkedCommon,
                        trigger: "blur",
                        message: "请输入字母、数字、下划线(非数字开头)"
                    },
                ],
                name: [
                    {required: true, message: "算子名称不能为空", trigger: ["blur", "change"]},
                    {
                        validator: this.checkedCommon,
                        trigger: "blur",
                        reg: /^[a-zA-Z0-9_（）\u4e00-\u9fa5]+$/,
                        message: "请输入中文、字母、数字、下划线"
                    }
                ],
                classify: [
                    {required: true, message: "分类不能为空", trigger: 'change'}
                ],
                expression: [
                    {required: true, trigger: 'change', message: "表达式不能为空"}
                ],
                returnType: [
                    {required: true, trigger: 'change', message: "返回类型不能为空"}
                ],
                memo: [
                    {
                        required: true, trigger: 'change', message: "算子说明不能为空"
                    }
                ],
                belong : [
                    {required : true , trigger: 'change', message: "可用版本不能为空"}
                ],
            },
            paramRules : {
                name : [
                    {required : true , trigger: "change", message: "中文名称不能为空"}
                ],
                dataType : [
                    {required : true , trigger: 'change', message: "数据类型不能为空"}
                ],
            },
            versionOptions : [
                {label : "基础版",value : "basic"},
                {label : "标准版",value : "standard"},
            ],
            //算子新增弹窗form数据
            currentData: {
                id: "",
                example: "",
                name: "",
                classify: "",
                expression: "",
                returnType: '',
                memo: "",
                status: "",
                belong : "basic",
                funcClassify : "system",
                functionCode : "",
                langType : "java",
                className : "",
                methodName : "",
                //用户信息新增弹窗复选框绑定值
                roleVal: [],
                password: "",
                udfParameterVos: [
                    {
                        name: '',
                        code: '',
                        dataType: ''
                    }
                ]
            },
            //用户信息新增弹窗复选框组
            roleData: [],
            //控制用户信息新增弹窗显隐参数
            operatorForm: {
                title: "",
                visible: false,
                loading: false,
                state: "",
                functionCode: "",
            },
            ruleTypeOptions: [
                {}
            ],
            parameter: {
                name: '',
                code: '',
                dataType: ""
            },
            showF: false
        };
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        clearFormData() {
            this.currentData = {
                id: "",
                example: "",
                name: "",
                classify: "",
                expression: "",
                returnType: '',
                memo: "",
                status: "",
                roleVal: [],
                udfParameterVos: [
                    {
                        name: '',
                        code: "",
                        dataType: ""
                    }
                ],
                password: "",
                funcClassify : "system",
                functionCode : "",
                langType : "java",
                className : "",
                methodName : "",
            };
            this.showF = false;
        },
        //校验
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        reNewData() {
            const vm = this;
            vm.$nextTick(() => {
                vm.$refs.form.clearValidate();
                vm.showF = true;
            })

        },
        //获取算子
        async getClassifyList() {
            const vm = this, {operatorServices, operatorMock} = this;
            vm.classifyList = [];
            let services = vm.getServices(operatorServices, operatorMock);
            await services.getOperatorTree(process.env.VUE_APP_SECRET).then(res => {
                if (res.data.status === 0) {
                    let data = res.data.data;
                    let obj = {};
                    data.forEach(item => {
                        if (item.code === "UDF_OPERATOR") {
                            obj = item;
                            let objs = [];
                            item.children.forEach(it => {
                                objs.push({
                                    code: it.code,
                                    id: it.id,
                                    name: it.name,
                                    pid: it.pId,
                                    udfType: it.udfType
                                });
                            });
                            obj.children = objs;
                        }
                    });
                    vm.classifyList.push(obj);
                }
            })
        },
        getExpression() {
            const vm = this , {operatorServices, operatorMock} = this;
            let services = vm.getServices(operatorServices, operatorMock);
            vm.currentData.udfParameterVos = [];
            services.expressionParser(vm.currentData.expression).then(res => {
                if (res.data.status === 0) {
                    res.data.data.forEach(item => {
                        vm.currentData.udfParameterVos.push({
                            name: '',
                            code: item,
                            dataType: ''
                        });

                    });
                }
            });
        },
        setSelectId(data) {
            let result = [];
            data.forEach(d => {
                if (d) {
                    result.push({id: d});
                }
            });
            return result;
        },
        //添加用户
        async addUser() {
            const vm = this;
            let {operatorForm} = this;
            operatorForm.title = "算子注册";
            operatorForm.state = "add";
            operatorForm.functionCode = "udfOperatorManageCreateUdf";
            await vm.getClassifyList();
            await vm.getParameterTypes();
            const layer = this.$dgLayer({
                title : '算子注册',
                content : require("./setting.vue"),
                maxmin:true,
                move:false,
                area  :['1200px' , "90%"],
                props : {
                    operatorForm ,
                    classifyList : vm.classifyList,
                    ruleTypeOptions : vm.ruleTypeOptions,
                },
                on : {
                    submit(){
                        vm.changePage(1);
                    }
                },
                cancel: function (index, layero) {
                    // 关闭对应弹窗的ID
                    // vm.changePage(1 , 20)
                    vm.changePage(1);
                    layer.close(index);
                    return false;
                },
            })
        },

        async getParameterTypes() {
            const vm = this, {operatorServices, operatorMock} = this;
            let services = vm.getServices(operatorServices, operatorMock);
            await services.getParameterTypes().then(res => {
                if (res.data.status === 0) {
                    vm.ruleTypeOptions = res.data.data;
                }
            })
        },

        //编辑表格
        //编辑弹窗功能显示与否
        async edit(row, state, title) {
            const vm = this;
            let {operatorForm, currentData} = this;
            operatorForm.state = state || 'edit';
            operatorForm.title = title || "算子信息编辑";
            operatorForm.functionCode = "udfOperatorManageEditUdf";
            currentData.id = row.id;
            await vm.getClassifyList();
            await vm.getParameterTypes();
            currentData.example = row.example || "";
            currentData.returnType = row.returnType;
            currentData.name = row.name || "";
            currentData.classify = row.classify || "";
            currentData.expression = row.expression || "";
            currentData.memo = row.memo || "";
            currentData.status = row.enableState || "";
            currentData.password = row.password || "";
            currentData.belong = row.belong || "";
            currentData.roleVal = [];
            currentData.udfParameterVos = JSON.parse(JSON.stringify(row.udfParameterVos));
            currentData.funcClassify = row.funcClassify ||"",
            currentData.functionCode = row.functionCode ||"",
            this.$nextTick(() =>{
                if (vm.$refs.codeEditor) {
                    vm.$refs.codeEditor.statement = row.functionCode ||"";
                }
            })
            currentData.langType = row.langType || "",
            currentData.className = row.className || "",
            currentData.methodName = row.methodName || "";
            const layer = this.$dgLayer({
                title : '算子信息编辑',
                content : require("./setting.vue"),
                maxmin:true,
                move:false,
                area  :['1200px' , "90%"],
                props : {
                    operatorForm ,
                    classifyList : vm.classifyList,
                    ruleTypeOptions : vm.ruleTypeOptions,
                    rowData : JSON.parse(JSON.stringify(currentData)),
                },
                on : {
                    submit(){
                        vm.changePage(1);
                    }
                },
                cancel: function (index, layero) {
                    // 关闭对应弹窗的ID
                    // vm.changePage(1 , 20)
                    vm.changePage(1);
                    layer.close(index);
                    return false;
                },
            })

        },
        //删除表格行
        deleteRow(row) {
            const vm = this, {operatorServices, operatorMock} = this;
            let services = vm.getServices(operatorServices, operatorMock);
            vm.confirm("提示", "你确定删除\"" + row.name + "\"？", () => {
                services.deleteUdfOperator(row.id).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功");
                        vm.changePage(1);
                    } else {
                        vm.$message.warning(`\"${row.name}\"删除失败`);
                    }
                })
            })
        },
        getBelong(data) {
            const {tableData} = this;
            data.forEach((d, inx) => {
                let objRelSet = d.tSysAuthObjRelSet;
                if (!tableData[inx].roleList) tableData[inx].roleList = [];
                if (objRelSet.length) {
                    objRelSet.forEach(rel => {
                        if (rel.toAuthObj) {
                            if (rel.relationType === "0") {
                                tableData[inx].belong = rel.toAuthObj.objName;
                                tableData[inx].belong_code = rel.toAuthObj.objCode;
                                tableData[inx].belong_id = rel.toAuthObj.id;
                            } else if (rel.relationType === "1") {
                                tableData[inx].roleList.push(
                                    {
                                        objName: rel.toAuthObj.objName,
                                        objCode: rel.toAuthObj.objCode,
                                        id: rel.toAuthObj.id,
                                    }
                                )
                            }
                        }
                    })
                }
            })
        },
        async changePage(index) {
            const vm = this;
            let {settings, searchVal , operatorServices , operatorMock} = this;
            settings.loading = true;
            let pageSize = vm.paginationProps.pageSize;
            let services = vm.getServices(operatorServices, operatorMock);
            let data = {
                code: searchVal,
                name: searchVal,
                pageNum: index,
                pageSize: pageSize
            };
            services.getOperatorListPage(data, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.tableData = result.dataList;
                    vm.total = result.totalCount;
                    vm.paginationProps.currentPage = result.pageIndex;
                    // vm.getBelong(result);
                }
            })
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        //mock 查询列表数据
        async searchTableData() {
            const vm = this;
            let searchVal = vm.searchVal.toLowerCase();
            await vm.changePage(1);
            vm.tableData = vm.tableData.filter(tab => {
                return tab.objName.toLowerCase().indexOf(searchVal) > -1 || tab.objCode.toLowerCase().indexOf(searchVal) > -1;
            })
        }
    },
    created() {
        this.changePage(1);
    }
}
