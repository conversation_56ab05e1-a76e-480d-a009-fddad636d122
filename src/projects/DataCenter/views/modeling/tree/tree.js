import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters,mapState} from "vuex"

export default {
    name: "tree" ,
    mixins : [treeMethods , commonMixins , servicesMixins],
    computed : {
        ...mapGetters(["userRight","userInfo"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        disableClick(){
            return (data) => data.disabled;
        },
        ...mapState({
            // 他人空间目录 Id
            otherUserCatalogId: (state) => state.plans.otherUserCatalogId,
            // 可编辑节点 非分享，非他人目录(不等于他人目录 Id ，没有用户 id 值)
            nodeEditable: (state) => {
                return (data) => data.id !== state.plans.shareId && data.id !== state.plans.otherUserCatalogId && data.currentUserId === state.user.userInfo?.id;
            }
        })
    },
    props:{
        hasSave:Boolean, //显示操作按钮
        editDir:Boolean, //弹窗编辑树
        dirId : String, //目录选中Id
        dirLevel : Number , //目录可显示层级
        curId : String ,//移动节点Id
    },
    data(){
        return {
            defaultProps : {
                value : "id" ,
                label : "name" ,
                children : "children"
            },
            treeMenu : [] ,
            addBtnTxt : '新增子目录' ,
            menu: [
                {
                    name : "新增子目录",
                    fn : this.newTreeDir ,
                    show : (right , node) => node && node.level < 5
                },{
                    name : "移动目录到",
                    fn : this.showMoveLog ,
                    show : () => true
                },
                {
                    name: '重命名目录',
                    fn: this.editNode ,
                    show : () => true
                },
                //分割线
                {
                    name : "divider" ,
                    show : () => true
                },
                {
                    name: '删除目录',
                    fn: this.deleteNode ,
                    show : () => true
                }
            ],
            sortWay : "" ,
            currentId : "",
            expandData: []
        }
    },
    methods : {
        moveTreeDir(curNode , parentNode){
            const vm = this;
            let moveToNode = curNode;
            moveToNode.pId = parentNode.id;
            this.$refs.tree.remove(curNode);
            let parent = this.$refs.tree.getNode(parentNode);
            this.$refs.tree.append(moveToNode , parent);
            this.sortTreeData(this.sortWay);
            this.expandData.push(vm.currentId);
            vm.$nextTick(()=>{
                if(vm.$refs.tree){
                    vm.$refs.tree.setCurrentKey(vm.currentId);
                    vm.nodeClick(moveToNode ,parent);
                }
            });
        },
        showMoveLog(){
            this.$emit("moveToDir" , this.selectedData , this.selectedNode);
        },
        newTreeDir(data){
            let dir = data || this.selectedData;
            this.$emit("addTreeDir" , dir);
        },
        setTree(){
            this.$emit("setTreeData",this.data);
        },
        /**
         * 移动时 过滤当前节点
         */
        filterCurNode(data){
            const vm = this , {curId} = vm;
            let result ;
            if(curId) result = data.filter(item => item.id !== curId);
            else result = data;
            return result;
        },
        /**
         * 设置 树节点数据
         * @param data
         * @param level
         * @return {*}
         */
        setTreeNode(data , level){
            const vm = this , {editDir , dirLevel ,filterCurNode , curId} = vm;
            let child_l = level + 1;
            return filterCurNode(data).map(item => {
                let disabled = curId ? level >= dirLevel : editDir && dirLevel !== undefined ? level >= dirLevel -1 : false;
                let children = item.children && item.children.length && !disabled? vm.setTreeNode(item.children , child_l) :[];
                let icon = item.dirType === 'TRANS_DIR_STANDARD' ?
                    'dg-iconp icon-journal-b' : item.dirType === 'TRANS_DIR_MF_MY' ? 'dg-iconp icon-notebook' : '';
                return {
                    id: item.id,
                    label: item.name,
                    name : item.name ,
                    icon ,
                    children,
                    isParent: true,
                    pId: item.pId === "-1" ?  "0" : item.pId,
                    dirType: item.dirType,
                    operateTime : item.operateTime,
                    level,
                    isActive : false,
                    currentUserId: item.currentUserId,//其他用户目录的用户 Id
                    disabled: item.id === vm.otherUserCatalogId,
                }
            });
        },
        /**
         * 排序树节点
         * @param val
         */
        async sortTreeData(val){
            const vm = this;
            vm.sortWay = val;
            if(val === 'time'){
                vm.data = await vm.treeSortByWay(vm.data , 'operateTime' , 'sortInTime' , true);
            }else if(val === 'letter') {
                vm.data = await vm.treeSortByWay(vm.data , 'name' , 'sortInLetter' , true);
            }
            vm.$nextTick(()=>{
                if(vm.$refs.tree){
                    vm.$refs.tree.setCurrentKey(vm.currentId);
                    vm.$emit("reSearch");
                }
            });
        },
        /**
         * 初始化树
         * @return {Promise<void>}
         */
        async initTree() {
            let parentId = '';
            const vm = this , {modelingServices , modelingMock , settings ,dirId } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            settings.loading = true;
            services.queryTransTree(parentId , '' , 'TRANS_DIR_MF' , settings).then( async res => {
                if(res.data.status === 0){
                    if(!res.data.data || res.data.data.length === 0) return ;
                    let curId , curN = res.data.data.find(no => no.dirType === "TRANS_DIR_MF_MY") , result = res.data.data;
                    if(curN) curId = curN.id;
                    if(dirId) curId = dirId;
                    vm.data = await vm.setTreeNode(result , 1);
                    vm.setExpandData(vm.data);
                    // 增、改目录过滤他人空间
                    if(vm.hasSave) {
                        vm.data = vm.data.filter(l => vm.nodeEditable(l));
                    }
                    vm.currentId = curId;
                    vm.sortTreeData("time");
                    vm.$nextTick(()=>{
                        if(vm.$refs.tree){
                            this.expandData.push(curId);
                            vm.$refs.tree.setCurrentKey(curId);
                            let currentNode = vm.$refs.tree.getCurrentNode();
                            let nodeD = vm.$refs.tree.getNode(currentNode);
                            // vm.$emit("initTable" , currentNode);
                            vm.nodeClick(currentNode ,nodeD);
                        }
                    });
                    // vm.setTree();
                }
            })

        },
        /**
         * 设置默认展开层级
         * @param {*} data 
         */
        setExpandData(data){
            this.expandData.push(...data.map(item => item.id))
        },
        nodeClick(v,i){
            this.currentId = v.id;
            this.$emit('nodeClick',v.name , v.id , i);
        },
        addChildren(event , node, object) {//新增子目录
            this.newTreeDir(object);
            // this.showMenu(node, object);
            // this.prompt('新增子目录', '', this.addTreeNode , '目录名称' , 'child' );
        },
        editNode(value) {//编辑
            this.prompt('重命名目录', this.selectedData.name, this.editTree , '目录名称' , 'child' );
        },
        /**
         * 添加目录
         * @param value
         * @param parent
         * @param id
         */
        addTreeNode(value , parent , id) {
            const vm = this;
            let pId  = parent && parent.id || vm.selectedData.id;
            let parentData = parent || vm.selectedData ,
                parentN = vm.$refs.tree.getNode(parentData);
            let operateTime = new Date().Format();
            let newTreeNode = {
                children: [],
                id ,
                isParent: false,
                name: value,
                pId: pId ,
                open :false,
                operateTime,
                currentUserId: vm.userInfo?.id
            };
            parentN.data.children.unshift(newTreeNode);
            this.sortTreeData(this.sortWay);

            vm.expandData.push(newTreeNode.id);
            vm.$nextTick(()=>{
                vm.$refs.tree.setCurrentKey(newTreeNode.id);
                let currentNode = vm.$refs.tree.getCurrentNode();
                let nodeD = vm.$refs.tree.getNode(currentNode);
                vm.nodeClick(currentNode ,nodeD);
            });
        },
        async deleteNode() {//删除
            const vm = this , {modelingServices , modelingMock  } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            let node = vm.getTreeNode() , nodeN = node.data.name;
            let currentNode = vm.$refs.tree.getCurrentNode();
            const index = node.children.findIndex(d => d.id === node.data.id);
            let flag = false;
            await services.isDeleteTransClassify(node.data.id).then(par => {
                if(par.data.status === 0 ){
                    flag = par.data.data
                }
            })
            if(flag){
                return vm.$message.warning("该目录下有模型被引用，不能删除");
            }
            vm.confirm(`确认删除\"${nodeN}\"及同步删除\"${nodeN}\"下的数据模型吗` , '删除' , ()=>{
                services.deleteTransClassify(node.data.id).then(res => {
                    if(res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        node.children.splice(index,1);
                        if(currentNode.id === node.data.id){
                            vm.$emit("initTable" , vm.selectedNode.parent.data);
                            vm.$refs.tree.setCurrentKey(vm.selectedNode.parent.data.id);
                        }
                    }
                })
            });
        },
        editTree(value) {
            const vm = this , {modelingServices , modelingMock  } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            let node = this.selectedData;
            services.updateTransClassify(node.id , value).then(res => {
                if(res.data.status === 0){
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.name = value;
                }
            })
        },
    }
}
