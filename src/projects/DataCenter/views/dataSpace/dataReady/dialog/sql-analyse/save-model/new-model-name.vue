<template>
    <div class="newModelName" v-loading="loadParams.loading">
        <el-form :model="nameForm" label-suffix=":" :rules="rules" ref="form"
                 label-width="110px">
            <el-form-item :label="label.chLabel" prop="chName">
                <el-input v-model.trim="nameForm.chName" ref="name" v-input-limit:fieldName maxlength="100"
                          :placeholder="c_placeholder"></el-input>
            </el-form-item>
            <el-form-item :label="label.engLabel" prop="engName" v-if="isNew">
                <el-input v-model.trim="nameForm.engName" v-input-limit:fieldCode
                          maxlength="100" :placeholder="e_placeholder"></el-input>
            </el-form-item>
            <el-form-item :label="label.dsTypeId" prop="dsTypeId" v-if="isNew">
                <template v-if="!loadParams.loading">
                    <dg-tree-drop
                            v-model="nameForm.dsTypeId"
                            placeholder="请选择"
                            :props="defaultProps"
                            :data="dataTypeList"
                            :tree-props="{
                                'default-expanded-keys': nameForm.dsTypeId ? [...expandKeys,nameForm.dsTypeId] : expandKeys,
                                'current-node-key': nameForm.dsTypeId,
                                'nodeKey': 'id',
                            }"
                            check-leaf
                            filterable
                            check-strictly
                            visible-type="leaf"
                    ></dg-tree-drop>
                </template>
            </el-form-item>
            <el-form-item label="保存到目录" prop="savePath" v-if="isNew">
                <div class="content__wrap">
                    <save-tree-view ref="treeView" :data="pathOpt" :expand-data="[nodeId]" @node-click="pathChange"/>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
// import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import {outputMixins} from "@/api/commonMethods/output";
import saveTreeView from "@/components/common/saveTreeView/saveTreeView";
import {mapState,mapActions} from "vuex";

export default {
    name: "NewModelName",
    mixins: [servicesMixins, outputMixins],
    props: {
        label: Object,
        loadParams: {
            type: Object,
            default:()=>{
                return {
                    loading:false
                }
            }
        }
    },
    computed: {
        ...mapState({
            dataTypeList:(state) => state.plans.dataTypeList.filter(l => l.id !== state.plans.shareId && l.id !== state.plans.otherUserCatalogId),
            myCatalog: (state) => state.plans.myCatalog,
        }),
        expandKeys(){
            return this.dataTypeList.length > 0 ? [this.dataTypeList[0].id] :[]
        }
    },
    components: {saveTreeView},
    data() {
        return {
            nodeId: "",
            pathOpt: [],
            nameForm: {
                chName: '',
                engName: '',
                savePath: '',
                dsTypeId: '',
            },
            e_placeholder: "请输入英文名(限100个字符)",
            c_placeholder: "请输入中文名(限100个字符)",
            rules: {
                chName: [
                    {required: true, message: '请输入中文名', trigger: ['blur', 'change']},
                ],
                engName: [
                    {required: true, message: '请输入英文名', trigger: ['blur', 'change']}
                ],
                dsTypeId: [
                    {required: true, message: '请选择数据来源类型', trigger: ['blur', 'change']}
                ],
                savePath: [
                    {required: true, message: '请选择位置', trigger: ['blur', 'change']},
                ],
            },
            defaultProps: {
                children: "children",
                label: "label",
                value: "id"
            },
            shareId: '6a155c5cdf9d5ddcbd8459f05b37186a',
            isNew: true
        }
    },
    methods: {
        ...mapActions(["filterOtherCatalog"]),
        pathChange(data) {
            this.nameForm.savePath = data.id;
        },
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        showSelect(nodeId, row, dsTypeId) {
            this.nodeId = nodeId || "";
            this.isNew = !row.id;
            this.nameForm.savePath = nodeId || "";
            this.setName(row);
            this.nameForm.dsTypeId = dsTypeId || "";
            if(this.isNew) this.queryDataSetTree();
        },
        setName(row) {
            this.nameForm.chName = row.name || "";
            this.nameForm.engName = row.code || "";
        },
        queryDataSetTree() {
            const vm = this, {datasetServices, datasetMock,loadParams} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            //数据集位置
            loadParams.loading = true;
            return services.queryDataSetTree(false,loadParams).then(res => {
                if (res.data.status === 0) {
                    vm.filterOtherCatalog({data: res.data.data[0].children})
                    vm.pathOpt = vm.myCatalog;
                    vm.$nextTick(() => {
                        vm.$refs.treeView.setCurrentNode(vm.nodeId);
                    })
                }
            });
        }
    },
}
</script>

<style scoped>
.content__wrap {
    padding: 14px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    box-sizing: border-box;
    height: 250px;
}
</style>
