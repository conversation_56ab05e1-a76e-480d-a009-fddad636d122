<template>
    <common-dialog
            custom-class="params-dialog"
            :title="title"
            :width="width"
            :visible.sync="visible"
            @closed="clear_data"
            v-loading="loading"
    >
        <rule-page v-if="showParams"
                   @setRowList="setRowList" :inputColumns="[]"
                   v-bind="$attrs" ref="rulePage"
                   style="height:500px;"
                   :rowData="rowData"
                   :isGlobal="isGlobal"/>
        <span slot="footer">
                <el-button @click="close" size="mini">{{ btnCancelTxt }}</el-button>
                <el-button @click="saveParams" type="primary" size="mini">{{ btnCheckTxt }}</el-button>
            </span>
    </common-dialog>
</template>
<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import RulePage from "@/projects/DataCenter/views/plugin_3/ruleEditing/RulePage";
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";

export default {
    name: "expressiongDialog",
    mixins: [commonMixins, servicesMixins, listMixins, pluginMixins],
    components: {RulePage},
    props: {
        rowData: Object,
    },
    data() {
        return {
            isGlobal: true,
        }
    },
    methods: {
        /**
         * 编辑
         */
        showExpression(row, inx) {
            this.show();
            this.title = "表达式编辑";
            this.$nextTick(() => {
                this.$refs.rulePage.init(row, inx);
            })
        },
        saveParams() {
            this.$refs.rulePage.validate();
        },
        setRowList(data, index) {
            this.$emit("getFunc", data, index);
            this.close();
        },
    }
}
</script>
