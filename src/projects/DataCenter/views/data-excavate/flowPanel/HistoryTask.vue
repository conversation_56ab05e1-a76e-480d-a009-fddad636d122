<template>
    <div>
        <common-dialog custom-class="log" :width="width" title="历史任务日志"
                   :visible.sync="dialogFormVisible"
                   @closed="clearD"
        >
            <common-table
                    height="calc(75vh - 112px)"
                    :data="tableData"
                          :columns="tHeadData"
                          :pagination="false"
                          v-loading="loadData"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <i
                            class="icon ce_link model_status"
                            v-for="(col , inx) in operateIcons"
                            :key="inx"
                            :title="col.tip"
                            v-html="col.icon"
                            @click="col.clickFn(row,$index)"
                    ></i>
                </template>

            </common-table>

        </common-dialog>
    </div>
</template>

<script>
    export default {
        name: "HistoryTask",
        data() {
            return {
                loadData  :false ,
                dialogFormVisible: false,
                width: '1200px',
                active: true,
                openDialog: false,
                jobId:1,
                rowData: {},
                tableData: [],
                tHeadData: [
                    {
                        prop: "timestamp",
                        label: "时间",
                        minWidth: "200",
                        align: "left"
                    },
                    {
                        prop: "thread_name",
                        label: "线程名",
                        minWidth: "315",
                        align: "center"
                    },
                    {
                        prop: "log-level",
                        label: "日志等级",
                        align: "center"
                    },
                    {
                        prop: "java_class",
                        label: "类",
                        width : '100',
                        align: "center"
                    },
                    {
                        prop: "line",
                        label: "行",
                        align: "center"
                    },
                    {
                        prop : "detail",
                        label :"详情",
                        align : "center",
                        width : '200',
                        resizable : false
                    },
                    {
                        prop : "message",
                        label :"详情信息",
                        align : "center"
                    },
                ],

                operateIcons: [
                    //{icon: "&#xe660;", tip: "查看历史任务日志",show : ()=> true, clickFn: this.historyTask},
                    /*  {icon: "&#xe72c;", tip: "查看历史执行日志",show : (isSparkTask)=> !isSparkTask, clickFn: this.historyExecute},*/
                     // {icon: "&#xe76b;", tip: "日志详情", clickFn: this.logDetails},
                    //{icon: "&#xe65f;", tip: "删除日志", show : ()=> true,clickFn: this.deleteLog},
                ],
                taskInfo: {},
            };
        },
        methods: {
            show(row,HistoryTableData) {
                this.taskInfo = row;
                this.dialogFormVisible = true;
                const _this = this;
                _this.loadData = false;
                _this.tableData=HistoryTableData;
            },
            // changePage(index){
            // },
            logDetails(row) {

                const _this = this;
                _this.$axios.get("/mlsql/getJumpLogUrl?jobId=" + row.jobId).then(res => {
                    if (res.data.status === 0) {
                        window.open(res.data.data, '_blank');
                    }
                })
            },
            insertStr(soure, start, newStr) {
                return soure.slice(0, start) + newStr + soure.slice(start);
            },
            clearD() {
                this.openDialog = false;
            },
        },
    }
</script>

<style scoped>
    .pagination {
        margin: 5px 0;
        text-align: center;
    }
</style>