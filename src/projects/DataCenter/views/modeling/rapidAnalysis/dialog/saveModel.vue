<template>
    <common-dialog custom-class="save" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="clearD"
                   v-loading="settings.loading"
    >
        <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="'fastAnalysisCopyTrans'"></help-document></div>
        <el-form :model="form" label-width="100px" label-position="right">
            <el-form-item :label="formList.name.label" prop="transName"
                          :rules="[{ required: true, message: '请输入模型名称', trigger: ['blur','change'] }]">
                <div class="content__name">
                    <el-input v-model.trim="form.transName" maxlength="50" v-input-limit:trim
                              @input="inputFilterSpecial($event , 'form' ,'transName')"
                              :placeholder="formList.name.placeholder"></el-input>
                    <p>{{ formList.name.tip }}</p>
                </div>
            </el-form-item>
            <el-form-item :label="formList.pos.label" required>
                <div class="content__wrap">
                    <!--目录树-->
                    <DataTreeVue class="content__tree" ref="tree" v-if="openDialog" @nodeClick="treeDir"
                                 :dirId="classifyId" modelType="QUICK_SPARK" hasSave/>
                </div>
            </el-form-item>
            <el-form-item :label="formList.description.label">
                <el-input v-model="form.description" type="textarea"
                          :placeholder="formList.description.placeholder"
                          maxlength="255"
                          rows="3"
                          resize="none"
                          show-word-limit></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button v-show="active" @click="stop" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button @click="save" type="primary" size="mini">{{ btnCheckTxt }}</el-button>
        </div>
    </common-dialog>
</template>

<script>
import {globalBus} from "@/api/globalBus";
import DataTreeVue from "@/projects/DataCenter/views/modeling/dialog/ModelTree";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";

export default {
    name: "saveModel",
    mixins: [commonMixins, servicesMixins, listMixins],
    components: {
        DataTreeVue
    },
    data() {
        return {
            visible: false,
            width: '800px',
            active: true,
            title: "",
            classifyId: "",
            form: {
                instance: "",
                mode: "",
                transName: "",
                processMode: "",
                description: ""
            },
            formList: {
                name: {
                    label: '模型名称 :',
                    placeholder: '请输入模型名称',
                    tip: "名称最大不超过50字符，只允许除/、\\、<、>等特殊字符以外。"
                },
                pos: {
                    label: "保存到目录 :"
                },
                description: {
                    label: "模型描述 :",
                    placeholder: "请输入对模型进行描述的一段说明"
                }
            },

            optInstanceData: [
                {
                    value: "运行实例",
                    label: "运行实例"
                }
            ],
            optSolveModeData: [
                {
                    value: "",
                    label: ""
                }
            ],
            rowData: {},
            isModel: false,
            openDialog: false,
            saveOther: false,
            openType:''
        }
    },
    methods: {
        treeDir(label, value) {
            this.classifyId = value;
        },
        /**
         *  @param rowData
         * @param saveInstance
         * @param saveOther
         * @param title
         * @param isModel
         * @param openType
         * */
        show(rowData, saveInstance ,  title="保存" , saveOther=false , isModel=false, openType='') {
            this.form.instance = saveInstance;
            this.openType = openType;
            this.rowData = rowData;
            this.form.transName = rowData.modelName;
            this.visible = true;
            this.openDialog = true;
            this.isModel = isModel;
            this.title = isModel ? "保存" : title ? title : "另存为";
            if (saveOther) {
                this.saveOther = saveOther;
            }
            if (rowData.dirId && rowData.dirId !== "-1") {
                this.classifyId = rowData.dirId;
            } else if (rowData.dirParentId) {
                this.classifyId = rowData.dirParentId;
            }
        },
        /**
         * 保存接口
         */
        saveFn(services, settings) {
            const vm = this;
            let data = {
                transName: this.form.transName,
                runMode: this.form.mode,
                transId: this.rowData.transId,
                instanceCode: this.form.instance,
                handleMode: this.form.processMode,
                classifyId: this.classifyId,
                dirType: "TRANS_DIR_MF",
                memo: this.form.description
            };
            services.updateTransRapid(data, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data !== "该名称已存在！") {
                        globalBus.$emit('changeName', vm.form.transName, vm.rowData.transId);
                        vm.visible = false;
                        if (vm.isModel) {
                            services.save(vm.rowData.transId).then(res => {
                                if (res.data.status === 0) {
                                    globalBus.$emit("modelSaveSuccess", vm.rowData.transId);
                                }
                            });//保存模板
                        }
                        vm.$emit("modelSaveSuccess", vm.rowData.transId);
                        vm.$message.success("保存成功！");
                    } else {
                        vm.$message.warning(res.data.data);
                    }
                }
            })
        },
        /**
         * 另存为接口
         */
        saveOtherFn(services, settings) {
            const vm = this, {rowData, classifyId, form} = vm, {transId} = rowData, {transName, description} = form;
            services.asSaveTransRapid(transId, classifyId, transName, description, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data !== "该名称已存在！") {
                        vm.visible = false;
                        const {id , name} = res.data.data;
                        vm.$message.success("保存成功！");
                        let model = {
                            dirParentId: "",
                            modelName: name,
                            modelSpec: "",
                            modelType: "",
                            releaseDate: "",
                            status: "",
                            transId: id,
                            isNew: false
                        };
                        vm.openType === "list" ? vm.$emit("close") : vm.$emit("openTab" , model);
                    }
                }
            })
        },
        save() {
            const vm = this, {rapidAnalysisServices, rapidAnalysisMock, settings, saveOther} = this;
            let services = vm.getServices(rapidAnalysisServices, rapidAnalysisMock);
            if (vm.classifyId === "") {
                vm.$message.warning("请选择方案所在目录！")
            } else if (vm.form.transName === "") {
                vm.$message.warning("请输入模型名称！")
            } else {
                settings.loading = true;
                if (saveOther) {
                    vm.saveOtherFn(services, settings);
                } else {
                    vm.saveFn(services, settings);
                }
            }
        },
        clearD() {
            this.openDialog = false;
        },
        stop() {
            this.openDialog = false;
            this.visible = false;
        },
    },
}
</script>

<style scoped lang="less">
.content {
    &__name {
        p {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 21px;
            margin-top: 4px;
        }
    }

    &__tree.dataTree {
        float: none;
        margin: 0;
        border: none;
        height: 100%;
        width: 100%;
        padding: 0;
    }

    &__wrap {
        padding: 14px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        box-sizing: border-box;
        height: 250px;
    }
}
</style>
