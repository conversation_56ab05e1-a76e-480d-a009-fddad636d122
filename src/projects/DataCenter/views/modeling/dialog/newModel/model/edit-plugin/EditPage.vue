<template>
    <div class="EditPage" v-loading="settings.loading">
        <params ref="params" v-on="$listeners" @setPreviewAble="setPreviewAble" :isCase="isCase" :previewObj="preview"
                :sourceData="nodeData" :rowData="rowData" :loadParams="settings"/>
        <el-button v-show="!settings.loading" v-footer @click="closePage">{{ btnCloseTxt }}</el-button>
        <el-button v-show="!settings.loading" v-footer v-if="nodeData.keyWord === 'cicadaFileOutputMeta'"
                   @click="downLoad">{{ downloadTxt }}
        </el-button>
        <el-button v-show="!settings.loading" v-footer v-if="nodeData.keyWord === 'cicadaScriptMeta'"
                   @click="runScript">{{ runScriptTxt }}
        </el-button>
        <el-button v-show="!settings.loading" v-footer
                   v-else-if="nodeData.keyWord !== 'ruleExecutorPlugin' && nodeData.keyWord !== 'cicadaSpecialBusinessMeta'"
                   :disabled="rowData.isEditParam ? false|| !preview.isPreviewAble : !previewObj.previewAble "
                   @click="panelPreview">{{ previewTxt }}
        </el-button>
        <el-button v-show="!settings.loading" v-footer v-if="!isCase" @click="panelSave" type="primary">{{
            saveBtnTxt
            }}
        </el-button>

    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import Params from "@/components/flowPanel/flow-plugin/flow-attr/params";
import {common} from "@/api/commonMethods/common";

export default {
    name: "EditPage",
    mixins: [commonMixins, common],
    components: {
        Params
    },
    props: {
        rowData: Object,
        nodeData: Object,
        previewObj: Object,
        isCase: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            preview: {
                isPreviewAble: true,
            }
        };
    },
    methods: {
        /**
         * 设置预览置灰
         */
        setPreviewAble(isDisabled) {
            this.previewObj.previewAble = false;
        },
        /**
         * 保存
         */
        panelSave() {
            this.$refs.params.save(this.close);
        },
        /**
         * w文件下载插件下载
         */
        downLoad() {
            this.$refs.params.downLoad();
        },
        /**
         * 预览
         */
        panelPreview() {
            const vm = this;
            const h = this.$createElement;
            let showConfirm = sessionStorage.getItem("showPreviewConfirm") ? JSON.parse(sessionStorage.getItem("showPreviewConfirm")) : true;

            if (showConfirm) {
                // eslint-disable-next-line no-inner-declarations
                function checkChange(val) {
                    noShowAgain = val;
                }

                let noShowAgain = !showConfirm;
                this.msgBox("提示",
                    h('div', null, [
                        h('p', null, '预览数据将执行保存插件信息操作，是否继续？'),
                        h('el-checkbox', {
                            props: {checked: noShowAgain},
                            on: {
                                change: (val) => checkChange(val)
                            }
                        }, '不再显示提示')
                    ]),
                    () => {
                        vm.$refs.params.save(() => {
                            vm.close();
                            vm.$refs.params.preview();
                        },false,1,2);
                        sessionStorage.setItem("showPreviewConfirm", !noShowAgain);
                    }, () => {
                    }, () => {
                    },
                    {
                        type: "warning",
                        closeOnClickModal: false,
                    })
            } else {
                vm.$refs.params.save(() => {
                    vm.close();
                    vm.$refs.params.preview();
                },false);
            }


        },
        close() {
            this.$emit("saveSuccess");
        },
        closePage() {
            this.$emit("close")
        },
        runScript() {
            this.$refs.params.runScript();
        }
    },
}
</script>

<style scoped>
.EditPage {
    height: 100%;
}
</style>
