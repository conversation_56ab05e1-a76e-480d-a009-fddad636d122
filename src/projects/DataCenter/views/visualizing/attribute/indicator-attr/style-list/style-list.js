import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

export default {
    name: "styleList" ,
    components : {
        chartStyle,
    },
    mixins :[attrStyle],
    props: {
        node: Object,
        dir_dataSetId : String
    },
    data(){
        return {
            activeTabs:["title", "legend","itemStyle" ,"fieldSetting"] ,
            styleList : {
                title :{
                    label :"标题内容",
                    settingForm :{
                        show : {
                            label : "显示标题" ,
                            type : "checkbox",
                        },
                        custom_style : {
                            label : "字体样式" ,
                            type : "sty_radio"
                        },
                        fontFamily : {
                            type : "select_font",
                            isLine : true
                        },
                        fontSize :{
                            type : "select_size" ,
                            isLine : true
                        },
                        fontWeight : {
                            type : "select_w",
                            isLine : true
                        },
                        fontStyle : {
                            type : "checkBtn" ,
                            isLine : true
                        },
                        color : {
                            type : "font_color" ,
                            isLine : true
                        },
                        align :{
                            type :"font_align" ,
                            isLine : true
                        },
                        text : {
                            type : "form_input" ,
                            pos : "right" ,
                            label : "标题名称" ,
                            maxlength :30 ,
                            width : "76px",
                            placeholder :"请輸入标题名称(限30个字符)"
                        },
                        secShow : {
                            label : "显示副标题" ,
                            type : "checkbox",
                        },
                        sec_custom_style : {
                            label : "字体样式" ,
                            type : "sty_radio"
                        },
                        secFontFamily : {
                            type : "select_font",
                            isLine : true,
                            relKey: 'sec_custom_style'
                        },
                        secFontSize :{
                            type : "select_size" ,
                            isLine : true,
                            relKey: 'sec_custom_style'
                        },
                        secFontWeight : {
                            type : "select_w",
                            isLine : true,
                            relKey: 'sec_custom_style'
                        },
                        secFontStyle : {
                            type : "checkBtn" ,
                            isLine : true,
                            relKey: 'sec_custom_style'
                        },
                        secColor : {
                            type : "font_color" ,
                            isLine : true,
                            relKey: 'sec_custom_style'
                        },
                        secAlign :{
                            type :"font_align" ,
                            isLine : true,
                            relKey: 'sec_custom_style'
                        },
                        secText : {
                            type : "form_textarea" ,
                            pos : "top" ,
                            label : "副标题名称" ,
                            maxlength :200 ,
                            rows: 4,
                            width : "76px",
                            placeholder :"请輸入副标题名称(限200个字符)"
                        },
                    },
                    form : {
                        show : true ,
                        secShow : false ,
                        custom_style : "default",
                        sec_custom_style : "default",
                        fontFamily : "sans-serif",
                        secFontFamily : "sans-serif",
                        fontSize : "12px",
                        secFontSize : "12px",
                        fontWeight : "normal",
                        secFontWeight : "normal",
                        fontStyle : [],
                        secFontStyle : [],
                        color : "#666" ,
                        secColor : "#666" ,
                        text : "" ,
                        secText:"",
                        align : "center",
                        secAlign : "center",
                    }
                },
                itemStyle :{
                    label : "指标块样式配置" ,
                    settingForm :{
                        lineItems : {
                            label : "每行最多个数" ,
                            type : "number",
                            step : 1 ,
                            min : 1 ,
                            max : 10
                        },
                        hideDimension : {
                            label : "隐藏维度",
                            type : "checkbox",
                            isLine : true
                        },
                        hideDimensionName : {
                            label : "隐藏维度名称",
                            type : "checkbox",
                            isLine : true
                        },
                        hideMainName : {
                            label : '隐藏主指标名称',
                            type : "checkbox",
                            isLine : true
                        },
                        mainColor : {
                            label : "主指标数据值颜色选择" ,
                            type : "form_color_picker"
                        },
                        tipPos : {
                            label : "指标在组合指标块位置",
                            type : "radio_btns" ,
                            align: "left",
                            options: [
                                {
                                    label: '横向居中',
                                    value: 'center'
                                }, {
                                    label: '横向居左',
                                    value: 'start'
                                }
                            ]
                        }
                    },
                    form : {
                        lineItems : 4,
                        hideDimension  : false ,
                        hideMainName : false ,
                        hideDimensionName: true ,
                        mainColor : "#555",
                        tipPos : 'center'
                    }
                },
                fieldSetting : {
                    label :"系列设置" ,
                    settingForm : {
                        alias : {
                            label : "别名" ,
                            type :"field_alias",
                            maxlength :100 ,
                            width : "120px",
                            pos :"right",
                            placeholder : "请输入别名(限100个字符)"
                        },
                        dataFix : {
                            title : '指标数据值前后缀',
                            type : "form_list" ,
                            inLine: true ,
                            list : {
                                prefix : "前缀" ,
                                suffix: "后缀"
                            }

                        }
                    },
                    form :{
                        options : [],
                        prefix : "" ,
                        suffix : ""
                    }
                }
            },
            emitStyle :{}
        }
    },
    methods :{
        getAllStyle(){
            const {emitStyle} = this;
            let styles = {};
            for(let k in emitStyle){
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        initValue(){
            const vm = this ;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData) ;
                let {title , itemStyle  , fieldSetting } = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = Object.assign({},vm.styleList.title.form,title) : vm.emitStyle.title = vm.styleList.title.form;
                itemStyle ? vm.emitStyle.itemStyle = vm.styleList.itemStyle.form = itemStyle : vm.emitStyle.itemStyle = vm.styleList.itemStyle.form;
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form ;
                vm.node.setData = false;
            })

        }
    },

}
