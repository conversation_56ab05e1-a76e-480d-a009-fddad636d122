<template>
    <div class="no-data" :style="{marginTop: top}">
        <img :src="src" />
        <p>{{title}}</p>
        <slot></slot>
    </div>
</template>
<script>
export default {
    name: 'no-data',
    props: {
        // 距离顶部的偏移量
        top: {
            type: String,
            default: '10%'
        },
        // 暂无数据预览图
        src: {
            type: String,
            default: require('@/assets/images/common/empty-img.png')
        },
        // 文案
        title: {
            type: String,
            default: '暂无数据'
        }
    }
}
</script>
<style lang="less" scoped>
.no-data{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
        width:100px;
    }
    p{
        padding-top: 12px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
}
</style>
