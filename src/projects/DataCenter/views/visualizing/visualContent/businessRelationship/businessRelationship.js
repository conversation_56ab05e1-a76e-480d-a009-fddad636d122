import {chartMxins} from "../chart-mixins/chart-mixins";
import {globalBus} from "@/api/globalBus";
import numerify from 'numerify'
import vue from "vue";
export default {
    name: "businessRelationship",
    mixins: [chartMxins],
    data() {
        return {
            graphExtend: {
                title: {
                    show:true
                },
                tooltip: {
                    appendToBody: true ,
                    position : this.tooltipPos
                },
                animationDurationUpdate: 1500,
                animationEasingUpdate: 'quinticInOut',
                legend: {
                    top:"26px",
                    show: true,
                    data: [] // 此处不显示根节点学生
                },
                series: [
                    {
                        type: 'graph',
                        layout: 'force',
                        symbolSize: function(size) {
                            return 50;
                        },
                        //是否展示边箭头
                        // edgeSymbol: ['circle', 'arrow'],
                        edgeSymbolSize: [4, 10],
                        edgeLabel: {
                            normal: {
                                show: true,//线是否展示名称
                                textStyle: {
                                    fontSize: 13
                                },
                                formatter: "{c}"
                            }
                        },
                        force: {
                            //互斥距离
                            repulsion: 50,
                            //线的大小
                            edgeLength: [30, 60]
                        },
                        //高亮点，其他暗
                        focusNodeAdjacency: true,
                        roam: true,
                        categories: [],
                        label: {
                            normal: {
                                show: false,
                                textStyle: {
                                    fontSize: 13
                                },
                            }
                        },
                        lineStyle: {
                            color: 'source',
                        },
                        emphasis: {
                            focus: 'adjacency',
                            lineStyle: {
                                width: 3
                            }
                        },
                        data: [],
                        links:[]
                    }
                ]
            },
            nodeSize:[8,25],
            loading:false,
            data_empty: false,
            nodesMax:0,
            nodeMaxSize:0,
        }
    },
    methods: {
        /**
         * tooltip位置
         */
        tooltipPos(point, params, dom, rect, size){
            let x = 0, // x坐标位置
                y = 0, // y坐标位置
                pointX = point[0],
                pointY = point[1],
                boxWidth = size.contentSize[0],
                boxHeight = size.contentSize[1],
                viewWidth = size.viewSize[0],
                viewHeight = size.viewSize[1];
            if (boxWidth > pointX) {
                x = 5;
            }else {
                x = pointX - boxWidth;
            }
            if (boxHeight > pointY) {
                y = 5;
            }else {
                y = pointY - boxHeight;
            }
            return [x , y];
        },
        getNodeData() {
            globalBus.$on('setBuisinessRelationPanel', this.setHistogramPanel);
        },
        chartResize() {
            const vm = this;
            let myChart = this.$echarts.init(this.$refs.graphChart);
            // this.nodeSize[1] = this.$refs.graphChart.offsetWidth/20;
            // this.setSymbolSize();
            // this.graphExtend.series[0].data = this.chartData.nodes;
            this.graphExtend.series[0].force.repulsion = this.$refs.graphChart.offsetWidth/8;
            this.graphExtend.series[0].force.edgeLength = this.$refs.graphChart.offsetWidth/8;
            this.graphExtend.series[0].symbolSize = function(size){
                return vm.$refs.graphChart.offsetWidth/20;
            };
            myChart.clear();
            let options = Object.assign({},this.graphExtend);
            myChart.setOption(options);
            myChart.resize();
        },
        getMessage(msg) {
            const vm = this;
            let result = JSON.parse(msg.data), data = result.data;
            if (vm.socket) vm.socket.close();
            if (result.status === 0 && data) {
                vm.chartData = data;
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                vm.set_digital_format(nodeData.xData);
                if (data.nodes.length === 0 ) vm.data_empty = true; //没有数据为true
                    else vm.data_empty = false;
                vue.nextTick(() => {
                    vm.setChartExtend(nodeData.style);
                });
                vm.loading = false;
            } else {
                vm.data_empty = true; //没有数据为true
                vm.loading = false;
                vm.chartData = {};
            }
        },
        setChartExtend(settings) {
            this.setColor(settings.legend.line_color, settings.legend.line_color_theme, settings.legend.line_color_type );
            this.setTitle(settings.title);
            this.setDataShow(settings.legend);
            this.setGraphToolTip();
            this.init();
        },
        setDataShow(legend) {
            this.graphExtend.series[0].edgeLabel.normal.show = legend.edge_show;
            this.graphExtend.series[0].label.normal.show = legend.node_show;
        },
        setTitle(title) {
            const vm = this, {graphExtend} = this;
            graphExtend.title.show = title.show;
            graphExtend.title.text = title.text;
            if (title.custom_style === 'custom') {
                let fStyle = title.fontStyle.length ? title.fontStyle[0] : "normal";
                graphExtend.title.textStyle = {
                    color: title.color,
                    fontFamily: title.fontFamily,
                    fontSize: parseInt(title.fontSize),
                    fontWeight: title.fontWeight,
                    fontStyle: fStyle
                };
                graphExtend.title.left = title.align;
                // extend.title.textStyle.fontStyle = title.fontStyle.length ? title.fontStyle[0] : true;
            } else {
                graphExtend.title.textStyle = {};
                graphExtend.title.left = 'center';
                graphExtend.title.textStyle.fontStyle = "normal";
            }
        },
        setSymbolSize(){
            const vm = this;
            this.chartData.nodes.forEach(element => {
                if (element.value > this.nodesMax) this.nodesMax = element.value;
            });
            let min = this.nodeSize[0], max = this.nodeSize[1]
            this.chartData.nodes.forEach(element => {
                element.draggable = true;
                let size = element.value === 0 ? 0 : ((element.value / this.nodesMax) * max + min);
                if (this.nodeMaxSize < size) this.nodeMaxSize = size;
                element.symbolSize = size;
            });
        },
        setGraphToolTip() {
            const vm = this;
            this.graphExtend.tooltip.formatter = function (params) {
                let result;
                if(params.dataType === "edge") {
                    let startTime = params.data.startTime 
                                    ? params.data.startTime.substring(0 ,4) + '-' + params.data.startTime.substring(4 ,6)+ '-' + params.data.startTime.substring(6) 
                                    : "",
                        endTime =   params.data.endTime 
                                    ? params.data.endTime.substring(0 ,4) + '-' + params.data.endTime.substring(4 ,6)+ '-' + params.data.endTime.substring(6)
                                    : "";
                    let startTimeString = "开始日期：" + startTime,
                        endTimeString = "结束日期：" + endTime;
                    result = `<div>\
                                <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                ${params.value }
                                </div>\
                                <div>\
                                <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;"></span>\
                                ${   startTimeString}
                                </div>\
                                <div>\
                                <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;"></span>\
                                ${   endTimeString}
                                </div>\
                                    
                            </div>`;
                }else {
                    let name = vm.chartData.nodes[params.dataIndex].name,
                        nodeCategory = params.data.category,
                        nodeTooltipName = vm.chartData.categories[nodeCategory].name;
                    let tooltipMsg = nodeTooltipName + ":" + name;
                    result = `<div>\
                                <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                ${tooltipMsg }
                                </div>\
                                    
                            </div>`;
                }
                return result;
            }
        },
        async init() {
            const vm = this;
            let myChart = vm.$echarts.init(this.$refs.graphChart);
            await vm.setLineSettings();
            let nodes = JSON.parse(JSON.stringify(this.chartData.nodes));
            nodes.forEach(element => {
                if (element.name) {
                    if(element.name.length > 3) {
                        element.name = element.name.substring(0, 3) + '...';
                    }
                }
                element.draggable = true;
            });
            vm.graphExtend.series[0].data = nodes;
            vm.chartData.links.forEach(element => {
                element.value = element.name;
            });
            vm.graphExtend.series[0].links = this.chartData.links;

            vm.graphExtend.series[0].categories = this.chartData.categories;
            vm.graphExtend.legend.data = this.chartData.categories;
            vm.chartResize();
        },
        /**
         * 设置曲线曲度
         * @param item
         * @param lines
         * @param defaultCurveness
         */
        setLineCurveness(item , lines ,defaultCurveness){
            let tsKey = item.source + '_' + item.target;
            if(!lines[tsKey]){
                lines[tsKey] = defaultCurveness;
                item.lineStyle.curveness = defaultCurveness;
            }else {
                lines[tsKey] += 0.2;
                item.lineStyle.curveness = lines[tsKey];
            }
        },
        setLinks() {
            let links = JSON.parse(JSON.stringify(this.chartData.links)),
                linksObject = {},
                linksList = [];
            links.forEach(element => {
                let lObjKey = element.source +"-" + element.target,
                    lObjValue = element.name;
                for(let key in linksObject){
                    if(key === lObjKey) lObjValue = linksObject[lObjKey] +"," + lObjValue;
                }
                linksObject[lObjKey] = lObjValue;
            });
            for(let key in linksObject){
                let arr = key.split("-");
                let linkObject = {
                    name : linksObject[key],
                    source: parseInt(arr[0]),
                    target : parseInt(arr[1])
                };
                linksList.push(linkObject);
            }
            return linksList;
        },
        setLineColor(item , relationColorMap ,colors , index , vm){
            if(!relationColorMap[item.name]){
                relationColorMap[item.name] = colors[index];
                item.lineStyle = {color : colors[index]};
                index++;
                if(index === colors.length) {
                    let newColor = vm.getRandomColor();
                    while (colors.indexOf(newColor) > -1){
                        newColor = vm.getRandomColor();
                    }
                    colors.push(newColor);
                }
            }else {
                item.lineStyle = {color : relationColorMap[item.name]};
            }
            return {colors , index}
        },
        /**
         * 配置边
         */
       async setLineSettings(){
            const vm = this ;let colors = JSON.parse(JSON.stringify(vm.colors));
            let relationColorMap = {} , index = 0;
            let lines = {} , defaultCurveness = 0.15; //配置曲线曲度
            for(let item of vm.chartData.links) {
                let result = await vm.setLineColor(item , relationColorMap ,  colors , index , vm);
                colors = result.colors; index = result.index;
                vm.setLineCurveness(item, lines ,defaultCurveness);
            }
        },
        /**
         * 获取随机颜色
         * @return {string}
         */
        getRandomColor(){
            return '#'+('00000'+(Math.random()*0x1000000<<0).toString(16)).slice(-6);
        },
        setColor(color, theme, type) {
            const vm = this;
            if (type === "theme") {
                this.graphExtend.color = this.themes[theme];
            } else if (type === "custom") {
                if (color && color.length > 0) {
                    this.graphExtend.color = color;
                } else {
                    this.graphExtend.color = [...this.colors];
                }
            }
        },
        /**
         * 预览限制
         */
        preview() {
            const vm = this;
            vm.$nextTick(()=>{
                let myChart = this.$echarts.init(this.$refs.graphChart);
                myChart.clear();
            })
            vm.data_empty = true;
            if (!vm.node.widget) {
                return;
            }
            let {beforeData, widgetDataset} = JSON.parse(vm.node.widget),
                    nodeData = beforeData ? JSON.parse(beforeData) : {};

            if (nodeData.xAxis && nodeData.xAxis.length === 0) {
                return;
            }
            if ( nodeData.yAxis && nodeData.yAxis.length === 0) {
                return;
            }
            if (!widgetDataset) {
                return;
            }
            if (!widgetDataset.tableCode) {
                return;
            }
            if (!widgetDataset.widgetDatasetDims && widgetDataset.widgetDatasetDims.length === 0) {
                return;
            }
            if (!widgetDataset.widgetDatasetMeasures && widgetDataset.widgetDatasetMeasures.length === 0) {
                return;
            }
            vm.setHistogramPanel();
        },
    },
}
