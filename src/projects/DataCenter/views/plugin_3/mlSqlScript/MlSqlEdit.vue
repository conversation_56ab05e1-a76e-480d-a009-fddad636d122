<template>
    <div>
        <div v-if="stepTableColumns.length>0" style="height: 35px">
            <el-form :inline="true">
                <el-form-item label="上一步表名：">
                            <span class="ce-tip">
                                {{stepTableName}}
                            </span>
                </el-form-item>
                <el-form-item label="上一步表字段：">
                    <el-select v-model="optionColumn" size="mini">
                        <el-option v-for="opt in stepTableColumns"
                                   :key="opt"
                                   :value="opt"
                                   :label="opt"
                        ></el-option>
                    </el-select>
                </el-form-item>

            </el-form>
        </div>
        <h1></h1>
        <AceEdit v-model="content"
                 @init="editorInit"
                 @getCursor="getCursor"
                 lang="sql"
                 :value="mlSqlScript"
                 ref="aceEdit"
                 theme="chrome"
                 :options="options"
                 autoComplete='true'
                 @setCompletions="setCompletions"
                 width="95%"
                 :height="height"
        ></AceEdit>
        <div style="margin-top: 10px">
            <common-table
                    v-if="preview.isVisible"
                    class="model_status"
                    :data="preview.list"
                    :columns="preview.columns"
                    :highlight-current-row="true"
                    :pagination="false"
            >
            </common-table>
        </div>
        <div v-if="isShowErrorMsg && !preview.isVisible"
             style="padding: 10px">
            <el-input
                    rows="6"
                    type="textarea"
                    v-model="errorMsg">

            </el-input>
        </div>
    </div>
</template>

<script>
    import AceEdit from './AceEdit';
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";

    export default {
        name: "MlSqlEdit",
        components: {
            AceEdit,
        },
        mixins : [pluginMixins , servicesMixins],
        props: {
            mlSqlScript: String,
            stepTableName: String,
            transId: String,
            stepId: String,
            stepTableSql: String,
            stepTableColumns: Array,
            loadParams : Object
        },
        data() {
            return {
                height:'calc(60vh - 130px)',
                dialogFormVisible: false,
                width: '900',
                active: true,
                oldEdit: {},
                title: "MlSql脚本编辑",
                // 分页
                openDialog: false,
                optionColumn: '',
                isSparkTask: false,
                isShowErrorMsg: false,
                errorMsg: '',
                rowData: {},
                tableData: [],
                content: "",
                resultStr: '',
                loading: false,
                options: {/*vue2-ace-editor编辑器配置自动补全等*/
                    enableBasicAutocompletion: true,
                    enableSnippets: true,
                    enableLiveAutocompletion: true/*自动补全*/
                },
                preview: {
                    isVisible: false,
                    list: [],
                    columns: [],
                },
            };
        },
        methods: {

            setCompletions(editor, session, pos, prefix, callback) {
                let dataMap = [];
                this.checkMlSql(prefix);
                this.tableData.forEach(item => {
                    let data = {
                        caption: item.name,
                        value: item.name
                    };
                    dataMap.push(data);
                });
                if (prefix.length === 0) {
                    return callback(null, [])
                } else {
                    return callback(null, dataMap)
                }
            },
            insertStr(soure, start, newStr) {
                return soure.slice(0, start) + newStr + soure.slice(start);
            },
            show(rowData) {
                this.dialogFormVisible = true;
                this.content = this.mlSqlScript;
            },

            clearD() {
                this.dialogFormVisible = false;
            },
            async save() {
                const vm = this;
                let allSql = vm.stepTableSql + vm.content;
                let sqlList = allSql.split(";");
                for (let i = 0; i < sqlList.length; i++) {
                    if (sqlList[i].indexOf("load") == -1 && sqlList[i].indexOf("connect") == -1) {
                        sqlList[i] = sqlList[i].replace(/" as "/g, " AS ").replace(/" As "/g, " AS ").replace(/" aS"/g, " AS ");
                        let strings = sqlList[i].split("AS");
                        /*if (strings.length = 1)
                            sqlList[i] = sqlList[i].replace(" AS ", ' limit 10 AS ');*/
                    }
                }
                let sql = "";
                sqlList.forEach(item => {
                    if (item.trim() != "") {
                        if (item.indexOf(";") != -1) sql += item;
                        else sql += item + ";";
                    }
                });
                await vm.$axios.post("/mlsql/runScript", {
                    "sql": sql,
                    "includeSchema": true,
                    "transStepId": vm.transId
                }
                /*, {
                    transId: vm.transId,
                    stepId: vm.stepId
                }*/
                ).then(res => {
                    vm.loading = false;
                    this.dialogFormVisible = false;
                });
                return await this.$emit('saveScript', this.content);
            },
            reSetLoading() {
                this.preview.columns = [];
                this.preview.list = [];
            },
            showData(resultStr) {
                this.reSetLoading();
                let resultData = JSON.parse(resultStr);
                const vm = this;
                vm.preview.isVisible = true;
                if (resultData) {
                    let cData = [];
                    resultData.data.forEach(res => {
                        for (let key in res) {
                            if (cData.indexOf(key) === -1) {
                                cData.push(key);
                            }
                        }
                    });
                    if (cData.length > 0) {
                        cData.forEach(col => {
                            vm.preview.columns.push({
                                label: col,
                                prop: col,
                                align: 'center',
                                minWidth: '160px'
                            })
                        });
                        let lines = resultData.data;
                        if (lines.length > 5){
                            for (let i = 0; i < 5; i++) {
                                vm.preview.list.push(lines[i]);
                            }
                        }else {
                            vm.preview.list = lines;
                        }
                    }
                }
            },
            editorInit: function (editor) {
                const vm = this;
                vm.oldEdit = editor;
                require('brace/ext/language_tools') //language extension prerequsite...
                require('brace/mode/html')
                require('brace/mode/sql')    //language
                require('brace/mode/less')
                require('brace/theme/chrome')
                require('brace/snippets/javascript') //snippet
            },
            getCursor(data) {
                const vm = this;
            },
            getMlSqlScript() {
                let vm = this;
                return vm.content;
            },
            async runScript() {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                if (this.stepTableName !== '' && this.stepTableName !== undefined && this.stepTableName !== null){
                    let sqlStr = this.content;
                    if (sqlStr!==undefined||sqlStr!==null){
                        sqlStr = sqlStr.replace(/\s/g, "");//正则去掉字符串中所有空格
                    }
                    if (sqlStr === ''||sqlStr === undefined || sqlStr === null){
                        this.content = 'select * from '+ this.stepTableName + " as table_a;";
                    }
                } else {
                    if (this.content === ''||this.content === undefined || this.content === null){
                        this.$message.warning("请输入sql脚本！");
                        return;
                    }
                }
                settings.loading = true;
                let allSql = vm.stepTableSql + vm.content;
                let sqlList = allSql.split(";");

                for (let i = 0; i < sqlList.length; i++) {
                    if (sqlList[i].indexOf("load") == -1 && sqlList[i].indexOf("connect") == -1) {
                        sqlList[i] = sqlList[i].replace(/" as "/g, " AS ").replace(/" As "/g, " AS ").replace(/" aS"/g, " AS ");
                        let strings = sqlList[i].split("AS");
                       /* if (strings.length = 1)
                            sqlList[i] = sqlList[i].replace(" AS ", ' limit 10 AS ');*/
                    }
                }
                let sql = "";
                sqlList.forEach(item => {
                    if (item.trim() != "") {
                        if (item.indexOf(";") != -1) sql += item;
                        else sql += item + ";";
                    }
                });
                await vm.$axios.post("/mlsql/runScript", {
                    "sql": sql,
                    "includeSchema": true,
                    "transStepId": vm.transId
                }, /*{
                    transId: vm.transId,
                    stepId: vm.stepId
                }*/).then(res => {
                    settings.loading = false;
                    vm.height = '245px';
                    if (res.data.data !== null && res.data.data.indexOf("error") !== -1) {
                        vm.isShowErrorMsg = true;
                        vm.preview.isVisible = false;
                        vm.errorMsg = res.data.data;
                        return this.$message.error("运行失败");
                    } else if (res.data.status === 0) {
                        vm.isShowErrorMsg = false;
                        vm.resultStr = res.data.data;
                        this.showData(vm.resultStr);
                        //vm.height = '250px';
                        return this.$message.success("运行成功");
                    } else {
                        vm.isShowErrorMsg = true;
                        vm.preview.isVisible = false;
                        vm.errorMsg = res.data.detailErrorMsg;
                        return this.$message.error("运行失败");
                    }
                });
            },
            async checkMlSql(prefix) {
                const vm = this;
                let cursor = vm.oldEdit.selection.getCursor();
                let headers = {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                }
                let transformRequest = [
                    function (data) {
                        let ret = ''
                        for (let it in data) {
                            ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
                        }
                        ret = ret.substring(0, ret.lastIndexOf('&'))
                        return ret
                    }
                ];
                let strings = vm.stepTableSql.toString().split("\n");
                await vm.$axios.post("/mlsql/complementSQL", {
                    sql: vm.stepTableSql + this.content,
                    lineNum: cursor.row + strings.length,
                    columnNum: cursor.column,
                    isDebug: false,
                    queryType: "robot"
                }, {headers: headers, transformRequest: transformRequest}).then(res => {
                    if (res.data.status === 0) {
                        vm.tableData = JSON.parse(res.data.data);
                    }
                    ;
                });
            },
        },
        computed:{
            getAceHeight:function () {
                if (!this.preview.isVisible) {
                    return '300';
                }
                return '200';
            },
        },
    }
</script>

<style scoped>

</style>