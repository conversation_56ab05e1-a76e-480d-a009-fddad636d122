/**
 * 码表 接口
 * @Author: wangjt
 * @Date: 2020-05-08
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/standermb/";

/*
*  刷新码表信息
* @param id , pId , keyword
 * @returns {Promise<*>}
* */
export async function flushCache() {
    return await reqUtil.get({
        url: root + 'flushCache',
    });
}

/*
* 获取码表 列表
* @params searchVal, currentPage, pageSize
* @returns {Promise<*>}
* */

export async function getList(params) {
    return await reqUtil.post({
        url :root + 'list'
    },params)
}

/*
* 删除
* @params id
* @returns {Promise<*>}
* */

export async function deleteData(id , value) {
    return await reqUtil.post({
        url :root + 'delete?id='+ id
    },value)
}

