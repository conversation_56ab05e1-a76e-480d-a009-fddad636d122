<template>
    <div class="rapid-analysis">
        <!-- 头部菜单区域 -->
        <header class="rapid-analysis__header">
            <div class="rapid-analysis__tabs analysis-tab">
                <div class="rapid-analysis__add" title="新建数据集" :style="{'left' : addLeft+'px'}">
                    <!--<el-popover placement="top-start" width="106" trigger="click" :visible-arrow="false" :offset="-50">-->
                        <!--<div class="rapid-analysis__add-box">-->
                            <!--<div @click="addTab({},false , dirId)">{{addModelTxt}}</div>-->
                            <!--<div>{{openModelTxt}}</div>-->
                        <!--</div>-->
                        <!--<i slot="reference" class="dg-iconp icon-l-plus"></i>-->
                    <!--</el-popover>-->
                    <i slot="reference" class="dg-iconp icon-l-plus" @click="addTab({},false , dirId)" title="新建数据集" v-if="add"></i>
                </div>
                <model-tab
                        class="ce-rapid_tab"
                        v-if="editableTabs.length"
                        v-model="editableTabsValue"
                        :editable-tabs="editableTabs"
                        @tab-click="tabClick"
                        @tab-remove="removeTab"
                >
                    <model-panel slot="tabCont" slot-scope="{item}" :rowData="item.rowData" :ref="'panel'+item.id" :selTableInfo="selTableInfo"
                                 :tabId="item.id" :type="isAdd" :dataSetId="dataSetId" :add="add" :save_dataSetId="save_dataSetId" :isShowDW="isShowDW" :leftTreeId="leftTreeId"/>
                </model-tab>
            </div>
            <span class="rapid-analysis__menus" ref="icon" @click="showMore = !showMore">
                <i class="dg-iconp icon-menu"></i>
            </span>
            <div class="rapid-analysis__more" v-show="showMore">
                <div class="rapid-analysis__item" v-for="(menu, idx) in editableTabs" :title="menu.label" :key="idx"
                     :class="{'active' : menu.id === editableTabsValue }" @click="checkTab(menu)">{{ menu.label }}</div>
            </div>
            <span class="rapid-analysis__actions">
                <el-button type="primary" size="mini" class="rapid-analysis__actions-item" @click="handleSave('')">
                    <i class="dg-iconp icon-save"></i>{{saveTip}}
                </el-button>
                <el-button size="mini" class="rapid-analysis__actions-item" @click="paramShow">
                    <!-- {{saveTip}} -->
                    {{paramTip}}
                </el-button>
                <!--<el-divider direction="vertical" v-show="!add||save_dataSetId"></el-divider>-->
                <el-button size="mini" class="rapid-analysis__actions-item" @click="handleSave('saveOther')" v-show="!add||save_dataSetId">
                    <i class="dg-iconp icon-save"></i>{{saveOTip}}
                </el-button>
                <el-divider direction="vertical"></el-divider>
                <span class="rapid-analysis__actions-item"></span>
                <!--<i class="dg-iconp icon-help"></i>-->
                 <help-document :code="!add||save_dataSetId ? 'dataSetOperationDataColumn' : 'dataSetOperationAccreditLogicDataObj'" class="mr-3"></help-document>
<!--                <i :class="['dg-iconp', !isFull ? 'icon-maximize' : 'icon-minimum']" @click="handleFull" :title="isFull?'退出全屏':'全屏'"></i>-->
                <i class="dg-iconp icon-l-close" @click="handleClose" title="关闭"></i>
            </span>
        </header>
        <GlobalVariable ref="variable" :rowData="rowData" @variableSaveSuccess='variableSaveSuccess'/>
        <save-model ref="SaveModel" @save="setSave" :dirId="dirId"></save-model>

    </div>
</template>
<script>
    import {globalBus} from "@/api/globalBus";
    import ModelPanel from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/dataTable/modelPanel"
    import SelfModelPanel from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/selfDataSet/modelPanel"
    import ModelTab from "@/projects/DataCenter/views/modeling/dialog/model-tab"
    import {tabMixins} from "@/projects/DataCenter/views/modeling/dialog/model-tab/model-tab-mixins"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import GlobalVariable from "./global-variable"
    import { mapActions, mapState,mapGetters } from "vuex";
    import saveModel from "@/projects/DataCenter/views/dataSpace/dialog/saveModel";
    export default {
        name: "dataTable",
        mixins : [tabMixins ,commonMixins,servicesMixins],
        components: {
            ModelPanel,
            ModelTab,
            SelfModelPanel,
            saveModel,
            GlobalVariable,
        },
        props: {
            dirId: String,
            type:String,
            dataSetId: String,
            add: Boolean,
            selTableInfo:Object,//用于默认选中数据表
            isShowDW:Boolean,//是否有数仓权限
            leftTreeId:String,//编辑时左侧数选择的表
        },
        data() {
            return {
                rowData : {
                    isEditParam : true,
                },
                model: "FieldChoose",
                isFull: false,
                paramTip : "参数设置",
                saveTip : "保存",
                saveOTip : "另存为",
                activeMenu: "model1",
                menus: [],
                showMore: false,
                instanceList: null,
                save_dataSetId:'',
                shareId:'6a155c5cdf9d5ddcbd8459f05b37186a',
                paramDataSetId : null,//保存后返回的iD
            };
        },
        computed: {
            ...mapState({
                plans: (state) => state.plans.plans,
                planId: (state) => state.plans.planId,
                planD : (state) => state.plans.plans.find(pl => pl.id === state.plans.planId)
            }),
            ...mapGetters(["userInfo"])
        },
        watch: {
            "editableTabs.length": {
                handler(len) {
                    let data = this.plans;
                    if (!this.isAdd) {
                        data.splice(this.planIndex, 1);
                    } else {
                        data.push(this.editableTabs[len - 1]);
                    }
                    this.setPlanId(this.editableTabsValue);
                    this.addPlan(data);
                },
                deep: true,
            },
            editableTabsValue(val) {
                this.setPlanId(val);
            },
        },
        filters: {
            filterFunctions(activeFunction, functions) {
                let fit = functions.find((item) => item.value === activeFunction);
                if (fit) {
                    return fit.label;
                } else {
                    return "";
                }
            }
        },
        mounted() {
            // 点击外部关闭更多消息
            const vm = this;
            document.addEventListener("click", (e) => {
                const dom = document.getElementsByClassName("rapid-analysis__more")[0];
                const target = vm.$refs.icon;
                if (target && target.contains(e.target)) {
                    return;
                }
                if (dom && !dom.contains(e.target)) {
                    this.showMore = false;
                }
            });

            vm.getTreeData();
        },
        methods: {
            ...mapActions(["addPlan", "setPlanId"]),

            //重写addNewTrans方法
            addNewTrans(rowData, transName) {
                this.showFlow(rowData, '', transName);
            },
            variableSaveSuccess() {
                const vm = this;
                vm.$nextTick(() =>{
                    vm.$refs['panel' + vm.planId].$refs.tableList.getVaribleInfo();
                })
            },
            paramShow() {
                let dataSetId = this.paramDataSetId || this.dataSetId ;
                // if (!dataSetId) {
                //     this.$message.warning("请先进行保存!");
                //     return;
                // }
                this.$refs.variable.init(dataSetId);
            },
            addTab(rowData , isTemplate=false , dirId ) {
                this.isAdd = true;
                let newTransName = '新建数据集_' + this.tabIndex;
                this.tabIndex ++;
                this.isTemplate = isTemplate;
                rowData.isTemplate = isTemplate;
                rowData.saved = this.saved = false;
                if (rowData.id === undefined) {
                    rowData.dirId = dirId;
                    this.addNewTrans(rowData, newTransName);
                } else {
                    rowData.saved = true;
                    newTransName=rowData.name;
                    this.showFlow(rowData, rowData.id, newTransName);
                }
                this.showAddBox = false;
            },
            //重写showFlow方法
            showFlow(rowData, new_trans_id, newTransName) {
                new_trans_id = new Date().getTime().toString();
                let tabLabel;
                tabLabel = rowData.name ? rowData.name : newTransName;
                let rowD = rowData.name ? rowData : {
                    dirParentId: "",
                    name: newTransName,
                    transId: new_trans_id,
                    isNew: true ,
                    id : "",
                    ...rowData
                };
                let newTab = {
                    id: new_trans_id,
                    label: tabLabel,
                    rowData: rowD,
                };
                let hasTab = false;
                hasTab = this.hasTabs(newTab, hasTab);//
                if (hasTab || this.editableTabs.length === 0) {
                    this.editableTabs.push(newTab);
                    this.editableTabsValue = new_trans_id;
                }
            },
            /**
             * 方案实例
             * */
            setInstance(instance){
                this.instanceList = instance;
            },
            //获取目录用于保存
            getTreeData(){
                const vm = this, {dataReadyServices, dataReadyMock, settings} = this;
                let services = vm.getServices(dataReadyServices, dataReadyMock);
                settings.loading = true;
                services.queryDataSetTree(false, settings).then(res => {
                    if (res.data.status === 0) {
                        vm.treeInfo = res.data.data[0].children ? res.data.data[0].children.filter(n=>n.id!=vm.shareId) : [];
                    }
                });
            },

            //保存
            handleSave(type){
                const {planId, plans} = this;
                const fit = plans.find((item) => item.id === planId);
                const {rowData} = fit;
                if(!type&&!rowData.id){
                    let objNode = this.$refs['panel'+this.planId].selectNode();//里层
                    if(!objNode.id){
                        return this.$message.warning('请先选择数据表！');
                    }
                }
                let validation = this.$refs['panel'+planId].validation();
                if(validation.length){
                    this.$refs.SaveModel.stop();
                    let data= validation.reduce((a,b)=>{return {name:(a.name||a.code)+'、'+(b.name||b.code)}})
                    return this.$message.warning(data.name+'未选择数据格式');
                }
                let res = this.$refs['panel'+planId].validate();
                if(!res) return;
                if (this.planId) {
                    let isShowTree=type?true:(rowData.id?false:true);
                    let label=!type&&!rowData.id?this.$refs['panel' + this.planId].$refs.leftTree.selectLeftTree.label:fit.label;
                    this.$refs.SaveModel.show(rowData, this.treeInfo,type ? true:false,label,'',isShowTree);
                }
            },
            //保存字段信息
            setSave(value,id,type){
                const vm = this , {dataReadyServices , dataReadyMock,settings,planId, plans } = this;
                let services = vm.getServices(dataReadyServices , dataReadyMock);
                settings.loading = true;
                const fit = plans.find((item) => item.id === planId);
                const {rowData} = fit;
                if(type){
                    vm.saveOther(value,id);
                    return
                }
                vm.$nextTick(()=>{
                    let objNode = vm.$refs['panel'+vm.planId].selectNode();//里层
                    let column = vm.$refs['panel'+vm.planId].column();
                    if(rowData.id) {
                        let data = {
                            column: column
                        }
                        let editId=vm.dataSetId?vm.dataSetId: rowData.id;
                        services.editColumns(data, value, editId, settings).then(res => {
                            if (res.data.status === 0) {
                                globalBus.$emit('changeName', value, vm.planId);
                                globalBus.$emit("modelSaveSuccess", vm.planId);
                                vm.filterSave(editId);
                                vm.$refs.SaveModel.stop();
                                vm.$refs['panel'+vm.planId].refreshColumn(editId);
                            }
                        })
                    }
                    else {
                        if(!objNode.id){
                            return this.$message.warning('请先选择数据表！');
                        }
                        let data = {
                            columns:column,
                            dataObjIds:[objNode.id],
                            name:value,
                          dataSetTreeNodeId:id,
                        };
                        services.saveAll(data,   settings).then(res => {
                            if (res.data.status === 0) {
                                vm.paramDataSetId = res.data.data.dataSetId;
                                vm.$refs['panel'+vm.planId].refreshColumn(res.data.data.dataSetId);
                                vm.save_dataSetId=res.data.data.dataSetId;
                                vm.isAdd=false;
                                vm.$refs['panel'+vm.planId].setTree(objNode.dataObjId);
                                vm.changeDatasetId(value , vm.planId , vm.save_dataSetId);
                                vm.filterSave(res.data.data.dataSetId);
                                vm.$refs.SaveModel.stop();
                            }
                        })
                    }
                })


            },
            /**
             * 修改数据集名称 和数据集id
             * */
            changeDatasetId(name, id ,datasetId) {
                this.triggerEvent('resize' , 300);
                this.editableTabs.forEach(item => {
                    if (item.id === id) {
                        item.label = name;
                        item.rowData.id = datasetId;
                    }
                })
            },
            //另存为
            saveOther(name,id){
                const vm = this, {dataReadyServices , dataReadyMock,settings } = this;
                settings.loading=true;
                let services = vm.getServices(dataReadyServices , dataReadyMock);
                services.createLogicDataSet(id, name).then(res => {
                    if (res.data.status === 0) {
                        let new_datasetId = res.data.data.id;
                        let params = {
                            name : name ,
                            logicDataSetId : vm.add?vm.save_dataSetId:vm.dataSetId ,
                            metaDataObjIds : vm.add?[vm.save_dataSetId]:[vm.dataSetId],
                            saveAsId :new_datasetId
                        };
                        services.saveAs(params , settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("保存成功");
                                vm.$refs.SaveModel.stop();
                            }
                        })
                    }
                })
            },
            //保存过滤信息
            filterSave(datasetId){
                const vm = this , {dataReadyServices , dataReadyMock ,settings } = this;
                let services = vm.getServices(dataReadyServices , dataReadyMock);
                let res = vm.$refs['panel'+vm.planId].validate();
                if(!res) return;
                let setParamsVo = vm.setParamsVo();
                let params = {
                    condition : JSON.stringify( setParamsVo) ,
                    dataSetId : datasetId ,
                    userId : vm.userInfo.id
                };
                settings.loading = true;
                services.filterCondition(params ,settings).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("保存成功！");
                        // vm.$refs['panel'+vm.planId].preview();
                        vm.$refs['panel'+vm.planId].filterRefresh(setParamsVo);
                    }
                });
            },
            setParamsVo(){
                const vm = this;
                let condition = vm.$refs['panel' + vm.planId].conditionInfo() , result = [];
                condition.forEach((con ,i )=> {
                    let fields = [];
                    con.children.forEach(ins => {
                        if(ins.id)
                            fields.push(ins);
                    });
                    result.push({
                        groupType:con.relation,
                        connectionType: condition.length > i + 1 ? condition[i + 1].connectionType  :  'and',
                        filterFieldInfo:fields
                    });
                });
                return result;
            },
            setRow(item){
                let row = {
                    id : "",
                    fieldName:'',
                    fieldCode:'',
                    operator:'',
                    startValue:'',
                    startOperator:'',
                    endVale:'',
                    endOperator:'',
                    inputValue:'' ,
                    format : item.format
                };
                row.fieldName = item.name;
                row.fieldCode = item.field;
                row.operator = item.operator;
                row.id = item.id;
                row.variable = item.variable;
                switch (item.format) {
                    case 'TEXT' :
                    case 'NUMBER':
                        row.inputValue = item.inputVal;
                        break;
                    case 'NUMBER':
                        row.inputValue = item.spaceVal;
                        row.startOperator = item.select1;
                        row.endOperator = item.select2;
                        row.startValue = item.spaceVal1;
                        row.endValue = item.spaceVal2;
                        break;
                    default:
                        row.inputValue = item.time;
                        row.startValue = item.timeSpace && item.timeSpace[0] ?  item.timeSpace[0] : "";
                        row.endValue =  item.timeSpace && item.timeSpace[1] ? item.timeSpace[1] : "";
                        break;
                }
                return row;

            },
            /**
             * 点击step
             */
            handleStepClick(step) {
                switch (step.value) {
                    case "xzzd":
                        this.model = "FieldChoose";
                        break;
                    case "tjgl":
                        this.model = "CondFilter";
                        break;
                }
            },
            /**
             * 点击菜单
             */
            handleTab(item) {
                this.activeMenu = item.value;
            },
            /**
             * 关闭弹窗
             */
            handleClose() {
                const vm = this;
                let tabs = vm.editableTabs.filter(p=>p.rowData.id).map(n=>n.rowData.id);
                vm.confirm("关闭" , "确认关闭该页面",()=>{
                    vm.$emit("close", tabs);
                })
            },
            /**
             * 全屏
             */
            handleFull() {
                this.isFull = !this.isFull;
                if (
                    (document.fullScreenElement !== undefined && document.fullScreenElement === null) ||
                    (document.msFullscreenElement !== undefined && document.msFullscreenElement === null) ||
                    (document.mozFullScreen !== undefined && !document.mozFullScreen) ||
                    (document.webkitIsFullScreen !== undefined && !document.webkitIsFullScreen)
                ) {
                    if (document.documentElement.requestFullScreen) {
                        document.documentElement.requestFullScreen();
                    } else if (document.documentElement.mozRequestFullScreen) {
                        document.documentElement.mozRequestFullScreen();
                    } else if (document.documentElement.webkitRequestFullScreen) {
                        document.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                    } else if (document.documentElement.msRequestFullscreen) {
                        document.documentElement.msRequestFullscreen();
                    }
                } else {
                    if (document.cancelFullScreen) {
                        document.cancelFullScreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.webkitCancelFullScreen) {
                        document.webkitCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                }
            }
        }
    };
</script>
<style lang="less" scoped>
    .menu-active {
        background: rgba(0, 136, 255, 0.08);
        border-radius: 2px;
        font-size: 14px;
        color: #0088ff;
    }
    .rapid-analysis {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        &__header {
            width: 100%;
            display: block;
            align-items: center;
            background-color: #ffffff;
            //padding: 0 16px;
            height: 40px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);
            padding-top: 5px;
            position: relative;
        }
        &__actions {
            position: absolute;
            right: 20px;
            top: 14px;
            &-item {
                cursor: pointer;
                font-size: 14px;
                //color: rgba(0, 0, 0, 0.85);
            }
            i {
                //font-size: 16px;
                //color: #000000;
                margin: 0 4px;
                cursor: pointer;
            }
        }
        &__menus {
            position: absolute;
            padding-right: 20px;
            cursor: pointer;
            user-select: none;
            left: 10px;
            top: 14px;
            z-index: 10;
            background: #fff;
            &::before {
                content: "";
                position: absolute;
                top: 50%;
                margin-top: -9px;
                right: -1px;
                height: 18px;
                width: 1px;
                background-color: rgba(0, 0, 0, 0.09);
            }
        }
        &__more {
            position: fixed;
            top: 46px;
            left: 0;
            background: #ffffff;
            border: 1px solid rgba(0, 0, 0, 0.15);
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
            border-radius: 2px;
            max-width: 260px;
            max-height: 300px;
            z-index: 8;
            overflow: auto;
            overflow-x: hidden;
            div.rapid-analysis__item {
                line-height: 36px;
                cursor: pointer;
                padding: 0 20px;
                box-sizing: border-box;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                &:hover, &.active {
                    color: #0088ff;
                }
            }
        }
        &__tabs {
            align-items: center;
            height: 100%;
            display: block;
        }
        &__add {
            padding: 0 14px;
            cursor: pointer;
            line-height: 40px;
            z-index: 1;
            position: absolute;
            transition: 200ms;
            top : 4px;
            &-box {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                div {
                    line-height: 36px;
                    cursor: pointer;
                    &:hover {
                        color: #0088ff;
                    }
                }
            }
            i {
                font-size: 16px;
            }
        }
        &__menu {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0 20px;
            cursor: pointer;
            height: 100%;
            box-sizing: border-box;
            margin-top: 1px;
            &::before {
                content: "";
                position: absolute;
                top: 50%;
                margin-top: -9px;
                right: -1px;
                height: 18px;
                width: 1px;
                background-color: rgba(0, 0, 0, 0.09);
            }
            i {
                margin-left: 4px;
            }
            &:hover {
                color: #0088ff;
            }
            &--active {
                color: #0088ff;
                background: #f5f5f5;
                border: 1px solid rgb(224, 224, 224);
                border-bottom: none;
                border-radius: 4px 4px 0 0;
            }
        }

        &__main {
            display: flex;
            margin-top: 8px;
            flex: 1;
            overflow: hidden;
        }

        &__aside {
            width: 208px;
            margin-right: 11px;
            overflow: hidden;
            /deep/.el-card__body {
                height: 100%;
                overflow: auto;
            }
            &-content {
                display: flex;
                flex-direction: column;
                overflow: hidden;
                padding: 7px 14px;
                box-sizing: border-box;
            }
        }

        &__steps {
            overflow: hidden;
        }

        &__wrap {
            overflow: hidden;
            flex: 1;
            margin-right: 14px;
        }

        &__open {
            position: fixed;
            top: 54px;
            right: 0;
            width: 45px;
            height: 120px;
            font-weight: bold;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            background: #ffffff;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
            border-radius: 2px 0 0 2px;
            padding: 13px;
            box-sizing: border-box;
            user-select: none;
            cursor: pointer;
            i {
                color: rgba(0, 0, 0, 0.45);
                font-weight: 400;
            }
        }
    }
    .sub-title {
        padding: 12px 14px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
    }
</style>
