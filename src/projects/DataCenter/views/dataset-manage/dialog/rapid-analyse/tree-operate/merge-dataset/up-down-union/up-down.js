import addDataSet from "../../../../add-dataset"
import {common} from "@/api/commonMethods/common"
import {
    servicesMixins
} from "../../../../../service-mixins/service-mixins"
import {
    commonMixins
} from "@/api/commonMethods/common-mixins"
import {
    isNull
} from "xe-utils/methods"
import {mapGetters} from "vuex";
import { indexOf } from "core-js/fn/array";
import { all } from "core-js/fn/promise";
export default {
    name: "upDown",
    mixins: [commonMixins, servicesMixins, common],
    computed : {...mapGetters(["userInfo"])},
    components: {
        addDataSet
    },
    props: {
        datasetId: String,
        isStep:Boolean
    },
    data() {
        //合并数据集-上下合并
        return {
            //当前数据集字段数
            datasetLength: "",
            union_dataSet: "union_dataSet",
            mergeUpDownData: [{
                title: "合并结果",
                del: false,
                id: 1,
                list: [
                ]
            }, ],
            currentData: [],
            unionTableData: [],
            showColumn:[],
            tableList: [],
            initDataSet: [],
            currentDataTypeId: "",
            currentCode:"",
            currentLable:"",
            unionDataTypeId: "",
            unionCode:"",
            unionLable:"",
            unionDataSetInfos:"",
            metaColumnInfos:[],
            allBackData:"",
            isAllChoose:false,
            notAllChoose:true,
            disabled:false,
            isInit:true,
            stepSubmit:false,
            stepData:"",
            addDataSetTitle:"",
            addDataSetId:"",
            isBack:false
        }

    },
    methods: {
        //合并上下 删除行
        deleteMergeData(row) {
            const vm = this
            vm.confirm("提示" , "确认删除？" , ()=> {
                let index = this.mergeUpDownData.indexOf(row);
                this.$delete(this.mergeUpDownData, index);
                vm.unionTableData = []
                vm.isBack = false  
                vm.addDataSetId = ""  
            })
            
        },
        addDataSet() {
            this.$refs.dataset.show(this.datasetId,this.addDataSetId);
        },
        editUnionTable(node) {
            const vm = this;
            vm.addDataSetId = node[0].id
            this.addDataSetTitle = node[0].name || node[0].code
            this.unionDataSetInfos = {
                id:node[0].id,
                name:node[0].name,
                code:node[0].code,
                globalCode:node[0].globalCode,
            }
            
            this.initColumnsTree(node[0].id, false).then(res => {
                this.unionTableListPush();
            })
        },
        unionTableListPush() {
            let a = [];
            for (let index = 0; index < this.datasetLength; index++) {
                a[index] = ({
                    isSelect: true,
                    iscompatible: true,
                    selectList: this.tableList,
                })
            }
            if (this.mergeUpDownData.length == 3) {
                this.mergeUpDownData.pop();
            }
            this.mergeUpDownData.push({
                id: 3,
                title: this.addDataSetTitle,
                del: true,
                list: a,
            });
            if (!this.isBack) {
                for (let index = 0; index < this.datasetLength; index++) {
                    for (let j = 0; j < this.datasetLength; j++) {
                        if (a[0].selectList[index] != undefined) {
                            if (a[0].selectList[index].code == this.mergeUpDownData[1].list[j].value) {
                                this.mergeUpDownData[2].list[j].value = a[0].selectList[index].label;
                                this.mergeUpDownData[0].list[j].value = a[0].selectList[index].label;
                                this.unionTableData[j] = {
                                    id: a[0].selectList[index].value,
                                    typeId: a[0].selectList[index].dataTypeId,
                                    code:a[0].selectList[index].code,
                                    label:a[0].selectList[index].label,
                                    name:a[0].selectList[index].label
                                };
                                this.showColumn[j] = {
                                    id:"",
                                    name:this.mergeUpDownData[0].list[j].value,
                                    code:""
                                }
                            }else {
                                this.mergeUpDownData[0].list[j].value = this.mergeUpDownData[1].list[j].value;
                                this.showColumn[j] = {
                                    id:"",
                                    name:this.mergeUpDownData[0].list[j].value,
                                    code:""
                                }
                            }
                        }
                    }
                }
            }
            
        },
        inputChange(i) {
            this.showColumn[i] = {
                id:"",
                name:this.mergeUpDownData[0].list[i].value,
                code:""
            }
        },
        selectChange(val, i, title) {
            //强制刷新
            this.$forceUpdate()
            this.getDataTypeId(val, title);
            if (title === "当前数据集") {
                this.currentData[i] = {
                    id: val.value,
                    typeId: this.currentDataTypeId,
                    code:this.currentCode,
                    // label:this.currentLable,
                    name:this.currentLable,
                };
            } else {
                this.unionTableData[i] = {
                    id: val.value,
                    typeId: this.unionDataTypeId,
                    code:this.unionCode,
                    label:this.unionLable,
                    name:this.unionLable,
                };
            }
            this.isSelectEqual(val, i);
        },
        //获得下拉框选中值dataTypeId
        getDataTypeId(val, title) {
            if (title === "当前数据集") {
                for (let index = 0; index < this.datasetLength; index++) {
                    if (val.value == this.mergeUpDownData[1].list[0].selectList[index].value) {
                        this.currentDataTypeId = this.mergeUpDownData[1].list[0].selectList[index].dataTypeId;
                        this.currentCode = this.mergeUpDownData[1].list[0].selectList[index].code;
                        this.currentLable = this.mergeUpDownData[1].list[0].selectList[index].label;
                    }
                }
            } else {
                if (this.mergeUpDownData.length == 3) {
                    for (let index = 0; index < val.selectList.length; index++) {
                        if (val.value == this.mergeUpDownData[2].list[0].selectList[index].value) {
                            this.unionDataTypeId = this.mergeUpDownData[2].list[0].selectList[index].dataTypeId;
                            this.unionCode = this.mergeUpDownData[2].list[0].selectList[index].code;
                            this.unionLable = this.mergeUpDownData[2].list[0].selectList[index].label;
                        }
                    }
                }
            }
        },
        //判断下拉框变化数据是否相等
        isSelectEqual(val, i) {
            const unionIs_exist = this.unionTableData[i] != undefined;
            const currentDataIs_exist = this.currentData[i] != undefined;
            if (unionIs_exist && currentDataIs_exist) {
                this.$axios.get("/editDataSet/checkDataType?dT1=" 
                                + this.unionTableData[i].typeId 
                                + "&dT2=" + this.currentData[i].typeId
                ).then(res => {
                    if (res.data.data) {
                        this.mergeUpDownData[1].list[i].iscompatible = true;
                        this.mergeUpDownData[2].list[i].iscompatible = true;
                    } else {
                        this.alertMessage('warning', '合并结果集对应字段类型不兼容')
                        val.iscompatible = false;
                    }
                }).catch(err=>{
                    this.loading = false;
                });
                // if (this.unionTableData[i].typeId != this.currentData[i].typeId) {
                //     this.alertMessage('warning', '合并结果集对应字段类型不兼容')
                //     val.iscompatible = false;
                // } else {
                //     this.mergeUpDownData[1].list[i].iscompatible = true;
                //     this.mergeUpDownData[2].list[i].iscompatible = true;
                // }
            }
        },
        //弹出信息
        alertMessage(action, msg) {
            this.$message({
                message: msg,
                type: action,
            });
        },
        async initColumnsTree(node, isCurrent) {
            const vm = this,
                {
                    datasetServices,
                    datasetMock
                } = this;
            let services = vm.getServices(datasetServices, datasetMock);
            vm.tableList = [];
            const {
                data: res
            } = await services.getLogicColumn("", node)
            if (res.status === 0) {
                let result = res.data;
                result.forEach(item => {
                    if (isCurrent) {
                        vm.metaColumnInfos.push({
                            name:item.name,
                            columnCode:item.code,
                            indexType:item.indexType,
                            dataTypeId:item.dataTypeId,
                            format:item.format,
                            memo:item.memo,
                            numberFormat:item.numberFormat,
                            belongParentId:item.belongParentId,
                            columnExpression:item.funcExp,
                            columnAlias:item.columnAlias
                        })
                        vm.initDataSet.push({
                            label: item.name || item.code,
                            value: item.id,
                            code: item.code,
                            dataTypeId: item.dataTypeId
                        })
                        this.datasetLength = result.length;
                    } else {
                        vm.tableList.push({
                            label: item.name || item.code,
                            value: item.id,
                            code: item.code,
                            dataTypeId: item.dataTypeId,
                        })
                    }
                })
                
            }
        },
        initCurrentDataSet() {
            const vm = this;
            let b = [];
            let unionResult = [];
            for (let index = 0; index < vm.datasetLength; index++) {
                b[index] = ({
                    isSelect: true,
                    iscompatible: true,
                    selectList: this.initDataSet,
                });
                unionResult[index] = ({
                    isSelect: false,
                    id: index + 10,
                    value: ""
                })
            }
            this.mergeUpDownData[0].list = unionResult;
            this.mergeUpDownData.push({
                id: 2,
                title: "当前数据集",
                del: false,
                list: b,
            });
            if (!this.isBack) {
                for (let index = 0; index < vm.datasetLength; index++) {
                    this.mergeUpDownData[1].list[index].value = this.initDataSet[index].label;
                    this.currentData[index] = {
                        id: this.initDataSet[index].value,
                        typeId: this.initDataSet[index].dataTypeId,
                        code:this.initDataSet[index].code,
                        // label: this.initDataSet[index].label,
                        name: this.initDataSet[index].label,
                    };
                }
            }
            
        },
        setUnionInfoJson() {
            let unionInfoJson = {
                unionShowColumn:[],
                presentColumn:[],
                unionColumn:[],
                unionDataSetInfo:"",
                metaColumnInfo:this.metaColumnInfos,
            }
            unionInfoJson.unionDataSetInfo = this.unionDataSetInfos;
            for (let index = 0; index < this.datasetLength; index++) {
                let flag = this.currentData[index] != null && this.unionTableData[index] != null && this.showColumn[index] != undefined ;
                
                let isEqual = this.mergeUpDownData[2].list[index].iscompatible && this.mergeUpDownData[1].list[index].iscompatible

                if (flag) {
                    if (this.showColumn[index].name != "") {
                        if (isEqual) {
                            unionInfoJson.unionShowColumn.push(this.showColumn[index]);
                            unionInfoJson.presentColumn.push(this.currentData[index]);
                            unionInfoJson.unionColumn.push(this.unionTableData[index]);
                        }
                        
                        this.isAllChoose = true;
                        this.notAllChoose = false;
                    }
                }else {
                    const allNull = this.currentData[index] != null || this.unionTableData[index] != null || this.showColumn[index] != undefined ;
                    if(allNull) 
                    {
                        this.notAllChoose = true;
                        unionInfoJson.unionShowColumn.splice(index,1) ;
                        unionInfoJson.presentColumn.splice(index,1);
                        unionInfoJson.unionColumn.splice(index,1);
                    }
                }
            };
            return unionInfoJson;
        },
        setUnionInfo() {
            let unionInfoJson = this.setUnionInfoJson();
            let json = {
                code: "",
                dataSetAlias: "",
                dataSetId: this.datasetId,
                id: "",
                joinType: "UNION",
                metaTargetDataSetId: unionInfoJson.unionDataSetInfo.id,
                sourceDataSetAlias: "",
                sourceDataSetCode: "",
                sourceDataSetId: this.datasetId,
                targetDataSetAlias: "",
                targetDataSetCode: unionInfoJson.unionDataSetInfo.globalCode,
                targetDataSetId:unionInfoJson.unionDataSetInfo.id,
                unionInfoJson: JSON.stringify(unionInfoJson),
                unionSql: "",
                userId: this.userInfo.id,
            }
            return json;
        },
        submit() {
            const vm = this;
            let params = vm.setUnionInfo();
            if (this.mergeUpDownData[2] === undefined) {
                this.alertMessage('warning','请选择数据集')
            }
            else{
                if(vm.isAllChoose != true ) {
                    this.alertMessage('warning','请输入完整信息')
                } else {
                    if ( vm.notAllChoose === true) {
                        this.alertMessage('warning', '信息未输入完整')
                    }
                    if (this.stepSubmit) {
                        this.stepData.result = params;
                        vm.$emit("merge" , this.stepData );
                    }else {
                        vm.$emit("merge" , params );
                    }
                }
            }
            
        },
        async initBackData() {
            const vm = this , {datasetServices , datasetMock , datasetId } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            const res = await services.getJoinAndFilter(datasetId , 'union')
                if (res.data.data != null) {
                    this.isBack = true;
                    this.allBackData = res.data.data;
                    this.datasetLength = JSON.parse(res.data.data.unionInfoJson).metaColumnInfo.length;
                    this.addDataSetTitle = JSON.parse(res.data.data.unionInfoJson).unionDataSetInfo.name
                    JSON.parse(res.data.data.unionInfoJson).metaColumnInfo.forEach(item=>{
                        vm.initDataSetPush(item);
                    })
                    this.initCurrentDataSet();
                    this.initColumnsTree(res.data.data.targetDataSetId, false);
                    this.unionTableListPush();
                } else {
                    this.init();
                }
                // if(res.data.status === 0) {
                //     let result = res.data.data;
                //     if(result){
                //         vm.metaDatasetId = result.targetDataSetId;
                //         vm.initBackData({canEdit : true , ...result});
                //     }
                // }
        },
        init() {
            this.initColumnsTree(this.datasetId, true).then(res => {
                this.initCurrentDataSet();
            })
        },
        //当前数据集字段初始化
        initDataSetPush(item) {
            const vm = this
            vm.initDataSet.push({
                label: item.name || item.columnCode,
                value: item.columnId,
                code: item.columnCode,
                dataTypeId: item.dataTypeId
            })
        },
        stepBackDataShow(step,data) {
            const vm = this
            this.addDataSetTitle = JSON.parse(data.result.unionInfoJson).unionDataSetInfo.name
            if (!step) {
                this.stepSubmit = true;
                this.stepData = data;
                vm.allBackData = data.result;
                vm.disabled = !data.canEdit;
            }
        },
         //回显
        backDataShow(){
            if (this.allBackData.unionInfoJson != "undefined") {
                this.backDataPush();
            }
        },
        backDataPush(){
            let allBackDataInfo = JSON.parse(this.allBackData.unionInfoJson);
            this.addDataSetId = this.allBackData.targetDataSetId;
            let backLength = JSON.parse(this.allBackData.unionInfoJson).presentColumn.length;
            for (let index = 0; index < backLength; index++) {
                this.mergeUpDownData[0].list[index].value = allBackDataInfo.unionShowColumn[index].name;
                this.mergeUpDownData[1].list[index].value = allBackDataInfo.presentColumn[index].name;
                this.mergeUpDownData[2].list[index].value = allBackDataInfo.unionColumn[index].name;  
                this.showColumn[index] = {
                    id:"",
                    name:allBackDataInfo.unionShowColumn[index].name,
                    code:""
                }
            }
            this.currentData = allBackDataInfo.presentColumn;
            this.unionTableData = allBackDataInfo.unionColumn;
            this.unionDataSetInfos = allBackDataInfo.unionDataSetInfo;
            this.metaColumnInfos = allBackDataInfo.metaColumnInfo;
        }
    },
    created() {
    },
    mounted() {
        this.initBackData().then(res=>{
            if (this.allBackData != null && this.allBackData !="") {
                this.backDataShow();
            }
        });
    },
}