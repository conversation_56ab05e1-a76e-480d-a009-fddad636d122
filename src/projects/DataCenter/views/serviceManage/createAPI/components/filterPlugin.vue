<template>
    <div class="ce-filter">
        <div class="cond-filter__form mb10" v-if="!atLeast&&!conditions.length">
            <label class="is-required">设置过滤条件：</label>
            <el-button type="primary" size="small" @click="handleAddGroup">添加条件</el-button>
        </div>
        <div class="ce-filter_group" v-for="(item, idx) in conditions" :key="idx">
            <div class="ce-filter_logic">
                <dg-select v-if="idx > 0" class="logic-select logic-select--thin" v-model="item.logic"
                           :data="radioList"></dg-select>
                <div v-else class="logic-select logic-select--thin logic-select--item ">全部</div>
            </div>
            <div class="ce-filter_main">
                <div class="ce-filter_header">
                    <div v-if="plugType!= 'service'">
                        <span>{{ satisfyTip }}</span>
                        <dg-select
                                class="logic-select"
                                v-model="item.childrenLogic"
                                :data="childrenLogics"
                        ></dg-select>
                        <span>{{ conTip }}</span>
                    </div>
                    <div class="ce-filter_btns">
                        <el-link type="primary" :underline="false"
                                 v-text="addCon"
                                 @click="handleAddGroup"
                                 v-if="idx === 0"
                        ></el-link>
                    </div>
                </div>

                <div class="ce-filter-item" v-for="(child, index) in item.children" :key="index+1">
                    <div class="ce-filter_form">
                        <dg-select
                                class="filter-item__field"
                                v-model="child.field"
                                :data="columns"
                                placeholder="请选择字段"
                                filterable
                                @change="selectColumn( ...arguments , child, index, idx)"
                        ></dg-select>
                        <el-select class="filter-item__operator" v-model="child.condition" @change="symbolChange($event ,child.compareOption, child)">
                            <el-option v-for="(item,index) in child.compareOption"
                                       :key="index" :label="item.tipN" :value="`${item.value}|${item.label}`">
                                <span class="ce-opt_l">{{ item.tip }}</span>
                                <span class="ce-opt_r">{{ item.tipN }}</span>
                            </el-option>
                        </el-select>
                        <!--值类型-->
                        <!--<dg-select class="ce-value_type mr6"  v-if="showIfNull(child)" v-model="child.valueType" :data="valueOpt" @change="valTypeChange($event , child)"></dg-select>-->
                        <!--值输入-->
                        <div v-if="showIfNull(child)">
                            <div v-if="child.valueType === 'constValue'">
                                <div v-if="inputText(child)">
                                    <el-input class="ce-value_input" v-model="child.value"  maxlength="30"></el-input>
                                </div>
                                <div v-else-if="inputNumber(child)">
                                    <el-input-number  class="ce-value_input" v-model.trim="child.value"  ></el-input-number>
                                </div>
                                <div v-else-if="inputDate(child)" class="filter-item__date">
                                    <el-date-picker
                                            v-model="child.value"
                                            type="date"
                                            :picker-options="getPickerOptions('' , child.valueEnd)"
                                            placeholder="选择日期时间">
                                    </el-date-picker>
                                    <span v-if="showEndValue(child)" class="p10 g5">一</span>
                                    <el-date-picker
                                            v-if="showEndValue(child)"
                                            v-model="child.valueEnd"
                                            type="date"
                                            :picker-options="getPickerOptions(child.value ,'')"
                                            placeholder="选择日期时间">
                                    </el-date-picker>
                                </div>
                            </div>
                            <div v-else-if="child.valueType === 'variable'">
                                <dg-select
                                        class="ce-value_input"
                                        filterable
                                        v-if="inputText(child) && inputNumber(child)"
                                        v-model="child.value"
                                        :data="filterColumnsByType(child.fieldType , variableColumns)"
                                ></dg-select>

                                <div v-else>
                                    <dg-select
                                            class="ce-value_input"
                                            filterable
                                            v-model="child.value"
                                            :data="filterColumnsByType(child.fieldType , variableColumns)"
                                    ></dg-select>
                                    <span v-if="showEndValue(child)" class="p10 g5">一</span>
                                    <dg-select
                                            class="ce-value_input"
                                            filterable
                                            v-if="showEndValue(child)"
                                            v-model="child.value"
                                            :data="filterColumnsByType(child.fieldType , variableColumns)"
                                    ></dg-select>
                                </div>
                            </div>
                            <div v-else>
                                <dg-select
                                        class="ce-value_input"
                                        filterable
                                        v-if="inputText(child) && inputNumber(child)"
                                        v-model="child.value"
                                        :data="filterColumnsByType(child.fieldType , columns)"
                                ></dg-select>
                                <div v-else>
                                    <dg-select
                                            class="ce-value_input"
                                            filterable
                                            v-model="child.value"
                                            :data="filterColumnsByType(child.fieldType , columns)"
                                    ></dg-select>
                                    <span v-if="showEndValue(child)" class="p10 g5">一</span>
                                    <dg-select
                                            class="ce-value_input"
                                            filterable
                                            v-if="showEndValue(child)"
                                            v-model="child.valueEnd"
                                            :data="filterColumnsByType(child.fieldType ,columns)"
                                    ></dg-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="filter-item__btns ce-icon-group">
                        <el-popconfirm
                                size="mini"
                                title="确认删除?"
                                @confirm="handleDeleteGroup(idx)"
                        >
                            <el-link
                                    slot="reference"
                                    :underline="false"
                                    type="primary"
                                    title="删除"
                                    icon="el-icon-delete"
                            ></el-link>
                        </el-popconfirm>

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    export default {
        name: "filterPlugin",
        props: {
            echoCondition: { // 回填
                type: Array,
                default: () => []
            },
            filterColumns: { //过滤条件
                type: Array,
                default: () => []
            },
            atLeast: { //至少一条条件
                type: Boolean,
                default: false
            },
            /**
             * //插件类型  plugin 条件过滤插件 ,
             *           dataset 数据集
             *           区别过滤判断符 判断字段类型的 属性参 不一致
             */
            plugType: {
                type: String,
                default: "dataset"
            },
            variableColumns: { //变量数据 参数的类型需要用的是type
                type: Array,
                default: () => []
            }
        },
        data() {
            return {
                satisfyTip: "满足以下",
                conTip: "条件",
                deleteCon: "删除条件",
                addCon: "添加条件",
                conditions: [],

                radioList: [
                    {
                        value: 'and',
                        label: '且'
                    },
                    {
                        value: 'or',
                        label: '或'
                    },
                ],
                childrenLogics: [
                    {label: "全部", value: "and"},
                    {label: "部分", value: "or"}
                ],
                valueColumns: [],
                isResouceShow:0,
                num_option: [
                    {
                        label: '等于',
                        value: '=',
                        tip: '=',
                        tipN: '等于',
                    },
                    {
                        label: '不等于',
                        value: '!=',
                        tip: '!=',
                        tipN: '不等于',
                    },
                    {
                        label: '大于',
                        value: '>',
                        tip: '>',
                        tipN: '大于',

                    }, {
                        label: '小于',
                        value: '<',
                        tip: '<',
                        tipN: '小于',
                    }, {
                        label: '大于等于',
                        value: '>=',
                        tip: '>=',
                        tipN: '大于等于',
                    }, {
                        label: '小于等于',
                        value: '<=',
                        tip: '<=',
                        tipN: '小于等于',
                    },

                ],
                time_option: [
                    {
                        label: '等于',
                        value: '=',
                        tip: '=',
                        tipN: '等于',
                    },
                    {
                        label: '不等于',
                        value: '!=',
                        tip: '!=',
                        tipN: '不等于',
                    },
                    /* {
                        label: "介于",
                        value: "in",
                        tip: "in",
                    },
                    {
                        label: "不介于",
                        value: "not in",
                        tip: "not in",
                    }, */
                    {
                        label: '大于',
                        value: '>',
                        tip: '>',
                        tipN: '大于',

                    }, {
                        label: '小于',
                        value: '<',
                        tip: '<',
                        tipN: '小于',
                    }, {
                        label: '大于等于',
                        value: '>=',
                        tip: '>=',
                        tipN: '大于等于',
                    }, {
                        label: '小于等于',
                        value: '<=',
                        tip: '<=',
                        tipN: '小于等于',
                    },

                ],
                txt_option: [
                    {
                        label: '等于',
                        value: '=',
                        tip: '=',
                        tipN: '等于',
                    },
                    {
                        label: '不等于',
                        value: '!=',
                        tip: '!=',
                        tipN: '不等于',
                    },
                    {
                        label: '大于',
                        value: '>',
                        tip: '>',
                        tipN: '大于',

                    }, {
                        label: '小于',
                        value: '<',
                        tip: '<',
                        tipN: '小于',
                    }, {
                        label: '大于等于',
                        value: '>=',
                        tip: '>=',
                        tipN: '大于等于',
                    }, {
                        label: '小于等于',
                        value: '<=',
                        tip: '<=',
                        tipN: '小于等于',
                    },
                    {
                        label: '包含',
                        value: 'like',
                        tip: 'LIKE',
                        tipN: '模糊匹配',
                    },
                    {
                        label: '不包含',
                        value: 'not like',
                        tip: 'NOT LIKE',
                        tipN: '不匹配',
                    },
                    {
                        label: '开头是',
                        value: 'like',
                        tip: 'LIKE ..%',
                        tipN: '开头是',
                    },
                    {
                        label: '开头不是',
                        value: 'not like',
                        tip: 'NOT LIKE ..%',
                        tipN: '开头不是',
                    },
                    {
                        label: '结尾是',
                        value: 'like',
                        tip: 'LIKE %..',
                        tipN: '结尾是',
                    },
                    {
                        label: '结尾不是',
                        value: 'not like',
                        tip: 'NOT LIKE %..',
                        tipN: '结尾不是',
                    },
                ],
            }
        },
        computed:{
            columns(){
                if(this.plugType === 'service'){
                    this.isResouceShow++;
                }
                return this.filterColumns
            },
            valueOpt() {
                const vm = this, {plugType} = vm;
                if (plugType === 'dataset') {
                    return [
                        {label: "常量值", value: "constValue"},
                        {label: "字段", value: "field"},
                    ]
                } else if (plugType === 'plugin') {
                    return [
                        {label: "常量值", value: "constValue"},
                        {label: "字段", value: "field"},
                        {label: "变量值", value: "variable"},
                    ]
                }else if (plugType === 'service') {
                    return [
                        {label: "常量值", value: "constValue"},
                    ]
                }
            },
        },
        watch: {
            conditions : {
                handler(val){
                    this.$emit("change" , this.conditions);
                },
                deep : true
            }
        },
        methods: {
            refreshFilter(){
                this.conditions = [];
            },
            initCondition(data){
                const vm = this , {plugType} = vm;
                if(plugType === "dataset"){
                    vm.conditions = vm.setDatasetCondition(data);
                }else if(plugType === "plugin" || plugType === "service"){
                    vm.conditions = vm.setPluginCondition(data , plugType);
                }
            },
            setPluginCondition(data ,plugType){
                const vm= this;
                data.forEach(con => {
                    con.children.forEach(child => {
                        child.isMust = con.isMust;
                        child.fieldType = vm.getValTypes(child.defaultType.toLowerCase(), plugType);
                        vm.setCompareOpt(child);
                    })
                })
                return data;
            },
            setDatasetCondition(data){
                const vm= this;
                return data.map( (cond , i) => {
                    cond.children = cond.filterFieldInfo.map(child => {
                        vm.setCompareOpt(child);
                        return child;
                    });
                    cond.childrenLogic = data[i].groupType;
                    cond.logic = i != 0 ? data[i-1].connectionType : 'and';
                    return cond;
                })
            },
            validate(){
                const vm = this , {conditions} = vm;
                let res = true;
                for(let cond of conditions){
                    res = cond.children.every(child => {
                        if(child.condValue === 'in' || child.condValue === 'not in'){
                            return child.field !== "" && child.value !== "" && child.valueEnd !== "";
                        }else if(child.condValue === 'is null' || child.condValue === 'is not null') {
                            return child.field !== "";
                        }else {
                            return child.field !== "" && child.value !== "";
                        }
                    })
                    if(!res) break;
                }
                if(!res) vm.$message.warning("请完善条件配置");
                return res;
            },
            /**
             *  值类型改变
             */
            valTypeChange(val , child){
                child.value = "";
                child.valueEnd = "";
            },
            /**
             * 按类型筛选字段
             * @param type
             * @param columns
             * @return {*}
             */
            filterColumnsByType(type , columns){
                const vm = this , {plugType} =vm;
                if(!type) return columns;
                return columns.filter(col => {
                    let valType = vm.getValTypes(vm.getColumnType(col) ,plugType);
                    return valType === type;
                })
            },
            inputText(child){
                return child.fieldType === 'text' || child.fieldType === '';
            },
            inputNumber(child){
                return child.fieldType === 'number';
            },
            inputDate(child){
                return child.fieldType === 'date';
            },
            showEndValue(child){
                return child.condValue === "between" || child.condValue === "not between";
            },
            getPickerOptions(start, end) {
                let disabledDate = (time) => {
                    if (start) {
                        return time.getTime() < new Date(start).getTime();
                    } else if (end) {
                        return time.getTime() > new Date(end).getTime();
                    }
                    return false;
                };
                return {disabledDate};
            },
            setCompareOpt(child) {
                const vm = this, { txt_option, time_option, num_option} = vm;
                child.compareOption = child.fieldType === 'text' || child.fieldType === '' ? txt_option :
                    child.fieldType === 'number' ? num_option :
                        child.fieldType === 'date' ? time_option : [];
            },
            /**
             * 为空 ，非空 判断
             * @param child
             * @return {boolean}
             */
            showIfNull(child) {
                return !(child.condValue === 'is not null' || child.condValue === 'is null');
            },
            conditionChange(condition, i) {

            },
            /**
             * 分三大类 number text date
             * @param type
             * @param plugType
             * @return {string}
             */
            getValTypes(type, plugType) {
                let valType = "";
                if (plugType === 'dataset') {
                    switch (type) {
                        case "text" :
                            valType = "text";
                            break;
                        case "number" :
                            valType = "number";
                            break;
                        default:
                            valType = "date";
                            break;
                    }
                } else if (plugType === 'plugin'|| plugType === 'service') {
                    switch (type) {
                        case "date" :
                        case "time" :
                        case "timestamp" :
                            valType = "date";
                            break;
                        case "string":
                            valType = "text";
                            break;
                        case "biginteger" :
                        case "integer" :
                        case "double" :
                        case "number" :
                            valType = "number";
                            break;
                        default:
                            break;
                    }
                }
                return valType;
            },
            /**
             * 字段 对应的类型
             * @param data
             * @return {string}
             */
            getColumnType(data , islower=true){
                const vm = this, {plugType} = vm;
                let valType;
                if (plugType === 'dataset') {
                    valType = islower ? data.format.toLowerCase() : data.format;
                } else if (plugType === 'plugin') {
                    valType = islower ? data.type.toLowerCase() :  data.type;
                }else if (plugType === 'service') {
                    valType = islower ? data.dataType.toLowerCase() : data.dataType;
                }
                return valType;
            },
            /**
             * 选择字段
             * //数据集 name label id
             * @param val
             * @param data
             * @param child
             */
            selectCascaderColumn(val, child) {
                const vm = this, {plugType} = vm;
                let data = vm.filterColumns.find(p=>p.value === val[0]).children.find(n=>n.value === val[1]);
                child.condition = "=|等于";
                child.condLabel = "等于";
                child.condValue = "=";
                child.value = "";
                child.valueEnd = "";

                child.id = data.id || "";

                child.fieldName = data.label;
                child.fieldCode = val[1];
                let valType = vm.getColumnType(data);
                child.defaultType = vm.getColumnType(data , false);
                child.fieldType = vm.getValTypes(valType, plugType);
                vm.setCompareOpt(child);
            },
            /**
             * 选择字段
             * //数据集 name label id
             * @param val
             * @param data
             * @param child
             */
            selectColumn(val, data, child) {
                const vm = this, {plugType} = vm;
                child.condition = "=|等于";
                child.condLabel = "等于";
                child.condValue = "=";
                child.value = "";
                child.valueEnd = "";

                child.id = data.id || "";

                child.fieldName = data.label;
                child.fieldCode = val;
                let valType = vm.getColumnType(data);
                child.defaultType = vm.getColumnType(data , false);
                child.fieldType = vm.getValTypes(valType, plugType);
                vm.setCompareOpt(child);
            },
            /**
             * 逻辑符号改变
             */
            symbolChange(val ,option , child){
                let data = option.find(opt => `${opt.value}|${opt.label}` === val);
                child.condLabel = data && data.label || "";
                child.condValue = data && data.value || "";
            },
            /**
             * 数据集获取过滤条件数据
             */
            getConditions(){
                const vm = this , {plugType} = vm;
                let {conditions} = vm;
                if(plugType ==="dataset"){
                    return vm.datasetGetCondition(conditions);
                }else if(plugType === "plugin" || plugType === "service") {
                    return vm.pluginGetCondition(conditions);
                }
            },
            /**
             * 数据集获取条件数据
             */
            datasetGetCondition(conditions){
                return conditions.map(cond =>{
                    cond.children = cond.children.map(child => {
                        let {value , valueType ,fieldType , condLabel ,condValue , valueEnd } = child;
                        let inputValue = value , includevalue = valueEnd;
                        if (valueType === 'constValue'  && fieldType === 'date') {
                            inputValue = value.valueOf() / 1000;
                        }
                        if(valueType === 'constValue'  && (condValue === "not in" || condValue === "in") ) {

                            inputValue = value.valueOf() / 1000;
                            includevalue = valueEnd.valueOf() / 1000;
                        }
                        child.format = child.defaultType;
                        child.variable = child.valueType;
                        child.operator = child.condLabel;
                        child.inputValue = inputValue;
                        child.includevalue = includevalue;
                        delete child.compareOption;
                        return child;
                    })
                    cond.relation = cond.childrenLogic;
                    cond.connectionType = cond.logic;
                    return cond;
                })
            },
            /**
             * 条件过滤插件获取数据
             */
            pluginGetCondition(conditions){
                return conditions;
            },
            /**
             * 添加条件组
             */
            handleAddGroup() {
                const vm = this , {txt_option} = vm;
                vm.conditions.push({
                    logic: "and",
                    childrenLogic: "and",
                    children: [
                        {
                            field: "",
                            condition: "=|等于",
                            condLabel: "等于",
                            condValue: "=",
                            value: "",
                            valueEnd: "",
                            fieldType: "",
                            valueType: "constValue",
                            defaultType : "",
                            compareOption : txt_option
                        }
                    ]
                });
            },
            /**
             * 添加子条件
             * @param children
             */
            handleAddChild(children) {
                const {txt_option} = this;
                children.push({
                    field: "",
                    condition: "=|等于",
                    condLabel: "等于",
                    condValue: "=",
                    value: "",
                    valueEnd: "",
                    fieldType: "",
                    valueType: "constValue",
                    defaultType : "",
                    compareOption : txt_option
                });
            },
            handleDeleteChild(children, index, idx) {
                const vm = this, {atLeast} = vm;
                if (atLeast && vm.conditions.length === 1 && children.length === 1) {
                    vm.$message.warning("至少需要一个条件");
                } else if (children.length === 1) {
                    vm.handleDeleteGroup(idx);
                } else {
                    children.splice(index, 1);
                }

            },
            handleDeleteGroup(idx) {
                this.conditions.splice(idx, 1);
            }
        },
        created() {
            const vm = this;
            if (vm.atLeast) {
                vm.handleAddGroup();
            }
            vm.time_option = [...vm.time_option,{
                label: "介于",
                value: "between",
                tip: "BETWEEN",
                tipN: "介于",
            }]
        },
    }
</script>

<style scoped lang="less">
    @mainColor: #0088ff;
    .ce-value_input {
        width : 220px;
    }
    .ce-value_type {width : 100px}
    .ce-opt_l {
        float: left;
    }

    .ce-opt_r {
        float: right;
        color: #8492a6;
    }

    .ce-filter {
        display: flex;
        align-items: stretch;
        flex-direction: column;
        min-width: 920px;
        &_group {
            color: @mainColor;
            display: flex;
            flex: 1;
        }

        &_logic {
            flex: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 16px;
            padding-top: 4px;

            &:after {
                flex: 1;
                content: "";
                width: 2px;
                background-color: rgba(@mainColor, 0.12);
            }
        }

        &_main {
            flex: 1;
        }

        &_header {
            display: flex;
            justify-content: space-between;

            .logic-select {
                margin: 0 12px;
            }
        }

        &_btns {
            margin-left: 14px;
            float: right;
        }
        &_form {
            display: flex;
        }
        &-item {
            margin-top: 14px;
            display: flex;
            background: #efefef;
            padding: 12px 14px;
            border-radius: 2px;
            justify-content: space-between;
        }
    }

    // 图标按钮组
    .filter-box {
        flex-grow: 1;

        /deep/ .el-card__body {
            position: relative;
            padding-top: 8px;
            padding-bottom: 0;
        }

        &__add {
            position: absolute;
            top: 8px;
            right: 32px;
            z-index: 1;
        }

    }

    .filter-item {
        display: flex;

        &__field,
        &__value {
            flex: none;
            width: 200px;
        }

        &__cascader{
            flex: none;
            width: 300px;
        }

        &__operator {
            flex: none;
            width: 130px;
            margin: 0 6px;
        }

        &__date .el-date-editor.el-input{
            flex: none;
            width: 180px;
            margin: 0 6px;
        }

        &__btns {
            margin-left: 14px;
            line-height: 1.875rem;
            .el-link {
                margin: 0 2px;
                font-size: 16px;
            }
        }

        &:not(:hover) &__btns {
            display: none;
        }
    }

    // 逻辑选择器样式
    .logic-select {
        width: 84px;

        /deep/ .el-input__inner {
            border-color: @mainColor;
            color: @mainColor;
        }

        /deep/ .el-input__icon {
            color: @mainColor;
        }

        &--item {
            background-color: rgba(@mainColor, 0.12);
        }

        &--thin {
            @thinHeight: 1.875rem;
            width: 42px;
            height:  @thinHeight;
            line-height: @thinHeight;
            text-align: center;
            /deep/ .el-input__inner {
                padding: 0 12px 0 6px;
                background-color: rgba(@mainColor, 0.12);
                border: none;
                height: @thinHeight;
                line-height: @thinHeight;
            }

            /deep/ .el-input__suffix {
                right: 4px;
            }

            /deep/ .el-input__icon {
                width: 12px;
            }

        }
    }


</style>

<style>
    .el-popper .el-cascader-node__label{
        width: 200px;
        overflow: hidden;
    }
</style>
