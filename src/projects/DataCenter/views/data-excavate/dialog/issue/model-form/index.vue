<template>
    <div class="modelForm" align="center">
        <el-form ref="form" :model="serviceInfo" label-position="right" size="mini" :rules="serviceRules" label-width="160px">
            <el-form-item label="服务中文名:"  prop="serviceChName">
                <el-input v-model.trim="serviceInfo.serviceChName"
                          :placeholder="service_p.serviceChName"
                          maxlength="50"
                          v-input-filter="serviceInfo.serviceChName"></el-input>
            </el-form-item>
            <el-form-item label="服务英文名:" prop="serviceName">
                <el-input v-model.trim="serviceInfo.serviceName"
                          :placeholder="service_p.serviceName"
                          maxlength="50"
                          @input="serviceInfo.serviceName = serviceInfo.serviceName.replace(/[^a-zA-Z]+/g,'')"
                ></el-input>
            </el-form-item>
            <el-form-item label="模型中文名:"   prop="chName">
                <el-input  v-model.trim="serviceInfo.chName"
                           :placeholder="service_p.chName"
                           maxlength="50"
                           v-input-filter="serviceInfo.chName" ></el-input>
            </el-form-item>
            <el-form-item label="模型英文名:"   prop="engName">
                <el-input  v-model.trim="serviceInfo.engName"
                           :placeholder="service_p.engName"
                           maxlength="50"
                          @input="serviceInfo.engName = serviceInfo.engName.replace(/[^a-zA-Z]+/g,'')"></el-input>
            </el-form-item>
            <el-form-item label="模型版本号:"  prop="version">
                <el-input  v-model.trim.number="serviceInfo.version"
                           v-input-filter:int="serviceInfo.version"
                           :placeholder="service_p.version"
                           maxlength="5"
                          ></el-input>
            </el-form-item>
            <el-form-item label="服务类型:"   prop="type">
                <el-select  v-model="serviceInfo.type">
                    <el-option v-for="item in serviceTypeOption"
                               :label="item.label"
                               :value="item.value"
                               :key="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="服务说明:" >
                <el-input maxlength="100" v-model="serviceInfo.explain" :placeholder="service_p.explain" type="textarea"></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script src="./model-form.js"></script>
<style scoped lang="less" src="./model-form.less"></style>