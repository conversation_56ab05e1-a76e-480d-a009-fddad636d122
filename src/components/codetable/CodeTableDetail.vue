<template>
    <div class="CodeTableDetail" v-loading="loading">
        <el-form class="ovh" size="mini" :inline="true" label-position="right" label-width="100px">
            <el-form-item
                    v-for="(item , key) in formD"
                    :key="key"
                    :label="item.label + ':'"
            >
                <div class="ell" :title="form[key]">{{form[key]}}</div>
            </el-form-item>
        </el-form>
        <common-table
                :data="tableData"
                :columns="tHeadData"
                tooltip-effect="light"
                cell-class-name="ce-ellipsis"
                size="mini"
                height="334px"
        ></common-table>
    </div>
</template>

<script>
    import CommonTable from '@/components/common/CommonTable'

    export default {
        name: "CodeTableDetail",
        components: {
            CommonTable
        },
        props: {row: Object},
        data() {
            return {
                loading :false ,
                form: {
                    cnName: '',
                    enName: '',
                    codeNum: ''
                },
                formD: {
                    cnName: {
                        label: '码表中文名'
                    },
                    enName: {
                        label: '码表英文名'
                    },
                    codeNum: {
                        label: '码值数量'
                    }
                },
                tableData: [],
                tHeadData: [
                    {
                        label: '序号',
                        type: 'index',
                        align: 'center',
                        width: '60px'
                    },
                    {
                        label: '码key',
                        prop: 'valCode',
                        align: 'center'
                    }, {
                        label: '码val',
                        prop: 'keyCode',
                        align: 'center'
                    }
                ]
            }
        },
        methods: {
            init() {
                this.loading = true;
                this.$axios.get("/standermb/details?id=" + this.row.id).then(res => {
                    if (res.data.status === 0) {
                        this.form = res.data.data;
                        this.tableData=res.data.data.list;
                    }
                    this.loading = false;
                }).catch(err=>{
                    this.loading = false;
                });
            }
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>

</style>