<template>
    <div class="model-publish">
        <el-tabs class="model-publish_tab" v-model="activeName" @tab-click="tabClick">
            <el-tab-pane v-for="(tab , inx) in tabPanes" :key="inx" :label="tab.label" :name="tab.value">
            </el-tab-pane>
        </el-tabs>
        <div class="model-publish_con">
            <component ref="tableList" class="list_cont" :is="activeName" :tabData="tabData"></component>
        </div>
    </div>
</template>

<script>
    import publishedList from "@/projects/DataCenter/views/model-market/management/model-page/publishedList";
    import releasedList from "@/projects/DataCenter/views/model-market/management/model-page/releasedList";
    import marketUse from "@store/modules/market/market-constant"
    export default {
        name: "Management",
        mixins:[marketUse],
        components: {
            publishedList,
            releasedList,
        },
        computed:{
            tabData(){
                return this.m_market_get_model_types();
            }
        },
        data() {
            return {
                activeName: "publishedList",
                tabPanes: [
                    {label: "已发布的模型", value: "publishedList"},
                    {label: "待发布的模型", value: "releasedList"},
                ],
            }
        },
        methods: {
            tabClick() {
                const vm = this, {activeName} = vm;
                vm.$nextTick(() => {
                    //vm.$refs.tableList.searchTable();
                })
            }
        },
        created() {
            this.$nextTick(() => {
                //this.$refs.tableList.show();
            })
        }
    }
</script>

<style scoped lang="less">
.model-publish{
    height: calc(100% - 12px);
    background: #fff;
    border-radius: 2px;
    box-sizing: border-box;
    padding: 10px 0 0 0;
    .list_cont{
        padding: 0 20px;
    }
    &_con{
        height: calc(100% - 54px);
    }
}
.model-publish_tab {
    &.el-tabs--top /deep/ .el-tabs__item.is-bottom:nth-child(2),
    &.el-tabs--top /deep/ .el-tabs__item.is-top:nth-child(2) {
        padding-left: 30px;
    }
}
</style>
