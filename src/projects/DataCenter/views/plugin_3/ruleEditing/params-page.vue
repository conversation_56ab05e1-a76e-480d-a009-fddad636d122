<template>
    <div class="params-page">
        <el-form
                ref="form"
                class="height100"
                :model="{ list: tableData }"
                @submit.native.prevent>
            <common-table
                    height="100%"
                    :pagination="false"
                    :data="tableData"
                    :columns="tableHead"
            >
                <template slot="header-operate" slot-scope="{row , $index}">
                    <el-button v-for="(btn , i) in operateIcon.head"
                               :key="i"
                               :title="btn.title"
                               :class="btn.icon" type="text"
                               class="dg-form-column__button" @click="btn.clickFn"></el-button>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                    <el-form-item>
                             <span v-for="(btn , i) in operateIcon.body"
                                   :key="i">
                        <el-popconfirm
                                v-if="btn.type === 'delete'"
                                size="mini"
                                title="确认删除?"
                                @confirm="btn.clickFn(row,$index)"
                        >
                            <el-button type="text"
                                       slot="reference"
                                       class="dg-form-column__button"
                                       :title="btn.title">
                                {{ btn.title }}
                            </el-button>
                        </el-popconfirm>
                         <el-button
                                 v-else
                                 type="text"
                                 class="dg-form-column__button"
                                 :title="btn.title"
                                 :class="{'show_button': rowData.isEditParam}"
                                 @click="btn.clickFn(row , $index)">{{ btn.title }}</el-button>
                    </span>
                    </el-form-item>
                </template>
                <template slot="length" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.length`"
                                  :rules="rules.length">
                        <el-input v-model.number="row.length"
                                  :maxlength="inputMax"
                                  :placeholder="tableTip.length.placeholder"
                                  v-input-limit:number
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="expressionScript" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.expressionScript`"
                                  :rules="rules.expressionScript">
                        <el-input v-model="row.expressionScript"
                                  :title="row.expressionScript"
                                  :placeholder="tableTip.expressionScript.placeholder"
                                  disabled
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="precision" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.precision`"
                                  :rules="rules.precision">
                        <el-input v-model.number.trim="row.precision"
                                  :placeholder="tableTip.precision.placeholder"
                                  :disabled="row.precisionDis"
                                  v-input-limit:number
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="columnName" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.columnName`"
                                  :rules="[...rules.columnName , ...checkSameName($index , '请勿输入相同字段中文名' , tableData , 'columnName')]">
                        <el-input v-model.trim="row.columnName"
                                  :placeholder="tableTip.columnName.placeholder"
                                  maxlength="25"
                                  v-input-limit:fieldName
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="columnCode" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.columnCode`"
                                  :rules="[...rules.columnCode , ...checkSameName($index , '请勿输入相同字段名' , tableData , 'columnCode')]">
                        <el-input v-model.trim="row.columnCode"
                                  :placeholder="tableTip.columnCode.placeholder"
                                  maxlength="25"
                                  v-input-limit:fieldCode
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="type" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.type`"
                                  :rules="rules.type">
                        <dg-select v-model="row.type"
                                   :data="types"
                                   class="width100"
                                   @change="selectChange($event , row)"
                                   :placeholder="tableTip.type.placeholder"></dg-select>
                    </el-form-item>
                </template>
            </common-table>
        </el-form>
<!--        <common-dialog
                custom-class="params-dialog"
                :title="title"
                :width="width"
                :visible.sync="visible"
                @closed="clear_data"
                v-loading="loading"
        >
            <rule-page v-if="showParams" @setRowList="setRowList" v-bind="$attrs" ref="rulePage"/>
            <span slot="footer">
                <el-button @click="close" size="mini">{{ btnCancelTxt }}</el-button>
                <el-button @click="saveParams" type="primary" size="mini">{{ btnCheckTxt }}</el-button>
            </span>
        </common-dialog>-->

    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
// import RulePage from "@/projects/DataCenter/views/plugin_3/ruleEditing/RulePage";
import {listMixins} from "@/api/commonMethods/list-mixins";

export default {
    name: "params-page",
    mixins: [pluginMixins, listMixins],
    components: {
        // RulePage
    },
    props : {
        tableH : {
            type : String ,
            default : '55vh'
        },
        rowData: Object,
    },
    data() {
        return {
            width: "1000px",
            rules: {
                length: [
                    {required: true, message: "请输入字段长度", trigger: ['blur', 'change']},
                    {type: 'number', message: '字段长度必须为数字值', trigger: ['blur', 'change']}
                ],
                precision: [
                    {required: true, message: "请输入字段精度", trigger: ['blur', 'change']},
                    {type: 'number', message: '字段精度必须为数字值', trigger: ['blur', 'change']}
                ],
                columnName: [
                    {required: true, message: "请输入字段中文名", trigger: ['blur', 'change']},
                    {
                        validator: this.checkedCommon,
                        reg: /[a-z0-9A-Z\u4e00-\u9fa5\-]+/g,
                        message: "字段中文名限制特殊字符",
                        trigger: ["blur", "change"]
                    }
                ],
                columnCode: [
                    {required: true, message: "请输入字段", trigger: ['blur', 'change']},
                    {
                        validator: this.checkedCommon,
                        reg: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                        message: "字段名允许字母数字下划线(非数字开头)",
                        trigger: ["blur", "change"]
                    }
                ],
                type: [
                    {required: true, message: "请选择字段类型", trigger: ['change']}
                ],
                expressionScript: [
                    {required: true, message: "请配置表达式", trigger: ['change']}
                ]
            },
            tableTip: {
                length: {
                    placeholder: "请输入字段长度",
                },
                precision: {
                    placeholder: "请输入字段精度"
                },
                columnName: {
                    placeholder: "请输入字段中文名"
                },
                columnCode: {
                    placeholder: "请输入字段名"
                },
                type: {
                    placeholder: "请选择字段类型"
                },
                expressionScript: {
                    placeholder: "请配置表达式"
                }

            },
            tableData: [],
            tableHead: [
                /* {
                    label: "#",
                    type: "index",
                    width:"50"
                }, */
                {
                    label: "字段名",
                    prop: "columnCode",
                    align: 'center',
                    minWidth:"140"
                }, {
                    label: "字段中文名",
                    prop: "columnName",
                    align: 'center',
                    minWidth : "140"
                },
                {
                    label: "字段类型",
                    prop: "type",
                    align: 'center',
                     minWidth:"120"
                }, {
                    label: "字段长度",
                    prop: "length",
                     minWidth:"120"
                }, {
                    label: "字段精度",
                    prop: "precision",
                    align: 'center',
                     minWidth:"120"
                }, {
                    label: "表达式",
                    prop: "expressionScript",
                    align: 'center',
                     minWidth:"100"
                },
                {
                    prop: "operate",
                    align: 'center',
                     width:"170"
                
                }
            ],
            columns: [],
            types: [],
            operateIcon: {
                head: [
                    {
                        title: "新增字段",
                        icon: "el-icon-circle-plus",
                        clickFn: this.addField
                    }
                ],
                body: [
                    {title: "表达式配置", type: "edit", icon: 'el-icon-edit', clickFn: this.editField},
                    {title: "删除", type: "delete", icon: 'el-icon-delete', clickFn: this.deleteField},
                ]
            },
            inputMax: 14
        }
    },
    computed: {},
    methods: {
        /**
         *  最大输入长度
         * */
        selectChange(data , row) {
            if (data === '4028f9fc59a545110159a5451ff1000f') {
                this.inputMax = 9;
            } else {
                this.inputMax = 14;
            }
            // 字符类型
            row.length = ['4028f9fc59a545110159a5451ff00008','4028f9fc59a545110159a5451fef0004'].includes(row.type) ? 500 : 32;
            this.setFieldVal(row);
        },
        setRowList(data, index, operator) {
            // if (data.isCommonUseOperator) {
            this.tableData[index].isCommonUseOperator = data.isCommonUseOperator;
            this.tableData[index].commonUseOperatorParams = data.commonUseOperatorParams;
            this.tableData[index].usedOperatorId = data.usedOperatorId;
            this.tableData[index].expressionScript = data.expressionScript;

            this.tableData[index].ifCustomInput = data.ifCustomInput;
            if(operator) {
                let typeId = this.types.find(t => t.label === operator.returnType)?.value;
                if(typeId) {
                    this.tableData[index].type = typeId;
                    this.selectChange(typeId, this.tableData[index]);
                }
            }
            // } else {
            //     this.tableData[index].expressionScript = data.expressionScript;
            //     this.tableData[index].isCommonUseOperator = data.isCommonUseOperator;
            // }
        },
        saveParams() {
            this.$refs.rulePage.validate();
        },
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        /**
         * 字段
         * @return {*}
         */
        getColumns() {
            const vm = this;
            return vm.columns;
        },
        /**
         * 添加
         */
        addField() {
            this.tableData.push({
                type: "",
                length: "",
                precision: "",
                columnName: "",
                columnCode: "",
                columnId: "",
                expressionScript: "",
                isCommonUseOperator: false,
                commonUseOperatorParams: "",
                usedOperatorId: "",
                precisionDis: false,
                ifCustomInput: 'false',
            })
        },
        /**
         * 配置参数 精度 控制值
         **/
        setFieldVal(data) {
            let numIds = ["4028f9fc59a545110159a5451fee0001" , "4028f9fc59a545110159a5451fef0002"]; //数值类型id
            if(numIds.indexOf(data.type) > -1){
                data.precisionDis = false;
            }else {
                data.precisionDis = true;
                data.precision = 0;
            }

        },
        /**
         * 初始化设置精度
         * */
        setAllFieldVal(){
            const vm = this;
            this.tableData.forEach(list =>{
                vm.setFieldVal(list);
            })
        },
        /**
         * 删除
         * @param row
         * @param inx
         */
        deleteField(row, inx) {
            this.tableData.splice(inx, 1);
        },
        /**
         * 编辑
         */
        editField(row, inx) {
           /*  this.show();
            this.title = "表达式编辑";
            this.$nextTick(() => {
                this.$refs.rulePage.init(row, inx);
            }) */
            const vm = this;
            let layer = this.$dgLayer({
                title: "表达式编辑",
                content: require("@/projects/DataCenter/views/plugin_3/ruleEditing/RulePage"),
                move: false,
                maxmin: false,
                resize:false,
                area: ["100%", "100%"],
                props : {
                    ...vm.$attrs,
                    rowData: vm.rowData,
                },
                on : {
                    setRowList : vm.setRowList ,
                    close (){
                        layer.close(layer.dialogIndex);
                    }
                }
            });
            layer.$children[0].init(row,inx);

        },
        /**
         * 初始化
         */
        initData(fieldTypes, inputColumns, outputColumns) {
            if (outputColumns.length) {
                this.tableData = outputColumns;
                this.setAllFieldVal();
            } else {
                this.addField();
            }
            this.columns = inputColumns;
            this.types = fieldTypes;
        },
    },
}
</script>

<style scoped lang="less">
.dg-form-column__button,
.dg-table .el-table .el-icon-circle-plus {
    margin: 0 4px;
}

.params-page {
    height: 100%;
    .el-form-item {
        //margin-bottom: 0;
    }
}
.show_button{
    pointer-events: all;
    cursor: pointer;
    opacity: 1;
}
</style>
