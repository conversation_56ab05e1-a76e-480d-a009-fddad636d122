<template>
    <div class="roles-manage ce-manage_list" v-loading="settings.loading">
        <div class="roles-header">
            <div class="ce-table_btns">
                <dg-button type="primary" v-if="rights.indexOf(addUserRight) > -1" @click="addUser">注册
                </dg-button>
            </div>
            <div class="ce-table__search">
                <el-input
                        size="mini"
                        placeholder="请输入算子名称进行搜索"
                        v-model.trim="searchVal"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'searchVal')"
                        @keyup.native.enter="isMock ? searchTableData() : changePage(1)"
                >
                    <i class="el-icon-search el-input__icon poi" slot="suffix"
                       @click="isMock ? searchTableData() : changePage(1)"></i>
                </el-input>
            </div>
        </div>
        <div class="roles-content">
            <common-table
                    :data="tableData"
                    :columns="tableHead"
                    :max-height="tableBodyH"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100 ce-table-h__auto"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ?  ()=>{} : changePage($event)"
                    @change-size="changeSize"
            >
                <template slot="operate" slot-scope="{row}">
                    <span class="r-c-action" v-for="(opt , inx) in operator" :key="inx"
                            v-if="opt.condition(row , rights)">
                        <dg-button type="text" v-html="opt.label" :title="opt.label"
                                    @click="opt.clickFn(row)"></dg-button>
                    </span>
                </template>
            </common-table>
        </div>
        <!-- 用户信息新增弹窗begin -->
      
    </div>
</template>

<script src="./operator-manage.js"></script>
<style scoped lang="less" src="./operator-manage.less"></style>
