<template>
    <div class="height100">
        <ce-list-tree
                ref="tree"
                :isEdited="()=>false"
                :treeProps="defaultProps"
                :getTreeInterface="getAPIList"
                :treeBind="{
                'expand-on-click-node' : true ,
                ...treeBind
            }"
        >
            <template slot="nodeLabel" slot-scope="{node,  data }">
                <span
                        :title="data.name"
                        :class="{
                              'el-icon-folder' : !node.expanded && data.elementOrClassify === '0' ,
                              'el-icon-folder-opened' : node.expanded && data.elementOrClassify === '0',
                              'el-icon-tickets' : data.elementOrClassify === '1'
                            }"
                        class="node-label">{{ data.name }}</span>
            </template>
        </ce-list-tree>
        <!-- <dg-tree
                v-model="vals"
                :data="data"
                node-key="id"
                :props="defaultProps"
                @node-click="handleNodeClick"
                show-checkbox
                ref="tree"
        ></dg-tree> -->
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit" >{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import CeListTree from "dc-front-plugin/src/components/CeListTree/CeListTree";
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";
import {common} from "dc-front-plugin/src/api/commonMethods/common";
    export default {
        props : {
            apiTreeInfo : Array ,
        },
        components: {CeListTree },
        mixins: [commonMixins, common],
        data() {
            return {
                treeBind: {
                    'show-checkbox' : true
                },
                vals: '',
                data: [],
                defaultProps: {
                    value : "id" ,
                    label : "name" ,
                    children : "children"
                }
            };
        },
        methods: {
            handleNodeClick(data) {
                console.log(data);
            },
            close(){
                this.$emit("close");
            },
            submit() {
                const vm = this;
                let chooseInfo = this.$refs.tree.$refs.tree.getCheckedNodes(true);
                this.$emit('submit', chooseInfo);
                vm.close();
            },
            init() {
                const vm = this;
                vm.res.transClassifyRelation.forEach(element => {
                    vm.setTree(vm.res.transClassify, element.busiClassifyId, element.elementId);
                });
                let treeInfo = [{
                    children : [],
                    id : "1",
                    name : "全选",
                }];
                vm.res.transClassify.forEach(item =>{
                    treeInfo[0].children.push(item);
                })
                vm.data = JSON.parse(JSON.stringify(treeInfo));
            },
            setTree(data, busiClassifyId, elementId) {
                const vm = this;
                data.forEach(item => {
                    if (item.id === busiClassifyId) {
                        if(!item.children) item.children = [];
                        let childrenInfo = vm.res.transMetas.filter(e => e.id === elementId);
                        item.children.push(childrenInfo[0]);
                    }
                    if (item.children && item.children.length > 0) vm.setTree(item.children, busiClassifyId, elementId)
                })
            },
            getAPIList(settings) {
                const vm =  this;
                settings.loading = false;
                return new Promise((resolve) => {
                    // @ts-ignore
                    resolve(vm.apiTreeInfo)
                })
            }
        },
        created() {
            // this.init();
            this.data = this.apiTreeInfo;
        }
    };
</script>