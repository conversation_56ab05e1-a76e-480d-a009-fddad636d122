<template>
    <tree-and-list :sizeLeft="15.8" draggable>
        <tree-card ref="treeCard" slot="left" :card-name="cardName" @filterData="filterTree" :class="{'disabled':listLoad}">
            <i slot="add" :title="newFile" class="el-icon-folder-add poi" @click="showAddDialog()"></i>
            <el-popover slot="sort" popper-class="ce-popover" v-model="showAddBox" trigger="click" width="96">
                <div class="ce-tree__pop-box">
                    <div class="ce-tree__pop-item" v-for="(menu , k) in sortMenu"
                         :class="{'active' : k === sortVal}"
                         @click="setTreeSort(k)" :key="k">
                        <i class="eda_icon" :class="menu.icon"></i>
                        <span>{{ menu.label }}</span>
                    </div>
                </div>
                <i slot="reference" class="eda_icon ce-sort_icon"
                   :title="sortVal === 'letter'?letterSort : timeSort"
                   :class="{'iconsort' : sortVal === 'letter' , 'iconshijianpaixutubiao' : sortVal === 'time' }"></i>
            </el-popover>
            <tree
                    slot="tree"
                    ref="dataTree"
                    :hasSave="false"
                    @reSearch="reSearch"
                    @addTreeDir="showAddDialog"
                    @setTreeData="setTreeData"
                    @nodeClick="treeDir"
                    @initTable="initTable"
                    @moveToDir="moveToDir"
            ></tree>
        </tree-card>

        <list ref="modelingCenterTable"
              :renewListLoad="renewListLoad"
              @openNewModel="openNewModel"
              @reName="rename"
              @moveTo="moveTo"
              @modelEdit="modelEdit"
              @previewResult="previewResult"
              @share="shareFn"
              :isCase="isCase"
              @serveIssue="serveIssue"
              @saveAs="saveAs"
        >
        </list>
        <div slot="other">
            <!--建模模板页-->
            <NewModel v-if="newModel" @backTo="backTo" @newModel="addModelTab" @addModule="addModule"/>
            <!--建模页-->
            <ModelTabs ref="modelPage" :treeData="treeData" v-if="modelTabs" @backTo="backTo" @closeTab="backTo"
                       @openNewModel="openNewModel"/>
            <!--重命名-->
            <RenameDailog ref="refRenameDialog" @rename="dialogRenameEmit"/>
            <!--移动-->
            <OpenModel ref="move" @update="update"/>
            <!--预览-->
            <preview ref="preview"/>
            <!--分享-->
            <share ref="share"/>
            <!--服务发布-->
            <issue ref="issue"></issue>
            <!--新建树 目录-->
            <add-tree-dir ref="treeDir" v-loading="settings.loading" @addTreeDir="addTreeDir" @moveTreeDir="moveTreeDir" @filterData="filterEditTree" >
                <tree slot="tree" ref="editTree" slot-scope="scope" v-bind="scope" @nodeClick="nodeClick" />
            </add-tree-dir>
            <!-- 另存为 -->
            <SaveModel ref="SaveModel" @saveSuccess="closeSave"  />
        </div>
    </tree-and-list>
</template>
<script src="./modeling.js">
</script>
<style scoped lang="less"></style>
