import ModelingCenterTable from "./operate/ModelCenterTable";
import PreviewSparkTable from './operate/PreviewSparkTable';
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins";
import {common} from "@/api/commonMethods/common"
export default {
    name: "sqlAnalyse" ,
    components : {
        ModelingCenterTable ,
        PreviewSparkTable
    },
    mixins : [ commonMixins , servicesMixins , common],
    props: {
        rowData: Object,
    },
    data(){
        return {
            schemaName:"",
            schemaId:"",
            tableSize: 10,
            loading: false,
            preview: {
                isVisible: false,
                reNew :false ,
                list: [],
                columns: [],
            },
            isExist: false,
            info: "",
            dialogVisible: false,
            inputValueTree: "",
            textarea: "",
            btns: [
                {
                    txt: '返回',
                    type: '',
                    fn: this.backFn
                }
            ],
            title: "",
            width :"760px" ,
            spark_dialog : {
                reNew : false ,
                visible :false
            },
            maxlength : 100 ,
            showTxt : true ,
        }
    },
    methods: {
        toRead(){
            this.showTxt  = true;
        },
        toEdit(){
            const vm = this;
            vm.showTxt  = false;
            vm.$nextTick(()=>{
                vm.$refs.name_input.focus();
            })
        },
        clear_dialog(){
            const vm = this;
            vm.spark_dialog.reNew = false;
        },
        changeNum() {
            let strs = this.tableSize.match(/./g);
            strs.forEach(item => {
                if (isNaN(item)) {
                    this.tableSize = this.tableSize.replace(item, '');
                }
            })


        },
        query() {
            let sql = "";
            if (this.textarea.indexOf("limit") > -1) {
                sql = this.textarea.substring(0, this.textarea.indexOf("limit"));
            } else {
                sql = this.textarea;
            }
            this.btnPreviewEvent(sql);
        },
        save() {
            const vm = this , {datasetServices , datasetMock ,textarea ,rowData ,title ,settings } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;
            services.sqlSave(title , rowData.id , textarea ,settings).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("保存成功！");
                }
            });
        },
        titleChange(val) {
            this.$refs.title.style.minWidth = val.length * 16 + 'px';
            if (val.length >= 30) {
                this.$message.info('标题限30个字符以内!')
            }
        },
        backFn() {
            this.$emit('closePlane');
        },
        handleSizeChange(val) {
            this.$refs.modelingCenterTable.refPageChange(val);
            //加载数据
        },
        handleCurrentChange(val) {
            this.$refs.modelingCenterTable.refPageChange(val);
            //加载数据
        },
        searchTreeEvent() {
            this.$message("tree");
        },
        changeTableData(vals) {
            vals.sqlDataObjColumns.forEach((item, i) => {
                let index = 1;
                vals.sqlDataObjColumns.forEach((val, j) => {
                    if (item.colName === val.colName && i !== j) {
                        val.colName += index++;
                    }
                })
            });
            this.$refs.modelingCenterTable.setTableData(vals);
        },
        btnAnalyze() {
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            if (vm.textarea === null || vm.textarea === '') {
                vm.$message.warning("请先输入SQL语句！");
                return;
            }
            services.sqlColumns( vm.textarea,this.schemaId ).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result === null) {
                        vm.$message.warning("查询数据为空，可能连接出现异常！");
                    } else {
                        if (vm.checkCol(result)) {
                            vm.$message.warning("分析结果中存在相同字段名称，请使用别名区分！");
                        } else {
                            vm.$refs.modelingCenterTable.setTableData(res.data.data)
                        }
                    }
                }
            })
        },
        btnDataSetEvent() {
            this.spark_dialog.visible = true;
            this.spark_dialog.reNew = true;
            // this.$refs.previewTable.getSparkTable(this.schemaId);
        },
        btnPreviewEvent(val) {
            const vm = this , {datasetServices , datasetMock ,settings , tableSize } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            if (val === null || val === "") {
                vm.$message.warning("请输入SQL语句！");
                return;
            }else if(tableSize > 1000){
                vm.$message.warning("预览数据量不允许超过1000");
                return;
            }
            settings.loading = true;
            vm.preview.isVisible = true;
            vm.preview.reNew = true;
            services.sqlPreview(tableSize ,val , settings,this.schemaId).then(res => {
                if (res.data.status === 0) {
                    let headData = res.data.data.fieldName;//集合
                    let valueDat = res.data.data.fieldValue;//集合
                    if (res.data.data !== null) {
                        vm.preview.columns = [];
                        vm.tableSize = valueDat.length;
                        for (let key in headData) {
                            vm.preview.columns.push({
                                prop: key,
                                label: key,
                                minWidth : key.length < 6 ? 120 : key.length * 16 + 32
                            });
                        }
                        vm.preview.list = valueDat;
                        /*// 渲染表头数据
                        vm.preview.columns = [];
                        // vm.tableSize = resData.length;
                        for (let key in resData[0]) {
                            vm.preview.columns.push({
                                prop: key,
                                label: key,
                                minWidth : key.length < 6 ? 120 : key.length * 16 + 32
                            });
                        }
                        vm.preview.list = [];
                        //渲染表格数据
                        resData.forEach(item => {
                            vm.preview.list.push(item);
                        });*/
                    }
                }
            })

        },
        checkCol(vals) {
            let hash = {};
            let flag = false;
            vals.forEach(item => {
                if (hash[item.name] === undefined) {
                    hash[item.name] = item.name
                } else {
                    flag = true;
                }
            });
            return flag;
        },
        btnSaveEvent() {
            const vm = this;
            if (this.textarea.trim() === "") {
                this.$message.warning("请输入SQL语句！");
                return;
            }
            vm.confirm("保存", "是否保存?" , ()=> {
                vm.save();
            })
        },
        getTableHeight() {
            //if(!this.$refs.getTableH) return ;
            this.$nextTick(() => {
                let tableHeight = this.$refs.getTableH.clientHeight;
                this.$refs.modelingCenterTable.resizeTableHeight(tableHeight);
            })

        },
        countHeight() {
            let $this = this;
            let resizeTimer = null;
            if (resizeTimer) clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                $this.getTableHeight();
            }, 300)
        },
        reSetLoading() {
            this.reNew = false;
            this.preview.columns = [];
            this.preview.list = [];
            this.tableSize = 10;
            this.settings.loading = false;
        },
        editSqlPage() {
            const vm = this , {datasetServices , datasetMock  } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            services.sqlQuery(vm.rowData.id).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.schemaId = result.schemaId;
                    vm.schemaName = result.schemaName;
                    if(result.sql){
                        vm.textarea = result.sql;
                        vm.btnAnalyze();
                    }
                }
            })
        },
    },
    created() {
        this.title = this.rowData.name;
        this.editSqlPage();
    },
    mounted() {
        this.getTableHeight();
        window.addEventListener(
            "resize", this.countHeight, false
        );
    },
    beforeDestroy() {
        //解绑 resize
        window.removeEventListener("resize", this.countHeight);
    },
}