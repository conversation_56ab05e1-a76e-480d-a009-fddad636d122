export default {
    name: "<PERSON><PERSON>nd<PERSON><PERSON>" ,
    props :{
        leftWidth: {
            type: [Number , String],
            default: 'auto'
        }
    },
    data(){
        return {

        }
    },
    computed: {

        widthPx() {
            const {leftWidth} = this;
            if (leftWidth > 0) {
                return `${leftWidth}px`
            }else if(typeof leftWidth === 'string' ){
                return leftWidth;
            } else {
                return 0;
            }
        }
    }
}