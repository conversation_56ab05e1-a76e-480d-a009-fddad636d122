<template>
    <div class="modelNode node selectn"
         ref="node"
         :style="flowNodeContainer"
         @mouseenter="showDelete"
         @mouseleave="hideDelete"
         @mouseup="changeNodeSite"

    >
        <div class="flow-shadow">
            <div class="flow-node-header" @contextmenu.prevent.stop="showNodeMenu(node)" @dblclick="editAttr(node)">
                <i class="dg-iconp flow-node-drag" title="连线" v-show="node.canLink !== undefined ? node.canLink : true"
                   :class="{
                            'icon-time-c' : node.showType == 'udf_time_operate',
                            'icon-text' : node.showType == 'udf_text_operate',
                            'icon-algorithm' : node.showType == 'udf_calculate_operate' ,
                            'icon-constant' : node.showType == 'CONSTANT_OPERATOR' ,
                            'icon-variable' : node.showType == 'VARIABLE_OPERATOR' ,
                          }"
                ></i>
                <span class="flow-node-close" v-show="mouseEnter">
                    <span class="dg-iconp icon-cross" title="删除" @click="deleteNode"></span>
                 </span>
                <p :title="plugLabel">
                    <span v-if="!edits[node.id]">{{plugLabel}}</span>
                    <span v-else @click.stop.prevent>
                        <el-input ref="input" v-model="node.name" maxlength="50" size="mini"></el-input>
                    </span>
                </p>
            </div>
        </div>


        <Contextmenu ref="contextmenu" class="context-menu">
            <ul>
                <li v-for="(menu, idx) in nodeMenu" :key="idx"
                    @click="menu.clickFn($event)">
                    <label>{{ menu.label }}</label>
                </li>
            </ul>
        </Contextmenu>
    </div>
</template>

<script>
    import ModelNode from '../../modeling/flowPanel/ModelNode';
    import {globalBus} from "@/api/globalBus";
    import Contextmenu from "vue-context-menu";
    export default {
        name: "RuleNode" ,
        extends : ModelNode,
        components: {
            Contextmenu,
        },
        computed : {
            plugLabel(){
                const {node} = this;
                return node.label ? `${node.label}(${node.name}) ` : node.name;
            }
        },
        data(){
            return {
                edits: {},
                nodeMenu: [

                    {
                        label: "重命名",
                        value: "reName",
                        clickFn: this.editNode
                    },{
                        label: "删除",
                        value: "delete",
                        clickFn: this.deleteNode
                    }
                ],
            }
        },
        methods:{
            /**
             *  显示右键菜单
             * */
            showNodeMenu(node) {
                console.log(node)
                this.$refs.contextmenu.open();
            },
        }
    }
</script>

<style scoped lang="less">
    /deep/ .context-menu {
        .ctx-menu {
            width: 200px;
            background: #ffffff;
            border: 1px solid rgba(0, 0, 0, 0.15);
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
            border-radius: 2px;

            li {
                line-height: 36px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 14px;
                box-sizing: border-box;
                font-size: 14px;
                cursor: pointer;

                &:nth-child(3) {
                    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
                }

                label {
                    color: rgba(0, 0, 0, 0.85);
                    cursor: pointer;
                }

                span {
                    color: rgba(0, 0, 0, 0.45);
                    cursor: pointer;
                }

                &:hover {
                    background: rgba(#0088ff, 0.09);

                    label {
                        color: rgba(#0088ff, 0.85);
                    }

                    span {
                        color: rgba(#0088ff, 0.85);
                    }
                }
            }
        }
    }
    .modelNode {
        z-index: 10;
    }

    .edit-icon {
        float: right;
        line-height: 20px;
    }

    .node {
        border-radius: 6px;
    }


    .flow-node-header {
        //box-shadow: 0 0 6px 0 rgba(0, 136, 255 ,45%);
        display: inline-flex;
        cursor: pointer;
        border: 1px solid rgba(0, 136, 255, 0.35);
        background: #fff;
        i{
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 34px;
            height: 34px;
            cursor: pointer;
            background: rgba(0, 136, 255, 0.12);
            color:rgba(0, 136, 255, 1);
        }
        p{
            padding: 0 30px 0 14px;
            box-sizing: border-box;
            width: 166px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            background-color: #fff;
            line-height: 34px;
            cursor: pointer;
            color:rgba(0,0,0,.85);
        }
        .flow-node-close{
            position: absolute;
            right: -10px;
            top: -10px;
            color: rgba(0, 136, 255, 1);
            font-size: 14px;
            span{
                background-color: #fff;
                border-radius: 10px;
            }
        }
    }
    .flow-node-header:hover {
        //box-shadow: #66a6e0 0 0 6px 0;
        box-shadow: 0 0 6px 0 rgba(0, 136, 255, 0.45);
    }

    /*.flow-node-header i {*/
        /*line-height: 20px;*/
        /*vertical-align: middle;*/
    /*}*/


    .flow-node-body {
        position: relative;
        padding: 5px 26px 5px 26px;
        background-color: #fff;
        text-align: center;
        cursor: pointer;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
        border: 2px solid #66a6e0;
        border-top: none;
    }

    .node:hover .flow-name, .node:hover .eda_icon {
        color: #289bf1;
    }

    .flow-name {
        padding: 0 5px 10px 5px;
        font-size: 16px;
        color: #666;
        position: absolute;
        left: 50%;
        top:100%;
        white-space: nowrap;
        transform: translate(-50% , 0);
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .m-icon {
        height: 28px;
        margin: auto;
        text-align: center;
    }

    .m-icon > .eda_icon {
        width: 28px;
        height: 28px;
    }
    .flow-shadow {
        background: none;
        border-radius:6px;
    }
    /*.flow-shadow:hover {*/
        /*box-shadow: #66a6e0 0 0 12px 0;*/
        /**/
    /*}*/
    .m-field {
        max-width:120px;
        min-width: 36px;
        white-space: nowrap;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        line-height: 30px;
    }
</style>
