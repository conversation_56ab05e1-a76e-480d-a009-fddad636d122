<template>
    <tree-and-list left-width="220">
        <tree slot="left"
              ref="dataTree"
              :hasSave="false"
              @setTreeData="setTreeData"
              @nodeClick="treeDir"
              @initTable="initTable"
        ></tree>
        <list ref="modelingCenterTable"
              @openNewModel="openNewModel"
              @reName="rename"
              @tableItemEvent="onTableItemEvent"
              @modelEdit="modelEdit"
              @moveTo="moveTo"
              @previewResult="previewResult"
              @share="shareFn"
              @serveIssue="serveIssue"
        >
        </list>
        <div slot="other">
            <!--建模模板页-->
            <NewModel v-if="newModel" @backTo="backTo" @newModel="addModelTab"  @addModule="addModule"/>
            <!--建模页-->
            <ModelTabs ref="modelPage" :treeData="treeData" v-if="modelTabs" @backTo="backTo" @closeTab="backTo" @openNewModel="openNewModel" />
            <!--模型配置-->
            <ModelEditDailogVue ref="modelEditDailog" @update="update"/>
            <!--重命名-->
            <RenameDailog ref="refRenameDialog" @rename="dialogRenameEmit"/>
            <!--移动-->
            <OpenModel ref="move" @update="update"/>
            <!--预览-->
            <preview ref="preview"/>
            <!--分享-->
            <share ref="share" />
            <!--服务发布-->
            <issue ref="issue"></issue>
        </div>
    </tree-and-list>
</template>
<script src="./data-excavate.js"></script>