<template>
    <span class="el-tree-node__label" v-html="filterLabel"></span>
</template>

<script>
import { Option } from "element-ui";

export default {
    props: {
        isHighLight: {
            type: Boolean
        },
        filter: {
            type: String
        }
    },
    mixins: [Option],
    computed: {
        filterLabel() {

            // 增加过滤值高亮
            const temple = this.isHighLight && this.filter ? this.label.replace(
                new RegExp(this.filter + '{1}'),
                `<span class="dg-tree-filter__light">${this.filter}</span>`
            ) : this.label;

            return temple;
        }
    }
};
</script>
