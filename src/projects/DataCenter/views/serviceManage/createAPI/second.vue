<template>
    <div v-loading="settings.loading">
        <inquiryAndVerification ref="inquiryAndVerification"
                                :type="type" :dataSetIds="dataSetIds"
                                :desenList="desenList" :modelData="modelData" :regionType="regionType" :dataSetColumnList="dataSetColumnList"
                                :columnInfo="columnInfo" :singleOrManyTable="singleOrManyTable" v-show="['4','6'].includes(regionType) && singleOrManyTable !=='2' && dataSetColumnList.length"></inquiryAndVerification>
        <el-form ref="ruleForm" label-width="110px" class="demo-ruleForm" v-show="['1', '2', '5', '7'].includes(regionType) || (regionType === '6' && singleOrManyTable ==='2')">
            <el-form-item label="设置参数" v-if="regionType !=='2'">
                <el-button type="primary" @click="addColumns">添加</el-button>
                <el-button type="primary" @click="updataService">更新服务参数</el-button>
            </el-form-item>
            <el-form-item :label="reqLabel"  class='singerH'>
                <common-table
                        :data="reqTableData"
                        :columns="reqTHeadData"
                        :pagination="false"
                        class="width100"
                        :border="false"
                        row-key="paramCode"
                >
                    <template slot="isMust" slot-scope="scope">
                        <span>{{scope.row.isMust === 't' ? '是' :  '否'}}</span>
                    </template>
                    <template slot="memo" slot-scope="scope">
                        <el-input v-model="scope.row.memo" maxlength="30"></el-input>
                    </template>
                    <template slot="example" slot-scope="scope">
                        <!--<el-date-picker-->
                                <!--type="datetime"-->
                                <!--v-model="scope.row.example"-->
                                <!--v-if="scope.row.type === 'Time'">-->
                        <!--</el-date-picker>-->
                        <el-input v-model="scope.row.example" v-if="scope.row.type !='Time' "  @change="changeInput" maxlength="30"></el-input>
                    </template>
                </common-table>
            </el-form-item>
            <el-form-item :label="backLabel" class='singerH'>
                <!--模型分析、比对订阅、数据碰撞-->
                <common-table
                        :data="backTableData"
                        :columns="regionType === '2' ? backHeadData : [...backHeadData,{prop: 'desensitization',label: '数据脱敏', width: 150}]"
                        class="width100"
                        :pagination="false"
                        :border="false"
                        row-key="paramCode"
                >
                    <template slot="desensitization" slot-scope="scope">
                        <dg-select v-model="scope.row.desensitization" :data="desenList" label-name="name" value-name="code" @change="changeInput" v-if="scope.row.desensitization"></dg-select>
                        <span v-else></span>
                    </template>
                </common-table>

            </el-form-item>
            <el-form-item  v-if="regionType ==='5'">
                <el-radio-group v-model="contrastType" @change="contrastTypeChange" :class="{'ce-disabled_model' : type === 'edit' }">
                    <el-radio :label="item.value" v-for="item in radioList" :key="item.value">{{item.label}}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="regionType ==='5' && contrastType === '0'">
                <div class="busiDiv" :class="{'ce-disabled_model' : type === 'edit' }">
                    <div class="deltaTime mr10">
                        <span>业务数据集:</span>
                        <dg-select :data="busiDataSetList" v-model="busiDataSet"  label-name="name" value-name="id" @change="showOutputColumn(busiDataSet, '')"></dg-select>
                    </div>
                    <div class="deltaTime">
                        <span>增量时间字段:</span>
                        <dg-select :data="timeColumnList" v-model="deltaTime"  label-name="name" value-name="name"></dg-select>
                    </div>
                </div>
            </el-form-item>
        </el-form>
        <column ref="setColumn" :columnInfo="columnInfo" 
                @setTableData="setTableData"
                :requestTableData="requestTableData"
                :responseTableData="responseTableData" :region="regionType"></column>

        <addParameter ref="variable" :rowData="modelData" @addParaSave="addParaSave"/>
    </div>
</template>

<script>
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import column from "./columns"
import addParameter from "@/projects/DataCenter/views/modeling/flowPanel/createApi/addParameter"
import inquiryAndVerification from "./components/inquiryAndVerification";
export default {
    name: "secondStep",
    mixins: [ commonMixins, servicesMixins],
    components : {column ,addParameter, inquiryAndVerification},
    props:{
        selectDataSet:Object,
        modelInfo : Object,
        region : String,
        nextTipData:Object,
        type:String,
    },
    watch: {
        region(v) {
            this.regionType = v;
        }
    },
    data() {
        return {
            contrastType:'0',
            radioList:[
                {label :' 增量对比', value: '0'},
                {label :' 全量对比', value: '1'},
            ],
            regionType:'',
            reqLabel: '请求参数',
            backLabel: '返回参数',
            desenList:[],
            reqTableData:[],
            reqTableDataDefault: [
                {
                    "paramCode": "requestId",
                    "paramName": "请求id",
                    "paramValue": "",
                    "type": "String",
                    "isMust": "f",
                    "memo": "ID"
                },
                {
                    "paramCode": "operatorDataSet",
                    "paramName": "请求数据",
                    "paramValue": "",
                    "type": "Map",
                    "isMust": "t",
                    "memo": "请求数据",
                    "children": []
                },
            ],
            reqTHeadData:[
                {
                    prop: "paramCode",
                    label: "参数名",
                    minWidth: 120,
                },{
                    prop: "memo",
                    label: "参数描述",
                    width: 150,
                },{
                    prop: "type",
                    label: "参数类型",
                    width: 90,
                },{
                    prop: "isMust",
                    label: "是否必填",
                    width: 90,
                },{
                    prop: "example",
                    label: "示例值",
                    minWidth: 130,
                }
            ],
            setBackTableInfo:[
                { paramCode: 'success' , paramName: 'success' , memo:'请求是否成功，true成功，false失败', type:'String', desensitization:'' },
                { paramCode: 'statusCode' , paramName: 'statusCode' , memo:'响应状态码', type:'String', desensitization:'' },
                { paramCode: 'requestId' , paramName: 'requestId' , memo:'请求批次ID', type:'String', desensitization:'' },
                { paramCode: 'pageInfo' , paramName: 'pageInfo' , memo:'分页信息', type:'Object',children : [],desensitization:''},
                { paramCode: 'msg' , paramName: 'msg' , memo:'响应信息', type:'String',desensitization:'' },

            ],
            backTableData:[] ,
            backHeadData: [
                {
                    prop: "paramCode",
                    label: "参数名",
                    minWidth: 150,
                },{
                    prop: "memo",
                    label: "参数描述",
                    minWidth: 200,
                },{
                    prop: "type",
                    label: "参数类型",
                    minWidth: 100,
                },{
                    prop: "example",
                    label: "说明",
                    minWidth: 150,
                }
            ],

            requestTableData : [],
            responseTableData : [],
            columnInfo : [],
            nodeTableName :"", //数据集名字
            dbType : "", //数据集类型
            interfaceName :"", //模型名称
            originalData : [],
            expandData: [],
            apiInfo: {},
            modelData: this.modelInfo,
            deltaTime:'',
            timeInfo:[
                {"paramCode": "startTime", "paramName": "订阅开始时间", "paramValue": "", "type": "Time", "isMust": "t", "memo": "订阅开始时间", "children": []},
                {"paramCode": "endTime", "paramName": "订阅结束时间", "paramValue": "", "type": "Time", "isMust": "t", "memo": "订阅结束时间", "children": []},
            ],
            timeColumnList: [],
            busiDataSetList:[],
            busiDataSet:'',
            timeList:["timestamp"],
            transId:'',
            dataSetColumnList: [],
            singleOrManyTable: '',
            dataSetIds: [],//选择的数据集id
            allTypeList:{
                varchar: 'String',
                string: 'String',
                text: 'String',
                blob: 'String',
                timestamp: 'Date',
                time: 'Date',
                date: 'Date',
                number: 'Number',
                double: 'Number',
                integer: 'Number',
                biginteger: 'Number',
                bigdecimal:'Number',
                float8: 'Number',
                float4: 'Number',
                bytea: 'String',
                int2: 'Number',
                int4: 'Number',
                int8: 'Number',
                boolean: 'Boolean',
            },
        };
    },
    methods: {
        changeInput(){
            this.$forceUpdate()
        },
        guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                let r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            })
        },
        addColumns(){
            if(this.regionType === "1" || this.regionType === "5" || this.regionType === "7"){
                let param = this.reqTableData.find(n=>n.paramCode === 'params')
                let list = param ? param.children: [];
                this.$refs.variable.init(list);
                return
            }
            this.columnInfo.forEach(element => {
                element.setSearchColumn = false;
                element.example = '';
                element.setResponseColumn = false;
                element.desensitization = 'noDesensitization'
                this.requestTableData.forEach(item => {
                    if (item.paramValue === element.paramValue) {
                        element.setSearchColumn = true;
                        element.example = item.example;
                    }
                });
                this.responseTableData.forEach(e => {
                    if (e.paramValue === element.paramValue) {
                        element.example = e.example;
                        element.setResponseColumn = true;
                        element.desensitization = e.desensitization
                    }
                });
            });
            this.$refs.setColumn.show();
        },
        setTableData(data){
            const vm = this;
            let requestInfo = [], requestList = [];
            requestInfo = data.filter((item) =>{return item.setSearchColumn});
            if(vm.requestTableData.length){
                requestInfo.forEach(p=>{
                    let info = vm.requestTableData.find(n=>n.code === p.code);
                    requestList.push(info ? info : p)
                })
                vm.requestTableData = requestList;
            }
            else{
                vm.requestTableData = requestInfo;
            }
            let responseInfo = [], responseList = [];
            responseInfo = data.filter((item) =>{return item.setResponseColumn});
            if(vm.responseTableData.length){
                responseInfo.forEach(p=>{
                    let info = vm.responseTableData.find(n=>n.code === p.code);
                    responseList.push(info ? info : p)
                })
                vm.responseTableData = responseList;
            }
            else{
                vm.responseTableData = responseInfo;
            }
        },
        getDataSetColumnLoop(data, region, singleOrManyTable){
            const vm = this;
            vm.regionType = region;
            vm.singleOrManyTable = singleOrManyTable;
            if(vm.regionType === "1"){
                vm.nodeTableName = data.name;
                return
            }

            vm.dataSetIds = data.map(n=>n.id);
            vm.columnInfo = [];
            vm.dataSetColumnList = [];
            data.forEach(async(n,i)=>{
                await vm.getDataSetColumn(n,i);
                if(i == data.length - 1){
                    this.$refs.inquiryAndVerification.initData(singleOrManyTable);
                }
            })
        },
        //数据查询---获取表字段
        async getDataSetColumn(data,i){
            if(i === 0) {
                this.dbType = this.selectDataSet && this.selectDataSet.db_type ? this.selectDataSet.db_type  : data.dbType;
            }
            this.nodeTableName = data.name;
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            if(!data.id) return
            settings.loading = true;
            await services.getDataSetColumn(data.id, settings).then(res => {
                if (res.data.status === 0) {
                    res.data.data.forEach(item =>{
                        item.setSearchColumn = false;
                        item.setResponseColumn = false;
                        item.setSortColumn = false;
                        item.isMust = true;
                        item.memo = item.memo || item.name || "-";
                        item.paramValue = item.code;
                        item.paramCode = item.code;
                        item.paramValue = item.code;
                        item.label = item.name || item.code;
                        item.value = item.code;
                        item.dataSetName = data.name || data.code;
                        item.dataSetId = data.id;
                        item.datasetId = data.id;
                        item.dataSetTableName = data.belongTableName;
                        item.dataType = vm.allTypeList[item.dataType.toLowerCase()] || 'String';
                        vm.columnInfo.push(item);
                    })
                    vm.dataSetColumnList.push({dataSet: data.name, id: data.id, code: data.code, name:data.name,dataSetTableName: data.belongTableName, column: res.data.data});
                }
            });
        },
        addParaSave(data){
            if(!data.length){
                let index = this.reqTableData.findIndex(n=>n.paramCode === 'params');
                if(index != -1)
                    this.reqTableData.splice(index,1);
                this.$refs.variable.clearD();
                return
            }
            let params = this.reqTableData.find(n=>n.paramCode === 'params');
            params ? params.children = [] : this.reqTableData.push({
                "paramCode": "params",
                "paramName": "其它参数",
                "paramValue": "",
                "type": "List",
                "isMust": "f",
                "memo": "其它参数",
                "children": []
            });

            let ids = this.reqTableData.find(n=>n.paramCode === 'params').children.map(n=>n.id) || [];
            data.forEach(n=> {
                let info ={
                    id:n.id,
                    paramCode:n.name,
                    paramName: n.name,
                    paramValue: "",
                    isMust: n.isMust ? 't' : 'f',
                    type:n.dataType,
                    example:n.varValue,
                }
                if(!ids.includes(n.id)){
                    this.$nextTick(()=>{
                        this.reqTableData.find(n=>n.paramCode === 'params').children.push(info);
                    })
                }
            })
            this.$refs.variable.clearD();
        },
        /**
         * api类型为模型分析、订阅、碰撞 根据id获取服务输入插件信息  新增时
         */
        getInputInfo(transId, region, singleOrManyTable){
            const vm = this;
            if(vm.transId  === transId && vm.regionType === region){
                return
            }
            vm.singleOrManyTable = singleOrManyTable;
            if(region){
                vm.regionType = region;
            }
            vm.transId = transId;
            vm.reqTableDataDefault[1].children = [];
            vm.modelData.transId = vm.modelData.transId || transId;
            let services = vm.$services("servicesServices");
            vm.backTableData = [];
            let arr = JSON.parse(JSON.stringify(vm.reqTableDataDefault));
            if(region === '5'){
                vm.queryTransSteps(transId); //获取业务数据集
                let paramTime = vm.reqTableData.find(n=>n.paramCode === 'startTime' || n.paramCode === 'endTime');
                if(!paramTime){
                    vm.reqTableData = [...arr, ...vm.timeInfo];
                }
            }
            else{
                vm.reqTableData = arr;
            }
            //获取服务输出信息，同步的返回参数显示operatorDataSet，异步不显示
            services.queryTransMetaServiceOutputInfo(transId).then(res =>{
                if (res.data.status === 0) {
                    vm.setBackTableInfo.forEach(n=>{
                        vm.backTableData.push(n);
                    })

                    let info = { paramCode: 'operatorDataSet' , paramName: 'operatorDataSet' , memo:'响应数据集合', type:'List',children : [] , desensitization:''};
                    info.children = res.data.data && res.data.data.serviceOutputColumns.map(n=>{
                        return { paramCode: n.columnCode, paramName:n.columnName, memo:n.columnName, type:n.columnType,children:[], desensitization:'noDesensitization'}
                    }) || []
                    vm.backTableData.push(info);
                }
            })
            services.queryTransMetaServiceInputInfo(transId).then(res => {
                if (res.data.status === 0) {
                    if(res.data.data.length){
                        res.data.data.forEach((p,i)=>{
                            if(!p.tableName) return
                            let data = {
                                paramCode: p.tableName,
                                paramName: "",
                                paramValue: "",
                                type: "List",
                                isMust: 't',
                                memo: p.pluginName,
                                children: p.serviceInputColumns.map(n=>{
                                    return { paramCode: n.columnCode, type: n.columnType, paramName: n.columnName, isMust: n.required ? 't' : 'f', example: n.sampleValue ,memo:n.columnName}
                                }) || []
                            }
                            let info = vm.reqTableData.find(n=>n.paramCode === 'operatorDataSet');
                            info && info.children.push(data);
                        })
                    }
                    else{
                        return vm.$message.warning("暂无API请求入参组件或暂无数据!")
                    }
                }
            })
        },

        /**
         * api类型为AI建模，渲染表格
         */
        setAIModelingTableData(nextTipData, rowInfo, region){
            const vm = this;
            vm.regionType = region || vm.region;
            if (nextTipData && nextTipData.input_meta) {
                vm.reqTableData = [],
                vm.backTableData = [];
                for (const key in nextTipData.input_meta) {
                    vm.reqTableData.push({
                        paramCode : key,
                        type : nextTipData.input_meta[key],
                        isMust : 't',
                        showDiv : true,
                        memo : "",
                        example : "",
                    })
                }
                for (const key in nextTipData.output_meta) {
                    vm.backTableData.push({
                        paramCode : key,
                        type : nextTipData.output_meta[key],
                        isMust : 't',
                        desensitization:'noDesensitization',
                    })
                }
                if(vm.type === 'edit'){
                    vm.reqTableData.forEach(item => {
                        rowInfo.paramList && rowInfo.paramList.forEach(e =>{
                            if (item.paramCode === e.code || item.paramCode === e.paramCode) {
                                item.example = e.example;
                                item.memo = e.memo;
                            }
                        })
                    })
                }
            }
        },
        /**
         * api类型为模型类，渲染表格
         */
        setModelData(data,type){
            const vm = this;
            vm.reqTableData = data.paramList || [];
            vm.backTableData = data.ginsengList ? data.ginsengList.sort((a,b)=>a.paramCode > b.paramCode ? -1 : a.paramCode < b.paramCode ? 1 : 0) : [];
            vm.modelData.transId = vm.modelData.modelId || data.modelId || data.transId;
            vm.transId = vm.modelData.transId;
            if(type === '5'){
                vm.queryTransSteps(data.modelId);
                vm.deltaTime = data.incrementalColumn;
                vm.busiDataSet = data.incrementalStepId;
                vm.contrastType = data.compareType;
                if(vm.busiDataSet){
                    vm.showOutputColumn(vm.busiDataSet, vm.deltaTime);
                }
            }
        },
        /**
         * api类型为模型分析类、数据查询类，渲染表格
         */
        setTableInfo(data, type){
            const vm = this;
            vm.singleOrManyTable = data.singleOrManyTable;
            vm.regionType = vm.region || type;
            if(['1','5','7'].includes(type) || (type === '6' && data.singleOrManyTable === '2')){
                vm.setModelData(data, type);
            }
            else if(['4'].includes(type) || (type === '6' && data.singleOrManyTable != '2')){
                vm.$refs.inquiryAndVerification.setEditInitData(data, type);
            }
        },
        validation(){
            let flag = true;
            if(this.contrastType === '0' && (!this.busiDataSet || !this.deltaTime)){
                this.$message.warning("请选择业务数据集或增量时间字段");
                flag = false;
            }
            return flag;
        },
        //获取数据脱敏下拉框数据
        getDesenTypes(){
            const vm = this;
            let services = vm.$services("serviceManage");
            vm.desenList = [];
            services.getDesenTypes().then(res => {
                if (res.data.status === 0) {
                    vm.desenList = res.data.data;
                }
            })
        },
        /**
         * 比对订阅--根据模型id获取业务数据集
         */
        queryTransSteps(transId){
            let services = this.$services("serviceManage");
            this.busiDataSetList = [];
            this.busiDataSet = '';
            services.queryTransSteps(transId).then(res => {
                if (res.data.status === 0) {
                    this.busiDataSetList = res.data.data;
                }
            })
        },
        /**
         * 比对订阅--根据步骤id获取表字段
         */
        showOutputColumn(tableID, deltaTime){
            const vm = this;
            let service = vm.$services("plugin");
            vm.deltaTime = deltaTime;
            service.showOutputColumn(tableID).then(res => {
                if (res.data.status === 0) {
                    vm.timeColumnList = res.data.data.filter(n=>{
                        return vm.timeList.includes(n.type.toLowerCase())
                    });
                }
            });
        },
        contrastTypeChange(){
            if(this.contrastType === '1'){
                this.deltaTime = '';
                this.busiDataSet = '';
            }
        },
        //数据查询和信息核查保存校验
        validationInquiryAndVerification(){
            return this.$refs.inquiryAndVerification.validation();
        },
        //数据查询和信息核查获取数据
        getInquiryAndVerificationParamsInfo(){
            return this.$refs.inquiryAndVerification.getParamsInfo();
        },
        resetParam(){
            this.$refs.inquiryAndVerification.resetParam();
        },
        clearInfo(){
            this.requestTableData = [];
            this.reqTableData = [];
            this.responseTableData = [];
            this.inspectTableData = [];
            this.backTableData = [];
            this.dataSetIds = [];
            this.transId = '';
        },
        //更新服务参数
        async updataService(){
            //获取服务输出信息，同步的返回参数显示operatorDataSet，异步不显示
            await this.$axios.all([
                this.queryTransMetaServiceOutputInfo(),
                this.queryTransMetaServiceInputInfo()
            ]);
            this.$message.success("更新成功")
        },
        //获取服务输出信息
        queryTransMetaServiceOutputInfo(){
            const vm = this;
            let services = vm.$services("servicesServices");
            services.queryTransMetaServiceOutputInfo(vm.transId).then(res =>{
                if (res.data.status === 0) {
                    let operatorDataSet = vm.backTableData.find(n=>n.paramCode === 'operatorDataSet');
                    if(operatorDataSet){
                        let children = operatorDataSet.children;
                        operatorDataSet.children = [];
                        res.data.data && res.data.data.serviceOutputColumns.forEach(k=>{
                            let op = children.find(p=>p.paramCode === k.columnCode);
                            if(op){
                                operatorDataSet.children.push(op)
                            }
                            else{
                                let obj = { paramCode: k.columnCode, paramName: k.columnName, memo: k.columnName, type: k.columnType,children:[], desensitization:'noDesensitization'};
                                operatorDataSet.children.push(obj)
                            }
                        })
                    }
                    else{
                        let info = { paramCode: 'operatorDataSet' , paramName: 'operatorDataSet' , memo:'响应数据集合', type:'List',children : [] , desensitization:''};
                        info.children = res.data.data.serviceOutputColumns.map(n=>{
                            return { paramCode: n.columnCode, paramName:n.columnName, memo:n.columnName, type:n.columnType,children:[], desensitization:'noDesensitization'}
                        })
                        vm.backTableData.push(info);
                    }
                }
            })
        },
        //获取服务输入信息
        queryTransMetaServiceInputInfo(){
            const vm = this;
            let services = vm.$services("servicesServices");
            services.queryTransMetaServiceInputInfo(vm.transId).then(res => {
                if (res.data.status === 0) {
                    if(res.data.data.length){
                        res.data.data.forEach((p,i)=>{
                            if(!p.tableName) return
                            let data = {
                                paramCode: p.tableName,
                                paramName: "",
                                paramValue: "",
                                type: "List",
                                isMust: 't',
                                memo: p.pluginName,
                                children: []
                            }
                            let info = vm.reqTableData.find(n=>n.paramCode === 'operatorDataSet');
                            let col = info.children.find(n=>n.paramCode === p.tableName);
                            let serviceInputColumns = p.serviceInputColumns.map(g=>{
                                return {  paramCode: g.columnCode, type: g.columnType, paramName: g.columnName, isMust: g.required ? 't' : 'f', example: g.sampleValue ,memo: g.columnName}
                            })
                            if(col){
                                let children = col.children;
                                col.children = [];
                                serviceInputColumns.forEach(m=>{
                                    let opt = children.find(h=>h.paramCode === m.paramCode);
                                    opt.example = m.example;
                                    col.children.push(opt ? opt : m);
                                })
                            }
                            else{
                                data.children = serviceInputColumns;
                                info && info.children.push(data);
                            }
                        })
                    }
                    else{
                        return vm.$message.warning("暂无API请求入参组件或暂无数据!")
                    }
                }
            })
        }
    },
    created(){
        this.apiInfo = {};
        this.getDesenTypes();
    }
}
</script>


<style scoped lang="less" src="./index.less"></style>