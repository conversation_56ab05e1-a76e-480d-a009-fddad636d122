

.m-t-u-button {
    padding-bottom: 14px;
}
.m-t-u-content {
    .m-t-u-c-item {
        margin-bottom: 12px;
        .m-t-u-c-i-head {
            display: flex;
            justify-content: space-between;
            // margin-bottom: 14px;
            padding-bottom: 10px;
            box-sizing: border-box;
            label {
                font-size: 16px;
                font-weight: bold;
            }
            button {
                padding: 0;
                height: 24px;
            }
        }
        .m-t-u-c-i-box {
            display: flex;
            //换行
            flex-wrap:wrap;
            // justify-content: space-between;
            margin: 0 auto;
            .m-t-u-c-i-b-item {
                width: calc(100% / 6 - 5px);
                padding-left: 10px;
                padding-bottom: 10px;
            }
        }
    }
}

.on {
    border: 1px solid white;
  }
  .off {
    border: 1px solid red;
  }