import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins";
import formPage from "./form/index.vue"

export default {
    name: "rapid-form",
    mixins: [commonMixins, servicesMixins],
    components: {
        formPage
    },
    data() {
        return {
            title: "快速分析",
            visible: false,
            reNew: false,
            saveOther: false,
        }
    },
    methods: {
        show(other , nodeId) {
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.saveOther = !!other;
            if (other) {
                vm.title = "另存为"
            } else {
                vm.title = "快速分析";
            }
            if(nodeId) {
                vm.$nextTick(()=>{
                    vm.$refs.form_page.setNodeId(nodeId);
                })
            }
        },
        newAnalysis() {
            const vm = this, {datasetServices, datasetMock, settings} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            vm.$refs.form_page.validate(valid => {
                if (valid) {
                    let data = vm.$refs.form_page.analyse_data;
                    settings.loading = true;
                    services.createLogicDataSet({
                        id:data.site,name: data.name
                    }, settings).then(res => {
                        if (res.data.status === 0) {
                            data.new_datasetId = res.data.data.id;
                            if(vm.saveOther){
                                vm.$emit("saveOther" , data);
                            }else {
                                vm.$router.push({name: 'analysis', params: data});
                            }

                        }
                    })

                }
            })
        },
        clearData() {
            this.reNew = false;
        }
    }
}
