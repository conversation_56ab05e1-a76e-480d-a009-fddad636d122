<template>
    <div>
        <div>
            <p>基本信息</p>
            <div>
                <el-form ref="form"  :model="basicForm" label-width="120px">
                    <el-form-item v-for="(item, k) in basicInfo" :key="k" :label="item.label" :rules="item.rules">
                        <el-input v-model="basicForm[k]" v-if="item.type === 'input'" :placeholder="item.placeholder"></el-input>
                        <dg-select v-model="basicForm[k]" v-if="item.type === 'select'" :data="item.option" :placeholder="item.placeholder"></dg-select>
                        <dg-radio-group v-if="item.type === 'radio'" v-model="basicForm[k]" :data="item.option" :call-off="true" @change="radioChange"></dg-radio-group>
                        <div v-if="item.type === 'button'">
                            <common-table
                                    class="ce-table"
                                    :data="portData"
                                    :columns="tHeadData"
                                    :pagination="false"
                                    size="mini"
                            >
                                <template slot="header-operate">
                                    <el-button type="text" title="新增" icon="el-icon-circle-plus" @click="customAdd"></el-button>
                                </template>
                                <template slot="operate" slot-scope="{row , $index}">
                                    <el-popconfirm
                                            size="mini"
                                            title="确认删除?"
                                            @confirm="customDel(row ,$index)">
                                        <el-button slot="reference" type="text">删除</el-button>
                                    </el-popconfirm>
                                    <el-button type="text">|</el-button>
                                    <el-button type="text" @click="setParameter(row,type)">设置参数</el-button>
                                </template>
                                <template slot="name" slot-scope="{row , $index}">
                                    <el-form-item>
                                        <el-input
                                                v-model="row.name"
                                        ></el-input>
                                    </el-form-item>
                                </template>
                                <template slot="method" slot-scope="{row , $index}">
                                    <el-form-item>
                                        <dg-select
                                                v-model="row.method"
                                                :data="methodList"
                                        ></dg-select>
                                    </el-form-item>
                                </template>
                                <template slot="type" slot-scope="{row , $index}">
                                    <el-form-item>
                                        <dg-select
                                                v-model="row.type"
                                                :data="postTypeList"
                                        ></dg-select>
                                    </el-form-item>
                                </template>
                            </common-table>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div v-if="type!='look'">
            <dg-button v-footer @click="close">取消</dg-button>
            <dg-button v-footer type="primary" @click="submit">确定</dg-button>
        </div>
    </div>
</template>

<script>
    export default {
        name: "operation",
        props:{
            type: String,
            info: Object,
        },
        data(){
            return {
                basicForm:{
                    classify: 'mediation',
                },
                methodList:[
                    {label: 'post', value:'post'},
                    {label: 'get', value:'get'},
                ],
                postTypeList:[
                    {label: '数据查询', value:'数据查询'},
                    {label: '信息核查', value:'信息核查'},
                    {label: '对比订阅', value:'对比订阅'},
                    {label: '模型分析', value:'模型分析'},
                    {label: '数据碰撞', value:'数据碰撞'},
                ],
                basicInfo: {
                    classify: {
                        type: 'radio',
                        label:'服务分类',
                        placeholder:'',
                        rules :[
                            {required : true , message : "请输入代理地址" , trigger : 'change'}
                        ],
                        option: [
                            { label: '中介服务', value: 'mediation'},
                            { label: '第三方服务', value: 'thirdParty'},
                        ]
                    },
                    mediationName: {
                        type: 'select',
                        label:'中介名称',
                        placeholder:'',
                        rules :[
                            {required : true , message : "请选择中介名称" , trigger : 'change'}
                        ],
                        option: []
                    },
                    mediationAddress: {
                        type: 'input',
                        label:'中介地址(http)',
                        placeholder:'请输入',
                        rules :[
                            {required : true , message : "请输入中介地址" , trigger : 'change'}
                        ]
                    },
                    version: {
                        type: 'select',
                        label:'版本',
                        placeholder:'',
                        rules :[
                            {required : true , message : "请选择版本" , trigger : 'change'}
                        ],
                        option: []
                    },
                    serviceId: {
                        type: 'input',
                        label:'服务ID',
                        placeholder:'请输入',
                        rules :[
                            {required : true , message : "请输入服务ID" , trigger : 'change'}
                        ],
                    },
                    serviceName: {
                        type: 'input',
                        label:'服务名称',
                        placeholder:'请输入',
                        rules :[
                            {required : true , message : "请输入服务名称" , trigger : 'change'}
                        ],
                    },
                    servicePort: {
                        type: 'button',
                        label:'服务接口',
                        placeholder:'',
                        rules :[
                            {required : true , message : "请输入服务接口" , trigger : 'change'}
                        ],
                        btnTxt:'添加'
                    }
                },
                portData: [{name:123}],
                tHeadData: [
                    {
                        prop: 'name',
                        label: "请求名称",
                        minWidth: 150
                    }, {
                        prop: 'method',
                        label: "请求方法",
                        minWidth: 80
                    }, {
                        prop: 'type',
                        label: "接口类型",
                        minWidth: 80,
                    },{
                        prop: 'operate',
                        label: "操作",
                        width: 150,
                        align : "center",
                    },
                ]
            }
        },
        methods:{
            radioChange(){

            },
            /**
             * 列表删除
             */
            customDel(row, inx){
                const vm = this;
                vm.portData.splice(inx, 1);
            },
            customAdd(){
                const vm = this;
                vm.portData.unshift({
                    name: '',
                    method: vm.methodList[0].value,
                    type: vm.postTypeList[0].value,
                });
            },
            setParameter(row, type){
                let layer = this.$dgLayer({
                    title : '参数设置',
                    content : require("./setParameter"),
                    maxmin: false,
                    move: false,
                    props:{
                        type: type,
                        info: row || {},
                    },
                    noneBtnField: type === 'look',
                    area : ["1000px" , "600px"],
                    on : {
                        submit(){
                        }
                    }
                })
            },
            close(){
                this.$emit('close');
            },
            submit(){
                const vm = this;
                vm.close();
            }
        }
    }
</script>

<style scoped lang="less">

</style>