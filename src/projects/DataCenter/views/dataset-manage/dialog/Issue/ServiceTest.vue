<template>
    <div class="serviceTestMain">
        <div>
            <el-container>
                <el-header height="34px">
                    基本服务信息：
                </el-header>
                <div class="marginTop">
                    <el-form>
                        <el-form-item class="formWidth" size="mini" label="服务英文名：" label-width="180px">
                            <el-input readonly v-model="serviceBase.englishName"/>
                        </el-form-item>
                        <el-form-item class="formWidth" size="mini" label="默认模型：" label-width="180px">
                            <el-input readonly v-model="serviceBase.defaultModel"/>
                        </el-form-item>
                    </el-form>
                </div>
            </el-container>
            <el-container>
                <el-header height="34px">
                    <span class="conHead">输入参数：</span>
                    <el-radio-group v-model="language" @change="changeLanguage" size="mini">
                        <el-radio-button label="中文"></el-radio-button>
                        <el-radio-button label="code"></el-radio-button>
                    </el-radio-group>
                </el-header>
                <div class="marginTop">
                    <el-form>
                        <el-form-item class="formWidth" size="mini" v-for="item in inParam" :label="item.inParamLabel"
                                      label-width="180px">
                            <el-input v-model="item.inParamValue"/>
                        </el-form-item>
                    </el-form>
                    <el-button class="testRun" @click="testRun" size="mini" type="primary">测试运行</el-button>
                </div>
            </el-container>
            <el-container>
                <el-header height="34px">
                    输出参数：
                </el-header>
                <div>
                    <common-table
                            :data="tableData"
                            :columns="tHeadData"
                            border
                            :pagination="false"
                    >
                    </common-table>
                </div>
            </el-container>
            <el-container>
                <el-header height="34px">
                    异常信息：
                </el-header>
                <div>
                    <span style="color: red">{{this.errorInfo}}</span>
                </div>
            </el-container>
        </div>
    </div>
</template>

<script>
    import CommonTable from '@/components/common/CommonTable'
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {common} from "@/api/commonMethods/common"
    import {servicesMixins} from "../../service-mixins/service-mixins";
    export default {
        name: "ServiceTest",
        components:{
            CommonTable,
        },
        mixins: [commonMixins, servicesMixins, common],
        data() {
            return {
                tableData:[],
                tHeadData: [
                    {
                        prop: "modelName",
                        label: "名称",
                        minWidth: 160,
                        align: "left"
                    }
                    ],
                errorInfo:"",
                modelId: "",
                // 基本服务信息数据模型
                serviceBase: {
                    englishName: "",
                    defaultModel: "",
                },
                //输入参数
                language: "中文",

                inParam: [
                    // {
                    //     inParamLabel: "satisfactionLevelVal",
                    //     inParamValue: "",
                    // },
                    // {
                    //     inParamLabel: "lastEvaluationVal",
                    //     inParamValue: "",
                    // },
                    // {
                    //     inParamLabel: "timeSpendCompanyVal",
                    //     inParamValue: "",
                    // },
                ],
                inParamCh: [
                    // "字段1","字段2","字段3"
                ],
                inParamEng: [
                    // "1","2","3"
                ],

                //输出参数
                outParam: "",

            }
        },
        methods: {
            changeLanguage(val) {
                const _this = this;
                _this.inParam.forEach((item, i) => {
                    if (val === "code") {
                        item.inParamLabel = this.inParamEng[i];
                    } else {
                        item.inParamLabel = this.inParamCh[i];

                    }
                });
            },
            setServiceTest(modelInfo, serviceName, paramInfo, modelId) {
                this.modelId = modelId;
                this.serviceBase.englishName = serviceName;
                this.serviceBase.defaultModel = modelInfo.engName;

                this.inParam = [];
                paramInfo.forEach(val => {
                    this.inParam.push({
                        inParamLabel: val.paramChName,
                        inParamValue: val.defaultValue,
                    });

                    this.inParamEng.push(val.devParamName);
                    this.inParamCh.push(val.paramChName);
                });
            },
            testRun() {
                const vm = this, {datasetServices, datasetMock } = this;
                let services = vm.getServices(datasetServices, datasetMock);
                const _this = this;
                let paramList = [];
                _this.inParam.forEach((item,i)=>{
                    paramList.push({
                        paramCode:_this.inParamEng[i],
                        paramName:_this.inParamCh[i],
                        defaultValue: item.inParamValue
                    });
                });
                let param = {
                    modelId:_this.modelId,
                    paramList:paramList,
                };
                services.testService(param).then(res =>{
                    // if(res.data.status === 1){
                    //     this.errorInfo = res.data.msg;
                    // }else{
                    //     let columnMeta = res.data.data;
                    //
                    //     console.log(res.data.data)
                    // }
                    try{
                        let data = JSON.parse(res.data.data);
                        //设置表的头 3 数据集服务发布实际调用
                        _this.tHeadData = [];
                        data.rows[0].columnNames.forEach(item =>{
                            let tHead = {
                                prop: item,
                                label: item,
                                minWidth: 160,
                                align: "center"
                            };
                            _this.tHeadData.push(tHead);
                        });
                        //回显表格的内容
                        _this.tableData = [];
                        for(let key in data.rows ){
                            _this.tableData.push(data.rows[key].columns);
                        }
                    }catch (e) {
                        console.log(e);
                        _this.errorInfo = res.data.msg;
                    }

                })

            },
        },
        created() {
            // this.inParamLabel = this.inParamChLabel;
        },
    }
</script>

<style scoped>
    .serviceTestMain {
        margin-top: 0px;
    }

    .el-header {
        background-color: #EBEBEB;
        color: #333;
        text-align: left;
        line-height: 30px;
    }

    .marginTop {
        margin-top: 5px;
    }

    .formWidth {
        width: 40%;
    }

    .conHead {
        margin-right: calc(100% - 200px);
    }

    .testRun {
        margin-left: calc(100% - 200px);
        margin-bottom: 5px;
    }
</style>