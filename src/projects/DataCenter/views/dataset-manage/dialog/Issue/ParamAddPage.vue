<template>
    <div class="addPage">
        <div v-if="addType === 'callParam'">
            <!--                        <el-container>-->
            <!--                            <el-header height="35px">调入参数设置</el-header>-->
            <!--                            <div class="SetPrompt">-->
            <!--                                <el-steps :space="200" :active="3" align-center>-->
            <!--                                    <el-step title="选择调入参数"></el-step>-->
            <!--                                    <el-step title="设置查询逻辑"></el-step>-->
            <!--                                    <el-step title="设置过滤条件"></el-step>-->
            <!--                                </el-steps>-->
            <!--                            </div>-->
            <!--                        </el-container>-->
            <el-container>
                <el-header height="34px" style="line-height: 34px;"><i style="color: red;margin-right: 5px">*</i>设置调入参数
                    <i class="icon ce_link model_status addIcon"
                       :title="addOperation.tip"
                       v-html="addOperation.icon"
                       @click="addOperation.clickFn"
                    ></i>
                </el-header>
                <div class="filterTable">
                    <common-table
                            :data="paramTableData"
                            :columns="paramHeadData"
                            height="calc(37.5vh - 112px)"
                            :pagination="false"
                    >
                        <template v-for="item in paramHeadData" :slot="item.prop" slot-scope="scope">
                            <el-select size="mini" v-model="paramTableData[scope.$index][item.prop]"
                                       v-if="item.templateType==='select'"
                                       @change="selectParamType(scope.row[item.prop],scope.row,item)"
                            >

                                <el-option v-for="opt in item.options"
                                           :label="opt.label"
                                           :value="opt.value"
                                           :key="opt.value">
                                </el-option>
                            </el-select>
                            <el-input size="mini" v-model.trim="paramTableData[scope.$index][item.prop]"
                                      v-if="item.templateType==='input'"
                                      maxLength="101"
                                      @blur="inputValue(paramTableData[scope.$index][item.prop],scope.$index)"
                            >
                            </el-input>
                            <span v-if="item.templateType==='tableCell'">{{scope.row[item.prop]}}</span>


                            <el-popconfirm
                                    v-if="item.prop==='operation'"
                                    size="mini"
                                    title="确认删除?"
                                    @confirm="item.clickFn(scope.row,scope.$index)"

                            >
                                <em
                                        slot="reference"
                                        class="icon ce_link model_status"
                                        :title="item.tip"
                                        v-html="item.icon"
                                ></em>
                            </el-popconfirm>


                            <!--<i v-if="item.prop==='operation'"
                               class="icon ce_link model_status"
                               :title="item.tip"
                               v-html="item.icon"
                               @click="item.clickFn(scope.row,scope.$index)"
                            ></i>-->
                        </template>
                    </common-table>
                </div>
            </el-container>
        </div>
        <div v-if="addType === 'callParticipate'">
            <!--                        <el-container>-->
            <!--                            <el-header height="35px">调出参数设置</el-header>-->
            <!--                            <div class="outputPrompt">-->
            <!--                                <el-steps :space="200" :active="4" align-center>-->
            <!--                                    <el-step title="选择调出参数"></el-step>-->
            <!--                                    <el-step title="设置参数名称"></el-step>-->
            <!--                                    <el-step title="选择调出参数类型"></el-step>-->
            <!--                                    <el-step title="设置返回数据过代码滤条件"></el-step>-->
            <!--                                </el-steps>-->
            <!--                            </div>-->
            <!--                        </el-container>-->
            <el-container>
                <el-header height="34px" style="line-height: 34px;"><i style="color: red;margin-right: 5px">*</i>设置调出参数
                    <i class="icon ce_link model_status addIcon"
                       :title="addOperation.tip"
                       v-html="addOperation.icon"
                       @click="addOperation.clickFn('output')"
                    ></i></el-header>
                <div class="filterTable">
                    <common-table
                            :data="callParticipateData"
                            :columns="callParticipateHeadData"
                            :pagination="false"
                           height="calc(37.5vh - 112px)"
                    >
                        <template v-for="item in callParticipateHeadData" :slot="item.prop" slot-scope="scope">
                            <el-select filterable size="mini" v-model="callParticipateData[scope.$index][item.prop]"
                                       v-if="item.templateType==='select'"
                                       @change="selectParticipateType(scope.row[item.prop],scope.row,item)"
                            >

                                <el-option v-for="opt in item.options"
                                           :label="opt.label"
                                           :value="opt.value"
                                           :key="opt.value">
                                </el-option>
                            </el-select>
                            <el-input size="mini" v-model.trim="callParticipateData[scope.$index][item.prop]"
                                      v-if="item.templateType==='input'"
                                      maxLength="101"
                                      @blur="outputValue(callParticipateData[scope.$index][item.prop],scope.$index)"
                            >
                            </el-input>
                            <span v-if="item.templateType==='tableCell'">{{scope.row[item.prop]}}</span>

                            <el-popconfirm
                                    size="mini"
                                    title="确认删除?"
                                    @confirm="item.clickFn(scope.row,scope.$index,'callParticipate')"

                            >
                                <em
                                        slot="reference"
                                        class="icon ce_link model_status"
                                        :title="item.tip"
                                        v-html="item.icon"
                                        v-if="item.prop==='operation'"
                                ></em>
                            </el-popconfirm>

                            <!--                            <i v-if="item.prop==='operation'"-->
                            <!--                               class="icon ce_link model_status"-->
                            <!--                               :title="item.tip"-->
                            <!--                               v-html="item.icon"-->
                            <!--                               @click="item.clickFn(scope.row,scope.$index,'callParticipate')"-->
                            <!--                            ></i>-->
                        </template>


                        <!--                        <template v-for="item in callParticipateHeadData" :slot="item.prop" slot-scope="scope">-->

                        <!--                            <el-popover-->
                        <!--                                    v-if="item.templateType ==='pop'"-->
                        <!--                                    trigger="click"-->
                        <!--                            >-->
                        <!--                                <el-input size="mini" slot="reference"-->
                        <!--                                          v-model="callParticipateData[scope.$index][item.prop]"></el-input>-->
                        <!--                                <el-checkbox-group v-model="callParticipateData[scope.$index].checkList">-->
                        <!--                                    <div class="p5" v-for="check in item.options" :key="check.value">-->
                        <!--                                        <el-checkbox :label="check.value" @change="addToList">{{check.label}}-->
                        <!--                                        </el-checkbox>-->
                        <!--                                    </div>-->
                        <!--                                </el-checkbox-group>-->
                        <!--                            </el-popover>-->
                        <!--                            <el-select-->
                        <!--                                    filterable-->
                        <!--                                    size="mini"-->
                        <!--                                    @change="selectCallPar(item.prop)"-->
                        <!--                                    v-model="callParticipateData[scope.$index][item.prop]"-->
                        <!--                                    v-if="item.templateType==='select'">-->
                        <!--                                <el-option v-for="opt in item.options"-->
                        <!--                                           :label="opt.label"-->
                        <!--                                           :value="opt.value"-->
                        <!--                                           :key="opt.value"></el-option>-->
                        <!--                            </el-select>-->
                        <!--                            <el-input size="mini" v-model.trim="callParticipateData[scope.$index][item.prop]"-->
                        <!--                                      v-if="item.templateType==='input'">-->
                        <!--                            </el-input>-->
                        <!--                            <i v-if="item.prop==='operation'"-->
                        <!--                               class="icon ce_link model_status"-->
                        <!--                               :title="item.tip"-->
                        <!--                               v-html="item.icon"-->
                        <!--                               @click="item.clickFn(scope.row,scope.$index,'callParticipate')"-->
                        <!--                            ></i>-->
                        <!--                        </template>-->


                    </common-table>
                </div>
            </el-container>
            <!--            <el-container>-->
            <!--                <el-header height="34px">集合详情</el-header>-->
            <!--                <div class="filterTable">-->
            <!--                    <common-table-->
            <!--                            :data="listTableData"-->
            <!--                            :columns="listTableHeadData"-->
            <!--                            border-->
            <!--                            tooltip-effect="light"-->
            <!--                            size="mini"-->
            <!--                    >-->
            <!--                        <template slot="listDataType" slot-scope="scope">-->
            <!--                            <el-select size="mini" v-model="listTableData[scope.$index].listDataType">-->
            <!--                                <el-option v-for="opt in listTableHeadData[2].options"-->
            <!--                                           :key="opt.value"-->
            <!--                                           :value="opt.value"-->
            <!--                                           :label="opt.label"></el-option>-->
            <!--                            </el-select>-->
            <!--                        </template>-->
            <!--                        <template slot="operation" slot-scope="scope">-->
            <!--                            <i-->
            <!--                                    class="icon ce_link model_status"-->
            <!--                                    :title="listTableHeadData[3].tip"-->
            <!--                                    v-html="listTableHeadData[3].icon"-->
            <!--                                    @click="listTableHeadData[3].clickFn(scope.row,scope.$index)"-->
            <!--                            ></i>-->
            <!--                        </template>-->
            <!--                    </common-table>-->
            <!--                </div>-->
            <!--            </el-container>-->
        </div>
        <div v-if="addType ==='callParam'">
            <el-container>
                <el-header height="35px">过滤条件设置：
                    <el-button class="addButton"
                               size="mini"
                               icon="el-icon-plus"
                               type="primary"
                               @click="addFilterCondition"
                    >过滤条件
                    </el-button>
                </el-header>
                <div class="filterTable">
                    <common-table
                            :data="filterTableData"
                            :columns="filterHeadData"
                            :pagination="false"
                            height="calc(37.5vh - 112px)"
                    >
                        <template slot="operation" slot-scope="scope">
                            <!--                            <i-->
                            <!--                                    class="icon ce_link model_status"-->
                            <!--                                    v-for="(col , inx) in operateIcons"-->
                            <!--                                    :key="inx"-->
                            <!--                                    :title="col.tip"-->
                            <!--                                    v-html="col.icon"-->
                            <!--                                    @click="col.clickFn(scope.row,scope.$index)"-->
                            <!--                            ></i>-->

                            <el-popconfirm
                                    size="mini"
                                    title="确认删除?"
                                    @confirm="operateIcons[0].clickFn(scope.row,scope.$index)"

                            >
                                <em
                                        slot="reference"
                                        class="icon ce_link model_status"
                                        :title="operateIcons[0].tip"
                                        v-html="operateIcons[0].icon"
                                ></em>
                            </el-popconfirm>
                        </template>


                    </common-table>
                </div>
            </el-container>

            <!--            过滤弹窗-->
            <common-dialog
                    title="过滤条件"
                    :visible.sync="filterVisible"
                    width="1060px"
                    :before-close="handleClose"
                    @closed="crearOpt"
            >
                <ParamFilter ref="paramFilter" v-if="clearData" :filterPage="filterPage" :filterParam="filterParam"/>
                <div slot="footer">
                    <el-button @click="closeFilterDialog" size="mini">取消</el-button>
                    <el-button @click="filterConfirm" type="primary" size="mini">确定</el-button>
                </div>
            </common-dialog>
        </div>
    </div>
</template>

<script>
    import ParamFilter from '../../../visualizing/attribute/dataFilter/FilterCommon'
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {common} from "@/api/commonMethods/common"
    import {servicesMixins} from "../../service-mixins/service-mixins";
    export default {
        name: "ParamAddPage",
        props: {
            addType: String,
            row: Object,
        },
        mixins: [commonMixins, servicesMixins, common],
        components: {
            ParamFilter
        },
        data() {
            return {
                clearData: false,
                // 输出参数是否存在
                isCallNameSake: false,
                isParamNameSake: false,


                //用于传入后台的过滤参数
                resFilterParam: [],

                filterParam: [],
                paramType: "",
                filterPage: "issue",
                checkList: [],
                //集合详情
                listTableData: [
                    // {
                    //     listParamName: "1",
                    //     listParamChName: "2",
                    //     listDataType: "",
                    // }
                ],
                listTableHeadData: [
                    {
                        prop: "listParamName",
                        label: "开发参数名称",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "listParamChName",
                        label: "参数中文名",
                        minWidth: 160,
                        align: "center",
                    }, {
                        prop: "listDataType",
                        label: "数据类型",
                        minWidth: 160,
                        align: "center",
                        value: "",
                        options: [
                            {
                                label: "String",
                                value: "String",
                            }, {
                                label: "Int",
                                value: "Int",
                            }
                        ],
                    },
                    {
                        prop: "operation",
                        label: "操作",
                        minWidth: 160,
                        align: "center",
                        icon: "&#xe65f;",
                        tip: "删除",
                        clickFn: this.delListParamFn,
                    },
                ],

                //设置调出参数
                callParticipateData: [],
                callParticipateHeadData: [
                    {
                        prop: "callParamName",
                        label: "开发参数名称",
                        minWidth: 160,
                        'show-overflow-tooltip': false,
                        align: "center",
                        value: "",
                        options: [],
                        templateType: "select",
                        // templateType: "pop",
                    },
                    {
                        prop: "callParamChName",
                        label: "参数中文名",
                        minWidth: 160,
                        'show-overflow-tooltip': false,
                        align: "center",
                        value: "",
                        templateType: "input",
                    }, {
                        prop: "callDataType",
                        label: "数据类型",
                        minWidth: 140,
                        align: "center",
                        'show-overflow-tooltip': false,
                        value: "",
                        options: [
                            // {
                            //     label: "Short",
                            //     value: "Short"
                            // }, {
                            //     label: "Integer",
                            //     value: "Integer"
                            // }, {
                            //     label: "Long",
                            //     value: "Long"
                            // }, {
                            //     label: "Float",
                            //     value: "Float"
                            // }, {
                            //     label: "Double",
                            //     value: "Double"
                            // }, {
                            //     label: "String",
                            //     value: "String"
                            // }, {
                            //     label: "Text",
                            //     value: "Text"
                            // }, {
                            //     label: "BigDecimal",
                            //     value: "BigDecimal"
                            // }, {
                            //     label: "BigInteger",
                            //     value: "BigInteger"
                            // }, {
                            //     label: "Blob",
                            //     value: "Blob"
                            // }, {
                            //     label: "Boolean",
                            //     value: "Boolean"
                            // },
                        ],
                        templateType: "tableCell",
                    },
                    {
                        prop: "operation",
                        label: "操作",
                        align: "center",
                        icon: "&#xe65f;",
                        tip: "删除",
                        clickFn: this.delParamFn,
                    },

                ],
                addOperation: {icon: "&#xe61d;", tip: "添加", clickFn: this.addParamCol},
                filterVisible: false,
                //过滤条件删除操作
                operateIcons: [
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.delFiltration},
                ],

                //设置过滤条件
                filterTableData: [],
                filterHeadData: [
                    {
                        prop: "paramName",
                        label: "参数名称",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "filtration",
                        label: "过滤设置",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "operation",
                        label: "操作",
                        minWidth: 50,
                        align: "center"
                    },
                ],
                //设置调入参数
                paramAllData: [],
                paramTableData: [],
                paramHeadData: [
                    {
                        prop: "paramName",
                        label: "开发参数名称",
                        minWidth: 160,
                        align: "center",
                        'show-overflow-tooltip': false,
                        value: "",
                        options: [],
                        templateType: "select",
                    },
                    {
                        prop: "paramChName",
                        label: "参数中文名",
                        minWidth: 160,
                        align: "center",
                        'show-overflow-tooltip': false,
                        value: "",
                        templateType: "input",
                    },
                    // {
                    //     prop: "queryLogic",
                    //     label: "查询逻辑",
                    //     minWidth: 160,
                    //     align: "center",
                    //     value: "",
                    //     options: [
                    //         {
                    //             label: "逻辑1",
                    //             value: "logic1",
                    //         }, {
                    //             label: "逻辑2",
                    //             value: "logic2",
                    //         }
                    //     ],
                    //     templateType: "select",
                    // },
                    {
                        prop: "dataType",
                        label: "数据类型",
                        minWidth: 160,
                        align: "center",
                        'show-overflow-tooltip': false,
                        value: "",
                        templateType: "tableCell",
                        options: [
                            {
                                label: "String",
                                value: "String",
                            }, {
                                label: "Int",
                                value: "Int",
                            }
                        ],
                    },
                    {
                        prop: "operation",
                        label: "操作",
                        minWidth: 80,
                        align: "center",
                        icon: "&#xe65f;",
                        tip: "删除",
                        clickFn: this.delParamFn,
                    },
                ],
            }
        },
        methods: {
            crearOpt() {
                this.clearData = false;
            },
            checkValue(val, index) {
                let flag = false;
                let nullFlag = false;
                this.paramTableData.forEach((item, i) => {
                    if (val.trim() === item.paramChName.trim() && index !== i) {
                        flag = true;
                    } else {
                        if (val.trim() === "") {
                            nullFlag = true;
                        }
                    }
                });

                if (flag && !nullFlag) {
                    this.isCallNameSake = true;
                } else {
                    this.isCallNameSake = false;
                }
            },
            //输入参数中文名校验
            inputValue(val, index) {
                this.checkDeleteValue();
                if (val.length > 100) {
                    this.$message.warning("中文名称长度不能超过100");
                    this.paramTableData[index].paramChName = val.substring(0, 100);
                }
            },
            checkOutValue(val, index) {
                let flag = false;
                this.callParticipateData.forEach((item, i) => {
                    if (val === item.callParamChName && i !== index) {
                        flag = true;
                    }
                });
                if (flag) {
                    this.isParamNameSake = true;
                } else {
                    this.isParamNameSake = false;
                }
            },
            outputValue(val, index) {
                // this.checkOutValue(val,index);
                this.checkDeleteOutValue();
                if (val.length > 100) {
                    this.$message.warning("中文名称长度不能超过100");
                    this.callParticipateData[index].callParamChName = val.substring(0, 100);
                }
            },

            setTableData(params, type, filter, resFilter) {
                const _this = this;
                if (type === "callParam") {
                    _this.paramTableData = [];
                    params.forEach(item => {
                        _this.paramTableData.push(
                            {
                                paramName: item.devParamName,
                                paramChName: item.paramChName,
                                dataType: item.paramType,
                            }
                        )
                    });
                    _this.filterTableData = filter;
                    _this.resFilterParam = resFilter;
                } else {
                    _this.callParticipateData = [];
                    params.forEach(item => {
                        _this.callParticipateData.push(
                            {
                                callParamName: item.devParamName,
                                callParamChName: item.paramChName,
                                callDataType: item.paramType,
                                checkList: []
                            }
                        )
                    })
                }
            },

            getInParam() {
                let resData = {
                    param: [],
                    filter: [],
                    //用于传入后台的过滤数据
                    resFilter: [],
                };
                if (this.addType === "callParam") {

                    if (this.isCallNameSake) {
                        return undefined;
                    }

                    resData.param = this.paramTableData;
                    resData.filter = this.filterTableData;
                    resData.resFilter = this.resFilterParam;
                } else {
                    if (this.isParamNameSake) {
                        return undefined;
                    }
                    // return this.callParticipateData;
                    resData.param = this.callParticipateData;
                }
                return resData;
            },

            selectParticipateType(val, row, item) {
                const _this = this;
                let paramNum = 0;
                this.callParticipateData.forEach(ind => {
                    if (val === ind.callParamName) {
                        paramNum++;
                    }
                });
                if (paramNum > 1) {
                    row.callParamName = "";
                    this.$message.warning("该参数已存在");
                    return;
                }
                item.options.forEach(opt => {
                    if (val === opt.value) {
                        row.callDataType = opt.type
                    }
                })
            },
            selectParamType(val, row, item) {
                // const _this = this;
                // let paramNum = 0;
                // this.paramTableData.forEach(ind => {
                //     if (val === ind.paramName) {
                //         paramNum++;
                //     }
                // });
                // if (paramNum > 1) {
                //     row.paramName = "";
                //     this.$message.warning("该字段已存在！");
                //     return;
                // }
                item.options.forEach(opt => {
                    if (val === opt.value) {
                        row.dataType = opt.type
                    }
                });

                //过滤字段
                this.paramHeadData[0].options = this.getFilterParam();

            },
            addToList() {
                this.listTableData = [];
                this.callParticipateData.forEach(item => {
                    item.checkList.forEach(val => {
                        this.listTableData.push(
                            {
                                listParamName: val,
                                listParamChName: val,
                                listDataType: "",
                            }
                        );
                    });

                });

            },

            selectCallPar(type) {
                const _this = this;

                if (type === 'callParamName') {
                    _this.listTableData = [];
                    _this.callParticipateData.forEach(item => {
                        item.callParamName.forEach(call => {
                            this.listTableData.push({
                                listParamName: call,
                                listParamChName: call,
                                listDataType: "",
                            })
                        })
                    })

                }
            },
            delListParamFn(row, index) {
                this.listTableData.splice(index, 1);
            },
            addFilterCondition() {
                //添加过滤条件时，需要将选择选中的入参和选中的过滤条件的字段过滤掉
                if (this.paramType !== "output") {
                    this.filterParam = this.getFilterParam();
                }
                this.filterVisible = true;
                this.clearData = true;

            },
            getFilterParam() {
                let returnData = [];
                //过滤已经选择的字段
                this.paramAllData.forEach(item => {
                    let isExit = false;
                    this.paramTableData.forEach(v => {
                        if (item.value === v.paramName) {
                            isExit = true;
                        }
                    });
                    if (!isExit) {
                        returnData.push(item);
                    }
                });

                let resData = [];
                //过滤已经选择用于过滤设置的字段
                returnData.forEach(r => {
                    let isExitFilter = false;
                    if (this.filterTableData.length > 0) {
                        this.filterTableData.forEach(v => {
                            if (r.value === v.paramName) {
                                isExitFilter = true;
                            }
                        });
                    }
                    if (!isExitFilter) {
                        resData.push(r);
                    }
                });
                return resData;
            },


            addParamCol(type) {
                this.paramType = type;
                if (type === "output") {
                    // if (this.callParticipateData.length === 1) {
                    //     this.$message.warning("目前只允许一个调入参数");
                    //     return;
                    // }

                    if (this.isParamNameSake) {
                        this.$message.warning("中文名称不能重复！");
                    } else {
                        this.callParticipateData.push({
                            callParamName: "",
                            callParamChName: "",
                            callDataType: "",
                            checkList: []
                        })
                    }

                } else {
                    if (this.isCallNameSake) {
                        this.$message.warning("中文名称不能重复！");
                    } else {
                        let options = this.getFilterParam();
                        this.paramTableData.push({
                            paramName: "",
                            paramChName: "",
                            queryLogic: "",
                            dataType: ""
                        });
                        this.paramHeadData[0].options = options;
                    }

                }

            },
            checkDeleteValue() {
                let flag = false;
                this.paramTableData.forEach((item, i) => {
                    this.paramTableData.forEach((val, j) => {
                        if (item.paramChName === val.paramChName && i !== j) {
                            flag = true;
                        }
                    })
                });
                if (flag) {
                    this.isCallNameSake = true;
                } else {
                    this.isCallNameSake = false;
                }
            },
            checkDeleteOutValue() {
                let flag = false;
                this.callParticipateData.forEach((item, i) => {
                    this.callParticipateData.forEach((val, j) => {
                        if (item.callParamChName === val.callParamChName && i !== j) {
                            flag = true;
                        }
                    })
                });
                if (flag) {
                    this.isParamNameSake = true;
                } else {
                    this.isParamNameSake = false;
                }
            },
            delParamFn(row, index, type) {
                if (type === 'callParticipate') {
                    this.callParticipateData.splice(index, 1);
                    this.checkDeleteOutValue();
                } else {
                    this.paramTableData.splice(index, 1);
                    this.paramHeadData[0].options = this.getFilterParam();
                    this.checkDeleteValue();
                }
            },
            delFiltration(row, index) {
                this.resFilterParam.forEach((item, i) => {
                    if (item.field.id === row.id) {
                        this.resFilterParam.splice(i, 1);
                    }
                });
                this.filterTableData.splice(index, 1);
                this.paramHeadData[0].options = this.getFilterParam();
            },
            handleClose() {
                this.filterVisible = false;
            },
            filterConfirm() {
                let res = this.$refs.paramFilter.getResult();

                if (!res || res.param === null) {
                    //this.$message.warning("请选择过滤字段");
                    return;
                }

                let flag = false;
                this.filterTableData.forEach(val => {
                    if (val.filterType === res.param.type && val.paramName === res.param.value) {
                        flag = true;
                        this.$message.warning("参数[" + res.param.value + "]已存在相同类型的过滤设置");
                    }
                });
                if (flag) {
                    return;
                }
                if (res.result.value === "" || res.result.value === null || res.result.value.length <= 0) {
                    this.$message.warning("未设置过滤条件");
                    return;
                }


                let filtration = this.changeFiltration(res.result);
                this.filterTableData.push(
                    {
                        paramName: res.param.label,
                        paramValue: res.param.value,
                        filtration: filtration,
                        filterType: res.param.type,
                        id: res.param.id,
                    }
                );
                this.paramHeadData[0].options = this.getFilterParam();
                //封装条件过滤的json对象
                let field = {
                    id: res.param.id,
                    name: res.param.label,
                    code: res.param.value,
                    jsType: res.param.type
                };
                res.result.field = field;
                this.resFilterParam.push(res.result);

                this.filterVisible = false;
            },


            changeFiltration(val) {
                let resData = "";
                switch (val.type) {
                    case "year":
                        resData = "年份 = " + new Date(val.value[0]).getFullYear();
                        break;
                    case "month":
                        let yearDate = new Date(val.value[0]);
                        resData = "年月 = " + yearDate.getFullYear() + "-" + (yearDate.getMonth() + 1);
                        break;
                    case "date":
                        let date = new Date(val.value[0]);
                        resData = "日期 = " + date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
                        break;
                    case "datetime":
                        let dateTime = new Date(val.value[0]);
                        resData = "时间 = " + dateTime.getFullYear() + "-" + (dateTime.getMonth() + 1) + "-" + dateTime.getDate() + " " + dateTime.getHours() + ":" + dateTime.getMinutes() + ":" + dateTime.getSeconds();
                        break;
                    case "monthrange":
                        //非动态
                        if (val.tab === "picker") {
                            let date1 = new Date(val.value[0]);
                            let date2 = new Date(val.value[1]);
                            resData = date1.getFullYear() + "-" + (date1.getMonth() + 1) + " 到 " + date2.getFullYear() + "-" + (date2.getMonth() + 1);
                        } else {
                            let range = val.value.range === 'before' ? '前' : '后',
                                time = new Date(val.value.value).Format('yyyy-MM-dd hh:mm:ss');
                            resData = time + " " + range;
                        }
                        break;
                    case "daterange":
                        //非动态
                        if (val.tab === "picker") {
                            let date1 = new Date(val.value[0]);
                            let date2 = new Date(val.value[1]);
                            resData = date1.getFullYear() + "-" + (date1.getMonth() + 1) + date1.getDate() + "到" + date2.getFullYear() + "-" + (date2.getMonth() + 1) + date2.getDate();
                        } else {
                            let range = val.value.range === 'before' ? '前' : '后',
                                time = new Date(val.value.value).Format('yyyy-MM-dd hh:mm:ss');
                            resData = time + " " + range;
                        }
                        break;
                    case "precise":
                        resData = "精准过滤 = " + val.value;
                        break;
                    case "prefix_fuzzy":
                        resData = "前缀模糊过滤 = " + val.value;
                        break;
                    case "suffix_fuzzy":
                        resData = "后缀模糊过滤 = " + val.value;
                        break;
                    case "fuzzy":
                        resData = "模糊过滤 = " + val.value;
                        break;
                    case "enum_tree":
                        resData = val.codeLabel + ' = ' + val.value;
                        break;
                    case "digital":
                        console.log(val);
                        resData = '数值 ' + val.value.leftV.logical + " " + val.value.leftV.value;
                        break;
                    case "interval_digital":
                        resData = '数值' + val.value.rightV.logical + " " + val.value.rightV.value + ' && 数值' + val.value.leftV.logical + " " + val.value.leftV.value;
                        break;
                }
                return resData;
            },


            closeFilterDialog() {
                this.filterVisible = false;
            },
        },
        created() {
            const vm = this, {datasetServices, datasetMock } = this;
            let services = vm.getServices(datasetServices, datasetMock);
            let rowData = this.row;
            let param = {
                dbType: rowData.db_type,
                name: "",
                objId: rowData.id,
                pageIndex: 0,
                pageSize: 0,
                sortField: "name",
                sortOrder: "desc",
            };
            if (rowData.dbType !== 'HbaseHtable') {
                let req ;
                if(vm.row.isModel){
                    req = services.queryElasticsColumns(param);
                }else {
                    req = services.getDataSetColumn(rowData.id);
                }
                req.then(res => {
                    if (res.data.status === 0) {
                        let resData = res.data.data;
                        resData.forEach(item => {
                            this.paramHeadData[0].options.push(
                                {
                                    id: item.id,
                                    label: item.code,
                                    value: item.code,
                                    type: item.dataType,
                                    jsType: item.jsType,
                                    dbType: item.dbType
                                }
                            );
                        });
                        this.callParticipateHeadData[0].options = this.paramHeadData[0].options;
                        this.paramAllData = this.paramHeadData[0].options;

                    }
                })
            }
        }
    }
</script>

<style scoped>
    .el-header {
        background-color: #EBEBEB;
        color: #333;
        text-align: left;
        line-height: 30px;
    }

    .filterTable {
        margin: 5px 0px 5px 0px;
    }

    .SetPrompt {
        align: center;
        margin: 5px 5px 5px 115px;
    }

    .outputPrompt {
        align: center;
        margin: 5px 5px 5px 70px;
    }

    .addIcon {
        /*margin-left: calc(100% - 125px);*/
        float: right;
    }

    .addButton {
        margin-top: 3px;
    }
</style>
