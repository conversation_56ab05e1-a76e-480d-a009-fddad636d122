<template>
    <div class="tree">
        <div class="d-s-l-top">
            <el-input
                    size="mini"
                    placeholder="请输入名称搜索"
                    suffix-icon="el-icon-search"
                    v-model.trim="filterText"
            ></el-input>
        </div>
        <div class="d-v-l-head">
            <!--<dg-button
                    v-if="rights.indexOf(rightOfAdd) > -1"
                    type="primary"
                    icon="el-icon-circle-check"
                    @click="empowerShow"
            >{{btnTxt}}</dg-button>-->
            <el-radio-group v-model="activeName">
                <el-radio-button class="ce-radio_two" v-for="(radio , inx) in tab_panel" :key="inx"
                                 :label="radio.value">{{radio.label}}
                </el-radio-button>
            </el-radio-group>
        </div>
        <div class="d-s-l-bottom" v-loading="settings.loading">
            <div class="d-s-l-b-tree tree_cont" v-show="activeName === 'custom'">
                <dg-tree
                        :data="data"
                        node-key="id"
                        :expand-on-click-node="false"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        :default-expand-all="true"
                        ref="tree"
                        :highlight-current="true"
                        @node-click="nodeClick"
                >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span v-if="data.globalCode === null || data.globalCode === undefined" class="node-label">{{ node.label }}</span>
                        <span class="node-label" draggable="true" v-else
                              @dragend="dragend()"
                              @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data  })"
                        >{{ node.label }}</span>

                    </span>
                </dg-tree>
            </div>
            <div class="d-s-l-b-tree tree_cont" v-show="activeName === 'standard'">
                <dg-tree
                        :data="ds_data"
                        node-key="id"
                        :expand-on-click-node="false"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        :default-expand-all="true"
                        ref="ds_tree"
                        :highlight-current="true"
                        @node-click="getCenterTable"
                >
                     <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span class="node-label" draggable="true" v-if="data.level === 4"
                              @dragend="dragend()"
                              @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data  })"
                        >{{ node.label }}</span>
                         <span v-else class="node-label">{{ node.label }}</span>
                    </span>
                </dg-tree>
            </div>
        </div>
    </div>
</template>

<script src="./tree.js"></script>
<style scoped lang="less" src="./tree.less"></style>