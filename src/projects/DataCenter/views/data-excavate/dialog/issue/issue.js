import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins";
import ModelParams from "./model-params"
import ModelForm from "./model-form"
export default {
    name: "issue" ,
    mixins :[commonMixins , dialog , servicesMixins ],
    components : {ModelForm , ModelParams},
    data(){
        return {
            title : "模型方案发布" ,
            width : "1000px" ,
            rowData : {},
            showForm : false ,
        }
    },
    methods : {
        show(row){
            const vm = this;
            vm.visible =true;
            vm.reNew = true;
            vm.showForm = false;
            vm.rowData = row;
        },
        next(){
            this.showForm = true;
        },
        prev(){
            this.showForm = false;
        },
        getParams(){},
        submit(){
            const vm = this , {modelingServices , modelingMock ,settings  } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            vm.$refs.form.validate(valid =>{
                if(valid){
                    settings.loading = true;
                    let allParams =  vm.$refs.params.getAllParams();
                    let formData = vm.$refs.form.serviceInfo;

                    let paramsVo = {
                        filterJson: "[]",
                        ginsengList: [],
                        implChineseName : formData.chName ,
                        implEnglishName: formData.engName,
                        implVersion: formData.version,
                        interfaceChineseName: formData.serviceChName,
                        interfaceEnglishName: formData.serviceName,
                        modelId: vm.rowData.transId,
                        paramList: allParams,
                        serviceType: formData.type ,
                        memo : formData.explain
                    };
                    services.createService(paramsVo , settings).then(res => {
                        if(res.data.status === 0){
                            vm.$message.success("发布成功!");
                            vm.visible = false;
                        }
                    })
                }
            })
        }
    }
}