@height : 60px;
.x-main-container {
  height: 100vh;
  background-color: rgb(245,245,245);
  overflow: hidden;
}
.x-main-header {
  padding: 0 22px;
  display: flex;
  background: #252b34;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}
.x-main-menu {
  flex: 1;
  display: flex;
  align-items: center;
  &-item {
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    color: rgba(255, 255, 255, 0.65);
    cursor: pointer;
    height: 100%;
    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.12);
    }
    i {
      margin-right: 10px;
      font-size: 14px;
    }
    &.is-active {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.12);
    }
  }
}
.x-main-logo {
  width: 22.5rem;
  display: flex;
  align-items: center;
  i{
    font-size: 36px;
    color: #fff;
    margin-right: 10px;
  }

  span {
    font-weight: bold;
    font-size: 28px;
    color: #ffffff;
  }
}
.x-main-body {
  height: calc(100% - 60px);
}
.x-main-content {
  overflow: hidden;
  padding: 14px 1.5rem 0 1.5rem;
  display: flex;
  flex-direction: column;
}
.x-main-cont {
  overflow: hidden;
  flex: 1;
  padding: 14px 0 0 0;
}
.x-main-right {
  //width: 220px;
}
.x-main-side {
  overflow: hidden;
  background: #191e25;
  display: flex;
  flex-direction: column;
  &-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 12px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.65);
    cursor: pointer;
    &:hover {
      color: #ffffff;
      background: rgba( #1890ff,  0.25);
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: #1890ff;
        border-radius: 0 4px 4px 0;
      }
    }
    &.is-active {
      color: #ffffff;
      background: rgba( #1890ff,  0.25);
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: #1890ff;
        border-radius: 0 4px 4px 0;
      }
    }
  }
}
.x-main-footer{
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245,245,245);
  font-size: 12px;
  color: rgba(0,0,0,0.45);
}

.ce-logout {
  font-size: 14px;
  color: #fff;
}

.ce-user__list {
  display: flex;
  justify-content: flex-end;
  height: @height;
  line-height: @height;
  align-items:center ;
  color: #fff;
  > li:not(:first-child) {
    margin-left: 12px;
  }
  .el-divider {
    background: rgba(255,255,255,.8);
    margin: 0 12px;
  }
}
