import data from "./index"
import Mock from "mockjs"

/*
*  查询数仓 表格
 * @param id , pId , keyword
 * @returns {Promise<*>}
* */
export async function queryDataBaseTable({pageSize , pageIndex , name , sortField , sortOrder, dwDbId,isStdlib }) {
    return await Mock.mock(data.queryDataBaseTable);
}
/*
*  是否流式数据
 * @param id , stream
 * @returns {Promise<*>}
* */
export async function updateStream( id , stream ) {
    return await Mock.mock(data.updateStream);
}
/*
*  发布
 * @param sourceId
 * @returns {Promise<*>}
* */
export async function isIssue( sourceId ) {
    return await Mock.mock(data.isIssue);
}
/*
*  删除
 * @param tableId , dbType ,classifyId
 * @returns {Promise<*>}
* */
export async function deleteTable( tableId , dbType ,classifyId ) {
    return await Mock.mock(data.deleteTable);
}
/*
*  详情
 * @param pageSize , pageIndex ,name , sortField , sortOrder, objId , dbType
 * @returns {Promise<*>}
* */
export async function queryElasticsColumns( {pageSize , pageIndex ,name , sortField , sortOrder, objId , dbType }) {
    return await Mock.mock(data.queryElasticsColumns);
}
/*
*  预览
 * @param tableId , tableName ,limit , dbType
 * @returns {Promise<*>}
* */
export async function preview( {tableId , tableName ,limit , dbType }) {
    return await Mock.mock(data.preview);
}
/*
*  获取数据对象数据
 * @param id
 * @returns {Promise<*>}
* */
export async function getDataObject( id ) {
    return await Mock.mock(data.getDataObject);
}
/*
*  添加数据对象数据
 * @param dataBaseId
 * @returns {Promise<*>}
* */
export async function saveTable( dataBaseId , values ,settings ) {
    settings.loading = false;
    return await Mock.mock(data.saveTable);
}
/*
*  发布服务
 * @param param
 * @returns {Promise<*>}
* */
export async function createService( param ) {
    return await Mock.mock(data.createService);
}
/*
*  测试服务
 * @param param
 * @returns {Promise<*>}
* */
export async function testService( param ) {
    return await Mock.mock(data.testService);
}

/*
*  获取 流式数据
 * @param id
 * @returns {Promise<*>}
* */
export async function getTableInfo( id ) {
    return await Mock.mock(data.getTableInfo);
}

/*
*  新增数据源信息
 * @param id
 * @returns {Promise<*>}
* */
export async function getDataSetInfo( id ) {
    return await Mock.mock(data.getDataSetInfo);
}

/*
*  保存添加数据源
 * @param
 * @returns {Promise<*>}
* */
export async function saveDWDBInstance(classifyId , busiDirId , sourceData ,settings ) {
    settings.loading = false;
    return await Mock.mock(data.saveDWDBInstance);
}


/*
*  查询所有授权数据
 * @param dwid
 * @returns {Promise<*>}
* */
export async function queryDataBaseTableAll( dwId ) {
    return await Mock.mock(data.queryDataBaseTableAll);
}

/*
*  查询所有授权数据
 * @param id
 * @returns {Promise<*>}
* */
export async function queryDataBaseTableAllByUserOrRole( dwId , ids ) {
    return await Mock.mock(data.queryDataBaseTableAllByUserOrRole);
}

/*
*  添加数据源授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function addDataSetAuth( dataSetAuthVo , settings ) {
    settings.loading = false;
    return await Mock.mock(data.addDataSetAuth);
}

/*
*  批量授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function dataSetsAuth( dataSetAuthVo  ) {
    return await Mock.mock(data.dataSetsAuth);
}
/*
*  单独授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function dataSetsAuthOne( dataSetAuthVo , settings , path ) {
    return await Mock.mock(data.dataSetsAuthOne);
}

/*
*  数据集注册
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function dataSetAuthRegister( dataSetAuthVo ) {
    return await Mock.mock(data.dataSetAuthRegister);
}

/*
*  批量取消授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function cancelDataSetsAuth( dataSetAuthVo  ) {
    return await Mock.mock(data.cancelDataSetsAuth);
}
/*
*  查询所有授权角色
 * @param isDataSet , dataSetId
 * @returns {Promise<*>}
* */
export async function queryAllRoleAuth( dataSetId  ) {
    return await Mock.mock(data.queryAllRoleAuth);
}

/*
*  查询所有授权用户
 * @param isDataSet , dataSetId
 * @returns {Promise<*>}
* */
export async function getAllUserAuth(  dataSetId  ) {
    return await Mock.mock(data.getAllUserAuth);
}

/*
*  极速表
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function setFastTable( tableId , isFast ) {
    return await Mock.mock(data.setFastTable);
}

/*
*  获取库实例
 * @param type
 * @returns {Promise<*>}
* */
export async function getDataSetByDataSetType( type ) {
    return await Mock.mock(data.getDataSetByDataSetType);
}


/*
*  查询数仓 树
 * @param id , pId , keyword
 * @returns {Promise<*>}
* */
export async function queryDirTree(id = "" , pId = "" , keyword = "" , settings ) {
    settings.loading = false;
    return await Mock.mock(data.queryDirTree);
}
/*
*  查询数仓 树及对象
 * @param
 * @returns {Promise<*>}
* */
export async function getDataObjectTree(settings) {
    settings.loading = false;
    return await Mock.mock(data.getDataObjectTree);
}
