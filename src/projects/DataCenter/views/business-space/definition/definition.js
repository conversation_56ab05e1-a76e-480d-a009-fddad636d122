import LineModel from "@/projects/DataCenter/views/business-space/components/LineModel";
import FlowModel from "@/projects/DataCenter/views/business-space/components/FlowModel";

export default {
    name: "definition",
    components: {
        LineModel,
        FlowModel
    },
    data() {
        return {
            title1: "业务定义",
            title2: "指标中心",
            title3: "标签中心",
            list1: [
                [
                    {
                        icon: "icon-Basic dg-iconp",
                        label: "基础配置",
                        clickFn: this.showBaseSetting,
                    },

                ], [
                    {
                        icon: "icon-briefcase dg-iconp",
                        label: "业务实体",
                        clickFn: this.showBusinessEntity
                    },

                ],
                [
                    {
                        icon: "icon-journal-b dg-iconp",
                        label: "业务事实",
                        clickFn: () => {
                        }
                    },
                ],
                [
                    {
                        icon: "icon-release dg-iconp",
                        label: "发布管理",
                        clickFn: () => {
                        }
                    }
                ]
                ,
                [
                    {
                        icon: "icon-automation dg-iconp",
                        label: "自动物理化",
                        clickFn: () => {
                        }
                    }
                ]

            ],
            list2: [
                [
                    {
                        icon: "icon-data-d1 dg-iconp",
                        label: "通用统计周期",
                        clickFn: () => {
                        }
                    },
                    {
                        icon: "icon-atom dg-iconp",
                        label: "原子指标",
                        clickFn: () => {
                        }
                    },
                    {
                        icon: "icon-meau dg-iconp",
                        label: "通用限定",
                        clickFn: () => {
                        }
                    },
                ], [
                    {
                        icon: "icon-data-e dg-iconp",
                        label: "业务指标",
                        clickFn: () => {
                        }
                    },
                ], [
                    {
                        icon: "icon-quota dg-iconp",
                        label: "指标计算",
                        clickFn: () => {
                        }
                    },
                ], [
                    {
                        icon: "icon-statistics1 dg-iconp",
                        label: "汇总物理化",
                        clickFn: () => {
                        }
                    },
                ]
            ],
            list3: [
                [
                    {
                        icon: "icon-label-b dg-iconp",
                        label: "标签管理",
                        clickFn: () => {
                        }
                    }
                ], [
                    {
                        icon: "icon-automation dg-iconp",
                        label: "自动物理化",
                        clickFn: () => {
                        }
                    }
                ], [
                    {
                        icon: "icon-book dg-iconp",
                        label: "标签计算",
                        clickFn: () => {
                        }
                    }
                ],
            ],
            rowData : null
        }
    },
    methods: {
        /**
         * 基础配置
         */
        showBaseSetting() {
            const vm = this , {rowData} = vm;
            let layer = vm.$dgLayer({
                title : "基础配置" ,
                content : require("../dialog/baseSettings/index.vue"),
                maxmin: false,
                noneBtnField: true,
                move:false,
                area : ["100%" , "100%"],
                props : {
                    row : rowData
                }
            })
        },
        /**
         * 业务实体
         */
        showBusinessEntity(){
            const vm = this , {rowData} = vm;
            let layer = vm.$dgLayer({
                title : "定义业务实体" ,
                content : require("../dialog/businessEntity/index.vue"),
                maxmin: false,
                noneBtnField: true,
                move:false,
                area : ["100%" , "100%"],
                props : {
                    row : rowData
                }
            })
        }
    },
    created() {
        const vm = this;
        let {row} = vm.$route.params;
        if(row){
            vm.rowData = row;
        }else {
            vm.$router.push({name:"businessSpaceOverview"});
        }

    }
}
