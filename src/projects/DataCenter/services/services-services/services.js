import * as reqUtil from '@/api/reqUtil'
const root = "/cicada/service/input/" , root1 = "/cicada/service/output/" , root2 = "/cicada/service/publish/" ;
/*
*  查询服务输入信息
 * @param
 * @returns {Promise<*>}
* */
export async function queryMetaInfo( tranStepId, settings ) {
    return await reqUtil.post({
        url: root+ 'queryMetaInfo?tranStepId=' + tranStepId,
        settings
    });
}

/*
* 保存服务输入信息
 * @param
 * @returns {Promise<*>}
* */
export async function saveMetaInfo(tranStepId, transId, data, settings ) {
    return await reqUtil.post({
        url: root+ 'saveMetaInfo?tranStepId=' + tranStepId + '&transId=' + transId,
        settings
    },data);
}

/*
* 保存测试数据
 * @param
 * @returns {Promise<*>}
* */
export async function saveTestData(tranStepId, data, settings ) {
    return await reqUtil.post({
        url: root+ 'saveTestData?tranStepId=' + tranStepId,
        settings
    },data);
}


/*
* 查询上游字段
 * @param
 * @returns {Promise<*>}
* */
export async function getFields(tranStepId, settings ) {
    return await reqUtil.post({
        url: root1+ 'getFields?tranStepId=' + tranStepId,
        settings
    });
}

/*
* 查询服务输出信息
 * @param
 * @returns {Promise<*>}
* */
export async function queryMetaInfoOutPut(tranStepId, settings ) {
    return await reqUtil.post({
        url: root1 + 'queryMetaInfo?tranStepId=' + tranStepId,
        settings
    });
}


/*
* 保存服务输出信息
 * @param
 * @returns {Promise<*>}
* */
export async function saveMetaInfoOutPut(tranStepId, data, settings ) {
    return await reqUtil.post({
        url: root1 + 'saveMetaInfo?tranStepId=' + tranStepId,
        settings
    },data);
}

/*
* 生成api
 * @param
 * @returns {Promise<*>}
* */
export async function createModelService( data, settings ) {
    return await reqUtil.post({
        url: '/publish/createModelService',
        settings
    },data);
}

/*
* 编辑api
 * @param
 * @returns {Promise<*>}
* */
export async function editModelService( data, settings ) {
    return await reqUtil.post({
        url: '/publish/editModelService',
        settings
    },data);
}

/*
* 生成api回显 - 暂未用上
 * @param
 * @returns {Promise<*>}
* */
export async function queryServiceDetailsBySorceId( transId, settings ) {
    return await reqUtil.get({
        url: '/publishManagement/queryServiceDetailsBySorceId?sourceId=' + transId,
        settings
    });
}


/*
* 模型id来获取服务输入插件信息的接口
 * @param
 * @returns {Promise<*>}
* */
export async function queryTransMetaServiceInputInfo( transId, settings ) {
    return await reqUtil.post({
        url: root + 'queryTransMetaServiceInputInfo?transId=' + transId,
        settings
    });
}

/*
* 模型id来获取服务api列表
 * @param
 * @returns {Promise<*>}
* */
export async function queryPublishedServiceMetaSelectList( transId, settings ) {
    return await reqUtil.get({
        url:'/publishManagement/queryPublishedServiceMetaSelectList?sourceId=' + transId,
        settings
    });
}

/*
* 根据服务id来获取服务详情
 * @param
 * @returns {Promise<*>}
* */
export async function queryServiceDetails( serviceMetaId, settings ) {
    return await reqUtil.get({
        url:'/publishManagement/queryServiceDetails?id=' + serviceMetaId,
        settings
    });
}

/*
* 模型服务测试
* serviceMetaId
* testDataJson
 * @param
 * @returns {Promise<*>}
* */
export async function testModelService( data, settings ) {
    return await reqUtil.post({
        url:'/publish/testModelService',
        settings
    },data);
}

/*
* 获取服务输出信息
 * @param
 * @returns {Promise<*>}
* */
export async function queryTransMetaServiceOutputInfo(transId, settings ) {
    return await reqUtil.post({
        url: root1 + 'queryTransMetaServiceOutputInfo?transId=' + transId,
        settings
    },{transId});
}
/*
* 模型服务测试异步获取结果
 * @param
 * @returns {Promise<*>}
* */
export async function testModelServiceGetResult(data, settings ) {
    return await reqUtil.post({
        url:'/publish/testModelServiceGetResult',
        settings
    },data);
}

/**
* 服务输入插件数据集生成数据
 * @param
 * @returns {Promise<*>}
* */
export async function prviewDataWithColumn(data, settings ) {
    return await reqUtil.post({
        url:'/editDataSet/prviewDataWithColumn',
        settings
    },data);
}

/**
 * 获取核查输出插件信息
 */
export async function queryDataCheckOutput(tranStepId, settings) {
    return await reqUtil.get({
        url : "/cicada/plugin/serviceCheckOutput/queryData?tranStepId=" + tranStepId ,
        settings
    })
}

/**
 * 核查输出插件-保存
 * tranStepId
 * cicadaFileOutputMeta
 */
export async function savePluginCheckOutput(tranStepId,cicadaFileOutputMeta, settings) {
    return await reqUtil.post({
        url : "/cicada/plugin/serviceCheckOutput/savePlugin?tranStepId=" + tranStepId,
        settings
    },cicadaFileOutputMeta)
}


/**
 * 核查输出插件-获取服务输入插件字段
 */
export async function getCheckColumnCheckOutput(transId, settings) {
    return await reqUtil.get({
        url : "/cicada/plugin/serviceCheckOutput/getCheckColumn?transId=" + transId ,
        settings
    })
}

/**
 * 根据模型id获取核查插件信息
 */
export async function getInfoCheckParam(transId , settings) {
    return await reqUtil.get({
        url : "/cicada/plugin/serviceCheckOutput/getInfoCheckParam?transId=" + transId ,
        settings
    })
}

/**
 * 查询融合规则详情
 */
export async function ruleExectorDetailView(tranStepId , settings) {
    return await reqUtil.get({
        url : "/plugin/ruleExector/detailView?tranStepId=" + tranStepId ,
        settings
    })
}

/**
 * 查询融合离线计算插件详情
 */
export async function sapecialBusinessQueryData(tranStepId , settings) {
    return await reqUtil.get({
        url : "/cicada/cicadaSpecialBusiness/queryData?tranStepId=" + tranStepId ,
        settings
    })
}

/**
 * 查询融合离线计算插件类型下拉框
 */
export async function getBusinessType(settings) {
    return await reqUtil.get({
        url : "/cicada/cicadaSpecialBusiness/getBusinessType",
        settings
    })
}

/**
 * 融合离线计算保存
 */
export async function savePlugin({tranStepId, type, schemaId, schemaName, settings}) {
    return await reqUtil.post({
        url : "/cicada/cicadaSpecialBusiness/savePlugin?tranStepId=" + tranStepId,
        settings
    },{tranStepId, type, schemaId, schemaName})
}

/**
 * 融合离线计算列表
 */
export async function getSchemaList(settings) {
    return await reqUtil.get({
        url : "/cicada/cicadaSpecialBusiness/getSchemaList",
        settings
    })
}