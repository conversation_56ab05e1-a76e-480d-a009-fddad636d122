<!-- 
    linxp
    2021.06.11
-->
<template>
    <div class="main-content" @contextmenu.prevent="onContextShow()">
        <slot name="toolbar"></slot>
        <div class="main-content__wrap">
            <common-panel ref="panel" :max-x="maxX" :max-y="maxY" :min-x="minX" :min-y="minY" :zoom="panel.zoom"
                :x="panel.x" :y="panel.y" :min-zoom="minZoom" :max-zoom="maxZoom" @changeZoom="updatePos"
                :container="rowData.container">
                <slot name="flow"></slot>
            </common-panel>
        </div>

        <div class="main-content__opera">
            <details-info ref="detailsInfo" :rowData="rowData" :nodeData="nodeData" v-on="$listeners"></details-info>
        </div>

        <div class="main-content__footer" :class="{ 'ml50': !showLeftPlugin }">
            <!-- <div>
                 <span :title="menuTip.undo">
                     <i class="dg-iconp icon-back"></i>
                 </span>
                 <span :title="menuTip.redo">
                     <i class="dg-iconp icon-forward"></i>
                 </span>
             </div>-->
            <div>
                <span :title="menuTip.enlarge" @click="handleScale(1)">
                    <i class="dg-iconp icon-enlarge"></i>
                </span>
                <span :title="menuTip.narrow" @click="handleScale(-1)">
                    <i class="dg-iconp icon-narrow"></i>
                </span>
            </div>
            <span :title="menuTip.goCenter" @click="goCanvasCenter()">
                <i class="dg-iconp icon-centre"></i>
            </span>
            <span :title="menuTip.resetZoom" @click="resetCanvasZoom">
                <i class="dg-iconp icon-mapping"></i>
            </span>
            <el-popover v-if="false" placement="top" :offset="120" width="270" trigger="click" :visible-arrow="false">
                <ul class="key-menu">
                    <li v-for="(item, idx) in keyMenu" :key="idx" @click="item.clickFn">
                        <label>{{ item.label }}</label>
                        <div>
                            <span class="" v-for="(order, index) in item.key" :key="index">
                                <span class="key-menu__tag">{{ order }}</span>
                                <span class="key-menu__add" v-if="index < item.key.length - 1">+</span>
                            </span>
                        </div>
                    </li>
                </ul>
                <span slot="reference" :title="menuTip.menu">
                    <i class="dg-iconp icon-key"></i>
                </span>
            </el-popover>
        </div>

        <Contextmenu ref="contextmenu" class="context-menu">
            <ul v-if="isNodeMenu">
                <li v-for="(menu, idx) in nodeMenu" :key="idx" @click="menu.clickFn($event)">
                    <label>{{ menu.label }}</label>
                </li>
            </ul>
            <ul v-else>
                <li v-for="(item, idx) in commomMenus" :key="idx" @click="item.clickFn($event)">
                    <label>{{ item.label }}</label>
                    <span>{{ item.key }}</span>
                </li>
            </ul>
        </Contextmenu>
        <pre-view ref="preview"></pre-view>
    </div>
</template>
<script>
import Contextmenu from "vue-context-menu";
import { globalBus } from "@/api/globalBus";
import commonPanel from "@/components/flowPanel/common-panel"
import { mapGetters } from "vuex";
import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import detailsInfo from "@/projects/DataCenter/views/modeling/dialog/newModel/model/details-info/index"
export default {
    name: "main-content",
    props: {
        rowData: Object,
        nodeData: Object,
        isFocus: Boolean
    },
    components: {
        Contextmenu,
        commonPanel,
        PreView,
        detailsInfo
    },
    data() {
        return {
            panel: {
                x: 0,
                y: 0,
                zoom: 1,
            },
            minZoom: .4,
            maxZoom: 3,
            // 节点菜单
            nodeMenu: [
                /*{
                    label: "执行到此处",
                    value: "runHere",
                    clickFn: () => {
                    }
                },
                {
                    label: "执行该节点",
                    value: "runThis",
                    clickFn: () => {
                    }
                },*/
                {
                    label: "复制",
                    value: "copy",
                    clickFn: this.copyNode
                },
                {
                    label: "预览数据",
                    value: "preview",
                    clickFn: this.preview
                },

                {
                    label: "重命名",
                    value: "reName",
                    clickFn: this.reNameNode
                }, {
                    label: "删除",
                    value: "delete",
                    clickFn: this.deleteNode
                },
                /*{
                    label: "收藏",
                    value: "store",
                    clickFn: () => {
                    }
                },
                {
                    label: "查看输出",
                    value: "output",
                    clickFn: () => {
                    }
                },
                {
                    label: "查看日志",
                    value: "log",
                    clickFn: () => {
                    }
                }*/
            ],
            // 主体菜单
            commomMenus: [
                /*{
                    label: "粘贴",
                    key: "Ctrl+V",
                    value: "cv",
                    clickFn: () => {
                    }
                },
                {
                    label: "撤销",
                    key: "Ctrl+Z",
                    value: "cz",
                    clickFn: () => {
                    }
                },
                {
                    label: "还原",
                    key: "Ctrl+Shift+Z",
                    value: "csz",
                    clickFn: () => {
                    }
                },*/
                {
                    label: "放大",
                    key: "Ctrl++",
                    value: "c+",
                    clickFn: () => this.handleScale(1)
                },
                {
                    label: "缩小",
                    key: "Ctrl+-",
                    value: "c-",
                    clickFn: () => this.handleScale(-1)
                },
                {
                    label: "原始大小",
                    key: "Ctrl+O",
                    value: "co",
                    clickFn: () => this.resetCanvasZoom()
                },
                {
                    label: "自适应",
                    key: "Ctrl+Enter",
                    value: "ce",
                    clickFn: () => this.goCanvasCenter()
                }
            ],

            // keybord
            keyMenu: [
                /*{
                    label: "撤销",
                    key: ["Ctrl", "Z"],
                    clickFn : ()=>{}
                },
                {
                    label: "还原",
                    key: ["Ctrl", "Shift", "Z"],
                    clickFn : ()=>{}
                },
                {
                    label: "删除",
                    key: ["Delete"],
                    clickFn : ()=>{}
                },*/
                {
                    label: "放大",
                    key: ["Ctrl", "+"],
                    clickFn: () => { }
                },
                {
                    label: "缩小",
                    key: ["Ctrl", "-"],
                    clickFn: () => { }
                },
                {
                    label: "原始大小",
                    key: ["Ctrl", "O"],
                    clickFn: () => { }
                },
                {
                    label: "自适应",
                    key: ["Ctrl", "Enter"],
                    clickFn: () => { }
                },
                /*{
                    label: "移动画布",
                    key: ["空格"],
                    clickFn : ()=>{}
                }*/
            ],
            isNodeMenu: false,
            currentNode: null,
            menuTip: {
                undo: "撤销",
                redo: "还原",
                narrow: "缩小",
                enlarge: "放大",
                resetZoom: "原始大小",
                goCenter: "自适应",
                menu: "菜单"
            }
        };
    },
    inject: ['showPlugIn'],
    computed: {
        maxX() {
            return 2 * window.screen.width;
        },
        maxY() {
            return 2 * window.screen.height;
        },
        minX() {
            return -2 * window.screen.width;
        },
        minY() {
            return -8 * window.screen.height;
        },
        ...mapGetters(["planNodes"]),
        showLeftPlugin() {
            return this.showPlugIn();
        }
    },
    methods: {
        setData(data) {
            this.$refs.detailsInfo.setData(data);
        },
        /**
         * 预览
         * */
        preview() {
            const { currentNode } = this;
            // this.$slots.flow[0].componentInstance.reNameNode(currentNode);
            if (!currentNode.checkClick) {
                this.$message.warning("插件初始化未完成，请耐心等待！");
                return;
            }
            if(!currentNode.status) {
                this.$message.warning("插件未完成配置，无法预览");
                return;
            }
            /*  if(this.currentNode.keyWord === 'cicadaStandardSqlInput') this.$refs.preview.previewRdb(this.currentNode.id, this.rowData.transId, );
             else if (this.currentNode.keyWord === 'cicadaStandardSqlOutput') this.$refs.preview.outPutPluginPreviewRdb(this.currentNode.id);
             else */
            this.$refs.preview.show(this.rowData, currentNode);
        },
        /**
         * 重命名
         * */
        reNameNode() {
            const { currentNode } = this;
            this.$slots.flow[0].componentInstance.reNameNode(currentNode);
        },
        /**
         * 适应大小及位置
         * @param zoom 比例
         * @param align 位置
         * */
        goCanvasCenter(zoom, align) {
            const vm = this, { planNodes } = vm;
            let plugWidth = 234, plugHeight = 50; //插件大小
            let maxX = 0, minX = 0, maxY = 0, minY = 0, s_zoom = zoom;
            if (planNodes.length) {
                maxX = Math.max.apply(Math, planNodes.map(item => {
                    return parseInt(item.left)
                }));
                minX = Math.min.apply(Math, planNodes.map(item => {
                    return parseInt(item.left)
                }));
                maxY = Math.max.apply(Math, planNodes.map(item => {
                    return parseInt(item.top)
                }));
                minY = Math.min.apply(Math, planNodes.map(item => {
                    return parseInt(item.top)
                }));
                maxX += 2 * plugWidth;
                minX -= plugWidth / 2;
                maxY += 2 * plugHeight;
                minY -= plugHeight + 50;
            } else {
                s_zoom = 1;
            }
            vm.$refs.panel.zoomToFit({ maxX, minX, maxY, minY }, s_zoom, align);


        },
        /**
         * 重置大小位置
         * */
        resetCanvasZoom() {
            this.goCanvasCenter(1, 'topLeft');
        },
        /**
         * 更新大小，位置
         * @param arg
         * */
        updatePos(arg) {
            const vm = this;
            vm.panel = arg;
            vm.$slots.flow[0].componentInstance.setZoom(arg);
        },
        /**
         * 删除插件节点
         * */
        deleteNode() {
            const { currentNode } = this;
            this.$slots.flow[0].componentInstance.deleteNode(currentNode);
        },
        /**
         * 复制节点
         * */
        copyNode() {
            const { currentNode } = this;
            this.$slots.flow[0].componentInstance.copyNode(this.rowData, currentNode);
        },
        /**
         * 菜单点击事件
         */
        handleRightMenu(item) {

        },
        /**
         * 画布菜单
         * */
        onContextShow(data) {
            // this.isNodeMenu = false;
            // this.$refs.contextmenu.open();
        },
        /**
         * 插件菜单
         * */
        onShowNodeMenu(data) {
            if (!this.isFocus) return;
            this.currentNode = this.copyArrayObj(data);
            this.isNodeMenu = true;
            this.$refs.contextmenu.open();
        },
        /**
         * 手动缩放
         * @param num
         */
        handleScale(num) {
            let operFn = num < 0 ? 'zoomIn' : 'zoomOut';
            let { width, height } = this.$el.getBoundingClientRect();
            this.$refs.panel[operFn]({ x: width / 2, y: height / 2 });
        },
        resetNodeData() {
            this.$emit("resetNodeData")
        }
    },
    created() {
        globalBus.$on("showNodeMenu", this.onShowNodeMenu);
    },
    mounted() {
        const vm = this;
        let div = document.querySelector(`.${vm.rowData.container} .main-content .main-content__wrap`);
        div && div.addEventListener("click", this.resetNodeData);
    },
    beforeDestroy() {
        globalBus.$off("showNodeMenu", this.onShowNodeMenu);
        let div = document.querySelector('.main-content .main-content__wrap');
        div && div.removeEventListener("click", () => { });
    }
};
</script>
<style lang="less" scoped>
.main-content {
    position: relative;
    height: 100%;

    &__wrap {
        display: block;
        flex-direction: column;
        align-items: center;
        height: 100%;
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
    }

    &__row {
        display: flex;
        align-items: center;
    }

    &__opera {
        position: absolute;
        bottom: 0;
        left: 0px;
        display: flex;
        width: 100%;
        align-items: center;
    }

    .node {
        display: flex;
        align-items: center;
        border-radius: 2px;
        margin: 32px;
        cursor: pointer;
        position: relative;
        background-color: #fff;

        span.node__icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 34px;
            height: 34px;
        }

        p {
            padding: 0 14px;
            min-width: 115px;
            box-sizing: border-box;
        }

        &--primary {
            border: 1px solid rgba(0, 136, 255, 0.35);

            span.node__icon {
                background: rgba(#0088ff, 0.12);

                i {
                    color: #1890ff;
                }
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(0, 136, 255, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(0, 136, 255, 0.45);
            }
        }

        &--info {
            border: 1px solid rgba(250, 173, 20, 0.35);

            span.node__icon {
                background-color: rgba(#faad14, 0.12);

                i {
                    color: #faad14;
                }
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(250, 173, 20, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(250, 173, 20, 0.45);
            }
        }

        &--success {
            border: 1px solid rgba(82, 196, 26, 0.35);

            span.node__icon {
                background-color: rgba(#52c41a, 0.12);

                i {
                    color: #52c41a;
                }
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(82, 196, 26, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(82, 196, 26, 0.45);
            }
        }

        &--tip-icon {
            display: flex;
            align-items: center;
            position: absolute;
            top: -7px;
            right: -7px;
            width: 14px;
            height: 14px;
            z-index: 9;
            background-color: #fff;
            border-radius: 50%;

            i {
                font-size: 14px;
            }

            .icon-tick {
                color: #52c41a;
            }

            .icon-exclamatory {
                color: #faad14;
            }

            .icon-cross {
                color: #fa4040;
            }
        }
    }

    &__footer {
        position: absolute;
        top: 50px;
        left: 0px;
        display: flex;
        align-items: center;

        div {
            margin: 0 4px;
            display: flex;
            justify-content: center;

            span {
                margin: 0;

                &:first-child {
                    position: relative;

                    &::before {
                        content: "";
                        position: absolute;
                        top: 4px;
                        right: 0;
                        width: 1px;
                        height: 16px;
                        background-color: rgba(0, 0, 0, 0.08);
                    }
                }
            }
        }

        span {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #ffffff;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
            border-radius: 2px;
            margin-left: 4px;
            cursor: pointer;

            i {
                color: rgba(0, 0, 0, 0.65);
            }

            &:hover {
                i {
                    color: #1890ff;
                }
            }
        }
    }
}

/deep/ .context-menu {
    .ctx-menu {
        width: 200px;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
        border-radius: 2px;

        li {
            line-height: 36px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 14px;
            box-sizing: border-box;
            font-size: 14px;
            cursor: pointer;

            &:nth-child(4) {
                border-bottom: 1px solid rgba(0, 0, 0, 0.15);
            }

            label {
                color: rgba(0, 0, 0, 0.85);
                cursor: pointer;
            }

            span {
                color: rgba(0, 0, 0, 0.45);
                cursor: pointer;
            }

            &:hover {
                background: rgba(#0088ff, 0.09);

                label {
                    color: rgba(#0088ff, 0.85);
                }

                span {
                    color: rgba(#0088ff, 0.85);
                }
            }
        }
    }
}

/deep/ .node-menu {
    li {
        &:nth-child(3) {
            border-bottom: none !important;
        }

        &:nth-child(2) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.15);
        }

        &:nth-child(5) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.15);
        }
    }
}
</style>
