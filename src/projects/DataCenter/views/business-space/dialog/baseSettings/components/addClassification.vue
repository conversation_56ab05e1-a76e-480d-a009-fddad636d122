<template>
<!--  新建分类  -->
    <div class="addClassification">
        <el-form :model="form" ref="form" label-position="right" label-width="140px">
            <el-form-item v-for="(item , key) in layoutForm"
                          :key="key"
                          :prop="key"
                          :rules="item.rules"
                          :label="`${item.label}:`"
            >
                <template v-if="item.limit">
                    <el-input :type="item.type"
                              v-model="form[key]"
                              :maxlength="item.maxLength"
                              :placeholder="item.placeholder"
                              v-input-limit:[item.limit]
                    ></el-input>
                </template>
                <el-input v-else :type="item.type"
                          v-model="form[key]"
                          :maxlength="item.maxLength"
                          :placeholder="item.placeholder"
                          resize="none"
                          rows="5"
                          show-word-limit
                ></el-input>
            </el-form-item>
        </el-form>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "addClassification" ,
    mixins:[commonMixins],
    props : {
        typeName : String
    },
    data(){
        return {
            form : {
                name : "",
                code : "",
                memo : "",
            } ,
            layoutForm : {
                name : {
                    label : `${this.typeName}分类名称` ,
                    type : "text",
                    limit : "fieldName" ,
                    maxLength : 30 ,
                    placeholder : `请输入${this.typeName}分类名称` ,
                    rules : [
                        {required : true , message : `请输入${this.typeName}分类名称` ,trigger : ["change" , "blur"]}
                    ]
                },
                code : {
                    label : "标识编码" ,
                    type : "text" ,
                    placeholder : "请输入标识编码" ,
                    limit : "fieldCode" ,
                    maxLength : 30,
                    rules : [
                        {required : true , message : "请输入标识编码" ,trigger : ["change" , "blur"]}
                    ]
                },
                memo : {
                    label : "备注" ,
                    type : "textarea" ,
                    placeholder: "请输入备注信息" ,
                    maxLength : 300,
                    rules :[]
                }
            }
        }
    },
    methods : {
        close(){
            this.$emit("close");
        },
        submit(){
            const vm = this;
            vm.$refs.form.validate(valid=>{
                if(valid){
                    vm.$emit("submit" , vm.form);
                    vm.close();
                }
            })
        },
    }
}
</script>

<style scoped>

</style>
