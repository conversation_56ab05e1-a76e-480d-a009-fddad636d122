<template>
        <el-drawer
            class="test-dialog"
            title="测试API"
            width="80%"
            height="100%"
            :visible.sync="visible"
            :before-close="clear_data"
            direction="rtl"
            custom-class="set-drawer"
            append-to-body
            :size="950"
            :show-close="true"
            :close-on-press-escape="false"
            ref="drawer"
            v-if="showParams"
        >
            <div class="set-drawer__content">
                <!--参数配置弹窗内容页-->
                <div class="set-drawer__main">
                    <test ref="group" v-if="showParams && callRequest" v-bind="$attrs" :row="row" @getAsyncInfo="getAsyncInfo"></test>
                    <!--<div slot="title"><span class="el-dialog__title">测试API</span> <help-document :code="documentCode"></help-document></div>-->
                    <ApiTestPage :pageId="row.serviceId" v-if="!callRequest"></ApiTestPage>
                </div>
                <div class="set-drawer__footer" v-if="showParams && callRequest" >
                    <el-button size="mini" @click="clear_data">取消</el-button>
                    <el-button size="mini" type="primary" @click="subscribe" v-if="row.serviceType === '比对订阅'">订阅测试</el-button>
                    <el-button size="mini" type="primary" @click="testClick">开始测试</el-button>
                </div>
            </div>
            <subscribeDialog ref="subscribeDialog" v-if="showSubscribe" @getAsyncInfo="getAsyncInfo"></subscribeDialog>
        </el-drawer>
        
</template>

<script>
import test from "./test"
import subscribeDialog from './subscribeDialog'
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import ApiTestPage from "@/components/common/api-test-page/ApiTestPage";

export default {
    name: "testDialog",
    mixins : [servicesMixins, commonMixins],
    components: {test,subscribeDialog ,ApiTestPage},
    props:{
        callRequest:{
            type: Boolean,
            default: false,
        },
    },
    data(){
        return {
            visible :false,
            showParams : false,
            detailInfo : [],
            row:[],
            documentCode:'',
            showSubscribe:false,
            typeList : {
                "模型分析" : "modelService",
                "数据碰撞" : "dataCollision",
                "比对订阅" : "compareService",
                "AI算法" : "aiService",
                "信息核查" : "informationVerification",
                "逻辑服务" : "logicService",
                "推荐服务" : "recommendService",
                "数据查询" : "dataService",
            }
        }
    },
    methods: {
        clear_data(){
            this.visible = false;
            this.showParams = false;
            this.showSubscribe = false;
            if(this.row.refresh){
                this.$emit('refreshTable')
            }
        },
        show(row,code){
            this.documentCode = code;
            this.row = row;
            this.visible = true;
            this.showParams = true;
            this.showSubscribe = true;
        },
        //测试
        testClick(){
            this.$refs.group.testClick();
        },
        //订阅
        subscribe(){
            this.$refs.subscribeDialog.show(this.row)
        },
        getAsyncInfo(taskId){
            this.$emit('getAsyncInfo',taskId);
        }
    },
}
</script>

<style scoped lang="less">
    /deep/.ce-common_dialog > .el-dialog__body{
        height: calc(75vh - 114px);
    }
.set-drawer {
    &__content {
        height: calc(100vh - 86px);
    }

    &__main {
        height: calc(100% - 2rem - 21px);
        padding: 20px 14px;
        box-sizing: border-box;
        overflow: auto;
    }

    &__footer {
        padding: 10px 20px;
        text-align: right;
        border-top: 1px solid #ddd;
    }
}
</style>
