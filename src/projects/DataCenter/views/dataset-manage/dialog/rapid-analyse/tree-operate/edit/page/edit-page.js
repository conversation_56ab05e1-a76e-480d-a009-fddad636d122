

export default {
    name: "editPage" ,
    props : {
        node : Object
    },
    computed : {
        columnName (){
            if(this.node.indexType === 'MEASURE'){
                return '度量';
            }else if(this.node.indexType === 'DIMENSION'){
                return '维度';
            }
        } ,
        canEdit(){
            return this.node.canEdit === false;
        }
    },
    data(){
        return {
            list_form : {
                name : {
                    label : "" ,
                    maxlength : 50 ,
                    disabled : this.canEdit ,
                    placeholder : "请输入显示名",
                    tip : "名称只能由中英文、数字及下划线、斜线、反斜线、竖线、小括号、中括号组成，不超过50个字符"
                },
                physics :{
                    label : "物理字段名",
                    disabled :true ,
                    placeholder :"" ,
                    maxlength : 100
                },
                columnAlias :{
                    label : "别名" ,
                    disabled : this.canEdit ,
                    placeholder : "请输入别名(限100个字符)",
                    maxlength : 100
                },
                memo :{
                    label : "备注信息",
                    disabled : this.canEdit ,
                    placeholder : "请输入备注信息(限100个字符)",
                    maxlength : 100
                }

            } ,
            form :{
                id : "" ,
                name : "" ,
                physics : "" ,
                memo : "" ,
                columnAlias : ""
            } ,
            rules : {
                name : [
                    {required : true , message : "显示名称不能为空" , trigger : ['blur' , 'change']} ,
                    {validator : this.checkedCommon , reg : /^[a-zA-Z0-9\\\/\|\(\)\[\]_\u4e00-\u9fa5]+$/ ,message : "显示名称格式不符合规则" , trigger : ["blur" , "change"]}
                ],
                columnAlias : [
                    {validator : this.checkedCommon , reg : /^[a-zA-Z][A-Za-z_0-9]*$/ ,message : "别名由字母、数字、下划线组成且不以数字开头" , trigger : ["blur" , "change"]}
                ]
            }
        }
    },
    methods : {
        init(){
            const vm = this;
            vm.form.id = vm.node.id;
            vm.form.name = vm.node.label;
            vm.form.physics = vm.node.code;
            vm.form.memo = vm.node.memo ?  vm.node.memo : vm.node.label;
            vm.form.columnAlias = vm.node.columnAlias ?  vm.node.columnAlias : "";
        },
        validate(...arg){
            this.$refs.form.validate(...arg);
        }
    },
    created (){
        this.init();
    }
}