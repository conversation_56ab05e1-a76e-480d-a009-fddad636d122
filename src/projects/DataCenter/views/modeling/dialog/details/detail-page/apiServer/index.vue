<template>
    <div class="api" v-loading="settings.loading">
        <div class="api-left" v-if="apiList.length && !serviceDetail.serviceId">
            <ul v-if="apiList.length">
                <li v-for="(item , index) in apiList"
                    :class="{'active': chooseInfo.id == item.id}"
                    :key="index"
                    @click="clickLi(item)"
                    :title="item.name"
                    class="poi"
                >
                    {{item.name}}
                </li>
            </ul>
            <no-data v-else title="暂无数据" top="5%" src=""></no-data>
        </div>
        <div class="api-content">
            <div v-if="apiList.length || serviceDetail.serviceId">
                <p>
                    <span>基本信息</span>
                    <dg-button size="mini" @click="planDetails" v-if="['1','5','7'].includes(modelInfo.serviceType) || ((modelInfo.serviceType==='6' && detailsDataInfo.singleOrManyTable === '2'))">方案详情</dg-button>
                    <dg-button size="mini" @click="testClick" v-if="rightJudge('test')">测试</dg-button>
                    <dg-button size="mini" @click="createAPI('edit')" v-if="rightJudge('update') && !isOtherUser">编辑</dg-button>
                    <dg-button type="primary" size="mini" @click="createAPI('add')" v-if="rightJudge('create') && !isOtherUser">生成API</dg-button>
                </p>
                <div class="api-content_info">
                    <div v-for="(item, index) in resultInfo" :key="index" class="api-content_info_div">
                        <label>{{item.label}}</label>
                        <div @click="lookDetail(item)" :class="{'api-content_info_look': item.isClick}">
                            <span class="valueClass">
                                <el-tooltip class="item" effect="dark" :content=" item.value === 0 || item.value ? item.value.toString() : ''" placement="bottom">
                                        <span class="valueUrl">{{item.value}}</span>
                                </el-tooltip>
                                <i class="el-icon-document-copy poi" @click.stop="urlCopy(item.value)" v-if="item.isCopy" title="复制"></i>
                            </span>
                        </div>
                        <!--<div @click="lookDetail(item)" :class="{'api-content_info_look': item.isClick}" :title="item.value">{{item.value}}</div>-->
                    </div>
                </div>
                <!--<div v-if="['1','5','7', '2'].includes(modelInfo.serviceType)">-->
                    <!--<p>请求参数</p>-->
                    <!--<parameter :tableList="requestList" :count="requestCount" :type="'detail'" v-if="showParams" :serviceType="modelInfo.serviceType"></parameter>-->
                    <!--<p>返回参数</p>-->
                    <!--<parameter :par="'back'" :tableList="backList" :count="backCount" v-if="showParams" :serviceType="modelInfo.serviceType"></parameter>-->
                <!--</div>-->
                <parameter-new  v-if="showParams && detailsDataInfo" :detailsDataInfo="detailsDataInfo"></parameter-new>
            </div>
            <div  v-else>
                <dg-button type="primary" size="mini" @click="createAPI('add')" v-if="rightJudge('create')">生成API</dg-button>
                <no-data title="暂无数据" top="5%"></no-data>
            </div>
        </div>

        <!--生成api-->
        <create-api ref="createAPI" @changePage="changePage" @getApiList="refresh" :type="addOrEditType" :modelInfo="modelInfo" :scriptId="scriptId" :entrance="entrance" :isAi="isAi" v-if="showCreateAPI"/>
        <!--查看api详情-->
        <api-detail ref="apiDetail" :modelInfo="info"/>
        <!--测试-->
        <testDialog ref="testDialog" @getAsyncInfo="getAsyncInfo"></testDialog>
    </div>
</template>

<script>
    import createApi from "@/projects/DataCenter/views/serviceManage/dialog/createAPI"
    import testDialog from "@/projects/DataCenter/views/serviceManage/dialog/testDialog"
    import NoData from "@/components/no-data";
    import apiDetail from "./apiDetail"
    import parameterNew from "./parameterNew"
    import parameter from "./parameter"
    import {mapGetters} from "vuex"
    import {globalBus} from "@/api/globalBus";
    import $right from "@/assets/data/right-data/right-data"
    export default {
        name: "apiServer",
        computed: {
            ...mapGetters(["userRight"]),
            rights() {
                let rights = [];
                if (this.userRight) {
                    this.userRight.forEach(r => {
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            },
        },
        components: {
            parameter,
            parameterNew,
            createApi,
            apiDetail,
            NoData,
            testDialog,
        },
        props : {
            info : Object ,
            type: String,
            isAi : {
                type : Boolean,
                default  : false
            },
            serviceDetail: {  //模型服务详情
                type : Object,
                default  : function(){
                    return {}
                }
            },
            modelTest: {
                type : Boolean,
                default  : false
            },
            isShare: {
                type : Boolean,
                default  : false
            },
            isService:{
                type : Boolean,
                default  : false
            },
            isOtherUser: Boolean, // 是否他人用户
        },
        data() {
            return {
                apiList: [],
                infoList: {
                    name: { label: 'API名称：', value: ''},
                    type: { label: 'API类型：', value: ''},
                    method: { label: '调用方式：', value: ''},
                    address: { label: '调用地址：', value: '',isCopy:true ,},
                    requestUrl: { label: '发送请求地址：', value: '', isCopy:true ,},
                    resultUrl: { label: '获取结果地址：', value: '', isClick:true ,isCopy:true ,},
                    requestMethod: { label: '请求方法：', value: ''},
                    version: { label: '版本号：', value: ''},
                    describe: { label: 'API描述：', value: ''},
                },
                aiInfoList :{
                    name: { label: 'API名称：', value: ''},
                    type: { label: 'API类型：', value: ''},
                    address: { label: '调用地址：', value: '', isCopy:true ,},
                    requestMethod: { label: '请求方法：', value: ''},
                    version: { label: '版本号：', value: ''},
                    describe: { label: 'API描述：', value: ''},
                },
                resultInfo: {},
                chooseInfo:{},
                requestList: [],
                requestCount: 0,
                backListCopy: [
                    { code: 'success' , name: 'success' , memo:'请求是否成功，true成功，false失败', data_type:'String', import: '',children : []  },
                    { code: 'requestId' , name: 'requestId' , memo:'请求批次ID', data_type:'String', import: '',children : []  },
                    { code: 'statusCode' , name: 'statusCode' , memo:'响应状态码', data_type:'String', import: '',children : []  },
                    { code: 'msg' , name: 'msg' , memo:'响应信息', data_type:'String', import: '',children : []  },
                    { code: 'pageInfo' , name: 'pageInfo' , memo:'分页信息', data_type:'Object', import: '',children : []},
                ],
                backList: [],
                backCount: 0,
                showParams : false,
                serviceInfo:{},//服务详情
                settings: {
                    loading: false,
                },
                taskId: '', //异步获取详情id,测试后才会生成
                entrance: this.isAi ? 'ai' : 'model',
                scriptId: '',
                addOrEditType:'add', //新增或编辑
                modelInfo:this.info,
                showCreateAPI:false,
                serviceTypeCode:{
                    '数据查询': '4' ,
                    '信息核查': '6' ,
                    '比对订阅': '5' ,
                    '数据碰撞': '7' ,
                    '模型分析': '1' ,
                    'AI算法': '2' ,
                },
                detailsDataInfo:'',
                isKFK: false,
            }
        },
        mounted() {
            if(this.serviceDetail && this.serviceDetail.serviceId){
                this.clickLi({id:this.serviceDetail.serviceId})
            }
            else{
                this.getApiList();
                if(!this.isAi)
                    this.getDataSets();
            }
        },
        methods: {
            changePage(){
                this.$emit('changePage');
            },
            refresh(id){
                if(this.serviceDetail && this.serviceDetail.serviceId){
                    this.clickLi({id:id})
                }
                else{
                    this.getApiList();
                }
            },
            rightJudge(type){
                const vm = this;
                let judgeType = {
                    create : function (){ //生成API权限判断
                        return vm.createAPIRightJudge();
                    },
                    update : function (){ //编辑权限判断
                        return vm.updateRightJudge();
                    },
                    test : function (){ //测试权限判断
                        return vm.testAPIRightJudge();
                    },
                };
                return judgeType[type]();
            },
            createAPIRightJudge(){
                if (this.isAi || this.entrance == 'ai') { //AI建模API服务生成API权限判断
                    return this.rights.indexOf('AIModelingBuildApi') > -1 && !this.serviceDetail.serviceId && !this.modelTest && !this.isShare;
                }else if(this.entrance == 'model'){
                    return this.rights.indexOf('modelServiceGenerateAPI') > -1 && !this.serviceDetail.serviceId && !this.modelTest && !this.isShare;
                }
                else {
                    return this.rights.indexOf('serviceManagementGenerateAPI') > -1 && !this.serviceDetail.serviceId && !this.modelTest && !this.isShare;
                }
            },
            updateRightJudge(){
                if (this.isAi || this.entrance == 'ai' || this.entrance == 'model') { //AI建模和模型API服务编辑权限判断
                    return this.rights.indexOf('serviceManagementedit') > -1 && !this.modelTest && !this.isShare
                }
                else {
                    return this.rights.indexOf('serviceManagementGenerateAPI') > -1 && !this.modelTest && !this.isShare;
                }
            },
            testAPIRightJudge(){
                if (this.isAi || this.entrance == 'ai') { //AI建模API服务测试权限判断
                    return this.rights.indexOf('AIModelingTestApi') > -1;
                }else if(this.entrance == 'model'){
                    return this.rights.indexOf('modelServiceTest') > -1;
                }
                else {
                    return this.rights.indexOf('serviceManagementTest') > -1;
                }
            },
            setParam(type){
                if(['模型分析','数据碰撞','比对订阅'].includes(type)){
                    this.entrance = 'model';
                    this.scriptId = '';
                    //深拷贝防止数据变化
                    this.resultInfo = JSON.parse(JSON.stringify(this.infoList))
                }
                else if(type === 'AI算法'){
                    this.entrance = 'ai';
                    this.scriptId = this.info.transId;
                    this.resultInfo = Object.assign({}, this.aiInfoList);
                }
                else{
                    this.scriptId = '';
                    this.resultInfo = Object.assign({}, this.aiInfoList);
                }
            },
            /**
             * 左侧
             */
            async getApiList(id){
                let services = this.$services('modeling');
                this.apiList = [];
                let type = this.isAi ? 'ai' : '';
                await services.getApiList(this.info.transId, type).then(res =>{
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        result && result.forEach(element => {
                            this.apiList.push({
                                name : element.name,
                                id : element.id,
                            })
                        });
                    }
                })
                if(this.apiList.length){
                    this.chooseInfo = id ? this.apiList.find(n=>n.id == id) : this.apiList[0];
                    this.clickLi(this.chooseInfo)
                }
            },
            //生成api、编辑
            createAPI(val){
                this.addOrEditType = val;
                this.modelInfo.memo = this.serviceInfo.memo;
                if(this.entrance === 'ai'){
                    this.modelInfo.modelName = this.serviceInfo.apiName;
                }
                if(!this.scriptId && this.isAi ) this.scriptId = this.modelInfo.transId;
                if(this.isAi){
                    this.modelInfo.paramList = this.serviceInfo.inputParams && this.serviceInfo.inputParams[0] ? this.serviceInfo.inputParams[0].children : [];
                }
                this.showCreateAPI = true;
                this.$nextTick(()=>{
                    let info = {}
                    if(val === 'edit'){
                        info = {serviceType: this.serviceInfo.apiType,serviceMetaId:this.serviceInfo.serviceMetaId}
                    }
                    this.$refs.createAPI.show({},'',info,this.serviceInfo.classifyId);
                })
            },
            //测试
            testClick(){
                if(this.serviceInfo.status!= '1'){
                    return this.$message.warning('请先启用服务')
                }
                let par = Object.assign([],this.requestList);
                par.forEach(n=>{
                    this.setParamName(n);
                })
                let info = {
                    paramList: par,
                    serviceMetaId: this.serviceInfo.serviceMetaId,
                    serviceType: this.serviceInfo.apiType,
                    interfaceChineseName : this.serviceInfo.apiName,
                    requestPath : this.serviceInfo.requestPath,
                    serviceId : this.serviceInfo.servicePublishId,
                    isSync: this.serviceInfo.async,
                }
                let functionCode = '';
                if(this.serviceInfo.apiType === '数据查询' || this.serviceInfo.apiType === '信息核查'){
                    info.ginsengList = this.backList;
                }
                if(['模型分析','数据碰撞','比对订阅'].includes(this.serviceInfo.apiType)){
                    functionCode = 'modelServiceTest'
                }
                if(this.serviceInfo.apiType === 'AI算法'){
                    functionCode = 'AIModelingTestApi'
                }
                this.$refs.testDialog.show(info , functionCode);
            },
            setParamName(info){
                info.paramName = info.name || info.code;
                info.paramCode = info.code;
                info.isMust = info.is_must;
                info.paramId = info.id;
                if(info.children.length){
                    info.children.forEach(p=>{
                        this.setParamName(p);
                    })
                }
            },
            //字符串截取
            getCaption(obj){
                let index = obj.lastIndexOf("\.");
                obj = obj.substring(index+1,obj.length);
                return obj;
            },
            setDataType(info){
                info.data_type = this.getCaption(info.data_type);
                info.paramCode = info.code;
                info.dataType = info.data_type;
                info.isMust = info.is_must;
                info.desensitization = info.desen_type;
                if(info.children && info.children.length){
                    info.children.forEach(n=>{
                        this.setDataType(n);
                    })
                }
            },

            async  clickLi(item) {
                const vm = this;
                await vm.getServiceOutput();
                vm.taskId = '';
                vm.settings.loading = true;
                vm.showParams = false;
                let services = vm.$services('modeling');
                vm.requestList = [];
                vm.chooseInfo = item;
                await services.getServiceDetailById(item.id, vm.settings).then(async res =>{
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        vm.setParam(result.apiType);
                        vm.serviceInfo = JSON.parse(JSON.stringify(result)) ;
                        vm.modelInfo.serviceType = vm.serviceTypeCode[result.apiType];
                        vm.modelInfo.apiServiceId = vm.type === 'modeling' ?  result.serviceMetaId : result.servicePublishId;
                        vm.modelInfo.serviceMetaId = result.serviceMetaId;
                        if( ['4','6'].includes(vm.modelInfo.serviceType) && vm.modelInfo.serviceMetaId && vm.detailsDataInfo.singleOrManyTable != '2'){
                            await vm.queryServiceDetails(result.serviceMetaId);
                        }


                        if(['1','5','7'].includes(vm.modelInfo.serviceType) && result.async === 'true' || (vm.modelInfo.serviceType === '6' && vm.detailsDataInfo.singleOrManyTable === '2')){
                            vm.resultInfo =  JSON.parse(JSON.stringify(vm.infoList));
                            delete vm.resultInfo.address;
                            vm.resultInfo.requestUrl.value = result.requestPath;
                            vm.resultInfo.method.value = '异步调用';
                            let url = this.serviceInfo.requestPath;
                            let index = url.lastIndexOf("\/");
                            let str = url.substring(0,index+1);
                            vm.resultInfo.resultUrl.value = str + 'getValue';
                            vm.serviceInfo.resultUrl = this.resultInfo.resultUrl.value;
                            vm.requestList = result.inputParams[0].children || [];
                            vm.requestList = vm.requestList.filter(n=>{return n.code != "requestId";})
                            vm.backList = result.outputParams[0].children || [];
                            let operatorDataSet = vm.backList.findIndex(n=>n.code === 'operatorDataSet');
                            if(operatorDataSet != -1){
                                vm.backList.splice(operatorDataSet, 1);
                            }
                           if(vm.isKFK) delete vm.resultInfo.resultUrl;
                        }
                        else if(['1','5','7'].includes(vm.modelInfo.serviceType) && result.async != 'true'){
                            vm.resultInfo.address.value = result.requestPath;
                            delete vm.resultInfo.resultUrl;
                            delete vm.resultInfo.requestUrl;
                            vm.requestList = result.inputParams[0].children || [];
                            vm.requestList = vm.requestList.filter(n=>{return n.code != "requestId";})
                            vm.backList = result.outputParams[0].children || [];
                            vm.resultInfo.method.value = '同步调用';
                        }else if(['4','6'].includes(vm.modelInfo.serviceType) || (vm.modelInfo.serviceType === '6' && vm.detailsDataInfo.singleOrManyTable != '2')){
                            if(result.outputParams && result.outputParams.length){
                                result.outputParams.forEach(n=>{
                                    n.data_type = n.extended_type;
                                    n.paramCode = n.code;
                                    n.paramName = n.name || n.code;
                                })
                            }
                            vm.requestList = result.inputParams || [];
                            vm.backList = result.outputParams|| [] ;
                            vm.resultInfo.address.value = result.requestPath;
                        }
                        else {
                            vm.resultInfo.address.value = result.requestPath;
                            vm.requestList = result.inputParams[0].children || [];
                            vm.backList = result.outputParams[0].children || [];
                        }

                        vm.requestList.forEach(n=>{
                            vm.setDataType(n);
                        })

                        vm.backList.forEach(n=>{
                            vm.setDataType(n);
                        })

                        if(!['4','6'].includes(vm.modelInfo.serviceType) || (vm.modelInfo.serviceType == '6' && vm.detailsDataInfo.singleOrManyTable === '2')){
                            vm.detailsDataInfo = {
                                requestList: vm.requestList,
                                backList: vm.backList,
                                serviceTypeCode : vm.modelInfo.serviceType,
                                singleOrManyTable: vm.modelInfo.singleOrManyTable,
                            }
                        }
                        vm.resultInfo.name.value = result.apiName;
                        vm.resultInfo.type.value = result.apiType;
                        vm.resultInfo.requestMethod.value =  result.requestType,
                        vm.resultInfo.version.value =  result.version,
                        vm.resultInfo.describe.value =  result.memo;
                        vm.showParams = true;
                    }
                })
            },
            //异步查看详情
            lookDetail(info){
                if(info.isClick) {
                    let data = {
                        apiName: this.serviceInfo.apiName,
                        resultUrl: this.serviceInfo.resultUrl,
                        requestType: this.serviceInfo.requestType,
                        serviceMetaId: this.serviceInfo.serviceMetaId,
                        servicePublishId: this.serviceInfo.servicePublishId,
                        serviceType: this.serviceTypeCode[this.serviceInfo.apiType],
                        sourceId: this.serviceInfo.sourceId,
                        outputParams: this.serviceInfo.outputParams,
                        serviceId: this.serviceInfo.servicePublishId,
                    }
                    this.$refs.apiDetail.show(data, this.taskId);
                }
            },
            //获取服务输出信息
            getOutInfo(){
                const vm = this;
                let services = vm.$services('servicesServices');
                services.queryTransMetaServiceOutputInfo(vm.serviceInfo.sourceId).then(res =>{
                    if (res.data.status === 0) {
                        let column = res.data.data.serviceOutputColumns.map(n=>{
                            return { code: n.columnCode , name: n.columnName , memo:n.columnName, data_type:n.columnType, import: '',children : []}
                        });
                        let dataSet = { code: 'operatorDataSet' , name: 'operatorDataSet' , memo:'响应数据集合', data_type:'List', import: '',children : column  };
                        vm.backList.push(dataSet);
                    }
                })
            },
            //判断是否有数据集
            getDataSets(){
                const vm = this;
                let services = vm.$services("modeling");
                services.getDataSets(this.modelInfo.transId).then(res=> {
                    if (res.data.status === 0) {
                        this.modelInfo.dataSetList = res.data.data || []
                    }
                    else{
                        this.modelInfo.serviceType = '1';
                        this.modelInfo.dataSetList = [];
                    }
                })
            },
            //异步地址截取
            getAsyncInfo(taskId){
                this.taskId = taskId;
            },
            urlCopy(value){
                //创建一个textarea节点
                let textarea = document.createElement('textarea');
                //为textarea节点添加style属性
                textarea.setAttribute('style','position:fixed;top:0;left:0;opacity:0;z-index:-10;');
                //把要复制的文本添加到textarea节点中
                let text = document.createTextNode(value);
                textarea.appendChild(text);
                //把textarea节点添加到body节点中
                document.body.appendChild(textarea);
                //选中textarea节点的文本内容
                textarea.select();
                //执行复制命令
                if(document.execCommand('copy')){
                    this.$message.success("复制成功")
                }else{
                    this.$message.success("复制失败")
                }
                //复制完成后从body节点删除textarea节点
                document.body.removeChild(textarea);
            },
            /**
             * api类型为模型分析或数据查询 根据服务id获取服务详情 更新时
             */
            async queryServiceDetails(serviceMetaId){
                const vm = this;
                let services = vm.$services("servicesServices");
                vm.settings.loading = true;
                await services.queryServiceDetails(serviceMetaId, vm.settings).then(res=> {
                    if (res.data.status === 0) {
                        res.data.data.serviceTypeCode = vm.serviceTypeCode[res.data.data.serviceType];
                        vm.detailsDataInfo = res.data.data;
                        vm.modelInfo.singleOrManyTable = vm.detailsDataInfo.singleOrManyTable;
                    }
                });
            },
            //查看方案详情
            async planDetails(){
                const vm = this;
                if(['1','5','7'].includes(vm.modelInfo.serviceType) || (vm.modelInfo.serviceType == '6' && vm.modelInfo.singleOrManyTable == '2')){
                    if(vm.entrance === 'model'){
                        globalBus.$emit('changeModelNodeShowState');
                    }
                    let layer = vm.$dgLayer({
                        title : '方案详情' ,
                        content :require("../modelSetting") ,
                        maxmin:false,
                        move:false,
                        noneBtnField: true,
                        area : ['100%' , '100%'],
                        props : {
                            info:{
                                transId: vm.serviceInfo.sourceId || vm.modelInfo.modelId,
                                modelName: vm.modelInfo.sourceName || vm.modelInfo.modelName,
                                isService:vm.isService
                            },
                        },
                        cancel:function(){
                            globalBus.$emit('changeModelNodeShowState');
                        }
                    })
                }
                else{
                    let conditionInfo = [];
                    vm.detailsDataInfo.manyJoinMetas.forEach((n, i)=>{
                        conditionInfo.push({label:n.leftStepName ,name:n.leftStepName , id: n.leftStepId});
                        if(i === vm.detailsDataInfo.manyJoinMetas.length - 1){
                            conditionInfo.push({label:n.rightStepName , name:n.rightStepName ,id: n.rightStepId});
                        }
                    })
                    let layer = vm.$dgLayer({
                        title : '方案详情' ,
                        content :require("../../../../../serviceManage/createAPI/selectTable"),
                        maxmin:false,
                        move:false,
                        noneBtnField: false,
                        area : ['1200px' , '80vh'],
                        props : {
                            tableDataList: conditionInfo,
                            info: {
                                manyJoinMetas:vm.detailsDataInfo.manyJoinMetas,
                                dbType:vm.detailsDataInfo.dbType,
                                conditionInfo:vm.detailsDataInfo.encasulationJudgContion
                            },
                            type: 'edit',
                        },
                    })
                }
            },
            async getServiceOutput(){
                const vm = this;
                let services = vm.$services("serviceManage");
                await services.getServiceOutput().then(res=> {
                    if (res.data.status === 0) {
                        vm.isKFK = res.data.data === "kafka-storage"
                    }
                });
            }
        }
    }
</script>

<style scoped lang="less">
    .api {
        height: 100%;
        display: flex;
        &-left {
            width: 240px;
            background-color: #fff;
            margin-right:10px;
            overflow-y: auto;
            ul li{
                line-height: 40px;
                padding: 0 20px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            ul li.active{
                background-color: rgba(24,144,255,.12);
                color: #1890ff;
            }
        }
        &-content {
            flex: 1;
            padding:10px;
            background-color: #fff;
            overflow-y: auto;
            p {
                font-size: 16px;
                font-weight: bold;
                button {
                    float: right;
                    margin-left: 5px;
                }
            }
            &_info {
                display: flex;
                flex-wrap: wrap;
                &_div {
                    width: 50%;
                    line-height: 35px;
                    display: inline-flex;
                    label {
                        color: #acacac;
                        width: 120px;
                        min-width: 130px;
                        display: inline-block;
                        text-align: right;
                    }
                    div {
                        width: calc(100% - 150px);
                        display: inline-block;
                        white-space:nowrap;
                        overflow:hidden;
                        text-overflow:ellipsis;
                    }
                    .valueClass{
                        display: flex;
                        width:100%;
                        line-height: 35px;
                        .valueUrl{
                            display: inline-block;
                            white-space:nowrap;
                            overflow:hidden;
                            text-overflow:ellipsis;
                            margin-right:10px;
                        }
                        i{
                            line-height: 35px;
                        }
                        i:hover{
                            color:#1890ff;
                        }
                    }
                }

                &_look {
                    cursor: pointer;
                    color: #0088FF;
                }
            }
        }
    }
</style>
