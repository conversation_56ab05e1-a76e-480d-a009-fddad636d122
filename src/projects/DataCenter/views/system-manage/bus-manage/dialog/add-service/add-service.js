import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
export default {
    name: "add-service",
    mixins : [commonMixins , servicesMixins, listMixins, common],
    components: {  },
    computed : {
    } ,
    props:{
        rowData: Object,
        serviceDetailShow:Boolean,
        busId:String
    },
    data() {
        return {
            
            // 角色管理表格数据
            tableData: [],
            responseTableData:[],
            requestTableData:[],
            visible:false,
            serviceName:"",
            requestName:"sender_id",
            idCardName:"end_user.id_card",
            options: [],
            reNewId:"",
            isIDCard:true,
            department:"end_user.department",
            userNameCode:"end_user.name"
        };
    },
    methods: {
        codeCheck(i, value) {
            for (let index = 0; index < this.requestTableData.length; index++) {
                if (index !== i) {
                    if(this.requestTableData[index].code === value.code) {
                        this.$message({
                            message: '请勿输入相同的字段名称',
                            type: 'warning'
                        });
                        value.code = ''
                    }
                }
            }
        },
        changeCode(value) {
            value.defaultValue = value.defaultValue.replace(/[\u4E00-\u9FA5]|[\uFE30-\uFFA0\*\@\#\$\%\^\&\￥\^\……]/g,'');
        },
        IDCardValite(value) {
            var idcard_patter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;
            if(!idcard_patter.test(value)) {
                this.$message({
                    message: '身份证格式错误',
                    type: 'warning'
                });
                this.isIDCard = false;
            }else this.isIDCard = true;
        },
        deleteLast(index, row) {
            const vm = this;
            vm.confirm("删除" , "确定删除该条件" , ()=>{
                vm.requestTableData.splice(index, 1);
            })
            
        },
        submit(){
            const vm = this , {busServices , groupMock, settings} = this;
            let services = vm.getServices(busServices );
            if (this.serviceName === "") {
                this.$message({
                    message: '请输入服务方名称',
                    type: 'warning'
                });
                return;
            };
            let flag = true;
            vm.requestTableData.forEach(element => {
                if (element.isRequired) {
                    if (element.defaultValue === "" || element.defaultValue === null) {
                        flag = false;
                    };
                }
            });
            vm.responseTableData.forEach(element => {
                if (element.isRequired) {
                    if (element.defaultValue === "" || element.defaultValue === null) {
                        flag = false;
                    };
                }
            });
            if (!flag) {
                this.$message({
                    message: '必填请输入',
                    type: 'warning'
                });
                return;
            }
            if (!this.isIDCard) {
                this.$message({
                    message: '身份证格式错误',
                    type: 'warning'
                });
                return;
            }
            let requestHeaderCfgList = [], responseHeaderCfgList = [], allChooseFlag = true;
            this.requestTableData.forEach(element => {
                if (element.name !=="" && element.code!=="") {
                    let rowRequestElement = {
                        id: "",
                        name: element.name,
                        code: element.code,
                        columnType: element.columnType||"String",
                        isRequired: element.isRequired,
                        defaultValue: element.code === 'timestamp' ? element.defaultValue * 1000 : element.defaultValue,
                        configType: 0
                    };
                    requestHeaderCfgList.push(rowRequestElement);
                }else allChooseFlag = false;
                
            });
            if (!allChooseFlag) {
                this.$message({
                    message: '请确认是否填写字段名称和字段中文名称',
                    type: 'warning'
                });
                return;
            }
            this.responseTableData.forEach(element => {
                let rowResponseElement = {
                    id: "",
                    name: element.name,
                    code: element.code,
                    columnType: element.columnType,
                    isRequired: element.isRequired,
                    defaultValue: element.defaultValue,
                    configType: 1
                };
                responseHeaderCfgList.push(rowResponseElement);
            });
            let serviceInfoVo = {
                serviceId : this.rowData.serviceId,
                id:this.reNewId,
                name:this.serviceName,
                busId: this.busId,
                requestHeaderCfgList : requestHeaderCfgList,
                responseHeaderCfgList : responseHeaderCfgList,
            };
            settings.loading = true;
            services.saveServiceInfo(serviceInfoVo, settings).then(res =>{
                if (res.data.status === 0) {
                    this.$message({
                        message: '保存成功',
                        type: 'success'
                    });
                    this.$emit("closeDialog"),
                    this.$emit("changePage");
                }
            });
        },
        show() {
            this.visible = true;
        },
        add(){
            this.requestTableData.push({
                name: "",
                code: "",
                columnType: "",
                isRequired: false,
                defaultValue:"",
                addNew:true,
                isDelete:true
            });
        },
        deleteit(row) {
            this.requestTableData.splice(row.index, 1);
        },
        closeDialog() {
            this.visible = false;
        },
        async getHeaderInfo() {
            const vm = this , {busServices , groupMock,settings} = this;
            let services = vm.getServices(busServices );
            settings.loading = true;
            await services.getRequestHeader(settings).then(async res => {
                if (res.data.status === 0) {
                    this.requestTableData = res.data.data.requestHeaderCfgList,
                    this.responseTableData = res.data.data.responseHeaderCfgList;
                }
            });
        },
        getRequestId() {
            const vm = this , {busServices , groupMock} = this;
            let services = vm.getServices(busServices );
            services.getSenderListId().then(async res => {
                if (res.data.status === 0) {
                    res.data.data.forEach(element => {
                        let requestIdOption = {
                            value:element.default_value,
                            label:element.default_value
                        };
                        this.options.push(requestIdOption);
                    });
                };
            });
        },
        async reNew() {
            if (this.rowData.id === 'undefined') return;
            this.reNewId = this.rowData.id;
            const vm = this , {busServices , groupMock, settings} = this;
            let services = vm.getServices(busServices );
            this.serviceName = this.rowData.name;
            settings.loading = true;
            await services.getServiceInfoById(this.rowData.id, settings).then(async res => {
                if (res.data.status === 0) {
                    this.requestTableData = res.data.data.requestHeaderCfgList.map((item) =>{
                        if (item.code === 'timestamp') item.defaultValue = item.defaultValue.substring(0, 14);
                        return item;
                    }),
                    this.responseTableData = res.data.data.responseHeaderCfgList;
                }
            });
        }
    },
    created(){
        this.getRequestId();
        if (this.rowData.id !== undefined) {
            this.reNew();
        }else {
            this.getHeaderInfo();
        };
    }
};