<template>
    <div  class="model-result" v-loading="settings.loading">
        <div class="model-result_filter">
            <div class="model-result_group">
                <span>结果集名称：</span>
                <!--<dg-select v-model="selectDataSet" value-key="selectData.label" :data="selectData" @change="changePage(1)"> </dg-select>-->
                <el-select class="selectHead" v-model="selectDataSet" value-key="id" @change="changePage">
                    <el-option :label="item.code" :value="item" v-for="(item) in selectData" :key="item.id"></el-option>
                </el-select>
            </div>

            <div>
                <el-select
                        v-model="selHead"
                        multiple
                        filterable
                        remote
                        clearable
                        class="selectHead"
                        collapse-tags
                        placeholder="选择显示字段"
                        @change="refresh"
                        :remote-method="remoteMethod">
                    <el-option
                            v-for="item in options"
                            :key="item.prop"
                            :label="item.label"
                            :value="item.label">
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="model-result_table">
            <common-table
                    v-if="tableData.length"
                    :data="tableData"
                    :columns="tableColumns"
                    :border="false"
                    :paging-type="'client'"
                    :pagination-props="paginationProps"
                    :pagination-total="totalCount"
                    :max-height="tableBodyH"
                    @change-size="changeSize"
            >
                <template v-for="(item , i) in tableColumns" #[item.prop]="{row}">
                    <el-dropdown v-if="item.hasLink && row[item.prop]" :key="i" @command="handleCommand($event , row[item.prop],row)">
                                <span class="el-dropdown-link">
                                    <span>{{row[item.prop]}}</span>
                                    <i class="el-icon-arrow-down el-icon--right"></i></span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-for="(opt , k) in item.linkOptions" :key="k"
                                              :command="opt"
                            >{{opt.name}}</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </common-table>
            <no-data v-else="tableData.length === 0" :title="emptyTxt" top="5%">
            </no-data>
        </div>
    </div>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import NoData from "@/components/no-data";
    import {coustTableH} from "dc-front-plugin/src/api/commonMethods/count-table-h";
    import {max} from "lodash";
    export default {
        name: "modelResult",
        mixins: [coustTableH , commonMixins],
        components: {
            NoData,
        },
        props :{
            info: Object
        },
        data() {
            return {
                selectDataSet: '',
                selectData: [],
                filterTxt: '',
                tableData: [],
                tableColumns: [],
                tableColumnsCopy: [],
                totalCount: 0,
                emptyTxt: '暂无数据',
                selHead: [],
                options: [],
            }
        },
        mounted(){
            this.getDataSet();
        },
        methods: {
            getValueByKeys(keys=[],row) {
                for(let k of keys) {
                    if(row[k]) return row[k]
                }
            },
            handleCommand(opt, value, row){
                const { linkFieldKeys } = this.$globalConfig;
                const {mark , url} = opt;
                let valSec = mark === "person" ? ( linkFieldKeys && this.getValueByKeys(linkFieldKeys["person"], row) || row['姓名'] || row['xm']) : "";
                let linkUrl = url.replace(/{userToken}/,sessionStorage.getItem("userToken")||"")
                    .replace(/{value}/,value)
                    .replace(/{value1}/,valSec);
                window.open(linkUrl);
            },
            getDataSet(){
                const vm = this;
                let services = vm.$services("modeling");
                services.getDataSets(this.info.transId).then(res=> {
                    if (res.data.status === 0) {
                        vm.selectData = res.data.data;
                        if(vm.selectData.length){
                            vm.selectDataSet = vm.selectData[0];
                            vm.changePage()
                        }
                    }
                })
            },
            loadElementColumn(params) {
                const vm = this, {$globalConfig, $services} = vm;
                if ($globalConfig.datasetLinkUrl) {
                    return $services("dataReady").loadElementColumn(params);
                } else return Promise.resolve({
                    data: {
                        status: 0,
                        data: {},
                    }
                })
            },
            changePage() {
                const vm = this, {settings} = this;
                let services = vm.$services("dataSource");
                let data = {
                    tableId: vm.selectDataSet.id,
                    tableName: vm.selectDataSet.code,
                    limit: "100",
                    dbType: "RdbDataObj",
                    isFile : "0",
                }
                settings.loading = true;
                vm.tableColumns = [];
                vm.tableData = [];
                vm.totalCount = 0;
                vm.tableColumnsCopy = [];
                vm.options = [];
                services.preview(data, settings).then( async (res)=> {
                    if (res.data.status === 0) {
                        const {fieldName, fieldValue} = res.data.data;
                        if(!fieldName) return;
                        let dataLinkObj = {};
                        const params = Object.keys(fieldName);
                        let res1 = await vm.loadElementColumn({
                            columnList:params
                        });
                        if(res1.data.status === 0) dataLinkObj = res1.data.data;
                        Object.keys(fieldName).forEach(key=>{
                            const hasLink = !!dataLinkObj[key];
                            const maxLen = max(fieldValue.map(li => li[key]? li[key].length : 0));
                            vm.tableColumns.push({
                                label : key ,
                                prop : key,
                                minWidth: hasLink && maxLen ? Math.max(maxLen * 10 + 32 , 120) : key.length < 6 ? 120 : key.length * 16 + 32,
                                hasLink ,
                                linkOptions: hasLink ? dataLinkObj[key] : []
                            })
                        })
                        vm.tableData = fieldValue;
                        vm.totalCount = vm.tableData.length;
                        vm.tableColumnsCopy = vm.tableColumns;
                        vm.options = vm.tableColumns;
                    }
                })
            },
            changeSize(val) {
                this.paginationProps.pageSize = val;
                this.changePage();
            },
            refresh(){
                const vm = this;
                vm.tableColumns =[];
                if(vm.selHead.length){
                    vm.tableColumnsCopy.forEach(n=>{
                        if(vm.selHead.includes(n.prop)){
                            vm.tableColumns.push(n);
                        }
                    })
                }
                else{
                    vm.tableColumns = vm.tableColumnsCopy;
                }
            },
            remoteMethod(query) {
                if (query !== '') {
                    setTimeout(() => {
                        this.options = this.tableColumnsCopy.filter(item => {
                            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
                        });
                    }, 200);
                } else {
                    this.options = this.tableColumnsCopy;
                }
            },
        }
    }
</script>

<style scoped lang="less">
.selectHead{
    width:240px;
}
.model-result {
    background-color: #fff;
    height:100%;
    padding:10px 20px;
    box-sizing: border-box;
    &_filter {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding-bottom: 10px;
    }
    &_group {
        display: flex;
        align-items: center;
        white-space:nowrap;
        gap:12px;
    }
    &_table {
        height: calc(100% - 2rem - 20px);
    }
}
</style>
