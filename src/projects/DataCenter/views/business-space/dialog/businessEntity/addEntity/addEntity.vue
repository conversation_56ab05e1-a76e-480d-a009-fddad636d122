<template>
    <el-form class="addEntity" :model="form" label-position="right" label-width="120px">
        <el-form-item v-for="(item , k) in layoutForm"
                      :key="k"
                      :prop="k"
                      :label="`${item.label}:`"
                      :rules="item.rules"
        >
            <el-input v-if="item.type==='input'"
                      v-model="form[k]"
                      size="mini"
                      :maxlength="maxlength"
                      :placeholder="item.placeholder"
                      v-input-limit:[item.limit]
            ></el-input>
            <ce-select-drop
                    v-if="item.type === 'selectTree'"
                    ref="pos_tree"
                    :placeholder="item.placeholder"
                    :props="defaultProps"
                    filterable
                    visible-type="leaf"
                    :filterNodeMethod="filterNode"
                    v-model="form[k]"
                    :data="item.option"
                    @current-change="selectTreeChange"
            ></ce-select-drop>
        </el-form-item>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </el-form>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {treeMethods} from "@/api/treeNameCheck/treeName";

export default {
    name: "addEntity",
    mixins:[commonMixins],
    props : {
        data : Object
    },
    data(){
        return {
            form : {
                name : "" ,
                code : "" ,
                site : ""
            },
            defaultProps: {
                value: 'id',
                label: "label",
                children: "children",
            },
            maxlength : 30 ,
            layoutForm : {
                name : {
                    label : "实体名称" ,
                    type : 'input' ,
                    limit : "fieldName" ,
                    placeholder : "请输入实体名称",
                    rules :[
                        {required : true , message : "请输入实体名称" , trigger : ['blur' , 'change']}
                    ]
                },
                code : {
                    label : "英文名称" ,
                    type : 'input' ,
                    limit : "fieldCode" ,
                    placeholder : "请输入英文名称",
                    rules :[
                        {required : true , message : "请输入英文名称" , trigger : ['blur' , 'change']}
                    ]
                },
                site : {
                    label : "所属父级" ,
                    type : 'selectTree' ,
                    placeholder : "请选择所属父级",
                    rules :[
                        {required : true , message : "请选择所属父级" , trigger : [ 'change']}
                    ]
                },

            }
        }
    },
    methods : {
        filterNode : treeMethods.methods.filterNode ,
        close(){
            this.$emit("close");
        },
        submit(){

        },
        selectTreeChange(){}
    },
    created() {
        if(this.data){ //回显form

        }
    }
}
</script>

<style scoped>

</style>
