<template>
    <div class="cornResult">
        <el-row type="flex" class="row-box">
            <el-col :span="3"></el-col>
            <el-col :span="3" v-for="( col , inx ) in tabData" :key="inx">{{col.label}}</el-col>
        </el-row>
        <el-row type="flex" :gutter="10">
            <el-col :span="3">{{expression}}</el-col>
            <el-col :span="3" v-for="(input , inx) in cornCode" :key="inx">
                <el-input size="mini" class="el-input--center" v-model="cornCode[inx].value" :readonly="true" ></el-input>
            </el-col>
        </el-row>
        <el-row type="flew" class="row_mt">
            <el-col :span="3">{{corn}}</el-col>
            <el-col :span="21">
                <el-input size="mini" class="el-input--center" v-model="cornJoint" :readonly="true" ></el-input>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";

    export default {
        name: "CornResult" ,
        props : {
            tabData : Array
        },
        data(){
            return {
                expression : '表达式字段 :' ,
                corn : 'Cron 表达式 :',
                cornCode : [
                    {
                        type : 'wildcard' ,
                        value :  "*"
                    },
                    {
                        type : 'wildcard' ,
                        value :  "*"
                    }, {
                        type : 'wildcard' ,
                        value :  "*"
                    },{
                        type : 'wildcard' ,
                        value :  "*"
                    },{
                        type : 'wildcard' ,
                        value :  "*"
                    },{
                        type : 'wildcard' ,
                        value :  "?"
                    },{
                        type : 'wildcard' ,
                        value :  "*"
                    }
                ],
            }
        },
        computed : {
            cornJoint (){
                let value = [];
                this.cornCode.forEach(code => {
                    value.push( code.value );
                });
                return value.join(' ');//空格隔开
            }
        },
        methods : {
            /*
            *   wildcard 通配符 * ? L
            *   range 范围值
            *   distance 从某一时间点起 间隔 多久
            *   sort 第几个星期
            *   last 最后一个星期几
            *   assign 指定
            *   workDay 离几号最近的工作日
            *  */
            dataChange(i , resultData){ //
                let result = resultData.split('/'); //
                this.cornCode[i].type = result[0];
                this.cornCode[i].value = result[1] ? result[1] : '*';
            },
            cornResult (){
                return {
                    value : this.cornJoint ,
                    result : this.cornCode
                };

            },
            initResult(data){
                if( data.result.length ){
                    data.result.forEach((r , i) =>{
                        this.cornCode[i].value = r.value;
                    });
                }
            },
            cornDataFn(){
                globalBus.$on('cornData' , this.initResult ); //数据回填，暂未完善
            }
        },
        created() {
            this.cornDataFn();
        }
    }
</script>

<style scoped>
    .cornResult {
        margin-top: 12px;
        text-align: center;
        line-height: 30px;
    }
    .row_mt {
        margin-top: 10px;
    }
</style>
<style>
    .el-input--center .el-input__inner {
        text-align: center;
    }
</style>