<template>
    <el-tabs
            class="ce-editTabs"
            type="card" closable
            v-bind="$attrs"
            ref="el-tabs"
            v-on="$listeners">
        <el-tab-pane
                v-for="item in editableTabs"
                :key="item.id"
                :label="item.label | filterLabel"
                :name="item.id"
        >
            <slot name="tabCont" :item="item"></slot>
        </el-tab-pane>
    </el-tabs>
</template>

<script>
export default {
    name: "ModelTab" ,
    props : {
        editableTabs : Array
    },
    watch:{
        editableTabs:{
            handler(val){
                this.$nextTick(()=>{
                    this.addTitle(val)
                })
            },
            deep: true
        }
    },
    filters: {
        filterLabel(label){
            const length = String(label).length;
            if(length > 10){
                return String(label).substr(0,10) + '...'
            }
            return label
        }
    },
    methods: {
        // 给el-tab 添加title
        addTitle(tabData){
            if(!this.$refs['el-tabs']) return;
            this.$nextTick(()=>{
                const tabs = this.$refs['el-tabs'].$refs['nav'].$refs['tabs'];
                tabs.forEach(dom => {
                    tabData.forEach(item => {
                        if(dom.id === 'tab-' + item.id){
                            dom.title = item.label
                        }
                    })
                })
            })
        }
    },
    mounted(){
        this.addTitle(this.editableTabs);
    }
}
</script>

<style scoped>

</style>
