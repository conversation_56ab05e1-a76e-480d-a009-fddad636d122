<template>
    <div class="efficitivePolicePlugin">
        <el-form class="attrForm" label-position="right" label-width="160px" :model="effectiveData"  @submit.native.prevent>
            <el-form-item label="开始时间字段:" required>
                <el-select
                        v-model="effectiveData.startTime"
                        placeholder="请选择开始时间"
                        filterable
                >
                    <el-option
                            v-for="item in inputColumn"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="结束时间字段:" required>
               <el-select
                        v-model="effectiveData.endTime"
                        placeholder="请选择结束时间"
                        filterable
                >
                    <el-option
                            v-for="item in inputColumn"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="分组字段:" required>
                 <el-select
                        v-model="effectiveData.groupField"
                        placeholder="请选择分组字段"
                        filterable
                        multiple
                >
                    <el-option
                            v-for="item in inputColumn"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <div slot="label" class="ce-form_title">输出字段</div>
           </el-form-item>
            <el-form-item label="年份输出字段:" required>
                <el-input v-model="effectiveData.year"
                        @input="checkColumn($event,'year')"></el-input>

            </el-form-item>
            <el-form-item label="时间输出字段:" required>
                 <el-input v-model="effectiveData.showTime"
                            @input="checkColumn($event,'showTime')"></el-input>
            </el-form-item>
            <el-form-item label="警情数输出字段" required>
               <el-input v-model="effectiveData.num" @input="checkColumn($event,'num')"></el-input>
            </el-form-item>

        </el-form>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import {common} from "@/api/commonMethods/common"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"

    export default {
        name: "EfficitivePolicePlugin",
        mixins: [pluginMixins,common, commonMixins, servicesMixins],
        components: {preView},
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data() {
            return {
                tabActiveName: "first",
                inputColumn: [],
                width: "145px",
                effectiveData: {
                    startTime: "",
                    endTime: "",
                    groupField: [],
                    year: "",
                    showTime: "",
                    num: ""
                }
            }
        },
        methods: {
            save(...args){
              this.saveFn(...args);
            },
            preview(){
              this.previewFn();
            },
            checkColumn(val, type) {
                const vm = this;
                let reg = new RegExp(/^[\u4E00-\u9FA5]+$/);
                let strs = new Array(); //定义一数组
                strs = val.split(""); //字符分割
                let tem = "";
                strs.forEach((item, i) => {
                    let n = Number(item);
                    if (reg.test(item) || item === " " || (i === 0 && !isNaN(n))) {
                        tem += "";
                    } else {
                        tem += item;
                    }
                });
                if (type === "year") {
                    vm.effectiveData.year = tem;
                } else if (type === "showTime") {
                    vm.effectiveData.showTime = tem;
                } else {
                    vm.effectiveData.num = tem;
                }
            },
            previewFn() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },

            //保存
            saveFn(saveName,showTip) {
                const vm = this, {pluginServices, pluginMock,loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let group = [];

                vm.effectiveData.groupField.forEach(i => {
                    vm.inputColumn.forEach(j => {
                        if (i === j.value) {
                            group.push({
                                fieldCode: j.value,
                                fieldName: j.label,
                                fieldType: j.type
                            })
                        }
                    });
                });

                let newField = [];

                newField.push({
                    fieldCode: vm.effectiveData.num,
                    fieldName: vm.effectiveData.num,
                    fieldType: "Integer",
                    type: "number"
                });
                newField.push({
                    fieldCode: vm.effectiveData.year,
                    fieldName: vm.effectiveData.year,
                    fieldType: "String",
                    type: "year"
                });
                newField.push({
                    fieldCode: vm.effectiveData.showTime,
                    fieldName: vm.effectiveData.showTime,
                    fieldType: "Integer",
                    type: "showTime"
                });
                let params = {
                    startTime: vm.effectiveData.startTime,
                    endTime: vm.effectiveData.endTime,
                    groupByField: group,
                    newField: newField
                };

                if (params.startTime === "" || params.startTime === null || params.startTime === undefined) {
                    this.$message.warning("请选择开始时间！");
                    return;
                }

                if (params.endTime === "" || params.endTime === null || params.endTime === undefined) {
                    this.$message.warning("请结束时间！");
                    return;
                }
                if (params.groupByField.length <= 0) {
                    this.$message.warning("请选择分组字段！");
                    return;
                }
                let isSave = true;
                let set = new Set();
                params.newField.forEach(item => {
                    set.add(item.fieldCode);
                    if (item.fieldCode === "") {
                        if (item.type === "number" && isSave) {
                            this.$message.warning("平均有效警情数输出字段不能为空！");
                            isSave = false;
                        } else if (item.type === "year" && isSave) {
                            this.$message.warning("年份输出字段不能为空！");
                            isSave = false;
                        } else if (item.type === "showTime" && isSave) {
                            this.$message.warning("时间输出字段不能为空！");
                            isSave = false;
                        }
                    }
                });
                if (!isSave) {
                    return;
                }

                if (set.size < 3) {
                    this.$message.warning("输出字段不能相同！");
                    return;
                }
                loadParams.loading = true;
                services.saveEffectivePluginPage(this.sourceData.id, params,loadParams).then(res => {
                    if (res.data.status == 0) {
                        showTip && this.$message.success("保存成功！");
                        loadParams.loading = false;
                        vm.$emit("setPreviewAble");
                        if(saveName)saveName();
                    } else {
                        this.$message.error("保存失败！");
                    }

                });
            },
            /**
             * 检查输入是否变化
             */
            check(changeArr,meta){
                 let isChange = false;
                if(meta.groupByField){
                    for(let item of meta.groupByField){
                        if (changeArr.indexOf(item.fieldCode) > -1){
                            isChange = false;
                        }  else{
                            isChange = true;
                            this.$message.error("上游输入发生更改！");
                            break;
                        }
                    }
                }
                return !(meta.startTime && changeArr.indexOf(meta.startTime) > -1 && meta.endTime && changeArr.indexOf(meta.endTime) > -1 && !isChange);
            },
            init(){
                const vm = this, {pluginServices, pluginMock,loadParams} = this;
                loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                services.initEffective(vm.sourceData.id,loadParams).then(res => {
                    if (res.data.status == 0) {
                        let meta = res.data.data.pluginMeta;

                        let changeArr = [];
                        res.data.data.column.forEach(item => {
                            changeArr.push(item.columnName);
                            vm.inputColumn.push(
                                {
                                    label: item.columnZhName === null ? item.columnName : item.columnZhName,
                                    value: item.columnName,
                                    type: item.columnType
                                }
                            );
                        });
                        vm.effectiveData.endTime = changeArr.includes(meta.endTime)? meta.endTime : '';
                        vm.effectiveData.startTime = changeArr.includes(meta.startTime)? meta.startTime : '';

                        if (meta.groupByField !== null) {
                            meta.groupByField.forEach(item => {
                                if(changeArr.includes(item.fieldCode))
                                    vm.effectiveData.groupField.push(item.fieldCode);
                            });
                        }

                        if (meta.newField !== null) {
                            meta.newField.forEach(i => {
                                if (i.type === "number") {
                                    vm.effectiveData.num = i.fieldCode;
                                } else if (i.type === "year") {
                                    vm.effectiveData.year = i.fieldCode;
                                } else {
                                    vm.effectiveData.showTime = i.fieldCode;
                                }
                            });
                        }
                    } 
                });
            }
        },
        created() {
            this.init();
        }
    }
</script>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped>
    .ce-form_title {
        font-weight: bold;
        color: #333;
    }
</style>
