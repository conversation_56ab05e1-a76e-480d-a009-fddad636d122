<!--高级检索-->
<template>
    <div class="senior-query">
        <el-form class="ce-form" ref="form" :model="form" label-width="100px" label-position="right">
            <el-form-item v-for="(item , k) in layForm" :key="k" :prop="k" :label="item.label" :rules="item.rules ? item.rules :[]">
                <el-input v-if="item.type ==='input'" clearable
                          v-model.trim="form[k]"
                          :placeholder="item.placeholder"
                          v-input-limit:[item.limit]
                          :ce-custom-reg="item.reg"
                />
                <dg-checkbox-group v-if="item.type === 'checkbox'" v-model="form[k]" :data="item.option"></dg-checkbox-group>
                <el-date-picker
                        class="senior-query_date"
                        v-if="item.type === 'datePicker'"
                        v-model="form[k]"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                </el-date-picker>
                <dg-select v-if="item.type === 'select'" v-model="form[k]" :data="item.option" :placeholder="item.placeholder"></dg-select>
            </el-form-item>
            <el-form-item label-width="0" class="tc">
                <el-button type="primary" @click="query">{{queryTxt}}</el-button>
                <el-button @click="reSetForm">{{resetTxt}}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "senior-query",
    data(){
        return {
            form : {
                userName : "" ,
                ipAddress:  "" ,
                operateType : "" ,
                state: "" ,
                functionalModule: "" ,
                time : ""
            },
            queryTxt : "查询",
            resetTxt : "重置",
            layForm : {
                userName : {
                    label : "用户名:" ,
                    type : "input" ,
                    placeholder : "请输入用户名",
                    limit : "fieldName"
                },
                ipAddress : {
                    label : "终端标识:",
                    type : "input" ,
                    placeholder : "请输入终端标识",
                    limit : "custom" ,
                    reg : /[^\d\.]/g ,
                    rules : [
                        {validator : this.checkedCommon ,
                            reg : /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/ ,
                            message : "终端不符合规则" ,
                            trigger : 'blur'}
                    ]
                },
                operateType : {
                    label : "操作类型:" ,
                    type  :"checkbox" ,
                    option : [
                        {label: "登录", value: "login"},
                        {label: "查询", value: "query"},
                        {label: "新增", value: "add"},
                        {label: "修改", value: "edit"},
                        {label: "删除", value: "delete"},
                    ]
                },
                state : {
                    label : "操作结果:" ,
                    type : "checkbox" ,
                    option : [
                        {label : '成功' ,value : "success"} ,
                        {label : '失败' ,value : "fail"} ,
                    ]
                },
                functionalModule : {
                    label : "功能模块:" ,
                    type : "select" ,
                    placeholder : "请选择功能模块" ,
                    option : []
                },
                time : {
                    label : "操作时间:" ,
                    type : "datePicker",
                    placeholder : "请选择操作时间"
                }
            }
        }
    },
    methods : {
        init(form , logModels ,operateTypeOpt ){
            this.layForm.functionalModule.option = logModels;
            this.layForm.operateType.option = operateTypeOpt;
            this.form.userName = form.userName ;
            this.form.ipAddress = form.ipAddress ;
            this.form.operateType = form.operateType ? form.operateType.join(",") : "" ;
            if(form.state)this.form.state = form.state;
            this.form.functionalModule = form.functionalModule;
            const {beginTime , endTime} = form;
            this.form.time = [];
            if(beginTime && endTime){
                this.form.time[0] = new Date(beginTime);
                this.form.time[1] = new Date(endTime);
            }
        },
        reSetForm(){
            this.$refs.form.resetFields();
        },
        query(){
            const vm  = this , {form } = vm;
            vm.$refs.form.validate(valid => {
                if(valid){
                    let beginTime = "" , endTime = "";
                    if(form.time && form.time[0] && form.time[1]){
                        beginTime = new Date(vm.form.time[0]).getTime();
                        endTime = new Date(vm.form.time[1]).getTime();
                    }
                    let {userName ,
                        ipAddress,
                        operateType ,
                        state ,
                        functionalModule
                    } = vm.form;
                    if( state.split(',').length === 2) state = "";
                    if(operateType && operateType.split(",").length >= 1 ) operateType = operateType.split(",");
                    this.$emit("seniorQuery" , {userName ,
                        ipAddress,
                        operateType ,
                        state ,
                        functionalModule,
                        beginTime ,
                        endTime
                    });
                }
            })
        }
    }
}
</script>

<style scoped lang="less">
    .senior-query {
        padding: 0 24px 0 0;
        &_date.el-input__inner {
            width: 100%;
        }
    }
</style>
