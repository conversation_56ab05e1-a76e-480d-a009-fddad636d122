<template>
    <div class="attribute"  :class="{'attrShow' : showClass}" :style="{'width' : width}">
        <div class="attrTitle">
            <em class="el-icon-close attr_close" @click="closeAttr(false)"></em>
            <div class="attrName">{{sourceData.name}}</div>
        </div>
        <div class="ce-attr_cont" v-loading="loading">
            <AttrCont :treeData = "treeData" :rowData="rowData" :sourceData="sourceData"  v-if="showAttr"  />
        </div>
    </div>
</template>

<script>
    import AttrCont from './AttrCont'
    import { globalBus } from '@/api/globalBus';
    export default {
        name: "Attribute",
        components : {
            AttrCont
        },
        props:{
            treeData:Array,
            tabId : String,
            rowData : Object
        },
        data(){
            return {
                showAttr : false ,
                showClass : true,
                sourceData : {} ,
                width : 0 ,
                loading : true
            }
        },
        methods : {
            slideLeft(){
                globalBus.$on('closeAttr',this.closeAttrFn);
                globalBus.$on('slideAttr' , this.slideFn );
            },
            slideFn(node , show, width , tabId){
                const vm = this;
                if(tabId !== this.tabId) return ;
                vm.loading = true;
                vm.showAttr = false;
                vm.sourceData = node;
                vm.width = width;
                vm.showClass = !show;
                setTimeout(()=>{
                    vm.showAttr = show;
                },100);
                setTimeout(()=>{
                    vm.loading = false;
                },700);
            },
            closeAttrFn(node){//删除节点id 与编辑面板ID一致，则关闭
                if(node.id === this.sourceData.id){
                    this.closeAttr(false)
                }
                // this.closeAttr(false)

            },
            closeAttr(close){
                this.showClass = !close ;
                this.slowSlide(close);
                globalBus.$emit('hideAttr',close);
                this.width = 0;
                // globalBus.$emit('showSource',!close);
            },
            slowSlide(close){
                let $this = this;
                setTimeout(() => {
                    $this.showAttr = close;
                },200)
            }
        },
        created() {
            this.slideLeft();
        },
        destroyed() {
            globalBus.$off('slideAttr' , this.slideFn );
            globalBus.$off('closeAttr',this.closeAttrFn);
        }
    }
</script>

<style scoped>
    .attribute {
        padding:5px ;
        width: 0;
        transition: 500ms;
        box-sizing: border-box;
        float: right;
        height: calc(100% - 1.5rem - 76px);
    }
    .ce-attr_cont {
        height: calc(100% - 38px);
    }
    .attrShow {
        display: none;
    }
    .attrTitle {
        padding:8px 10px;
        background: #f2f2f2;
        color: #555;
        font-size: 14px;
        border: 1px solid #dcdcdc;
        border-bottom:none;
    }


    .attrName {
        margin-right: 30px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .attr_close {
        float: right;
        font-size: 16px;
        line-height: 20px;
        cursor: pointer;
    }
    .attr_close:hover {
        color: #fff;
        background:#53a8ff;
    }
</style>