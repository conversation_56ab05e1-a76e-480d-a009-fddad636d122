<template>
    <tree-and-list :sizeLeft="15.8" draggable>
        <tree-card ref="treeCard" slot="left" :card-name="cardName" @filterData="filterTree" :class="{'disabled':listLoad}">
            <tree slot="tree"
                  ref="tree"
                  @getCenterTable="getTableData"
                  :has-save="false"
                  @addTreeDir="showAddDialog"
                  @reLoad="closeEditPanel"
            ></tree>
        </tree-card>
        <list ref="portal_table"
              :renewListLoad="renewListLoad"
              @showPanel="openEditPanel"
              @changeNodeKey="changeNodeKey"
        ></list>
        <div slot="other">
            <transition name="el-zoom-in-top">
                <edit-panel v-if="showEditPanel" ref="protal_panel" @closePanel="closeEditPanel"
                            :isCase="isCase"></edit-panel>
            </transition>
            <add-tree-dir ref="treeDir" v-loading="settings.loading" @addTreeDir="addTreeDir">
                <tree slot="tree" ref="editTree" slot-scope="scope" v-bind="scope" @nodeClick="nodeClick"/>
            </add-tree-dir>
        </div>
    </tree-and-list>
</template>

<script src="./portal.js"></script>
