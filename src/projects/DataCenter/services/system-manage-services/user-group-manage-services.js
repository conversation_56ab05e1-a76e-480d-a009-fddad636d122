/**
 * 用户组管理 接口
 * @Author: wangjt
 * @Date: 2020-05-06
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/userGroups/";

/*
*  获取列表
 * @param code , name , pageNum , pageSize,
 * @returns {Promise<*>}
* */
export async function queryUserGroupsList( params, settings ) {
    return await reqUtil.post({
        url: root + 'queryUserGroupsList',
        settings : settings
    }, params );
}

/*
*  查询 用户组上级
 * @param editId
 * @returns {Promise<*>}
* */
export async function queryAllUserGroups(editId) {
    return await reqUtil.post({
        url: root + 'queryAllUserGroups',
    },editId);
}

/*
*  用户组ID
 * @param
 * @returns {Promise<*>}
* */
export async function getGroupRandom() {
    return await reqUtil.post({
        url: root + 'getGroupRandom',
    });
}

/*
*  ID 获取用户组
 * @param
 * @returns {Promise<*>}
* */
export async function queryUserGroupsById(id) {
    return await reqUtil.post({
        url: root + 'queryUserGroupsById',
    },id);
}

/*
*  ID 获取用户组
 * @param
 * @returns {Promise<*>}
* */
export async function deleteUserGroup(id) {
    return await reqUtil.post({
        url: root + 'deleteUserGroup',
    },id);
}

/*
*  添加 用户组
 * @param
 * @returns {Promise<*>}
* */
export async function addUserGroup(params , settings) {
    return await reqUtil.post({
        url: root + 'addUserGroup',
        settings : settings
    },params);
}

/*
*  编辑 用户组
 * @param
 * @returns {Promise<*>}
* */
export async function editUserGroup(params , settings) {
    return await reqUtil.post({
        url: root + 'editUserGroup',
        settings : settings
    },params);
}