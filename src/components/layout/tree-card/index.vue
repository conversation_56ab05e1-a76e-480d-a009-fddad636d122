<template>
    <el-card shadow="never" class="treeCard">
        <div slot="header" class="data-model__tree-header">
            <span>{{ cardName }}</span>
            <slot name="add"></slot>
        </div>
        <div class="data-model__tabs">
            <slot name="tabs"></slot>
        </div>
        <div class="data-model__tree-search">
            <el-input v-model.trim="search" v-input-limit:trim suffix-icon="el-icon-search" placeholder="请输入名称搜索"/>
            <slot name="sort"></slot>
        </div>
        <div class="data-model__tree">
            <slot name="tree"></slot>
        </div>
    </el-card>
</template>

<script>
export default {
    name: "treeCard",
    props: {
        cardName: String
    },
    data() {
        return {
            search: ""
        }
    },
    watch : {
        search(val){
            this.$emit("filterData" , val);
        }
    }
}
</script>

<style scoped lang="less">

/deep/ .el-card__header {
    padding: 0;
}

.treeCard {
    height: 100%;
}

/deep/ .el-card__body {
    padding: 0;
    height: calc(100% - 50px);
}
@media screen and (max-width: 1365px){
    .data-model__tree {
        height: calc(100% - 24px - 50px);
    }
}

@media screen and (max-width: 1680px) and (min-width: 1366px){
    .data-model__tree {
        height: calc(100% - 28px - 50px);
    }
}

@media screen and (min-width: 1681px) {
    .data-model__tree {
        height: calc(100% - 32px - 50px);
    }
}
.data-model {
     height: 100%;

     &__wrap {
         height: 100%;
        
    }
     &__tree-header {
         display: flex;
         align-items: center;
         justify-content: space-between;
         height: 49px;
         padding: 0 20px;
         border-bottom: 1px solid rgba(0, 0, 0, 0.09);
        
    }

     &__tree-search {
         padding: 20px;
         display: flex;
         align-items: center;
    }

     &__tree {
         // padding: 0 20px 0;
        
    }
     /deep/ .el-tree-node__label {
         display: flex;
         justify-content: space-between;
         padding-right: 10px;
         font-size: 14px;
         color: rgba(0, 0, 0, 0.65);

         .el-tree-node__icon {
             display: none;
            
        }

        
    }

     /deep/ .el-tree-node__item {
         font-size: 14px;
         color: rgba(0, 0, 0, 0.65);

         i {
             margin-right: 4px;
            
        }

        
    }

     /deep/ .is-current {
         > .el-tree-node__content {
             .el-tree-node__icon {
                 display: block;
                
            }

            
        }

        
    }

     &__item {
         display: flex;
         align-items: center;
         font-size: 14px;
         color: rgba(0, 0, 0, 0.65);
         padding: 0 20px 0px 20px;

         span {
             margin-left: 8px;
            
        }

         .tag {
             display: inline-block;
             padding: 0 2px;
             background: #faad14;
             font-size: 12px;
             color: #ffffff;
             border-radius: 2px;
            
        }

        
    }

     &__table-wrap {
         padding: 28px;
        
    }

     &__table {
         margin: 14px 0;
        
    }

    
}

</style>
