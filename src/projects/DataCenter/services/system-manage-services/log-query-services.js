/*
* 日志查询 接口
* @Author: wangjt
* @Date: 2021-10-12
* @Project cicada-dataCenter-webui
* */

import * as reqUtil from '@/api/reqUtil'


/**
 * 获取日志列表数据
 * @param userName 用户
 * @param ipAddress 终端
 * @param state 操作结果
 * @param operateType 操作类型
 * @param endTime 结束
 * @param beginTime 开始
 * @param functionalModule 功能模块
 * @param pageIndex
 * @param pageSize
 * @return {Promise<*>}
 */
export async function getLogInfo({userName ,ipAddress, state, operateType ,endTime , beginTime , functionalModule , pageIndex , pageSize},settings){
    return await reqUtil.post({
        url : "/logAudit/getLogInfo",
        settings
    },{userName , ipAddress ,state, operateType ,endTime , beginTime , functionalModule , pageIndex , pageSize })
}
/**
 * 获取 功能模块
 * @return {Promise<*>}
 */
export async function getLogModels(){
    return await reqUtil.get({
        url : "/logAudit/getLogModels"
    })
}/**
 * 获取 操作类型
 * @return {Promise<*>}
 */
export async function getTypes(){
    return await reqUtil.get({
        url : "/logAudit/getTypes"
    })
}

/**
 * 获取 服务日志
 * @return {Promise<*>}
 */
export async function queryPublishedInterfaceExecLog(param,settings){
    return await reqUtil.post({
        url : "/log/queryPublishedInterfaceExecLog",
        settings
    },param)
}
