import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../service-mixins/service-mixins";

export default {
    name: "datasetTree",
    mixins : [treeMethods , commonMixins , servicesMixins],
    props : {
        curId :String,
        addDId:String
    },
    data(){
        return {
            defaultProps: {
                children: "children",
                label: "name" ,
                value : "id"
            },
            addDataId:this.addDId
        }
    },
    methods :{
        initTree() {
            const vm = this , {datasetServices , datasetMock , settings} = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;
       /*     services.queryDataSetTree(true ,settings).then(res => {
                if(res.data.status === 0){
                    let result =  res.data.data;
                    result[0].children.forEach(re => {
                        re.disabled = true;
                    });
                    vm.data = vm.removeCurId(result);
                }
            })*/
            services.datasetList(settings,vm.curId).then(res => {
                if(res.data.status === 0){
                    let result =  res.data.data;
                    result[0].children.forEach(re => {
                        re.disabled = true;
                    });
                    vm.data = vm.removeCurId(result);
                }
            })

        },
        removeCurId(data){
            const vm = this;
            return data.filter(item => {
                if(item.children && item.children.length){
                    item.children = vm.removeCurId(item.children);
                }
                item.name === null || item.name === "" ? item.name = item.code :true;
                if(item.isParent){
                    item.disabled = true;
                }
                return item.id !== vm.curId;
            })
        },
        getSelectNode(){
            return this.$refs.tree.getRadioNodes();
        }
    },
}