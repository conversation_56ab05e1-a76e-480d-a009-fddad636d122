<template>
    <div class="customPage">
        <code-form-page
                v-on="$listeners"
                :layoutForm="layoutForm"
                :form="form"
        >
        </code-form-page>
    </div>
</template>

<script>
import codeFormPage from "@/projects/DataCenter/views/business-space/dialog/baseSettings/TableManage/codeFormPage";

export default {
    name: "customPage",
    components: {codeFormPage},
    data() {
        return {
            form: {
                name: "",
                code: "",
                enumeration: ""
            },
            layoutForm: {
                name: {
                    label: "编码中文名",
                    type: 'input',
                    limit: "fieldName",
                    placeholder: "请输入编码中文名",
                    rules: [
                        {required: true, message: '请输入编码中文名', trigger: ['blur', 'change']},
                        {max : 30 , message : "码表中文名限30个字符" , trigger : 'change'}
                    ]
                },
                code: {
                    label: "编码英文名",
                    type: 'input',
                    limit: "fieldCode",
                    placeholder: "请输入编码英文名",
                    rules: [
                        {required: true, message: '请输入编码英文名', trigger: ['blur', 'change']},
                        {max : 30 , message : "码表英文名限30个字符" , trigger : 'change'}
                    ]
                },
                enumeration: {
                    label: "枚举值",
                    type: "table",
                    rules: [
                        {required: true, message: '请添加枚举值', trigger: 'change'},
                    ]
                }
            }
        }
    },
    methods: {

    }
}
</script>

<style scoped lang="less">

</style>
