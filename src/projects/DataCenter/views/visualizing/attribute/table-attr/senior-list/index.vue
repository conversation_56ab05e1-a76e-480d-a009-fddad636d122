<template>
    <div class="seniorList">
        <el-form label-width="120px" label-position="right">
            <el-form-item>
                <div slot="label" class="tr vm" style="display: inline-block;">
                    <el-checkbox v-model="refresh.opened" >{{refresh.label}}</el-checkbox>
                </div>
                <div>
                    <el-input :placeholder="refresh.placeholder"
                               v-model.trim.number="refresh.value"
                               @keyup.native="typeCheck(refresh.value)"
                               class="input-with-select">
                        <el-select class="ce-select_in" v-model="refresh.selectValue" slot="append">
                            <el-option v-for="opt in refresh.options" :key="opt.value" :label="opt.label"
                                       :value="opt.value"></el-option>
                        </el-select>
                    </el-input>
                    <span class="ce-tip">{{tip}}</span>
                </div>
            </el-form-item>
            <el-form-item :label="linkage.label">
                <el-button icon="el-icon-plus" size="mini" type="primary" @click="setRelation">{{linkage.btnTxt}}</el-button>
            </el-form-item>
            <el-form-item :label="jumpLink.label">
                <el-button :icon="jumpInfo ? 'el-icon-edit' : 'el-icon-plus'" size="mini" type="primary" @click="setJump">{{jumpInfo ? jumpLink.editTxt : jumpLink.btnTxt}}</el-button>
                <el-button type="text" @click="clearJump" v-if="jumpInfo">{{jumpLink.deleteTxt}}</el-button>
            </el-form-item>
        </el-form>
        <relation ref="relation" @storeRelation="storeRelation"></relation>
        <jump ref="jump" @storeJump="storeJump"></jump>
    </div>
</template>

<script src="./senior-list.js"></script>

<style scoped lang="less" src="./senior-list.less"></style>
