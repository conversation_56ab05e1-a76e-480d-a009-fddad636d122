<template>
    <div>
        <el-popover placement="bottom-start"
                    ref="btnPop"
                    class="mr5"
                    width="300" :visible-arrow="false" trigger="click"
        >
            <div class="actions">
                <div
                        class="actions__item"
                        v-for="(item, idx) in actions"
                        :key="idx"
                        @click="addNewModel(item)"
                >
                                    <span class="actions__item-icon">
                                        <i :style="{ color: item.color }" :class="['dg-iconp', item.icon]"></i>
                                    </span>
                    <div class="actions__item-info">
                        <div>{{ item.title }}</div>
                        <p>{{ item.desc }}</p>
                    </div>
                </div>
            </div>
            <dg-button slot="reference" type="text">添加数据集<i class="el-icon-arrow-down el-icon--right"></i>
            </dg-button>
        </el-popover>
    </div>
</template>

<script>

export default {
    name: "addDataSet",
    data() {
        return {
            actions: [
                {
                    icon: "icon-database1",
                    title: "源数据集",
                    desc: "将需要分析的数据表创建为数据集",
                    color: "#3AA3FF",
                    value: "data-table"
                },
                {
                    icon: "icon-database-a",
                    title: "自助数据集",
                    desc: "将多张数据表关联为一张表，满足以后数据分析",
                    color: "#FAAD14",
                    value: "data-set"
                },
                {
                    icon: "icon-SQL",
                    title: "SQL数据集",
                    desc: "通过自定义原生SQL语句生成数据集",
                    color: "#494DCB",
                    value: "sql"
                }
            ],
        }
    },
    methods: {
        addNewModel(...arg){
            this.$refs.btnPop.doClose();
            this.$emit("addNewDataset" , ...arg)
        },
    }
}
</script>

<style scoped lang="less">
.actions {
    margin: 0 -12px;

    &__item {
        display: flex;
        align-items: flex-start;
        padding: 20px;
        font-size: 14px;
        cursor: pointer;

        &:hover {
            //background: rgba($color: #0088ff, $alpha: 0.09);
            background: rgba(0, 136, 255, 0.09);
        }

        &-info {
            padding-left: 8px;

            div {
                color: rgba(0, 0, 0, 0.85);
                line-height: 1;
            }

            p {
                color: rgba(0, 0, 0, 0.44);
            }
        }

        &-icon {
            display: flex;
            align-items: flex-start;
            margin-top: -6px;

            i {
                font-size: 20px;
            }
        }
    }
}
</style>