<template>
    <div class="auto-container" :class="{'ce-show-arrow' : showArrow}">
        <span class="ce-arrow_left" v-if="showArrow && !hiddenBtn" @click="goLeft"><em class="el-icon-arrow-left"></em></span>
        <span class="ce-arrow_right" v-if="showArrow && !hiddenBtn" @click="goRight"><em class="el-icon-arrow-right"></em></span>
        <div class="ce-container" :style="{'width' : c_width + 'px' , 'transform' : 'translate(' + left + 'px' + ', 0)' }">
            <slot name="contain"></slot>
        </div>
    </div>
</template>

<script>
    import * as $ from "jquery"
    export default {
        name: "AutoContainer" ,
        data(){
            return {
                width : "" ,
                showArrow : false ,
                left : 0
            }
        },
        props : {
            childTag : String ,
            hiddenBtn : {
                type : Boolean ,
                default : false
            }
        },
        computed : {
            c_width (){
                return this.width;
            }
        },
        methods : {
            resize(){
                const vm = this;
                setTimeout(()=>{
                    vm.sizeCount();
                },600)

            },
            sizeCount(){
                const vm = this , {childTag} = this;
                vm.$nextTick(()=>{
                    let children = childTag ?  $(".ce-container").children(childTag).children() : $(".ce-container").children(),
                        container_width = $(".auto-container").width() ,
                        len = children.length ,
                        width = 0;
                    for (let i = 0; i < len; i++){
                        let item = children.eq(i);
                        width += item.outerWidth();
                    }
                    vm.width = width + 20;
                    vm.left = 0;
                    vm.showArrow = width >= container_width;
                    vm.$emit("showBtn" ,vm.showArrow );
                })
            },
            goLeft(){
                const vm = this , {width , left} = this;
                let container_width = $(".auto-container").outerWidth();
                if( container_width + left < 0 ){
                    vm.left += container_width;
                }else {
                    vm.left = 0;
                }
            },
            goRight(){
                const vm = this , {width} = this;
                let container_width = $(".auto-container").outerWidth();
                if(width - container_width + vm.left > container_width ){
                    vm.left -= container_width;
                }else {
                    vm.left = container_width - width;
                }
            }
        },
        mounted() {
            window.addEventListener(
                "resize", this.resize, false
            );
        },
        destroyed() {
            window.removeEventListener("resize",this.resize)
        }

    }
</script>

<style scoped>
    .auto-container {
        position: relative;
        height: 100%;
    }
    .ce-show-arrow {
        padding: 0 8px;
    }
    .ce-container {
        float: left;
        transform: translate(0,0);
        transition: 300ms;
    }
    .ce-arrow_left ,.ce-arrow_right{
        position: absolute;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        font-size: 18px;
        cursor: pointer;
        z-index: 20;
        background: #fff;
    }
    .ce-arrow_left {
        left: 0;
        border-right: 1px solid #eee;
    }
    .ce-arrow_right {
        right: 0;
        border-left: 1px solid #eee;
    }
    .ce-arrow_left:hover , .ce-arrow_right:hover {
        background: #E8F6FF;
        color: #289bf1;
        border-color: rgba(40, 155, 241,.2);
    }
</style>