<template>
    <div class="tree" v-loading="settings.loading">
        <dg-tree
                :data="data"
                node-key="id"
                :expand-on-click-node="false"
                :props="defaultProps"
                :filter-node-method="filterNode"
                ref="tree"
                :highlight-current="true"
                :default-expanded-keys="expandData"
                @node-click="nodeClick"
        >
                    <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }"
                          @mouseenter="rightMenu($event , node , data)">
                        <span class="node-label"
                              :title="data.name"
                              :class="{
                            'el-icon-folder' : !node.expanded ,
                            'el-icon-folder-opened' : node.expanded,
                          }"
                        >{{ data.name }}</span>

                        <span class="label__action" v-if="data.id !== '0' && !hasSave">
                            <dg-button type="text" :title="addBtnTxt" class="el-icon-plus b"
                                       @click.stop="addChildren($event , node , data)"
                                       v-if="data.level === 1"></dg-button>
                            <dir-edit-action v-else-if="countHasRightMenu > 0"
                                             :value="node.data"
                                             @command="menuCommand($event , node , data)" :data="treeMenu"
                                             :rights="rights" :node="node">
                            </dir-edit-action>
                        </span>
                    </span>
        </dg-tree>
    </div>
</template>

<script src="./tree.js"></script>
<style scoped lang="less">
    .tree {
        height: 100%;
        overflow: auto;
    }
    @media screen and (max-width: 1365px){
        .d-s-l-bottom ,.d-s-l-b-tree {
            height: calc(100% - 24px);
        }
    }

    @media screen and (max-width: 1680px) and (min-width: 1366px){
        .d-s-l-bottom ,.d-s-l-b-tree {
            height: calc(100% - 28px);
        }
    }

    @media screen and (min-width: 1681px) {
        .d-s-l-bottom ,.d-s-l-b-tree {
            height: calc(100% - 32px);
        }
    }
    .tree_cont {
        padding-top: 10px;
        height: 100%;
        box-sizing: border-box;
        overflow: auto;
    }
    .custom-tree-node  {
        width: calc(100% - 32px) ;
        overflow: hidden;
    }
    .node-label {
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
    }

    .el-dropdown-link-rote {
        transform: rotate(90deg);
        display: flex;
        align-items: center;
    }

    .upload-row {
        p {
            line-height: 1.4;
        }
        .upload-row-p1 {
            color: #cccccc;
            font-size: 14px;
            margin-top: 10px;
        }
        .upload-row-p2 {
            margin-top: 20px;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
        }
    }

    .node-label::before {
        padding-right: 5px;
    }

</style>
