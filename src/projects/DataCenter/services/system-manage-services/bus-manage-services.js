/**
 * 总线服务管理 接口
 * @Author: chenzt
 * @Date: 2021-03-18
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/busInfoManagement/" ;
const root1 = "/serviceRequestInfoController/" ;

/*
*  获取总线信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getBusInfo() {
    return await reqUtil.get({
        url: root + 'getBusInfo',
    } );
}

/*
*  获取总线版本
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getBusVersion() {
    return await reqUtil.get({
        url: root + 'getBusVersion',
    } );
}


/*
*  保存总线信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function saveOrUpdateBusInfo(busInfoVo,userId, settings ) {
    return await reqUtil.post({
        url: root1 + 'saveOrUpdateBusInfo',
        settings : settings
    },  busInfoVo,userId );
}

/*
*  查询列表
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getServiceInfoPage(map, settings ) {
    return await reqUtil.post({
        url: root1 + 'getServiceInfoPage',
        settings : settings
    },  map );
}
/*
*  删除接口配置
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function deleteInterfaceBodyById(id ) {
    return await reqUtil.post({
        url: root1 + 'deleteInterfaceBodyById?id=' + id,
    } );
}
/*
*  删除服务配置
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function deleteServiceById(id ) {
    return await reqUtil.post({
        url: root1 + 'deleteServiceById?id=' + id,
    } );
}

/*
*  新增服务信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function saveServiceInfo(serviceInfoVo, settings) {
    return await reqUtil.post({
        url: root1 + 'saveServiceInfo',
        settings
    },  serviceInfoVo );
}

/*
*  新增接口信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function saveInterfaceBody(interfaceCfgVo, settings) {
    return await reqUtil.post({
        url: root1 + 'saveInterfaceBody',
        settings
    },  interfaceCfgVo );
}

/*
*  获取服务类型信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getServiceTypeList( ) {
    return await reqUtil.post({
        url: root1 + 'getServiceTypeList',
    } );
}


/*
*  获取服务请求头信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getRequestHeader(settings) {
    return await reqUtil.post({
        url: root1 + 'getRequestHeader',
        settings
    } );
}
/*
*  获取请求方id信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getSenderListId() {
    return await reqUtil.post({
        url: root1 + 'getSenderListId',
    } );
}

/*
*  获取服务请求头信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getServiceInfoById(id, settings) {
    return await reqUtil.post({
        url: root1 + 'getServiceInfoById?id=' + id,
        settings
    });
}

/*
*  获取接口编辑信息
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getBodyAttributeByCfgId(cfgId, settings) {
    return await reqUtil.post({
        url: root1 + 'getBodyAttributeByCfgId?cfgId=' + cfgId,
        settings
    });
}




