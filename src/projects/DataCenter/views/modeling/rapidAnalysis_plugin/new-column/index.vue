<!--
    linxp
    2021.06.17
 -->
<template>
    <div class="new-column">
        <fold-card title="新增列" :showTip="false">
            <div class="ce-plugin_box ce-plugin_content">
                <div class="ce-plugin_common">
                    <params-page ref="page" :rowData="rowData" :inputColumns="inputColumns"></params-page>
                </div>
                <el-button class="ce-plugin_save"  type="primary" @click="save">{{ saveBtnTxt }}</el-button>
            </div>

        </fold-card>
        <el-card
                class="ce-plugin_card"
                shadow="never"
                :body-style="{ padding: 0,height: '100%' }"
        >
            <div class="ce-plugin_content ce-plugin_table">
                 <preview-page ref="preview">
                        <div slot="reflash" :class="{'disabled' : !previewAble}" @click="preview" class="new-column__wrap-reflash mr5">
                            <i class="dg-iconp icon-refresh"></i>
                        </div>
                </preview-page>
            </div>
        </el-card>
    </div>
</template>
<script>
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import previewPage from "@/projects/DataCenter/views/plugin_3/component/preview-page"
import paramsPage from "@/projects/DataCenter/views/plugin_3/ruleEditing/params-page"
export default {
    name: "new-column",
    mixins: [servicesMixins],
    components: {previewPage , paramsPage},
    props: {
        config: Object,
        planId: String,
        loadParams: Object,
        errArray:Array,
        previewAble : Boolean,
        rowData:Object
    },
    data() {
        return {
            width: "1200px",
            btnActive: "预览数据",
            types: [],
            inputColumns: [],
            outputColumns: [],
        };
    },
    created() {
        this.init();
    },
    methods: {
        init() {
            const vm = this, {services, config, loadParams} = vm;
            loadParams.loading = true;
            services.operatorPage(config.id, loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.inputColumns = result.inputColumn.map(col => {
                        col.label = col.columnName || col.columnCode;
                        col.value = col.columnCode;
                        return col;
                    });
                    vm.outputColumns = result.outputColumns;
                    vm.types = result.types;
                    vm.$refs.page.initData(vm.types , vm.inputColumns ,vm.outputColumns);
                    if (vm.outputColumns.length){
                        if (vm.errArray.indexOf(config.id) > -1){
                            vm.$message.error('该步骤出现错误！');
                        }else {
                            vm.preview();
                        }
                    }
                }
            })

        },
        preview() {
            this.$refs.preview.show({transId: this.planId}, this.config);
        },
        /**
         * 保存
         * */
        async save() {
            const vm = this, {services, config, loadParams} = vm;
            let params = vm.setParamsVo();
            if (!params.length) {
                vm.$message.warning("暂无配置数据");
                return;
            }
            vm.$refs.page.validate(async valid => {
                if(valid){
                    loadParams.loading = true;
                    let {data} = await services.saveOperatorExp(config.id, {cicadaServiceOrgOutputs: params}, loadParams);
                    if (data.status === 0) {
                        vm.$message.success("保存成功");
                        vm.$emit("saveSuccess" , config.id);
                        vm.preview();
                    }
                }else {
                    vm.$message.warning("请正确完善字段信息");
                }
            })

        },
        setParamsVo() {
            const vm = this;
            let list = vm.$refs.page.tableData;
            return list.map(li => {
                return {
                    columnName: li.columnName,
                    columnCode: li.columnCode,
                    columnId: li.columnId,
                    type: li.type,
                    length: li.length,
                    precision: li.precision,
                    expressionScript: li.expressionScript,
                    isCommonUseOperator: li.isCommonUseOperator,
                    commonUseOperatorParams: li.commonUseOperatorParams,
                    usedOperatorId: li.usedOperatorId,
                }
            });
        }
    }
}
;
</script>

<style lang="less" scoped>
@import "../components/plugin.less";
.dg-form-column__button,
.dg-table .el-table .el-icon-circle-plus {
    margin: 0 4px;
}

.params-page {
    .el-form-item {
        margin-bottom: 0;
    }
}
    .new-column {
    height: 100%;
    display: flex;
    flex-direction: column;

    /deep/ .el-card__header {
        padding: 0 !important;
    }

    .comm__header {
        padding: 1rem 1.75rem;
    }

    &__content {
        padding: 0.875rem 1.875rem;

    }
    &_box {
        display:flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .el-card {
        flex-shrink: 0;
        height: auto;
        box-sizing: border-box;
    }

    .el-card + .el-card {
        margin-top: 14px;
    }

    &__header &__content {
        padding: 14px 30px;
    }

    &__wrap {
        &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        &-actions {
            display: flex;
            align-items: center;
        }

        &-reflash {
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 2px;
            margin-left: 0.25rem;
            cursor: pointer;

            i {
                color: rgba(0, 0, 0, 0.45);
            }

            &:hover {
                i {
                    color: @mainColor;
                }
            }
        }

        &-list {
            padding-top: 0.875rem;
        }
    }

    &__item {
        display: flex;
    }

    &__fs {
        width: 100%;

        &-header {
            width: 100%;
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            color: @mainColor;
            justify-content: space-between;
        }

        &-content {
            padding: 0.875rem 0;
        }
    }

    .edit-table {
        /deep/ .el-table td {
            padding: 0.65rem 0;
        }

        /deep/ .el-table th {
            padding: 0.625rem 0;
        }
    }

    .table-header {
        display: flex;
        flex-direction: column;
        line-height: 1;
        align-items: flex-start;
        font-size: 0.875rem;

        div {
            line-height: 1;
            padding: 0;
        }
    }
}
</style>
