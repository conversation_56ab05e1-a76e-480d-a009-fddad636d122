<template>
    <div>
        <common-dialog :width="width" title="历史执行日志"
                   :visible.sync="dialogFormVisible"
                   @closed="clearD"
                       custom-class="log"
        >
            <common-table :data="tableData"
                          :columns="tHeadData"
                          :pagination="false"
                          class="width100"
                          height="calc(75vh - 112px)"
            >
            </common-table>
<!--            &lt;!&ndash;    分页     &ndash;&gt;-->
<!--            <el-pagination-->
<!--                    class="pagination"-->
<!--                    background-->
<!--                    @current-change="changePage"-->
<!--                    :current-page="currentPage"-->
<!--                    :page-size="pageSize"-->
<!--                    layout="total, prev, pager, next, jumper"-->
<!--                    :total="total"-->
<!--            ></el-pagination>-->

        </common-dialog>
    </div>
</template>

<script>
    export default {
        name: "HistoryExecute",
        data() {
            return {
                dialogFormVisible: false,
                width : '1200px',
                active : true ,
                // 分页
                currentPage: 1,
                total: 0,
                pageSize: 10,

                openDialog : false,
                rowData:{},
                tableData:[],
                tHeadData:[
                    {
                        prop: "processName",
                        label: "过程名称",
                        minWidth: "100",
                        align: "left"
                    },{
                        prop: "startTime",
                        label: "开始时间",
                        minWidth: "160",
                        align: "center"
                    },
                    {
                        prop: "endTime",
                        label: "结束时间",
                        minWidth: "160",
                        align: "center"
                    },
                    {
                        prop: "inputNum",
                        label: "输入数",
                        align: "center"
                    },
                    {
                        prop: "disposeNum",
                        label: "处理数",
                        align: "center"
                    },
                    {
                        prop: "instantSpeed",
                        label: "瞬时速度",
                        align: "center"
                    },
                    {
                        prop: "speed",
                        label: "速度",
                        align: "center"
                    },
                    {
                        prop: "elapsedTime",
                        label: "耗时(S)",
                        align: "center"
                    },
                    {
                        prop: "executeStatus",
                        label: "执行状态",
                        align: "center",
                        minWidth: "100",
                    },
                    {
                        prop: "executeInfo",
                        label: "执行消息",
                        align: "center",
                        minWidth: "100",
                    },

                ],
                taskInfo:{}
            };
        },
        methods:{
            show(row){
                const _this = this;
                this.taskInfo = row;
                this.dialogFormVisible = true;
                // console.log(this.taskInfo);
                _this.$axios.get("/taskLog/transHistoryExecLogPage?taskId="+this.taskInfo.taskId).then(res =>{
                    // console.log("历史执行日志：",res);
                    if(res.data.status === 0){
                        _this.tableData = [];
                        res.data.data.forEach(item =>{
                            let tableNode =
                            {
                                processName:item.orgName,
                                startTime:item.startTime,
                                endTime:item.endTime,
                                inputNum:item.inDataCount,
                                disposeNum:item.dataSetCount,
                                instantSpeed:item.instantSpeed,
                                speed:item.speed,
                                elapsedTime:item.consumeSecond,
                                executeStatus:item.curState,
                                executeInfo:item.msg,
                            };
                            _this.tableData.push(tableNode);
                        })
                    }
                }).catch(err =>{
                    console.log(err);
                    _this.$message.error("服务器异常，请联系管理员！")
                })
            },
            // changePage(index){
            //     console.log(index);
            // },
            clearD(){
                this.openDialog = false;
            },
        },
    }
</script>

<style scoped>
    .pagination {
        margin: 5px 0;
        text-align: center;
    }
</style>