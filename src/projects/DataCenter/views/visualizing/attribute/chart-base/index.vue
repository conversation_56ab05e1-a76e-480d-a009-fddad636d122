<!--
            <chart-base> 方法 变量
            axisList : 表单结构数据
            showDrill : 显示钻取
            filterSelects : 过滤条件
            chartData : 表单绑定数据模型

            pgis :
                @markChange 标记类型改变事件

            @allowDrop 字段拖拽经过
            @dropEnd 字段放置
            @changeVal 度量维度配置选项值改变
            @duplicateFun 去重
            @nodeFilter 字段过滤
            @showDrilling 钻取
            @deleteNode 删除字段
            @changeFastCount 快速计算
            @deleteDrill 删除钻取
            @drillDropEnd 钻取放置字段
            @addDrilling 添加钻取
            @deleteDrillNode 删除钻取字段
            @deleteDrillDime 删除钻取当前级别
            @inputData 更新数据
            @addFilter 添加过滤条件
            @typeCheck 数字类型输入校验
            @dropFilter 拖拽放置过滤字段
            @filterData 设置过滤数据
            @updateValue 更新过滤条件

            表单form type 类型说明
            select : 维度度量 拖拽字段输入框
            drill_select : 钻取的 字段拖拽输入框
            input ：文本输入框
            limit_number : 数字输入框(数据数量top10)
            selectGroup ： 条件过滤组件
            inputSelect ： 下拉输入组合
            input_number ：数字输入 (最大展示条数)
-->
<template>
    <el-form ref="form" class="ce-last-none_m-item" label-position="top" size="mini"
             :model="chartData">
        <template v-for="(axis , key) in axisList">
        <el-form-item
            :key="key"
            :prop="axis.prop"
            :rules="axis.rule"
            v-if="axis.show(axis , axisList)"
        >
            <div slot="label" v-if="axis.label" class="tr vm dib" >
                <span class="l">{{axis.label + ' :'}}</span>
            </div>
            <div v-if="axis.type === 'select'"
                 @dragover="allowDrop"
                 @drop="dropEnd($event , key)">
                        <span class="ce-append-tip"
                              v-if="axis.showLimit">{{axis.value.length}}/{{axis.multipleLimit}}</span>
                <axis-node-list :axis="axis" :keyVal="key"
                                :showDrill="showDrill"
                                v-on="$listeners" v-bind="$attrs"
                                :hiddenOpt="hiddenOpt"
                ></axis-node-list>
            </div>
            <div v-if="axis.type === 'drill_select'">
                <div class="pb10 mt8 bbc" >
                    <el-input v-if="axis.list.length" v-model="axis.list[0].parent" :title="axis.list[0].parent" readonly >
                        <el-button class="ce-drill_parent" slot="append" icon="el-icon-delete" @click="deleteDrill"></el-button>
                    </el-input>
                    <div class="rel mt6"
                         v-for="(dime , k ) in axis.list" :key="k"
                         @dragover="allowDrop"
                         @drop="drillDropEnd($event , k)"
                    >
                        <span class="ce-append-tip" v-if="dime.showLimit">{{dime.value.length}}/{{dime.multipleLimit}}</span>
                        <ul class="ce-node_box">
                            <span class="ce-placeholder" v-show="dime.value.length === 0">{{dime.placeholder}}</span>
                            <div v-for="(node , index) in dime.options" :key="index">
                                <li :title="node.label" class="ce-node_item ce-drill_item">
                                    <span>{{node.label}}</span>
                                    <dg-button v-if="k < 2" type="text" title="钻取" icon="el-icon-bottom-right" @click="addDrilling(dime , k , index)" ></dg-button>
                                    <i class="el-icon-close ce-node_close" @click="deleteDrillNode(k , index)"></i>
                                </li>
                            </div>
                            <dg-button v-if="k > 0" class="ce-drill_delete" title="删除" type="text" icon="el-icon-delete" @click="deleteDrillDime(k)"></dg-button>
                        </ul>

                    </div>
                </div>
            </div>
            <el-input
                maxlength="31"
                v-if="axis.type === 'input'"
                v-model="axis.value"
                :placeholder="axis.placeholder"
                :readonly="axis.readonly"
                @focus="inputData"
            >
            </el-input>
            <div v-if="axis.type === 'timeInput'">
                <el-input
                    class="timeInputWidth"
                    maxlength="31"
                    v-model="axis.value"
                    :placeholder="axis.placeholder"
                    :readonly="axis.readonly"
                    @focus="inputData"
                >
                </el-input>
                <el-select v-model="axis.timeValue" placeholder="请选择" class="timeSelectWidth">
                    <el-option
                    v-for="item in timeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
            
            
            <el-select v-model="axis.value" filterable placeholder="请选择" v-if="axis.type === 'selectChoose'" >
                <el-option
                v-for="item in axis.options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
            <el-select v-model="axis.value" 
                filterable placeholder="请选择" 
                v-if="axis.type === 'selectS'" 
                multiple
                collapse-tags>
                <el-option
                v-for="item in axis.options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
            <el-date-picker
                style="width:95%;"
                v-if="axis.type === 'dataChoose'"
                v-model="axis.value"
                type="daterange"
                unlink-panels
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right">
            </el-date-picker>
            <el-input
                v-if="axis.type === 'limit_number'"
                v-model.number="axis.value"
                v-input-limit:number
                :placeholder="axis.placeholder"
                :readonly="axis.readonly"
                @focus="inputData"
            >
            </el-input>
            <div class="ce-filter_block" v-if="axis.type === 'selectGroup'">
                <div class="mb5">
                    <el-button type="primary" size="mini" icon="el-icon-plus" plain @click="addFilter">过滤条件</el-button>
                </div>
                <DataFilterSelect
                    v-for="(select , i) in filterSelects"
                    :key="i"
                    :item="axis"
                    class="ce-select_noicon ce-input_mg"
                    :val.sync="select.value"
                    :select="select"
                    :addIcon="i"
                    v-on="$listeners" v-bind="$attrs"
                    @drop="dropFilter($event , i , key )"
                />
            </div>
            <div v-if="axis.type === 'inputSelect'">
                <el-input  :placeholder="axis.placeholder"
                           v-model.trim.number="axis.value"
                           @focus="inputData"
                           @keyup.native="typeCheck(axis.value , key )"
                           class="input-with-select">
                    <el-select class="ce-select_in" v-model="axis.selectValue" @focus="inputData" slot="append">
                        <el-option v-for="opt in axis.options" :key="opt.value" :label="opt.label"
                                   :value="opt.value"></el-option>
                    </el-select>
                </el-input>
                <span class="ce-tip">{{tip}}</span>
            </div>
            <el-input v-if="axis.type === 'input_number'"
                      v-model.number="axis.value"
                      v-input-limit:number
                      @keyup.native="typeCheck(axis.value , key )"
            ></el-input>
            <div v-if="axis.type === 'mark_radio'">
                <el-radio-group  v-model="axis.value" @change="markChange">
                    <el-radio v-for="radio in axis.options" :key="radio.value" :label="radio.value">
                        <span>{{radio.label}}</span>
                        <div class="ce-opt_custom" v-if="radio.custom">
                            <span>{{radio.custom_t}}</span>
                            <el-input-number class="ce-mark_inputnum" v-model="axis.circle_radius" controls-position="right" :min="10"></el-input-number>
                            <span>{{radio.unit}}</span>
                        </div>
                    </el-radio>
                </el-radio-group>
            </div>
            <div  v-if="axis.type === 'radio'">
                <el-radio-group v-model="axis.value">
                    <div class="mt10" v-for="(radio , key) in axis.options" :key="key">
                        <el-radio :label="key">{{radio.label}}</el-radio>
                    </div>
                </el-radio-group>
            </div>
        </el-form-item>
        </template>
    </el-form>
</template>

<script src="./chart-base.js"></script>
<style scoped lang="less" src="./chart-base.less"></style>
<style scoped lang="less" src="../css/attr.less"></style>
