<template>
    <div class="option">
        <div>
            <p class="option-p">基础信息</p>
            <el-form label-width="100px"  ref="form" :model="form" class="option-basic_form">
                <el-form-item v-for="(item,k) in baseInfo" :key="k" :prop="k" :label="item.label" :rules="item.rules">
                    <el-input v-if="item.type === 'input'" :placeholder="item.placeholder" v-model="form[k]"></el-input>
                    <dg-select v-else v-model="form[k]" :data="item.option" :placeholder="item.placeholder"></dg-select>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <p class="option-p">请求参数</p>
            <common-table
                    class="ce-table"
                    :data="requestData"
                    :columns="requestHeadData"
                    :pagination="false"
                    size="mini"
            >
                <template slot="header-operate">
                    <el-button type="text" title="新增" icon="el-icon-circle-plus" @click="customAdd"></el-button>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                    <el-popconfirm
                            size="mini"
                            title="确认删除?"
                            @confirm="customDel(row ,$index)"
                            v-if="row.isAdd"
                    >
                        <el-button slot="reference" type="text">删除</el-button>
                    </el-popconfirm>
                </template>
                <template slot="name" slot-scope="{row , $index}">
                    <el-input v-model="row.name" v-if="row.isAdd"></el-input>
                    <span v-else>{{row.name}}</span>
                </template>
                <template slot="value" slot-scope="{row , $index}">
                    <el-input v-model="row.value"></el-input>
                </template>
            </common-table>
        </div>
        <dg-button v-footer @click="testLink">测试连接</dg-button>
        <dg-button v-footer @click="close">取消</dg-button>
        <dg-button v-footer type="primary" @click="submit">确定</dg-button>
    </div>
</template>

<script>
    import {servicesMixins} from "../../service-mixins/service-mixins";
    export default {
        name: "mediationOption",
        mixins: [servicesMixins],
        data(){
            return {
                form:{
                    address: '',
                    version: '',
                    name: '',
                },
                baseInfo:{
                    address:{
                        label:'代理地址',
                        type:'input',
                        placeholder:'请输入',
                        rules :[
                            {required : true , message : "请输入代理地址" , trigger : 'change'}
                        ],
                    },
                    version:{
                        label:'版本',
                        type:'select',
                        option:[],
                        placeholder:'请选择',
                        rules :[
                            {required : true , message : "请选择版本" , trigger : 'change'}
                        ],
                    },
                    name:{
                        label:'代理名称',
                        type:'input',
                        placeholder:'请输入',
                        rules :[
                            {required : true , message : "请输入代理名称" , trigger : 'change'}
                        ],
                    }
                },
                requestData:[{name:'2',value:''}],
                requestHeadData:[
                    {
                        prop: 'name',
                        label: "参数名",
                    }, {
                        prop: 'value',
                        label: "参数值",
                    },{
                        prop: 'operate',
                        label: "操作",
                        width: 80,
                        align : "center",
                    },
                ],
            }
        },
        methods:{
            /**
             * 列表删除
             */
            customDel(row, inx){
                const vm = this;
                vm.requestData.splice(inx, 1);
            },
            customAdd(){
                this.requestData.unshift({
                    name : "",
                    value : "",
                    isAdd : true,
                });
            },
            testLink(){

            },
            close(){
                this.$emit("close");
            },
            submit(){

            },

        }
    }
</script>

<style scoped lang="less">
.option{
    &-p{
        font-weight: bold;
        font-size: 16px;
    }
    &-basic{
        &_form{
            width:80%;
        }
    }
}
</style>