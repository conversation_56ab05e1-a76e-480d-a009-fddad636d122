<template>
    <div class="market-home">
        <dg-scrollbar native>
            <div class="market-home_top">
                <div class="grid-content" v-for="(item , k) in dataList" :key="k">
                    <div class="market-home_top_item">
                        <i class="dg-iconp market-home_top_icon" :class="item.icon"></i>
                        <span class="market-home_top_name">{{ item.name }}</span>
                    </div>
                    <span class="market-home_top_number" :title="item.num"
                          :style="{ color: item.color}">{{ item.num }}</span>
                </div>
            </div>
            <div class="market-home_body">
                <div class="market-home_body_left">
                    <el-tabs class="ce-share_tab" v-model="activeName" @tab-click="tabClick">
                        <el-tab-pane v-for="(tab , inx) in tabPanes" :key="inx" :label="tab.label" :name="tab.value">
                        </el-tab-pane>
                    </el-tabs>
                    <el-link :underline="false" href="/market/modelMarketIndex" class="market-home_body_more">{{ moreTxt }} <i class="el-icon-arrow-right"></i></el-link>
                    <ul class="market-home_body_box" v-loading="settings.loading">
                        <list-card v-for="(list , i) in listData" :key="i" :index="countInx+i" :data="list"/>
                        <no-data class="pct100" v-if="!listData.length"/>
                    </ul>
                </div>
                <div class="market-home_body_right">
                    <rank-card :data="userPublishList" :title="groundTitle" :userType="'userPublish'"
                               :num="countInfo.modelPublishNum"/>
                    <rank-card :data="userFocusList" :title="focusTitle" :userType="'userFocus'"
                               :num="countInfo.modelFocusNum"/>
                </div>
            </div>
            <div class="pt20"></div>
        </dg-scrollbar>
    </div>
</template>

<script>
import ListCard from "@/projects/DataCenter/views/model-market/home/<USER>/list-card"
import {common} from "@/api/commonMethods/common";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import NoData from "@/components/no-data"
import rankCard from "@/projects/DataCenter/views/model-market/home/<USER>/rankCard";
import {servicesMixins} from "../service-mixins/service-mixins";

export default {
    name: "index",
    mixins: [common, commonMixins, servicesMixins],
    components: {
        ListCard,
        NoData,
        rankCard
    },
    computed: {
        countInx() {
            const vm = this;
            return (vm.currentPage - 1) * vm.pageSize + 1;
        }
    },
    created() {
        this.getModelCount();
        this.getRankingList(1);
        this.modelPublishRankingList();
        this.modelUserRankingList();
    },
    data() {
        return {
            dataList: {
                num: {icon: 'icon-model1 bg-model-num', name: '模型上架数', num: '0', color: "#3DA4FF"},
                visit: {icon: 'icon-data-c2 bg-model-visit', name: '用户访问量', num: '0', color: "#8198F3"},
                browse: {icon: 'icon-browse bg-model-browse', name: '用户浏览量', num: '0', color: "#BCD782"},
                attention: {icon: 'icon-follow-c bg-model-attention', name: '用户关注量', num: '0', color: "#66C4C2"}
            },
            activeName: "Browse",
            tabPanes: [
                {
                    value: "Browse",
                    label: "浏览榜"
                }, {
                    value: "Focus",
                    label: "关注榜"
                }, {
                    value: "Evaluation",
                    label: "好评榜"
                }, {
                    value: "Surge",
                    label: "飙升榜"
                },
            ],
            /*
            * 可具体按后端数据 的code 或者 type 映射图标和类型名称
            *  数据模型 AI模型 仪表盘
            * {"trans" : "#icon-model1" ,"visual" :"#icon-dashboard" , "aImodel" : "#icon-AImodel"};
            * */
            listData: [],
            groundTitle : "用户排行-上架榜" ,
            focusList : [] ,//关注榜
            focusTitle : "用户排行-关注榜",
            currentPage:1,
            pageSize:9,
            totalCount:0,
            userPublishList:[],
            userFocusList:[],
            countInfo:{}
        }
    },
    methods: {
        //模型信息统计
        getModelCount() {
            const vm = this, {manageServices, manageMock, settings} = this;
            let services = vm.getServices(manageServices, manageMock);
            services.modelCountInfo().then(res => {
                if (res.data.status === 0) {
                    vm.countInfo = res.data.data;
                    vm.dataList.num.num = res.data.data.modelPublishNum;
                    vm.dataList.visit.num = res.data.data.userLoginNum;
                    vm.dataList.browse.num = res.data.data.modelBrowseNum;
                    vm.dataList.attention.num = res.data.data.modelFocusNum;
                }
            })
        },
        //查询模型排行榜列表
        getRankingList(index) {
            const vm = this, {manageServices, manageMock, settings} = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                rankType: vm.activeName,
                pageNum: index,
                pageSize: vm.pageSize,
            };
            settings.loading = true;
            vm.currentPage = index;
            vm.listData = [];
            services.modelRankingList(data , settings).then(res => {
                if (res.data.status === 0) {
                    res.data.data.dataList.forEach(n=>{
                        n.avgscore = n.avgscore ? parseFloat(n.avgscore.toFixed(2)) : 0;
                        n.label = n.labels ? n.labels.map(p=>p.labelName) : []
                    })
                    vm.listData = res.data.data.dataList;
                    vm.totalCount = res.data.data.totalCount;
                }
            })
        },
        //查询模型用户排行上架榜列表
        modelPublishRankingList() {
            const vm = this, {manageServices, manageMock, settings} = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                rankType: "userPublish",
                pageNum: 1,
                pageSize: 5
            }
            services.modelUserPublishRankingList(data).then(res => {
                if (res.data.status === 0) {
                    vm.userPublishList = res.data.data.dataList;
                }
            })
        },
        //查询模型用户排行关注榜列表
        modelUserRankingList() {
            const vm = this, {manageServices, manageMock, settings} = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                rankType: "userFocus",
                pageNum: 1,
                pageSize: 5
            }
            services.modelUserPublishRankingList(data).then(res => {
                if (res.data.status === 0) {
                    vm.userFocusList = res.data.data.dataList;
                }
            })
        },
        tabClick() {
            this.getRankingList(1);
        },
    }
}
</script>

<style scoped lang="less">
.market-home {
    height: 100%;
    overflow: hidden;

    .bg-model-num {
        background: rgba(0, 136, 255, 0.75);
    }

    .bg-model-visit {
        background: rgba(109, 135, 241, 0.85);
    }

    .bg-model-browse {
        background: rgba(179, 210, 108, 0.85);
    }

    .bg-model-attention {
        background: rgba(54, 178, 173, 0.75);
    }

    &_top {
        height: 124px;
        background-color: #fff;
        overflow: hidden;
        display: flex;
        padding: 21px 28px;
        width: calc(100% - 9px);
        box-sizing: border-box;
        justify-content: space-between;

        .grid-content:not(:last-child) {
            margin-right: 20px;
        }

        &_icon {
            font-size: 30px;
            color: #fff;
            margin-right: 15px;
            border-radius: 2px;
            padding: 5px;
            width: 40px;
            height: 40px;
            line-height: 30px;
            box-sizing: border-box;
        }

        &_number {
            font-family: DINCondensed-Bold;
            font-size: 28px;
            text-align: right;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding-left: 10px;
        }

        &_name {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);

        }

        &_item {
            display: flex;
            align-content: center;
            align-items: center;
        }
    }

    &_body {
        width: calc(100% - 9px);
        display: flex;
        min-height: calc(100vh - 124px - 114px);
        padding-top: 14px;
        box-sizing: border-box;
        &_left {
            background: #fff;
            flex: 1;
            margin-right: 14px;
            border-radius: 2px;
            position: relative;
        }

        &_more {
            position: absolute;
            top: 16px;
            right: 28px;
            z-index: 20;
            color: rgba(0,0,0,.45);
            cursor: pointer;
        }

        &_pagination {
            text-align: right;
            padding: 0 21px;
        }

        &_right {
            width: 20rem;
        }

        &_box {
            background: #fff;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            align-content: flex-start;
            padding: 0 21px 14px;
            height: 700px;
            .list-card {
                width: calc(33.3% - 16px);
                margin: 7px;
            }
        }
    }

}

.grid-content {
    border-radius: 2px;
    background: rgba(0, 136, 255, 0.04);
    height: 82px;
    line-height: 82px;
    padding: 0 21px;
    display: flex;
    flex: 1;
    justify-content: space-between;
}

.ce-share_tab {
    &.el-tabs--top /deep/ .el-tabs__item.is-bottom:nth-child(2),
    &.el-tabs--top /deep/ .el-tabs__item.is-top:nth-child(2) {
        padding-left: 30px;
    }

    &.el-tabs--top /deep/ .el-tabs__item {
        line-height: 56px;
        height: 56px;
    }
}

</style>
