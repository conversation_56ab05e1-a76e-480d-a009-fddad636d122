<template>
    <div class="histogramAttr chartAttr">
        <el-tabs v-model="activeName" size="mini" type="border-card" class="ce-tabs_two ce-tabs_flex" >
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-attr_body">
            <data-list ref="data_list" v-bind="$props" @setAxisTxt="setAxisTxt" @setAxisName="setAxisName" @nodeDataRenew="nodeDataRenew" v-show="activeName === 'data'"></data-list>
            <style-list ref="style_list" @editAlias="editAlias" v-bind="$props" :active="activeName === 'style' || isEdit" @nodeDataRenew="nodeDataRenew" v-show="activeName === 'style'"></style-list>
            <senior-list ref="senior_list" v-bind="$props" @renewAllData="renewAllData" @nodeDataRenew="nodeDataRenew" v-show="activeName === 'senior'"></senior-list>
        </div>
        <div class="ce-attr_foot">
            <el-button class="pct60" type="primary" size="mini" @click="renewAllData">{{btnLabel}}</el-button>
        </div>
    </div>
</template>

<script src="./pgis.js"></script>
<style scoped lang="less" src="../css/attr.less"></style>
<style scoped lang="less" src="./pgis.less"></style>