<template>
    <div class="height100">
        <el-form :model="form" label-position="right" label-width="150px">
            <el-form-item label="计算类型：" required>
                <dg-select v-model="form.type" :data="typeList" filterable></dg-select>
            </el-form-item>
            <el-form-item label="离线计算库：" v-if="form.type !== 'FeatureDetailExtraction'">
                <dg-select v-model="form.schemaId" :data="schemaList" filterable></dg-select>
            </el-form-item>
            <el-form-item label="模式名称："  v-if="form.type !== 'FeatureDetailExtraction'">
                <el-input v-model="form.schemaName"></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "index",
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data() {
            return {
                form: {
                    type: '',
                    schemaId: '',
                    schemaName: '',
                },
                typeList: [],
                schemaList: [],
            }
        },
        methods:{
            async init() {
                this.loadParams.loading = true;
                await this.$axios.all([
                    this.getBusinessType(),
                    this.getSchemaList(),
                ]).catch(() => {
                    this.loadParams.loading = false;
                })
                await this.sapecialBusinessQueryData();
                this.loadParams.loading = false;
            },
            async getSchemaList() {
                const vm = this, {loadParams} = this;
                let services = vm.$services("servicesServices");
                loadParams.loading = true;
                await services.getSchemaList(loadParams).then(res => {
                    vm.schemaList = res.data.data.map(n=>{
                        return {label: n.name, value: n.id};
                    });
                })
            },
            async sapecialBusinessQueryData() {
                const vm = this, {loadParams} = this;
                let services = vm.$services("servicesServices");
                loadParams.loading = true;
                await services.sapecialBusinessQueryData(vm.sourceData.id,loadParams).then(res => {
                    vm.form = {
                        type: res.data.data.type,
                        schemaId: res.data.data.schemaId,
                        schemaName: res.data.data.schemaName,
                    }
                })
            },
            async getBusinessType() {
                const vm = this, {loadParams} = this;
                let services = vm.$services("servicesServices");
                loadParams.loading = true;
                await services.getBusinessType(loadParams).then(res => {
                    vm.typeList = res.data.data.map(n=>{
                        n.label = n.name;
                        n.value = n.code;
                        return n;
                    })
                })
            },
            save(fn,showTip) {
                const vm = this , {loadParams} = vm;
                if(!vm.form.type){
                    return vm.$message.warning("请选择计算类型");
                }
                // if(vm.form.type === 'FeatureCalculation' && !vm.form.schemaId){
                //     return vm.$message.warning("请选择离线计算库");
                // }
                loadParams.loading = true;
                let services = this.$services("servicesServices");
                let data ={
                    tranStepId: vm.sourceData.id,
                    type: vm.form.type,
                    schemaId: vm.form.type === 'FeatureDetailExtraction' ? '' : vm.form.schemaId,
                    schemaName: vm.form.type === 'FeatureDetailExtraction' ? '' : vm.form.schemaName,
                    settings: loadParams ,
                };
                services.savePlugin(data).then(res => {
                    showTip && vm.$message.success("保存成功");
                    vm.$emit("setPreviewAble");
                    fn && fn();
                })
            },
        },
        mounted() {
            this.init();
        }
    }
</script>

<style scoped>

</style>
