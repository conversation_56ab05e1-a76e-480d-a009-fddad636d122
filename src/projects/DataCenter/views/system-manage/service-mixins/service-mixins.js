export const servicesMixins = {
    data(){
        const {isMock} = this;
        let userServices = require("@/projects/DataCenter/services/system-manage-services/user-manage-services") ,
            userMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/user-manage-mock"):{},
            groupServices = require("@/projects/DataCenter/services/system-manage-services/user-group-manage-services"),
            groupMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/user-group-manage-mock"):{},
            rolesServices = require("@/projects/DataCenter/services/system-manage-services/roles-manage-services"),
            rolesMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/roles-manage-mock"):{},
            operatorServices = require("@/projects/DataCenter/services/system-manage-services/operator-manage-services"),
            operatorMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/operator-manage-mock"):{},
            busServices = require("@/projects/DataCenter/services/system-manage-services/bus-manage-services") ,
            busMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/bus-manage-mock"):{} ,
            mediationServices = require("@/projects/DataCenter/services/system-manage-services/mediation-services") ,
            mediationMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/mediation-mock") :{};
        return {
            userServices ,
            userMock ,
            groupServices ,
            groupMock ,
            rolesServices ,
            rolesMock,
            operatorServices ,
            operatorMock,
            busServices ,
            busMock,
            mediationServices,
            mediationMock,
        }
    }
}
