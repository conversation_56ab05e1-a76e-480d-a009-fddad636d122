/**
 * 快速分析 接口
 * @Author: qzx
 * @Date: 2021-09-01
 * @Project cicada-dataCenter-webui
 */

import * as reqUtil from '@/api/reqUtil'

const root = "/dataModeling/rapidAnalysis/" , root1="/transOperator/rapid/";

/*
*  查询目录树
 * @param
 * @returns {Promise<*>}
* */

export async function getRapidAnalysisTree(settings ) {
    return await reqUtil.get({
        url : root + "getRapidAnalysisTree" ,
        settings : settings
    })
}

/*
*  新增目录树
 * @param parentClassifyId  classifyName dirType:TRANS_RAPID_DIR_MF
 * @returns {Promise<*>}
* */

export async function addRapidAnaDir(data,settings ) {
    return await reqUtil.post({
        url : root + "addRapidAnaDir" ,
        settings : settings
    },data)
}


/*
*  重命名目录树
 * @param classifyId  newClassifyName
 * @returns {Promise<*>}
* */

export async function renameRapidDir(data,settings ) {
    return await reqUtil.get({
        url : root + "renameRapidDir" ,
        settings : settings
    },data)
}

/*
*  移动目录
 * @param curryClassifyId  newParentClassifyId
 * @returns {Promise<*>}
* */

export async function moveDirectory(data,settings ) {
    return await reqUtil.get({
        url : root + "moveDirectory" ,
        settings : settings
    },data)
}

/*
*  删除目录
 * @param transClassifyId
 * @returns {Promise<*>}
* */

export async function deleteDir(data,settings ) {
    return await reqUtil.get({
        url : root + "deleteDir" ,
        settings : settings
    },data)
}

/*
*  列表展示
 * @param classifyId  rapidAnaName pageNum pageSize
 * @returns {Promise<*>}
* */

export async function getRapidAnaPage(data,settings ) {
    return await reqUtil.post({
        url : root + "getRapidAnaPage" ,
        settings : settings
    },data)
}


/*
*  查询目录及列表数据
 * @param transClassifyId
 * @returns {Promise<*>}
* */
export async function getTreeAllNodeRapidAnaPage(dirType , settings) {
    return await reqUtil.get({
        url :root + "getTreeAllNode" ,
        settings : settings
    },{dirType})
}

/*
*  删除
* */
export async function deleteTransRapid(transId , settings) {
    return await reqUtil.get({
        url :root1 + "deleteTrans" ,
        settings : settings
    },{transId})
}

/*
*  另存为
* */
export async function asSaveTransRapid(transId , classifyId , transName , memo ,settings) {
    return await reqUtil.post({
        url :root1 + "asSaveTrans" ,
        settings : settings
    },{transId , classifyId , transName , memo})
}

/*
*  移动
* */
export async function moveModelRapid(elementId ,classifyId, settings) {
    return await reqUtil.get({
        url :root1 + "moveModel" ,
        settings : settings
    },{elementId , classifyId})
}

/*
*  保存
* */
export async function updateTransRapid(params , settings) {
    return await reqUtil.post({
        url :root1 + "updateTrans" ,
        settings : settings
    },params)
}

/*
*  新建模型
* */
export async function saveTempTransRapid(transName,transType="QUICK_SPARK",settings) {
    return await reqUtil.get({
        url :root1 + "saveTempTrans" ,
        settings : settings
    },{transName,transType})
}