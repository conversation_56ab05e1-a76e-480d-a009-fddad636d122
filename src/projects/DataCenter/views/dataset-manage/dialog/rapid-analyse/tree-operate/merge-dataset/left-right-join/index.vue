<template>
    <div class="join" v-loading="settings.loading">
        <div class="merge-tabs-leftright">
            <div class="m-t-l-item">
                <label>{{join_table}}</label>
                <div class="m-t-l-i-content">
                    <ce-select-drop
                            class="expect-select"
                            ref="pos_tree"
                            placeholder="请选择数据集"
                            :props="defaultProps"
                            :disabled="disabled"
                            filterable
                            check-leaf
                            check-strictly
                            clearable
                            visible-type="leaf"
                            :tree-props="treeBind"
                            :filterNodeMethod="filterNode"
                            v-model="tableName"
                            @input="datasetChange"
                            :data="datasets"></ce-select-drop>
                </div>
            </div>
            <template v-if="tableName.length">
                <div class="m-t-l-item">
                    <label>{{join_label}}</label>
                    <div class="m-t-l-i-content">
                        <dg-radio-group
                                size="small"
                                :disabled="disabled"
                                :value="join_way"
                                :data="join_opts"
                                @change="radioChange"
                        ></dg-radio-group>
                    </div>
                </div>
                <div class="m-t-l-item" v-if="hasData">
                    <label>{{join_gist}}</label>
                    <div class="m-t-l-i-content">
                        <dg-table
                                class="ce-table"
                                :data="tableData"
                                :pagination="false"
                                size="mini"
                                style="width: calc(100% - 8px)"
                                :max-height="maxheight"
                                border
                        >
                            <!--<dg-table-column minWidth="120" label="关联结果" align="center">
                                <template slot-scope="{row}">
                                    <el-input placeholder="请输入关联结果" v-model="row.result"></el-input>
                                </template>
                            </dg-table-column>-->
                            <dg-table-column minWidth="160"  label="当前表" align="center">
                                <template slot-scope="{row}">
                                    <dg-select class="width100" filterable :disabled="disabled" v-model="row.source" :data="tableList" @change="setFieldCode(arguments , row ,'source')"></dg-select>
                                </template>
                            </dg-table-column>
                            <dg-table-column  v-for="(column , key) in tableListOpts"
                                              :key="key"
                                              align="center"
                                              :minWidth="minWs[key]"
                                              :prop="key"
                                              :label="listLabel[key]">
                                <template slot-scope="{row}">
                                    <dg-select
                                            v-model="row[key]"
                                            :disabled="disabled"
                                            filterable
                                            :data="column"
                                            class="width100"
                                            @change="setFieldCode(arguments , row ,key)"
                                    ></dg-select>
                                </template>
                            </dg-table-column>
                            <dg-table-column :fixed="'right'" width="70" align="center">
                                <template slot="header">
                                    <dg-button type="text" :disabled="disabled" class="el-icon-circle-plus " @click="addMergeTable"></dg-button>
                                </template>
                                <template slot-scope="{row , $index}">
                                    <dg-button type="text" :disabled="disabled" class="el-icon-delete "
                                       @click="deleteRow($index)"
                                    ></dg-button>
                                </template>
                            </dg-table-column>
                        </dg-table>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script src="./left-right-join.js"></script>

<style scoped lang="less" src="./left-right-join.less"></style>