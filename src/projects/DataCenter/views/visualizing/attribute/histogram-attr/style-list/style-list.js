import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

export default {
    name: "styleList" ,
    components : {
        chartStyle,
    },
    mixins :[attrStyle],
    props: {
        node: Object,
        dir_dataSetId : String
    },
    data(){
        return {
            activeTabs:["title", "legend","xAxis" , "yAxis" , "fieldSetting"] ,
            styleList : {
                title :{
                    label :"标题内容",
                    settingForm :{
                        show : {
                            label : "显示标题" ,
                            type : "checkbox",
                        },
                        custom_style : {
                            label : "字体样式" ,
                            type : "sty_radio"
                        },
                        fontFamily : {
                            type : "select_font",
                            isLine : true
                        },
                        fontSize :{
                            type : "select_size" ,
                            isLine : true
                        },
                        fontWeight : {
                            type : "select_w",
                            isLine : true
                        },
                        fontStyle : {
                            type : "checkBtn" ,
                            isLine : true
                        },
                        color : {
                            type : "font_color" ,
                            isLine : true
                        },
                        align :{
                            type :"font_align" ,
                            isLine : true
                        },
                        text : {
                            type : "form_input" ,
                            pos : "right" ,
                            label : "标题名称" ,
                            maxlength :30 ,
                            width : "76px",
                            placeholder :"请輸入标题名称(限30个字符)"
                        },
                    },
                    form : {
                        show : true ,
                        custom_style : "default",
                        fontFamily : "sans-serif",
                        fontSize : "12px",
                        fontWeight : "normal",
                        fontStyle : [],
                        color : "#666" ,
                        text : "" ,
                        align : "center"
                    }
                },
                legend :{
                    label : "图表样式" ,
                    settingForm :{
                        label_show : {
                            label : "显示数据标签" ,
                            type : "checkbox",
                        },
                        label_pos :{
                            label : "数据标签显示位置" ,
                            type : "form_select" ,
                            width : "140px",
                            pos :"right",
                            options : [
                                {value :"outside" , label: "外部"} ,
                                {value :"inside" , label: "内部"} ,
                                {value :"insideTop", label: "内部居上"} ,
                                {value :"insideBottom", label: "内部居底"} ,
                            ]
                        },
                        legend_pos : {
                            title : "图例设置",
                            type : "radio_btns",
                            align :"center" ,
                            options : [
                                {
                                    label : '上方' ,
                                    value :'top'
                                },{
                                    label : '下方' ,
                                    value :'bottom'
                                },{
                                    label : '右侧' ,
                                    value :'right'
                                },{
                                    label : '左侧' ,
                                    value :'left'
                                },{
                                    label : '无' ,
                                    value :'none'
                                }
                            ]
                        } ,
                        orient :{
                            label :"图例列表布局" ,
                            type : "form_select" ,
                            width : "140px",
                            pos :"right",
                            options : [
                                {value :"horizontal" , label: "水平"} ,
                                {value :"vertical" , label: "垂直"} ,
                            ]
                        },
                        color : {
                            label : "配色方案" ,
                            type : "legend_color",
                            width : "90px"
                        },
                        show_dataZoom : {
                            label : "显示滚动条",
                            type : "checkbox"
                        },
                        zoomLock : {
                            label : "锁定数据区域",
                            type : "checkbox"
                        },
                        zoomArea : {
                            label : "数据范围",
                            type : "num_area",
                            start : "起点" ,
                            end :"末端" ,
                            step : 1 ,
                            min : 0 ,
                            max :100,
                            unit :"%"
                        },
                        minSpan :{
                            label : "最小数据区域",
                            type : "radio_number",
                            step : 1 ,
                            min : 0 ,
                            max :100,
                            unit :"%"
                        },
                        top : {
                            label : "上边距" ,
                            type : "number_slider",
                            step : 1 ,
                            min : 0 ,
                        },
                        bottom : {
                            label : "下边距" ,
                            type : "number_slider",
                            step : 1 ,
                            min : 0 ,
                        },
                        left : {
                            label : "左边距" ,
                            type : "number_slider",
                            step : 1 ,
                            min : 0 ,
                        },
                        right : {
                            label : "右边距" ,
                            type : "number_slider",
                            step : 1 ,
                            min : 0 ,
                        }
                    },
                    form : {
                        color_type :"custom",
                        color_theme :"default",
                        color : [] ,
                        label_show : false ,
                        label_pos : "outside",
                        orient :"horizontal",
                        legend_pos : "top" ,
                        show_dataZoom : true ,
                        top_type : "abs" ,
                        top : 80 ,
                        bottom_type : "abs" ,
                        bottom : 26 ,
                        left_type : "abs" ,
                        left : 40 ,
                        right_type : "abs" ,
                        right : 100 ,
                        zoomLock: false ,
                        setSpan : "default",
                        minSpan : 0,
                        start : 0 ,
                        end : 100 ,
                    }
                },
                xAxis :{
                    label : "水平轴" ,
                    settingForm : this.getAxisList("水平"),
                    form : {
                        showName : true ,
                        text : "" ,
                        nameLocation :"end",
                        show_axisLine : true,
                        line_width : 1 ,
                        line_type :"solid" ,
                        line_color : "#555" ,
                        axisLabel : -60
                    }
                },
                yAxis :{
                    label : "垂直轴" ,
                    settingForm : this.getAxisList("垂直"),
                    form : {
                        showName : true ,
                        text : "" ,
                        nameLocation :"end",
                        show_axisLine : true,
                        line_width : 1 ,
                        line_type :"solid" ,
                        line_color : "#555" ,
                        axisLabel : 0,
                        nameRotate : 0
                    }
                },
                fieldSetting : {
                    label :"系列设置" ,
                    settingForm : {
                        alias : {
                            label : "别名" ,
                            type :"field_alias",
                            maxlength :100 ,
                            width : "120px",
                            pos :"right",
                            placeholder : "请输入别名(限100个字符)"
                        }
                    },
                    form :{
                        options : [],
                    }
                }
            },
            emitStyle :{}
        }
    },
    methods :{
        getAllStyle(){
            const {emitStyle} = this;
            let styles = {};
            for(let k in emitStyle){
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        getAxisList(name){
            let form = {
                showName : {
                    label : `显示${name}轴名称` ,
                    type : "checkbox",
                },
                text : {
                    label : `${name}轴名称`,
                    placeholder :"请輸入轴名称",
                    type :"form_input",
                    width : "120px",
                    pos :"right",
                },
                nameRotate :{},
                nameLocation : {
                    label :`${name}轴名称显示位置` ,
                    type : "radio_btns",
                    align :"left",
                    options : [
                        {value : "start" , label :"起点"},
                        {value : "center" , label :"居中"},
                        {value : "end" , label :"末端"},
                    ]
                },
                show_axisLine :{
                    label : `显示${name}轴` ,
                    type : "checkbox",
                    isLine : true
                },
                line_type : {
                    type : "select",
                    class : "w100",
                    options : [
                        {value :"solid", label : "实线"},
                        {value :"dashed", label : "虚线"},
                        {value :"dotted", label : "点线"},
                    ],
                    isLine : true
                },
                line_width : {
                    type : "number",
                    isLine : true ,
                    step : .5 ,
                    min : 0 ,
                    max : 10
                },
                line_color :{
                    type :"color_picker" ,
                    isLine : true
                },
                axisLabel : {
                    label :"刻度标签旋转角度" ,
                    type : "number" ,
                    step : 1 ,
                    min : -90 ,
                    max : 90
                }
            };
            if(name === '垂直'){
                form.nameRotate = {
                    label :"轴名称旋转角度" ,
                    type : "number" ,
                    step : 1 ,
                    min : -90 ,
                    max : 90
                };
            }
            return form;
        },
        setStackLabel(){
            let stackCode = ['TransverseStackBarChartWidget' , 'StackBarChartWidget'];
            if(stackCode.indexOf(this.node.code) > -1){
                this.styleList.legend.form.label_pos = "inside";
            }
        },
        /**
         * 初始化样式配置
         */
        initValue(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                vm.setStackLabel();
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData) ;
                let {title , legend , xAxis ,yAxis , fieldSetting } = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                vm.setBarOrLineDefaultLegend(legend);
                xAxis ? vm.emitStyle.xAxis = vm.styleList.xAxis.form = xAxis : vm.emitStyle.xAxis = vm.styleList.xAxis.form ;
                yAxis ? vm.emitStyle.yAxis = vm.styleList.yAxis.form = yAxis : vm.emitStyle.yAxis = vm.styleList.yAxis.form ;
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form ;
                vm.node.setData = false;
            })
        },
        setAxisTxt(node, key){
            let names = [];
            const vm = this;
            if(node && key){
                node.forEach(no => {
                    let name = no.alias || no.label;
                    names.push(name);
                });
                let axis;
                let transverseCode = ['TransverseStackBarChartWidget' , 'TransverseBarChartWidget'];
                if(transverseCode.indexOf(vm.node.code) === -1 ){
                    axis = key === 'xAxis' ? 'yAxis' : 'xAxis';
                }else {
                    axis = key;
                }
                if(vm.styleList[axis]){
                    let axis_list;
                    if(vm.emitStyle[axis]){
                        axis_list = JSON.parse(JSON.stringify(vm.emitStyle[axis]));
                    }else {
                        axis_list = JSON.parse(JSON.stringify(vm.styleList[axis].form));
                    }
                    axis_list.text = names.join(' > ');
                    vm.styleList[axis].form = axis_list;
                }
            }
        },
    },

}
