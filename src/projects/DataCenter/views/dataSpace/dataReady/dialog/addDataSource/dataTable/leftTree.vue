<template>
    <div class="data-set-side">
        <template>
            <div class="tree" v-loading="settings.loading">
                <el-input
                        style="margin: 15px 0"
                        placeholder="关键字搜索"
                        suffix-icon="el-icon-search"
                        v-model="filterText"
                >
                </el-input>
                <dg-tree
                        node-key="id"
                        class="filter-tree"
                        :data="data"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        auto-expand-parent
                        highlight-current
                        :default-expanded-keys="defaultExpandedKeys"
                        ref="tree"
                        @node-click="nodeClick"
                >

                     <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }"
                           @mouseenter="rightMenu($event , node , data)">
                        <span class="node-label"
                              :title="data.label"
                              :class="{
                            'el-icon-folder' : !node.expanded  && !data.dataObjId,
                            'el-icon-folder-opened' : node.expanded && !data.dataObjId,
                            'el-icon-document':data.dataObjId
                          }"
                        >{{ data.label }}</span>

                    </span>
                </dg-tree>
            </div>
        </template>
    </div>
</template>

<script>
import {treeMethods} from "@/api/treeNameCheck/treeName"
import * as dataSourcesServices from "@/projects/DataCenter/views/dataSources/service-mixins/service-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";

export default {
    name: "leftTree",
    mixins: [treeMethods, dataSourcesServices.servicesMixins, commonMixins, servicesMixins],
    data() {
        return {
            data: [],
            defaultProps: {
                children: "children",
                label: "label"
            },
            filterText: "",
            allDataObj: [],
            defaultExpandedKeys: [],
            selectLeftTree: {},
            dbType: ["GREENPLUM", "ORACLE", "POSTGRESQL", "hwmpp", "Mysql" , "HIVE","TBASE" , "vertica" , "RdbDataObj","gbase"],
        }
    },
    props: {
        add: Boolean,
        selTableInfo: Object,
        isShowDW: Boolean,//是否有数仓权限
        leftTreeId: String,//编辑时左侧数选择的表
    },
    methods: {
        initTree() {
            const vm = this, {listApi, listMockApi, dataReadyServices, dataReadyMock, settings} = this;
            let listServices = vm.getServices(listApi, listMockApi);
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            settings.loading = true;
            vm.data = [];
            vm.defaultExpandedKeys = [];
            if (vm.isShowDW) {
                services.getDataPreparationTree(settings).then(res => {
                    vm.setTreeDataAEvent(res, settings, vm);
                });
            } else {
                listServices.getDatabaseDataObjectTree(settings).then(res => {
                    vm.setTreeDataAEvent(res, settings, vm);
                });
            }
        },
        /**
         *  数据表的数据，
         *  leftTreeId 编辑时的 表id ，add 新建数据集为true， 编辑时 ，执行 showTree 方法
         *  selTableInfo 由 数据源 基于某张表 创建数据集 进来的数据
         *  $emit("backCurNode"）传 当前选择的表 数据，在预览接口会使用
         */
        setTreeDataAEvent(res, settings, vm) {
            settings.loading = false;
            if (res.data.status === 0) {
                let result = res.data.data;
                vm.data = vm.filterTreeDataType(result);
                vm.leftTreeId && !vm.add ? vm.showTree(result, vm.leftTreeId) : '';
                if (vm.selTableInfo && vm.selTableInfo.id) {
                    vm.$emit('getListInfo', {id: vm.selTableInfo.tableMappingId, dataObjId: vm.selTableInfo.id});
                    vm.defaultExpandedKeys = [vm.selTableInfo.tableMappingId];
                    vm.$nextTick(() => {
                        vm.$refs.tree.setCurrentKey(vm.selTableInfo.tableMappingId);
                        let curData = this.$refs.tree.getNode(vm.selTableInfo.tableMappingId).data
                        vm.selectLeftTree = curData;
                        vm.$emit("backCurNode", curData);
                    })
                }
            }
        },
        /**
         * 过滤数据集类型
         * @param data
         * @returns {*[]}
         */
        filterTreeDataType(data=[]){
            const vm = this;
            const types = this.dbType.map(it => it.toLowerCase());
            return data.filter(li => {
                if(li.children && li.children.length) li.children = vm.filterTreeDataType(li.children);
                return li.children && li.children.length || li.instanceType && types.includes(li.instanceType.toLowerCase());
            })
        },
        getNode(list, id,) {
            for (let i in list) {
                if (list[i].dataObjId == id) {
                    return list[i];
                } else if (list[i].children) {
                    let node = this.getNode(list[i].children, id);
                    if (node !== undefined && node) {
                        return node;
                    }
                }
            }
        },
        getTreeData(data, id) {
            if (data.data.children)
                data.data.children = data.data.children.filter(n => n.id == id);
            if (data.level != 1) {
                return this.getTreeData(data.parent, data.data.id);
            } else {
                return data.data;
            }
        },
        showTree(treeData, leftTreeId) {
            this.$nextTick(() => {
                let nodeInfo = this.getNode(treeData, leftTreeId);
                if (nodeInfo) {
                    let nodeSelf = this.$refs.tree.getNode(nodeInfo.id);
                    this.defaultExpandedKeys = [nodeInfo.id];
                    this.data = [this.getTreeData(nodeSelf.parent, nodeSelf.data.id)];
                    this.$nextTick(() => {
                        this.$refs.tree.setCurrentKey(nodeInfo.id);
                        this.$emit("backCurNode", nodeSelf.data);
                    })
                }
            })
        },
        nodeClick(n) {
            this.selectLeftTree = {};
            if (n.instanceType === 'RdbDataObj') {
                this.selectLeftTree = n;
                this.$emit('getListInfo', n);
            }
        },
    }
}
</script>

<style lang="less" scoped>
.data-set-side {
    height: 100%;

    .tree {
        height: 100%;

        h3 {
            font-weight: bold;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }

        &__content {
            margin: 0 -28px;
        }

        &__item {
            display: flex;
            align-items: center;
            line-height: 32px;
            cursor: pointer;
            padding: 0 28px;

            &:hover {
                /*background-color: rgba($color: #1890ff, $alpha: 0.08);*/
            }

            i {
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                margin-right: 6px;
            }

            span {
                color: rgba(0, 0, 0, 0.85);
            }
        }
    }

    .icon-going {
        display: inline-block;
        transform: rotateY(180deg);
        margin-right: 4px;
    }
}

.filter-tree {
    overflow: auto;
    height: calc(100% - 30px - 2rem);
}

</style>
