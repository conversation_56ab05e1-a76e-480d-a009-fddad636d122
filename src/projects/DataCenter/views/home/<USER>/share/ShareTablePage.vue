<template>
<!-- 我的分享-->
    <div class="ShareTablePage" v-loading="settings.loading">
        <div class="height100" v-show="showStyle">
            <div :class="isEdited ? 'ShareTablePage_cont' : 'ShareTablePage_dispay'">
                <dg-scrollbar>
                    <share-card ref="showCard" :data="tableData" v-bind="$attrs" :isEdited="isEdited" show="myShare" @caseShareCardClick="caseShareCardClick">
                        <no-data :src="emptyImg" slot="empty" :title="emptyTxt" top="5%">
                            <div class="pt5" v-if="!isEdited && !settings.loading">
                                <el-button type="primary" @click="goToShare">{{ shareTxt }}</el-button>
                            </div>
                        </no-data>
                    </share-card>
                </dg-scrollbar>
            </div>
            <el-pagination
                    v-if="isEdited"
                    class="tr"
                    @size-change="changeSize"
                    @current-change="changePage"
                    v-bind="paginationProps"
                    :total="total">
            </el-pagination>
        </div>
        <div class="height100" v-show="!showStyle">
            <common-table
                    ref="multipleTable"
                    v-show="!isEdited ? tableData.length : true"
                    :data="tableData"
                    :columns="isEdited ? [selectOpt ,...tableColumns , operateOpt] : tableColumns"
                    :border="false"
                    :pagination-props="paginationProps"
                    :pagination-total="total"
                    stripe
                    :header-cell-class-name="({column})=>{ if(column.property === 'sourceName') return 'ce-case-source_name'}"
                    :max-height="tableBodyH"
                    @change-current="changePage($event) "
                    @change-size="changeSize"
                    @selection-change="selectionChange"
            >
                <template slot="sourceName" slot-scope="{row , $index}">
                    <div class="source" @click="caseShareCardClick(row)">{{ row.sourceName }}</div>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                    <el-popconfirm
                            title="取消分享后，资源将无法使用！确认取消分享?"
                            @confirm="operateBtn.clickFn( [row] , $index)">
                        <el-button slot="reference" type="text"
                                   :title="operateBtn.name"
                        >{{ operateBtn.name }}
                        </el-button>
                    </el-popconfirm>
                </template>
            </common-table>
            <no-data v-if="!isEdited && tableData.length === 0" :src="emptyImg" :title="emptyTxt" top="5%">
                <div class="pt5">
                    <el-button type="primary" @click="goToShare">{{ shareTxt }}</el-button>
                </div>
            </no-data>
        </div>
        <!--数据集详情页面-->
        <data-details ref="dataDetails"/>
        <!--数据服务详情页面-->
        <detailDialog ref="detailDialog"></detailDialog>
        <!--可视化详情页面-->
        <VisualizationEdit ref="visual_panel" v-if="showEditPanel" @closePanel="closeEditPanel"  />
    </div>
</template>

<script>
import emptyImg from "@/assets/images/common/empty-img.png";
import NoData from "@/components/no-data"
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {coustTableH} from "dc-front-plugin/src/api/commonMethods/count-table-h";
import {servicesMixins} from "../../service-mixins/service-mixins";
import ShareCard from "@/projects/DataCenter/views/home/<USER>/share/ShareCard";
import dataDetails from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/dataDetails";
import detailDialog from "@/projects/DataCenter/views/serviceManage/dialog/detailDialog"
import VisualizationEdit from '@/projects/DataCenter/views/visualizing/edit-panel/VisualizationEdit'
export default {
    name: "ShareTablePage",
    mixins: [commonMixins, coustTableH, servicesMixins],
    components: {NoData, ShareCard, dataDetails, detailDialog, VisualizationEdit},
    props: {
        showStyle: {
            type: Boolean,
            default: true
        },
        isEdited: {
            type: Boolean,
            default: false
        },
        pageSize: {
            type: Number,
            default: 9
        },
        pageSizes: {
            type: Array,
            default: () => {
                return [9, 18, 27, 36]
            }
        }
    },
    data() {
        return {
            showEditPanel : false,
            tableData: [],
            operateBtn: {
                name: "取消分享",
                clickFn: this.cancelShareSource
            },
            paginationProps: {
                currentPage: 1,
                pageSizes: [9, 18, 27, 36],
                pageSize: 9,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            shareTxt: "去分享",
            emptyImg,
            emptyTxt: "暂未分享相关内容",
            selectOpt: {
                type: "selection",
                "reserve-selection": true
            },
            operateOpt: {
                prop: "operate",
                label: "操作",
                align: "center",
                width: "150"
            },
            tableColumns: [
                {
                    prop: "sourceName",
                    label: "资源名称"
                },
                {
                    prop: "sourceType",
                    label: "资源类型"
                },  {
                    prop: "targetType",
                    label: "对象类型"
                }, {
                    prop: "target",
                    label: "被分享的对象"
                }, {
                    prop: "time",
                    label: "分享时间",
                    width: "200"
                }
            ],
            sourceType: "",
            sourceName: "",
            filterTxt: "",
            filterType: "",
            cancelStoreData : [] ,//取消分享数据
            isModelDetail: false, //是否是模型详情中的分享
            transId: '',
            typeList:{
                "dataSet" : "数据集",
                "dashboard" : "仪表盘",
                "dataService" : "数据查询",
                "modelService" : "模型分析",
                "aiService" : "AI算法",
                "compareService" : "比对订阅",
                "informationVerification" : "信息核查",
                "dataCollision" : "数据碰撞",
            }
        }
    },
    watch: {
        showStyle(val) {
            this.triggerEvent("resize", 300);
        }
    },
    methods: {
        caseShareCardClick(data) {
            if (data.sourceType === '数据集') {
                let row = Object.assign({}, data);
                row.id = row.resourceId;
                row.name = row.sourceName || row.resourceName;
                this.$refs.dataDetails.show(row);
            }else if (data.sourceType === '仪表盘'){
                this.openVisualizingPanel(data);
            }
            else {
                this.apiDetails(data);
            }
            // else if (data.sourceType === '数据查询'){
            //     this.$refs.detailDialog.show(data.resourceId);
            // }
            // else if(data.sourceType === '模型分析' || data.sourceType === 'AI算法'){
            //     this.$refs.detailDialog.show(data.serviceId, "service");
            // }

        },
        /**
         * API详情跳转
         * @param {} row
         */
        apiDetails(row){
            const vm = this;
            let modelInfo ={}
            modelInfo.transId =  row.resourceId;
            let layer = vm.$dgLayer({
                title: "api详情",
                content: require("@/projects/DataCenter/views/modeling/dialog/details/detail-page/apiServer/index"),
                move: false,
                props : {
                    info: Object.assign(modelInfo, row),
                    isAi: row.sourceType === 'AI算法' ? true : false,
                    type: row.sourceType != 'AI算法' ? 'modeling' : '',
                    isShare : true,
                    serviceDetail: {
                        serviceId: row.serviceId
                    }
                },
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
                area: ["80%", "80%"],
                btnAlign: 'r',
            });
        },
        /**
         *   可视化跳转
         * @param {*} data 
         */
        openVisualizingPanel(data){
            this.showEditPanel = true;
            this.$nextTick(()=>{
                this.$refs.visual_panel.getRouterData(  data.resourceId,"" , "" , "");
            })
        },
        closeEditPanel(groupId , filterTxt) {
            this.showEditPanel = false;
        },
        selectionChange(value) {
            this.cancelStoreData = value;
            this.$emit("getSelectVo", value);
        },
        changePage(inx = 1) {
            this.isModelDetail ? this.modelDetailList(inx) :this.shareList(inx);
        },
        shareList(inx){
            const vm = this;
            let {homeServices, homeMock, settings} = this;
            settings.loading = true;
            let params = {
                "pageNum": !vm.isEdited ? 1 : inx,
                "pageSize": !vm.isEdited ? 9 : this.paginationProps.pageSize,
                "resourceName": this.sourceName,
                "resourceType": this.sourceType,
                "userType": this.filterType === 'user' ? 0 : this.filterType === 'role' ? 2 : "", //0 用户 2 角色
                "username": this.filterTxt
            };
            vm.tableData = [];
            let services = vm.getServices(homeServices, homeMock);
            services.myShares(params, settings).then(res => {
                if (res.data.status === 0) {
                    res.data.data.dataList.forEach(element => {
                        let object = {
                            "sourceType": vm.typeList[element.resourceType],
                            "sourceName": element.resourceName,
                            "targetType": element.userTypeByShare === '0' ? "用户" : "角色",
                            "target": element.usernameByShare,
                            "time": element.shareTime ? new Date(element.shareTime).Format("yyyy-MM-dd hh:mm:ss") : "",
                            resourceId: element.resourceId,
                            userIdByShare: element.userIdByShare,
                            id: element.id,
                            resourceType : element.resourceType,
                            serviceId:element.serviceId
                        };
                        if(element.resourceType !== 'aiService'){
                            vm.tableData.push(object);
                        }
                        else{
                            vm.$hideAI ? '' : vm.tableData.push(object);
                        }
                    });
                    vm.paginationProps.currentPage = res.data.data.pageIndex;
                    vm.paginationProps.pageSize = res.data.data.pageSize;
                    vm.total = res.data.data.totalCount;
                    vm.countHeight();
                    // if (vm.showStyle) vm.$refs.showCard.clearChooseCard();
                }
            })
        },
        //模型详情中分享列表
        modelDetailList(inx){
            const vm = this,{ settings} = this;
            settings.loading = true;
            let params = {
                "pageNum": !vm.isEdited ? 1 : inx,
                "pageSize": !vm.isEdited ? 9 : this.paginationProps.pageSize,
                "resourceName": this.sourceName,
                "tranId": vm.transId,
                "objId":"",
            };
            vm.tableData = [];
            let services = vm.$services('modeling');
            services.getSharesByTran(params, settings).then(res => {
                if (res.data.status === 0) {
                    if(res.data.data.dataList && res.data.data.dataList.length){
                        res.data.data.dataList.forEach(element => {
                            let object = {
                                sourceType: vm.typeList[element.func_type],
                                sourceName: element.name,
                                targetType: element.obj.obj_type,
                                target: element.obj.obj_name,
                                time: element.create_time ? new Date(element.create_time).Format("yyyy-MM-dd hh:mm:ss") : "",
                                serviceId: element.serviceId,
                                id: element.id,
                                resourceId: element.id,
                                userIdByShare: element.obj.id,
                            };
                            vm.tableData.push(object);
                        });
                    }
                    vm.paginationProps.currentPage = res.data.data.pageIndex;
                    vm.paginationProps.pageSize = res.data.data.pageSize;
                    vm.total = res.data.data.totalCount;
                    vm.countHeight();
                    // if (vm.showStyle) vm.$refs.showCard.clearChooseCard();
                }
            })
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage();
        },
        /**
         * 外部调用 查询表格
         * @param sourceType 资源类型
         * @param sourceName 资源名称关键字
         * @param filterTxt 用户名或角色名
         * @param filterType 是否用户或角色
         */
        getTableData(sourceType, sourceName, filterTxt, filterType, isModelDetail, transId) {
            this.sourceType = sourceType;
            this.sourceName = sourceName;
            this.filterTxt = filterTxt;
            this.filterType = filterType;
            this.isModelDetail = isModelDetail;
            this.transId = transId;
            this.changePage();
        },
        /**
         * 取消分享
         * @param row
         */
        cancelShareSource(row) {
            const vm = this;
            let {homeServices, homeMock, settings} = this, resourceInfo = [], rowInfo = [];
            rowInfo = row;

            // if (vm.showStyle) {
            //     if (this.$refs.showCard.chooseCardInfo.length === 0) {
            //         flag = false;
            //     }
            //     this.tableData.forEach(item =>{
            //         if(this.$refs.showCard.chooseCardInfo.indexOf(item.id) > -1){
            //             let object = {
            //                 "objId": item.userIdByShare,
            //                 "objType": item.targetType === '用户' ? 0 : 2,
            //                 "funcCode": item.resourceId,
            //             };
            //             resourceInfo.push(object);
            //         }
            //     })
            // } else {
            resourceInfo = rowInfo.map(element => {
                return  {
                    "objId": element.userIdByShare,
                    "objType": element.targetType === '用户' ? 0 : 2,
                    "funcCode": element.resourceId,
                };
            });
            // }

            let params = {
                resources: resourceInfo,
            };
            settings.loading = true;
            let services = vm.getServices(homeServices, homeMock);
            services.cancelShare(params, settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("成功取消分享");
                    vm.removeSelection(row);
                    vm.changePage();
                }
            })
        },
        /**
         * 更新取消分享选择数据
         * */
        removeSelection(selections){
            const vm = this ;let {cancelStoreData} = vm;
            // let list = selections.map(can => can.id);
            // vm.cancelStoreData = cancelStoreData.filter(da => !list.includes(da.id));
            // vm.$emit("getSelectVo", vm.cancelStoreData);
            this.$refs.multipleTable.$refs.table.clearSelection()
        },
        /**
         * 表格初始化配置
         */
        tableInit() {
            this.paginationProps.pageSize = this.pageSize;
            this.paginationProps.pageSizes = this.pageSizes;
        },
        goToShare() {
            this.$router.push({name: "share", params: {tab: "ShareTableList", operate : "goToShare"}});
        }
    },
    created() {
        this.tableInit();
    }
}
</script>

<style scoped lang="less">
    @import "./shareCard.less";
</style>
