@color: #000;
@alpha65: .65;

.header-name {
  font-family: MicrosoftYaHei;
  font-size: 14px;
  color: rgba(@color, @alpha65);
  min-width:36px;
}

.name_txt {
  padding: 0 10px;
  font-weight: normal;
  font-size: 14px;
  max-width:calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 120px;
  cursor: pointer;
}


.history-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  width: 240px;
  height: 40px;
  background-color: #ffffff;
  box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  margin-top:5px;
}

.ce-detail_link {
  cursor: pointer;
}
.ce-detail_link:hover {
  color: @link-color;
}
.ce-time_h {
  max-height: calc(60vh);
  overflow: auto;
}
.ce-error_color {
  color:#F56C6C;
}