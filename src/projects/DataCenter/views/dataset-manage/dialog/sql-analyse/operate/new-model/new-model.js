import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../../service-mixins/service-mixins";
import NewModelName from './new-model-name'
export default {
    name: "newModel" ,
    mixins : [ dialog , commonMixins , servicesMixins],
    components : {
        NewModelName
    },
    props : {
        treeNode : Object
    },
    data(){
        return {
            title : "" ,
            width : "660px" ,
            label :{}
        }
    },
    methods :{
        show(nodeId){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.label = {
                engLabel: "英文名:",
                chLabel: "中文名:",
            };
            vm.title = "新建即席SQL分析";
            vm.$nextTick(() => {
                vm.$refs.newName.showSelect("model" , nodeId);
            })
        },
        submit(){
            const vm = this , {datasetServices , datasetMock ,settings } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            vm.$refs.newName.validate(valid => {
                if(valid){
                    settings.loading = true;
                    let {chName ,engName ,savePath, schemaId} = vm.$refs.newName.getFormData();
                    services.sqlCreate(savePath ,engName , chName ,settings,schemaId).then(res => {
                        if(res.data.status === 0){
                            vm.$emit('showPanel', {name: chName, id: res.data.data,schemaId});
                            vm.visible = false;
                        }
                    })

                }
            })

        }
    }
}