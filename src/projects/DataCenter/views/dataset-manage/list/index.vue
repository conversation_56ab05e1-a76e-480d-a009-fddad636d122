<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <div class="d-s-r-t-serach">
                <el-input
                        size="mini"
                        placeholder="请输入名称搜索"
                        v-model.trim="filterTxt"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'filterTxt')"
                        @keyup.enter.native="searchFn"
                >
                    <el-button type="primary" slot="append" @click="searchFn" class="ce-search_button" icon="el-icon-search"></el-button>
                </el-input>
            </div>
            <div class="d-s-r-t-btns">
                <dg-button icon="el-icon-data-line" v-if="rights.indexOf(rightSql) > -1 && treeNode.treeType !== 'sourceDataSetTree'" @click="sqlAnalyse">{{buttonTxt[0]}}</dg-button>
                <dg-button type="primary" icon="el-icon-news" v-if="rights.indexOf(rightAna) > -1 && treeNode.treeType !== 'sourceDataSetTree'" @click="nowAnaly">{{buttonTxt[1]}}</dg-button>
            </div>
        </div>
        <div class="d-s-r-table" v-if="!settings.loading">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    height="100%"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                <template v-if="row.db_type !== 'ElasticSearch'" slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action"
                          v-for="(item,index) in hasRightOptIcon"
                          :key="index"
                          v-if="index < 3"
                    >
                        <dg-button type="text"
                                   class="icon"
                                   size="medium"
                                   :class="item.class"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.tip"
                                   v-html="item.icon"
                        ></dg-button>
                    </span>
                    <el-dropdown trigger="click" @command="menuCommand" v-if="countHasRightMenu > 3">
                                <span class="el-dropdown-link">
                                    <i class="el-icon-caret-bottom el-icon right"></i>
                                </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                    v-for="(m_icon , i) in hasRightOptIcon"
                                    :key="i"
                                    :command="{m_icon ,row , $index}"
                                    v-if="i >= 3"
                            >
                                <dg-button type="text"
                                           class="icon"
                                           size="medium"
                                           :class="m_icon.class"
                                           :title="m_icon.tip"
                                           v-html="m_icon.icon"
                                ></dg-button>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </common-table>
        </div>
        <rapid-form ref="rapid_form" ></rapid-form>
        <new-model ref="sql_form" @showPanel="showSqlPage"></new-model>
        <sql-analyse ref="sql_panel" @closePlane="closeSqlPanel" v-if="showSqlPanel" :rowData="sqlRowData"></sql-analyse>
        <issue ref="issue" @closeService="closeServiceIssue" :row=row v-if="isShowIssue"></issue>
        <moveto-tree ref="moveto" @moveSuccess="moveSuccess"></moveto-tree>
        <sync-logic ref="syncLogic" ></sync-logic>
        <PlanDataPreviewDialog ref="planDataPreviewDialog"/>
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>