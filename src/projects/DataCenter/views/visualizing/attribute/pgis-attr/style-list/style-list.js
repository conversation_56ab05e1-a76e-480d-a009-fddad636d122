import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

export default {
    name: "styleList",
    components: {
        chartStyle,
    },
    mixins: [attrStyle],
    props: {
        node: Object,
        dir_dataSetId: String
    },
    data() {
        let typeOptions = [
                {
                    label: "国家地图",
                    value: "country"
                }, {
                    label: "区域地图",
                    value: "area"
                }
            ], mapOptions = this.transFormArea(0),
            mapCode = this.$map_area_code;
        return {
            activeTabs: ["title", "funcSettings" , "fieldSetting"],
            styleList: {
                title: {
                    label: "标题内容",
                    settingForm: {
                        show: {
                            label: "显示标题",
                            type: "checkbox",
                        },
                        custom_style: {
                            label: "字体样式",
                            type: "sty_radio"
                        },
                        fontFamily: {
                            type: "select_font",
                            isLine: true
                        },
                        fontSize: {
                            type: "select_size",
                            isLine: true
                        },
                        fontWeight: {
                            type: "select_w",
                            isLine: true
                        },
                        fontStyle: {
                            type: "checkBtn",
                            isLine: true
                        },
                        color: {
                            type: "font_color",
                            isLine: true
                        },
                        align: {
                            type: "font_align",
                            isLine: true
                        },
                        text: {
                            type: "form_input",
                            pos: "right",
                            label: "标题名称",
                            maxlength: 30,
                            width: "76px",
                            placeholder: "请輸入标题名称(限30个字符)"
                        },
                    },
                    form: {
                        show: true,
                        custom_style: "default",
                        fontFamily: "sans-serif",
                        fontSize: "12px",
                        fontWeight: "normal",
                        fontStyle: [],
                        color: "#666",
                        text: "",
                        align: "center"
                    }
                },
                funcSettings: {
                    label: "功能配置",
                    settingForm: {
                        mapCode: {
                            label: '显示范围',
                            type: 'map_code',
                            placeholder: '请选择地图',
                            width: "100px",
                            pos: "right",
                            props: {checkStrictly: true},
                            type_options: typeOptions,
                            sec_label: "显示内容",
                            sec_options: [{label: '中国', value: "china"}],
                            options: mapOptions
                        },
                    },
                    form: {
                        map_type: "area",
                        mapCode: mapCode ,
                        mapLevel : 6
                    }
                },
                fieldSetting : {
                    label :"系列设置" ,
                    settingForm : {
                        alias : {
                            label : "别名" ,
                            type :"field_alias",
                            maxlength :100 ,
                            width : "120px",
                            pos :"right",
                            placeholder : "请输入别名(限100个字符)"
                        }
                    },
                    form :{
                        options : [],
                    }
                }

            },
            emitStyle: {},

        }
    },
    methods: {
        getAllStyle() {
            const {emitStyle} = this;
            let styles = {};
            for (let k in emitStyle) {
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        setTransAxisLabel() {
            this.styleList.xAxis.form.axisLabel = 0;
        },
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                let transverseCode = ['TransverseStackBarChartWidget', 'TransverseBarChartWidget'];
                if (transverseCode.indexOf(vm.node.code) > -1) {
                    vm.setTransAxisLabel();
                }
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData);
                let {title, funcSettings , fieldSetting} = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                funcSettings ? vm.emitStyle.funcSettings = vm.styleList.funcSettings.form = funcSettings : vm.emitStyle.funcSettings = vm.styleList.funcSettings.form;
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form ;
                vm.node.setData = false;
            })
        },
        setAxisTxt(node, key) {
            let names = [];
            const vm = this;
            if (node && key) {
                node.forEach(no => {
                    let name = no.alias || no.label;
                    names.push(name);
                });
                let axis;
                let transverseCode = ['TransverseStackBarChartWidget', 'TransverseBarChartWidget'];
                if (transverseCode.indexOf(vm.node.code) === -1) {
                    axis = key === 'xAxis' ? 'yAxis' : 'xAxis';
                } else {
                    axis = key;
                }
                if (vm.styleList[axis]) {
                    let axis_list;
                    if (vm.emitStyle[axis]) {
                        axis_list = JSON.parse(JSON.stringify(vm.emitStyle[axis]));
                    } else {
                        axis_list = JSON.parse(JSON.stringify(vm.styleList[axis].form));
                    }
                    axis_list.text = names.join(' > ');
                    vm.styleList[axis].form = axis_list;
                }
            }
        },
    },

}
