<template>
    <div class="serviceOutPut">
        <el-tabs v-model="activeName">
            <el-tab-pane label="返回结果" name="backResult" > </el-tab-pane>
            <el-tab-pane label="高级配置" name="configuration"></el-tab-pane>
        </el-tabs>
        <div class="serviceOutPut_content">
            <div v-if="activeName === 'backResult'">
                <p class="mb10 ce-disable_for-btn">
                    <dg-button type="primary" @click="addColumn">添加返回字段</dg-button>
                    <dg-button @click="deleteInfo">删除</dg-button>
                </p>
                    <common-table
                            ref="dataList"
                            height="calc(100% - 42px)"
                            :pagination="false"
                            row-key="columnCode"
                            :data="dataList"
                            :columns="tableHead"
                            @selection-change="tableSelectionChange"
                    >

                        <template slot="operate" slot-scope="{row , $index}">
                            <el-popconfirm
                                    title="确认删除?"
                                    @confirm="deleteVar(row,$index)"
                            >
                                <el-button type="text"
                                           slot="reference"
                                           :title="'删除'">
                                    <span>删除</span>
                                </el-button>
                            </el-popconfirm>
                        </template>
                    </common-table>
            </div>
            <div v-else>
                <p>服务配置</p>
                <el-form
                        ref="ruleForm"
                        :rules="rules"
                        :model="configuration"
                        style="width: 100%;"
                        label-width="150px"
                >
                    <el-form-item label="服务调用方式：" required>
                        <el-radio-group v-model="configuration.async">
                            <!--<el-radio :label="false">同步调用</el-radio>-->
                            <el-radio :label="true">异步调用</el-radio>
                        </el-radio-group>
                        <div class="el-upload__tip">
                            {{configuration.async ? tip1 : tip}}
                        </div>
                    </el-form-item>
                    <el-form-item label="返回结果是否分页：" v-if="configuration.async">
                        <el-radio-group v-model="configuration.pageFlag">
                            <el-radio :label="false">不分页</el-radio>
                            <el-radio :label="true">分页</el-radio>
                        </el-radio-group>
                        <div class="el-upload__tip">
                            当返回结果记录数大于100时请选择分页，不分页则最多返回100条记录。
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <common-dialog custom-class="custom" :width="'800px'"
                       :title="'添加返回字段'"
                       :visible.sync="visible"
                       @closed="clearD">
            <div>
                <div class="ce-table__search mb10">
                    <el-input
                            size="mini"
                            placeholder="请输入搜索关键字"
                            v-model.trim="serachText"
                            @change="searchCode"
                    >
                        <i
                                class="el-icon-search el-input__icon poi"
                                slot="suffix"
                                @click="searchCode">
                        </i>
                    </el-input>
                </div>
                <common-table
                        ref="list"
                        height="calc(70vh - 154px)"
                        :pagination="false"
                        row-key="columnCode"
                        :data="columnData"
                        :columns="columnHead"
                        @selection-change="handleSelectionChange"
                >
                    <template slot="append" slot-scope="{row , $index}">
                        <el-table-column
                                type="selection"
                                width="55">
                        </el-table-column>
                    </template>
                </common-table>
            </div>
            <span slot="footer">
            <dg-button @click="clearD">取消</dg-button>
            <dg-button type="primary" @click="saveColumn">确定</dg-button>
        </span>
        </common-dialog>

        <preView ref="preview"></preView>
    </div>
</template>

<script>
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    export default {
        name: "metaServiceOutPut",
        components: {
            preView
        },
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data() {
            return {
                activeName: 'backResult',
                tableHead:[
                    {
                        type: "selection",
                        width: "50",
                    },
                    {
                        prop: "columnCode",
                        label: "字段名",
                        align: 'left'
                    }, {
                        prop: "columnName",
                        label: "字段中文名",
                        align: 'left'
                    },{
                        prop: "columnType",
                        align: "left",
                        label: "字段类型",
                    }, {
                        prop: "operate",
                        align: "center",
                        label: "操作",
                        width: "120"
                    }
                ],
                dataList: [],
                rules: {},
                configuration: {
                    pageFlag: false,
                    async: true,
                },
                tip: '应用系统请求时同步等待服务响应返回。',
                tip1: '应用系统请求后，再采用异步轮询获取服务响应的接入方式。',
                visible:false,
                columnData: [],
                columnHead: [
                    {
                        prop: "columnCode",
                        label: "参数名称",
                        align: 'left'
                    }, {
                        prop: "columnName",
                        label: "字段中文名",
                        align: 'left'
                    }, {
                        prop: "columnType",
                        align: "left",
                        label: "参数类型",
                    }
                ],
                columnList: [],
                serachText: '',
                columnDataCopy: [],
                multipleTableInfo: [],
                multipleSelection: [],
                longList: ["BYTE","SHORT","INTEGER","BIGINTEGER","LONG"],
                doubleList: ["FLOAT","BIGDECIMAL","DOUBLE"],
            }
        },
        mounted() {
            this.init();
        },
        methods: {
            searchCode() {
                const vm = this;
                vm.columnData = vm.filterText(vm.columnDataCopy);
                vm.multipleSelection.forEach(row => {
                    let info = vm.columnData.find(n=>n.columnCode == row.columnCode);
                    vm.$nextTick(()=>{
                        vm.$refs.list.$refs.table.toggleRowSelection(row);
                    })
                });
            },
            filterText(val) {
                let res = val.filter((v) => {
                    if(v.columnCode.toUpperCase().search(this.serachText.toUpperCase()) !== -1 ) {
                        return true;
                    }
                    else if(v.columnName !== null) {
                        if(v.columnName.toUpperCase().search(this.serachText.toUpperCase()) !== -1) return true;
                        else return false;
                    }
                    else {
                        return false;
                    }
                })
                return res;
            },
            setDataType(n){
                if(this.longList.includes(n.toLocaleUpperCase())){
                    return 'Long'
                }
                else if(this.doubleList.includes(n.toLocaleUpperCase())){
                    return 'Double'
                }
                else if(n.toLocaleUpperCase() === "BOOLEAN"){
                    return 'Boolean'
                }
                else{
                    return 'String'
                }
            },
            /**
             * 初始化-查询信息
             * */
            init(){
                const vm = this, {loadParams} = this;
                let services = vm.$services("servicesServices");
                loadParams.loading = true;
                services.getFields(vm.sourceData.id, loadParams).then(res => {
                    if (res.data.status === 0) {
                        vm.columnList = res.data.data.inputColumn.map(n=>{ return { columnCode: n.columnName, columnType: vm.setDataType(n.columnType), columnName: n.columnZhName}})
                        let ids = vm.columnList.map(n=>n.columnCode);
                        services.queryMetaInfoOutPut(vm.sourceData.id).then(res => {
                            if (res.data.status === 0) {
                                vm.dataList = res.data.data.serviceOutputColumns.filter(n=>ids.includes(n.columnCode));
                                if(vm.dataList.length)
                                    vm.$emit("setPreviewAble");
                                vm.configuration = {
                                    pageFlag: res.data.data.pageFlag,
                                    async: true,
                                }
                            }
                        })
                    }
                })

            },
            /**
             * 添加返回字段
             */
            addColumn(){
                this.columnData = [];
                this.serachText = '';
                let ids = this.dataList.map(n=> n.columnCode);
                this.columnList.forEach(n=>{
                    if(!ids.includes(n.columnCode)){
                        this.columnData.push(n);
                    }
                })
                this.columnDataCopy = [...this.columnData];
                this.visible = true;
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            /**
             * 添加返回字段-保存
             */
            saveColumn(){
                this.multipleSelection.forEach(n=>{
                    this.dataList.push(n)
                })
                this.clearD();
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);

            },
            /**
             * 保存服务输出信息
             */
            save(fn,showTip){
                const vm = this , {loadParams} = this;
                let services = vm.$services("servicesServices");
                let data = {
                    serviceOutputColumns: vm.dataList,
                    pageFlag: vm.configuration.pageFlag,
                    async: vm.configuration.async
                }
                loadParams.loading = true;
                services.saveMetaInfoOutPut(vm.sourceData.id, data, loadParams).then(res=> {
                    if (res.data.status === 0) {
                        showTip && vm.$message.success("保存成功");
                        vm.$emit("setPreviewAble");
                        fn && fn();
                    }
                })
            },
            tableSelectionChange(val){
                this.multipleTableInfo = val;
            },
            /**
             * 删除
             * @param inx
             */
            deleteVar(row,inx) {
                this.dataList.splice(inx, 1);
            },
            deleteInfo(){
                const vm = this;
                vm.multipleTableInfo.forEach(n=>{
                    let info = vm.dataList.find(p=>p.columnCode === n.columnCode);
                    let index =  vm.dataList.findIndex(p=>p.columnCode === n.columnCode);
                    if(index === 0 || index){
                        vm.dataList.splice(index, 1);
                        vm.$refs.dataList.$refs.table.toggleRowSelection(info);
                    }
                })
            },
            clearD(){
                this.multipleSelection = [];
                this.visible = false;
            },
            tabClick(){

            }
        }
    }
</script>

<style scoped lang="less">
.serviceOutPut{
    height: 100%;
    overflow: hidden;
    &_content {
        height: calc(100% - 54px);
        overflow: hidden;
        div{
            height: 100%;
        }
    }
}
</style>
