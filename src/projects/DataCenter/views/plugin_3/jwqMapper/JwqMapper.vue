<template>
    <div class="JwqMapper">
        <el-form class="attrForm" label-position="right" label-width="160px" :model="form">
            <el-form-item label="请选择经度字段:" required>
                <el-select
                        v-model="form.jdField"
                        placeholder="请选择字段"
                        filterable>
                    <el-option
                            v-for="opt in tableOption"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="请选择纬度字段:" required>
                <el-select
                        v-model="form.wdField"
                        placeholder="请选择字段"
                        filterable
                >
                    <el-option
                            v-for="opt in tableOption"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="请选择单位级别:" required>
                <el-radio v-for="item in jwTableData" :key="item.value" v-model="form.levelField" :label="item.value">{{item.label}}</el-radio>
            </el-form-item>
        </el-form>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import {common} from "@/api/commonMethods/common"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    export default {
        name: "JwqMapper",
        mixins: [pluginMixins,common, commonMixins, servicesMixins],
        components: {preView},
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return{
                tableOption: [],
                form: {
                    jdField: "",
                    wdField: "",
                    levelField: ""
                },
                ruleTypeOptions: [
                    {}
                ],
                outputColumn: {},
                defaultProps: {
                    value: 'value',
                    label: "label",
                    children: "children",
                },
                stepId: '',
                canSave: false,
                isSeniorShow: {},
                results: [],
                ruleTableData: [],
                jwTableData: [
                    {
                        label: "警务区",
                        value: "jwq"
                    }, {
                        label: "派出所",
                        value: "pcs"
                    },
                    {
                        label: "分局",
                        value: "fj"
                    },
                ],
                rules: {
                    jdField: [
                        {required: true, message: "经度字段不能为空", trigger: 'change'}
                    ],
                    wdField: [
                        {required: true, message: "纬度字段不能为空", trigger: 'change'}
                    ],
                    levelField: [
                        {required: true, message: "行政等级字段字段不能为空", trigger: 'change'}
                    ],
                }
            }

        },
        methods:{
            save(...args){
                if (this.form.jdField === '' || this.form.jdField === undefined || this.form.jdField === null){
                    this.$message.warning("经度字段不能为空!");
                    return;
                }
                if (this.form.wdField === '' || this.form.wdField === undefined || this.form.wdField === null){
                    this.$message.warning("纬度字段不能为空!");
                    return;
                }
                if (this.form.levelField === '' || this.form.levelField === undefined || this.form.levelField === null){
                    this.$message.warning("行政等级字段字段不能为空!");
                    return;
                }
                this.saveFn(...args);
            },

            preview(){
                this.previewFn();
            },

            previewFn() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },

            async saveFn(saveName,showTip) { //保存后才能预览
                const vm = this, {pluginServices, pluginMock, loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let jwqMapperMeta = {
                    jdField: vm.form.jdField,
                    wdField: vm.form.wdField,
                    levelField: vm.form.levelField,
                };
                loadParams.loading = true;
                services.saveJwqMapper(vm.sourceData.id, jwqMapperMeta, loadParams).then(res => {
                    if (res.data.status === 0) {
                        showTip && this.$message.success("保存成功!");
                        vm.$emit("setPreviewAble");
                        if(saveName)saveName();
                    }
                });
            },

            //初始化页面
            async init(node) {
                const vm = this, {pluginServices, pluginMock, loadParams} = this;
                loadParams.loading = true;
                vm.stepId = node.id;
                let services = vm.getServices(pluginServices, pluginMock);
                services.getJwqMapperPlugin(node.id,loadParams).then(res => {
                    vm.tableOption = res.data.data.column;
                    vm.tableOption.forEach(item => {
                        if (item.columnZhName == null) {
                            item.label = item.columnName;
                        } else item.label = item.columnZhName;
                        item.value = item.columnName;
                        // item.type = item.type;
                        return item;
                    });
                    vm.form.jdField = res.data.data.pluginMeta.jdField || "";
                    vm.form.wdField = res.data.data.pluginMeta.wdField || "";
                    vm.form.levelField = res.data.data.pluginMeta.levelField === null ? "jwq" : res.data.data.pluginMeta.levelField;
                    loadParams.loading = false;
                });
            },

        },
        created() {
            this.init(this.sourceData);
        }
    }
</script>
<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped>

</style>
