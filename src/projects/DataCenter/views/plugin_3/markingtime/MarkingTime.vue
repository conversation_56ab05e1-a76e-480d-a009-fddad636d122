<template>
    <div class="classificationCounting ce-marking">
        <div class="ce-marking__tip">
            <i class="el-icon-warning-outline"></i>
            <span class="pl5">{{msg}}</span>
        </div>
        <el-form
            ref="form"
            class="ce-marking__form"
            :model="{ list: markingTimeDataForm }"
            @submit.native.prevent>
            <common-table
                    height="100%"
                    :pagination="false"
                    :data="markingTimeDataForm"
                    :columns="tableHead"
            >
                 <template slot="header-operate" slot-scope="{row , $index}">
                    <el-button v-for="(btn , i) in operateIcon.head"
                               :key="i"
                               :title="btn.title"
                               :class="btn.icon" type="text"
                               class="dg-form-column__button" @click="btn.clickFn"></el-button>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                    <el-form-item>
                        <span v-for="(btn , i) in operateIcon.body" :key="i">
                            <el-popconfirm
                                    v-if="btn.type === 'delete'"
                                    size="mini"
                                    title="确认删除?"
                                    @confirm="btn.clickFn(row,$index)"
                            >
                                <el-button type="text"
                                        slot="reference"
                                        :title="btn.title">
                                    {{ btn.title }}
                                </el-button>
                            </el-popconfirm>
                        </span>
                    </el-form-item>
                </template>
                <template slot="columnValue" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.columnValue`"
                                    :rules="rules.columnValue">
                        <el-select size="mini"
                                    v-model="row.columnValue"
                                    placeholder="请选择输入字段"
                                    filterable
                        >
                            <el-option
                                v-for="item in columns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </template>
                <template slot="thresholdValue" slot-scope="{row , $index}">
                    <el-form-item :prop="`list.${$index}.thresholdValue`"
                        :rules="rules.thresholdValue">
                        <el-input-number
                             class="timeInputNumber" :min="1" :max="60"
                            controls-position="right" v-model="row.thresholdValue"
                        ></el-input-number>
                        <el-select v-model="row.timeFormatValue"
                            class="timeSelect">
                            <el-option
                                v-for="item in timeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </template>
                <template slot="normalizedFields" slot-scope="{row , $index}">
                      <el-form-item :prop="`list.${$index}.normalizedFields`"
                                    :rules="rules.normalizedFields">
                        <el-input v-model.trim="row.normalizedFields" v-input-limit:fieldCode></el-input>
                    </el-form-item>
                </template>
            </common-table>
        </el-form>

        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {pluginMixins} from "@/projects/DataCenter/views/plugin/component/plugin-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins"

export default {
    name: "MarkingTime",
    components: {
        PreView
    },
    mixins: [pluginMixins, commonMixins, servicesMixins],
    props: {
        sourceData: Object,
        rowData: Object,
        loadParams :Object
    },
    data() {
        return {
            msg: "系统只提供时间类型字段作为输入字段选择项",
             operateIcon: {
                head: [
                    {
                        title: "添加",
                        icon: "el-icon-plus",
                        clickFn: this.add
                    }
                ],
                body: [
                    {title: "删除", type: "delete", icon: 'el-icon-delete', clickFn: this.deleteClassificationData},
                ]
            },
            exps: [],
            addbtn: "添加",
            // timeFormatValue: "mins",
            timeOptions: [{
                value: 'hour',
                label: '时'
            }, {
                value: 'mins',
                label: '分'
            }, {
                value: 'seconds',
                label: '秒'
            }],
            minTip: "分",
            loading: false,
            dataTypes: ['Float', 'Double', 'BigDecimal'],
            isSeniorShow: {},
            activeName: 'setting_base',
            tab_panel: [
                {
                    label: '基本设置',
                    name: 'setting_base'
                }, {
                    label: '高级设置',
                    name: 'setting_senior'
                }
            ],

            rules: {
                columnValue: [
                    {required: true, message: '请选择输入字段', trigger: 'change'},
                ],
                normalizedFields: [
                    {required: true, message: '请输入输出字段', trigger: ['blur', 'change']},
                    {
                        validator: this.checkedCommon,
                        trigger: ['blur', 'change'],
                        reg: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                        message: "限字母、数字、下划线(非数字开头)"
                    },
                ],
                thresholdValue : [
                    {required: true, message: '请选择时间间隔', trigger: 'change'},
                ]
            },
            //分组统计
            markingTimeDataForm: [],
            tableHead :[
                {
                    prop : "columnValue" ,
                    label : "输入字段" ,
                    minWidth : "120"
                },
                {
                    prop : "thresholdValue" ,
                    label : "时间间隔" ,
                    minWidth : "120"
                },{
                    prop : "normalizedFields",
                    label : "输出字段",
                     minWidth : "120"
                },
                {
                    prop : "operate" ,
                    width : "90",
                    align : "center"
                }
            ],
            inputType: [],
            groupFieldName: [],
            columns: [],
        }
    },
    methods: {
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        selectDataType(val) {
            val.hasPrecision = this.checkDataType(val.outputTypeValue);
        },
        init() {
            const vm = this, {services , sourceData ,loadParams} = vm;
            loadParams.loading = true;
            services.markingTimeView(sourceData.id , loadParams).then(res => {
                if (res.data.status === 0) {
                    let pluginMeta = res.data.data;
                    //初始化输入字段
                    let columns = pluginMeta.column && pluginMeta.column.filter(col => {
                        col.name = col.columnZhName || col.columnName;
                        col.code = col.columnName ;
                        return col.columnType === "Date" || col.columnType === "Timestamp";
                    });
                    vm.columns = columns ? columns : [];
                    //初始化数据
                    if(pluginMeta.pluginMeta.markingTimeGroupInfo.length === 0) vm.add();
                    pluginMeta.pluginMeta.markingTimeGroupInfo.forEach(item => {
                        vm.markingTimeDataForm.push(
                            {
                                thresholdValue: item.thresholdValue, //阈值
                                columnValue: item.inputFieldCode,//输入字段
                                normalizedFields: item.normalizedFieldCode,//归一化字段
                                precisionValue: 0,
                                timeFormatValue: item.unit,
                            }
                        );
                    })
                }
            })
        },
        // 分组统计删除操作
        deleteClassificationData(row, index) {
            this.markingTimeDataForm.splice(index, 1);
        },
        save(saveName,showTip) {
            const vm = this , {services , sourceData ,loadParams} = vm;
            let pluginMeta = {};
            if (vm.markingTimeDataForm.length === 0) {
                vm.$message.warning("请添加配置信息！");
                return;
            }
            vm.validate(valid => {
                if (valid) {
                    pluginMeta['markingTimeGroupInfo'] = vm.getTimeGroup();
                    loadParams.loading = true;
                    services.saveMarkingTime(sourceData.id , pluginMeta , loadParams).then(res => {
                        if (res.data.status === 0) {
                            showTip && vm.$message.success("保存成功");
                            if(saveName) saveName();
                            vm.$emit("setPreviewAble");
                        }
                    })
                }
            })
        },
        getTimeGroup(){
            let result = [];
            const vm = this, {markingTimeDataForm , columns} = vm;
            markingTimeDataForm.forEach(item =>{
                let column = columns.find(col => item.columnValue === col.code);
                result.push({
                    'inputFieldName': column.name,
                    'inputFieldCode': column.code,
                    'thresholdValue': item.thresholdValue,
                    'normalizedFieldName': item.normalizedFields,
                    'normalizedFieldCode': item.normalizedFields,
                    'unit' : item.timeFormatValue,
                })
            })
            return result;
        },
        initSettingSenior() {
            this.$refs.settingSenior.initValue();
        },
        saveColumn() {
            this.$refs.settingSenior.saveFieldTable();
        },
        checkDataType(val) {
            return this.dataTypes.indexOf(val) > -1;
        },
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        add() {
            this.markingTimeDataForm.push(
                {
                    thresholdValue: 1, //阈值
                    columnValue: "",//输入字段
                    normalizedFields: "",//归一化字段
                    precisionValue: 0,
                    timeFormatValue: "mins"
                }
            );
        }
    },
    created() {

        this.init();
    }
}
</script>

<style lang="less" scoped src="../css/plugin.less"></style>
<style scoped lang="less">
.ce-marking {
    height: 100%;
    &__tip {
        color: #999;
        line-height:30px;
    }
    &__form {
        height:calc(100% - 30px);
    }
}
.countingRadio {
    width: 100%;
    text-align: center;
}

.countingContent {
    margin-top: 10px;
}

.countingSelect {
    height: 5px;
}

.btn_new_model {
    float: right;
    margin-bottom: 5px;
    margin-top: -27px;
    margin-left: 3px;
}

.timeInputNumber {
    width: 70%;
}

.timeSelect {
    padding-left:10px;
    width:30%;
}
</style>
