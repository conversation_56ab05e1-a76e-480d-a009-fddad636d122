<template>
    <div class="rapid-analysis">
        <!-- 头部菜单区域 -->
        <header class="rapid-analysis__header">
            <div class="rapid-analysis__tabs analysis-tab">
                <div class="rapid-analysis__add" :style="{'left' : addLeft+'px'}">
                    <!--                    <el-popover popper-class="ce-popover" placement="top-start" width="106" trigger="click" :visible-arrow="false" :offset="-50">
                                            <div class="rapid-analysis__add-box">
                                                <div @click="addTab({},false , dirId)">{{addModelTxt}}</div>
                                                <div>{{openModelTxt}}</div>
                                            </div>
                                            <i slot="reference" class="dg-iconp icon-l-plus"></i>
                                        </el-popover>-->
                    <i slot="reference" class="dg-iconp icon-l-plus" @click="addTab({},false , dirId)" title="新建数据集"
                       v-if="add"></i>
                </div>
                <model-tab
                        class="ce-rapid_tab ce-model_tab"
                        v-if="editableTabs.length"
                        v-model="editableTabsValue"
                        :editable-tabs="editableTabs"
                        @tab-click="tabClick"
                        @tab-remove="tabClose"
                >
                    <self-model-panel ref="selfPanel" slot="tabCont" slot-scope="{item}" :rowData="item.rowData"
                                      :tabId="item.id"/>
                </model-tab>
            </div>
            <span class="rapid-analysis__menus" ref="icon" @click="showMore = !showMore">
                <i class="dg-iconp icon-menu"></i>
            </span>
            <div class="rapid-analysis__more" v-show="showMore">
                <div class="rapid-analysis__item" v-for="(menu, idx) in editableTabs" :title="menu.label" :key="idx"
                     :class="{'active' : menu.id === editableTabsValue }" @click="checkTab(menu)">{{ menu.label }}
                </div>
            </div>
            <span class="rapid-analysis__actions">
                <el-button type="primary" size="mini" @click="handleSave('')">
                    <i class="dg-iconp icon-save"></i>{{ saveTip }}
                </el-button>
                <el-button size="mini" class="rapid-analysis__actions-item" @click="paramShow">
                    <!-- {{saveTip}} -->
                    {{paramTip}}
                </el-button>
                <el-divider direction="vertical" v-show="!isNewModel||save_dataSetId"></el-divider>
                <el-button size="mini" class="rapid-analysis__actions-item" @click="handleSave('saveOther')"
                      v-if="save_dataSetId">
                    <i class="dg-iconp icon-save"></i>{{ saveOTip }}
                </el-button>
                <!--<el-divider direction="vertical"></el-divider>-->
                <span class="rapid-analysis__actions-item"></span>
                <!--<i class="dg-iconp icon-help"></i>-->
<!--                <i :class="['dg-iconp', !isFull ? 'icon-maximize' : 'icon-minimum']" @click="handleFull"
                   :title="isFull?'退出全屏':'全屏'"></i>-->
                <i class="dg-iconp icon-l-close" @click="handleClose" title="关闭"></i>
            </span>
        </header>
        <save-model ref="SaveModel" @save="setSave" show-type :dsTypeId="setTypeId" :dirId="setDirId"></save-model>
        <GlobalVariable ref="variable" :rowData="rowData" />
    </div>
</template>
<script>
import SelfModelPanel from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/selfDataSet/modelPanel"
import ModelTab from "@/projects/DataCenter/views/modeling/dialog/model-tab"
import {tabMixins} from "@/projects/DataCenter/views/modeling/dialog/model-tab/model-tab-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import {mapActions, mapState} from "vuex";
import saveModel from "@/projects/DataCenter/views/dataSpace/dialog/saveModel";
// import {globalBus} from "@/api/globalBus";
import GlobalVariable from "../dataTable/global-variable/index"
export default {
    name: "selfDataSet",
    mixins: [tabMixins, commonMixins, servicesMixins],
    components: {
        ModelTab,
        SelfModelPanel,
        saveModel,
        GlobalVariable,
    },
    computed: {
        ...mapState({
            plans: (state) => state.plans.plans,
            planId: (state) => state.plans.planId,
            plansConfig: (state) => state.plans.plansConfig,
            outputColumns: (state) => state.plans.outputColumns,
            plansCondition: (state) => state.plans.plansCondition,
            editChange: (state) => state.plans.editChange,
            myCatalog: (state) => state.plans.myCatalog,
        }),
        isNewModel() {
            return this.currentRowData && this.currentRowData.isNew;
        },
        planAllSettings() {
            return {
                plansConfig: this.plansConfig,
                outputColumns: this.outputColumns,
                plansCondition: this.plansCondition
            }
        },
        setDirId(){
            return this.lastDirId || this.dirId
        },
        setTypeId(){
            return this.lastDsTypeId || this.typeId
        }
    },
    watch: {
        "editableTabs.length": {
            handler(len) {
                this.addPlan(this.editableTabs);
                this.setPlanId(this.editableTabsValue);
            },
            deep: true,
        },
        editableTabsValue(val) {
            this.setPlanId(val);
        },
    },
    props: {
        dirId: String,
        dsTypeId: String,
        type: String,
        dataSetID: String,
        add: Boolean,
    },
    data() {
        return {
            rowData : {
                isEditParam : true,
            },
            model: "FieldChoose",
            isFull: false,
            saveTip: "保存",
            saveOTip: "另存为",
            paramTip : "参数设置",
            showMore: false,
            data: [],
            save_dataSetId: '',
            isCloseAll : false ,//是否关闭全部
            shareId:'6a155c5cdf9d5ddcbd8459f05b37186a',
            saveIds: [],
            lastDirId: "", // 新建目录 id
            lastDsTypeId: "", // 新建分类 id 另存为 回显使用
        };
    },
    filters: {
        filterFunctions(activeFunction, functions) {
            let fit = functions.find((item) => item.value === activeFunction);
            if (fit) {
                return fit.label;
            } else {
                return "";
            }
        }
    },
    created() {
        this.saveIds = [];
        this.getTreeData();
    },
    mounted() {
        // 点击外部关闭更多消息
        const vm = this, {$services} = vm;
        // 获取类型分类
        this.getTypeList({$services});
        document.addEventListener("click", (e) => {
            const dom = document.getElementsByClassName("rapid-analysis__more")[0];
            const target = vm.$refs.icon;
            if (target && target.contains(e.target)) {
                return;
            }
            if (dom && !dom.contains(e.target)) {
                this.showMore = false;
            }
        });
    },
    beforeDestroy() {
        this.clearPlans();
    },
    methods: {
        ...mapActions(["addPlan", "setPlanId", "clearPlans" ,"setEditChange","filterOtherCatalog","getTypeList"]),
        paramShow() {
            let dataSetId = this.plans.filter(item => item.id === this.planId) && this.plans.filter(item => item.id === this.planId)[0].rowData.id;
            // if (!dataSetId) {
            //     this.$message.warning("请先进行保存!");
            //     return;
            // }
            this.$refs.variable.init(dataSetId);
        },
        tabClose(targetName) {
            const vm = this, {editableTabs ,editChange} = vm;
            let tabData = editableTabs.find(tab => tab.id === targetName);
            let {isNew} = tabData.rowData;
            if (isNew || editChange[targetName] ) {
                this.$dgAlert(`该数据集 "${tabData.label}" 未保存，是否放弃修改？`, "提示", {
                    type: 'warning',
                    showCancelButton: true,
                    showConfirmButton: true,
                    confirmButtonText: '保存并退出',
                    cancelButtonText: '取消编辑',
                }).then(() => {
                    vm.handleSave('');
                }).catch(_ => {
                    vm.removeTab(targetName);
                    if(vm.isCloseAll) vm.handleClose();
                });
            } else {
                vm.removeTab(targetName);
                if(vm.isCloseAll) vm.handleClose();
            }
            if (vm.editableTabs.length === 0 && vm.saveIds.length) {
                vm.$emit("close", [...new Set(vm.saveIds)]);
            }
        },
        //重写addNewTrans方法
        addNewTrans(rowData, transName, new_datasetId) {
            this.showFlow(rowData, new_datasetId, transName);
        },
        addTab(rowData, isTemplate = false, dirId) {
            const vm = this;
            let newTransName = '新建数据集_' + this.tabIndex;
            vm.tabIndex++;
            vm.isTemplate = isTemplate;
            rowData.isTemplate = isTemplate;
            rowData.saved = vm.saved = false;
            if (rowData.id === undefined) {
                rowData.dirId = dirId;
                vm.addNewTrans(rowData, newTransName);
            } else {
                rowData.saved = true;
                newTransName = rowData.name;
                vm.showFlow(rowData, rowData.id, newTransName);
            }
            vm.showAddBox = false;
        },
        //重写showFlow方法
        showFlow(rowData, new_trans_id, newTransName) {
            new_trans_id = new Date().getTime().toString();
            let tabLabel;
            tabLabel = rowData.name ? rowData.name : newTransName;
            let rowD = rowData.name ? rowData : {
                dirParentId: "",
                name: newTransName,
                id: '',
                isNew: true,
                ...rowData
            };
            let newTab = {
                id: new_trans_id,
                label: tabLabel,
                rowData: rowD,
            };
            this.currentRowData = rowD;
            let hasTab = false;
            hasTab = this.hasTabs(newTab, hasTab);//
            if (hasTab || this.editableTabs.length === 0) {
                this.editableTabs.push(newTab);
                this.editableTabsValue = new_trans_id;
            }
        },
        showCheckTip(res, msg) {
            this.$refs.SaveModel.stop();
            let data = res.reduce((a, b) => {
                return {name: (a.name || a.code) + '、' + (b.name || b.code)}
            })
            this.$message.warning(`'${data.name}' ${msg}`);
        },
        /**
         * 保存前校验
         * @param type
         * @return {ElMessageComponent}
         */
        handleSave(type) {
            const {planId, plans, plansConfig} = this;
            const fit = plans.find((item) => item.id === planId);
            const {rowData} = fit;
            if (this.joinValidate({plansConfig, planId})) return;
            let {dataFormat, aliasRes , nameRes} = this.$refs.selfPanel.validation();
            if (dataFormat.length) return this.showCheckTip(dataFormat, '未选择数据格式');
            if (aliasRes.length) return this.showCheckTip(aliasRes, '字段别名存在相同');
            if (nameRes.length) return this.showCheckTip(nameRes, '字段中文名存在相同');
            if (this.planId) {
                let isShowTree = type ? true : (rowData.isNew);
                this.$refs.SaveModel.show(rowData, this.treeInfo, !!type, fit.label, '', isShowTree);
            }
        },
        joinValidate({plansConfig, planId}) {
            let res = false;
            let hasSet = plansConfig[planId].some(li => li.joinType && !li.joinOption.joinVo);
            if (!(plansConfig[planId] && plansConfig[planId].length)) {
                res = true;
                this.$message.warning(`请先配置表关联`);
            } else if (plansConfig[planId].length === 1 || hasSet) {
                res = true;
                this.$message.warning(`请完善配置表关联`);
            }
            return res;
        },
        //另存为
        saveOther(name, id) {


        },
        //获取目录用于保存
        getTreeData() {
            const vm = this, {dataReadyServices, dataReadyMock, settings} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            settings.loading = true;
            services.queryDataSetTree(false, settings).then(res => {
                if (res.data.status === 0) {
                    vm.filterOtherCatalog({data: res.data.data[0].children})
                    vm.treeInfo = vm.myCatalog;
                }
            });
        },
        //保存字段信息
        setSave(value, id, type,row,level,dsTypeId) {
            const vm = this, {dataReadyServices, dataReadyMock, settings} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            settings.loading = true;
            if (type) { //暂无另存为
                // vm.saveOther(value, id);
                return;
            }
            let params = this.getSaveVo(value, id, dsTypeId);
            if (this.currentRowData.isNew) {
                services.saveSelfHelpDataSet(params, settings).then(res => {
                    if (res.data.code === 0) {
                        vm.lastDirId = id;
                        vm.lastDsTypeId = dsTypeId;
                        let datasetId = res.data.data.dataSetId;
                        vm.changeDatasetId(value, vm.planId, datasetId);
                        vm.$refs.SaveModel.stop();
                        vm.$message.success("保存成功");
                        vm.saveIds.push(datasetId)
                        vm.setEditChange({id:this.planId , change : false});
                        if(vm.isCloseAll) vm.handleClose();
                        vm.$nextTick(()=>{
                            // globalBus.$emit("saveAndPreview" , this.editableTabsValue)
                        })
                    }else {
                        vm.$refs.SaveModel.closeLoading();
                    }
                })
            } else {
                let firstTableCode = vm.plansConfig[vm.planId][0].code;
                let newParams = {
                    dataSetName: value,
                    dataSetCode: firstTableCode,
                    joinTableSteps: params.dataSetJoinVo,
                    columns: params.columns,
                    dataSetId: this.currentRowData.id,
                    dataObjIds : params.dataObjIds,
                    filterConditionStep: params.filterConditionStep
                };
                services.editSelfHelpDataSet(newParams, settings).then(res => {
                    if (res.data.code === 0) {
                        vm.changeDatasetId(value, vm.planId, newParams.dataSetId);
                        vm.$refs.SaveModel.stop();
                        vm.$message.success("保存成功");
                        vm.saveIds.push(newParams.dataSetId)
                        vm.setEditChange({id:this.planId , change : false});
                        if(vm.isCloseAll) vm.handleClose();
                        // globalBus.$emit("saveAndPreview" , this.editableTabsValue)
                    }else {
                        vm.$refs.SaveModel.closeLoading();
                    }
                })
            }
        },
        /**
         * 修改数据集名称 和数据集id
         * */
        changeDatasetId(name, id, datasetId) {
            const vm = this;
            this.triggerEvent('resize', 300);
            this.editableTabs.forEach(item => {
                if (item.id === id) {
                    item.label = name;
                    item.rowData.id = datasetId;
                    item.rowData.isNew = false;
                    item.rowData.saved = true;
                    vm.currentRowData = item.rowData;
                }
            })
            this.addPlan(this.editableTabs);
        },
        transCondition(data, columns) {
            if (!data) return [];
            data.forEach(con => {
                con.filterFieldInfo.forEach(li => {
                    let field = columns.find(ta => ta.columnAlias === li.field);
                    if (field) {
                        li.field = field.code;
                        li.fieldCode = field.code;
                    }
                    if (li.valueType === 'field') {
                        let valField = columns.find(ta => ta.columnAlias === li.value);
                        let valEndField = columns.find(ta => ta.columnAlias === li.valueEnd);
                        if (valField) {
                            li.value = valField.code;
                            li.inputValue = valField.code;
                        }
                        if (valEndField) li.valueEnd = valEndField.cdoe;
                    }
                })
            })
            return data;
        },
        /**
         * 设置保存参数
         * @param value
         * @param id
         * @param dsTypeId
         * @return {{dataObjIds, columns, name, dataSetTreeNodeId, dataSetJoinVo}}
         */
        getSaveVo(value, id,dsTypeId) {
            const {plansConfig, planId, outputColumns, plansCondition} = this;
            let columns = outputColumns[planId];
            let condition = this.transCondition(plansCondition[planId], columns);
            return {
                dataObjIds: plansConfig[planId].map(li => li.tableId),
                columns: columns.map(li => {
                    return {
                        indexType: li.indexType,
                        code: li.code,
                        belongParentId: li.belongParentId,
                        displayTypeId: li.displayTypeId,
                        dataTypeId: li.dataTypeId,
                        format: li.format,
                        name: li.name,
                        memo: li.memo,
                        id: li.id,
                        disabled: li.disabled,
                        columnAlias: li.columnAlias,
                        isHidden: false,
                        numberFormat: li.numberFormat,
                        isPk : li.isPk,
                    }
                }),
                name: value,
                dataSetDsTypeTreeNodeId: dsTypeId,
                dataSetTreeNodeId: id,
                dataSetJoinVo: plansConfig[planId].filter(li => !!li.joinType).map(it => it.joinOption.joinVo),
                filterConditionStep: {
                    condition: JSON.stringify(condition)
                }
            };
        },
        /**
         * 点击step
         */
        handleStepClick(step) {
            switch (step.value) {
                case "xzzd":
                    this.model = "FieldChoose";
                    break;
                case "tjgl":
                    this.model = "CondFilter";
                    break;
            }
        },
        /**
         * 关闭弹窗
         */
        handleClose() {
            let {editableTabs} = this;
            this.isCloseAll = true;
            if(editableTabs.length){
                this.tabClose(this.editableTabsValue);
            }
        },
        /**
         * 全屏
         */
        handleFull() {
            this.isFull = !this.isFull;
            if (
                    (document.fullScreenElement !== undefined && document.fullScreenElement === null) ||
                    (document.msFullscreenElement !== undefined && document.msFullscreenElement === null) ||
                    (document.mozFullScreen !== undefined && !document.mozFullScreen) ||
                    (document.webkitIsFullScreen !== undefined && !document.webkitIsFullScreen)
            ) {
                if (document.documentElement.requestFullScreen) {
                    document.documentElement.requestFullScreen();
                } else if (document.documentElement.mozRequestFullScreen) {
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.webkitRequestFullScreen) {
                    document.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            } else {
                if (document.cancelFullScreen) {
                    document.cancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        }
    }
};
</script>
<style lang="less" scoped>
.menu-active {
    background: rgba(0, 136, 255, 0.08);
    border-radius: 2px;
    font-size: 14px;
    color: #0088ff;
}

.rapid-analysis {
    width: 100%;
    height: 100%;
    overflow: hidden;

    &__header {
        width: 100%;
        display: block;
        align-items: center;
        background-color: #ffffff;
        //padding: 0 16px;
        height: 40px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        padding-top: 5px;
        position: relative;
    }

    &__actions {
        position: absolute;
        right: 20px;
        top: 14px;

        &-item {
            cursor: pointer;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }

        i {
            //font-size: 16px;
            //color: #000000;
            margin-right:6px;
            cursor: pointer;
        }
    }

    &__menus {
        position: absolute;
        padding-right: 20px;
        cursor: pointer;
        user-select: none;
        left: 10px;
        top: 14px;
        z-index: 10;
        background: #fff;

        &::before {
            content: "";
            position: absolute;
            top: 50%;
            margin-top: -9px;
            right: -1px;
            height: 18px;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.09);
        }
    }

    &__more {
        position: fixed;
        top: 46px;
        left: 0;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
        border-radius: 2px;
        max-width: 260px;
        max-height: 300px;
        z-index: 8;
        overflow: auto;
        overflow-x: hidden;

        div.rapid-analysis__item {
            line-height: 36px;
            cursor: pointer;
            padding: 0 20px;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &:hover, &.active {
                color: #0088ff;
            }
        }
    }

    &__tabs {
        align-items: center;
        height: 100%;
        display: block;
    }

    &__add {
        padding: 0 14px;
        cursor: pointer;
        line-height: 40px;
        z-index: 1;
        position: absolute;
        transition: 200ms;
        top: 4px;
        /*.ce-rapid_tab{*/
        /*/deep/.el-tabs__content{*/
        /*height: calc(100vh - 46px);*/
        /*}*/
        /*}*/

        &-box {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);

            div {
                line-height: 36px;
                cursor: pointer;

                &:hover {
                    color: #0088ff;
                }
            }
        }

        i {
            font-size: 16px;
        }
    }

    &__menu {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 20px;
        cursor: pointer;
        height: 100%;
        box-sizing: border-box;
        margin-top: 1px;

        &::before {
            content: "";
            position: absolute;
            top: 50%;
            margin-top: -9px;
            right: -1px;
            height: 18px;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.09);
        }

        i {
            margin-left: 4px;
        }

        &:hover {
            color: #0088ff;
        }

        &--active {
            color: #0088ff;
            background: #f5f5f5;
            border: 1px solid rgb(224, 224, 224);
            border-bottom: none;
            border-radius: 4px 4px 0 0;
        }
    }

    &__main {
        display: flex;
        margin-top: 8px;
        flex: 1;
        overflow: hidden;
    }

    &__aside {
        width: 208px;
        margin-right: 11px;
        overflow: hidden;

        /deep/ .el-card__body {
            height: 100%;
            overflow: auto;
        }

        &-content {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: 7px 14px;
            box-sizing: border-box;
        }
    }

    &__steps {
        overflow: auto;
    }

    &__wrap {
        overflow: hidden;
        flex: 1;
        margin-right: 14px;
    }

    &__open {
        position: fixed;
        top: 54px;
        right: 0;
        width: 45px;
        height: 120px;
        font-weight: bold;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        background: #ffffff;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
        border-radius: 2px 0 0 2px;
        padding: 13px;
        box-sizing: border-box;
        user-select: none;
        cursor: pointer;

        i {
            color: rgba(0, 0, 0, 0.45);
            font-weight: 400;
        }
    }
}

.sub-title {
    padding: 12px 14px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
}
</style>
