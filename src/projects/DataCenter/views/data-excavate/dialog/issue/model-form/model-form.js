export default {
    name: "modelForm",
    data() {
        return {
            serviceInfo: {
                serviceName: "",
                serviceChName: "",
                chName: "",
                engName: "",
                version: "",
                type: "4",
                explain: "",
            },
            service_p: {
                serviceName: "请输入服务英文名(限50个字符)",
                serviceChName: "请输入服务中文名(限50个字符)",
                chName: "请输入模型中文名(限50个字符)",
                engName: "请输入模型英文名(限50个字符)",
                version: "请输入模型版本号(限5位数)",
                explain: "请输入服务说明(限100个字符)",
            },
            serviceRules: {
                serviceName: [
                    {required: true, message: '服务中文名不能为空', trigger: ['change', 'blur']},
                    {validator: this.checkSame, trigger: ['change', 'blur']}
                ],
                serviceChName: [
                    {required: true, message: '服务英文名不能为空', trigger: ['change', 'blur']},
                    {validator: this.checkSame, trigger: ['change', 'blur']}
                ],
                chName: [
                    {required: true, message: '模型中文名不能为空', trigger: ['change', 'blur']},
                    {validator: this.checkSame, trigger: ['change', 'blur']}
                ],
                engName: [
                    {required: true, message: '模型英文名不能为空', trigger: ['change', 'blur']},
                    {validator: this.checkSame, trigger: ['change', 'blur']}
                ],
                version: [{required: true, message: '模型版本号不能为空', trigger: ['change', 'blur']}],
                type: [{required: true, message: '服务类型不能为空', trigger: ['change', 'blur']}],
            },
            serviceTypeOption: [
                {
                    label: "查询服务",
                    value: "4",
                }
            ],
        }
    },
    methods: {
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        ruleSame(rule, value, serviceInfo) {
            return value !== "" &&
                (
                    rule.field === "serviceName" &&
                    (value === serviceInfo.chName ||
                    value === serviceInfo.engName)

                ) ||
                (
                    rule.field === "serviceChName" &&
                    (value === serviceInfo.chName ||
                    value === serviceInfo.engName)

                ) ||
                (
                    rule.field === "chName" &&
                    (value === serviceInfo.serviceName ||
                    value === serviceInfo.serviceChName)

                ) ||
                (
                    rule.field === "engName" &&
                    (value === serviceInfo.serviceName ||
                    value === serviceInfo.serviceChName)

                );
        },
        checkSame(rule, value, callback) {
            const vm = this, {serviceInfo} = this;
            if (vm.ruleSame(rule, value, serviceInfo)) {
                callback(new Error("服务中文/英文名 和模型中文/英文名同名"))
            } else {
                callback();
            }
        },
    }
}