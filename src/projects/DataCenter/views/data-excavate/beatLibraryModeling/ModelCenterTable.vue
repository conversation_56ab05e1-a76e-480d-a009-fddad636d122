<template>
    <div>
        <el-table
                :data="tableData"
                borde
                size="mini"
                :height="tableHeight"
                tooltip-effect="light"
                ref="table"

        >
            <el-table-column
                    v-for="(item,index) in tHeadData"
                    :key="index"
                    :prop="item.prop"
                    :width="item.width"
                    :label="item.label"
                    :align="item.align"
                    :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    <label v-if="item.type==='tableCell'">{{scope.row[item.prop]}}</label>
                    <el-select filterable size="mini" v-model="scope.row[item.prop]" v-if="item.type==='select'">
                        <el-option v-for="opt in item.options "
                                   :label="opt.label"
                                   :value="opt.value"
                        ></el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
    export default {
        name: "ModelCenterTable",
        components: {},
        data() {
            return {
                dialogVisible: false,
                tableHeight: "0",
                tHeadData: [
                    {
                        prop: "name",
                        label: "字段名称",
                        width: "",
                        align: "left",
                        type: "tableCell"
                    },
                    // {
                    //     prop: "engName",
                    //     label: "字段英文名",
                    //     width: "",
                    //     align: "center",
                    //     type: "tableCell"
                    // },
                    {
                        prop: "type",
                        label: "字段类型",
                        width: "",
                        align: "center",
                        type: "tableCell"
                    },
                    // {
                    //     prop: "adaptiveControl",
                    //     label: "适配控件",
                    //     width: "",
                    //     align: "center",
                    //     type: "select",
                    //     options: [
                    //         {
                    //             label: "文本框",
                    //             value: "INPUT",
                    //         },
                    //         {
                    //             label: "下拉框",
                    //             value: "ComboBox",
                    //         },
                    //         {
                    //             label: "时间范围框",
                    //             value: "TimeRang",
                    //         },
                    //         {
                    //             label: "时间选择框",
                    //             value: "TimeSelect",
                    //         }
                    //     ],
                    // },
                    // {
                    //     prop: "encodeSetting",
                    //     label: "码表配置",
                    //     width: "",
                    //     align: "center",
                    //     type: "select",
                    //     fn: this.selectCode,
                    //     options: [],
                    // }
                ],
                tableData: [],
                codeSetting: {},
            };
        },
        methods: {
            selectCode(){},
            getTableData() {
                return this.tableData;
            },
            getAdaptiveControl(dispId) {
                //获取适配控件的label
                // let tempDisp = {};
                // this.tHeadData[2].options.forEach(val => {
                //     if (val.value === dispId) {
                //         tempDisp = {getCodeData
                //             label: val.label,
                //             value: dispId,
                //         }
                //     }
                // });
                // return tempDisp
            },
            setTableData(rows) {
                this.getCodeData();
                this.tableData = [];

                let adaptive = "";

                if (rows.sqlDataObjColumns !== null) {
                    rows.sqlDataObjColumns.forEach(item => {
                        // switch(item.dispId){
                        //     case'2e9e8dea5c1111e7907ba6006ad3db02':
                        //         adaptive = "INPUT";
                        //         break;
                        //     case'2e9e8dea5c1111e7907ba6006ad3db03':
                        //         adaptive = "ComboBox";
                        //         break;
                        //     case'2e9e8dea5c1111e7907ba6006ad3db04':
                        //         adaptive = "TimeSelect";
                        //         break;
                        //     case'2e9e8dea5c1111e7907ba6006ad3db05':
                        //         adaptive = "TimeRang";
                        //         break;
                        //     default:
                        //         adaptive = item.dispId;
                        // }
                        this.tableData.push({
                            name: item.colName,
                            engName: item.colCode,
                            type: rows.dataTypeIdCodeMap[item.dataTypeId],
                            adaptiveControl: adaptive,
                            encodeSetting: item.codeMapId,
                            dataTypeId: item.dataTypeId,
                        });
                    })
                }
            },
            refSearchModel(value) {
                this.$message(value);
                this.tableData = [
                    {
                        date: "2016-05-02",
                        name: value,
                        operation: "上海",
                        explain: "普陀区",
                        sort: "上海市普陀区金沙江路 1518 弄",
                        zip: 200333
                    }
                ];
            },
            refPageChange(index) {
                this.$message("当前页 " + index);
            },
            resizeTableHeight(height) {
                this.tableHeight = height;
            },
            showCodeTable() {
                this.dialogVisible = true;
            },
            getCodeData() {
                // const _this = this;
                // _this.$axios.get("/manage/sql/getBMCodeList").then(res => {
                //     _this.tHeadData[3].options = [];
                //     _this.tHeadData[3].options.push(
                //         {
                //             label:'请选择',
                //             value:'',
                //         }
                //     );
                //
                //     res.data.data.forEach(item => {
                //         _this.tHeadData[3].options.push(
                //             {
                //                 label: item.value,
                //                 value: item.extId,
                //             }
                //         );
                //     })
                // })
            }
        }
    };
</script>
<style scoped>
    i {
        padding: 5px;
    }
</style>