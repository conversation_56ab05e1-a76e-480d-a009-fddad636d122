<template>
    <div class="height100">
        <el-row  class="height100" :gutter="20" v-loading="settings.loading">
            <el-col :span="12" style="border-right:1px solid #555;overflow-y: auto;overflow-x: hidden;height:100%;">
                <div style="height:330px;">
                    <el-form ref="form" :model="form" label-width="100px" label-position="left" class="height90">
                        <el-form-item label="API名称：">
                            <span class="wordStyle">
                                {{form.interfaceChineseName}}
                            </span>
                        </el-form-item>
                        <el-form-item label="请求路径：">
                            <span class="wordStyle">
                                {{form.requestPath}}
                            </span>
                        </el-form-item>
                        <el-form-item label="请求参数">
                            <dg-button v-if="row.serviceType === '数据查询' || (row.serviceType === '信息核查' && row.singleOrManyTable != '2')" @click="setData(setQueryOrCheckInfo)">设置数据</dg-button>
                            <common-table
                                :data="requestTableData"
                                :columns="requestTHeadData"
                                :pagination="false"
                                class="width100"
                                :border="false"
                                row-key="paramName"
                                default-expand-all
                            >
                                <template slot="paramName" slot-scope="scope">
                                    <span style="color:red;" v-if="scope.row.isMust ==='t' || scope.row.is_must==='t'">*</span>
                                    {{['数据查询','信息核查'].includes(row.serviceType) ? scope.row.paramCode : scope.row.paramName}}
                                </template>
                                <template slot="defaultValue" slot-scope="scope">
                                    <dg-button v-if="scope.row.type === 'List' && scope.row.paramCode != 'params'" @click="setData(scope.row)">设置数据</dg-button>
                                    <div v-else>
                                        <el-input  v-input-limit:trim v-model="scope.row.defaultValue" @input="changeInput($event)" v-if="scope.row.showInput"></el-input>
                                    </div>
                                </template>
                            </common-table>
                        </el-form-item>
                    </el-form>
                </div>
            </el-col>

            <el-col :span="12" style="height:100%;" >
                <div class="height100">
                    <el-radio-group v-model.sync="showStyle">
                        <el-radio-button v-for="(item, key) in tabData" :label="item.value" :value="item.value">{{item.label}}</el-radio-button>
                    </el-radio-group>
                    <div class="text-form">
                        <div v-if="showStyle === 'text'" class="height100">
                            <p>响应</p>
                            <div  class="textClass" v-if="row.serviceType === '数据查询' || (row.serviceType === '信息核查' && row.singleOrManyTable != '2')">
                                {{texfInfo}}
                            </div>
                            <el-input
                                    v-else
                                    type="textarea"
                                    resize="none"
                                    v-model="form.response">
                            </el-input>
                        </div>
                        <!-- 数据查询表格展示 -->
                        <common-table
                                :data="data"
                                :columns="tHeadData"
                                :pagination="paginationDataSearch"
                                :paging-type="'client'"
                                :pagination-props="paginationPropsDataSearch"
                                height="calc(100% - 60px)"
                                class="width100"
                                :border="false"
                                v-else-if="row.serviceType === '数据查询' || (row.serviceType === '信息核查' && row.singleOrManyTable != '2')"
                        >
                        </common-table>
                        <common-table
                                :data="data"
                                :columns="tHeadData"
                                :pagination="pageFlag"
                                :paging-type="'server'"
                                :pagination-props="paginationProps"
                                :pagination-total="totalCount"
                                @change-current="changePage($event) "
                                @change-size="changeSize"
                                height="calc(100% - 60px)"
                                class="width100"
                                :border="false"
                                v-else
                        >
                        </common-table>
                    </div>
                </div>
            </el-col>
        </el-row>
        <setData ref="setData"></setData>
    </div>
</template>

<script>
import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import setData from './setData'
export default {
    name : "test",
        mixins: [commonMixins, servicesMixins],
    props :{
        row: Object,
    },
    components: {setData},
    data(){
        return {
            paginationDataSearch : true,
            paginationPropsDataSearch:{
                currentPage: 1,
                pageSizes: [10, 20, 30],
                pageSize: 10,
            },
            showStyle : "text",
            tabData : [{
                value : "text",
                label : "文本"
            },{
                value : "table",
                label : "表格"
            }],
            form:{},
            requestTableData:[],
            requestTHeadData: [{
                prop: "paramName",
                label: "前端参数名",
                minWidth: "",
            },{
                prop: "defaultValue",
                label: "值",
                minWidth: "",
            },],
            tHeadData : [],
            data : [],
            pageFlag: false,
            totalCount: 10,
            selectTable: {},
            texfInfo:'',
            setQueryOrCheckInfo:{}
        }
    },
    methods :{
        changeInput(e){
            this.$forceUpdate()
        },
        validationAI(info){
            let flag = false;
            if ((info.isMust === 't' || info.is_must === 't') && !info.defaultValue){
                flag = true;
            }
            else if(info.children && info.children.length){
                for(let i = 0; i< info.children.length; i++){
                    flag = this.validationAI(info.children[i]);
                    if(flag) break
                }
            }
            return flag;
        },
        validation(info){
            let flag = false;
            if ((info.isMust === 't' || info.is_must === 't') && !info.defaultValue){
                flag = true;
            }
            else if(info.children && info.children.length){
                for(let i = 0; i< info.children.length; i++){
                    flag = this.validation(info.children[i]);
                    if(flag) break
                }
            }
            return flag;
        },
        validationModel(info){
            let flag = false;
            if(info.type === 'List' && info.paramCode != 'params'){
                let par = info.childrenCopy.find(n => n.isMust == 't' || n.is_must == 't');
                if(info.setDataList && info.setDataList.length){
                    flag = false;
                }
                else{
                    flag = par ? true : false;
                }
            }
            else{
                if (info.paramCode != "operatorDataSet" && (info.isMust === 't' || info.is_must === 't') && !info.defaultValue){
                    flag = true;
                }
                else if(info.children && info.children.length){
                    for(let i = 0; i< info.children.length; i++){
                        flag = this.validationModel(info.children[i]);
                        if(flag) break
                    }
                }
            }
            return flag;
        },
        validationQueryOrCheck(){
            return !this.setQueryOrCheckInfo.setDataList.length;
        },
        testClick(){
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            let flag = false;
            let testType = (['模型分析', '回调服务', '数据碰撞', '比对订阅'].includes(vm.row.serviceType)) || (vm.row.serviceType === '信息核查' && vm.form.singleOrManyTable === '2');
            //判断请求数据是否必填项已填
            if(vm.row.serviceType === '数据查询' || (vm.row.serviceType === '信息核查' && vm.form.singleOrManyTable != '2')){
                vm.validationQueryOrCheck()
            }
            else{
                for(let i = 0; i< vm.requestTableData.length; i++){
                    flag = testType ? vm.validationModel(vm.requestTableData[i]) : vm.validationAI(vm.requestTableData[i]);
                    if(flag) break
                }
            }

            if (flag) {
                this.$message.warning("请填写请求参数的值信息");
                return;
            }
            settings.loading = true;
            let params = {
                serviceMetaId : this.row.serviceMetaId,
                paramList : this.requestTableData,
            }
            this.tHeadData = [];
            this.data = [];
            if (this.row.serviceType === "AI算法" || this.row.serviceType === "算法服务编辑") {
                let paramsData = {};
                this.requestTableData.forEach(item =>{
                    paramsData[item.code] = item.extended_type === 'Float' ? parseFloat(item.defaultValue) : item.defaultValue;
                })
                services.testAiService(this.row.serviceId, paramsData, settings).then(res => {
                    if (res.data.status === 0) {
                        this.setAIResultData(res.data.data)
                    }
                })
            } else if(['模型分析', '数据碰撞', '比对订阅'].includes(this.row.serviceType) || (vm.row.serviceType === "信息核查" && vm.row.singleOrManyTable == '2')){
                let ser = vm.$services("servicesServices");
                let dataSet = vm.requestTableData.find(n=>n.paramCode == "operatorDataSet");
                let operatorDataSet = {};
                if(dataSet && dataSet.children && dataSet.children.length){
                    dataSet.children.forEach(n=>{
                        operatorDataSet[n.paramCode] = n.setDataList ? [...n.setDataList] : [];
                    })
                }
                let par = {
                    serviceMetaId: vm.row.serviceMetaId,
                    testDataJson: {},
                }
                par.testDataJson = {
                    operatorDataSet:operatorDataSet,
                    params:vm.requestTableData.find(n=>n.paramCode == "params") ? vm.requestTableData.find(n=>n.paramCode == "params").children.map(n=>{return { paramCode: n.paramCode, paramValue: n.defaultValue}}) ||[] : []
                }
                par.testDataJson = JSON.stringify(par.testDataJson);
                ser.testModelService(par, settings).then(res => {
                    if (res.data.status === 0) {
                        let parInfo = JSON.parse(res.data.data);
                        vm.setModelResultData(parInfo);
                        if(parInfo.requestId)
                            vm.$emit('getAsyncInfo',parInfo.requestId);
                    }
                })
            } else if(this.row.serviceType === "回调服务"){
                vm.testModelServiceGetResult(1);
            }
            else {
                params = {
                    serviceMetaId : this.row.serviceMetaId,
                    paramVoList : this.setQueryOrCheckInfo.setDataList,
                }
                services.testQueryAndVercationService(params,settings).then(res => {
                    if (res.data.status === 0) {
                        vm.setResultData(res, true)
                    }
                })
            }
        },
        testModelServiceGetResult(index){
            const vm = this, { settings} = this;
            settings.loading = true;
            let ser = vm.$services("servicesServices");
            let par = {
                pageIndex: index,
                pageSize: vm.paginationProps.pageSize,
                requestId: vm.requestTableData[0].defaultValue,
                requestUrl: vm.form.resultUrl
            }
            vm.tHeadData = [];
            vm.totalCount = 0;
            ser.testModelServiceGetResult(par, settings).then(res => {
                if (res.data.status === 0) {
                    let parInfo = JSON.parse(res.data.data);
                    vm.setModelResultData(parInfo);
                    if(vm.pageFlag && parInfo.success){ //分页
                        vm.totalCount = parseInt(parInfo.pageInfo.totalCount);
                    }
                    else{
                        vm.totalCount = 0;
                    }
                }
            })
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.testModelServiceGetResult(1);
        },
        setModelResultData(res, asyncFlag){
            const vm = this;
            vm.data = [];
            vm.form.response = JSON.stringify(res);
            vm.data = res.operatorDataSet || [];
            for (const key in vm.data[0]) {
                vm.tHeadData.push({
                    prop : key,
                    label : key
                })
            }
        },
        setAIResultData(res){
            const vm = this;
            vm.data = [];
            this.form.response = JSON.stringify(res);
            for (const key in res.operatorDataSet) {
                let outString = key + ":" + res.operatorDataSet[key];

                this.tHeadData.push({
                    prop : key,
                    label : key
                })

            }
            this.data.push(res.operatorDataSet);
        },
        setResultData(res, isDy) {
            const vm = this;
            vm.tHeadData = [];
            if(vm.showStyle ==="text"){
                //this.$refs.texfInfo.innerHTML  = res.data.data;
                vm.texfInfo = res.data.data;
            }
            let rows = JSON.parse(res.data.data).operatorDataSet;
            if (rows && rows[0]) {
                for (let key in rows[0]) {
                    vm.tHeadData.push({
                        prop : key,
                        label : key
                    })
                    // vm.form.ginsengList && vm.form.ginsengList.forEach(e=>{
                    //     if (e.paramCode === key) {
                    //         vm.tHeadData.push({
                    //             prop : key,
                    //             label : isDy ? e.paramCode : e.paramName
                    //         })
                    //     }
                    // })
                }
            }
            this.data = rows;
        },
        //获取详情
        getModelServicesInfo(){
            let vm = this;
            let services = vm.$services("servicesServices");
            vm.requestTableData = [];
            services.queryServiceDetails(vm.row.serviceMetaId).then(res => {
                if (res.data.status === 0) {
                    res.data.data.paramList = res.data.data.paramList || [];
                    let arrayInfo = res.data.data.paramList.sort((a,b)=>{return b.paramCode.localeCompare(a.paramCode)});
                    arrayInfo.forEach(n=>{
                        vm.setParamName(n);
                        if(res.data.data.serviceType === '比对订阅'){
                            if( n.paramCode !='startTime' && n.paramCode !='endTime' && n.paramCode != 'requestId'){
                                vm.requestTableData.push(n)
                            }
                        }
                        else{
                            n.paramCode != 'requestId' ? vm.requestTableData.push(n) : '';
                        }
                    })
                    let operatorDataSet = vm.requestTableData.find(n=>n.paramCode === "operatorDataSet");
                    let hasRequestId = vm.requestTableData.find(n=>n.paramCode === "requestId");
                    if(hasRequestId) hasRequestId.showInput = true;
                    if(operatorDataSet && operatorDataSet.children){
                        operatorDataSet.children.forEach(n=>{
                            if(n.type === 'List'){
                                n.childrenCopy = n.children ? n.children : [];
                                n.children = [];
                                n.setDataList = [];
                                let obj ={};
                                n.childrenCopy.forEach(h=>{
                                    if(h.defaultValue){
                                        obj[h.paramCode] = h.defaultValue;
                                    }
                                })
                                n.setDataList.push(obj)
                            }
                        })
                    }

                    let params = vm.requestTableData.find(n=>n.paramCode === "params");
                    if(params && params.children){
                        params.children.forEach(n=>{
                            n.showInput = true;
                        })
                    }
                    // let params = vm.requestTableData.find(n=>n.paramCode === "params");
                    // if(params && params.children){
                    //     if(params.type === 'List'){
                    //         params.childrenCopy = params.children ? params.children : [];
                    //         params.children = [];
                    //     }
                    // }
                }
            })
        },

        //设置paramName
        setParamName(info){
            info.paramName = info.paramCode || info.paramName || info.name;
            info.paramcode = info.paramCode || info.code;
            info.defaultValue = info.type === 'Time' && info.example ?  new Date(info.example) : info.example;
            if(info.children && info.children.length){
                info.children.forEach(n=>{
                    this.setParamName(n);
                })
            }
        },
        aiGetInputParams(id){
            const vm = this;
            let services = this.$services('modeling');
            vm.requestTableData = [];
            services.getServiceDetailById(id).then(res =>{
                if (res.data.status === 0) {
                    res.data.data.inputParams.forEach(n=>{
                        vm.setParamName(n);
                    })

                    res.data.data.inputParams.forEach(element=>{
                        element.showInput = true;
                        element.children.forEach(i=>{
                            i.showInput = true;
                            vm.requestTableData.push(i);
                        })
                    })
                }
            })
        },

        //模型分析中设置数据
        setData(row){
            this.$refs.setData.show(row)
        },
    },
    mounted(){
        this.form = this.row;
        this.form.response = '';
        this.pageFlag = this.form.pageFlag || false;
        if (this.row.paramList) {
            this.row.paramList.forEach(element => {
                element.defaultValue = element.example;
                element.showInput = true;
            });
        }
        this.texfInfo = '';
        //获取详情
        if(['模型分析', '数据碰撞', '比对订阅'].includes(this.form.serviceType) || (this.form.serviceType === '信息核查' && this.form.singleOrManyTable === '2')){
            this.getModelServicesInfo();
        }else if (this.form.serviceType === 'AI算法') {
            this.aiGetInputParams(this.row.serviceId);
        }else this.requestTableData = this.row.paramList;

        if(['数据查询'].includes(this.row.serviceType) || (this.form.serviceType === '信息核查' && this.form.singleOrManyTable != '2')){
            this.setQueryOrCheckInfo = {childrenCopy: this.requestTableData,setDataList:[]};
            let obj ={};
            this.requestTableData.forEach(h=>{
                if(h.defaultValue){
                    obj[h.paramCode] = h.defaultValue;
                }
            })
            if(!(Object.keys(obj).length === 0 && obj.constructor === Object))
                this.setQueryOrCheckInfo.setDataList.push(obj);

        }
    }
}
</script>

<style scoped lang="less">
    .height90{
        height: 90%;
    }
    .textClass{
        height: 300px;
        overflow: auto;
        border-color: rgba(0,0,0,.25);
        padding: 5px 12px;
        border: 1px solid rgba(0, 0, 0, 0.25);
    }
    .text-form{
        height:calc(100% - 40px);
        /deep/.el-textarea{
            height:calc(100% - 26px);
            .el-textarea__inner{
                height:100%;
            }
        }
    }
</style>