import CommonTable from '@/components/common/CommonTable'
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {datasetMixins} from "../../../components/datasetMixins/dataset"
import {globalBus} from "@/api/globalBus";

export default {
    name: "dataList",
    props: {
        nodeList: Array
    },
    mixins: [datasetMixins, servicesMixins, commonMixins],
    components: {
        CommonTable
    },
    data() {
        return {
            defaultProps: {
                value: "id",
                label: "label",
                children: "children"
            },
            treeBind: {
                "default-expand-all": true
            },
            activeValue: "",
            searchType: {
                time: {
                    label: '时间过滤组件',
                    options: [
                        {
                            label: '年份',
                            value: 'year'
                        }, {
                            label: '年月',
                            value: 'month'
                        },
                        {
                            label: '日期',
                            value: 'date'
                        },
                        {
                            label: '时间',
                            value: 'datetime'
                        },
                        {
                            label: '年月区间',
                            value: 'monthrange'
                        },
                        {
                            label: '日期区间',
                            value: 'daterange'
                        },
                        {
                            label: '时间区间',
                            value: 'datetimerange'
                        },
                    ]
                },
                text: {
                    label: '文本过滤组件',
                    options: [
                        {
                            label: '精准过滤',
                            value: 'precise'
                        }, {
                            label: '前缀模糊过滤',
                            value: 'prefix_fuzzy'
                        }, {
                            label: '后缀模糊过滤',
                            value: 'suffix_fuzzy'
                        }, {
                            label: '模糊过滤',
                            value: 'fuzzy'
                        },
                        {
                            label: '不等于',
                            value: 'not_equal'
                        },
                    ]
                },
                enum: {
                    label: '枚举过滤组件',
                    options: [
                        {
                            label: '下拉框',
                            value: 'select'
                        },
                    ]
                },
                number: {
                    label: '数值过滤组件',
                    options: [
                        {
                            label: '数值',
                            value: 'digital'
                        }, {
                            label: '数值区间',
                            value: 'interval_digital'
                        }
                    ]
                }
            },
            filterHead: [
                {
                    label: '显示名称',
                    width: '90',
                    prop: "label",
                    align: 'center',
                    placeholder: '显示名称',
                    resizable: false
                },
                {
                    label: '显示控件',
                    prop: 'type',
                    width: '90',
                    align: 'center',
                    placeholder: '选择控件',
                    resizable: false
                },
                {
                    label: '条件设置',
                    prop: 'condition',
                    d_placeholder: "选择数据集",
                    f_placeholder: "选择字段",
                    width: '360',
                    align: 'center',
                    resizable: false
                },
                {
                    label: '',
                    prop: 'operate',
                    width: '50px',
                    align: 'center',
                    resizable: false
                }
            ],

            relationField: [],//关联字段
            loading: true,
            title1: '数据集',
            title2: '查询条件',
            data_tip: '添加图表',
            dataSetVal: '',
            dataSet: [],
            currentDataSet: {},
            bkDataset: [],
            data_tip1: '联动数据图表',
            charts: [],
            chartNodes: [],
            filterId: 0,
            filterList: [],
            editNode: null,
            setData: false,
            unused: '',
            enumOptions: [],
            enumValue: {},
            selects: {},
            enumSelected: {},
            buttons: [
                {
                    name: '确定',
                    clickFn: this.confirmFn,
                    type: 'primary'
                }, {
                    name: '取消',
                    clickFn: this.cancelFn,
                    type: '',
                }
            ],
            relation_charts: [],
            datasetField: {}, //关联图表数据集 可选字段
        }
    },
    watch: {
        resultData: {
            handler(val) {
                if (this.editNode && this.setData) {
                    this.$emit('nodeDataRenew', this.editNode.id);
                }
            },
            deep: true
        }
    },
    computed: {
        resultData() {
            return {
                unused: this.unused,
                // datasetId: this.dataSetVal,
                filterList: this.checkResult(this.filterList),
                // dataset: this.currentDataSet,
                relation_charts: this.relation_charts
            }
        },
        allCharts() {//所有图表
            return this.getAllCharts(this.nodeList);
        },
        relationDataset() {//关联数据集
            return this.getAllDataset(this.relation_charts);
        },
        withoutESDataset() { //除es数据集
            return this.getAllDataset(this.relation_charts, 'elasticsearch');
        }
    },
    methods: {
        /**
         * 校验 是否选择了字段
         * @param data
         */
        checkResult(data){
            const vm = this;
            return data.map(item => {
                item.prop = vm.checkField(item.condition , item);
                if(item.selectType === 'select_cascade') item.multipleNum = vm.getFieldNum(item.condition);
                return item;
            })
        },
        /**
         * 多选 最多字段数
         * @param condition
         * @returns {number}
         */
        getFieldNum(condition){
            let max = 0;
            condition.forEach(con => {
                if(con.fieldVal.length > max) max = con.fieldVal.length;
            });
            return max;
        },
        /**
         *
         * @param condition
         * @param data
         * @returns {boolean}
         */
        checkField(condition , data){
            return data.multiple ? condition.filter(con => con.field !== "" && con.field.length).length > 0 : data.prop;
        },
        /**
         * 多选字段
         * @param params
         * @param row
         * @param con
         * @param inx
         */
        mulFieldChange(params, row, con, inx) {
            con.field = [];
            params[0].forEach(item => {
                params[1].forEach(par => {
                    if(item === par.id){
                        con.field.push({
                            field: par,
                            jsType: par.format,
                        })
                    }
                })
            });
            if (inx) return;
            if (params[0].length) {
                row.prop = true;//验证可否渲染
                row.label = params[1][params[0].length - 1].label;
            } else {
                row.label = "";
            }
        },
        /**
         * 字段改变
         */
        fieldChange(params, row, con, inx) {
            con.field = params[1];
            con.jsType = params[1].format;
            row.prop = true;//验证可否渲染
            if (inx) return;
            row.label = params[1].label;
        },
        /**
         * 选择插件类型 过滤可选择字段
         */
        filterFiledByType(typeKey, options) {
            return options.filter(opt => {
                if (typeKey === 'enum') {
                    return true;
                } else if (typeKey === 'text') {
                    return opt.format === 'TEXT';
                } else if (typeKey === 'number') {
                    return opt.format === 'NUMBER';
                } else if (typeKey === 'time') {
                    return opt.format !== 'NUMBER' && opt.format !== 'TEXT';
                } else {
                    return true;
                }
            })
        },
        /**
         * 数据集 改变 对应的字段
         */
        filterDatasetChange(params, row) {
            const vm = this;
            row.fieldVal = "";
            row.datasetName =  params[1].label;
            row.fieldOpts = [...vm.datasetField[params[0]]];
        },
        /**
         * 获取数据集字段
         * @param data
         */
        async getDatasetFields(data) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            await services.getDatasetFields(data.datasetId).then(res => {
                if (res.data.status === 0) {
                    vm.datasetField[data.datasetId] = res.data.data.map(li => {
                        li.label = li.fieldAlias || li.name || li.code;
                        li.value = li.id;
                        li.dbtype = data.dataset.dbType;
                        return li;
                    });
                }
            })
        },
        /**
         * 获取选择图表关联数据集的字段
         * @param val
         * @param datas
         */
        async getFields(val, datas) {
            const vm = this;
            for(let i = 0;i< val.length; i++){
                let item = val[i];
                if (!vm.datasetField[item]) {
                    await vm.getDatasetFields(datas[i]);
                }
            }
        },
        /**
         * 获取数据集id 集合
         * @param datas
         */
        getDatasetIds(datas) {
            let allDataset = [];
            datas.forEach(da => {
                allDataset.push(da.datasetId);
            });
            return allDataset;
        },
        /**
         * 删除 取消选择的图表 关联数据集字段
         * @param allDataset
         */
        deleteFields(allDataset) {
            const vm = this;
            for (let k in vm.datasetField) {
                if (allDataset.indexOf(k) === -1) {
                    delete vm.datasetField[k];
                }
            }
        },
        /**
         * 获取已选图表数据
         */
        getCheckedCharts(val) {
            const vm = this;
            let charts = [];
            val.forEach(item => {
                vm.allCharts.forEach(cha => {
                    if (item === cha.linkageId) {
                        charts.push(cha);
                    }
                })
            });
            return charts;
        },
        /**
         * 关联图表
         * @param val
         * @param data
         */
        async chartsChange(val, data) {
            const vm = this;
            let chartData = data || await vm.getCheckedCharts(val);
            await vm.getFields(val, chartData);
            let allDataset = await vm.getDatasetIds(chartData);
            await vm.deleteFields(allDataset);
            await vm.resetDeleteDataset(allDataset);
        },
        /**
         * 重置 删除数据集的条件设置
         * @param allDataset
         */
        resetDeleteDataset(allDataset) {
            const vm = this;
            vm.filterList.forEach(list => {
                let prop = false;
                list.condition.forEach(co => {
                    if (co.dataset && allDataset.indexOf(co.dataset) === -1) {
                        co.fieldVal = "";
                        co.dataset = "";
                    }
                    if (co.fieldVal !== "") {
                        prop = true;
                    }
                });
                list.prop = prop;
            })
        },
        /**
         * 获取数据集
         * @param charts
         * @param condition
         * @returns {[]}
         */
        getAllDataset(charts, condition) {
            const vm = this;
            let nodes = JSON.parse(JSON.stringify(this.nodeList)), dataset = [];
            nodes.forEach(item => {
                let widget = JSON.parse(item.widget), beforeData = JSON.parse(widget.beforeData);
                let excludeCharts = ["SelectWidget", "TextWidget", "TabWidget"];
                if (excludeCharts.indexOf(item.code) === -1 && beforeData.datasetId && vm.relation_charts.indexOf(item.linkageId) > -1) {
                    let con = condition ? beforeData.dataset.dbType !== condition : true;
                    if (con) {
                        beforeData.dataset.value = beforeData.dataset.id;
                        dataset = dataset.filter(data => data.id !== beforeData.dataset.id);
                        dataset.push(beforeData.dataset);
                    }
                }
            });
            return dataset;
        },
        /**
         * 添加数据集、字段
         * @param index
         */
        addCondition(index) {
            this.filterList[index].prop = false;
            this.filterList[index].condition.push({
                dataset: "",
                field: {},
                fieldVal: "",
                jsType: "",
                fieldOpts: []
            });
            this.$refs.filterTable.$refs.table.doLayout();
        },
        /**
         *删除数据集、字段
         * @param inx
         * @param index
         */
        removeCondition(inx, index) {
            this.filterList[index].condition.splice(inx, 1);
        },
        /**
         * 获取所有图表
         * @param nodes
         */
        getAllCharts(nodes) {
            let charts = [], chartNods = JSON.parse(JSON.stringify(nodes));
            chartNods.forEach(chart => {
                let widget = JSON.parse(chart.widget), beforeData = JSON.parse(widget.beforeData);
                let excludeCharts = ["SelectWidget", "TextWidget", "TabWidget"];
                if (excludeCharts.indexOf(chart.code) === -1 && beforeData.datasetId) {
                    chart.value = chart.linkageId;
                    chart.label = `${widget.title}(${beforeData.dataset.label})`;
                    chart.datasetId = beforeData.datasetId;
                    chart.dataset = beforeData.dataset;
                    charts.push(chart);
                }
            });
            return charts;
        },
        confirmFn(scope) {
            scope.row.visible = false;
            scope.row.type = this.activeValue;
        },
        cancelFn(scope) {
            scope.row.visible = false;
        },
        filterNode: treeMethods.methods.filterNode,
        initList() {
            this.filterList.forEach(list => {
                list.visible = false;
            });
        },
        /**
         *
         * @param item
         * @param scope
         * @param typeKey
         */
        resetDatasetField(item, scope, typeKey) {
            if (scope.row.typeKey !== typeKey) {
                scope.row.condition = [];
                this.addCondition(scope.$index);
            } else if (scope.row.type === "precise" && item.value !== "precise") {
                scope.row.condition.forEach(con => {
                    if (con.field.dbType === "elasticsearch") {
                        con.dataset = "";
                        con.fieldVal = "";
                        con.field = {};
                        con.jsType = "";
                        con.fieldOpts = [];
                    }
                })
            }
        },
        /**
         * 选择过滤组件
         * @param item
         * @param scope
         * @param typeKey
         */
        async checkedItem(item, scope, typeKey) {
            if (scope.row.type === item.value && item.value !== 'select') {
                scope.row.visible = false;
                return;
            }
            this.activeValue = item.value;
            await this.resetDatasetField(item, scope, typeKey);
            scope.row.typeKey = typeKey;
            if (item.value !== 'select') {
                scope.row.type = item.value;
                scope.row.visible = false;
                this.setSpecialValue(item, scope);
            } else {
                this.setSpecialValue(item, scope);
                this.triggerEvent();//触发窗口resize
            }
        },
        setSpecialValue(item, scope) {
            delete scope.row.filterVal;
            delete scope.row.filter;
            delete scope.row.dim;
            delete scope.row.enumValue;
            delete scope.row.selectType;
            delete scope.row.options;
            delete scope.row.filterOpt;
            scope.row.multipleLimit = 0;
            scope.row.multiple = false;
            if (['monthrange', 'daterange','datetimerange', 'year', 'month', 'date', 'datetime'].indexOf(item.value) > -1) {
                scope.row.interval_date = {
                    range: 'before',
                    year: 0,
                    month: 0,
                    day: 0,
                    value: '',
                };
                scope.row.interval_tab = 'picker';
            } else if (item.value === 'digital') {
                scope.row.filterVal = {
                    leftV: {
                        value: '',
                        logical: '='
                    },
                    rightV: {
                        value: '',
                        logical: '>'
                    }
                };
            } else if (item.value === 'interval_digital') {
                scope.row.filterVal = {
                    leftV: {
                        value: '',
                        logical: '<'
                    },
                    rightV: {
                        value: '',
                        logical: '>'
                    }
                };
            } else if (item.value === 'select') {
                scope.row.options = [];
                scope.row.filterVal = "";
                scope.row.enumValue = this.enumOptions[0].value;
                this.getEnumValue([this.enumOptions[0].value, this.enumOptions[0]], scope);
            }
        },
        disabledSelect(inx) {
            this.$refs['select' + inx][0].blur();
            this.filterList.forEach((list, i) => {
                if (i !== inx) list.visible = false;
            });
            this.activeValue = this.filterList[inx].type;
        },
        clear() {
            this.filterList = [];
        },
        getNodeList() {
            this.$emit('getVisualNodes');
        },
        getVisualId(node) {
            this.editNode = node;
            this.initValue(node);
        },
        initValue(node) {//回填数据
            const vm = this;
            vm.$nextTick(async () => {
                vm.setData = false;
                if (node.widget) {
                    let {beforeData} = JSON.parse(node.widget), nodeData = JSON.parse(beforeData);
                    // nodeData.dataset ? vm.currentDataSet = nodeData.dataset : true;
                    // nodeData.dataset && nodeData.dataset.fields ? vm.setChartNode(nodeData.dataset.fields, nodeData.dbType) : true;
                    // nodeData.datasetId ? await vm.getCharts(nodeData.datasetId) : true;
                    // nodeData.datasetId ? vm.store_datasetId = nodeData.datasetId : true;
                    nodeData.filterList ? vm.filterList = JSON.parse(JSON.stringify(nodeData.filterList)) : true;
                    let allChartsId = [];
                    vm.allCharts.forEach(c => {
                        allChartsId.push(c.linkageId);
                    });
                    nodeData.relation_charts ? vm.relation_charts = nodeData.relation_charts.filter(chart => allChartsId.indexOf(chart) > -1) : true;
                    await vm.chartsChange(vm.relation_charts);
                    vm.reNewAllFieldOption();
                }
                this.unused = '0';
                this.initList();
                this.setData = true;
            })
        },
        /**
         * 更新字段选项
         */
        reNewAllFieldOption(){
            const vm = this;
            vm.filterList.forEach(list => {
                list.condition.forEach(con => {
                    if(con.dataset){
                        con.fieldOpts = [...vm.datasetField[con.dataset]];
                    }
                })
            })
        },
        setChartNode(fields, dbType) {
            this.chartNodes = [];
            for (let j = 0; j < fields.length; j++) {
                let f = fields[j];
                let l = {
                    label: f.name,
                    value: f.id,
                    dbType,
                    ...f
                };
                if (JSON.stringify(this.chartNodes).indexOf(JSON.stringify(l)) === -1) {
                    this.chartNodes.push(l); // 进行动态的操作
                }
            }
        },
        inputData() {
            this.setData = true;
        },
        getTabLabel(item, widget, tabId) {
            let {nodeList} = this, label;
            if (item.show) {
                label = widget.title;
            } else {
                let tabNode = nodeList.filter(to => to.id === tabId)[0];
                let tab_widget = JSON.parse(tabNode.widget);
                label = `${tab_widget.title} (${widget.title})`;
            }
            return label;
        },
        getCharts(val) {
            const vm = this;
            if (!val) return;
            vm.charts = [];
            vm.relation_charts = "";
            for (let i = 0; i < vm.nodeList.length; i++) {
                let node = vm.nodeList[i];
                let widget = JSON.parse(node.widget), beforeData = JSON.parse(widget.beforeData),
                    tabId = beforeData.tabId;
                if (node.code === 'SelectWidget') {
                    continue;
                }
                if (!widget.widgetDataset) {
                    continue;
                }
                if (!widget.widgetDataset.datasetId) {
                    continue;
                }
                if (val !== widget.widgetDataset.datasetId) {
                    continue;
                }
                let label = vm.getTabLabel(node, widget, tabId);
                let l = {label, value: node.id};
                vm.charts.push(l);
            }
        },
        getChartNodes(val) {
            if (!val) {
                return;
            }
            for (let i = 0; i < this.bkDataset.length; i++) {
                let d = this.bkDataset[i];
                if (val !== d.id) {
                    continue;
                }
                this.setChartNode(d.fields, d.dbType);
            }

        },
        dataSetChange(val, dbType, resullt, isInit) {
            this.setChartNode(resullt, dbType);
            if (val === this.store_datasetId) return;
            this.inputData();
            this.getNodeList();
            this.getCharts(val);
            this.store_datasetId = val;
            this.currentDataSet = this.getDataSet();
            if (!isInit) {
                this.clear();
            }
        },
        setFieldData(result, dbType, val, isInit) {
            this.dataSetChange(val, dbType, result, isInit);
        },
        chartNodeChange(val, scope) {
            this.chartNodes.forEach(node => {
                if (node.value === val) {
                    scope.row.label = node.label;
                    scope.row.field = node;
                    scope.row.jsType = node.format;
                }
            });
            scope.row.type = "";
            delete scope.row.filterVal;
            delete scope.row.filter;
            delete scope.row.dim;
            scope.row.dbType = this.currentDataSet.dbType;
        },
        searchTypeChange(jsType, index) { //控制过滤类型 暂不用
            this.inputData();
            this.searchType.forEach(type => {
                if (jsType) {
                    type.disabled = !(type.value === jsType);
                } else {
                    type.disabled = false;
                }
                switch (jsType) {
                    case 'number':
                        this.filterList[index].value = [0, 0];
                        break;
                    case 'time' :
                        this.filterList[index].value = [];
                        break;
                    default :
                        this.filterList[index].value = '';
                        break;
                }
                if (jsType === 'number' || jsType === 'string') { //逻辑查询 需要添加 logicalVal
                    this.filterList[index].logicalVal = '=';
                } else {
                    delete this.filterList[index].logicalVal;
                }
            })
        },
        addFilter() {
            this.inputData();
            let timeId = new Date().getTime().toString();
            this.filterList.push(
                {
                    condition: [{
                        dataset: "",
                        field: {},
                        fieldVal: "",
                        jsType: "",
                        fieldOpts: []
                    }],
                    label: '',
                    type: '',
                    options: [],
                    visible: false,
                    timeId
                }
            );
        },
        deleteFilter(index) {
            this.inputData();
            this.filterList.splice(index, 1);
        },
        save() {
            globalBus.$emit('setSearchPanel', this.editNode.id);
            globalBus.$emit('nodeDataRenew', this.resultData, this.editNode.id);
        },

        getDataSet() {
            for (let i = 0; i < this.bkDataset.length; i++) {
                if (this.dataSetVal === this.bkDataset[i].id) {
                    return this.bkDataset[i];
                }
            }
        },
        queryEnumSelect() {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            services.allMbEnumTreeList().then(res => {
                if (res.data.status === 0) {
                    res.data.data.forEach(item => {
                        vm.enumOptions.push({
                            label: item.name,
                            value: item.id,
                            code: item.code
                        })
                    })
                }
            })
        },
        setTreeLabel(data) {
            const vm = this;
            let arr = [];
            data.forEach(item => {
                item.label = item.name + ' ( ' + item.id + ' )';
                if (item.children) {
                    item.children = vm.setTreeLabel(item.children);
                }
                arr.push(item);
            });
            return arr;
        },
        /**
         *
         * @param num
         * @param places 提示词
         * @returns {[]}
         */
        addFilterCascade(num , places) {
            let values = [];
            for (let i = 0; i < num; i++) {
                let filterO = {
                    filterVal: "",
                    options: []
                };
                filterO.placeholder = places[i];
                values.push(filterO);
            }
            return JSON.parse(JSON.stringify(values));
        },
        getEnumValue(params, scope) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            vm.$set(scope.row, scope.row.enumValue, params[0]);
            scope.row.dim = null;
            scope.row.filter = null;
            if (params[1].code === 'FJ') {
                scope.row.selectType = "select_cascade";
                scope.row.condition.forEach(con => {
                    con.field = [];
                    con.fieldVal = [];
                });
                scope.row.filterOpt = vm.addFilterCascade(2 , ['分局','派出所']);
                scope.row.multiple = true;
                scope.row.multipleLimit = 2;
                services.linkagePoliceStation(1).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        scope.row.options = vm.getFilterTxt(result, 1);
                        scope.row.filterOpt[0].options = scope.row.options;
                    }
                })
            } else {
                services.getStanderMbCodeTreeList(params[0])
                    .then(res => {
                        if (res.data.status === 0) {
                            let result = res.data.data;
                            scope.row.condition.forEach(con => {
                                con.field = "";
                                con.fieldVal = "";
                            });
                            scope.row.filterVal = "";
                            /*if(params[1].code === '行政区域'){
                                scope.row.selectType = "select_tree";
                                scope.row.options = vm.setTreeLabel(result);
                            }else */
                            if (params[1].code === 'ZQSJ') {
                                scope.row.selectType = "select_cycle";
                                scope.row.options = result;
                            } else if (params[1].code === 'LJSJ') {
                                scope.row.selectType = "select_add";
                                scope.row.options = result.map(opt => {
                                    opt.filter = {};
                                    return opt;
                                })
                                //vm.getFilterTime(result);
                            } else {
                                scope.row.selectType = "select_add";
                                scope.row.options = vm.getFilterTxt(result);
                            }
                        }
                    })
            }

        },
        getFilterTxt(data, level) {
            let opts = [];
            data.forEach(li => {
                li.filter = {
                    addVal: li.value,
                    value: li.value,
                    type: "suffix_fuzzy",
                    parentType: 'text',
                };
                li.level = level || "";
                opts.push(li);
            });
            return opts;
        },
        getFilterTime(data) {
            let opts = [];
            data.forEach(li => {
                let time = new Date(new Date(new Date().toLocaleDateString()).getTime()),
                    end = time.Format('yyyyMMdd'),
                    start = time;
                switch (li.value.toLowerCase()) {
                    case "jyn_w" :
                        start = new Date(start.setTime(start.getTime() - 3600 * 1000 * 24 * 366)).Format('yyyyMMdd');
                        break;
                    case "jyjd_w":
                        start = new Date(start.setTime(start.getTime() - 3600 * 1000 * 24 * 91)).Format('yyyyMMdd');
                        break;
                    case "jyy_w":
                        start = new Date(start.setTime(start.getTime() - 3600 * 1000 * 24 * 31)).Format('yyyyMMdd');
                        break;
                    case "jyz_w":
                        start = new Date(start.setTime(start.getTime() - 3600 * 1000 * 24 * 8)).Format('yyyyMMdd');
                        break;
                    case "jyt_w":
                        start = new Date(start.setTime(start.getTime() - 3600 * 1000 * 24 * 2)).Format('yyyyMMdd');
                        break;
                    default:
                        break;
                }
                li.filter = {
                    addVal: true,
                    leftV: {
                        value: end,
                        logical: '<'
                    },
                    rightV: {
                        value: start,
                        logical: '>'
                    },
                    parentType: 'number',
                    type: "text"
                };
                opts.push(li);
            });
            return opts;
        },
        rewNewFormList(list, id) {
            const vm = this;
            if (vm.editNode.id !== id) return;
            vm.filterList.forEach((it, i) => {
                list.forEach(item => {
                    let condition = it.timeId === item.timeId && it.prop === item.prop && it.type === item.type;
                    if (condition) {
                        if(it.selectType === "select_cascade"){
                            if(list[i].filterOpt) it.filterOpt = JSON.parse(JSON.stringify(list[i].filterOpt));
                        }else {
                            it.filterVal = list[i].filterVal;
                            it.filter = list[i].filter;
                        }

                    }
                })
            })
        }
    },
    created() {
        // this.initDataSet();
        this.getNodeList();
        this.queryEnumSelect();
        globalBus.$on("rewNewFormList", this.rewNewFormList);
    },
    destroyed() {
        globalBus.$off("rewNewFormList", this.rewNewFormList);
    }

}
