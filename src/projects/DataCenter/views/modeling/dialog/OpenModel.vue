<template>
    <div class="openModel">
        <dg-dialog :close-on-click-modal="false"
                   :width="width"
                   :title="title"
                   :visible.sync="dialogVisible"
                   @closed="clearD"
                   :append-to-body="true"
                   class="model-dialog"
        >
            <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="documentCode"></help-document></div>
            <ModelTree @nodeClick="checkNode" :row="row" :isNew="isNew" has-save :modelType="modelType" v-if="reNew"/>
            <div slot="footer">
                <el-button @click="close" size="mini">{{btnCancelTxt}}</el-button>
                <el-button @click="checked" type="primary" size="mini">{{btnCheckTxt}}</el-button>
            </div>
        </dg-dialog>
    </div>
</template>

<script>
import BeatModelTree from '../beatLibraryModeling/BeatLibraryModelingTree'
import ModelTree from './ModelTree'
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";

export default {
    name: "OpenModel",
    mixins: [servicesMixins],
    data() {
        return {
            width: '800px',
            dialogVisible: false,
            reNew: false,
            classifyId: "",
            title: "",
            treeNode: "",
            row: {},
            treeType: "",
            operType: "",
            isSQLModel: false,
            modelType : "",//模型类型 ， 快速分析 ， 建模
            documentCode: "",
        }
    },
    components: {
        ModelTree,
        BeatModelTree,
    },
    methods: {
        getCenterTable(data) {
            if (data.parentId !== "-1") {
                this.treeNode = {
                    transId: data.id,
                };
            }
        },
        checkNode(label, value, node) {
            this.treeNode = {};
            if (node.data.elementOrDir === "0" || (this.operType === 'move')) { //node.level === 2 &&
                this.treeNode = {
                    dirParentId: "",
                    modelName: label,
                    modelSpec: "",
                    modelType: "",
                    releaseDate: "",
                    status: "",
                    transId: value,
                    isNew: false
                };
            }

            // this.classifyId = value;
        },
        /**
         * 打开
         * @param row
         * @param type
         * @param operType
         * @param mType
         */
        open(row, type, operType, mType) {
            this.treeType = type;
            this.operType = operType;
            this.modelType = mType;
            this.documentCode = this.modelType === 'QUICK_SPARK' ? 'fastAnalysisQueryTransTree' : '';
            this.row = row;
            this.dialogVisible = true;
            switch (type) {
                case 'dataCenter':
                    this.reNew = true;
                    if (operType === 'open') {
                        this.isNew = true;
                        this.title = "打开模型";
                    } else {
                        this.isNew = false;
                        this.title = "移动到";
                    }
                    break;
                case 'sqlModel':
                    this.isSQLModel = true;
                    this.reNew = false;
                    break;
            }
        },
        close() {
            this.dialogVisible = false;
        },
        /**
         * 确定
         */
        checked() {
            const vm = this, {services} = vm;
            if (vm.treeNode.transId === undefined) {
                vm.$message.warning("请选择模型！");
            } else {
                if (vm.operType === 'open') {
                    vm.dialogVisible = false;
                    vm.$emit("openPage", vm.treeNode);
                } else {
                    let id = vm.row.transId;
                    if (id === undefined) {
                        id = vm.row.id;
                    }
                    if(this.modelType === 'QUICK_SPARK'){
                        const {rapidAnalysisServices , rapidAnalysisMock,} = this;
                        let servicesRapid = vm.getServices(rapidAnalysisServices , rapidAnalysisMock);
                        servicesRapid.moveModelRapid(id, vm.treeNode.transId).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("移动成功");
                                vm.$emit("update");
                                vm.dialogVisible = false;
                            } else {
                                // vm.$message.warning(res.data.msg);
                            }
                        })
                    }
                    else{
                        services.moveModel(id, vm.treeNode.transId).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("移动成功");
                                vm.$emit("update");
                                vm.dialogVisible = false;
                            } else {
                                // vm.$message.warning(res.data.msg);
                            }
                        })
                    }

                }
            }
        },
        clearD() {
            this.reNew = false;
        }
    }
}
</script>

<style scoped lang="less">
.model-dialog {
    .dataTree {
        width: 100%;
        height: calc(75vh - 192px);
        margin: 0 auto;
        float: inherit;
        box-sizing: border-box;

        /deep/ .node-label {
            max-width: 100%;
        }
    }
}
</style>
