<template>
    <dg-dialog :close-on-click-modal="false"
               :width="width"
               :title="title"
               :visible.sync="dialogVisible"
               @closed="clearD"
               :append-to-body="true"
               class="model-dialog"
               top="10vh"
               v-loading="settings.loading"
    >
        <div slot="title"><span class="el-dialog__title">{{ title }}</span>
            <help-document :code="documentCode"></help-document>
        </div>

        <el-form label-suffix=":" label-width="110px" label-position="right">
            <el-form-item :label="formList.dsTypeId.label" required v-if="isDataset">
                <dg-tree-drop
                        v-model="dsTypeId"
                        placeholder="请选择"
                        :props="defaultProps"
                        :data="dataTypeList"
                        :tree-props="{
                        'default-expanded-keys': dsTypeId ? [...expandKeys,dsTypeId] : expandKeys,
                        'current-node-key':dsTypeId,
                        'nodeKey': 'id',
                    }"
                        check-leaf
                        filterable
                        check-strictly
                        visible-type="leaf"
                        ref="posTree"
                ></dg-tree-drop>
            </el-form-item>
            <el-form-item :label="formList.pos.label" required>
                <div class="dataTree">
                    <el-input placeholder="请输入名称" v-model="filterText" size="medium" class="ce-search">
                        <i slot="suffix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <div class="ce-tree selectn">
                        <dg-tree
                                class="filter-tree"
                                :data="data"
                                :props="defaultProps"
                                node-key="id"
                                :expand-on-click-node="false"
                                :filter-node-method="filterNode"
                                :highlight-current="true"
                                @node-click="nodeClick"
                                :default-expand-all="true"
                                ref="tree">

                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-label"
                          :title="data.name"
                          :class="{
                            'el-icon-folder' : !node.expanded ,
                            'el-icon-folder-opened' : node.expanded ,
                          }"
                    >{{ data.name }}</span>
                </span>

                        </dg-tree>
                    </div>
                </div>
            </el-form-item>
        </el-form>

        <div slot="footer">
            <el-button @click="clearD" size="mini">取消</el-button>
            <el-button @click="checked" type="primary" size="mini">确定</el-button>
        </div>
    </dg-dialog>
</template>

<script>
import {treeMethods} from "@/api/treeNameCheck/treeName";
import {mapState} from "vuex";

export default {
    name: "moveTree",
    mixins: [treeMethods],
    computed: {
        ...mapState({
            dataTypeList: (state) => state.plans.dataTypeList.filter(l => l.id !== state.plans.shareId && l.id !== state.plans.otherUserCatalogId),
        }),
        expandKeys() {
            return this.dataTypeList.length > 0 ? [this.dataTypeList[0].id] : []
        },
        isDataset() {
            return this.moduleType === "dataSet";
        }
    },
    data() {
        return {
            width: '800px',
            dialogVisible: false,
            title: '移动到',
            info: '',
            filterText: '',
            data: [],
            defaultProps: {
                children: 'children',
                label: 'label',
                value: "id"
            },
            moveDirId: '',
            dsTypeId: '',
            settings: {
                loading: false
            },
            moveNode: {},
            documentCode: '',
            formList: {
                dsTypeId: {
                    label: '数据来源类型',
                },
                pos: {
                    label: "保存到目录"
                },
            },
            moduleType: "",// 页面模块 ，数据源 、或者  数据集
            beforePos: null, // 暂存修改前的数据
        }
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        show(row, node, type, pos = new Map()) {
            this.data = node;
            this.info = row;
            this.dialogVisible = true;
            this.moduleType = type;
            this.beforePos = pos;
            this.moveDirId = pos.has("fromCatalog") ? pos.get("fromCatalog") : "";
            this.dsTypeId = pos.has("fromDsType") ? pos.get("fromDsType") : "";
            this.documentCode = type === 'dataSet' ? 'dataSetOperationMoveLogicDataSet' : 'dataConnectionMove';
            this.$nextTick(()=>{
                this.setCurrentKey();
            })

        },
        setCurrentKey() {
            this.moveDirId && this.$refs.tree?.setCurrentKey(this.moveDirId);
            this.dsTypeId && this.$refs.posTree?.setCurrentKey(this.dsTypeId);
        },
        initTree() {
        },
        nodeClick(v) {
            this.moveDirId = v.id;
            this.moveNode = v;
        },
        clearD() {
            this.dialogVisible = false;
            this.moveDirId = '';
            this.dsTypeId = '';
            this.data = [];
        },
        checked() {
            const {moveDirId,dsTypeId,beforePos } = this;
            if (!moveDirId) return this.$message.warning('请选择要移动到的目录！');
            if (this.isDataset && !dsTypeId) return this.$message.warning('请选择数据来源类型！');
            if(this.isDataset && moveDirId === beforePos.get('fromCatalog') && dsTypeId === beforePos.get('fromDsType') ) {
                return this.clearD();
            }
            if(!this.isDataset && moveDirId === beforePos.get('fromCatalog')){
                return this.clearD();
            }
            this.$emit('moveUpdate', this.info.id, this.moveDirId, this.moveNode, this.dsTypeId,beforePos);
        },
    }
}
</script>

<style scoped lang="less">

.dataTree {
    padding: 10px;
    background: #fff;
    border: 1px solid #ddd;
    height: calc(75vh - 212px - 2rem);
}

.ce-tree {
    margin-top: 12px;
    height: calc(100% - 50px);
    overflow: auto;
}

.ce-tree__menu {
    position: fixed;
    top: 0;
    min-width: 80px;
    text-align: left;
    border: 1px solid #ccc;
    background: #fff;
    padding: 0;
    z-index: 100;
    box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
    border-radius: 2px;
}

.ce-tree__menu li {
    cursor: pointer;
    list-style: none outside none;
    font-size: 12px;
    white-space: nowrap;
    border-bottom: 1px solid #eee;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
    color: #666;
}

.ce-tree__menu li:hover {
    color: @font-color;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
}

.node-label {
    //max-width: 166px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.node-label::before {
    padding-right: 5px;
}
</style>
