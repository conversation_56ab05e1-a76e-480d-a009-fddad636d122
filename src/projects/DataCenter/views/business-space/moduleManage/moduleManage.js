
export default {
    name: "moduleManage" ,
    data(){
        return {
            filterText : "" ,
            tableData : [
                {
                    name : "123" ,
                    code : "321"
                }
            ] ,
            tHeadData : [
                {
                    prop : "businessName" ,
                    label : "业务板块名称",
                    minWidth : "160px"
                },{
                    prop : "code" ,
                    label : "业务板块英文名",
                    minWidth : "160px"
                },{
                    prop : "description" ,
                    label : "描述",
                    minWidth : "160px"
                },{
                    prop : "creator" ,
                    label : "创建用户",
                    minWidth : "100px"
                },{
                    prop : "creatTime" ,
                    label : "创建时间",
                    minWidth : "140px"
                },{
                    prop : "site" ,
                    label : "存储位置",
                    minWidth : "100px"
                },{
                    prop : "operate" ,
                    label : "操作",
                    align:"center" ,
                    minWidth : "100px"
                },
            ] ,
            operateIcons : [
                {
                    name: "编辑",
                    clickFn: this.edit,
                    show: () => true,
                    condition: (right) => true
                },{
                    name: "删除",
                    clickFn: this.deleteM,
                    show: () => true,
                    condition: (right) => true
                },
            ] ,
            addBtnTxt :"新增业务板块"
        }
    },
    methods : {
        changePage(inx){

        },
        deleteM(row,inx){

        },
        edit(row,inx){

        },
        moduleClick(row){
            this.$router.push({name:"businessSpaceDefinition" , params : {row}})
        },
        addModule(){
            const vm = this;
            let layer = this.$dgLayer({
                title : "新建业务板块" ,
                content: require("../dialog/addBusiness/addBusiness.vue"),
                maxmin: false,
                move:false,
                area: ["1200px", "560px"],
            })
        }
    }
}
