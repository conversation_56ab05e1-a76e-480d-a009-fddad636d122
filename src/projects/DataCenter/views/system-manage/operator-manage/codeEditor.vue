<template>
    <div class="codeBox">
        <codemirror
            v-model="statement"
            :options="cmOptions"
            @ready="onCmReady"
        ></codemirror>
    </div>
</template>
 
<script>
// 引入语言
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/mode/javascript/javascript.js'

// 引入样式
import 'codemirror/theme/xq-light.css'


// require component
import { codemirror } from 'vue-codemirror'
 
// require styles
import 'codemirror/lib/codemirror.css'
 

import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/comment-fold'
import 'codemirror/addon/fold/markdown-fold'
import 'codemirror/addon/fold/xml-fold'
import 'codemirror/addon/fold/indent-fold'

import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/addon/hint/javascript-hint'
import 'codemirror/addon/hint/xml-hint'
import 'codemirror/addon/hint/sql-hint'
import 'codemirror/addon/hint/anyword-hint'
import 'codemirror/addon/display/autorefresh'

export default {
    components: {
        codemirror
    },
    props : {
        langType : {
            type : String ,
            default : 'java',
        },
        code: {
            type : String ,
            default : '',
        },
    },
    data () {
        return {
            statement : "",
            cmOptions: {
                theme: 'xq-light',
                mode: 'text/x-scala',
                readOnly: false,
                tabSize: 4, // 制表符
                indentUnit: 2, // 缩进位数
                lineNumbers: true,
                ineWiseCopyCut: true,
                viewportMargin: 1000,
                autofocus: true,
                autocorrect: true,
                spellcheck: true,
                specialChars: /[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,
                specialCharPlaceholder: function (ch) {
                let token = document.createElement("span");
                let content = "\u002e";
                token.className = "cm-invalidchar";
                if (typeof content == "string") {
                    token.appendChild(document.createTextNode(content));
                }
                token.title = "\\u" + ch.charCodeAt(0).toString(16);
                token.setAttribute("aria-label", token.title);
                    return token
                },
                extraKeys: {
                    Tab: (cm) => {
                        if (cm.somethingSelected()) {      // 存在文本选择
                            cm.indentSelection('add');    // 正向缩进文本
                        } else {                    // 无文本选择
                            cm.replaceSelection(Array(cm.getOption("indentUnit") + 1).join(" "), "end", "+input");  // 光标处插入 indentUnit 个空格
                        }
                    },
                },
                lint: false,
                // 代码折叠
                gutters: [
                    "CodeMirror-lint-markers",
                    "CodeMirror-linenumbers",
                    "CodeMirror-foldgutter"
                ],
                lineWrapping: false,
                foldGutter: true, // 启用行槽中的代码折叠
                autoCloseBrackets: true, // 自动闭合符号
                autoCloseTags: true,
                matchTags: { bothTags: true },
                matchBrackets: true, // 在光标点击紧挨{、]括号左、右侧时，自动突出显示匹配的括号 }、]
                styleSelectedText: true,
                styleActiveLine: true,
                autoRefresh: true,
                highlightSelectionMatches: {
                    minChars: 2,
                    trim: true,
                    style: "matchhighlight",
                    showToken: false
                },
                hintOptions: {
                    completeSingle: false
                }
            }
        }
    },
    methods: {
        onCmReady(cm) {
        // 这里的 cm 对象就是 codemirror 对象，等于 this.$refs.myCm.codemirror 
            cm.on("inputRead", (cm, obj) => {
                if (obj.text && obj.text.length > 0) {
                    let c = obj.text[0].charAt(obj.text[0].length - 1)
                    if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
                        cm.showHint({ completeSingle:false })
                    }
                }
            })
        }
    },
    computed: {
        codemirror() {
            return this.$refs.myCm.codemirror
        }
    },
    mounted() {
        this.cmOptions.mode = this.langType === 'java' ? 'text/x-java' : 'text/x-scala';
        this.statement = this.code || "";
    }
}
</script>
<style lang="less">
    .codeBox {
        height: 100%;
    }
    
    .CodeMirror {
        height: 400px;
        font-family: Arial, monospace;
        font-size: 16px;
        letter-spacing: 1.5px;
    }
    .CodeMirror-hints {
        position: absolute;
        z-index: 3000;
        overflow: hidden;
        list-style: none;
        
        margin: 0;
        padding: 2px;
        
        -webkit-box-shadow: 2px 3px 5px rgba(0,0,0,.2);
        -moz-box-shadow: 2px 3px 5px rgba(0,0,0,.2);
        box-shadow: 2px 3px 5px rgba(0,0,0,.2);
        border-radius: 3px;
        border: 1px solid silver;
        
        background: white;
        font-size: 90%;
        font-family: monospace;
        
        max-height: 20em;
        overflow-y: auto;
    }
</style>