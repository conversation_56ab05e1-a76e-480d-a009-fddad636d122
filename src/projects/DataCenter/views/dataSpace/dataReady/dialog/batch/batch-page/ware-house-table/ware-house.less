/deep/.expect-select {
  .el-select {
    width: 100%;
  }

}
.number-warehouse-table {
  label {
    display: flex;
    width: 110px;
    justify-content: flex-start;
  }
  .isRequire {
    label::before {
      content: "*";
      display: inline-block;
      color: #f5222d;
      margin-right: 4px;
    }
  }
  .n-w-t-top {
    display: flex;
    align-items: center;

    .n-w-t-top-one {
      margin: 0 12px;
      width: 120px;
    }
    .n-w-t-top-two {
      width: 490px;
    }
  }
  .n-w-t-center {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .n-w-t-c-left {
      display: flex;
      align-items: center;
      margin-right: 12px;
      .n-w-t-c-l-select {
        margin-left: 12px;
      }
    }
    .n-w-t-c-right {
      flex: 1;
      //width: 405px;
    }
  }
  .n-w-t-c-table {
    margin-top: 18px;
  }
}
