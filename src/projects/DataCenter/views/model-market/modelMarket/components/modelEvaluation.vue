<template>
    <div class="evaluation">
        <div>
            <div class="evaluation-score">
                <p>用户评价</p>
                <div class="evaluation-score-div">
                    <div>
                        <span>综合评分</span>
                        <el-rate
                                v-model="value"
                                disabled
                                show-score
                                text-color="#ff9900"
                                score-template="{value}">
                        </el-rate>
                    </div>
                    <dg-button type="primary" @click="goEvaluation" v-if=" modelDataInfo.focusType === '0'">我要评价</dg-button>
                </div>
            </div>
            <el-divider></el-divider>
            <div class="evaluation-list" v-if="listData.length">
                <div>
                    <div v-for="(item , index) in listData" :key="index">
                        <el-row :gutter="10">
                            <el-col :span="2" class="evaluation-list_left">
                                <el-avatar icon="el-icon-user-solid"></el-avatar>
                                <div class="overLineClamp1" :title="item.username">{{item.username}}</div>
                            </el-col>
                            <el-col :span="22">
                                <el-rate
                                        :title="item.score"
                                        v-model="item.score"
                                        disabled
                                        text-color="#ff9900">
                                </el-rate>
                                <div class="overLineClamp2 evaluation-list_content" :title="item.operatecontent">{{item.operatecontent}}</div>
                                <div class="evaluation-list_time">{{item.operatetime}}</div>
                            </el-col>
                        </el-row>
                        <el-divider></el-divider>
                    </div>
                </div>
                <div class="evaluation-list_pagination">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[5,10, 20]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="modelEvaluationInfo.totalCount">
                    </el-pagination>
                </div>
            </div>
            <div v-else>
                <no-data class="pct100"/>
            </div>
        </div>

        <evaluation-score ref="evaluationScore" @save="saveEvaluate"></evaluation-score>
    </div>
</template>

<script>
    import NoData from "@/components/no-data";
    import evaluationScore from "@/projects/DataCenter/views/model-market/dialog/evaluation";
    export default {
        name: "modelEvaluation",
        components: {
            NoData,
            evaluationScore,
        },
        props:{
            modelDataInfo:Object,
            modelEvaluationInfo:Object,
        },
        watch:{
            modelEvaluationInfo:{
                handler(newVal) {
                    this.value = this.modelDataInfo.avgScore;
                    this.listData = this.modelEvaluationInfo.data;
                },
                deep: true
            }
        },
        data(){
            return {
                value:this.modelDataInfo.avgScore,
                listData:[],
                currentPage:1,
                pageSize:5
            }
        },
        methods: {
            //评论
            goEvaluation(){
                this.$emit('getEvaluationInfo');
            },
            toEvaluation(data){
                this.$refs.evaluationScore.show(data);
            },
            closeEvaluation(){
                this.$refs.evaluationScore.close();
            },
            saveEvaluate( form){
                this.$emit('evaluateSave', form);
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.$emit('evaluateQuery', this.currentPage , this.pageSize);
            },
            handleCurrentChange(val){
                this.currentPage = val;
                this.$emit('evaluateQuery', this.currentPage , this.pageSize);
            }
        },
        created(){
            this.listData = this.modelEvaluationInfo.data;
        }
    }
</script>

<style scoped lang="less">
.evaluation{

    .overLineClamp1 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .overLineClamp2 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    &-score{
        p{
            font-size: 18px;
            color: rgba(0,0,0,0.85);
            margin:15px 0 15px 0;
            font-weight: bold;
        }
        &-div{
            display: flex;
            justify-content: space-between;
            div{
                display: flex;
                line-height: 20px;
            }
            span{
                margin-right:10px;
                font-size: 14px;
                color: rgba(0,0,0,0.85);
            }
        }
    }
    &-list{
        &_pagination{
            text-align: right;
        }
        &_left{
            text-align: center;
        }
        &_time{
            font-size: 12px;
            color: rgba(0,0,0,0.45);
        }
        &_content{
            font-size: 14px;
            color: rgba(0,0,0,0.85);
            line-height: 21px;
        }
    }
}
</style>