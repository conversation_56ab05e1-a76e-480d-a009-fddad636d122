import {common} from "@/api/commonMethods/common"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../../../service-mixins/service-mixins";
export default {
    name: "conditionPage",
    mixins : [common , commonMixins , servicesMixins],
    props : {datasetId : String},
    data() {
        return {
            dateFormat : "yyyy-MM-dd",
            conTxt: {
                addFilter: "添加过滤条件" ,
                isNull : "为空",
                norNull : "非空",
                i_placeholder : "请输入内容(限100个字符)" ,
                in : "介于" ,
                norIn : "不介于",
                n_placeholder : "请输入数值"
            },
            logicRadio : [
                {label : "且",value:"and"},
                {label : "或",value:"or"},
            ],
            filter_condition: [],
            fieldOpts: [],
            logic_options: [
                {
                    label: ">",
                    value: "<"
                },
                {
                    label: "≥",
                    value: "<="
                },
                {
                    label: "<",
                    value: ">"
                },
                {
                    label: "≤",
                    value: ">="
                },
                {
                    label: "=",
                    value: "="
                },
                {
                    label: "≠",
                    value: "!="
                }
            ],
            logic_options1 : [
                {
                    label: ">",
                    value: ">"
                },
                {
                    label: "≥",
                    value: ">="
                },
                {
                    label: "<",
                    value: "<"
                },
                {
                    label: "≤",
                    value: "<="
                },
                {
                    label: "=",
                    value: "="
                },
                {
                    label: "≠",
                    value: "!="
                }
            ],
            num_option : [
                { value: "等于"},
                { value: "不等于"},
                {value: "大于",},
                {value: "小于"},
                {value: "大于等于"},
                {value: "小于等于"},
                {value: "最大的N个"},
                {value: "最小的N个"},
                {value: "介于"},
                {value: "不介于"},
                {value: "为空"},
                {value: "非空"}
            ],
            time_option :  [
                {value: "介于"},
                {value: "不介于"},
                {value: "某个日期之前"},
                {value: "某个日期之后"},
                {value: "等于"},
                {value: "不等于"},
                {value: "为空"},
                {value: "非空"}
            ],
            txt_option : [
                {value: "包含"},
                {value: "不包含"},
                {value: "为空"},
                {value: "非空"},
                {value: "开头是"},
                {value: "开头不是"},
                {value: "结尾是"},
                {value: "结尾不是"}
            ],
            disabled : false ,
            numTip : "值"
        };
    },
    methods: {
        validate(){
            const vm = this, {filter_condition} = this;
            let result = true;
            for(let i = 0;i < filter_condition.length; i++){
                let inside = filter_condition[i].inside;

                for(let j = 0; j < inside.length; j++){
                    let ins = inside[j];
                    switch (ins.format) {
                        case 'TEXT' :
                            if(ins.logic !== "为空" && ins.logic !== "非空" ){
                                result = ins.inputVal !== "";
                            }
                            break;
                        case 'NUMBER' :
                            if(ins.logic !== "为空" && ins.logic !== "非空"){
                                if(ins.logic === "介于" || ins.logic === "不介于"){
                                    result = ins.spaceVal1 !== "" && ins.spaceVal2 !== "";
                                }else {
                                    result = ins.spaceVal !== "";
                                }
                            }
                            break;
                        default:
                            if(ins.logic !== "为空" && ins.logic !== "非空" ){
                                if(ins.logic === "介于" || ins.logic === "不介于"){
                                    result = ins.timeSpace[0] && ins.timeSpace[1];
                                }else {
                                    result = ins.time !== "";
                                }
                            }
                            break;

                    }
                    if(!result) {
                        vm.$message.warning("过滤条件结果值不能为空!");
                        break;
                    }
                }
                if(!result) break;
            }

            return result;
        },
        numChecked(val , item){
            if(!val){
                item.spaceVal = 0;
            }
        },
        logicChange(index ,inx){
            const vm  = this;
            vm.resetValue(index,inx);
            vm.filter_condition[index].inside[inx].reNew =false;
            setTimeout(()=> {
                vm.filter_condition[index].inside[inx].reNew =true;
            },10)
        },
        fieldSelected(params, index, inx) {
            let [val, data] = params;
            const vm  = this;
            vm.filter_condition[index].inside[inx].format = data.format;
            vm.filter_condition[index].inside[inx].name = data.name;
            vm.filter_condition[index].inside[inx].logic = "";
            vm.filter_condition[index].inside[inx].field = data.code;
            vm.initLogic(data.format ,index , inx);
        },
        initLogic(format ,index, inx  ){
            const vm  = this;
            switch (format) {
                case 'TEXT' :
                    vm.filter_condition[index].inside[inx].logic = vm.txt_option[0].value;
                    break;
                case 'NUMBER' :
                    vm.filter_condition[index].inside[inx].logic = vm.num_option[0].value;
                    break;
                default:
                    vm.filter_condition[index].inside[inx].logic = vm.time_option[0].value;
                    break;
            }
        },
        initCondition(data){
            const vm = this;
            vm.disabled = !data.canEdit;
            let condition = JSON.parse(data.condition);
            vm.filter_condition = [];
            condition.forEach(con =>{
                let item = {
                    relation : con.groupType ,
                    inside : []
                };
                con.filterFieldInfo.forEach(fields =>{
                    let row = {
                        id : fields.id,
                        field:  fields.fieldCode,
                        name: fields.fieldName,
                        logic:  fields.operator,
                        format : fields.format,
                        inputVal: "",
                        select1: ">",
                        select2: "<",
                        spaceVal: "",
                        spaceVal1: "",
                        spaceVal2: "",
                        time: "",
                        timeSpace: "",
                        reNew :true
                    };
                    switch (fields.format) {
                        case 'TEXT' :
                            row.inputVal = fields.inputValue;
                            break;
                        case 'NUMBER':
                            row.spaceVal = fields.inputValue;
                            row.select1  = fields.startOperator;
                            row.select2  = fields.endOperator;
                            row.spaceVal1 = fields.startValue;
                            row.spaceVal2 = fields.endValue;
                            break;
                        default:
                            row.timeSpace = [];
                            fields.inputValue !== "" ? row.time = fields.inputValue : true;
                            fields.startValue !== "" ? row.timeSpace[0] = fields.startValue : true;
                            fields.endValue !== "" ? row.timeSpace[1] = fields.endValue : true;
                            break;
                    }
                    item.inside.push(row);
                });
                vm.filter_condition.push(item);
            })
        },
        //添加行
        addRow(item ,index) {
            const row = {
                id : this.fieldOpts[0].value,
                field: "",
                name: this.fieldOpts[0].name,
                logic: "",
                format : this.fieldOpts[0].format ,
                inputVal: "",
                select1: ">",
                select2: "<",
                spaceVal: "",
                spaceVal1: "",
                spaceVal2: "",
                time: "",
                timeSpace: "",
                reNew :true
            };
            this.$set(item, item.length, row);
            this.fieldSelected([this.fieldOpts[0].value , this.fieldOpts[0]] ,index , item.length-1);
        },
        resetValue(index,inx){
            const vm = this;
            let row = vm.filter_condition[index].inside[inx];
            row.inputVal = "";
            row.spaceVal = "";
            row.spaceVal1 = "";
            row.spaceVal2 = "";
            row.time = "";
            row.timeSpace = "";
        },
        //添加大项
        addBigRow() {
            const row = {
                relation: "and",
                inside: [
                    {
                        id :this.fieldOpts[0].value,
                        field: "",
                        name: this.fieldOpts[0].name,
                        logic: "",
                        format : this.fieldOpts[0].format ,
                        inputVal: "",
                        select1: ">",
                        select2: "<",
                        spaceVal: "",
                        spaceVal1: "",
                        spaceVal2: "",
                        time: "",
                        timeSpace: "" ,
                        reNew :true
                    }
                ]
            };
            this.filter_condition.push(row);
            this.fieldSelected([this.fieldOpts[0].value , this.fieldOpts[0]] ,this.filter_condition.length -1 , 0);
        },
        async init(data = ""){
            const vm = this , {datasetServices , datasetMock , datasetId , settings } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            vm.fieldOpts = [];
            settings.loading = true;
            await services.getLogicColumn("" , vm.datasetId , settings).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    result.forEach(item => {
                        vm.fieldOpts.push({
                            label : item.name || item.code ,
                            code : item.code ,
                            value : item.id ,
                            format : item.format,
                            name : item.name
                        })
                    })
                }
            });
            if(!data){
                services.getJoinAndFilter(datasetId , 'filter').then(res => {
                    if(res.data.status === 0) {
                        if(res.data.data){
                            vm.initCondition( {canEdit : true , ...res.data.data});
                        }
                    }
                })
            }else {
                vm.initCondition({canEdit :data.canEdit , ...data.result });
            }

        },
        //删除外部行
        delBigRow(inside, item) {
            const vm = this;
            vm.confirm("删除" , "确定删除该条件" , ()=>{
                vm.$delete(inside, inside.indexOf(item));
                vm.filter_condition = vm.filter_condition.filter(v => v.inside.length > 0);
            })

        }
    }
}