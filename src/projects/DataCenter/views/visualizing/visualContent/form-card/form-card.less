.chartCard {
  width: 100%;
  height: 100%;
  overflow: auto;
}
.ce-padding {
  padding-left: 10px;
  padding-right: 10px;
}
.ce-simple_table {
    table-layout: fixed;
    width: 100%;
    height: auto;
    border: none;
  }
  .ce-simple__header {
    background: #e7eaf0;
  }
  .ce-simple_table th {
    padding: 6px 0;
    background: #FAFAFA;
    color: rgb(85, 85, 85);
    border-bottom: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;
  }
  .ce-simple__body {
    overflow: auto;
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;
    border-bottom: 1px solid #EBEEF5;
  }
  .ce-simple_table td {
    padding: 6px;
    text-align: center;
    border-bottom: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;

    /*文本超出换行*/
    // overflow:hidden;
    // word-wrap: break-word;
    // word-break: break-all;
    /*文本超出省略...*/
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
  .pre-wrap {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
  .empty-box {
    min-height:24px;
  }
  .omit{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
  }
  .newline{
    overflow:hidden;
    word-wrap: break-word;
    word-break: break-all;
  }
  // .ce-simple_table tr:nth-child(2n) {
  //   background: #FAFAFA;
  // }
  .ce-simple_table tr:nth-child(2n + 1) {
    background: #fff;
  }
  .ce-simple_table tbody tr:hover td {
    background-color: #d4ebfc;
  }
