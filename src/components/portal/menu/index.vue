<template>
    <el-menu class="primary-menu" v-bind="$attrs" v-on="$listeners" ref="menu" @select="menuClick">
        <template v-for="menu in menus">
            <el-menu-item v-if="!menu.children || menu.children.length === 0" :index="menu.id" :key="menu.id">
                <span class="menu-item">{{ menu.label }}</span>
            </el-menu-item>
            <el-submenu
                    v-else
                    :index="menu.id"
                    :key="menu.id"
                    popper-class="reset-submenu"
            >
                <template slot="title">
                    <span class="menu-item">{{ menu.label }}</span>
                </template>
                <submenu :children="menu.children"></submenu>
            </el-submenu>
        </template>
    </el-menu>
</template>
<script>
    import submenu from "./submenu";

    export default {
        name: "primary-menu",
        data(){
            return {}
        },
        props: {
            menus: {
                type: Array,
                default() {
                    return [];
                }
            }
        },
        methods: {
            /**
             * 抛出菜单 打开某个菜单方法
             * @param { string } index 当前菜单index
             */
            open(index) {
                this.$refs.menu.open(index)
            },
            menuClick(index , path){
                // console.log(index , path)
            }
        },
        components: {
            submenu
        }
    };
</script>
<style lang="less">
    .primary-menu {
        &.el-menu--horizontal {
            display: flex;
            border-bottom: none;

            > .el-menu-item {
                height: 4rem;
                line-height: 4rem;
            }

            .el-submenu {
                &__title {
                    height: 4rem;
                    line-height: 4rem;
                }

                &__icon-arrow {
                    color: #fff;
                }
            }
        }

        &.el-menu {
            border-right: none;
        }
    }
</style>