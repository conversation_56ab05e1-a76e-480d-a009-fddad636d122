import TreeAndList from "@/components/layout/treeAndListUi/index.vue"
import Tree from "./tree"
import List from "./list"
import HeaderPage from "./header/index.vue"
import Nodata from "./no-data/index.vue"
import addDataset from "../add-dataset/index.vue"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins";
import rapidForm from "../rapid-form/index.vue"
import mergeDatasetList from "../../dialog/rapid-analyse/tree-operate/merge-dataset"
export default {
    name: "analyse",
    mixins: [ commonMixins, servicesMixins],
    components :{
        TreeAndList,
        Tree ,
        List ,
        HeaderPage ,
        Nodata ,
        addDataset,
        rapidForm,
        mergeDatasetList
    },
    computed: {
        datasetId_for_req(){
            return this.new_datasetId !== "" ? this.new_datasetId : this.datasetId;
        }
    },
    data(){
        return {
            name : "" ,
            datasetId :"",
            treeVal : "" ,
            new_datasetId : "",
            relationId : "" , //步骤id 存储轨迹
            treeData : [],
            bodyStyle : {
                'height':'calc(100% - 3.5rem )' ,
                'box-sizing' : 'border-box'
            },
            treeNodeId : "",
            hasData : false ,
            treeNode : {},
            parentId : ""
        }
    },
    methods :{
        deleteStepSuccess(){
            this.editInit();
        },
        syncColumnStep(data){
            this.$refs.list.syncColumnStep(data , this.name);
        },
        editColumnStep(data){
            this.$refs.columns.editColumnStep(data);
        },
        addColumnStep(data){
            this.$refs.columns.addColumnStep(data);
        },
        filterConditionStep(data){
            this.$refs.list.filterConditionStep(data);
        },
        joinTableStep(data){
            this.$refs.list.joinTableStep(data);
        },
        unionTableStep(data){
            this.$refs.list.unionTableStep(data);
        },
        initTree(){
            this.editInit();
        },
        appendColumns(data , isNew){
            this.$refs.columns.appendColumns(data , isNew);
        },
        preview(data , addDatasetId){
            this.$refs.list.preview(data , addDatasetId);
        },
        async getAllColumns(){
            await this.$refs.columns.getAllColumns(this.$refs.columns.data);
            this.$refs.list.storeAllColumns(this.$refs.columns.all_columns);
        },
        init(){
            const vm = this;
            let {name , datasetId , new_datasetId ,site , treeNodeId ,treeNode , parentId } = vm.$route.params;
            if(!name){
                vm.$router.push({name:"dataSetOperation"});
            }
            if(parentId) vm.parentId = parentId;
            if(datasetId){
                vm.datasetId = datasetId;
                vm.editInit();
            }
            if(new_datasetId){
                vm.new_datasetId = new_datasetId;
                vm.datasetId = new_datasetId;
            }
            if(treeNode)  vm.treeNode = treeNode;
            if(site){
                vm.treeNodeId = site;
            }else if(treeNodeId){
                vm.treeNodeId = treeNodeId;
            }
            vm.name = name;

        },
        editInit(){
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            services.getLogicDataColumn( '' , vm.datasetId ).then(res => {
                if(res.data.status === 0){
                    if(res.data.data){
                        vm.hasData = true;
                        vm.initColumnsTree(res.data.data);
                    }
                }
            })
        },
        addDataset(){
            this.$refs.addDataset.show( this.datasetId||this.datasetId );
        },
        initColumnsTree(data , addDatasetId){
            const vm = this;
            if(addDatasetId){
                vm.datasetId = addDatasetId;
            }
            vm.hasData = true;
            vm.$nextTick(()=>{
                vm.$refs.columns.initTree(data , addDatasetId);
            })
        },
        mergeSuccess(data){
            const vm = this;
            vm.$nextTick(()=>{
                /*if(data) {
                    vm.$refs.columns.mergeDataset(data);
                }else {

                }*/
                vm.initTree();
            })
        },
        showSaveOther(){
            this.$refs.saveOther.show(true , this.parentId);
        },
        saveOther(data){ //另存为
            let params = {
                name : data.name ,
                logicDataSetId : this.datasetId ,
                metaDataObjIds :this.datasetId ?  [this.datasetId] :  [this.new_datasetId],
                saveAsId : data.new_datasetId
            };
            const vm = this, {datasetServices, datasetMock ,settings } = this;
            let services = vm.getServices(datasetServices, datasetMock);
            services.saveAs(params , settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("保存成功");
                    vm.$refs.saveOther.visible = false;
                }
            })
        },
        saveDataset(name){//保存
            let data = {
                name : name ,
                logicDataSetId : this.new_datasetId !== "" ? this.new_datasetId : this.datasetId ,
                metaDataObjIds :this.datasetId ?  [this.datasetId] :  [this.new_datasetId]
            };
            const vm = this, {datasetServices, datasetMock ,settings } = this;
            let services = vm.getServices(datasetServices, datasetMock);
            settings.loading = true;
            services.save(data , settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("保存成功");
                    vm.$emit("initTree");
                }
            })
        },
        editSuccess(){
            this.$refs.list.changePage(1);
        }
    },
    created(){
        this.init();
    }
}
