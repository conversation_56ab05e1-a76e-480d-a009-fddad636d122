
.ce-visualTabs.el-tabs--top {
  /deep/.el-tabs__item.is-top:nth-child(2) {
    padding-left: 14px;
  }
  /deep/.el-tabs__item.is-top:last-child {
    padding-right: 14px;
  }
}

.ce-visualTabs {
  display: inline-block;
  max-width:100%;
}

.ce-drop_area {
  height: calc(100% - 10px);
  border:4px dotted #ddd;
  text-align: center;
  font-size: 18px;
  color: #ccc;
  box-sizing: border-box;
  font-weight: bold;
  display: flex;

  span {
    flex : 1;
    align-self: center;
  }
}
.ce-tab_charts {
  overflow: auto;
}
.ce-tab_charts-win {
  height: 100%;
  overflow: auto;
}