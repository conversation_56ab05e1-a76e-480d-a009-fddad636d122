<template>
<div class="ce-collection">
    <el-tabs v-model="tabName">
        <el-tab-pane label="关联设置" name="first" ></el-tab-pane>
        <el-tab-pane label="输出字段" name="second"></el-tab-pane>
    </el-tabs>
     <div class="ce-plugin_base" v-show="tabName === 'first'">
        <el-form  size="mini" label-position="top">
            <el-form-item
                    v-for="(attr , i ) in attrsList"
                    :key="i"
            >
                <div class="ce-label_title"> <span > *</span> {{attr.label + " :"}}
                    <i class="el-icon-question" v-if="attr.label == '选择差集类型'"></i>
                </div>
                <span class="formTip" v-if="attr.tip"><i class="el-icon-info"></i>{{attr.tip}}</span>
                <div class="radio-group" v-if="attr.type === 'way'" >
                    <div v-for="(btn , inx ) in attr.btns"
                            :key="inx" :class="['radio',{'is-active' : btnActive === inx }]"
                            @click="btnCheck(inx , btn.value)"
                    >
                        <i class="dg-iconp radio_i" :class="btn.classN"></i>
                        <span>{{btn.name}}</span>
                    </div>
                </div>
                <template v-if="attr.type === 'data_obj'">
                    <el-row class="ce-plugin_contend" >
                        <el-col :span="8" >
                            <div class="tabelName" :title="tableInfo.left.tableName">来源表：{{tableInfo.left.tableName}}</div>
                        </el-col>
                        <el-col :span="1" class="h20"></el-col>
                        <el-col :span="8">
                            <div class="tabelName"  :title="tableInfo.right.tableName">关联表：{{tableInfo.right.tableName}}</div>
                        </el-col>
                    </el-row>
                    <el-row v-for="(joinField, joinIndex) in joinFieldList"
                            :key="joinIndex"
                            class="ce-plugin_contend"
                    >
                        <el-col :span="8">
                            <el-select v-model="joinField.left.code"
                                        filterable
                                        placeholder="请选择"
                                        @change="joinFieldChange(joinField.left.code , 'left' , joinIndex)"
                            >
                                <el-option
                                        v-for="field in leftTable.fields"
                                        :key="field.colCode"
                                        :label="field.colName"
                                        :value="field.colCode">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="1" class="tc">=</el-col>
                        <el-col :span="8" >
                            <el-select v-model="joinField.right.code"
                                        filterable
                                        placeholder="请选择"
                                        @change="joinFieldChange(joinField.right.code , 'right' ,joinIndex)"
                            >
                                <el-option
                                        v-for="field in rightTable.fields"
                                        :key="field.colCode"
                                        :label="field.colName"
                                        :value="field.colCode">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="2" class="pl10">
                            <i
                                    class="el-icon-close"
                                    style="cursor:pointer"
                                    title="删除"
                                    @click.stop="handleDeleteJoinField(joinIndex)"
                            ></i>
                        </el-col>
                    </el-row>
                    <el-row  class="ce-plugin_contend">
                        <el-button size="small" type="text" class="el-icon-circle-plus-outline" @click="handleAddJoinField" > 添加条件</el-button>
                    </el-row>
                </template>

            </el-form-item>
        </el-form>
        
    </div>
    <div class="ce-plugin_attr" v-show="tabName === 'second'">
        <div class="ce-plugin_head">
            <el-input
                    class="ce-plugin_input"
                    placeholder="请输入字段名/中文名"
                    suffix-icon="el-icon-search"
                    v-model="filterData"
            >
            </el-input>
            <el-button type="primary" @click="show()">选择输出字段</el-button>
        </div>
        <common-table
                height="calc(100% - 10px - 2rem)"
                :columns="resultColumns"
                :pagination="false"
                :data="filterDataTable"
                >

            <template
                    slot="opers"
                    slot-scope="{ row }">
                <el-popconfirm
                        title="确定删除吗？"
                        @confirm="handleClickDelete(row)"

                >
                    <i
                            style="cursor:pointer;color: #2f9bff"
                            slot="reference"
                            class="icon icon__btn icon__btn_delete pl5 pr5 f18 colorC"
                            title="删除"
                    >&#xe847;</i>
                </el-popconfirm>
            </template>
            <el-input slot="tempColName"
                        slot-scope="{ row }"
                        v-model.trim="row.tempColName"
            >
            </el-input>
        </common-table>
    </div>

    <common-dialog
            custom-class="params-dialog"
            :title="title"
            :width="width"
            :visible.sync="visible"
            @closed="clear_data"
            v-loading="loading"
    >

        <cicadaSubstractDialog ref="dialog"  v-if="showParams" :tableInfo="tableInfo" :leftTable="leftTable" :rightTable="rightTable" :type="type"
                                @updateOutPut="updateOutPut" :result="result" @cancel="cancel"></cicadaSubstractDialog>
        <div slot="footer"  >
            <el-button @click="canceled">取 消</el-button>
            <el-button type="primary" @click="submit">确定</el-button>
        </div>
    </common-dialog>
    <preView ref="preview"></preView>

</div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import operationDialogParam from "./operationDialogParam";
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import cicadaSubstractDialog from "./CicadaSubstractDialog"
    export default {
        name: "CicadaSubtractByKey",
        mixins : [pluginMixins , servicesMixins],
        components: {
            operationDialogParam,
            preView,
            cicadaSubstractDialog
        },
        props: {
            sourceData: Object,
            rowData : Object,
            loadParams: Object,
            hideMsg: Boolean
        },
        computed: {
            filterDataTable() {
                const vm = this;
                let f = vm.result.filter(data => {
                    if(data.colCode === vm.filterData ||  data.colCode.toLowerCase().indexOf(vm.filterData.toLowerCase()) !== -1 ||
                        data.tempColName === vm.filterData  ||  data.tempColName.toLowerCase().indexOf(vm.filterData.toLowerCase()) !== -1
                    ){
                        return true;
                    }
                })
                return f;
            }
        },
        created() {
            if(!this.hideMsg)this.initData();
            // this.isSeniorShow = {
            //     partitionShow: false,
            //     fieldInput: false,
            //     fieldOutPut: true,
            //     isCopyOrDispense: false,
            //     isSampleExpr: false,
            //     isThread: false,
            //     isException: false,
            //     notTransition:false,
            // }
        },
        data() {
            return {
                type: "left",
                width: '690px',
                filterData: "",
                tabName: "first",
                form: {
                    type : 'zcj'
                },
                resultColumns: [
                    // {type: 'index', label: '序号' ,align: 'center', width : 70},
                    {prop: 'colCode', label: '字段名'},
                    {prop: 'tempColName', label: '字段中文名'},
                    {prop: 'type', label: '数据类型'},
                    {prop: 'belongName', label: '来源于'},
                    {prop: 'opers', label: '操作',align: 'center', width: 70},
                ],
                btnValue: "Left",
                btnActive: 0,
                leftSelected:[],
                rightSelected:[],
                title: "添加输出字段",
                condtitionSelect: 'AND',
                attrsList: [
                    {
                        label: ' 选择差集类型',
                        btns: [
                            {
                                classN: 'icon-union-e',
                                value: 'CicadaLeftSubtractByKey',
                                name: '左差集'
                            }, {
                                classN: 'icon-union-f',
                                value: 'CicadaRightSubtractByKey',
                                name: '右差集'
                            },

                        ],
                        type: 'way',
                        value: '',
                    },
                    {
                        label: ' 选择关联条件',
                        type: 'data_obj',
                        value: ''
                    },
                    // {
                    //     label: ' 选择输出字段',
                    //     type: 'result',
                    //     value: '' ,
                    //     // tip : "若来源表出现重名字段,请修改字段别名,否则默认只保留一个字段"
                    // }
                ],
                tableInfo: { //左右表
                    left : {
                        tableName : ''
                    },
                    right : {
                        tableName : ''
                    }
                },
                joinFieldList: [  //连接字段
                    //{ left: '', right: '' }
                ],
                leftTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                rightTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                result: [],
                pluginMeta: {}
            }
        },
        methods: {
            handleClickDelete(row) {
                this.result.forEach((item, index) => {
                    if(item.colCode === row.colCode && item.belong === row.belong) {
                        this.result.splice(index, 1);
                    }
                })
            },
            canceled() {
                this.$refs.dialog.cancel();
            },
            submit() {
                this.$refs.dialog.submit();
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            save(saveName,showTip) {
                const vm = this, {pluginServices, pluginMock,  loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                if(this.tableInfo.left.tableName === "") {
                    this.$message.warning("输入源必须为两个");
                    return;
                }
                if(this.joinFieldList.length === 0) {
                    this.$message.warning("请选择连接字段");
                    return;
                }
                if(this.result.length === 0) {
                    this.$message.warning("请选择输出字段");
                    return;
                }
                for(let i = 0; i < vm.result.length; i++) {
                    if(!vm.judgeNull(vm.result[i].tempColName)) {
                        vm.$message.warning("请不要存在空字段");
                        return;
                    }
                    for(let j = i + 1; j < vm.result.length; j++) {
                        if(vm.result[i].tempColName === vm.result[j].tempColName) {
                            vm.$message.warning(`存在相同的字段中文名 "${vm.result[i].tempColName}",请修改配置`);
                            return;
                        }
                    }
                }

                loadParams.loading = true;
                vm.pluginMeta.cicadaAggregatorFieldExps = [];
                vm.result.forEach(item => {
                    vm.pluginMeta.cicadaAggregatorFieldExps.push({
                        objId: item.tableId,
                        objCode: item.tableCode,
                        replaceFieldName: item.colCode,
                        newFieldName: item.colCode,
                        valType: item.type,
                        length: item.length,
                        precision: item.precsn,
                        columnZhName: item.tempColName
                    })
                });

                vm.pluginMeta.cicadaAggregatorJoinColumns = [];
                vm.joinFieldList.forEach((value, index) => {
                    vm.pluginMeta.cicadaAggregatorJoinColumns.push({
                        index:index,
                        leftCol:value.left.code,
                        leftId:value.left.id,
                        rightCol:value.right.code,
                        rightId:value.right.id,
                    })
                });
                services.saveCicadaSubtractByKeyPlugin(vm.sourceData.id, vm.pluginMeta ,loadParams).then(result => {
                    if(result.data.status === 0){
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                        showTip && vm.$message.success("保存成功！");
                    }
                })
            },
           
            cancel() {
                this.clear_data();
                this.close();
            },
            initData() {   //初始化
                const vm = this, {pluginServices, pluginMock, settings,loadParams} = this;
                loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                services.cicadaSubtractByKeyPluginPage(vm.sourceData.id, loadParams).then(result => {

                    if (result.data.status === 0) {
                        let leftSelCol = [];
                        let rightSelCol = [];
                        result.data.data.cicadaAggregatorFieldExps.forEach(exp => {
                            if (exp.objCode.includes("_left")) {
                                leftSelCol.push({expName:exp.newFieldName, zhName: exp.columnZhName});
                            }
                            if (exp.objCode.includes("_right")) {
                                rightSelCol.push({expName:exp.newFieldName, zhName: exp.columnZhName});
                            }
                        });

                        result.data.data.columns.leftTableColumns.forEach(leftCol => {
                            let item = {
                                colCode: leftCol.columnName,
                                colName: leftCol.columnZhName === "" ? leftCol.columnName : leftCol.columnZhName,
                                id: leftCol.dataColumnId,
                                type: leftCol.columnType,
                                length: leftCol.length,
                                precsn: leftCol.precsn,
                                tempColName: leftCol.columnZhName === "" ? leftCol.columnName : leftCol.columnZhName,
                            };
                            let itemCopy = JSON.parse(JSON.stringify(item));
                            leftSelCol.forEach(temp => {
                                if(temp.expName === leftCol.columnName|| temp.expName === leftCol.columnZhName) {
                                    item.tempColName = temp.zhName;
                                    itemCopy.colName = temp.zhName;
                                    vm.leftSelected.push(itemCopy)
                                }
                            })
                            vm.leftTable.fields.push(item);
                        });
                        result.data.data.columns.rightTableColumns.forEach(rightCol => {
                            let item = {
                                colCode: rightCol.columnName,
                                colName: rightCol.columnZhName === "" ? rightCol.columnName : rightCol.columnZhName,
                                id: rightCol.dataColumnId,
                                type: rightCol.columnType,
                                length: rightCol.length,
                                precsn: rightCol.precsn,
                                tempColName: rightCol.columnZhName === "" ? rightCol.columnName : rightCol.columnZhName
                            };
                            let itemCopy = JSON.parse(JSON.stringify(item));
                            rightSelCol.forEach(temp => {
                                if(temp.expName === rightCol.columnName || temp.expName === rightCol.columnZhName) {
                                    item.tempColName = temp.zhName;
                                    itemCopy.colName = temp.zhName;
                                    vm.rightSelected.push(itemCopy);
                                }
                            })
                            vm.rightTable.fields.push(item);
                        });
                        // 设置表名
                        vm.tableInfo = {
                            left: {
                                tableName: result.data.data.pluginMeta.leftStepName,
                                tableCode: result.data.data.pluginMeta.leftStepCode,
                                tableId: result.data.data.pluginMeta.leftStepId
                            },
                            right: {
                                tableName: result.data.data.pluginMeta.rightStepName,
                                tableCode: result.data.data.pluginMeta.rightStepCode,
                                tableId: result.data.data.pluginMeta.rightStepId
                            }
                        };
                        vm.pluginMeta = result.data.data.pluginMeta;
                        // 初始化链接字段
                        vm.handleAddJoinField();

                        if (vm.pluginMeta.op.includes("Left")) {
                            vm.btnActive = 0;
                            vm.btnValue = 'Left';
                            vm.type = 'left';
                        } else {
                            vm.btnActive = 1;
                            vm.btnValue = 'Right';
                            vm.type = 'right';
                        }
                        vm.pluginMeta.cicadaAggregatorJoinColumns.forEach((item, index) => {
                            if (index !== 0) {
                                vm.handleAddJoinField();
                            }
                            vm.joinFieldChange(item.leftCol, 'left', index);
                            vm.joinFieldChange(item.rightCol, 'right', index);
                        });
                        if (vm.tableInfo.left && vm.tableInfo.right) {
                            vm.leftSelected.forEach(item => {
                                vm.result.push({belongName: '来源表：' + vm.tableInfo.left.tableName, belong:'left', row: item,
                                    tableCode: this.tableInfo.left.tableCode,
                                    tableId: this.tableInfo.left.tableId,
                                    tableName: this.tableInfo.left.tableName,
                                    colCode: item.colCode,
                                    colName: item.colName,
                                    id: item.id,
                                    length: item.length,
                                    precsn: item.precsn,
                                    tempColName: item.colName,
                                    type: item.type});
                            });
                            vm.rightSelected.forEach(item => {
                                vm.result.push({belongName: '关联表：' + vm.tableInfo.right.tableName, belong:'right', row: item,
                                    tableCode: this.tableInfo.right.tableCode,
                                    tableId: this.tableInfo.right.tableId,
                                    tableName: this.tableInfo.right.tableName,
                                    colCode: item.colCode,
                                    colName: item.colName,
                                    id: item.id,
                                    length: item.length,
                                    precsn: item.precsn,
                                    tempColName: item.colName,
                                    type: item.type});
                            });
                        }
                    }
                })
            },
            btnCheck(index, value) {
                this.btnActive = index;
                this.btnValue = value;
                // if(!this.pluginMeta.op) return;
                this.leftSelected = [];
                this.rightSelected = [];
                this.result = [];
                this.pluginMeta.op = value;
                if(value === 'CicadaLeftSubtractByKey') {
                    this.type = "left";
                    for(let i = 0; i < this.leftTable.fields.length; i++) {
                        this.leftTable.fields[i].tempColName = this.leftTable.fields[i].colName;
                    }
                }
                else {
                    this.type = "right";
                    for(let i = 0; i < this.rightTable.fields.length; i++) {
                        this.rightTable.fields[i].tempColName = this.rightTable.fields[i].colName;
                    }
                }
            },
            joinFieldChange(val, side, inx) {
                let id;
                let code;
                this[side + 'Table'].fields.forEach(data => {
                    if (data.colCode === val) {
                        id = data.id;
                        code = data.colCode;
                    }
                });
                this.joinFieldList[inx][side].id = id;
                this.joinFieldList[inx][side].code = code;
            },
            handleAddJoinField() { //连接字段
                const {leftTable, rightTable, joinFieldList} = this;
                let item = {left: {code: '', id: ''}, right: {code: '', id: ''}};
                if (leftTable.fields && leftTable.fields.length > 0) {
                    // item.left = leftTable.fields[0].colCode;
                    item.left = {
                        code: leftTable.fields[0].colCode,
                        id: leftTable.fields[0].id,
                    }
                }
                if (rightTable.fields && rightTable.fields.length > 0) {
                    // item.right = rightTable.fields[0].colCode;
                    item.right = {
                        code: rightTable.fields[0].colCode,
                        id: rightTable.fields[0].id,
                    }
                }
                joinFieldList.push(item)
            },
            handleDeleteJoinField(index) {//删除连接字段
                const {joinFieldList} = this;
                joinFieldList.splice(index, 1);
            },
            updateOutPut(data) {
                let map = {};
                for(let i = 0; i < data.length; i++) {
                    map[data[i].colCode + data[i].belong] = data[i];
                }
                this.result.forEach((item, index) => {
                    if(map[item.colCode + item.belong] === undefined) {
                        this.result.splice(index, 1);
                    }
                    else {
                        map[item.colCode + item.belong].tempColName = item.tempColName;
                    }
                })
                this.result = data;
                this.clear_data();
                this.close();
                this.triggerEvent('resize' , 300);
            },
        }
    }
</script>
<style lang="less" scoped src="./collection.less" ></style>
<style  scoped lang="less">
   
    .mainButton {
        width: 100%
    }
   
    .ce-operation_box {
        display: flex;
        justify-content: center;
    }
    
   
    .radio {
        display: flex;
        align-items: center;
        /*padding: 0 14px;*/

        justify-content: center;
        flex: 1;
        margin-right: 4px;
        width: 150px;
        height: 44px;
        border-radius: 2px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        box-sizing: border-box;
        cursor: pointer;
    }
    .radio:hover {
        border: 1px solid #0088ff;
        box-shadow: 0 2px 2px 0 rgba(0, 136, 255, 0.15);
    }
    .radio_i {
        font-size: 26px;
        color: #0088ff;
    }
    .radio_span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-left: 14px;
    }
    .is-active {
        border: 1px solid #0088ff;
        box-shadow: 0 2px 2px 0 rgba(0, 136, 255, 0.15);
    }
    

</style>
