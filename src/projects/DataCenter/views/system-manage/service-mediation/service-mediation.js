import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {servicesMixins} from "../service-mixins/service-mixins";

export default {
    name: "service-mediation",
    mixins: [commonMixins, servicesMixins, coustTableH],
    computed : {
        // ...mapGetters(["userInfo" , "userRight"]),
        // rights (){
        //     let rights = [];
        //     if(this.userRight){
        //         this.userRight.forEach(r =>{
        //             rights.push(r.funcCode)
        //         });
        //     }
        //     return rights;
        // }
    } ,
    data() {
        return {
            filterText: "",
            tHeadData: [
                {
                    prop: 'name',
                    label: "中介名称",
                    minWidth: 120
                }, {
                    prop: 'address',
                    label: "中介地址",
                    minWidth: 120
                }, {
                    prop: 'version',
                    label: "中介版本",
                    minWidth: 100,
                },{
                    prop: 'updateTime',
                    label: "更新时间",
                    minWidth: 120,
                },{
                    prop: 'operate',
                    label: "操作",
                    width: 200,
                    align : "center",
                },
            ],
            tableData: [],
            addTxt: "新增中介",
            operateIcons: [
                {
                    name: "编辑",
                    clickFn: this.editMediation,
                    show: () => true,
                    condition: (right) => true
                }, {
                    name: "查看",
                    clickFn: this.lookMediation,
                    show: () => true,
                    condition: (right) => true
                },{
                    name: "删除",
                    clickFn: this.deleteF,
                    show: () => true,
                    condition: (right) => true
                },
            ],
            filterPlaceholder: "请输入中介名称",
        }
    },
    methods: {
        editMediation(row){
            this.mediationOption('edit', row)
        },
        lookMediation(row){
            this.mediationOption('look', row)
        },
        /**
         * 新增or编辑弹窗
         */
        mediationOption(type, row){
            const vm = this;
            let layer = vm.$dgLayer({
                title : type === 'add' ? '新增服务中介' : (type === 'edit' ? '编辑服务中介' : '查看服务中介'),
                content : require("./dialog/mediationOption.vue"),
                maxmin:false,
                move:false,
                area : ["800px" , "600px"],
                on : {
                    submit(){
                    }
                }
            })
        },

        changePage(inx, size) {
            // const vm = this, {mediationServices , mediationMock, settings} = this;
            // vm.tableData = [];
            // settings.loading = true;
            // let services = vm.getServices(mediationServices , mediationMock);
            // services.getMediationList({
            //     keyWord: vm.filterText,
            //     pageSize : size,
            //     pageIndex : inx,
            //     settings
            // }).then(res => {
            //     vm.tableData = res || [];
            //     vm.$refs.table.total = res.totalCount;
            //     vm.triggerEvent();
            // });
        },
    },
    created(){
        this.changePage();
    }
}
