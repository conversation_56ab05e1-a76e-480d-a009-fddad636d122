import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../service-mixins/service-mixins";
import {mapGetters,mapState} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {createComponent} from '@/api/commonMethods/createComponent';
import helpDocument from "@/components/signed-components/helpDocument/helpDocument";

export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common, listMixins, coustTableH],
    computed: {
        ...mapGetters(["userRight","userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights, vm.treeNode.treeType !== "sourceDataSetTree")) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {

                if (me.condition(vm.rights, vm.treeNode.treeType !== "sourceDataSetTree")) {
                    opts.push(me);
                }

            });
            return opts;
        },
        isCatalog(){
            return this.treeType === "catalogue";
        },
        ...mapState({
            shareId: (state) => state.plans.shareId,
        }),
        // 是其他用户
        isOtherUser(){
            return this.treeNode.currentUserId !== this.userInfo?.id;
        },
        tHeadData(){
            const {isShare} = this;
            const operate = (isShare || this.isOtherUser) ? [] : [
                {
                    prop: 'operate',
                    label: '操作',
                    width: 250,
                    align: "center",
                    resizable: false
                }
            ];
            return [
                {
                    prop: "name",
                    label: "数据集名称",
                    minWidth: 180,
                    align: "left"
                },
                {
                    prop: "belong_type",
                    label: "数据集类型",
                    minWidth: 160,
                    align: "center"
                },
                {
                    prop: "path",
                    label: "所属目录",
                    minWidth: 200,
                    align: "left",
                    resizable: false
                },
                {
                    prop: 'user',
                    label: '创建人',
                    minWidth: 120,
                    align: "center",
                    resizable: false
                },
                {
                    prop: 'operate_time',
                    label: isShare ? '分享时间' : '更新时间',
                    width: 200,
                    align: "center",
                    resizable: false
                },
                ...operate,
            ]
        }
    },
    props: {
        treeAllInfo: Array,
        active: String,
        renewListLoad: Function,
    },
    watch: {
        "settings.loading": {
            handler(val) {
                this.renewListLoad(val);
            },
            deep: true
        }
    },
    data() {
        return {
            batchAddTxt: '批量添加',
            batchShareTxt: "批量分享",
            myShareTxt: "我的分享",
            sqlRowData: {},
            showSqlPanel: false,
            buttonTxt: ["添加SQL数据集"],
            rightSql: $right["dataSetOperationCreateSql"],//即席SQL分析
            isShare: false,
            selectType: "",
            volumeLicensing: $right['dataSetOperationSetBatchSharing'],//批量分享权限
            addDataSetObj: $right['dataSetOperationAccreditLogicDataObj'],//添加数据集权限
            actions: [
                {
                    icon: "icon-database1",
                    title: "源数据集",
                    desc: "将需要分析的数据表创建为数据集",
                    color: "#3AA3FF",
                    value: "data-table"
                },
                {
                    icon: "icon-database-a",
                    title: "自助数据集",
                    desc: "将多张数据表关联为一张数据表，满足以后数据分析",
                    color: "#FAAD14",
                    value: "data-set"
                },
                // {
                //     icon: "icon-excel",
                //     title: "Excel",
                //     desc: "通过上传Excel文件生成数据集",
                //     color: "#16CF91",
                //     value: "excel"
                // },
                {
                    icon: "icon-SQL",
                    title: "SQL数据集",
                    desc: "通过自定义SQL语句生成数据集",
                    color: "#494DCB",
                    value: "sql"
                }
            ],
            inputValueTable: "",
            tableData: [],
            selectData: [
                {label: '所有数据集类型', value: ''},
                {label: '源数据集', value: 'PHYSICAL'},
                {label: '自助数据集', value: 'SELF_HELP'},
                {label: 'SQL数据集', value: 'QUICK_SQL'},
            ],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe6c0;",
                    name: "编辑",
                    show: () => true,
                    clickFn: this.editContact,
                    condition: (right, type) => type ? right.indexOf($right["dataSetOperationDataColumn"]) > -1 :
                        false
                },
                {
                    icon: "&#xe6b5;",
                    name: "分享",
                    show: () => true,
                    clickFn: this.authShow,
                    condition: (right, type) => true
                    //type ? right.indexOf($right["dataSetOperationSetSingleShare"]) > -1  :false
                },
                {
                    icon: "&#xe6b5;",
                    name: "生成API",
                    show: () => true,
                    clickFn: this.createApi,
                    condition: (right, type) => right.indexOf($right["dataSetOperationCreateAPI"]) > -1
                },
                {
                    name: "移动到",
                    clickFn: this.moveTo,
                    show: () => true,
                    condition: (right, type) => type ? right.indexOf($right["dataSetOperationMoveLogicDataSet"]) > -1 :
                        false
                },
                {
                    name: "另存为",
                    clickFn: this.saveOther,
                    show: () => true,
                    condition: (right, type) => type ? right.indexOf($right["dataSetOperationSaveAs"]) > -1 : false
                },
                {
                    icon: "&#xe65f;",
                    name: "删除",
                    show: () => true,
                    clickFn: this.deleteDataset,
                    condition: (right, type) => right.indexOf($right["dataSetOperationDeleteDataSet"]) > -1 && type
                },
                // {
                //     icon: "&#xe790;",
                //     name: "预览",
                //     clickFn: this.preview,
                //     show: () => true,
                //     condition: (right, type) => right.indexOf($right["dataSetOperationPreviewData"]) > -1 && !type
                // },

                // {
                //     icon : "&#xe788;" ,
                //     tip : "同步数据集结构" ,
                //     clickFn : this.syncDataset ,
                //     condition : (right, type) => !type
                // },

            ],
            nodeId: "",
            addDataSourceTxt: "添加数据集",
            treeNode: {},
            typeMap: {
                "POSTGRESQL": "Postgresql",
                "GREENPLUM": "Greenplum",
                "ZOOKEEPER": "zookeeper",
                "ELASTICSEARCH": "Elasticsearch",
                "ORACLE": "Oracle",
                "HWMPP": "Libra",
                "HBASE": "hbase",
                "HIVE": "hive",
                "MYSQL": "Mysql"
            },
            treeType:"",// 数据集树类型
        }
    },
    methods: {
        /**
         * 获取数据集 目录位置，来源分类
         * @param row
         * @returns {*}
         */
        getDatasetPos(row){
            const vm = this, {$services, settings} = vm;
            settings.loading = true;
            let pos = new Map();
            return $services("dataReady").getDatasetTypeAPos(row.id,settings).then(res => {
                let result = res.data.data;
                if(res.data.status === 0) {
                    for(let k in result) {
                        let v = result[k];
                        pos.set(v,k);
                    }
                }
                return pos;
            })
        },
        linkToShare(){
            this.$router.push({
                name: "share",
                params: {
                    type: "dataSet",
                    tab: "ShareTableList",
                }
            })
        },
        /**
         * 批量添加数据集
         */
        batchAddFn() {
            const vm = this;
            let layer = this.$dgLayer({
                content: require("../dialog/batch-add-dataset/batchAddSource"),
                title: '批量添加',
                move: false,
                maxmin: false,
                area: ['1000px', '750px'],
                props : {
                    dirId: vm.nodeId,
                },
                on : {
                    success(){
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
                success: function (layero, index) {
                    createComponent(helpDocument , { code : 'dataSetOperationAddBatchLogicObj'} , layero.find(".layui-layer-title")[0])
                },
            })
        },
        /**
         * 新建、编辑 即席sql
         */
        sqlAnalyse(row,pos=new Map()) {
            // this.$refs.sql_form.show(this.nodeId);
            const vm = this, {treeType, nodeId} = this;
            let url = require("@views/dataSpace/dataReady/dialog/sql-analyse");
            const dirId = pos.has("fromCatalog") ? pos.get("fromCatalog") : treeType === "catalogue" ? nodeId : "", //treeType === "catalogue"? nodeId : "",
                dsTypeId = pos.has("fromDsType") ? pos.get("fromDsType") : "";// treeType === "datasource" && row ? row.parentid : "";
            let layer = this.$dgLayer({
                content: url,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    dirId,
                    dsTypeId,
                },
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
                success: function () {
                    layer.$children[0].addTab(row);
                }
            });

        },
        initTable(data, treeType) {
            this.nodeId = data.id;
            this.treeNode = data;
            this.isShare = false;
            this.treeType = treeType;
            // 来自分享
            if (data.id === this.shareId || data.msg && JSON.parse(data.msg).isShare) this.isShare = true;
            this.changePage(1);
        },
        setDirType(value, id, node, type,treeType) {
            const vm = this;
            vm.treeNode = node
            vm.treeType = treeType;
            vm.isShare = false;
            // 来自分享
            if (id === vm.shareId || node.msg && JSON.parse(node.msg).isShare) vm.isShare = true;
            vm.nodeId = id;
            vm.treeNode.treeType = type;
            vm.changePage(1);
        },
        changePage(index) {
            if (this.treeNode.treeType === "sourceDataSetTree") {
                this.sourcePageChange(index);
                //this.removeHeadRow();
            } else {
                //this.addHeadRow();
                this.dataSetPageChange(index);
            }
        },
        //我的数据表格数据
        dataSetPageChange(index) {
            this.tableData = [];
            const vm = this, {dataReadyServices, dataReadyMock, settings,isShare, treeType} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let params = {
                id: vm.nodeId ? vm.nodeId : null,
                name: vm.inputValueTable,
                page: index,
                dbType: vm.selectType,
                pageSize: vm.paginationProps.pageSize,
                isShare: treeType === "datasource" && isShare , // 按数据来源类型 且是来自分享传 true
                operatorId: vm.treeNode.currentUserId
            };
            settings.loading = true;
            vm.tableData = [];
            services.getDataSetPage(params, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result) {
                        vm.tableData = result.dataList.map(item => {
                            if (item.name === null || item.name === "") {
                                item.name = item.code;
                            }
                            return item;
                        });
                        vm.total = result.totalCount;
                    } else {
                        vm.total = 0;
                    }
                }
            })
        },
        //业务主题表格数据
        sourcePageChange(index) {
            const vm = this;
            const {dataReadyServices, dataReadyMock, settings} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            vm.paginationProps.currentPage = index;
            let params = {
                dwDbId: vm.nodeId,
                name: vm.inputValueTable,
                pageIndex: index,
                dbType: vm.selectType,
                pageSize: vm.paginationProps.pageSize,
                sortField: "name",
                sortOrder: "desc",
            };
            settings.loading = true;
            vm.tableData = [];
            services.queryDatasetBaseTableC(params, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result) {
                        vm.tableData = result.dataList.map(item => {
                            if (item.name === null || item.name === "") {
                                item.name = item.code;
                            }
                            return item;
                        });
                        vm.total = result.totalCount;
                    } else {
                        vm.total = 0;
                    }
                }
                setTimeout(() => {
                    settings.loading = false;
                }, 100)
            }).catch(err => {
                settings.loading = false;
            });
        },
        //删除
        deleteDataset(row) {
            if (this.treeNode.treeType === "sourceDataSetTree") {
                return
            }
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            services.checkDataSetUserInVisual(row.id).then(res => {
                if (res.data.status === 0) {
                    let msg = "", result = res.data.data;
                    if (result === "success") {
                        msg = `删除可能会导致其他数据集及其关联的API服务无法使用，是否确认删除 "${row.name}" 数据集？`;
                    } else {
                        msg = result;
                    }
                    vm.confirm('删除', msg, () => {
                        services.deleteDataSet(row.id).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("删除成功");
                                vm.changePage(1);
                            }
                        })
                    })
                }
            })
        },
        addNewModel(row) {
            this.$refs.btnPop.doClose();
            if (!row) return;
            if (row.value === "data-table" || row.value === 'data-set') {
                this.handleNewModel(row);
            } else if (row.value === "sql") {
                this.sqlAnalyse();
            }
        },
        handleNewModel(row) {
            if (!row) return
            const vm = this, {treeType, nodeId} = vm;
            let url = "";
            if (row.value === "data-table") {
                url = require("@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/dataTable/index.vue");
            } else if (row.value === "data-set") {
                url = require("@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/selfDataSet/index.vue");
            }
            const dirId = treeType === "catalogue"? nodeId : "",
                dsTypeId = "";

            let layer = this.$dgLayer({
                content: url,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    dirId,
                    dsTypeId,
                    type: row.value,
                    add: true,
                    isShowDW: vm.rights.indexOf($right["dataSetOperationDataWarehouse"]) > -1,
                },
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab({}, false, vm.nodeId);
        },

        //表格编辑
        async editContact(row) {
            if (!row) return
            let pos = await this.getDatasetPos(row);
            const vm = this;
            if (row.belong_type === 'QUICK_SQL') {
                vm.sqlAnalyse(row,pos);
                return;
            }
            let url = "";
            if (row.belong_type === "PHYSICAL") {
                url = require("@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/dataTable/index.vue");
            } else if (row.belong_type === "SELF_HELP") {
                url = require("@/projects/DataCenter/views/dataSpace/dataReady/dialog/addDataSource/selfDataSet/index.vue");
            }
            const dirId = pos.get("fromCatalog"),//treeType === "catalogue"? row.parentid : "",
                dsTypeId = pos.get("fromDsType");//treeType === "datasource" ? row.parentid : "";
            let layer = this.$dgLayer({
                content: url,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    dirId,
                    dsTypeId,
                    type: row.value,
                    add: false,
                    dataSetId: row.id,
                    leftTreeId: row.owner_id,
                    isShowDW: vm.rights.indexOf($right["dataSetOperationDataWarehouse"]) > -1,
                },
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab(row, false, vm.nodeId);
        },

        //预览
        preview(row) {
            this.$emit("previewDialog", row);
        },
        //同步数据结构
        syncDataset(row) {
            this.$emit("syncDataset", row);
        },
        authShow(row) {
            this.$emit("authShow", row, '');
        },
        batchFn() {//批量处理batchData
            this.$emit("batchData", this.treeNode.id);
        },
        reNewTable() {
            this.$refs.readyCenterTable.changePage(1);
        },
        update() {
            this.changePage(this.currentPage);
        },

        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
        //移动到
        async moveTo(row) {
            let pos = await this.getDatasetPos(row);
            this.$emit('moveTo', row,pos);
        },
        moveUpdate(dataSetID, treeNodeId,node,dsTypeId,beforePos) {
            const vm = this, {dataReadyServices, dataReadyMock, settings} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            let data = {
                dataSetID: dataSetID,
                treeNodeId:  beforePos.has("fromCatalog")? beforePos.get("fromCatalog") : "", // 修改前 目录 id
                dsTreeNodeId: beforePos.has("fromDsType")? beforePos.get("fromDsType") : "", // 修改前 分类 id
                dTreeNodeId: treeNodeId, // 修改后 目录 id
                dDsTreeNodeId: dsTypeId, // 修改后 分类 id
            }
            settings.loading = true;
            services.moveLogicDataSet(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success('移动成功！');
                    vm.$emit('closeMove', treeNodeId,dsTypeId);
                }
            })
        },
        detailShow(row) {
            this.$emit('detailShow', row, (this.isShare || this.isOtherUser))
        },
        //另存为
        async saveOther(row) {
            let pos = await this.getDatasetPos(row);
            this.$emit('saveOtherShow', row, pos);
        },
        saveOtherSure(name, treeId, saveOther, row, level,dsTypeId) {
            const vm = this, {dataReadyServices, dataReadyMock, settings} = this;
            settings.loading = true;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            services.createLogicDataSet({
                id:treeId,
                name,
                dsTypeId,
            }).then(res => {
                if (res.data.status === 0) {
                    let new_datasetId = res.data.data.id;
                    let params = {
                        name: name,
                        logicDataSetId: row.id,
                        metaDataObjIds: [row.id],
                        saveAsId: new_datasetId,
                    };
                    services.saveAs(params, settings).then(res => {
                        if (res.data.status === 0) {
                            vm.$message.success("保存成功");
                            this.$emit('saveOtherClose');
                            vm.changePage(1);
                        }
                    })
                } else {
                    settings.loading = false;
                }
            })
        },
        createApi(row) {
            this.$emit('createAPI', row);
        }
    },
}
