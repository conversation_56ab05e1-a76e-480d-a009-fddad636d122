<!-- 
 * 拖拽排序
    slot : listItem 自定义 拖拽项
    props : data 拖拽项 数据
    mode : 布局  横/竖 //horizontal ,vertical
    ghostClass : 拖拽时的 占位样式 可以使用默认的，自定义需要在父组件写对应的样式

    data 绑定的数据数组 需要要有唯一属性 id
    使用 插槽 listItem slot-scope="{item , $index , id}" :key="id"
    需要绑定 key 等于 id ,item 是 单个项的数据 $index 索引
    idKey 唯一的属性名 ，默认是 "id" ，如果是 value 没有id属性，则可设置 idKey="value"
 -->
<template>
    <draggable :list="data" class="sort-list"
               :class="mode === 'vertical'? 'sort-list_vertical' : 'sort-list_horizontal'"
               :ghost-class="ghostClass">
        <template v-for="(item , i) in data">
            <slot name="listItem" :item="item" :id="item[idKey]" :$index="i">
                <div class="sort-list_item" :key="item[idKey]">{{ item.label }}</div>
            </slot>
        </template>
    </draggable>
</template>

<script>
import draggable from "vuedraggable";
export default {
    name : "CeSortable",
    components: {
        draggable
    },
    props : {
        data : {
            type : Array,
            default : () => []
        },
        mode: {
            type: String,
            default: "horizontal",//horizontal ,vertical
        },
        ghostClass : {
            type : String ,
            default : "sort-list_ghost"
        },
        idKey : {
            type : String ,
            default : "id"
        }
    },

}
</script>

<style lang="less" scoped>
.sort-list {
    display: flex;
    flex-wrap:wrap;
    &_vertical {
        flex-direction: column;
    }
    &_horizontal {
        flex-direction: row;
    }
    &_item {
        background: #e1fae3;
        padding:0 14px;
        margin:6px;
        line-height: 30px;
        max-width:200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: move;
    }

}
</style>
<style lang="less">
.sort-list {
    &_ghost {
        background: none;
        border: 1px dashed #aaa;
        > * {
            opacity: .1;
        }
    }
}


</style>

