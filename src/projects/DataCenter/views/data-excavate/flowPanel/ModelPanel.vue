<template>
    <div class="modelPanel" ref="modelPanel" :class="{'modelMl':leftShow }" :style="{'margin-right': marginRight }">
        <FlowPanelEt @setInstanceList="setInstanceList" @slideAttr="slideRight" @hideSource="closeSource"
                     v-bind="$props">
            <FlowButtons slot="toolbar" slot-scope="scope" :isFocus="tabId === focusTab" :isTemplate="isTemplate" :lines="scope.lines" :status="status" :rowData="rowData" ref="flowButtons"/>
        </FlowPanelEt>

    </div>
</template>

<script>
    import FlowPanelEt from './FlowPanelEt'
    import {globalBus} from '@/api/globalBus'
    import FlowButtons from './FlowButtons'
    import $ from 'jquery'

    export default {
        name: "ModelPanel",
        props: {
            rowData: Object,
            tabId: String ,
            isTemplate :Boolean,
            modelParams : Object,
            focusTab : String
        },
        computed: {
            status() {
                return this.rowData.statusCode ? this.rowData.statusCode : 0;
            }
        },
        data() {
            return {
                leftShow: false,
                rightShow: false,
                marginRight: '10px'

            }
        },
        methods: {
            setInstanceList(value) {
                this.$refs.flowButtons.setInstanceList(value);
            },
            slideLeft() {
                globalBus.$on('slideS', (modelShow) => {
                    if ($(this.$refs.modelPanel).is(':visible')) this.leftShow = !modelShow;
                });
            },
            slideRight(attrWidth, tabId) {
                // this.rightShow = attrShow;
                if (this.tabId !== tabId) return;
                this.marginRight = attrWidth;
            },
            closeAttr() {
                globalBus.$on('hideAttr', (hide) => {
                    if ($(this.$refs.modelPanel).is(':visible')) {
                        this.marginRight = '10px';
                        //this.rightShow = hide;
                        // this.leftShow = !hide;
                    }
                })
            },
            closeSource(hide , tabId) {
                if (this.tabId !== tabId) return;
                this.leftShow = hide;
            }
        },
        components: {
            FlowPanelEt,
            FlowButtons
        },
        created() {
            this.slideLeft();
            this.closeAttr();
        }
    }
</script>

<style scoped>
    .modelPanel {
        height: calc(100% - 1.5rem - 88px);
        margin: 5px 10px 5px 33px;
        box-sizing: border-box;
        border: 1px solid #ddd;
        transition: 500ms;
        position: relative;
        background-image: linear-gradient(90deg, rgba(10, 15, 15, 0.05) 10%, rgba(0, 0, 0, 0) 10%), linear-gradient(rgba(10, 15, 15, 0.05) 10%, rgba(0, 0, 0, 0) 10%);
        background-size: 10px 10px;
        background-color: #f6faff;
    }



    .modelMl {
        margin-left: 243px;
    }

    .modelMr {
        margin-right: 400px;
    }
</style>
