import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../../service-mixins/service-mixins";
import conditionPage from "./condition-page"
import {mapGetters} from "vuex";

export default {
    name: "filterContion" ,
    mixins : [ dialog , commonMixins , servicesMixins],
    computed : {
        ...mapGetters(["userInfo"])
    },
    data() {
        return {
            title : "设置过滤条件" ,
            width : "1000px" ,
            datasetId :"" ,
            canEdit : true ,
            nodeData :{}
        }
    },
    components :{conditionPage},
    methods : {
        show(datasetId){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.canEdit = true;
            vm.datasetId = datasetId;
            vm.title = "设置过滤条件";
            vm.$nextTick(()=>{
                vm.$refs.condition.init();
            })
        },
        showHistory(datasetId , data){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.datasetId = datasetId;
            vm.canEdit = data.canEdit;
            vm.nodeData = data;
            vm.title = "设置过滤条件(轨迹步骤)";
            vm.$nextTick(()=>{
                vm.$refs.condition.init(data);
            })
        },
        submit(){
            const vm = this , {datasetServices , datasetMock ,settings ,datasetId ,userInfo ,nodeData } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let res = vm.$refs.condition.validate();
            if(!res) return;
            let params = {
                condition : JSON.stringify( vm.setParamsVo()) ,
                dataSetId : datasetId ,
                userId : userInfo.id
            };
            settings.loading = true;
            if(nodeData.step === true){
                let s_params = {
                    dataSetId: datasetId ,
                    stepId : nodeData.stepId ,
                    filterConditionStep : params
                };
                services.editDataStep(s_params ,settings).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("步骤编辑成功");
                        vm.$emit("preview");
                        vm.visible = false;
                    }
                })
            }else {
                services.filterCondition(params ,settings).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("已过滤数据");
                        vm.$emit("preview");
                        vm.visible = false;
                    }
                });
            }

        },
        setRow(item){
            let row = {
                id : "",
                fieldName:'',
                fieldCode:'',
                operator:'',
                startValue:'',
                startOperator:'',
                endVale:'',
                endOperator:'',
                inputValue:'' ,
                format : item.format
            };
            row.fieldName = item.name;
            row.fieldCode = item.field;
            row.operator = item.logic;
            row.id = item.id;
            switch (item.format) {
                case 'TEXT' :
                    row.inputValue = item.inputVal;
                    break;
                case 'NUMBER':
                    row.inputValue = item.spaceVal;
                    row.startOperator = item.select1;
                    row.endOperator = item.select2;
                    row.startValue = item.spaceVal1;
                    row.endValue = item.spaceVal2;
                    break;
                default:
                    row.inputValue = item.time;
                    row.startValue = item.timeSpace[0] ?  item.timeSpace[0] : "";
                    row.endValue = item.timeSpace[1] ? item.timeSpace[1] : "";
                    break;
            }
            return row;

        },
        setParamsVo(){
            const vm = this;
            let condition = vm.$refs.condition.filter_condition , result = [];
            condition.forEach(con => {
                let fields = [];
                con.inside.forEach(ins => {
                    fields.push(vm.setRow(ins));
                });
                result.push({
                    groupType:con.relation,
                    filterFieldInfo:fields
                });
            });
            return result;
        }
    }
}