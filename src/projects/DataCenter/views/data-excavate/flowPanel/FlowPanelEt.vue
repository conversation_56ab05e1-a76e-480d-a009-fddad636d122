<template>
    <div class="flow_scroll" @dragover="allowDrop" @drop="drop" v-loading="modelParams.loading">
        <slot name="toolbar" :lines="data.lineList"></slot>
        <div v-if="easyFlowVisible" class="flowPanelEt" ref="parent">
            <ModelNode
                v-for="node in data.nodeList"
                :key="node.id"
                v-if="node.show"
                :id="node.id"
                :node="node"
                @deleteNode="deleteNode"
                @changeNodeSite="changeNodeSite"
                @editNode="editNode"
                @editAttr="editAttr"
                :ref="node.id"
                :class="{'ce-plug_trans': transPlugins.indexOf(node.keyWord) > -1, 'ce-plug_output': outputPlugins.indexOf(node.keyWord) > -1}"
            />

            <NodeForm v-if="nodeFormVisible" ref="nodeForm" @change="changeVal"/>
        </div>
    </div>

</template>

<script>
/*
* 建模，挖掘插件图标相关组件 暂放到公用 components/flowPanel/flow-plugin目录下，原组件页面还暂留，为了后期如果建模和挖掘需要有不同的功能备用
* 当前使用公用，修改可只调整公用组件
* */
import {jsPlumb} from 'jsplumb'
import NodeForm from '@/components/flowPanel/flow/NodeForm'
import ModelNode from '@/components/flowPanel/flow-plugin/ModelNode.vue'
import {globalBus} from '@/api/globalBus';
import plugIcons from '@/assets/data/plug-icons.json'
import {common} from "@/api/commonMethods/common"
import {flowMixins} from "@/api/commonMethods/flow-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins";
import $ from "jquery";

export default {
    name: "FlowPanelEt",
    mixins: [commonMixins, servicesMixins, flowMixins, common],
    components: {
        ModelNode,
        NodeForm,
    },
    props: {
        rowData: Object,
        tabId: String,
        modelParams : Object
    },
    data() {
        return {
            canDrop: false,
            plugNode: null,
            resData: {},
            loading: true,
            transPlugins: ['serviceInputMeta', 'collisionPlugin', 'rowDenormaliserMeta', 'fieldFilteringMeta', 'labelMeta', 'scriptMeta', 'personCreatorMeta', 'dlwzMapperMeta', 'jwqMapperMeta', 'subtractByKeyPlugin', 'leftOrRightJoinPlugin', 'fullJoinPlugin', 'unionJoinPlugin', 'innerJoinPlugin', 'serviceOrganization', 'windowStreaming', 'reducePlugin', 'luceneBuilderMeta', 'jsonParsingContent', 'samplingAndShuntingPlugin', 'startTaskPlugin', 'dataSortPlugin', 'expressionExcuter', "fileUpload", "zipperTablePlugin", "conditionFilterPlugin", "dateZipperTablePlugin", "numberZipperTablePlugin"],
            outputPlugins: ['kVOutput', 'fullTextOutput', 'standardSqlOutput', 'scatterChartsPlugin', 'radarChartsPlugin', 'pieChartsPlugin', 'barChartsPlugin', 'lineChartsPlugin', 'csvOutput', 'gbaseTableOutput', 'gpOut'],
            deleteNodeWithLine: false,//删除的节点带连线
        }
    },
    methods: {
        editdialogAttr(node) {
            this.$emit('slideAttr', true);
            globalBus.$emit('slideAttr', node, true);
            globalBus.$emit('hideSource', true);
            this.$emit('hideSource', false);
        },
        attrWidth(node) {
            let nodes = ['散点图', '甘特图', '雷达图', '饼图', '柱状图', '折线图'], width = '460px';
            for (let i = 0; i < nodes.length; i++) {
                if (node.name.indexOf(nodes[i]) > -1) {
                    return width = '370px';
                }
            }
            return width;
        },
        editPlugAttr(node) {
            let width = this.attrWidth(node);
            this.$emit('slideAttr', width, this.tabId);
            globalBus.$emit('slideAttr', node, true, width, this.tabId);
            globalBus.$emit('hideSource', true, this.tabId);
            this.$emit('hideSource', false, this.tabId);
        },
        isAllow() {
            this.canDrop = false;
        },
        setPlugNode(plug) {
            this.plugNode = plug;
            this.canDrop = true;
        },
        bindFn() {
            globalBus.$on('dragend', this.isAllow);
            globalBus.$on('plugNode', this.setPlugNode);
        },
        unbindFn() {
            globalBus.$off('plugNode', this.setPlugNode);
            globalBus.$off('dragend', this.isAllow);
        },
        drop(e) {
            this.addNode(e, this.plugNode);
        },
        /*以下方法仅 于显示静态数据*/
        changeVal(node) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            this.data.nodeList.filter(item => {
                if (item.id === node.id) {
                    services.updateTransStepName(node.id, node.name)
                        .then(res => {
                            if (res.data.status === 0) {
                                item.name = node.name;
                                this.$message.success("修改成功！")
                            }
                        })
                }
            })
        },
        allowDrop(e) {
            if (this.canDrop) {
                e.preventDefault();
            }
        },
        //jsPlumb初始化
        jsPlumbInit() {
            const _this = this;
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            this.jsPlumb.ready(function () {
                // 导入默认配置
                _this.jsPlumb.importDefaults(_this.jsplumbSetting);
                // 会使整个jsPlumb立即重绘。
                _this.jsPlumb.setSuspendDrawing(false, true);
                // 初始化节点
                _this.loadEasyFlow();
                // 单点击了连接线,
                _this.jsPlumb.bind('click', function (conn, originalEvent) {
                    _this.confirm('提示', '确定删除所点击的线吗?', () => {
                        vm.deleteNodeWithLine = false;
                        _this.jsPlumb.deleteConnection(conn);
                    });
                });
                // 连线
                _this.jsPlumb.bind("connection", function (evt) {
                    let fromName = evt.source.innerText;
                    let fromId = evt.source.id;
                    let toName = evt.target.innerText;
                    let toId = evt.target.id;
                    services.addTransHop(fromName, toName, fromId, toId, _this.rowData.transId).then(result => {
                        if (result.data.status === 0) {
                            if (_this.loadEasyFlowFinish) {
                                _this.data.lineList.push({
                                    from: fromId,
                                    to: toId
                                })
                            }
                        }
                    })
                });

                // 删除连线
                _this.jsPlumb.bind("connectionDetached", function (evt) {
                    _this.deleteLine(evt.sourceId, evt.targetId)
                });

                // 改变线的连接节点
                _this.jsPlumb.bind("connectionMoved", function (evt) {
                    // console.log('connectionMoved', evt)
                    _this.changeLine(evt.originalSourceId, evt.originalTargetId)
                });
                // contextmenu
                _this.jsPlumb.bind("contextmenu", function (evt) {
                    console.log('contextmenu', evt)
                });
                // beforeDrop
                _this.jsPlumb.bind("beforeDrop", function (evt) {
                    // console.log('beforeDrop', evt)
                    let from = evt.sourceId;
                    let to = evt.targetId;
                    if (from === to) {
                        _this.$message.error('不能连接自己');
                        return false;
                    }
                    let chartCodes = ['scatterChartsPlugin', 'radarChartsPlugin', 'pieChartsPlugin', 'barChartsPlugin', 'lineChartsPlugin', 'fileOutputMeta', 'fullTextOutput', 'standardSqlOutput',],//输出类插件 不输出其他插件
                        otherCode = ['dataSortPlugin'],
                        outputCodes = ['fileInput', 'kVOutput', 'fullTextOutput', 'standardSqlOutput', 'csvOutput', 'gbaseTableOutput', 'gpOut',];
                    for (let i = 0; i < _this.data.lineList.length; i++) {
                        var line = _this.data.lineList[i];
                        if (line.from === from && line.to === to) {
                            _this.$message.error('不能重复连线');
                            return false;
                        }
                        if (line.from === to && line.to === from) {
                            _this.$message.error('不能回环哦');
                            return false;
                        }
                        for (let j in _this.data.nodeList) {
                            let code = _this.data.nodeList[j].keyWord;
                            if (_this.data.nodeList[j].id === to && [...outputCodes, ...otherCode].indexOf(code) > -1 && line.to === to) {
                                let pluginN = code === 'dataSortPlugin' ? '数据排序' : "输出";
                                _this.$message.error(`${pluginN}插件只能有一个输入!`);
                                return false;
                            }
                        }
                    }
                    for (let k in _this.data.nodeList) {
                        let code = _this.data.nodeList[k].keyWord;
                        if (_this.data.nodeList[k].id === from && chartCodes.indexOf(code) > -1) {
                            let pluginN = _this.data.nodeList[k].code.replace(/_.*/g, "");
                            vm.$message.error(`${pluginN}插件不能输出其他插件!`);
                            return false;
                        }
                    }
                    return true;
                });

                // beforeDetach
                _this.jsPlumb.bind("beforeDetach", async function (evt) {
                    //console.log('beforeDetach', evt)
                })
            })
        },
        checkOneRoad(from, to) {
            let lineArr = [];
            this.data.lineList.forEach(l => {
                if (lineArr.indexOf(l.from) === -1) {
                    lineArr.push(l.from);
                }
                if (lineArr.indexOf(l.to) === -1) {
                    lineArr.push(l.to);
                }
            });
            return lineArr.indexOf(to) === -1 && lineArr.indexOf(from) === -1;
        },
        checkParallelRod(to, lines) {
            const vm = this;
            let line = vm.data.lineList.filter(l => {
                return l.from === to || l.to === to;
            });
            if (line.length === 0 || lines.length === 0) return false;
            let lineArr = [];
            lines.forEach(l => {
                if (lineArr.indexOf(l.from) === -1 && line[0].from !== l.from) {
                    lineArr.push(l.from);
                }
                if (lineArr.indexOf(l.to) === -1 && line[0].to !== l.to) {
                    lineArr.push(l.to);
                }
            });
            return lineArr.indexOf(line[0].to) === -1 && lineArr.indexOf(line[0].from) === -1;
        },
        removeLineFn(from, to) {
            return this.data.lineList.filter(function (line) {
                return !(line.from === from && line.to === to);
            });
        },
        // 删除线
        async deleteLine(from, to) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            if (vm.deleteNodeWithLine) {
                return;
            }
            services.deleteTransHop(from, to, this.rowData.transId).then(result => {
                if (result.data.status === 0) {
                    vm.data.lineList = vm.data.lineList.filter(line => !(line.from === from && line.to === to));
                    vm.$message.success("步骤连线删除成功!");
                }
            })
        },
        // 改变连线
        changeLine(oldFrom, oldTo) {
            this.deleteLine(oldFrom, oldTo);
        },
        // 改变节点的位置
        changeNodeSite(data) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let x = data.left.substring(0, data.left.length - 2), y = data.top.substring(0, data.top.length - 2);
            services.moveTransStep(x, y, data.nodeId).then(res => {
                if (res.data.status === 0) {
                    for (var i = 0; i < this.data.nodeList.length; i++) {
                        let node = this.data.nodeList[i];
                        if (node.id === data.nodeId) {
                            node.left = data.left;
                            node.top = data.top;
                        }
                    }
                }
            })
        },
        // 添加新的节点
        addNode(evt, nodeMenu) {
            const $this = this;
            let width = 42;
            const index = this.index++;
            let x, y;
            if (evt.target.className === 'flowPanelEt') {
                x = evt.offsetX - width + this.$refs.parent.scrollLeft;
                y = evt.offsetY + this.$refs.parent.scrollTop - 25;
            } else {
                x = $(evt.target).parents('.modelNode').position().left + this.$refs.parent.scrollLeft + 10;
                y = $(evt.target).parents('.modelNode').position().top + this.$refs.parent.scrollTop + 10;
            }

            let name = this.newNameBeing(nodeMenu.label + "_" + index);
            let parentTransId = this.rowData.transId;
            //创建步骤
            this.addPluginTranStep(nodeMenu, name, x, y, parentTransId);
        },
        //名称是否已存在
        newNameBeing(name) {
            const vm = this;
            let sameN = vm.data.nodeList.filter(node => node.name === name);
            if (sameN.length) {
                return name + '-1';
            } else {
                return name;
            }
        },
        //添加插件
        addPluginTranStep(nodeMenu, name, x, y, parentTransId) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let pluginKeyWord = "", dataType = "";
            let tableID = "";
            /*if (nodeMenu.keyWord === undefined) {
                let type = nodeMenu.data.dbType;
                if (type === 'ElasticSearchIndex') {
                    nodeMenu.data.keyWord = 'fullTextInput';
                } else if (type === "HbaseHtable") {
                        nodeMenu.data.keyWord = 'kVInput';
                } else if (type === "RdbDataObj") {
                    nodeMenu.data.keyWord = "standardSqlInput";
                } else {
                    nodeMenu.data.keyWord = "dataSetPlugin";
                }
                //console.log("dbType : " + type + ", plugin : " + nodeMenu.data.keyWord);
                //数据集需要初始化数据集的数据
                dataType = nodeMenu.data.type;//用于前端显示插件的type
                pluginKeyWord = nodeMenu.data.keyWord;
            } else {
                dataType = nodeMenu.type;
                pluginKeyWord = nodeMenu.keyWord;
            }
            //跨库建模
            if (nodeMenu.data !== undefined && nodeMenu.data.parentId !== undefined) {
                tableID = nodeMenu.data.id;
            } else {
                tableID = nodeMenu.data == null ? "" : nodeMenu.data.classifierId
            }*/
            tableID = "";
            dataType = "";

            if (nodeMenu.type === "plug") {
                //转换插件
                pluginKeyWord = nodeMenu.keyWord;
                dataType = nodeMenu.type

            } else {
                let dt = nodeMenu.data.dbType;
                if (dt === "") {
                    this.$message.error("数据集信息不完整！");
                    return;
                }
                if (dt === "FILE") {
                    this.$message.error("暂不支持文件！");
                    return;
                }
                tableID = nodeMenu.data.id;
                dataType = dt;

                if (dt === 'ElasticSearch' || dt === 'ELASTICSEARCH' || dt === 'elasticsearch') {
                    pluginKeyWord = "fullTextInput";
                } else if (dt === 'Hbase' || dt === 'HBASE' || dt === 'hbase') {
                    pluginKeyWord = "kVInput";
                } else {
                    pluginKeyWord = "standardSqlInput";
                }
            }

            services.addPluginTranStep(pluginKeyWord, name, x, y, parentTransId).then(result => {
                let nodeId = result.data.data;
                if (result.data.status === 0) {
                    //判断拉取的是插件还是数据集
                    //console.log("插件信息", nodeMenu);
                    let canLink = pluginKeyWord !== 'startTaskPlugin';//限启动方案不能连线
                    let node = {
                        left: x + 'px',
                        top: y + 'px',
                        id: nodeId,
                        name: name,
                        code: name,
                        keyWord: pluginKeyWord,
                        tableId: tableID,
                        ico: nodeMenu.icon,
                        show: true,
                        type: dataType, //需要type 判断是数据资源还是组件
                        canLink: canLink,
                        checkClick: true
                    };
                    if (nodeMenu.type === undefined || nodeMenu.type === 'DS') {
                        let menu = {};
                        if (nodeMenu.type === 'DS') {
                            menu = nodeMenu;
                        } else {
                            menu = nodeMenu.data
                        }
                        node.checkClick = false;
                        vm.initPluginFn(menu, nodeId);
                    }
                    vm.data.nodeList.push(node);
                    vm.$nextTick(function () {
                        vm.jsPlumb.makeSource(nodeId, vm.jsplumbSourceOptions);
                        vm.jsPlumb.makeTarget(nodeId, vm.jsplumbTargetOptions);
                        vm.jsPlumb.draggable(nodeId, {
                            containment: 'parent'
                        })
                    })
                }
            })
        },
        insertAutoNewPlugin(vm, services, pluginVoId, pluginName, parentTransId, global_code) {
            services.insertAutoNewPlugin(pluginVoId, pluginName, parentTransId, global_code).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("数据集初始化完成!");
                } else {
                    vm.$message.error("初始化插件失败!");
                    vm.setSourceState(pluginVoId, 'checkError');
                }
                vm.setSourceState(pluginVoId, 'checkClick');
            })
        },
        initPluginReq(vm, services, params, path, transId) {
            services.initPlug(params, path).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("数据集初始化完成!");
                } else {
                    vm.$message.error("初始化插件失败!");
                    vm.setSourceState(transId, 'checkError');
                }
                vm.setSourceState(transId, 'checkClick');
            });
        },
        initPluginFn(nodeMenu, transId) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let params = null, path = "";

            if (nodeMenu.dbType === 'Hbase' || nodeMenu.dbType === 'HBASE' || nodeMenu.dbType === 'hbase') {
                path = "/kvInput/";
                params = {tranStepId: transId, dataObjId: nodeMenu.id};
            } else if (nodeMenu.dbType === 'ElasticSearch' || nodeMenu.dbType === 'ELASTICSEARCH' || nodeMenu.dbType === 'elasticsearch') {
                path = "/plugin/fulltext/input/";
                params = {tranStepId: transId, dataObjId: nodeMenu.id};
            } else {
                path = "/plugin/sql/";
                params = {transStepId: transId, dataObjId: nodeMenu.id};
            }
            /*if (nodeMenu.belongType === "LOGIC") {
                params.customDataSet = true;
            }*/
            params.customDataSet = true;
            vm.initPluginReq(vm, services, params, path, transId);

        },
        // 是否具有该线
        hasLine(from, to) {
            for (let i = 0; i < this.data.lineList.length; i++) {
                let line = this.data.lineList[i];
                if (line.from === from && line.to === to) {
                    return true;
                }
            }
            return false
        },
        // 是否含有相反的线
        hashOppositeLine(from, to) {
            return this.hasLine(to, from);
        },
        //是否有连线
        ownLine(from, to) {
            for (let i = 0; i < this.data.lineList.length; i++) {
                let line = this.data.lineList[i];
                if (line.from === from || line.to === to) {
                    return true;
                }
            }
            return false
        },
        //删除节点
        deleteNode(nodeId) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);

            let msg = '';
            for (let i = 0; i < this.data.lineList.length; i++) {
                if (nodeId.id === this.data.lineList[i].from) {
                    for (let j = 0; j < this.data.nodeList.length; j++) {
                        if (this.data.lineList[i].to === this.data.nodeList[j].id) {
                            if (this.data.nodeList[j].keyWord === "serviceOrganization") {
                                msg = ' (删除后请重新配置算子编排!)';
                                break;
                            }
                        }
                    }
                }
            }

            vm.confirm('提示', '确定要删除节点' + nodeId.name + '?' + msg, () => {
                vm.$nextTick(() => {
                    services.deleteTransStep(nodeId.id, this.rowData.transId).then(res => {
                        if (res.data.status === 0) {
                            vm.$message.success("成功删除节点");
                            vm.deleteNodeWithLine = true;
                            vm.jsPlumb.removeAllEndpoints(nodeId.id);
                            vm.data.nodeList = vm.data.nodeList.filter(node => {
                                if (node.id === nodeId.id) {
                                    node.show = false;
                                }
                                return node.id !== nodeId.id;
                            });
                            vm.data.lineList = vm.data.lineList.filter(node => !(node.from === nodeId.id || node.to === nodeId.id));
                            globalBus.$emit('closeAttr', nodeId);
                        }
                    })
                })
            })
        },
        //编辑节点
        editNode(nodeId) {
            this.nodeFormVisible = true;
            this.$nextTick(function () {
                this.$refs.nodeForm.init(this.data, nodeId)
            });
        },

        dataReload() {
            const vm = this, {modelingServices, modelingMock , modelParams } = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.easyFlowVisible = false;
            vm.data.nodeList = [];
            vm.data.lineList = [];
            modelParams.loading = true;
            vm.$nextTick(() => {
                services.loadTransPage(vm.rowData.transId , modelParams).then(result => {
                    if (result.data.status === 0) {
                        vm.setInstanceList(result.data.data.instanceList);
                        vm.easyFlowVisible = true;
                        result.data.data.transMetaVo.children.forEach((item) => {
                            let canLink = item.pluginVo.code !== 'startTaskPlugin';
                            let node = {
                                left: item.x + 'px',
                                top: item.y + 'px',
                                id: item.id,
                                name: item.name,
                                code: item.code,
                                keyWord: item.pluginVo.code,
                                ico: plugIcons[item.pluginVo.code],//item.pluginVo.name
                                show: true,
                                type: item.pluginVo.type, //需要type 判断是数据资源还是组件
                                canLink: canLink,
                                checkClick: true
                            };
                            vm.data.nodeList.push(node);
                        });
                        result.data.data.transMetaVo.hops.forEach((item) => {
                            let line = {
                                from: item.fromTrans.id,
                                to: item.toTrans.id,
                            };
                            vm.data.lineList.push(line);
                        })
                    } else {
                        vm.data.nodeList = [];
                        vm.data.lineList = [];
                    }
                    vm.$nextTick(() => {
                        vm.jsPlumb = jsPlumb.getInstance();
                        vm.$nextTick(() => {
                            vm.jsPlumbInit()
                        })
                    })
                })
            })
        },
        changeLabel() {
            var lines = this.jsPlumb.getConnections({
                source: 'nodeA',
                target: 'nodeB'
            });
            lines[0].setLabel({
                label: '   ',
                cssClass: 'labelClass'
            });
        },
        setInstanceList(value) {
            this.$emit("setInstanceList", value);
        },
    },
    created() {
        this.bindFn(); //获取组件信息
        this.dataReload();
    },
    destroyed() {
        this.unbindFn();
    }


}
</script>

<style scoped>
.flowPanelEt {
    margin-right: auto;
    transition: 300ms;
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
}

.flow_scroll {
    height: 100%;
    position: relative;
}

</style>
<style>
.ce-plug_trans .flow-node-header {
    background-color: #FFBB54;
}

.ce-plug_trans .flow-node-body {
    border-color: #FFBB54;
}

.ce-plug_trans .flow-shadow:hover {
    box-shadow: #FFBB54 0 0 12px 0;
}

.ce-plug_trans.node:hover .flow-name, .ce-plug_trans.node:hover .eda_icon {
    color: #FFBB54;
}

.ce-plug_output .flow-node-header {
    background-color: #66CDAA;
}

.ce-plug_output .flow-node-body {
    border-color: #66CDAA;
}

.ce-plug_output .flow-shadow:hover {
    box-shadow: #66CDAA 0 0 12px 0;
}

.ce-plug_output.node:hover .flow-name, .ce-plug_output.node:hover .eda_icon {
    color: #66CDAA;
}
</style>
