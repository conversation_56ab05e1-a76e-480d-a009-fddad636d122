<template>
    <div class="chartCard">
        <div class="listTitle" :style="[titleStyle , {'line-height' : titleH + 'px'}]" v-show="showTitle">{{title}}</div>
        <!--<div :id="chartId" class="ce-map_pgis"  v-loading="loading"></div>-->
        <map-component ref="map" :id="chartId"
                       :type="type"
                       :config="config"
                       class="ce-map_pgis"
                       load-end="loadEnd"
                       v-loading="loading"
        ></map-component>
    </div>
</template>
<script src="./pgis-card-all.js"></script>
<style scoped lang="less" src="../css/chart.less"></style>
<style scoped lang="less" src="./pgis-card.less"></style>
<style>
    .ce-icon_marker {
        width: 24px;
        height: 33px;
        background:url("../../../../../../assets/gamap/images/men1.png") no-repeat;
        background-size: cover;
    }
    .ce-map_marker {
        background: none;
        border:none;
    }
    .ce-icon_value {
        display: block;
        padding: 2px 0;
        font-size: 14px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
