<template>
    <common-dialog top="5rem" :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <previewResult v-if="reNew" :row="row" v-bind="$attrs" v-on="$listeners" ></previewResult>
        <span slot="footer" class="dialog-footer">
            <dg-button size="mini" @click="visible = false">关 闭</dg-button>
        </span>
    </common-dialog>
</template>

<script>
    import previewResult from "./preview-result.vue"

    export default {
        name: "preview",
        components: {
            previewResult
        },
        data() {
            return {
                title: "数据对象",
                visible: false,
                reNew: false ,
                width : "982px",
                row : {}
            }
        },
        methods: {
            clearData() {
                this.reNew = false;
            },
            show(row) {
                this.visible = true;
                this.reNew = true;
                this.row = row;
            },
        }
    }
</script>