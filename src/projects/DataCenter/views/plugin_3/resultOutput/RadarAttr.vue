<template>
  <div class="radarAttr ce-plug_cont" v-loading="settings.loading">
    <div class="ce-plug_attr">
      <el-form :model="plug_data"
               size="mini"
               label-position="top"
               :label-width="width"
               class="attrForm"
               ref="form"
      >
        <el-form-item
            v-for="(attr , i ) in attrsList"
            :key="i"
            :label="attr.label + ' :'"
            :prop="attr.props"
            :rules="attr.rule"
            v-if="!attr.hide"
        >
          <el-input class="ce-attr_item"
                    :placeholder="attr.placeholder"
                    v-if="attr.type ==='input'" v-model="attr.value"
          ></el-input>
          <div v-if="attr.type ==='select'">
            <i class="el-icon-info ce-tip" v-if="attr.hasTip" :title="attr.tip"></i>
            <el-select filterable class="ce-attr_item" v-model="attr.value" :placeholder="attr.placeholder"
                       @change="attr.change($event , attr.option , i)">
              <el-option
                  v-for="item in attr.option"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
      <FormTip/>
    </div>
    <el-dialog
        :title="plug_data.title"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :modal-append-to-body="true"
        :append-to-body="true"
        @closed="clearData"
    >
      <div class="ce-chart_box" v-loading="loadChart.loading">
        <RadarChart v-if="reNew" :radarChartData="chartData" :plugData="plug_data"></RadarChart>
        <div v-else class="ce-data-none p30">{{ data_none }}</div>
      </div>
    </el-dialog>
    <!-- <div class="attr_btn">
      <el-button
          v-for="(btn , inx) in buttons"
          :key="inx"
          size="mini"
          :type="btn.type"
          @click="btn.clickFn"
      >{{ btn.name }}
      </el-button>
    </div> -->
  </div>
</template>

<script>
import FormTip from './FormTip'
import RadarChart from './charts/RadarChart'
import {chartMixins} from "./chart-mixins/chart-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "./service-mixins";

export default {
  name: "RadarAttr",
  components: {
    FormTip,
    RadarChart
  },
  mixins: [chartMixins, commonMixins, servicesMixins],
  computed: {
    plug_data() {
      return {
        title: this.attrsList[0].value,
        dimension: this.attrsList[1].value,
        classification: this.attrsList[2].value,
        classChartCode: this.attrsList[3].value,
        grouping: this.attrsList[4] ? this.attrsList[4].value : '',
        groupChartCode: this.attrsList[5] ? this.attrsList[5].value : '',
        classificationL: this.attrsList[2].axisLabel,
        groupingL: this.attrsList[4] ? this.attrsList[4].axisLabel : ""
      }
    }
  },
  props: {
    sourceData: Object,
    rowData: Object
  },
  data() {
    return {
      dialogVisible: false,
      radarChartData: {},
      bmCodeList: [],
      width: '100px',
      attrsList: [
        {
          label: '图表标题',
          type: 'input',
          value: '雷达图图表展示',
          placeholder: '请输入图表标题',
          props: 'title',
          rule: [
            {required: true, message: '标题名称不能为空', trigger: ['blur', 'change']},
          ]
        }, {
          label: '维度分类',
          type: 'select',
          value: 'single',
          option: [
            {
              label: '单维度',
              value: 'single'
            }, {
              label: '双维度',
              value: 'double'
            }
          ],
          change: this.dimensionSet,
          props: 'dimension',
          placeholder: '请选择维度分类',
          rule: []
        },
      ],
      radarChartsPlugin: {},

    }
  },
  methods: {
    init() {
      const vm = this, {resultServices, resultMock, settings, radar_root} = this;
      let services = vm.getServices(resultServices, resultMock);
      vm.dimensionSet('single');
      settings.loading = true;
      let path = radar_root + "getRadarChartsPluginPage";
      services.getChartsPluginPage(vm.sourceData.id, settings, path).then(res => {
        if (res.data.status === 0) {
          let result = res.data.data;
          vm.nodeList[0].option = [];
          vm.nodeList2[0].option = [];
          result.columns.forEach(item => {

            let node = {
              label: item.columnZhName,
              value: item.columnName,
              columnType: item.columnType,
              length: item.length,
              precsn: item.presn,
              stepCode: item.stepCode,
              stepId: item.stepId,
              stepName: item.stepName,
            };
            vm.nodeList[0].option.push(node);
            vm.nodeList2[0].option.push(node);
          });
          vm.radarChartsPlugin = result.radarChartsPlugin;
          vm.bmCodeList.push({
            label: "无",
            value: "",
          });
          result.bmCode.forEach(item => {
            vm.bmCodeList.push({
              label: item.name,
              value: item.name,
            })
          });
          if (vm.radarChartsPlugin.title !== null) {
            vm.attrsList[0].value = this.radarChartsPlugin.title;
          }
          if (vm.radarChartsPlugin.groupColumn === "" || vm.radarChartsPlugin.groupColumn === null) {
            vm.dimensionSet('single');
          } else {
            vm.dimensionSet('double');
          }
          vm.attrsList[2].value = vm.radarChartsPlugin.columnCode || "";
          vm.savedChange(vm.attrsList[2].value);
          if (vm.radarChartsPlugin.bmCode !== null) {
            vm.attrsList[3].value = vm.radarChartsPlugin.bmCode;
          }

          vm.attrsList[3].option = vm.bmCodeList;
          vm.axisLabel(vm.attrsList[2].value, vm.nodeList[0].option, 2);
          if (vm.attrsList.length === 6) {
            vm.attrsList[1].value = 'double';
            vm.attrsList[4].value = vm.radarChartsPlugin.groupColumn;
            vm.attrsList[5].value = vm.radarChartsPlugin.groupBmCode;
            vm.axisLabel(vm.attrsList[4].value, vm.nodeList[0].option, 4);
          }
        }
      })
    },
    save(saveName,showTip) {
      const vm = this, {resultServices, resultMock, settings, radar_root} = this;
      let services = vm.getServices(resultServices, resultMock);
      if (vm.attrsList[1].value === 'double' && vm.plug_data.classification === vm.plug_data.grouping) {
        vm.$message.warning("分类字段和分组字段不能一样!");
        return;
      }
      vm.$refs.form.validate(valid => {
        if (valid) {
          let showType = 'general';
          let dimension = '';
          settings.loading = true;
          let path = radar_root + "saveRadarChartsPlugin";
          services.saveChartsPlugin(vm.sourceData.id, vm.plug_data.title, showType, vm.plug_data.classification, vm.plug_data.classChartCode, path, settings, vm.plug_data.groupChartCode, vm.plug_data.grouping, dimension)
              .then(res => {
                if (res.data.status === 0) {
                  vm.saved = true;
                    showTip && vm.$message.success(res.data.data);
                    vm.$emit("setPreviewAble");
                    if(saveName)saveName();
                } else {
                  vm.$message.error("保存失败！");
                }
              })
        }
      })
    },
  }
}
</script>
<style lang="less" scoped></style>
