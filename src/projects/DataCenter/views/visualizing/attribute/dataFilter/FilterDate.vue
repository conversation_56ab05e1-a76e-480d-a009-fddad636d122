<template>
    <div class="FilterDate">
        <div class="mb10" v-if="dateType">
            <el-radio-group v-model="tab" size="mini" @change="tabChange">
                <el-radio-button v-for="item in timeTabs" :key="item.value" :label="item.value">{{item.label}}
                </el-radio-button>
            </el-radio-group>
        </div>
        <el-date-picker
                v-if="reNew"
                v-show="tab === 'picker'"
                size="small"
                v-model="value"
                :type="type"
                :time-arrow-control="true"
                @change="changeFilter"
                :placeholder="tip"
                range-separator="至"
                :start-placeholder="'开始'+tip"
                :end-placeholder="'结束'+tip"
                :unlink-panels="true"
        >
        </el-date-picker>

        <div class="ce-interval" v-show="tab === 'interval'">
            <div class="p5">相对当前时间 : {{nowTime}}</div>
            <div class="ce-row">
               <!-- <div class="ce-col w220">
                    <el-input size="mini" v-model="resultTime" readonly></el-input>
                </div>-->
                <div class="ce-col w100">
                    <el-select
                            v-model="interval_result.range"
                            size="mini"
                            @change="interChange"
                    >
                        <el-option
                                v-for="opt in iOption"
                                :key="opt.value"
                                :value="opt.value"
                                :label="opt.label"
                        ></el-option>
                    </el-select>
                </div>
                <div class="cl mb10"></div>
                <div>
                    <el-input-number style="width:120px;"
                                     @change="interChange"
                                     controls-position="right"
                                     :min="0" :max="1000" v-model="interval_result.year"></el-input-number>
                    <span class="pl6 pr6">年</span>
                    <el-input-number style="width:110px;"
                                     @change="interChange"
                                     controls-position="right"
                                     :min="0" :max="1000" v-model="interval_result.month"></el-input-number>
                    <span class="pl6 pr6">个月</span>
                    <el-input-number style="width:110px;"
                                     @change="interChange"
                                     controls-position="right"
                                     v-if="type === 'daterange'"
                                     :min="0" :max="1000"
                                     v-model="interval_result.day"></el-input-number>
                    <span class="pl6 pr6" v-if="type === 'daterange'">天</span>
                </div>
            </div>
            <div class="tc">
                <el-button-group>
                    <el-button size="mini" type="primary" @click="clearDate">清除</el-button>
                    <!--<el-button size="mini" type="primary" @click="checkFilterData">确定</el-button>-->
                </el-button-group>
            </div>

        </div>


    </div>
</template>

<script>
    export default {
        name: "FilterDate",
        props: {
            data: [String, Date, Array, Number],
            type: String,
            tip: String,
            tabVal: {
                type: String,
                default: () => {
                    return 'picker'
                }
            },
            interval_date: Object
        },
        computed: {
            dateType() {
                let types = ['monthrange', 'daterange'];
                return types.indexOf(this.type) > -1;
            },
            nowTime() {
                return new Date().Format('yyyy-MM-dd');
            },
        },
        watch: {
            type(val) {
                const vm = this;
                this.reNew = false;
                let types = ['monthrange', 'daterange'];
                if (types.indexOf(val) === -1) {
                    this.tab = 'picker';
                } else {
                    this.tab = this.tabVal ? this.tabVal : 'picker';
                }
                this.interval_result = this.interval_date;
                setTimeout(()=>{
                    vm.reNew = true;
                },10);

            },
            data(val){
                this.value = val;
            }
        },
        data() {
            return {
                reNew : true ,
                resultTime:new Date(),
                interval: {
                    year: {label: '年'},
                    month: {
                        label: '月',
                        placeholder: '请选择月份',
                        option: []
                    },
                    day: {
                        label: '日',
                        placeholder: '请选择日期',
                        option: []
                    }
                },
                interval_result: this.interval_date,
                iOption: [
                    {
                        label: '前',
                        value: 'before'
                    },
                    {
                        label: '后',
                        value: 'after'
                    }
                ],
                value: this.data,
                tab: this.tabVal,
                timeTabs: [
                    {
                        label: '时间选择',
                        value: 'picker'
                    },
                    {
                        label: '动态区间',
                        value: 'interval'
                    }
                ]
            }
        },
        methods: {
            async interChange(){
                await this.countDate();
                this.checkFilterData();
            },
            checkFilterData() {
                this.$emit('changeFilter', this.value, this.tab, this.interval_result, true);
            },
            setResultTime() {
                if (this.interval_result && this.interval_result.value) {
                    this.resultTime = new Date(this.interval_result.value).Format('yyyy-MM-dd');
                } else {
                    this.resultTime = new Date().Format('yyyy-MM-dd');
                }
            },
            countDate() {
                let year, month, day, result, date;
                if (this.interval_result.month > 12) {
                    month = this.interval_result.month % 12;
                    year = this.interval_result.year + Math.floor(this.interval_result.month / 12);

                } else {
                    month = this.interval_result.month;
                    year = this.interval_result.year;
                }
                day = this.type === 'daterange' ?  this.interval_result.day * 24 * 60 * 60 * 1000 : 0;
                let now = new Date();
                date = '-' + now.getDate() + ' ' + now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds();
                if ( this.interval_result.range === 'before') {
                    if (now.getMonth() + 1 > month) {
                        result = now.getFullYear() - year + '-' + (now.getMonth() + 1 - month);
                    } else {
                        result =
                            now.getMonth() + 1 - month % 12 > 0 ?
                                now.getFullYear() - year - Math.floor(month / 12) + '-' + (now.getMonth() + 1 - month % 12) :
                                now.getFullYear() - year - Math.floor(month / 12) - 1 + '-' + (now.getMonth() + 13 - month % 12);
                    }
                    this.interval_result.value = new Date(result + date).getTime() - day;
                }else  {
                    result =
                        now.getMonth() + 1 + month % 12 > 12 ?
                            now.getFullYear() + year + Math.floor(month / 12) + 1 + '-' + (now.getMonth() + 1 + month % 12 - 12) :
                            now.getFullYear() + year + Math.floor(month / 12) + '-' + (now.getMonth() + 1 + month % 12);
                    this.interval_result.value = new Date(result + date).getTime() + day;
                }
                this.setResultTime();
            },
            changeFilter(val) {
                this.$emit('changeFilter', val, this.tab, this.interval_result);
            },
            tabChange(val) {
                this.$emit('changeFilter', this.value, val, this.interval_result);
            },
            intervalChange() {
                this.$emit('changeFilter', this.value, this.tab, this.interval_result);
            },

            clearDate() {
                this.interval_result = {
                    range: 'before',
                    value: '',
                    year: 0,
                    month: 0,
                    day: 0
                };
                this.resultTime = "";
                this.checkFilterData();
            }
        },
        created() {
            this.setResultTime();
        }
    }
</script>

<style scoped>
    .ce-interval {
        width: 450px;
        border: 1px solid #ddd;
        padding: 10px;
    }

    .ce-row {
        overflow: hidden;
        margin-bottom: 10px;
    }

    .ce-col {
        float: left;
    }
</style>
<style>
    .el-range-editor--small .el-range-separator {
        line-height: 28px;
    }
</style>
