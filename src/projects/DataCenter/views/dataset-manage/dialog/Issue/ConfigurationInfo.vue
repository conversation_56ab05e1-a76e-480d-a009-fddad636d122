<template>
    <div>
        <el-form :model="configData">
            <el-form-item label="文件路径：" label-width="150px">
              <!--  <SelectTree size="mini" :options="treeOptions" v-model="configData.filePath" :props="treeProps"
                            @change="nodeClick" :levelSelect="selectLevel"></SelectTree>-->
                <ce-select-drop
                        ref="pos_tree"
                        v-model="configData.filePath"
                        :props="defaultProps"
                        :filterNodeMethod="filterNode"
                        @singleSelected="nodeClick"
                        filterable
                        check-leaf
                        check-strictly

                >

                </ce-select-drop>
            </el-form-item>
            <el-form-item label="是否发布Dubbo：" label-width="150px">
                <el-radio v-model="configData.isDubbo" label="0">否</el-radio>
                <el-radio v-model="configData.isDubbo" label="1">是</el-radio>
            </el-form-item>
            <div v-if="configData.isDubbo=== '1'">
                <el-form-item label="路径：">
                    <el-row :gutter="2">
                        <el-col :span="11">
                            <el-select v-model="configData.path" @change="changePort">
                                <el-option v-for="item in pathOption"
                                           :label="item.label"
                                           :value="item.value"
                                           :key="item.key"
                                ></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="6">
                            <el-input readonly v-model="configData.port"></el-input>
                        </el-col>
                    </el-row>
                </el-form-item>
            </div>
        </el-form>
        <!--        服务测试页面-->
        <div>
            <common-dialog
                    custom-class="serve"
                    title="服务测试"
                    :visible.sync="serviceTestVisible"
                    width="80%"
            >
                <ServiceTest ref="serviceTest"/>
                <div slot="footer">
                    <el-button @click="previousStep" type="primary" size="mini">上一步</el-button>
                    <el-button @click="closeServiceTest" size="mini">取消</el-button>
                    <el-button @click="online" type="primary" size="mini">上线</el-button>
                </div>
            </common-dialog>
        </div>
    </div>
</template>

<script>
    import ServiceTest from './ServiceTest'
    import {treeMethods} from "@/api/treeNameCheck/treeName";

    export default {
        name: "ConfigurationInfo",
        components: { ServiceTest},
        data() {
            return {

                serviceTestVisible: false,

                treeOptions: [],
                selectLevel: 'children',

                defaultProps: {
                    id: "id",
                    label: "label",
                    children: "children",
                },

                configData: {
                    filePath: "",
                    port: "",
                    isDubbo: "0",
                    path: "",
                },

                pathOption: [
                    {
                        label: "路径1",
                        value: "path1",
                    },
                    {
                        label: "路径2",
                        value: "path2",
                    }
                ],
            }
        },
        methods: {
            filterNode : treeMethods.methods.filterNode ,
            online() {
            },
            closeServiceTest() {
            },
            previousStep() {
                this.serviceTestVisible = false;
            },
            serviceTestHandleClose() {
                this.serviceTestVisible = false;
            },
            showServiceTest() {
                this.serviceTestVisible = true;
            },


            changePort() {
                this.configData.port = "端口号"
            },
            getConfigInfo() {
                return this.configData;
            },
            nodeClick(data) {
                this.configData.filePath = data.value;
            },
        }
    }
</script>

<style scoped>

</style>