<!--批量测试api-->
<template>
    <div class="BatchTestPage api-outer">
        <div class="api-left api-left-batch" v-if="showLeft" v-loading="settings.loading">
            <div class="api-title">{{ chooseTip }}</div>
            <div class="api-batch-box selectn" @dragover="onDragOver" @drop="onDrop">
                <ul v-if="testApiList.length">
                    <ce-sortable mode="vertical" :data="testApiList">
                        <template slot="listItem" slot-scope="{item:api , id , $index:i}">
                            <li class="api-test-item"
                                :title="api.name"
                                v-if="api.isApi"
                                :key="i">
                                <div class="api-test-txt">
                                    <i class="dg-iconp icon-api pr8"></i>
                                    <span class="api-test-name">{{ api.name }}</span>
                                </div>
                                <div class="api-test-deal">
                                    <em class="el-icon-setting api-test-icon" title="设置参数"
                                        @click="settingParams(api)"></em>
                                    <em class="el-icon-delete api-test-icon" title="删除" @click="deleteApi(i)"></em>
                                </div>
                            </li>
                            <el-tooltip v-else :key="i" placement="right" effect="light">
                                <div class="api-test-pop" slot="content">
                                    <div class="api-test-pop-item" v-for="(li , j) in api.children" :key="j">
                                        <em class="dg-iconp icon-api pr5"></em>
                                        <span>{{ li.name }}</span>
                                    </div>
                                </div>
                                <li class="api-test-item"
                                    :title="api.name"
                                    :key="i">
                                    <div class="api-test-txt">
                                        <i class="el-icon-s-operation pr8"></i>
                                        <span class="api-test-name">{{ api.name }}</span>
                                        <span class="api-test-num">{{ `+${api.children.length}` }}</span>
                                    </div>
                                    <div class="api-test-deal">
                                        <em class="el-icon-delete api-test-icon" title="删除" @click="deleteApi(i)"></em>
                                    </div>
                                </li>
                            </el-tooltip>
                        </template>
                    </ce-sortable>
                </ul>
                <empty v-else class="api-list-empty" :value="useCaseButtonShow ?  emptyTxt : '' "></empty>
            </div>
            <div class="api-batch-group">
                <el-button v-for="(btn , i) in btnGroup" :key="i"
                           @click="btn.clickFn" v-if="btn.show()"
                           :type="btn.type"
                >{{ btn.label }}
                    <help-document :code="btn.code"></help-document>
                </el-button>
            </div>
        </div>
        <div class="api-right api-right-batch">
            <div class="api-title">{{ resTxt }}</div>
            <div class="api-batch-table">
                <common-table
                        :data="resData"
                        :columns="resHead"
                        :pagination="false"
                        height="100%"
                        class="width100"
                        :border="false"
                >
                    <template slot="operate" slot-scope="{row}">
                        <el-button type="text" @click="showDetail(row)">{{ detailTxt }}</el-button>
                    </template>
                    <template slot="result" slot-scope="{row}">
                        <span class="api-stare" v-html="$options.filters.respondState(row.result)"></span>
                    </template>
                </common-table>
            </div>
        </div>
    </div>
</template>

<script>
import CeSortable from "dc-front-plugin/src/components/CeSortable/CeSortable";
import {globalBus} from "@/api/globalBus";
import {mapGetters} from "vuex"
import {RequestDetail} from "./requestDetail";

export default {
    name: "BatchTestPage",
    components: {CeSortable},
    mixins : [RequestDetail],
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
    },
    props: {
        showLeft: {
            type: Boolean,
            default: true
        },
        useCaseButtonShow: {//判断测试用例跟导入用例按钮是否显示
            type: Boolean,
            default: true
        },
    },
    filters: {
        respondState(res) {
            return res === 'success' ? `
                <span class="api-state-success">
                    <i class="el-icon-success pr5"></i><span>成功</span>
                </span>
            ` : `
                <span class="api-state-error">
                    <i class="el-icon-error pr5"></i><span>失败</span>
                </span>
            `;
        }
    },
    data() {
        return {
            emptyTxt: '请从左侧API列表中拖拽API开始测试。',
            detailTxt: '查看详情',
            chooseTip: '选择API',
            resTxt: '调用结果',
            btnGroup: [
                {
                    label: '开始测试',
                    clickFn: this.startTest,
                    type: 'primary',
                    show: () => this.testApiList.length && this.rights.indexOf('testOnlineTestSingleService') > -1,
                    code: 'testSingleService'
                }, {
                    label: '保存用例',
                    clickFn: this.showSaveApi,
                    show: () => this.testApiList.length && this.useCaseButtonShow,
                    code: ''
                }, {
                    label: '导入用例',
                    clickFn: this.showImportApi,
                    show: () => true && this.rights.indexOf('testOnlineImportUseCase') > -1 && this.useCaseButtonShow,
                    code: 'importUseCase'
                },
            ],
            resData: [],
            resHead: [
                {
                    prop: 'apiName',
                    label: 'API名称',
                }, {
                    prop: 'result',
                    label: '请求结果',
                }, {
                    prop: 'usedTime',
                    label: '请求耗时(ms)',
                }, {
                    prop: 'operate',
                    label: '操作',
                    align: 'center',
                    width: 160
                },
            ],
            testApiList: [],
            dropItem: null,
            canDrop: false,
            settings :{
                loading : false
            },


            batchQuery: false,
            requestTableData: [],
            form:{},
            apiRes: {},
        }
    },
    methods: {
        settingParams(api) {
            let layer = this.$dgLayer({
                title: '设置参数',
                content: require('./ParamsList'),
                move: false,
                maxmin: false,
                area: ['600px', '550px'],
                props: {
                    apiRow: api,
                    showFooter: true,
                },
                on: {
                    setTestVo(vo, data, form , useCaseApiParams) {
                        api.testVo = vo;
                        api.reqTableData = data;
                        api.interName = form.interName;
                        api.requestUrl = form.requestUrl;
                        api.useCaseApiParams = useCaseApiParams;
                        layer.close(layer.dialogIndex);
                    }
                }
            })
        },
        showImportApi() {
            this.$emit("showImportApi");
        },
        showSaveApi() {
            const vm = this;
            this.validate(valid => {
                if (valid) {
                    let allApi = vm.getAllApi();
                    vm.$emit("showSaveApi", allApi);
                }
            })
        },
        validate(cb) {
            const {testApiList} = this;
            let unsetList = testApiList.filter(li => li.isApi && !li.testVo);
            if (unsetList.length) {
                let unset = unsetList.map(li => li.name).join('、');
                this.$message.warning(`API '${unset}' 未设置参数!`);
                return cb(false);
            }
            cb(true)
        },
        startTest() {
            const vm = this;
            this.validate( valid => {
                if (valid) {
                    vm.resData = [];
                    let {testVo: params, subTestVo} = vm.getAllTestVo();
                    let {apis: allApi, subApis} = vm.getAllApi();
                    vm.testBatch(params, allApi);
                    if (subTestVo && subTestVo.length) {
                        for (let i in subTestVo) {
                            vm.getSubscribe(subTestVo[i], subApis[i]);
                        }
                    }
                }
            })
        },
        testBatch(params, allApi) {
            const vm = this;
            if (!params.length) return;
            let services = this.$services("apiTest");
            services.testBatchService({params}).then(res => {
                if (res.data.code === 0) {
                    let result = res.data.data.map((li, i) => {
                        li.testVo = params[i];
                        li.api = JSON.parse(JSON.stringify(allApi[i]))
                        return li;
                    });
                    vm.resData.push.apply(vm.resData , result);
                }
            })
        },
        getSubscribe(paramsVo, api) {
            const vm = this;
            let ser = vm.$services("serviceManage");
            ser.serviceSubscribe(paramsVo).then(res => {
                vm.resData.push({
                    ...res,
                    apiName: api.name,
                    api,
                    usedTime: "",
                    code: res.status,
                    result: res.data.code === 0 ? 'success' : 'fail',
                });
            })
        },
        /**
         *
         * @return {{apis: *[], subApis: *[]}}
         */
        getAllApi() {
            let apis = [], subApis = [];
            this.testApiList.forEach(li => {
                if (li.isApi) {
                    if (li.interName === 'subscript') subApis.push(li);
                    else apis.push(li)
                } else {
                    li.children.forEach(ch => {
                        if (ch.apiType !== 'subscript') apis.push(ch);
                        if (ch.apiType === 'subscript') subApis.push(ch)
                    })
                }
            })
            return {apis, subApis};
        },
        /**
         * 获取测试参数
         * @return {{testVo: *[], subTestVo: *[]}}
         */
        getAllTestVo() {
            let testVo = [], subTestVo = [];
            this.testApiList.forEach(li => {
                if (li.isApi) {
                    if (li.interName === 'subscript') subTestVo.push(li.testVo);
                    else testVo.push(li.testVo);
                } else {
                    li.children.forEach(ch => {
                        if (ch.apiType !== 'subscript') testVo.push(ch.testVo);
                        if (ch.apiType === 'subscript') subTestVo.push(ch.testVo)
                    });
                }
            })
            return {testVo, subTestVo};
        },
        async onDrop() {
            if (!this.canDrop) return;
            let data = this.setDropData();
            if (!data) {
                return this.$message.warning("该目录内没有api");
            }
            this.settings.loading = true;
            let result = await this.getAllDetails(data);
            this.settings.loading = false;
            result = result.filter(li => !!li);
            if (!result.length) return;
            this.addList(result);
            this.canDrop = false;
        },
        async getAllDetails(data) {
            let list = [];
            for (let li of data) {
                let item = await this.getApiDetail(li);
                list.push(item);
            }
            return list;
        },
        async getApiDetail(li) {
            const vm = this;
            if(!li) return
            let id = li.id;
            let services = this.$services('modeling');
            return await services.getServiceDetailById(id).then(async res => {
                if (res.data.code === 0) {
                    let result = res.data.data;
                    vm.requestTableData = [];
                    let data = {
                        type: result.apiType,
                        async: result.async,
                        isApi: true,
                        id: result.servicePublishId,
                        serviceMetaId: result.serviceMetaId,
                        apiName: result.apiName,
                        name: result.apiName,
                    }
                    vm.apiRow = data;
                    let hasUnfilled = vm.judgmentParam(result);

                    if(!hasUnfilled){
                        await vm.getModelServicesInfo(result.serviceMetaId, result.apiType === '比对订阅')
                        let vo = vm.setTestVo();
                        data.testVo = vo;
                    }
                    return data
                } else {
                    return null;
                }
            })
        },
        addList(data) {
            let list = this.testApiList.map(li => li.id);
            let notList = [], hasList = [];
            data.forEach(li => {
                if (list.includes(li.id)) hasList.push(li);
                else {
                    notList.push(li);
                }
            });
            if (hasList.length) {
                let hasName = hasList.map(li => li.name).join("、");
                this.$message.warning(`API/用例 '${hasName}' 已存在！`)
            }
            this.testApiList.push.apply(this.testApiList, notList);
        },
        setDropData() {
            let {dropItem} = this;
            if (dropItem.elementOrClassify === '1') {
                return [dropItem];
            } else if (dropItem.children && dropItem.children.length) {
                let children = dropItem.children.filter(li => li.elementOrClassify === '1');
                if (children.length) return children;
            }
            return null;
        },
        onDragOver(e) {
            if (!this.canDrop) return;
            e.preventDefault();
            e.stopPropagation();
        },
        showDetail(row) {
            let layer = this.$dgLayer({
                title: `详情-${row.apiName}`,
                content: require('./RespondPage'),
                move: false,
                maxmin: false,
                area: ['1000px', '600px'],
                noneBtnField: true,
                props: {
                    row: row.api
                },
                success() {
                    if (row.api.interName === 'subscript') layer.$children[0].setSubResultData(row);
                    else layer.$children[0].setTestVo(row.testVo, row);
                }
            })
        },
        setDragItem(data) {
            this.dropItem = JSON.parse(JSON.stringify(data));
            this.canDrop = true;
        },
        bindGlobalFn() {
            globalBus.$on("api-drag-item", this.setDragItem);
        },
        offGlobalFn() {
            globalBus.$off("api-drag-item", this.setDragItem);
        },
        deleteApi(i) {
            this.testApiList.splice(i, 1);
        },

        backApiData() {},
        judgmentParam(result) {
            let flag = false;
            let info = result.inputParams.filter(n=>n.code === "schemeServiceRequestModel");
            if(info.length){
                info.forEach(p=>{
                    let data = p.children.find(n => n.code === "operatorDataSet");
                    if(data && data.is_must === 't'){
                        data.children.forEach(h => {
                            if(h.children.find(g => g.is_must === 't'&& !g.example)){
                                flag = true;
                            }
                        })
                    }
                })
            }
            else{
                if(result.inputParams.find(n => n.is_must === 't'&& !n.example)){
                    flag = true;
                }
            }
            return flag;
        }
    },

    created() {
        this.bindGlobalFn();
    },
    beforeDestroy() {
        this.offGlobalFn();
    }
}
</script>
<style lang="less">
.api {
    &-state {
        &-success {
            color: @secColorG1;
        }

        &-error {
            color: @secColorO2;
        }
    }
}
</style>
<style scoped lang="less" src="./testPage.less"></style>
