<template>
    <div>
        <el-form label-width="100px">
            <el-form-item label="选择引擎：" required>
                <dg-select :data="engineList" v-model="selectEngine"></dg-select>
            </el-form-item>
        </el-form>
        <el-button v-footer @click="clear" >取消</el-button>
        <el-button v-footer @click="run">运行</el-button>
    </div>
</template>

<script>
    export default {
        name: "index",
        data(){
            return {
                selectEngine: '',
                engineList: [],
            }
        },
        methods:{
            clear(){
                this.$emit('close');
            },
            run(){
                if(!this.selectEngine){
                    return this.$message.warning('请先选择引擎');
                }
                this.$emit('run',this.selectEngine);
            },
            init(){
                const vm = this;
                let services = vm.$services('modeling');
                services.engineList().then(res=>{
                    let engineList = [];
                    if (res.data.status === 0) {
                        res.data.data.forEach(n=>{
                            engineList = engineList.concat(Object.values(n));
                        })
                    }
                    vm.engineList = engineList.map(n=>{return {label: n, value: n}})
                    vm.selectEngine = vm.engineList.length ? vm.engineList[0].value : '';
                })
            }
        },
        created(){
            this.init()
        }
    }
</script>

<style scoped>

</style>