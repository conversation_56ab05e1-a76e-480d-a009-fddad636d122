import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import * as $ from 'jquery'

export default {
    name: "tree" ,
    mixins : [treeMethods , commonMixins , servicesMixins],
    computed : {
        ...mapGetters(["userRight"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    props:{
        hasSave:Boolean,
        isNew:Boolean,
        notFirst:Boolean,
    },
    data(){
        return {
            defaultProps : {
                value : "id" ,
                label : "name" ,
                children : "children"
            },
            treeMenu : [] ,
            addBtnTxt : '新增子目录' ,
            menu: [
                {
                    name: '修改目录',
                    fn: this.editNode ,
                    show : () => true
                }, {
                    name: '删除目录',
                    fn: this.deleteNode ,
                    show : () => true
                }
            ],
        }
    },
    methods : {
        setTree(){
            this.$emit("setTreeData",this.data);
        },
        async initTree() {
            let parentId = '';
            if(this.notFirst===true){
                parentId = '-1';
            }
            const vm = this , {modelingServices , modelingMock , settings } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            settings.loading = true;
            services.queryTransTree(parentId , '' , 'DIG_DIR_MF' , settings).then(res => {
                if(res.data.status === 0){
                    if(!res.data.data || res.data.data.length === 0) return ;
                    let child = [];let curId = res.data.data[0].id;
                    res.data.data.forEach(item => {
                        child.push({
                            id : item.id ,
                            name : item.name ,
                            children : item.children || [] ,
                            isParent : item.isParent ,
                            pId : "0",
                            dirType : item.dirType
                        })
                    });
                    vm.data = [
                        {
                            id: "0",
                            name: "全部",
                            isParent : true,
                            children: child
                        }
                    ];
                    vm.$nextTick(()=>{
                        if(vm.$refs.tree){
                            vm.$refs.tree.setCurrentKey(curId);
                            let currentNode = vm.$refs.tree.getCurrentNode();
                            vm.$emit("initTable" , currentNode);
                        }

                    });
                    vm.setTree();
                }
            })

        },
        nodeClick(v,i){
            this.$emit('nodeClick',v.name , v.id , i);
        },
        addChildren(event , node, object) {//新增子目录
            this.showMenu(node, object);
            this.prompt('新增子目录', '', this.addTreeNode , '目录名称' , 'child' );
        },
        editNode(value) {//编辑
            this.prompt('修改目录名', this.selectedData.name, this.editTree , '目录名称' , 'child' );
        },
        addTreeNode(value) {
            const vm = this , {modelingServices , modelingMock  } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            // let pId = vm.data[0].children.filter(item => {return item.name === '我的' && item.pId === "0"})[0].id;
            let pId  = vm.selectedData.id;
            services.createTransClassify(pId , value , "DIG_DIR_MF" ).then(res =>{
                if(res.data.status === 0){
                    vm.$message({
                        type: 'success',
                        message: "新增子目录" + ':' + value
                    });
                    let newTreeNode = {
                        children: [],
                        id: res.data.data,
                        isParent: false,
                        name: value,
                        pId: pId ,
                        open :false
                    };
                    vm.selectedData.children.unshift(newTreeNode)
                    // vm.data[0].children[0].children.unshift(newTreeNode);
                }
            })
        },
        deleteNode() {//删除
            const vm = this , {modelingServices , modelingMock  } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            let node = this.getTreeNode() , nodeN = node.data.name;
            let currentNode = vm.$refs.tree.getCurrentNode();
            const index = node.children.findIndex(d => d.id === node.data.id);
            vm.confirm(`确认删除\"${nodeN}\"及同步删除\"${nodeN}\"下的数据模型吗` , '删除' , ()=>{
                services.deleteTransClassify(node.data.id).then(res => {
                    if(res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        node.children.splice(index,1);
                        if(currentNode.id === node.data.id){
                            vm.$emit("initTable" , vm.selectedNode.parent.data);
                            vm.$refs.tree.setCurrentKey(vm.selectedNode.parent.data.id);
                        }
                    }
                })
            });

        },
        editTree(value) {
            const vm = this , {modelingServices , modelingMock  } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            let node = this.selectedData;
            services.updateTransClassify(node.id , value).then(res => {
                if(res.data.status === 0){
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.name = value;
                }
            })
        },
    }
}
