<template>
    <div class="flowButtons selectn" @drop.stop>
        <header class="main-content__header">
            <template v-for="(item, idx) in actions">
                <div
                        class="main-content_item"
                        :key="idx"
                        v-if="item.show(rights)"
                        :class="[active === item.value && 'is-active' , {'disabled' : loading}]"
                        @click="()=>{
                            item.fn();
                            handleWorker(item);}">
                    <i :class="['dg-iconp', item.icon]"></i>
                    <span>{{ item.label }}</span>
                    <div class="ce-driver"></div>
                </div>
            </template>
            <dg-button :type="isShowVideo ? 'primary' : 'default'" icon="el-icon-video-camera-solid" style="border-radius: 15px"  v-if="componentType" @click="caseVideo">
                {{isShowVideo ? '关闭案例视频' : '查看案例视频'}}
            </dg-button>
        </header>
        <div class="main-content__tip">
            <span v-if="modelState.type === 'success'">
                     <i :title="modelState.text" :class="modelStateIcon" class="dg-iconp icon-f-circle-check"></i>
                     <span>运行完成：{{ second }}</span>
                </span>
            <span v-else-if="modelState.type !== 'none'">
                <i class="status_icon" :title="modelState.text" :class="modelStateIcon"></i>
                {{ modelState.text }}
            </span>
        </div>
        <SqlPage ref="sql"/>
        <SaveModel ref="SaveModel" v-on="$listeners"/>
        <timing ref="timing" :rowData="rowData"/>
        <TaskLog ref="TaskLog"/>
        <GlobalVariable ref="variable" :rowData="rowData"/>
        <create-api ref="createAPI" :rowData="rowData"></create-api>
    </div>
</template>

<script>
import {common} from "@/api/commonMethods/common"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import SqlPage from "@/projects/DataCenter/views/visualizing/visual/sql-page/index"
import SaveModel from "@/projects/DataCenter/views/modeling/flowPanel/save-model";
import Timing from "@/projects/DataCenter/views/modeling/flowPanel/timeing-page"
import TaskLog from "@/projects/DataCenter/views/modeling/flowPanel/TaskLog"
import createApi from "@/projects/DataCenter/views/modeling/flowPanel/createApi/index"
import GlobalVariable from "@/projects/DataCenter/views/modeling/flowPanel/global-variable"
import {globalBus} from "@/api/globalBus";
import {checkFlowPath} from "@/api/commonMethods/check-flow-path";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"

export default {
    name: "flowButtons",
    mixins: [common, commonMixins, servicesMixins, checkFlowPath],
    props: {
        status: {
            type: Number,
            default: 0
        },
        rowData: Object,
        lines: Array,
        isTemplate: Boolean,
        isFocus: Boolean,
        nodes: Array,
        isCase : {
            type : Boolean,
            default : false,
        },
        componentType: String
    },
    components: {SqlPage, SaveModel, Timing, TaskLog, GlobalVariable, createApi},
    computed: {
        modelStateIcon() {
            return this.modelState.icon + ' status-' + this.modelState.type;
        },
        flowLine() {
            return this.lines;
        },
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
    },
    data() {
        return {
            isShowVideo:false,
            active: "",
            actions: {
                save: {
                    label: "保存",
                    icon: "icon-save",
                    value: "bc",
                    show:()=>this.rowData.isEditParam ? false :  !this.isCase,
                    fn: this.model_save
                },
                saveOther: {
                    label: "另存为",
                    icon: "icon-save-b",
                    value: "lcw",
                    show:()=>this.rowData.isEditParam ? false :  !this.isCase,
                    fn: this.saveOther
                },
                run: {
                    label: "运行",
                    icon: "icon-play",
                    value: "yx",
                    show:()=> this.rowData.isEditParam ? true : !this.isCase,
                    fn: this.model_run
                },
                // stop: {
                //     label: "停止",
                //     icon: "icon-play",
                //     value: "tz",
                //     show:()=> this.rowData.isEditParam ? true : !this.isCase,
                //     fn: this.model_stop
                // },
                sql: {
                    label: "查看SQL",
                    icon: "icon-data-b",
                    value: "yxsql",
                    show:()=> true,
                    fn: this.model_sql
                },
                timing: {
                    label: "定时作业",
                    icon: "icon-l-time",
                    value: "dsyx",
                    show:(right)=> this.rowData.isEditParam ? false : !this.isCase && right.indexOf($right["processModelingSaseSettingPage"]) > -1,
                    fn: this.setTiming
                },
                history: {
                    label: "运行历史",
                    icon: "icon-list",
                    value: "yxls",
                    show:()=> this.rowData.isService ? false : true,
                    fn: this.showTaskLog
                },
                variable: {
                    label: "参数设置",
                    icon: "icon-data-c",
                    value: "yxbl",
                    show:()=> (this.rowData.isEditParam || this.componentType) ? false : !this.isCase,
                    fn: this.showVariable
                },
                createAPI: {
                    label: "生成API",
                    icon: "iconfont icondaochu_huaban",
                    value: "scapi",
                    show:(right)=>(this.rowData.isEditParam || this.componentType) ? false :  !this.isCase && right.indexOf($right["modelServiceGenerateAPI"]) > -1,
                    fn: this.createAPI
                },
                testAPI: {
                    label: "测试API",
                    icon: "el-icon-finished",
                    value: "csapi",
                    show:(right)=>(this.rowData.isEditParam || this.componentType)? false :  !this.isCase && right.indexOf($right["modelServiceTest"]) > -1,
                    fn: this.testAPI
                },
                /*beauty: {
                    label: "一键美化",
                    icon : "el-icon-magic-stick",
                    value: "yjmh",
                    show:()=> true,
                    fn: this.beautyFn
                }*/
            },
            second: "3",
            states: [
                {type: 'none', text: '暂无状态', icon: 'icon icon-state'},
                {type: 'success', text: '任务完成', icon: 'icon icon-state'},
                {type: 'error', text: '异常', icon: 'icon icon-state'},
                /* {type: 'ongoing', text: '分配中', icon: 'el-icon-loading'},
                {type: 'none', text: '取消成功', icon: 'icon icon-state'}, */
                {type: 'ongoing', text: '执行中', icon: 'el-icon-loading'},
                {type: 'cancel', text: '执行取消', icon: 'el-icon-s-help'},
            ],
            modelState: {},
            instanceList: "",
            timeState: "",
            loading : false ,//防止双击触发
            executorServerName:'',
        }
    },
    methods: {
        beautyFn(){
            this.$emit("beautyFlow")
        },
        /**
         * 生成api
         * */
        createAPI() {
            if(this.rowData.isNew){
                return this.$message.warning("请先保存");
            }
            if(!this.nodes.some(item => item.keyWord === "cicadaMetaServiceInput") || !this.nodes.some(item => item.keyWord === "cicadaMetaServiceOutput")){
                return this.$message.warning('模型生成API时，必须包含API请求入参和API响应结果组件！')
            }
            else{
                this.$refs.createAPI.show(this.nodes.find(item => item.keyWord === "cicadaMetaServiceInput").id);
            }

        },
        /**
         * 测试api
         * */
        testAPI(){
            const vm = this;
            let info = vm.rowData;
            info.serviceType = '模型分析';
            if(info.isNew){
                return vm.$message.warning("请先保存");
            }
            let services = vm.$services('modeling');
            vm.apiList = [];
            services.getApiList(info.transId, '').then(res =>{
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if(result && result.length){
                        let layer = this.$dgLayer({
                            title: "api详情",
                            content: require("@/projects/DataCenter/views/modeling/dialog/details/detail-page/apiServer/index"),
                            move: false,
                            props : {
                                info: info,
                                isAi: false,
                                type: 'modeling',
                                modelTest: false,
                                serviceDetail: {}
                            },
                            on: {
                                close() {
                                    layer.close(layer.dialogIndex);
                                }
                            },
                            area: ["80%", "80%"],
                            btnAlign: 'r',
                        });
                    }
                    else{
                        vm.$message.warning("暂无API信息");
                    }
                }
            })
        },
        /**
         * 设置全局变量
         * */
        showVariable() {
            this.$refs.variable.init();
        },
        /**
         * 显示日志
         * */
        showTaskLog() {
            this.$refs.TaskLog.show(this.rowData, this.nodes);
        },
        /**
         *  设置模型实例
         * */
        setInstanceList(value) {
            if (value) this.instanceList = value;
        },
        /**
         * 定时运行
         * */
        setTiming() {
            this.$refs.timing.show();
        },
        /**
         * 工作步骤
         */
        handleWorker(item) {
            this.active = item.value;
        },
        /**
         * testCount 链路条数
         * testClose 链路是否回环封闭
         * @return {Promise<void>}
         */
        async model_run() {
            const vm = this, {nodes} = vm;
            if(vm.rowData.isEditParam){//四系跳三系先不用选择引擎
                vm.confirm("提示", '此操作将执行任务', () => {
                    vm.startJob();
                })
                return
            }
            let testCount = await vm.getAllPath(vm.flowLine);
            if (testCount) return;
            let testClose = await vm.hasClosePath();
            if (testClose) return;
            if (!nodes.length) {
                vm.$message.warning("请先配置插件");
                return;
            }
            let layer = vm.$dgLayer({
                title: '运行设置',
                content: require("@/components/common/schedulingChoice/index.vue"),
                props: {
                    value: '',
                },
                area: ["400px", "230px"],
                move: false,
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    },
                    run(engine){
                        vm.executorServerName = engine;
                        vm.confirm("提示", '此操作将执行任务', () => {
                            vm.startJob();//开始执行
                        })
                        layer.close(layer.dialogIndex);
                    }
                },
            })
        },
        /**
         * 任务停止
         */
        model_stop() {
            const vm = this, {nodes,modelingServices, modelingMock} = vm;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.confirm("提示", '当前模型有任务正在运行中，是否停止运行？', () => {
                let data = {
                    transId :vm.rowData.transId
                }
                vm.rowData.isEditParam ? data.type = '1' : null;
                services.taskStop(data).then(res => {
                    // console.log(res.data.data);
                })
            })
        },
        /**
         * 运行接口
         * */
        startJob() {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.$message({
                type: 'success',
                message: '开始执行！',
            });
            vm.$emit('setLoading', true)
            services.runJob(vm.rowData.transId, vm.rowData.isEditParam ? '' : vm.executorServerName, vm.rowData.isEditParam ? '1' : '').then(res => {
                vm.$emit('setLoading', false)
                // console.log(res.data.data);
            })
        },
        /**
         * 获取方案sql
         * */
        model_sql() {
            const vm = this, {services} = vm;
            vm.loading = true;
            services.getFlowSql(vm.rowData.transId, vm.rowData.isEditParam ? '1' : '', vm).then(res => {
                if (res.data.status === 0) {
                    // vm.$refs.sql.show(res.data.data, vm.rowData.modelName);
                    vm.showSqlPage(res.data.data, vm.rowData.modelName);
                }
            })
        },
        showSqlPage(data, title) {
            const vm = this;
            let layer = vm.$dgLayer({
                title,
                content: require("@/components/common/sqlPage/index.vue"),
                props: {
                    value: data,
                },
                area: ["1024px", "80%"],
                move: false
            })
        },
        /**
         * 另存为
         * 参数: 方案数据 ， 运行实例 ，标题 ， 是否另存为
         * */
        saveOther() {
            if(!this.checkHasDataSet()) return;
            if(this.rowData.isNew){
                this.$refs.SaveModel.show(this.rowData, this.instanceList, '模型另存为');
            }else {
                this.$refs.SaveModel.show(this.rowData, this.instanceList,'模型另存为',  true);
            }

        },
        /**
         * 判断是否有输入
         * @return {*}
         */
        checkHasDataSet(){
            const vm = this , {nodes} = vm;
            let hasDataset = nodes.some(item => item.type !== 'plug'||item.keyWord==='cicadaServiceInputMeta' || item.keyWord==="cicadaFileInputMeta" || item.keyWord === "cicadaMetaServiceInput");
            if(!hasDataset) vm.$message.warning("请配置数据集或输入插件");
            if(vm.componentType === '信息核查' && !nodes.some(item=>item.keyWord === 'cicadaMetaServiceCheckOutPut')){
                vm.$message.warning("请配置信息核查插件");
                return false;
            }
            return hasDataset;
        },
        /**
         * 判断是否有API请求入参、API响应结果
         * @return {*}
         */
        checkHasInputAndOut(){
            const vm = this , {nodes} = vm;
            let id = '';
            let hasInputAndOut = nodes.some(item => item.keyWord === "cicadaMetaServiceOutput") && nodes.some(item => item.keyWord === "cicadaMetaServiceInput");
            if(hasInputAndOut){
                id = nodes.find(item => item.keyWord === "cicadaMetaServiceInput").id
            }
            return id;
        },
        /**
         * 保存
         * @param e
         * @param isModel
         * @param transId
         */
        model_save(e, isModel = false, transId) {
            const vm = this;
            if (transId && transId !== vm.rowData.transId) return;
            if(!vm.checkHasDataSet()) return;
            if(this.nodes.filter(item => item.keyWord === "cicadaMetaServiceOutput").length > 1){
                return this.$message.warning('方案只能有一个API响应结果插件')
            }
            if(vm.rowData.isNew)vm.$refs.SaveModel.show(vm.rowData, vm.instanceList);
            else {
                vm.loading = true;
                setTimeout(()=>{
                    vm.loading = false;
                    vm.$message.success("保存成功");
                },300)
            }
        },
        getState() {
            const vm = this, {services} = this;
            services.jobStatusMlsql(vm.rowData.transId).then(res => {
                if (res.data.status === 0) {
                    let state, result = res.data.data, {status} = result.status;
                    if (status === "none") {
                        state = 0;
                    } else if (status === "success") {
                        vm.second = result.runTime;
                        state = 1;
                        globalBus.$emit('fileDownLoadsuccess', res.data.data.status);
                    } else if (status === "running") {
                        state = 3;
                    }else if (status === "cancel") {
                        state = 4;
                    } else {
                        state = 2;
                    }
                    vm.stateInit(state);
                }
            })

        },
        clearTime() {
            clearInterval(this.timeState);
        },
        startTime() {
            this.timeState = setInterval(this.getState, 1000);
        },
        stateInit(n) { // n 传进来状态
            this.modelState = this.states[n];
            let obj = {
                label: this.modelState.type === 'ongoing' ? "停止" : "运行",
                icon: "icon-play",
                value: this.modelState.type === 'ongoing' ? "tz" : "yx",
                show:()=> this.rowData.isEditParam ? true : !this.isCase,
                fn: this.modelState.type === 'ongoing' ? this.model_stop : this.model_run
            }
            this.actions.run = obj;
        },
        caseVideo(){
            this.isShowVideo = !this.isShowVideo;
            this.$emit('setVideoShow', this.isShowVideo);
        },
    },
    watch: {
        isFocus(val) {
            if (val) {
                this.startTime();
            } else {
                this.clearTime();
            }
        }
    },
    created() {
        this.stateInit(this.status);
        this.startTime();
    },
    destroyed() {
        this.clearTime();
    }
}
</script>

<style scoped lang="less">
.flowButtons {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    display: flex;
    flex-wrap: wrap;
    background: #f5f5f5;
    border-radius: 0 26px 26px 0;
    padding-left: 10px;
}

.main-content {
    &__header {
        display: flex;
        align-items: center;
        padding: 10px 0;

    }
    &_item {
        padding: 4px 8px;
        background: #ffffff;
        border-radius: 40px;
        margin-right: 7px;
        cursor: pointer;
        border: 1px solid transparent;
        position: relative;
        > span {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.85);
            margin-left: 4px;
        }

        > i {
            font-size: 14px;
            color: #1890ff;
        }
    }
    &_item:nth-child(2n+2) {
        margin-right: 15px;
        >.ce-driver {
            height: 24px;
            border-left: 1px solid  rgba(0,0,0,0.12);
            position: absolute;
            right: -8px;
            top:50%;
            transform: translate(0 , -50%);
        }
    }

    &__tip {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        position: absolute;
        top: 50px;
        left: 130px;

        i {
            font-size: 16px;
            //color: #00c08c;
            margin-right: 7px;
        }
        > span {
            position: fixed;
        }
    }

    &_item:hover {
        border: 1px solid #0088ff;
        box-shadow: 0 2px 4px 0 rgba(0, 136, 255, 0.12);

        span {
            color: #1890ff;
        }
    }
}
</style>
