<template>
<div v-loading="settings.loading">
    <el-form ref="form" class="addEntity" :model="form" label-position="right" label-width="120px">
        <el-form-item v-for="(item , k) in layoutForm"
                      :key="k"
                      :prop="k"
                      :label="`${item.label}:`"
                      :rules="item.rules"
        >
            <dg-select v-if="item.type==='typeSelect'"
                        size="mini"
                        v-model="form[k]"
                        filterable
                        @change="typeChange"
                        clearable
                        :placeholder="item.placeholder"
                        :data="item.data"
            ></dg-select>
            <dg-select v-if="item.type==='select'"
                        size="mini"
                        v-model="form[k]"
                        clearable
                        filterable
                        :placeholder="item.placeholder"
                        :data="item.data"
            ></dg-select>
            <ce-select-drop
                    
                    v-if="item.type === 'classifySelectTree'"
                    ref="pos_tree"
                    :placeholder="item.placeholder"
                    :props="defaultProps"
                    filterable
                    check-leaf
                    check-strictly  
                    visible-type="leaf"
                    :filterNodeMethod="filterNode"
                    v-model="form[k]"
                    :data="item.option"
                    @current-change="selectTreeChange($event, row)"
            ></ce-select-drop>
        </el-form-item>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </el-form>
</div>
</template>

<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";

export default {
    name: "settings",
    mixins:[commonMixins],
    props : {
        rowData : Object,
        codeTableData : Array,
    },
    data(){
        return {
            isEdit : false,
            form : {
                relationTableId : "" ,
                codeTypeColumn : "" ,
                codeTable : "",
            },
            defaultProps: {
                value: 'id',
                label: "name",
                children: "children",
            },
            layoutForm : {
                relationTableId : {
                    label : "选择数据集" ,
                    type : 'classifySelectTree' ,
                    placeholder : "请选择选择数据集",
                    rules :[
                        {required : true , message : "请选择选择数据集" , trigger : [ 'change']}
                    ],
                    option :[],
                },
                codeTypeColumn : {
                    label : "代码类型字段" ,
                    type : 'typeSelect' ,
                    placeholder : "请选择代码类型字段",
                    data : [],
                    rules :[
                        {required : false , message : "请输入实体名称" , trigger : ['blur' , 'change']}
                    ]
                },
                codeTable : {
                    label : "代码表" ,
                    type : 'select' ,
                    placeholder : "请选择代码表",
                    data : [],
                    rules :[
                        {required : false , message : "请选择代码表" , trigger : ['blur' , 'change']}
                    ]
                },
            },
        }
    },
    methods : {
        filterNode : treeMethods.methods.filterNode ,
        async selectTreeChange(val, isInit = false) {
            const vm = this, {settings} = vm;
            let services = vm.$services("dataReady");
            if (val.id === '') return;
            let data = {
                addDataSetId:'',
                currentDataSetId:val.id,
            };
            settings.loading = true;
            vm.form.codeTypeColumn = "";
            vm.form.codeTable  = "";
            vm.layoutForm.codeTypeColumn.data = [];
            await services.getLogicDataColumn(data,settings).then(res => {
                if(res.data.status === 0) {
                    let result = res.data.data;
                    result.columns[0].dimension.forEach(item => {
                        let object = {
                            label : item.name || item.code,
                            value : item.code,
                        }
                        vm.layoutForm.codeTypeColumn.data.push(object);
                    })
                }
            })
            // if(!isInit) this.$emit('tableChange', val)
        },
        close(){
            this.isEdit = false;
            this.$emit("close");
        },
        submit(){
            const vm = this;
            vm.$emit('submit', this.form);
            vm.close();
        },
        async typeChange() {
            const vm = this, {settings} = vm;
            let services = vm.$services("plugin3");
            vm.form.codeTable = "";
            if (vm.form.codeTypeColumn === '') return;
            settings.loading = true;
            vm.layoutForm.codeTable.data = [];
            let codeTypeColumnLabel = vm.layoutForm.codeTypeColumn.data.filter(item => item.value === vm.form.codeTypeColumn)[0].label;
            await services.prviewDataWithColumn({dataSetId : vm.form.relationTableId, addDataSetId:"", "page": 1,"pageSize": 1000,
                                            "columnList": [codeTypeColumnLabel], settings}).
                                        then(res => {
                                            let result = []
                                            res.data.data.dataList.forEach(element => {
                                                let flag = false;
                                                result.forEach(item =>{
                                                    if (item.value === element[codeTypeColumnLabel]) flag = true;
                                                })
                                                if (!flag) {
                                                    result.push({
                                                        label : element[codeTypeColumnLabel],
                                                        value : element[codeTypeColumnLabel],
                                                    })
                                                }
                                            });
                                            vm.layoutForm.codeTable.data = result;
            })
        },
        async init() {
            const vm = this;
            if (!this.rowData) return;
            await this.selectTreeChange({id : vm.rowData.relationTableId}, true);
            vm.layoutForm.relationTableId.option = vm.codeTableData;
            vm.form.relationTableId = JSON.parse(JSON.stringify(vm.rowData.relationTableId));
            vm.form.codeTypeColumn =  JSON.parse(JSON.stringify(vm.rowData.codeTypeColumn ));
            if(vm.form.relationTableId!=='')await this.typeChange();
            // vm.layoutForm.codeTypeColumn.data = vm.rowData.codeTableNameData;
            vm.form.codeTable = JSON.parse(JSON.stringify(vm.rowData.codeTable ));
            
        }
    },
    created() {
        this.init();
    },
    watch : {

    }
}
</script>

<style scoped>

</style>
