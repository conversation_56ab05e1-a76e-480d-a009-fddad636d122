<template>
    <div class="preview-result">
        <common-table
                :data="tableData"
                :columns="tableHead"
                :max-height="maxH"
                :pagination="false"
        >
            <template slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action"
                           v-for="(item,index) in operateIcon"
                           :key="index"
                          v-if="item.condition"
                    >
                        <dg-button type="text"
                                   class="icon"
                                   size="medium"
                                   :class="item.class"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.tip"
                                   v-html="item.icon"
                        ></dg-button>
                    </span>
            </template>
        </common-table>
        <data-preview ref="preview"></data-preview>
        <issue ref="issue" @closeService="closeServiceIssue" :row=rowData v-if="isShowIssue"></issue>
    </div>
</template>
<script src="./preview-result.js"></script>