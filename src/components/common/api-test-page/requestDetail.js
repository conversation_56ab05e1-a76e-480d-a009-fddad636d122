export const RequestDetail = {
    data() {
        return {
            placeholder_p: '请输入参数值',
            requestTableData: [],
            requestTHeadData: [
                {
                    prop: "paramName",
                    label: "参数名",
                    minWidth: "160",
                }, {
                    prop: "defaultValue",
                    label: "参数值",
                    minWidth: "160",
                }
            ],
            formList: {
                name: {
                    label: 'API名称',
                    type: 'text',
                },
                interName: {
                    label: '接口名称',
                    type: 'select',
                    placeholder: '请选择接口名称'
                },
                requestUrl: {
                    label: '请求地址',
                    type: 'text'
                },
                params: {
                    label: '请求参数',
                    type: 'table'
                }
            },
            form: {
                name: '',
                interName: 'postRequest',
                requestUrl: '',
                baseUrl: '',
                subUrl: '',
            },
            requestObj: {code: 'requestId', memo: '请求标识', data_type: 'String', is_must: 't', example: ''},
            taskId: '',
            batchQuery: false,
            apiRes: null,
            isKafka: false,
            useCaseApiParams: [],//用例参数
        }
    },
    computed: {
        isSubscript() {
            return this.form.interName === 'subscript';
        }
    },
    methods: {
        async isKafkaWorker() {
            const vm = this;
            let services = vm.$services("serviceManage");
            await services.getServiceOutput().then(res => {
                if (res.data.status === 0) {
                    vm.isKafka = res.data.data === "kafka-storage";
                }
            });
        },
        getInterOption() {
            const {apiRow, apiRes} = this;
            let opt = [
                {
                    label: '发送请求',
                    value: 'postRequest',
                },
            ], getOpt = [], subscript = [];
            if (!this.isKafka && (apiRow.async === 'true' || (apiRes && apiRes.serviceType === '信息核查' && apiRes.singleOrManyTable === '2'))) {
                getOpt = [
                    {
                        label: '获取结果',
                        value: 'getValue',
                    },
                ]
            }
            if (apiRes && apiRes.serviceType === '比对订阅') {
                subscript = [
                    {
                        label: '比对订阅',
                        value: 'subscript',
                    },
                ]
            }
            return [...opt, ...getOpt, ...subscript]
        },
        setRequestUrl() {
            const {form} = this;
            if (form.interName === 'getValue') {
                let url = form.baseUrl;
                let index = url.lastIndexOf("\/");
                let str = url.substring(0, index + 1);
                form.requestUrl = str + "getValue";
            } else if (form.interName === 'postRequest') {
                form.requestUrl = form.baseUrl;
            } else {
                form.requestUrl = form.subUrl;
            }
        },
        /**
         * 表格输入参数校验
         */
        tableValidate(showTip) {
            const vm = this;
            let flag = [];
            for (let i = 0; i < vm.requestTableData.length; i++) {
                vm.validationModel(vm.requestTableData[i], flag);
            }
            if (flag.length && showTip) vm.$message.error(`${flag.join('、')}不能为空!`)
            return !flag.length;
        },
        validationModel(info, flag) {
            if (info.type === 'List' && info.paramCode !== 'params' || info.batchQuery) {
                let par = info.childrenCopy.filter(n => n.isMust === 't' || n.is_must === 't');
                let allName = par.map(li => li.paramName);
                if (info.setDataList && info.setDataList.length) {
                    for (let k in info.setDataList[0]) {
                        let res = par.find(it => it.paramCode === k);
                        if (res && !info.setDataList[0][k]) {
                            flag.push(res.paramName)
                        }
                    }
                    if (!Object.keys(info.setDataList[0]).length) flag.push.apply(flag, allName);
                } else if (par) {
                    flag.push.apply(flag, allName);
                }
            } else {
                if (info.paramCode !== "operatorDataSet" && (info.isMust === 't' || info.is_must === 't') && !info.defaultValue) {
                    flag.push(info.paramName);
                } else if (info.children && info.children.length) {
                    for (let i = 0; i < info.children.length; i++) {
                        this.validationModel(info.children[i], flag);
                    }
                }
            }
        },
        /**
         * 订阅测试参数
         */
        setSubScriptTestVo(form, operatorDataSet) {
            const {requestTableData} = this;
            return {
                url: form.requestUrl,
                operatorDataSet: operatorDataSet,
                params: requestTableData.find(n => n.paramCode === "params") ? requestTableData.find(n => n.paramCode === "params").children.map(n => {
                    return {paramCode: n.paramCode, paramValue: n.defaultValue}
                }) || [] : [],
                startTime: requestTableData.find(n => n.paramCode === "startTime").defaultValue,
                endTime: requestTableData.find(n => n.paramCode === "endTime").defaultValue,
            };
        },
        /**
         * 树层级参数设置
         * @param data
         * @param isClear
         */
        setTreeParams(data, isClear = true) {
            const vm = this;
            if (isClear) vm.useCaseApiParams = [];
            data.forEach(item => {
                if (item.paramCode === 'params') {
                    let object = {}, objectList = [];
                    item.children.forEach(e => {
                        object[e.paramCode] = e.defaultValue;
                        objectList.push(object);
                    })
                    let itemObject = {
                        "parentParamCode": 'params',
                        "params": objectList
                    };
                    vm.useCaseApiParams.push(itemObject);
                } else {
                    if (item.children && item.children.length > 0) vm.setTreeParams(item.children, false);
                    else if (['startTime', 'endTime'].includes(item.paramCode)) {
                        vm.useCaseApiParams.push({
                            "parentParamCode": item.paramcode,
                            "params": item.defaultValue
                        });
                    } else {
                        vm.useCaseApiParams.push({
                            "parentParamCode": item.paramcode,
                            "params": item.setDataList
                        });
                    }
                }

            })
        },
        //设置批量查询参数
        setBatchQueryParams(requestData) {
            let paramObject = {};
            this.useCaseApiParams = [];
            paramObject.parentParamCode = "";
            paramObject.params = requestData[0].setDataList;
            this.useCaseApiParams.push(paramObject);
        },
        //其他默认的参数
        setOtherParams(requestData) {
            let paramObject = {};
            let itemObject = {};
            this.useCaseApiParams = [];
            requestData.forEach(item => {
                itemObject[item.paramCode] = item.defaultValue;
            })
            paramObject.parentParamCode = "";
            paramObject.params = [itemObject];
            this.useCaseApiParams.push(paramObject);
        },
        //获取getValue 参数
        setGetParams(requestId){
            let paramObject = {
                parentParamCode:"",
                params : [{requestId}]
            };
            this.useCaseApiParams = [paramObject];
        },
        /**
         * 设置测试参数
         * @return {{operatorDataSet, startTime, endTime, params: *[], url: (string|string|*)}|{pageIndex: number, testDataJson: string, requestId: string, requestUrl: (string|string|*), pageSize: number, serviceId, apiType: string}|ElMessageComponent}
         */
        setTestVo(showTip = false) {
            const vm = this, {apiRow, apiRes} = this;
            if (!apiRes) {
                this.$message.warning("API详情获取异常");
                return null;
            }
            if (!vm.tableValidate(showTip)) return null;
            let {requestTableData, form} = vm;
            let vo = {
                serviceId: apiRow.id,
                testDataJson: "",
                apiType: form.interName,
                requestId: '',
                requestUrl: form.requestUrl,
                pageSize: 10,
                pageIndex: 1
            };
            if (form.interName === 'getValue') {
                vo.requestId = requestTableData[0].defaultValue;
                this.setGetParams(vo.requestId);
                return vo;
            }
            if (['模型分析', '数据碰撞', '比对订阅', '数据查询', 'AI算法', '信息核查'].includes(apiRow.type)) {
                let dataSet = requestTableData.find(n => n.paramCode === "operatorDataSet");
                let operatorDataSet = {};
                if (dataSet && dataSet.children && dataSet.children.length) {
                    dataSet.children.forEach(n => {
                        operatorDataSet[n.paramCode] = n.setDataList ? [...n.setDataList] : [];
                    })
                }
                vm.setTreeParams(requestTableData)
                if (vm.isSubscript) return vm.setSubScriptTestVo(form, operatorDataSet);
                let testDataJson = {
                    operatorDataSet: operatorDataSet,
                    params: requestTableData.find(n => n.paramCode === "params") ?
                        requestTableData.find(n => n.paramCode === "params").children.map(n => {
                            return {paramCode: n.paramCode, paramValue: n.defaultValue}
                        }) || [] : []
                };
                if (['AI算法'].includes(apiRow.type)) {
                    testDataJson = {
                        params: this.getAITestParams(requestTableData)
                    }
                    vm.setOtherParams(requestTableData);
                } else if (['信息核查', '数据查询'].includes(apiRow.type)) {
                    if (vm.batchQuery && (apiRes.serviceType === '数据查询' || (apiRes.serviceType === '信息核查' && apiRes.singleOrManyTable !== '2'))) {
                        testDataJson = requestTableData[0].setDataList;
                        vm.setBatchQueryParams(requestTableData);
                    } else if (apiRes.serviceType !== '信息核查' || apiRes.singleOrManyTable !== '2') {
                        testDataJson = [this.getAITestParams(requestTableData)];
                        vm.setOtherParams(requestTableData);
                    }
                }
                vo.testDataJson = JSON.stringify(testDataJson);
            }
            return vo;

        },
        getAITestParams(data) {
            let params = {};
            data.forEach(tab => {
                params[tab.paramCode] = tab.defaultValue;
            })
            return params;
        },
        //异步地址截取
        getAsyncInfo(taskId) {
            this.taskId = taskId;
        },
        aiGetInputParams(id) {
            const vm = this;
            let services = this.$services('modeling');
            vm.requestTableData = [];
            services.getServiceDetailById(id).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    this.form.name = result.apiName;
                    this.form.baseUrl = result.requestPath;
                    result.inputParams.forEach(n => {
                        vm.setParamName(n);
                    })
                    result.inputParams.forEach(element => {
                        element.showInput = true;
                        element.children.forEach(i => {
                            i.showInput = true;
                            vm.requestTableData.push(i);
                        })
                    })
                }
            })
        },
        /**
         * 设置请求参数表格数据
         * @param result
         * @param showTime
         */
        async setReqTableData(result, showTime) {
            const vm = this;
            let paramList = result.paramList || [];
            let arrayInfo = paramList.sort((a, b) => {
                return b.paramCode.localeCompare(a.paramCode)
            });
            let {batchQuery} = result;
            if ((result.serviceType === '数据查询' || (result.serviceType === '信息核查' && result.singleOrManyTable !== '2')) && batchQuery === '0') {
                arrayInfo.forEach(li => {
                    vm.setParamName(li)
                });
                vm.batchQuery = true;
                let obj = {};
                arrayInfo.forEach(h => {
                    if (h.defaultValue) {
                        obj[h.paramCode] = h.defaultValue;
                    }
                })
                let item = {
                    paramName: '批量查询参数',
                    paramCode: '批量查询参数',
                    childrenCopy: arrayInfo,
                    setDataList: [obj],
                    batchQuery: true
                };
                if (vm.row && vm.row.paramObject && vm.row.paramObject.useCaseApiParams && vm.row.paramObject.apiType === 'postRequest') {
                    let defList = vm.row.paramObject.useCaseApiParams;
                    item.setDataList = defList.length && defList[0].params || [{}];
                }
                vm.requestTableData.push(item);
            } else {
                arrayInfo.forEach(n => {
                    vm.setParamName(n);
                    if (result.serviceType === '比对订阅' && !showTime) {
                        if (n.paramCode !== 'startTime' && n.paramCode !== 'endTime' && n.paramCode !== 'requestId') {
                            vm.requestTableData.push(n)
                        }
                    } else {
                        n.paramCode !== 'requestId' ? vm.requestTableData.push(n) : '';
                    }
                })
            }
        },
        async getRequestDetail(id) {
            const vm = this;
            let services = vm.$services('servicesServices');
            return await services.queryServiceDetails(id, vm.loadParams).then(res => {
                if (res.data.code === 0) {
                    vm.apiRes = res.data.data;
                    vm.form.name = vm.apiRes.interfaceChineseName;
                    vm.form.baseUrl = vm.apiRes.requestPath;
                    vm.form.subUrl = vm.apiRes.relativePath;
                    vm.setRequestUrl();
                    return vm.apiRes;
                }
            })
        },
        /**
         * 获取模型服务信息
         * showTime : boolean 比对订阅 订阅测试显示时间参数
         */
        async getModelServicesInfo(id, showTime = false) {
            const vm = this;
            vm.requestTableData = [];
            let result = await vm.getRequestDetail(id);
            if (!result) return;
            vm.setReqTableData(result, showTime);
            let operatorDataSet = vm.requestTableData.find(n => n.paramCode === "operatorDataSet");
            let hasRequestId = vm.requestTableData.find(n => n.paramCode === "requestId");
            if (hasRequestId) hasRequestId.showInput = true;
            if (operatorDataSet && operatorDataSet.children) {
                operatorDataSet.children.forEach(n => {
                    if (n.type === 'List') {
                        n.childrenCopy = n.children ? n.children : [];
                        n.children = [];
                        n.setDataList = [];
                        let obj = {};
                        n.childrenCopy.forEach(h => {
                            if (h.defaultValue) {
                                obj[h.paramCode] = h.defaultValue;
                            }
                        })
                        n.setDataList.push(obj);
                        if (vm.row && vm.row.paramObject && vm.row.paramObject.useCaseApiParams && vm.row.paramObject.apiType === 'postRequest') {
                            let defList = vm.row.paramObject.useCaseApiParams.filter(e => e.parentParamCode === n.paramCode);
                            n.setDataList = defList.length && defList[0].params || [{}];
                        }
                    }
                })
            }
        },
        //设置paramName
        setParamName(info) {
            info.paramName = info.memo || info.paramName || info.name || info.paramCode;
            info.paramcode = info.paramCode || info.code;
            if (!['map', 'list'].includes(info.type.toLowerCase())) {
                info.defaultValue = "";
                info.showInput = true;
            }
            info.defaultValue = info.type === 'Time' && info.example ? new Date(info.example) : info.example;
            if (this.row && this.row.paramObject && this.row.paramObject.useCaseApiParams && this.row.paramObject.useCaseApiParams.length && this.row.paramObject.apiType === 'postRequest') {
                info.defaultValue = this.row.paramObject.useCaseApiParams[0].params[0]
                    && this.row.paramObject.useCaseApiParams[0].params[0][info.paramCode] || "";
            }
            if (info.children && info.children.length) {
                info.children.forEach(n => {
                    this.setParamName(n);
                })
            }
        },
        /**
         * modelService 模型分析
         * dataCollision 数据碰撞
         * compareService 比对订阅
         * aiService AI算法
         * informationVerification 信息核查
         * logicService 逻辑服务
         * recommendService 推荐服务
         * dataService 数据查询
         *
         */
        async getApiDetail() {
            const {apiRow} = this;
            let id = apiRow.serviceMetaId;
            await this.isKafkaWorker();
            //批量测试 设置回显
            if (apiRow.reqTableData) {
                await this.getRequestDetail(id);
                this.requestTableData = JSON.parse(JSON.stringify(apiRow.reqTableData));
                this.form.interName = apiRow.interName || "postRequest";
                if (apiRow.requestUrl) this.form.requestUrl = apiRow.requestUrl;
                return;
            }
            if (['模型分析', '数据碰撞', '信息核查', '比对订阅', '逻辑服务', '推荐服务', '数据查询', 'AI算法'].includes(apiRow.type)) {
                await this.getModelServicesInfo(id);
            }
        },
        async backApiData(){
            const {row} = this;
            this.setTestVo();
            if (row && row.paramObject && row.paramObject.useCaseApiParams) {
                let {apiType} = row.paramObject;
                this.form.interName = apiType;
                await this.interChange(apiType);
            }
        },
        async interChange(val) {
            const {row} = this;
            if (val === 'postRequest') {
                await this.getApiDetail();
            } else if (val === 'getValue') {
                await this.setGetResultData();
            } else if (val === 'subscript') {
                let id = this.apiRow.serviceMetaId;
                await this.getModelServicesInfo(id, true);
            }
            if (row && row.requestTableData && row.paramObject && val === row.paramObject.apiType){
                this.requestTableData = row.requestTableData;//回显设置参数
            }
            this.setRequestUrl();
        },
        /**
         * 设置获取结果 表格数据
         */
        setGetResultData() {
            let {requestObj , row} = this;
            let param = Object.assign({}, requestObj);
            let taskId ;
            if(row && row.paramObject) {
                taskId = row.paramObject.useCaseApiParams[0].params[0].requestId;
            }
            param.paramName = param.code;
            param.paramCode = param.code;
            param.isMust = param.isMust === 't';
            param.example = this.taskId || taskId;
            param.defaultValue = param.example;
            param.showInput = true;
            this.requestTableData = [param];
        },
        async init(){
            await this.getApiDetail();
            await this.backApiData();
        }
    },
    created() {
        this.init();
    }
}
