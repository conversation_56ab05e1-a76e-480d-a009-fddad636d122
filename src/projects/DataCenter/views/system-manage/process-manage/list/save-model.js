/**
 * 模型另存为
 * @type {{methods: {}}}
 */
export const SaveModel = {
    methods: {
        getTransForSaveProps(row) {
            let modelTypeName = "模型";

            const loadTreeInter = this.loadTransTree;
            const saveInter = (...args) => this.transSaveAs(row, ...args);
            const custom = this.$globalConfig.showVersionAndFirm ? ['version', 'productionFirm'] : [];
            const showItems = ['transName', 'pos', 'description', ...custom];
            return {
                modelTypeName,
                loadTreeInter,
                saveInter,
                showItems,
            }
        },
        loadTransTree(settings) {
            return this.$services("modeling").queryTransTree('', '', 'TRANS_DIR_MF', settings).then(res => {
                if (res.data.status === 0) return res.data.data;
            })
        },
        // 模型另存为
        transSaveAs(row, params, settings) {
            const data = {
                transId: row.resource_id,
                classifyId: params.classifyId,
                transName: params.transName,
                memo: params.memo,
                version: params.version,
                productionFirm: params.productionFirm,
            };
            return this.$services("modeling").modelSaveOther(data, settings);
        },
        getVisualProps(row) {
            const modelTypeName = '仪表盘';
            const loadTreeInter = this.loadVisualTree;
            const saveInter = (...args) => this.visualSaveAs(row, ...args);
            return {
                modelTypeName,
                loadTreeInter,
                saveInter,
                showItems: ['transName', 'pos'],
                treeWrapHeight: '350px',
            }
        },
        // 可视化另存为
        visualSaveAs(row, params, settings) {
            return this.$services('visual').copy({
                id: row.resource_id,
                groupId: params.classifyId,
                dashboardName: params.transName
            }, settings)
        },
        // 加载可视化数
        loadVisualTree(settings) {
            return this.$services('visual').queryTree(settings).then(res => {
                if (res.data.status === 0) return this.setTreeData(res.data.data)
            })
        },
        // 处理可视化树的数据
        setTreeData(data) {
            const vm = this;
            let myList = data.myList;
            let my = {
                label: '我的空间',
                name: 'myFile',
                id: '1',
                pId: "0",
                icon: "dg-iconp icon-notebook",
                isParent: true,
                currentUserId: vm.userInfo.id,
                children: vm.getChildList(myList, "1")
            };
            return [my];
        },
        getChildList(list, pId) {
            if (!list) return;
            const vm = this;
            return list.map(d => {
                let children = d.children && d.children.length ?
                    vm.getChildList(d.children, d.id) : [];
                return {
                    ...d,
                    id: d.id,
                    pId: pId,
                    name: d.name,
                    label: d.name,
                    isParent: false,
                    children,
                    isActive: false,
                    currentUserId: d.operateUserId,
                };
            });
        },
        getPortalProps(row) {
            const modelTypeName = '模型主题';
            const loadTreeInter = this.loadPortalTree;
            const saveInter = (...args) => this.portalSaveAs(row, ...args)
            return {
                modelTypeName,
                loadTreeInter,
                saveInter,
                showItems: ['transName', 'pos'],
                treeWrapHeight: '350px',
            }
        },
        loadPortalTree(settings) {
            return this.$services('portal').queryPortalTree(settings).then(res => {
                if (res.data.status === 0) return res.data.data[0].children.filter(child => child.id !== '2');
            })
        },
        portalSaveAs(row, params, settings) {
            return this.$services('portal').copy({
                id: row.resource_id,
                classifyId: params.classifyId,
                portalName: params.transName,
            }, settings)
        }
    }
}
