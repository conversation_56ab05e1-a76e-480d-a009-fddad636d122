<template>
    <div>


        <pgChoice ref="pg" :isAdd="isAdd" v-if="keyId.toUpperCase() === 'POSTGRESQL'"
                  @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </pgChoice>
        <gpChoice ref="gp" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'GREENPLUM'"
                  @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </gpChoice>
        <TBaseChoice ref="tbase" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'TBASE'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </TBaseChoice>
        <verticaChoice ref="vertica" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'VERTICA'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </verticaChoice>
        <!--<zkChoice ref="zk"  v-else-if="keyId === 'zookeeper'" @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp"></zkChoice>-->
        <esChoice ref="es" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'ELASTICSEARCH'"
                  @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </esChoice>
        <mysqlChoice ref="mysql" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'MYSQL'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </mysqlChoice>
        <redisChoice ref="redis" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'REDIS'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </redisChoice>
        <kafkaChoice ref="kafka" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'KAFKA'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </kafkaChoice>
        <oracleChoice ref="orcl" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'ORACLE'"
                      @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </oracleChoice>

        <libraChoice ref="libra" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'HWMPP'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </libraChoice>
        <hbaseChoice ref="hbase" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'HBASE'"
                     @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" :zkVersionList="zkVersionList"
                     @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </hbaseChoice>
        <hiveChoice ref="hive" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'HIVE'"
                    @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </hiveChoice>
        <gbaseChoice ref="gbase" :isAdd="isAdd" v-else-if="keyId.toUpperCase() === 'GBASE'"
                    @changeDataSetInfo="changeDataSetInfo" :dataVersion="dataVersion" @serachIp="serachIp">
            <slot slot="custom" name="customChoice"></slot>
        </gbaseChoice>
    </div>
</template>

<script>
import pgChoice from "./fchoice/pgChoice"
import gpChoice from "./fchoice/gpChoice"
import zkChoice from "./fchoice/zkChoice"
import esChoice from "./fchoice/esChoice"
import oracleChoice from "./fchoice/OracleChoice"
import libraChoice from './fchoice/libraChoice'
import hbaseChoice from './fchoice/hbaseChoice'
import hiveChoice from './fchoice/hiveChoice'
import mysqlChoice from './fchoice/mysqlChoice'
import redisChoice from "./fchoice/redisChoice";
import kafkaChoice from "./fchoice/kafkaChoice";
import TBaseChoice from "./fchoice/TBaseChoice";
import verticaChoice from "./fchoice/verticaChoice";
import gbaseChoice from "./fchoice/gbaseChoice";
export default {
    name: "Choice",
    components: {
        redisChoice,
        kafkaChoice,
        pgChoice,
        gpChoice,
        zkChoice,
        esChoice,
        oracleChoice,
        libraChoice,
        hbaseChoice,
        hiveChoice,
        mysqlChoice,
        TBaseChoice,
        verticaChoice,
        gbaseChoice,
    },
    props: {
        keyId: String,
        dataVersion: Array,
        zkVersionList: Array,
        isAdd: Boolean,
    },
    data() {
        return {
            ruleForm: {
                "POSTGRESQL": "pg",
                "GREENPLUM": "gp",
                "ZOOKEEPER": "zk",
                "ELASTICSEARCH": "es",
                "ORACLE": "orcl",
                "HWMPP": "libra",
                "HBASE": "hbase",
                "HIVE": "hive",
                "MYSQL": "mysql",
                "REDIS": "redis",
                "KAFKA": "kafka",
                "TBASE": "tbase",
                "VERTICA" : "vertica",
                "GBASE" : "gbase",
            }
        }
    },
    methods: {
        changeDataSet(val) {
            let key = this.keyId.toUpperCase();
            this.$refs[this.ruleForm[key]].setDataSetInfo(val);
        },
        changeDataSetInfo(val) {
            this.$emit("changeDataSetInfo", val);
        },
        serachIp(val) {
            this.$emit("serachIp", val);
        },
        valiatorRule() {
            let key = this.keyId.toUpperCase();
            return this.$refs[this.ruleForm[key]].valdate();
        },
        getResult() {
            let key = this.keyId.toUpperCase();
            return this.$refs[this.ruleForm[key]].dataSetInfo;
        }
    }
}
</script>

<style scoped>

</style>
