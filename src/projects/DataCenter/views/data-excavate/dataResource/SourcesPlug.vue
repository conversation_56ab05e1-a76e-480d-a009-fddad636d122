<template>
    <div class="sourcesPlug" ref="plug_box">
        <div class="showIcon">
            <div class="sources-plug"
                 draggable="true"
                 v-for="(plug , inx ) in storeData"
                 :key="inx"
                 @dragstart="addNode(plug)"
                 @dragend="dragend"
                 :title="plug.label"
            >
                <div class="m-icon">
                    <svg class="eda_icon" aria-hidden="true">
                        <use :xlink:href="plug.icon"></use>
                    </svg>
                </div>
                <p class="m-name">{{plug.label}}</p>
            </div>
        </div>
        <div class="arrow-box">
            <div class="arrow-prev" :class="{'active':iconIndex !== 0}" @click="prevData"></div>
            <div class="arrow-next" :class="{'active':iconIndex +  pageCount < iconData.length}" @click="nextData"></div>
        </div>
    </div>
</template>

<script>
    import $ from 'jquery'
    import { globalBus } from '@/api/globalBus';
    export default {
        name: "SourcesPlug",
        props:{
            sourceData:Array,
        },
        data(){
            return {
                storeData : [],
                iconH : 80 ,  //高度80
                colCount : 3,//一行三个图标
                iconIndex : 0 ,//显示的图标 最后的索引
                pageCount : 9 ,//一页图标数
                iconData : []
            }
        },
        methods : {
            dragend(){
                globalBus.$emit('dragend');
            },
            addNode(plug){
                globalBus.$emit('plugNode', plug);
            },
            initData(data){
                this.iconData = data;
                this.initIcon();
            },
            initIcon(){
               this.getPageCount();
               this.createFilter();
            },
            createFilter(){
                this.storeData = this.iconData.filter((icon , i) =>{
                    return  i < this.pageCount + this.iconIndex && i >= this.iconIndex ;
                });
            },
            nextData(){
                if(this.iconIndex +  this.pageCount < this.iconData.length){
                    this.getPageCount();
                    this.switchEffect(() =>{
                            this.iconIndex += this.pageCount;
                            this.createFilter();
                    });
                }
            },
            prevData(){
                if(this.iconIndex > 0){
                    this.getPageCount();
                    this.switchEffect(() =>{
                        this.iconIndex = this.iconIndex - this.pageCount < 0 ? 0 : this.iconIndex - this.pageCount;
                        this.createFilter();
                    });
                }

            },
            switchEffect(dataRenew){
                let $plug_box = $(this.$refs.plug_box).children('.showIcon');
                $plug_box.slideUp(300);
                setTimeout(() =>{
                    dataRenew();
                    $plug_box.fadeIn(300);
                },400);
            },
            getPageCount(){
                let contH = $(this.$refs.plug_box).parent('.list-cont-item').height();
                let rowCount = Math.floor(contH / this.iconH);
                this.pageCount = rowCount * this.colCount;
            },
            setDefault(arr){
                this.storeData = arr;
                this.iconData = arr;
                this.initIcon();
            }

        },
        mounted(){
            $(window).resize(this.initIcon);
            this.setDefault(this.sourceData);
        },
        created() {

        }

    }
</script>

<style scoped lang="less">

    .sourcesPlug {
        overflow: hidden;

    }
    .sources-plug {
        width: 33.3%;
        float: left;
        padding: 10px 2px;
        text-align: center;
        box-sizing: border-box;
    }
    .m-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin: auto;
        text-align: center;
    }

    .m-icon .eda_icon {
        width: 40px;
        height: 36px;
    }

    .m-name {
        font-size: 1rem;
        display: block;
        width: 100%;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: @main-font;
    }
    .arrow-box {
        overflow: hidden;
        padding:0 10px;
        position: absolute;
        bottom: 5px;
        width:100%;
        box-sizing: border-box;
    }
    .arrow-next {
        background:url("../../../assets/images/arrow/arrow-next-b.png") no-repeat center;
        width: 18px;
        height: 15px;
        float: right;
        cursor: not-allowed;
    }
    .arrow-prev {
        background:url("../../../assets/images/arrow/arrow-prev-b.png") no-repeat center;
        width: 18px;
        height: 15px;
        float: left;
        cursor: not-allowed;
    }
    .arrow-next.active {
        cursor: pointer;
        background:url("../../../assets/images/arrow/arrow-next.png") no-repeat center;
    }
    .arrow-prev.active {
        cursor: pointer;
        background:url("../../../assets/images/arrow/arrow-prev.png") no-repeat center;
    }
</style>
