import {treeMethods} from "@/api/treeNameCheck/treeName"
import * as dataSourcesServices from "@/projects/DataCenter/views/dataSources/service-mixins/service-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../service-mixins/service-mixins";
import {mapGetters, mapActions,mapState} from "vuex"
import {DatasetMixins} from "@views/dataSpace/dataReady/dialog/dataset-tree-panel/dataset-mixins"
import {cloneDeep} from "lodash";

export default {
    name: "tree",
    mixins: [treeMethods, dataSourcesServices.servicesMixins, commonMixins, servicesMixins, DatasetMixins],
    computed: {
        ...mapGetters(["userRight","userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        // 移动数据集使用到的 目录数据
        catalogData(){
            let data = this.isCatalog ? this.data : this.storeCatalog;
            this.filterOtherCatalog({data});
            return this.myCatalog;
        },
        ...mapState({
            myCatalog: (state) => state.plans.myCatalog,
            otherUserCatalogId: (state) => state.plans.otherUserCatalogId,
            // 可编辑节点 非分享，非他人目录(不等于他人目录 Id ，没有用户 id 值)
            nodeEditable: (state) =>{
                return (data) => data.id !== state.plans.shareId && data.id !== state.plans.otherUserCatalogId && data.currentUserId === state.user.userInfo?.id;
            }
        }),
        disableClick(){
            return (data) => data.disabled;
        }
    },
    props: {
        hasSave: Boolean, //显示操作按钮
        editDir: Boolean, //弹窗编辑树
        dirId: String, //目录选中Id
        dirLevel: Number, //目录可显示层级
        curId: String,//移动节点Id
        active: String, //选中tab
        showTreeType: { // 显示数据集目录分类、数据来源分类
            type: Boolean,
            default: false,
        }
    },
    watch: {
        filterText(val) {
            this.$refs.dgtree.filter(val);
            this.$refs.tree.filter(val);
        }
    },
    data() {
        return {
            defaultProps: {
                value: "id",
                label: "name",
                children: "children"
            },
            treeMenu: [],
            addBtnTxt: '新增子目录',
            menu: [
                // {
                //     name : "新增目录",
                //     fn : this.newTreeDir ,
                //     show : (right , node) => node && node.level < 4
                // },addChildren
                {
                    name: "新增子目录",
                    fn: this.newTreeDir,
                    show: (right, node) => node && node.level < 5
                },
                {
                    name: "移动目录到",
                    fn: this.showMoveLog,
                    show: () => true
                },
                {
                    name: '重命名目录',
                    fn: this.editNode,
                    show: () => true
                },
                //分割线
                {
                    name: "divider",
                    show: () => true
                }, {
                    name: '删除目录',
                    fn: this.deleteNode,
                    show: () => true
                }
            ],
            currentId: "",
            tabActive: 'myData',
            sourceDatasetTree: [],
            expandedKeys: [],
            curData: {},
            expandData: [],
            storeCatalog: [],// 暂存目录，移动数据集使用
        }
    },
    methods: {
        ...mapActions(["getTypeList","filterOtherCatalog"]),
        //目录选中
        setSelectDir(nodeId, type) {
            if (type === 'add') {
                this.$nextTick(() => {
                    this.setSelectNode(nodeId);
                });
            } else {
                this.setSelectNode(nodeId);
            }
        },
        setSelectNode(nodeId){
            this.expandData.push(nodeId);
            this.$refs.tree.setCurrentKey(nodeId);
            let nodeD = this.$refs.tree.getNode(nodeId);
            this.nodeClick(nodeD.data, nodeD);
        },
        //移动目录
        moveTreeDir(curNode, parentNode) {
            const vm = this;
            let moveToNode = curNode;
            moveToNode.pId = parentNode.id;
            vm.$refs.tree.remove(curNode);
            let parent = vm.$refs.tree.getNode(parentNode);
            this.$refs.tree.append(moveToNode, parent);
            vm.expandData = [parentNode.id, curNode.id];
            vm.$nextTick(() => {
                if (vm.$refs.tree) {
                    vm.$refs.tree.setCurrentKey(vm.currentId);
                    vm.nodeClick(moveToNode, parent);
                }
            });
        },
        //移动目录到
        showMoveLog() {
            this.$emit("moveToDir", this.selectedData, this.selectedNode);
        },
        //新增目录
        newTreeDir(data) {
            let dir = data || this.selectedData;
            this.$emit("addTreeDir", dir);
        },
        setTree() {
            let data = this.active === 'myData' ? this.data : this.sourceDatasetTree;
            this.filterOtherCatalog({data});
            this.$emit("setTreeData", this.myCatalog);
        },
        /**
         * 移动时 过滤当前节点
         */
        filterCurNode(data) {
            const vm = this, {curId} = vm;
            let result;
            if (curId) result = data.filter(item => item.id !== curId);
            else result = data;
            return result;
        },
        setTreeNode(data, level) {
            const vm = this, {editDir, dirLevel, filterCurNode, curId} = vm;
            let child_l = level + 1;
            return filterCurNode(data).map(item => {
                let disabled = curId ? level >= dirLevel : editDir && dirLevel !== undefined ? level >= dirLevel - 1 : false;
                let children = item.children && item.children.length && !disabled ? vm.setTreeNode(item.children, child_l) : [];
                let icon = item.code === 'DATA_SET_DIR_STANDARD' ?
                    'dg-iconp icon-journal-b' : item.code === 'DATASET_DIR_MY' ? 'dg-iconp icon-notebook' :
                        item.code === '来自分享' ? 'el-icon-share' : 'el-icon-folder';
                return {
                    id: item.msg ? item.id + `_${level}` : item.id, // 来自分享的分类数据存在重复 id，在点击事件再去掉 _${level}
                    label: item.name,
                    name: item.name,
                    children,
                    icon,
                    isParent: true,
                    pId: item.pId === "-1" ? "0" : item.pId,
                    dirType: item.dirType,
                    operateTime: item.operateTime,
                    level,
                    code: item.code,
                    isActive: false,
                    msg: item.msg,
                    currentUserId: item.currentUserId,//其他用户目录的用户 Id
                    disabled: item.id === vm.otherUserCatalogId,
                }
            });
        },
        /**
         * 按目录分类 获取数据集目录
         * @returns {*}
         */
        getDatasetByCatalog(isLoading) {
            const vm = this, {$services, dirId} = vm;
            let has = false;
            if (this.hasDataObj) {
                has = true;
            }
            let settings;
            if (isLoading) {
                settings = vm.settings;
                settings.loading = true;
            }
            vm.expandData = [];
            return $services("dataReady").queryDataSetTree(has, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    let curData = result[0].children.filter(d => d.code === "DATASET_DIR_MY")[0],
                        curId = curData ? curData.id : "";
                    vm.curData = curData;
                    if (dirId) curId = dirId;
                    vm.data = vm.setTreeNode(result[0].children, 1);
                    vm.setExpandData(vm.data);
                    if (vm.editDir) { //弹窗时移除“来自分享节点”
                        vm.filterOtherCatalog({data:vm.data});
                        vm.data = vm.myCatalog;
                    }
                    vm.selectedData = vm.data.filter(d => d.code === "DATASET_DIR_MY")[0];
                    vm.$nextTick(() => {
                        vm.expandData.push(curId);
                        // 即席 sql 分析跳转，已弃用先注释
                        /*let {treeNodeId, treeNode} = vm.$route.params;
                        if (treeNode && treeNode.treeType !== "sourceDataSetTree" && treeNodeId) {
                            curId = treeNodeId;
                            vm.nodeClickByKey(treeNodeId);
                            // vm.active = "myData";
                        } else if (!treeNode) {
                            vm.$emit("initTable", curData);
                        }*/
                        vm.currentId = curId;
                        vm.$nextTick(() => {
                            if (vm.$refs.tree && curId) {
                                vm.$refs.tree.setCurrentKey(curId);
                                let currentNode = vm.$refs.tree.getCurrentNode();
                                let nodeD = vm.$refs.tree.getNode(currentNode);
                                vm.nodeClick(currentNode, nodeD);
                            }
                        });
                    });
                    vm.setTree();
                }
            });
        },
        getDatasetBySource(isLoading) {
            const vm = this, {$services} = vm;
            // 切换分类前 保存目录的数据
            this.storeCatalog = cloneDeep(this.data);
            let settings;
            if (isLoading) {
                settings = vm.settings;
                settings.loading = true;
            }
            vm.expandData = [];
            return this.getTypeList({settings, $services}).then(result => {
                if (!result.length) return;
                let curData = result[0],
                    curId = curData ? curData.id : "";
                vm.curData = curData;
                vm.data = vm.setTreeNode(result, 1);
                vm.setExpandData(vm.data);
                vm.currentId = curId;
                vm.$nextTick(() => {
                    if (vm.$refs.tree) {
                        vm.$refs.tree.setCurrentKey(curId);
                        let currentNode = vm.$refs.tree.getCurrentNode();
                        let nodeD = vm.$refs.tree.getNode(currentNode);
                        vm.nodeClick(currentNode, nodeD);
                    }
                });

            })
        },
        getDatasetCurNode() {
            let data;
            const {treeType} = this;
            if (treeType === "catalogue") {
                data = this.data.filter(d => d.code === "DATASET_DIR_MY")[0];
            } else if (treeType === "datasource") {
                data = this.data[0];
            }
            this.$refs.tree.setCurrentKey(data);
            this.$emit("initTable", data);
            this.setTree();
        },
        loadDatasetBySource(){
            const {$services} = this;
            return this.getTypeList({$services});
        },
        /**
         * 初始化树
         * @return {Promise<void>}
         */
        async initTree() {
            const vm = this, {settings} = this;
            settings.loading = true;
            Promise.all([
                vm.treeTypeChange(false),
                vm.getDataSourceTree(),
                vm.loadDatasetBySource(),
            ]).finally(() => {
                settings.loading = false;
            })
        },
        setDataSourceTreeNode(data, level) {
            const vm = this;
            let child_l = level + 1;
            return data.map(item => {
                let disabled = item.instanceType && item.instanceType.toUpperCase() === 'ELASTICSEARCH';
                item.children = item.children && item.children.length && !disabled ? vm.setDataSourceTreeNode(item.children, child_l) : [];
                return item;
            });
        },
        //初始化数据仓库的树
        getDataSourceTree() {
            const vm = this, {$services} = this;
            return $services("dataSource").getDataSourceTree(null, vm.isLogic).then(res => {
                if (res.data.status === 0) {
                    vm.sourceDatasetTree = vm.setDataSourceTreeNode(res.data.data);
                    if (vm.sourceDatasetTree.length)
                        vm.expandedKeys = [vm.sourceDatasetTree[0].id];
                    let {treeNodeId, treeNode} = vm.$route.params;
                    if (treeNode && treeNode.treeType === "sourceDataSetTree" && treeNodeId) {
                        vm.active = "theme";
                        vm.$nextTick(() => {
                            vm.$refs.dgtree.setCurrentKey(treeNodeId);
                            let node = vm.$refs.dgtree.getNode(treeNodeId);
                            vm.dgNodeClick(node.data);
                            vm.expandedKeys = [treeNodeId];
                        })
                    }
                }
            });
        },
        /**
         * 设置默认展开层级
         * @param {*} data
         */
        setExpandData(data) {
            this.expandData.push(...data.map(item => item.id))
        },
        nodeClick(v, i) {
            this.currentId = v.id;
            const id = v.id.split('_')[0], data = {...i.data,id};
            this.$emit('nodeClick', v.name, id, data, i);
        },
        dgNodeClick(data, i) {
            const vm = this;
            vm.redata = data;
            vm.$nextTick(() => {
                vm.$refs.tree.setCurrentKey(null);
            });
            this.$emit('nodeClick', data.name, data.id, i.data, 'sourceDataSetTree');
        },
        //新增子目录
        addChildren(event, node, object) {
            this.newTreeDir(object);
            //this.$emit('reName',object?object:this.selectedData,'add','新增子目录');
        },
        addTreeNode(value, data) {
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            let pId = data.id;
            services.addDataSetTreeNode(pId, value).then(res => {
                if (res.data.status === 0) {
                    vm.successAddTip(value);
                    let newTreeNode = res.data.data;
                    newTreeNode.currentUserId = vm.userInfo.id;
                    newTreeNode.level = data.level + 1;
                    let parentData = data || vm.selectedData,
                        parentN = vm.$refs.tree.getNode(parentData);
                    if (!parentN.data.children)
                        parentN.data.children = [];
                    parentN.data.children.unshift(newTreeNode);
                    vm.setSelectDir(newTreeNode.id, 'add');
                    vm.$emit('closeAddFirst');
                }
            })
        },
        //重命名目录
        editNode() {
            this.prompt('重命名目录', this.selectedData.name, this.editTree, '目录名称', 'child');
            //this.$emit('reName',this.selectedData,'reName','重命名目录');
        },
        //删除目录
        deleteNode() {
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            let node = this.getTreeNode();
            let currentNode = vm.$refs.tree.getCurrentNode();
            let nodeId = vm.selectedData.id, nodeN = vm.selectedData.name;
            const index = node.children.findIndex(d => d.id === node.data.id);
            vm.confirm(`确认删除"${nodeN}"及同步删除"${nodeN}"下的数据集吗`, '删除', () => {
                services.deleteDataSetTreeNode(nodeId).then(res => {
                    if (res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        node.children.splice(index, 1);
                        if (currentNode.id === node.data.id) {
                            vm.$refs.tree.setCurrentKey(vm.selectedNode.parent.data.id);
                            if (node.data.level === 1) {
                                vm.$emit("initTable", vm.selectedNode.parent.data[0]);
                            } else {
                                vm.$emit("initTable", vm.selectedNode.parent.data);
                            }
                        }
                    }
                })
            });
        },
        editTree(value) {
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            let nodeId = vm.selectedData.id, oldName = vm.selectedData.name;
            services.reNameDataSetTreeNode(nodeId, value, oldName).then(res => {
                if (res.data.status === 0) {
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.name = value;
                    vm.$emit('closeAddFirst');
                }
            })
        },
    }
}
