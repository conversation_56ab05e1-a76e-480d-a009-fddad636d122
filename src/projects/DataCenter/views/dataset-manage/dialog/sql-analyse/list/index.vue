<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <div class="d-s-r-t-serach">
                <el-input
                        size="mini"
                        placeholder="请输入名称搜索"
                        suffix-icon="el-icon-search"
                        v-model.trim="filterTxt"
                        @keyup.enter.native="searchData"
                ></el-input>
            </div>
            <div class="d-s-r-t-btns">
                <dg-button type="primary" icon="el-icon-plus" @click="addVisualPanel" v-if="rights.indexOf(addModelRight) > -1">{{buttonTxt}}</dg-button>
            </div>
        </div>
        <div class="d-s-r-table">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    height="100%"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action"
                          v-for="(item,index) in hasRightOptIcon"
                          :key="index"
                          v-if="index < 4"
                    >
                        <dg-button type="text"
                                   class="icon"
                                   size="medium"
                                   :class="item.class"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.tip"
                                   v-html="item.icon"
                        ></dg-button>
                    </span>
                    <el-dropdown trigger="click" @command="menuCommand" v-if="countHasRightMenu > 4">
                                <span class="el-dropdown-link">
                                    <i class="el-icon-caret-bottom el-icon right"></i>
                                </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                    v-for="(m_icon , i) in hasRightOptIcon"
                                    :key="i"
                                    :command="{m_icon ,row , $index}"
                                    v-if="i >= 3"
                            >
                                <dg-button type="text"
                                           class="icon"
                                           size="medium"
                                           :class="m_icon.class"
                                           :title="m_icon.tip"
                                           v-html="m_icon.icon"
                                ></dg-button>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </common-table>
        </div>
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>