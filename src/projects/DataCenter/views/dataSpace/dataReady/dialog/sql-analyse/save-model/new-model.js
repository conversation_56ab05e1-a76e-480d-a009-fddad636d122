import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import NewModelName from './new-model-name'
import {globalBus} from "@/api/globalBus";

export default {
    name: "newModel",
    mixins: [dialog, commonMixins, servicesMixins],
    components: {
        NewModelName
    },
    data() {
        return {
            title: "",
            width: "800px",
            label: {},
            schemaId: "",
            sql: "",
            row: {}
        }
    },
    methods: {
        show(nodeId, schemaId, sql, row, title, dsTypeId) {
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.label = {
                engLabel: "英文名",
                chLabel: "中文名",
                dsTypeId: "数据来源类型"
            };
            vm.schemaId = schemaId;
            vm.sql = sql;
            vm.row = row;
            vm.title = title;
            vm.$nextTick(() => {
                vm.$refs.newName.showSelect(nodeId, row, dsTypeId);
            })
        },
        submit() {
            const vm = this, {datasetServices, datasetMock, settings, row} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            vm.$refs.newName.validate(valid => {
                if (valid) {
                    settings.loading = true;
                    let {schemaId, sql} = vm;
                    let {chName, engName, savePath, dsTypeId} = vm.$refs.newName.nameForm;
                    if (row.id) {
                        vm.update(vm, services, row.id, chName, sql, row.parentid, row, settings);
                    } else {
                        vm.saveFn(vm, services, savePath, engName, chName, schemaId, sql,dsTypeId, row, settings);
                    }
                }
            })
        },
        /**
         * 保存
         * @param vm
         * @param services
         * @param classifyId 目录节点ID
         * @param dataObjCode
         * @param dataObjName
         * @param schemaId
         * @param sql
         * @param dsTypeClassifyId
         * @param row
         * @param settings
         */
        saveFn(vm, services, classifyId, dataObjCode, dataObjName, schemaId, sql,dsTypeClassifyId, row, settings) {
            services.sqlSave(
                {
                    classifyId,
                    dataObjCode,
                    dataObjName,
                    schemaId,
                    sql,
                    dsTypeClassifyId
                },
                settings).then(res => {
                if (res.data.status === 0) {
                    let caseInfo = {name: dataObjName, code: dataObjCode, id: res.data.data.dataObjId, parentid: classifyId,dsTypeId:dsTypeClassifyId};
                    globalBus.$emit("caseSaveSuccess", row.tabId, dataObjName, caseInfo);
                    vm.visible = false;
                    vm.$message.success("保存成功");
                    vm.showOther(caseInfo);
                    vm.$emit("sqlSetSuccess");
                }
            })
        },
        /**
         * 修改
         */
        update(vm, services, dataObjId, chName, sql, classifyId, row, settings) {
            services.sqlUpdate(dataObjId, chName, sql, classifyId, settings).then(res => {
                if (res.data.status === 0) {
                    let tabId = row.tabId && dataObjId !== row.tabId ? row.tabId : dataObjId;
                    globalBus.$emit("caseSaveSuccess", tabId, chName, {name: chName});
                    vm.visible = false;
                    vm.$message.success("保存成功");
                }
            })
        },
        /**
         * 显示另存为方案
         * @param caseInfo
         */
        showOther(caseInfo) {
            let isOther = this.title === '另存为';
            if (isOther) {
                this.$emit("addTab", caseInfo);
            }
        }
    }
}
