<template>
    <el-form size="mini">
        <el-form-item label="图例位置 :">
            <el-radio-group size="mini" :value="value" v-on="$listeners" @change="changeFn">
                <el-radio-button class="ce-mini_btn" v-for="rad in positionO"
                                 :key="rad.value"
                                 :label="rad.value"
                >{{rad.label}}
                </el-radio-button>
            </el-radio-group>
        </el-form-item>
    </el-form>
</template>

<script>
    export default {
        name: "LegendEditing" ,
        props : {
            value: { type : String  },
        },
        data(){
            return {
                positionO : [
                    {
                        label : '上' ,
                        value :'top'
                    },{
                        label : '右' ,
                        value :'right'
                    },{
                        label : '下' ,
                        value :'bottom'
                    },{
                        label : '左' ,
                        value :'left'
                    },
                ]
            }
        },
        methods : {
            changeFn(){
                this.$emit('legendChange' , this.value);
            }
        }
    }
</script>

<style scoped>

</style>