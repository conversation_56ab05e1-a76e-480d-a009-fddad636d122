<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/4/30
-->
<template>
<div class="picker__outer">
    <dg-select class="picker__sel" v-model="timeWay" :data="wayOptions" @change="wayChange"></dg-select>
    <quarter-picker
        class="picker__time"
        v-if="showQuarter"
        :format="format"
        :placeholder="placeholder"
        v-model="quarterDate"
        @change="timeChange"
        />
    <el-date-picker
        v-else
        class="picker__time"
        :format="format"
        v-model="value"
        :type="timeWay"
        @change="timeChange"
        :placeholder="placeholder">
    </el-date-picker>
</div>
</template>

<script>
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear"
dayjs.extend(quarterOfYear)
export default {
    name: "time-range-picker",
    computed:{
        showQuarter(){
            return this.timeWay === "quarter";
        },
        placeholder(){
            return this.timeWay === "year" ? '请选择年' : this.timeWay === "month" ? '请选择月份' : this.timeWay === "quarter" ? '请选择季度' :'';
        },
        format(){
            return this.timeWay === "year" ? 'yyyy年' :
                this.timeWay === "month" ? 'yyyy年MM月' :
                    this.timeWay === "quarter" ? 'yyyy年q季度' :'';
        }
    },
    data() {
        return {
            timeWay :"year",
            wayOptions:[
                {label : '按年' , value: 'year'},
                {label : '按季度' , value: 'quarter'},
                {label : '按月' , value: 'month'},
            ],
            quarterDate :"",
            value:""
        }
    },
    methods:{
        timeChange(val){
            const { timeWay } = this;
            if(!val) {
                this.$emit("timeChange",'' ,'');
                return;
            }
            let curDate = dayjs(val);
            let start = curDate.startOf(timeWay).format('YYYY-MM-DD HH:mm:ss'),
                end = curDate.endOf(timeWay).format('YYYY-MM-DD HH:mm:ss');
            this.$emit("timeChange",start ,end);
        },
        wayChange(){
            this.value = "";
            this.quarterDate = "";
            this.$emit("timeChange",'' ,'', true);
        }
    }
}
</script>

<style scoped lang="less">
.picker {
    &__outer {
        display: inline-flex;
        gap:8px;

        /deep/ .el-date-editor.el-input, .el-date-editor.el-input__inner {
            width: auto;
        }
    }

    &__sel {
        width :100px;
    }
    &__time {}

}

</style>
