<template>
    <div class="planTable">
        <table-menu>
            <template slot="prepend">
                <el-form-item>
                    <el-input
                            v-model.trim="inputValueTable"
                            clearable
                            size="small"
                            placeholder="请输入中文/英文名称"
                            @keyup.enter.native="searchTableEvent"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" size="mini" @click="searchTableEvent">查询</el-button>
                </el-form-item>
            </template>
            <template slot="append">
                <div v-if="treeNode.isParent === false">
                    <el-switch
                            class="mr10"
                            v-model="isStdlib"
                            active-color="#13ce66"
                            inactive-color="#E5E5E5"
                            active-text="标准表"
                            inactive-text="原始表"
                            @change="changeTable"
                    ></el-switch>
                    <el-button type="primary" size="mini" v-if="rights.indexOf(addDataObj) > -1" @click="btnSynchClick" class="r">
                        添加数据对象
                    </el-button>
                </div>
            </template>
        </table-menu>
        <common-table
                :data="tableData"
                :columns="tHeadData"
                tooltip-effect="light"
                v-loading="loading"
        >
            <template slot="operate" slot-scope="scope">
                <i
                        v-for="(item,index) in operateIcons"
                        :key="index"
                        v-if="item.condition(treeNode , item , rights)"
                        class="icon ce_link pl5 pr5 f18"
                        @click="item.clickFn(scope.$index, scope.row)"
                        :title="item.tip"
                        v-html="item.icon"
                ></i>
            </template>

        </common-table>
        <el-pagination
                class="mt5 mb5 tc"
                background
                @current-change="changePage"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="total, prev, pager, next, jumper"
                :total="total"
        ></el-pagination>
        <Issue ref="issue" @closeService="closeServiceIssue" :row=row v-if="isShowIssue"></Issue>
        <common-dialog title="编辑" width="50%" :visible.sync="editVisible">
            <PlanEdit ref="planEdit"/>
            <div slot="footer">
                <el-button @click="closeEdit" size="mini">取消</el-button>
                <el-button @click="btnSureClick" size="mini" type="primary">确定</el-button>
            </div>
        </common-dialog>
    </div>
</template>
<script>
    import Issue from "./Issue/IssuePage"
    import PlanEdit from "./PlanEdit";
    import CommonTable from "@/components/common/CommonTable"
    import TableMenu from "@/components/common/tableMenu/TableMenu"
    import CommonDialog from "@/components/common/dialog/CommonDialog"
    import * as listApi from "./request/listApi"
    import {mapGetters} from "vuex"
    import $right from "@/assets/data/right-data/right-data"

    export default {
        name: "PlanTable",
        components: {
            PlanEdit,
            Issue,
            CommonTable,
            TableMenu,
            CommonDialog
        },
        computed : {
            ...mapGetters(["userRight"]),
            rights (){
                let rights = [];
                if(this.userRight){
                    this.userRight.forEach(r =>{
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            }
        },
        data() {
            return {
                isStdlib: false,
                editVisible: false,
                addDataObj :$right['dataWarehouseViewGetDataObject'] ,
                row: {},
                isShowIssue: false,
                inputValueTable: "",
                tip: "详情",
                currentPage: 1,
                total: 0,
                pageSize: 10,
                is_stripe: true,
                tHeadData: [
                    {
                        prop: "englistName",
                        label: "数据对象英文名",
                        width: "",
                        align: "left"
                    },
                    {
                        prop: "chineseName",
                        label: "数据对象中文名",
                        width: "",
                        align: "center"
                    },
                    {
                        prop: "userName",
                        label: "用户名",
                        width: "",
                        align: "center"
                    },
                    {
                        prop: "dataSetName",
                        label: "数据源",
                        width: "",
                        align: "center"
                    },
                    {
                        prop: "stream",
                        label: "是否流式数据",
                        width: "",
                        align: "center"
                    },
                    {
                        prop: "operate",
                        label: "操作",
                        width: "180",
                        align: "center"
                    }
                ],
                operateIcons: [
                    {icon: "&#xe6c5;", tip: "详情", clickFn: this.showDetail , condition :(treeNode , item , right) => !(treeNode.instanceType === 'hbase' && item.tip === '发布') && right.indexOf($right['dataWarehouseQueryElasticsColumnst']) > -1 },
                    {icon: "&#xe790;", tip: "数据预览", clickFn: this.dataPreview, condition :(treeNode , item , right) => !(treeNode.instanceType === 'hbase' && item.tip === '发布') && right.indexOf($right['dataWarehousePreview']) > -1 },
                    {icon: "&#xe6c0;", tip: "编辑", clickFn: this.edit , condition : (treeNode , item , right) => !(treeNode.instanceType === 'hbase' && item.tip === '发布') && right.indexOf($right['dataWarehouseGetTableInfo']) > -1 },
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteTDataLine , condition :(treeNode , item , right) => !(treeNode.instanceType === 'hbase' && item.tip === '发布') && right.indexOf($right['dataWarehouseDeleteTable']) > -1 },
                    {icon: "&#xe886;", tip: "发布", clickFn: this.issueEven , condition :(treeNode , item, right) => !(treeNode.instanceType === 'hbase' && item.tip === '发布') && right.indexOf($right['dataWarehouseIsIssue']) > -1}
                ],
                tableData: [],
                selectData: {
                    id: "",
                    stream: "",
                },
                treeNode: {},
                condition: "",
                loading: false,
                isShow: true,
            };
        },
        methods: {
            changeTable() {
                this.searchTableEvent();
            },


            btnSureClick() {
                let info = this.$refs.planEdit.getStreamInfo();
                let temp = info.row.stream !== "否";
                this.selectData.id = info.row.id;
                this.selectData.stream = info.stream;
                if (info.stream === temp) {
                    this.$message.success("保存成功");
                    this.editVisible = false;
                    return;
                }
                listApi.updateStream(info.row.tableMappingId, info.stream).then(res => {
                    if (res.data.status === 0) {
                        this.$message.success("修改成功");
                        this.tableData.forEach(item => {
                            if (item.id === this.selectData.id) {
                                item.stream = this.selectData.stream ? "是" : "否";
                            }
                        });
                        this.editVisible = false;
                    }
                })
            },
            closeEdit() {
                this.editVisible = false;
            },
            edit(index, row) {
                this.editVisible = true;
                const _this = this;
                this.$nextTick(() => {
                    _this.$refs.planEdit.show(row);
                })
            },
            issueEven(index, row) {
                listApi.isIssue(row.id).then(res => {
                    if (res.data.status === 0) {
                        if (res.data.data) {
                            this.$message.warning("此数据集已经发布服务，请勿重新操作");
                        } else {
                            this.row = row;
                            this.isShowIssue = true;
                        }
                    }
                }).catch(err => {
                    console.log(err);
                    this.$message.warning("服务器异常，请联系管理员！");
                });

            },
            closeServiceIssue() {
                this.isShowIssue = false;
            },
            // operateEvent(index, row) {
            //   this.$emit("operateEvent", row);
            // },
            setShow(index) {
                if (index === "busi") {
                    this.isShow = false;
                } else {
                    this.isShow = true;
                }
            },
            refSearchModel(value) {
                this.$message(value);
            },
            refPageChange(index) {
                this.$message("当前页 " + index);
            },
            changePage(index) {
                const vm = this;
                if (this.treeNode.id !== undefined) {
                    vm.loading = true;
                    vm.currentPage = index;
                    let data = {
                        pageSize: vm.pageSize,
                        pageIndex: vm.currentPage,
                        name: vm.inputValueTable,
                        sortField: "name",
                        sortOrder: "desc",
                        dwDbId: vm.treeNode.id,
                        isStdlib: this.isStdlib,
                    };
                    vm.tableData = [];
                    listApi.queryDataBaseTable(data).then(res => {
                        if (res.data.status === 1) {
                            vm.$message.error(res.data.msg);
                        } else {
                            vm.total = res.data.data.totalCount;
                            // console.log("初始化表格返回的数据：",res);
                            if (res.data.data.totalCount === 0) {
                                // vm.$message.warning("暂无数据");
                                // clearTimeout(time);
                            } else {
                                res.data.data.dataList.forEach(item => {
                                    let node = {
                                        pId: vm.treeNode.id,
                                        id: item.id,
                                        chineseName: item.name !== "null" ? item.name : "",
                                        englistName: item.code !== "null" ? item.code : "",
                                        synchTime: item.opTime !== "null" ? item.opTime : "",
                                        dbType: item.dbType,
                                        dataSetName: item.dataSetName,
                                        userName: item.userName,
                                        // busi: item.busi === "busi" ? "业务数据" : "主数据",
                                        stream: item.stream === "0" ? "否" : "是",
                                        tableMappingId: item.tableMappingId,
                                    };
                                    vm.tableData.push(node);
                                    // console.log(item);
                                });
                            }
                            setTimeout(this.closeLoad, 200);
                        }
                    }).catch(err => {
                        console.error(err);
                        vm.loading = false;
                        vm.$message.error("服务器异常请联系，请联系管理员！");
                    });
                }
            },
            initTabelData(treeNodeData) { // 列表内容更改，loadData 只是本地数据
                this.treeNode = treeNodeData;
                if (this.treeNode.isParent === false) {
                    // console.log("最后一级树点击：")
                    this.changePage(1);
                }
                // this.loadData();
            },
            conditionQuery(condition) {
                this.condition = condition;
            },
            createData() {

            },
            showDetail(index, row) {
                this.$emit("showTableDetailDialog", row);
            },
            dataPreview(index, row) {
                this.$emit("showDataPreviewDialog", row);
            },
            deleteTDataLine(index, row) {
                this.$confirm('确定删除该数据对象吗?', '删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    listApi.deleteTable(row.tableMappingId, '', row.id).then(res => {
                        if (res.data.status === 0) {
                            if (res.data.data !== undefined) {
                                this.$message.warning(res.data.data);
                            } else {
                                this.$message.success("删除成功!");
                                this.changePage(1);
                            }
                        }
                    }).catch(err => {
                        console.log(err);
                        this.$message.error("服务器异常,请联系管理员!")
                    })

                }).catch(() => {
                })

            },
            btnSynchClick() {
                this.$emit("showSynchDialog", this.treeNode);
            },
            searchTableEvent() {
                this.changePage(1);
            },
            clearTable() {
                this.tableData = [];
            },
            loadData() {

            },
            closeLoad() {
                this.loading = false;
            }
        },
        created() {
            this.changePage(1);
            this.loading = false;
            // this.loadData();
            this.total = this.tableData.length;
        }
    };
</script>
<style scoped>

</style>