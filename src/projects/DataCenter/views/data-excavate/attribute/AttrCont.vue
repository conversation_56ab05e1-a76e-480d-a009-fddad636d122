<template>
    <div class="attrCont">
        <div class="ce-plug_box">
            <!--******************输入插件***********************-->
            <StandardSqlInput :sourceData="sourceData" v-if="sourceData.keyWord === 'standardSqlInput'"/>
            <!--关系库输入-->
            <KvBaseInput :sourceData="sourceData" v-else-if="sourceData.keyWord === 'kVInput'"></KvBaseInput>
            <!-- kv库输入-->
            <FullTextInput :sourceData="sourceData" v-else-if="sourceData.keyWord === 'fullTextInput'"></FullTextInput>
            <!-- 全文库输入-->

            <!--******************输出插件************************-->
            <!-- <File :sourceData="sourceData" v-else-if="sourceData.keyWord ==='fileOutput'" />--> <!--文本输出-->
            <FullTextBase :sourceData="sourceData" v-else-if="sourceData.keyWord ==='fullTextOutput'"/> <!--全文库表输出-->
            <KvBase :sourceData="sourceData" v-else-if="sourceData.keyWord ==='kVOutput'"/> <!--kv库表输出-->
            <RelationBase :sourceData="sourceData" v-else-if="sourceData.keyWord ==='standardSqlOutput'"/> <!--关系库表输出-->
            <!--ftp 上传-->
            <csv-output :sourceData="sourceData" v-else-if="sourceData.keyWord === 'csvOutput'"/> <!--csAv 输出-->
            <gbase-output :sourceData="sourceData" v-else-if="sourceData.keyWord === 'gbaseTableOutput'"/>
            <!--gbase 输出-->
            <gp-output :sourceData="sourceData" v-else-if="sourceData.keyWord === 'gpOut'"/> <!--gp 输出-->


            <!--******************转换插件***********************-->
            <SubtractByKey :sourceData="sourceData" :rowData="rowData"
                           v-else-if="sourceData.keyWord === 'subtractByKeyPlugin'"/><!--左右排除-->
            <ScatterAttr :sourceData="sourceData" :rowData="rowData"
                         v-else-if="sourceData.keyWord === 'scatterChartsPlugin'"/> <!--散点图-->
            <!--            <GanttAttr :sourceData="sourceData" v-else-if="sourceData.keyWord === 'ganttChartsPlugin'"/> &lt;!&ndash;甘特图&ndash;&gt;-->
            <RadarAttr :sourceData="sourceData" :rowData="rowData"
                       v-else-if="sourceData.keyWord === 'radarChartsPlugin'"/> <!--雷达图-->
            <PieAttr :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'pieChartsPlugin'"/>
            <!--饼图-->
            <HistogramAttr :sourceData="sourceData" :rowData="rowData"
                           v-else-if="sourceData.keyWord === 'barChartsPlugin'"/> <!--柱状图-->
            <LineAttr :sourceData="sourceData" :rowData="rowData"
                      v-else-if="sourceData.keyWord === 'lineChartsPlugin'"/> <!--折线图-->

            <LeftOrRightJoin :sourceData="sourceData" :rowData="rowData"
                             v-else-if="sourceData.keyWord === 'leftOrRightJoinPlugin'"/><!--左右连接-->
            <FullJoin :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'fullJoinPlugin'"/>
            <!--全连接-->
            <InnerJoin :sourceData="sourceData" :rowData="rowData"
                       v-else-if="sourceData.keyWord === 'innerJoinPlugin'"/><!--交集-->
            <UnionJoin :sourceData="sourceData" :rowData="rowData"
                       v-else-if="sourceData.keyWord === 'unionJoinPlugin'"/><!--并集-->
            <CollisionPlugIn :sourceData="sourceData" :rowData="rowData"
                             v-else-if="sourceData.keyWord === 'collisionPlugin'"/><!--碰撞插件-->
            <FileUploadPlugin :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'fileInputMeta'"/><!--文件上传-->

            <FileDownLoadPlugin :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'fileOutputMeta'"/><!--文件输出-->


            <ClassificationCounting :sourceData="sourceData" :row="rowData"
                                    v-else-if="sourceData.keyWord === 'reducePlugin'"/><!--分组统计-->
            <JsonParsing :sourceData="sourceData" :rowData="rowData"
                         v-else-if="sourceData.keyWord === 'jsonParsingContent'"/><!--json解析-->


            <StreamingDispose :sourceData="sourceData" :row="rowData"
                              v-else-if="sourceData.keyWord === 'windowStreaming'"></StreamingDispose><!-- 时间窗-->
            <SamplingAndShunting :rowData="rowData" :sourceData="sourceData"
                                 v-else-if="sourceData.keyWord === 'samplingAndShuntingPlugin'"></SamplingAndShunting>
            <!-- 采样分流-->
            <StartProgram :treeData="treeData" :sourceData="sourceData"
                          v-else-if="sourceData.keyWord === 'startTaskPlugin'"></StartProgram>
            <!--            标签插件-->
            <LabelPlugin :sourceData="sourceData" :row="rowData"
                         v-else-if="sourceData.keyWord === 'labelMeta'"></LabelPlugin>
            <!--有效警情插件-->
            <effictivePolicePlugin :sourceData="sourceData" :row="rowData"
                                   v-else-if="sourceData.keyWord === 'effectivePolice'"></effictivePolicePlugin>
            <PersonCreatorPlugin :sourceData="sourceData" :row="rowData"
                                 v-else-if="sourceData.keyWord === 'personCreatorMeta'"></PersonCreatorPlugin>
            <GeoMappingPlugin :sourceData="sourceData" :row="rowData"
                              v-else-if="sourceData.keyWord === 'dlwzMapperMeta'"></GeoMappingPlugin>
            <JwqMapper :rowData="rowData" :sourceData="sourceData" v-else-if="sourceData.keyWord === 'jwqMapperMeta'"/>
            <!--警务区计算-->
            <AddressClean :rowData="rowData" :sourceData="sourceData"
                          v-else-if="sourceData.keyWord === 'addressCleanMeta'"/><!--警务区计算-->
            <ElementLinkPlugin :sourceData="sourceData" :rowData="rowData"
                               v-else-if="sourceData.keyWord === 'elementLinkMeta'"></ElementLinkPlugin>

            <!--
                        <RuleEditingPanel :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'serviceOrganization'" />&lt;!&ndash; 规则编排  &ndash;&gt;
            -->

            <ExcavateExpressionExcuter :sourceData="sourceData" :rowData="rowData"
                                       v-else-if="sourceData.keyWord === 'serviceOrganization'"/>
            <!--            拉链表-->
            <ZipperTable :sourceData="sourceData" :row="rowData"
                         v-else-if="sourceData.keyWord === 'zipperTablePlugin'"/>

            <!--            时间拉链表-->
            <DateZipperTable :sourceData="sourceData" :row="rowData"
                         v-else-if="sourceData.keyWord === 'dateZipperTablePlugin'"/>

            <!--            数值拉链表-->
            <NumberZipperTable :sourceData="sourceData" :row="rowData"
                         v-else-if="sourceData.keyWord === 'numberZipperTablePlugin'"/>
            <ServiceInputPlugin :sourceData="sourceData" :rowData="rowData"
                         v-else-if="sourceData.keyWord === 'serviceInputMeta'"/>
            <!--            条件过滤组件-->
            <ConditionFilterPlugin :sourceData="sourceData" :row="rowData"
                                   v-else-if="sourceData.keyWord === 'conditionFilterPlugin'"/>

            <AcrossLibrary :sourceData="sourceData" :rowData="rowData"
                           v-else-if="sourceData.keyWord === 'dataSetPlugin'"></AcrossLibrary>
            <FtpUpload :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'fileUpload'"/>


            <data-sort-plugin :sourceData="sourceData" :row="rowData"
                              v-else-if="sourceData.keyWord === 'dataSortPlugin'"></data-sort-plugin> <!--排序-->
            <MlSqlScript :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'scriptMeta'"/>
            <LucenePlugin :sourceData="sourceData" :rowData="rowData" v-else-if="sourceData.keyWord === 'luceneBuilderMeta'"/><!--lucene索引构建插件-->

            <RowDenormaliser :sourceData="sourceData" v-else-if="sourceData.keyWord === 'rowDenormaliserMeta'"
                             :rowData="rowData"/>
            <FieldFilteringPlugin :sourceData="sourceData" :rowData="rowData"
                                  v-else-if="sourceData.keyWord === 'fieldFilteringMeta'"></FieldFilteringPlugin>
            <Normalization :sourceData="sourceData" :rowData="rowData"
                           v-else-if="sourceData.keyWord === 'normalizationMeta'"></Normalization>
        </div>

    </div>
</template>

<script>
    import AcrossLibrary from '../../plugin/metaDataInput/acrossLibrary/AcrossLibrary'
    import FileUploadPlugin from "../../plugin/fileUpLoadPlugin/FileUpLoadPlugin"
    import FileDownLoadPlugin from "../../plugin/fileDownLoadPulgin/FileDownLoadPlugin"
    import ServiceInputPlugin from "../../plugin/serviceInput/index"
    import SubtractByKey from '../../plugin/collectionOperations/SubtractByKey'
    import ScatterAttr from '../../plugin/resultOutput/ScatterAttr'
    import GanttAttr from '../../plugin/resultOutput/GanttAttr'
    import RadarAttr from '../../plugin/resultOutput/RadarAttr'
    import PieAttr from '../../plugin/resultOutput/PieAttr'
    import HistogramAttr from '../../plugin/resultOutput/HistogramAttr'
    import LineAttr from '../../plugin/resultOutput/LineAttr'
    import StandardSqlInput from '../../plugin/metaDataInput/standardSql/StandardSqlInput'
    import KvBaseInput from '../../plugin/metaDataInput/keyValue/KvBaseInput'
    import FullTextInput from '../../plugin/metaDataInput/fullText/FullTextInput'
    import LeftOrRightJoin from '../../plugin/collectionOperations/LeftOrRightJoin'
    import FullJoin from '../../plugin/collectionOperations/FullJoin'
    import InnerJoin from '../../plugin/collectionOperations/InnerJoin'
    import UnionJoin from '../../plugin/collectionOperations/UnionJoin'
    import CollisionPlugIn from '../../plugin/collectionOperations/CollisionPlugIn'

    import File from '../attribute/resultOutput/File'
    import FullTextBase from '../../plugin/metaDataOutput/fullText/FullTextBase'
    import KvBase from '../../plugin/metaDataOutput/keyValue/KvBase'
    import RelationBase from '../../plugin/metaDataOutput/standardSql/RelationBase'

    import ClassificationCounting from '../../plugin/classificationCounting/ClassificationCounting'
    import StreamingDispose from '../../plugin/streamingDispose/streaming/StreamingDispose.vue'
    import SamplingAndShunting from '../../plugin/processControl/SamplingAndShunting'
    import StartProgram from '../../plugin/processControl/StartProgram'

    import ExcavateExpressionExcuter from '../../plugin/ruleEditing/ExcavateExpressionExcuter'
    import ZipperTable from '../../plugin/zipperTablePlugin/zipperTablePlugin'
    import DateZipperTable from '../../plugin/zipperTablePlugin/dateZipperTablePlugin'
    import NumberZipperTable from '../../plugin/zipperTablePlugin/numberZipperTablePlugin'
    import ConditionFilterPlugin from '../../plugin/conditionFilterPlugin/conditionFilterPlugin'

    import RuleEditingPanel from '../../plugin/ruleEditing/RuleEditingPanel'
    import FtpUpload from "../../plugin/ftpUpload/FtpUpload.vue"
    import csvOutput from "../../plugin/metaDataOutput/csv/csvOutput.vue"
    import gbaseOutput from "../../plugin/metaDataOutput/gbase/gbaseOutput.vue"
    import gpOutput from "../../plugin/metaDataOutput/greenplum/gpOutput.vue"
    import dataSortPlugin from "../../plugin/dataSortPlugin/index"
    import LabelPlugin from "../../plugin/labelPlugin/LabelPlugin.vue"
    import effictivePolicePlugin from "../../plugin/effictivePolicePlugin/EffictivePolicePlugin"
    import PersonCreatorPlugin from "../../plugin/personCreatorPlugin/PersonCreatorPlugin.vue"
    import GeoMappingPlugin from "../../plugin/geoMappingPlugin/GeoMappingPlugin.vue"
    import ElementLinkPlugin from "../../plugin/elementLinkPlugin/ElementLinkPlugin.vue"
    import MlSqlScript from '../../plugin/mlSqlScriptr/MlsqlScript'
    import RowDenormaliser from '../../plugin/rowDenormaliser/RowDenormaliser'
    import FieldFilteringPlugin from "../../plugin/FieldFilteringPlugin/FieldFilteringPlugin";
    import JsonParsing from "../../plugin/jsonParsing/JsonParsing"
    import JwqMapper from "../../plugin/jwqMapper/JwqMapper"
    import AddressClean from "../../plugin/addressCleanPlugin/AddressClean"
    import LucenePlugin from "../../plugin/lucenePlugin/LucenePlugin"
    import Normalization from "../../plugin/normalizationPlugin/Normalization"

    export default {
        name: "AttrCont",
        props: {
            sourceData: Object,
            treeData: Array,
            rowData: Object
        },
        components: {
            FileDownLoadPlugin,
            FileUploadPlugin,
            ServiceInputPlugin,
            CollisionPlugIn,
            AcrossLibrary,
            SubtractByKey,
            ScatterAttr,
            GanttAttr,
            JsonParsing,
            AddressClean,
            JwqMapper,
            RadarAttr,
            PieAttr,
            HistogramAttr,
            LineAttr,
            StandardSqlInput,
            KvBaseInput,
            LeftOrRightJoin,
            FullJoin,
            InnerJoin,
            UnionJoin,

            ExcavateExpressionExcuter,
            ZipperTable,
            DateZipperTable,
            NumberZipperTable,
            ConditionFilterPlugin,

            ClassificationCounting,
            StreamingDispose,
            SamplingAndShunting,
            StartProgram,

            File,
            FullTextBase,
            KvBase,
            RelationBase,
            FullTextInput,
            RuleEditingPanel,
            FtpUpload,
            csvOutput,
            gbaseOutput,
            gpOutput,
            dataSortPlugin,
            MlSqlScript,
            LabelPlugin,
            GeoMappingPlugin,
            ElementLinkPlugin,
            PersonCreatorPlugin,
            RowDenormaliser,
            FieldFilteringPlugin,
            effictivePolicePlugin,
            LucenePlugin,
            Normalization
        },
        data() {
            return {}
        },
        methods: {
            initPreviewBtn() { //是否有存储了数据 ，才可预览

            },

            close() {
                this.$emit('closeAttr', false);
            },
        },
        created() {
            this.initPreviewBtn();
        }
    }
</script>

<style scoped>
    .attrCont {
        height: calc(100% - 2px);
        border: 1px solid #ddd;
        background: #fff;
        box-sizing: border-box;
    }

    .ce-plug_box {
        height: 100%;
    }
</style>