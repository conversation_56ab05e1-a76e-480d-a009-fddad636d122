

export default {
    name: "syncPage" ,
    props : {
        datasetId : String
    },
    data(){
        return {
            form : [],
            form_data : [],
            disabled : false ,

        }
    },
    methods : {
        init(data){
            const vm = this;
            vm.form_data = [];
            data.forEach( (li , i) => {
                if(li.newColumn.length){
                    let allV = [];
                    vm.form_data[i] = {label : "" , code :li.code , data : [] , id : li.id};
                    vm.form_data[i].data = li.newColumn.map(item => {
                        allV.push(item.id);
                        item.value = item.id;
                        item.belongDataSetId = li.id;
                        item.label = item.name || item.code;
                        return item;
                    });
                    vm.form_data[i].label = li.name || li.code;
                    vm.form[i] = allV;
                }
            })
        },
        initHistory(data ,name , disabled){
            const vm = this;
            vm.disabled = disabled;
            let result = JSON.parse(data.result);
            vm.form_data = [];
            result.forEach( (li , i) => {
                let allV = [];
                vm.form_data[i] = {label : li.parentName , code : li.parentCode , data : [] , id : li.parentId};
                vm.form_data[i].data = li.columns.map(item => {
                    allV.push(item.id);
                    item.value = item.id;
                    item.label = item.name || item.code;
                    return item;
                });
                vm.form[i] = allV;
            })
        },
        getSyncNodes(){
            const vm = this , {form_data} = this;
            let allColumns = [];
            form_data.forEach( (form , i) => {
                let column = {
                    parentName : form.label,
                    parentId : form.id ,
                    parentCode: form.code,
                    columns: []
                };
                column.columns = vm.form_data[i].data.filter(item => vm.form[i].indexOf(item.id) > -1);
                allColumns.push(column);
            });
            return allColumns;
        },
    },

}