.roles-manage {
  .roles-header {
    display: flex;
    justify-content: space-between;

    .r-h-search {
      width: 286px;
      height: 32px;
    }
  }

  .roles-content {
    padding: 15px 0;
    //display: flex;
    //align-items: center;
    height: calc(100% - 30px);
    box-sizing: border-box;
    .r-c-action:not(:last-child)::after {
      content: "";
      display: inline-block;
      width: 1px;
      height: 12px;
      background-color: rgba(0, 0, 0, 0.15);
      margin: 0 8px;
    }
  }
}

.pop-btns {
  width: 100%;

  div {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
