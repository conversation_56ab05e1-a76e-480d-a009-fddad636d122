<template>
    <common-dialog custom-class="addCustomBody" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="clearD"
                   :fullscreen="dialogFullScreen"
                   top="8vh"
                   v-loading="settings.loading"
    >
        <div slot="title" class="custom-dialog__title">
            <span class="el-dialog__title">{{ title }}</span>
            <help-document :code="'dataSetOperationPreviewData'"></help-document>
            <div class="custom-dialog__menu" @click="fullChange">
                <i :class="dialogFullScreen ? 'dg-icon-scalescreen':'dg-icon-fullscreen'"></i>
            </div>
        </div>
        <div class="ce-list" :data-full="dialogFullScreen">
            <el-tabs v-model="activeName" @tab-click="handleClick" class="height100">
                <el-tab-pane label="数据预览" name="preview" class="height100">
                    <preview ref="preview" class="tabPane" :previewInfo="previewInfo" v-if="visible"
                             @refresh="previewList" @getPreview="getPreview"></preview>
                </el-tab-pane>
                <!--<el-tab-pane label="血缘分析" name="analyse"></el-tab-pane>-->
                <!--<el-tab-pane label="更新记录" name="updateRecord"></el-tab-pane>-->
            </el-tabs>

            <div class="other-button">
                <slot name="button" :row="rowInfo"></slot>
                <dg-button @click="syncTable" :class="{disabled: settings.loading}"
                           v-if="!rowInfo.resourceId && rowInfo.belong_type === 'PHYSICAL' && !isShare">同步数据结构
                </dg-button>
            </div>
            <!--<sync-data ref="syncData" :syncDataList="syncDataList" @syncSure="syncSure"></sync-data>-->
            <sync-data ref="syncData" :syncDataList="syncDataList" @syncSure="syncSure"
                       :syncType="syncType"></sync-data>
        </div>
    </common-dialog>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
// import CeTabPanel from "@/components/signed-components/ceTabPanel/CeTabPanel";
import preview from "../../dialog/preview";
import syncData from "../../dialog/dataStructSync";
import {max} from "lodash";
export default {
    name: "dataDetails",
    mixins: [commonMixins, servicesMixins],
    components: {
        // CeTabPanel,
        preview,
        syncData
    },
    data() {
        const { $CancelToken } = this;
        return {
            syncType: 'dataSet',
            title: '',
            visible: false,
            width: '1200px',
            dialogFullScreen:false,
            previewInfo: {
                errColumns: [],
                tHeadDataPreview: [],
                totalCount: 0,
                tHeadDataColumn: [
                    {
                        prop: "code",
                        label: "字段名",
                        minWidth: 150,
                        align: "left"
                    },
                    {
                        prop: "name",
                        label: "字段中文名",
                        minWidth: 150,
                        align: "left"
                    },
                    {
                        prop: "dataType",
                        label: "数据类型",
                        minWidth: 150,
                        align: "left"
                    },
                    // {
                    //     prop: "dataType",
                    //     label: "来源于",
                    //     minWidth: 150,
                    //     align: "left"
                    // },
                ],
                previewTableData: [],
                columnTableData: [],
                loading: false,
                settings: {
                    loading: false
                },
                isErr: false,
                errMsg: ''
            },
            activeName: "preview",
            settings: {
                loading: false
            },
            rowInfo: {},
            syncDataList: {},
            isShare: false,
            httpSource: $CancelToken.source(),
        }
    },
    methods: {
        fullChange(){
            this.dialogFullScreen = !this.dialogFullScreen;
        },
        show(row, isShare) {
            this.title = row.name;
            this.rowInfo = row;
            this.visible = true;
            this.isShare = isShare;
            this.getPreview();
        },
        handleClick(val, tab) {
            //console.log(val, tab)
        },
        async getPreview() {
            if (this.rowInfo.belong_type === 'PHYSICAL') this.getErrorColumn();
            if (this.rowInfo.belong_type !== 'QUICK_SQL') {
                this.getColumn();
            } else {
                this.getSqlColumns();
            }
            this.previewList(1);
        },
        loadElementColumn(params) {
            const vm = this, {$globalConfig, $services} = vm;
            if ($globalConfig.datasetLinkUrl) {
                return $services("dataReady").loadElementColumn(params);
            } else return Promise.resolve({
                data: {
                    status: 0,
                    data: {},
                }
            })
        },
        //预览
        previewList(index) {
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            vm.previewInfo.loading = true;
            vm.previewInfo.tHeadDataPreview = [];
            vm.previewInfo.previewTableData = [];
            let data = {
                dataSetId: vm.rowInfo.id,
                addDataSetId: '',
                page: index,
                pageSize: vm.$refs.preview ? vm.$refs.preview.paginationPropsPre.pageSize : 10,
            };
            let dataLinkObj = {};
            this.httpSource = this.$CancelToken.source();
            Promise.all([
                vm.loadElementColumn(data),
                services.previewLogicDataSet(data,null,{
                    cancelToken: vm.httpSource.token
                })
            ]).then(([res1, res])=> {
                if(res1.data.status === 0 ) dataLinkObj = res1.data.data;
                if (res.data.status === 0) {
                    vm.previewInfo.isErr = false;
                    vm.previewInfo.errMsg = '';
                    let result = res.data.data, head;
                    vm.previewInfo.totalCount = result.totalCount;
                    if (result && result.dataList && result.dataList.length) {
                        head = result.dataList[0];
                        vm.previewInfo.tHeadDataPreview = Object.keys(head).map(key => {
                            const hasLink = !!dataLinkObj[key];
                            const maxLen = max(result.dataList.map(li => li[key]? li[key].length : 0));
                            return {
                                label: key,
                                prop: key,
                                minWidth: hasLink && maxLen ? Math.max(maxLen * 10 + 32 , 120) : key.length < 6 ? 120 : key.length * 16 + 32,
                                hasLink,
                                linkOptions: hasLink ? dataLinkObj[key] : []
                            };
                        })
                        vm.previewInfo.previewTableData = result.dataList;
                    }
                    vm.$refs.preview.init();
                } else {
                    vm.previewInfo.isErr = true;
                    vm.previewInfo.errMsg = res.data.msg
                }
            }).finally(()=>{
                vm.previewInfo.loading = false;
            })
        },
        //获取报错字段
        getErrorColumn() {
            const vm = this, {dataReadyServices, dataReadyMock, settings} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            let data = {
                logicId: vm.rowInfo.id
            }
            services.getErrorColumns(data, settings).then(res => {
                vm.previewInfo.errColumns = [];
                if (res.data.status === 0) {
                    vm.previewInfo.errColumns = res.data.data.data.deleteColumns;
                }
            })
        },
        //字段列表
        getColumn() {
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            vm.previewInfo.settings.loading = true;
            let data = {
                addDataSetId: '',
                currentDataSetId: vm.rowInfo.id,
            };
            vm.previewInfo.columnTableData = [];
            vm.columnTableDataCopy = [];
            services.getLogicDataColumn(data, vm.previewInfo.settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data, data = [];
                    if (result.columns && result.columns.length) {
                        result.columns.forEach(col => {
                            if (col.dimension && col.dimension.length) data.push.apply(data, col.dimension);
                            if (col.measure && col.measure.length) data.push.apply(data, col.measure);
                        })
                    }
                    vm.previewInfo.columnTableData = data;
                    vm.previewInfo.columnTableDataCopy = data;
                    vm.previewInfo.columnTotal = vm.previewInfo.columnTableData.length;
                }
            })
        },
        getSqlColumns() {
            const vm = this, {$services, rowInfo} = vm;
            let services = $services("dataset");
            vm.previewInfo.settings.loading = true;
            services.sqlQuery(rowInfo.id).then(res => {
                if (res.data.code === 0) {
                    let result = res.data.data;
                    services.sqlColumns(result.sql, result.schemaId).then(res => {
                        if (res.data.code === 0) {
                            let tableD = res.data.data || [], data = [];
                            data = tableD.map(li => {
                                return {
                                    ...li,
                                    code: li.name,
                                }
                            })
                            vm.previewInfo.columnTableData = data;
                            vm.previewInfo.columnTableDataCopy = data;
                            vm.previewInfo.columnTotal = vm.previewInfo.columnTableData.length;
                            vm.previewInfo.settings.loading = false;
                        }
                    }).catch(() => {
                        vm.previewInfo.settings.loading = false;
                    })
                }
            }).catch(() => {
                vm.previewInfo.settings.loading = false;
            })
        },
        changeSize(val) {
            this.previewInfo.paginationProps.pageSize = val;
        },

        //同步数据结构
        syncTable() {
            const vm = this, {dataReadyServices, dataReadyMock} = this;
            let services = vm.getServices(dataReadyServices, dataReadyMock);
            vm.syncDataList = {
                addColumnList: [],
                deleteColumnList: [],
                updateColumnList: [],

            }
            let data = {
                logicDataObjId: vm.rowInfo.id
            }
            services.getSyncLogicDataColumns(data).then(res => {
                if (res.data.status === 0) {
                    vm.syncDataList = {
                        addColumnList: res.data.data.ADD.map(n => {
                            n.label = n.code;
                            n.value = n.id;
                            return n;
                        }),
                        deleteColumnList: res.data.data.DELETE.map(n => {
                            n.label = n.code;
                            n.value = n.id;
                            return n;
                        }),
                        updateColumnList: res.data.data.EDIT.map(n => {
                            n.label = n.code;
                            n.value = n.id;
                            return n;
                        }),
                    }
                    if (!vm.syncDataList.addColumnList.length && !vm.syncDataList.deleteColumnList.length && !vm.syncDataList.updateColumnList.length) {
                        vm.$message.success("暂无字段同步");
                        return
                    } else {
                        this.$refs.syncData.show(this.rowInfo, this.rowInfo);
                    }
                }
            });
        },
        //同步数据集
        syncSure() {
            const vm = this, {settings, dataReadyServices, dataReadyMock} = this;
            let services = this.getServices(dataReadyServices, dataReadyMock);
            settings.loading = true;
            let data = {
                // dataObjId:vm.rowInfo.owner_id,
                // dbType:vm.rowInfo.db_type,
                logicId: vm.rowInfo.id
            }
            services.syncColumns(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("同步成功");
                    vm.syncDataList = {};
                    vm.getPreview();
                    vm.$refs.syncData.clearD();
                }
            });
        },
        // 弹窗关闭 需要清空 和 取消预览请求
        clearD() {
            this.previewInfo.errMsg = "";
            this.previewInfo.isErr = false;
            this.previewInfo.tHeadDataPreview = [];
            this.previewInfo.previewTableData = [];
            this.previewInfo.columnTableData = [];
            this.httpSource.cancel();
            this.visible = false;
        }
    }
}
</script>

<style scoped lang="less">
/deep/ .addCustomBody {
    .dg-dialog__body {
        padding: 10px 32px;
    }
    .dg-dialog__header {
        height: 56px;
    }
}

.other-button {
    position: absolute;
    top: 10px;
    right: 32px;
}

.ce-list {
    height: calc(95vh - 200px);

    /deep/ .el-tabs__content {
        height: calc(100% - 65px);
    }
    &[data-full=true] {
        height:calc(100vh - 76px);
    }
}
.custom-dialog {
    &__menu {
        position: absolute;
        font-size:16px;
        line-height:1.5;
        cursor: pointer;
        color: @fontDesColor;
    }
}
@media screen and (max-width: 1365px) {
    .custom-dialog__menu {
        top : 10px;
        right: 50px;
    }
}
@media screen and (max-width: 1680px) and (min-width: 1366px) {
    .custom-dialog__menu {
        top : 14px;
        right: 54px;
    }
}

@media screen and (min-width: 1681px) {
    .custom-dialog__menu {
        top : 18px;
        right: 58px;
    }
}
</style>
