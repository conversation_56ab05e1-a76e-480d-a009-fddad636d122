import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import rapidForm from "../dialog/rapid-form"
import newModel from "../dialog/sql-analyse/operate/new-model"
import sqlAnalyse from "../dialog/sql-analyse"
import Issue from "../dialog/Issue/IssuePage"
import MovetoTree from "@/components/common/moveto-tree"
import * as dataSourcesServices from "../../dataSources/service-mixins/service-mixins"
import PlanDataPreviewDialog from "../../dataSources/dialog/PlanDataPreviewDialog"
import {listMixins} from "@/api/commonMethods/list-mixins";
import syncLogic from "../dialog/rapid-analyse/tree-operate/sync-logic-data"
export default {
    name: "list",
    components: {
        rapidForm,
        newModel,
        sqlAnalyse,
        Issue,
        MovetoTree,
        PlanDataPreviewDialog,
        syncLogic
    },
    mixins: [commonMixins, dataSourcesServices.servicesMixins, servicesMixins, common , listMixins],
    computed: {
        ...mapGetters(["userRight","userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights, vm.treeNode.treeType !== "sourceDataSetTree")) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights, vm.treeNode.treeType !== "sourceDataSetTree")) {
                    opts.push(me);
                }
            });
            return opts;
        }
    },
    data() {
        return {
            isShowIssue: false,
            filterTxt: "",
            buttonTxt: ["即席SQL分析", "快速分析"],
            rightSql: $right["dataSetOperationCreateSql"],
            rightAna: $right["dataSetOperationCreateLogicDataSet"],
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "名称",
                    minWidth: 200,
                    align: "left"
                },
                {
                    prop: "user",
                    label: "创建人",
                    width: "",
                    align: "center"
                },
                {
                    prop: "operate_time",
                    label: "最后修改时间",
                    minWidth: 160,
                    align: "center"
                },
                /*{
                    prop: "is_fast",
                    label: "是否极速表",
                    type: "status-tpl",
                    "cmp-props-options": [
                        {name: "是", status: "1"},
                        {name: "否", status: "0"}
                    ],
                    resizable: false
                }, */
                {
                    prop: 'operate',
                    label: '操作',
                    width: 180,
                    align: "center",
                    resizable: false
                }
            ],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe6cd;",
                    tip: "创建仪表盘",
                    clickFn: this.created_visual,
                    condition: (right) => right.indexOf($right["dataSetOperationDeleteDataSet"]) > -1
                }, {
                    icon: "&#xe886;",
                    tip: "服务发布",
                    clickFn: this.issue_services,
                    condition: (right) => right.indexOf($right["dataSetOperationIsIssue"]) > -1
                }, {
                    icon: "&#xe6c0;",
                    tip: "编辑",
                    clickFn: this.edit,
                    condition: (right, type) => type ? right.indexOf($right["dataSetOperationDataColumn"]) > -1  :
                        false
                },{
                    icon: "&#xe790;",
                    tip: "预览",
                    clickFn: this.preview,
                    condition: (right, type) => right.indexOf($right["dataSetOperationPreviewData"]) > -1 && !type
                },  {
                    icon: "&#xe65f;",
                    tip: "删除",
                    clickFn: this.deleteDataset,
                    condition: (right, type) => right.indexOf($right["dataSetOperationDeleteDataSet"]) > -1 && type
                },
                {
                    icon : "&#xe788;" ,
                    tip : "同步数据集结构" ,
                    clickFn : this.syncDataset ,
                    condition : (right, type) => !type
                },/*{
                    icon: "&#xe656;",
                    tip: "高级配置",
                    clickFn: this.seniorSetting,
                    condition: (right) => right.indexOf($right["dataSetOperationEditDataSetColumn"]) > -1
                },
                {icon : "&#xe89a;" ,
                    class : "roate" ,
                    tip : "极速表" ,
                    clickFn : this.setFast ,
                    condition : ( right) => true
                },*/
                {
                    icon: "&#xe6bf;",
                    tip: "移动",
                    clickFn: this.moveTo,
                    condition: (right, type) => right.indexOf($right["dataSetOperationMoveLogicDataSet"]) > -1 && type
                }
            ],
            nodeId: "",
            treeNode: {},
            sqlRowData: {},
            showSqlPanel: false,
            row: {}
        }
    },
    methods: {
        syncDataset(value){
            this.$refs.syncLogic.show(value.id);
        },
        preview(row) {
            this.$refs.planDataPreviewDialog.datasetShow(row);
        },
        setFast(row) {
            let fast, tip, msg;
            if (row.is_fast === "1") {
                fast = false;
                tip = "确定变为普通表吗?";
                msg = "成功转为普通表";
            } else {
                fast = true;
                tip = "确定升级为极速表吗?";
                msg = "升级成功";
            }
            const vm = this, {datasetServices, datasetMock} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            vm.confirm('提示', tip, () => {
                services.changeIsFast(row.id, fast).then(res => {
                    if (res.data.code === 0) {
                        if (res.data.data !== null) {
                            vm.$message.warning(res.data.data);
                        } else {
                            vm.$message.success(msg);
                            vm.changePage(1);
                        }
                    }
                })
            }, null, 'info');
        },
        moveSuccess(id) {
            this.$emit("moveSuccess", id);
        },
        seniorSetting() {
        },
        closeServiceIssue() {
            this.isShowIssue = false;
        },
        closeSqlPanel() {
            this.showSqlPanel = false;
            this.changePage(1);
        },
        created_visual(row) {
            const vm = this;
            vm.confirm("提示", "确认使用此数据集创建仪表盘?", () => {
                vm.$router.push({name: "dashboard", params: {datasetId: row.id, rowList: row}})
            })
        },
        issue_services(row) {
            const vm = this, {datasetServices, datasetMock} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            services.isIssue(row.id).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data) {
                        this.$message.warning("此数据集已经发布服务，请勿重新操作");
                    } else {
                        this.row = row;
                        this.isShowIssue = true;
                    }
                }
            })
        },
        editSqlPage(row) {
            this.showSqlPanel = true;
            this.sqlRowData = row;
        },
        edit(row) {
            const vm = this;
            if (row.belong_type === 'QUICK_SQL') {
                vm.editSqlPage(row);
            } else {
                let data = {
                    name: row.name,
                    datasetId: row.id,
                    treeNodeId: vm.treeNode.id,
                    treeNode: vm.treeNode,
                    parentId : row.parentid
                };
                vm.$router.push({name: 'analysis', params: data});
            }
        },
        deleteDataset(row) {
            const vm = this, {datasetServices, datasetMock} = this;
            let services = vm.getServices(datasetServices, datasetMock);

            services.checkDataSetUserInVisual(row.id).then(res => {
                if (res.data.status === 0) {
                    let msg = "", result = res.data.data;
                    if (result === "success") {
                        msg = `删除可能会导致其他数据集无法使用，是否确认删除 \"${row.name}\" 数据集？`;
                    } else {
                        msg = result;
                    }
                    vm.confirm("删除", msg, () => {
                        services.deleteDataSet(row.id).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("删除成功");
                                vm.changePage(1);
                            }
                        })
                    });
                }
            })
        },
        moveTo(row) {
            const vm = this, {datasetServices, datasetMock} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            this.$refs.moveto.show(row, services);
        },
        sqlAnalyse() {
            this.$refs.sql_form.show(this.nodeId);
        },
        showSqlPage(row) {
            this.showSqlPanel = true;
            this.sqlRowData = row;
        },
        nowAnaly() {
            this.$refs.rapid_form.show(false ,this.nodeId);
        },
        menuCommand(command) {
            command.m_icon.clickFn(command.row, command.$index);
        },
        dataSetPageChange(index) {
            const vm = this, {datasetServices, datasetMock, settings} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let params = {
                id: vm.nodeId,
                name: vm.filterTxt,
                page: index,
                pageSize: vm.paginationProps.pageSize
            };
            settings.loading = true;
            vm.tableData = [];
            services.getDataSetPage(params, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result) {
                        vm.tableData = result.dataList.map(item => {
                            if (item.name === null || item.name === "") {
                                item.name = item.code;
                            }
                            return item;
                        });
                        vm.total = result.totalCount;
                    } else {
                        vm.total = 0;
                    }
                }
            })
        },
        addHeadRow(){
            if(this.tHeadData[2].prop !== 'operateUserName'){
                this.tHeadData.splice(2, 0, {
                    prop : "operateUserName",
                    label : "编辑者",
                    width: "" ,
                    align : "center"
                });
            }

        },
        removeHeadRow(){
            if(this.tHeadData[2].prop === 'operateUserName'){
                this.tHeadData.splice(2,1);
            }

        },
        changePage(index) {
            if (this.treeNode.treeType === "sourceDataSetTree") {
                this.sourcePageChange(index);
                this.removeHeadRow();
            } else {
                this.addHeadRow();
                this.dataSetPageChange(index);
            }
        },
        sourcePageChange(index) {
            const vm = this;
            const {listApi, listMock, settings} = this;
            let services = vm.getServices(listApi, listMock);
            vm.paginationProps.currentPage = index;
            let params = {
                dwDbId: vm.nodeId,
                name: vm.filterTxt,
                pageIndex: index,
                pageSize: vm.paginationProps.pageSize,
                sortField: "name",
                sortOrder: "desc",
            };
            settings.loading = true;
            vm.tableData = [];
            services.queryDatasetBaseTable(params).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result) {
                        vm.tableData = result.dataList.map(item => {
                            if (item.name === null || item.name === "") {
                                item.name = item.code;
                            }
                            return item;
                        });
                        vm.total = result.totalCount;
                    } else {
                        vm.total = 0;
                    }
                }
                setTimeout(()=> {
                    settings.loading = false;
                },100)
            }).catch(err =>{
                settings.loading = false;
            });
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        searchFn() {
            this.changePage(1);
        },
        getDataSetPageByTree(node) {
            const vm = this;
            vm.treeNode = node;
            if (node.pId !== "-1") {
                vm.nodeId = node.id;
            } else {
                vm.nodeId = "";
            }
            vm.changePage(1);
        },
    },
}
