export default {
    name: "role-form-pop",
    inheritAttrs: false,
    props:{
        showPrev:{
            type: Boolean,
            default: true
        },
        popTableData:{
            type: null,
            default(){
                return []
            }
        },
        title:{
            type: String,
            default:'角色权限设置'
        },
        isEdited :{
            type : Boolean ,
            default : false
        },
        formData : {
            type : Object
        },
        selectedRight : Array,


    },
    data(){
        return{
            roleTip : "角色名称",
            //弹窗搜索功能
            addSearchVal: "",
            tableData : [],
            activeName:'all',
            modeList:[],
            visualList:[],
            dataList:[],
            managementList:[],
            scenarioCaseList:[],
            serviceSpaceList:[],
        }
    },
    watch:{
        "$attrs.visible":function(newValue,oldValue){
            if(newValue){
                this.changeArr();
            }

         }
    },
    methods:{
        changeArr(){
            const vm = this;
            vm.modeList = vm.popTableData.filter(n=>['dataModeling'].includes(n.parentCode));
            vm.visualList = vm.popTableData.filter(n=>['dataVisualAnalysis'].includes(n.parentCode));
            vm.dataList = vm.popTableData.filter(n=>['dataAssets'].includes(n.parentCode));
            vm.managementList = vm.popTableData.filter(n=>['systemManagement'].includes(n.parentCode));
            vm.scenarioCaseList = vm.popTableData.filter(n=>['scenarioCase'].includes(n.parentCode));
            vm.serviceSpaceList = vm.popTableData.filter(n=>['serviceSpace'].includes(n.parentCode));
        },
        selectArr(name){
            const vm = this;
            let data=[];
            switch(name){
                case 'modelSpace':
                    data = vm.modeList;
                    break;
                case 'visualSpace':
                    data = vm.visualList;
                    break;
                case 'dataSpace':
                    data = vm.dataList;
                    break;
                case 'management':
                    data = vm.managementList;
                    break;
                case 'scenarioCase':
                    data = vm.scenarioCaseList;
                    break;
                case 'serviceSpace':
                    data = vm.serviceSpaceList;
                    break;
                default:
                    data = vm.popTableData;
                    break;
            }
            return data;
        },
        handleClick(tab, event) {
            if(!tab) return this.popTableData;
            this.activeName = tab.name;
            this.tableData = this.selectArr(this.activeName);
        },
        searchFn(){
            const vm = this;
            let addSearchVal = vm.addSearchVal.toLowerCase() ;
            let popTableData = this.selectArr(this.activeName);
            vm.tableData = popTableData.filter(pop =>{
                return pop.menu.toLowerCase().indexOf(addSearchVal) > -1 || pop.func.toLowerCase().indexOf(addSearchVal) > -1;
            })
        },
        setSelectData(){
            const vm = this;
            let result = [] , resArray = [];
            vm.popTableData.forEach(pop =>{
                if(pop.selectAll.length){
                    let selectVal = Array.isArray(pop.selectAll) ? pop.selectAll :  pop.selectAll.split(",");
                    selectVal.forEach(p => {
                        if(p){
                            result = result.filter(re =>re.funcCode !== p);
                            resArray = resArray.filter(re => re !== p);
                            result.push({funcCode : p});
                            resArray.push(p);
                        }

                    })
                }
            });
            return {result , resArray};
        },
        onCancel(){
            this.$emit('update:visible',false)
        },
        prev(){
            this.$emit('prev')
        },
        getAddData(resArray){
            const vm = this;
            let result = [];
            resArray.forEach(it =>{
                if(vm.selectedRight.indexOf(it) === -1){
                    result.push({funcCode : it})
                }
            });
            return result;
        },
        getDeleteData(resArray){
            const vm = this;
            let result = [];
            vm.selectedRight.forEach(it =>{
                if(resArray.indexOf(it) === -1){
                    result.push({funcCode : it})
                }
            });
            return result;

        },
        async onSubmit() {
            let data = await this.setSelectData();
            let add = await this.getAddData(data.resArray);
            let del = this.getDeleteData(data.resArray);
            this.$emit('save',data.result, true , add , del);
        },
        reNewData(){
            this.addSearchVal = "";
            this.searchFn();
        },
        async updateFn(){
            let data = await this.setSelectData();
            let add = await this.getAddData(data.resArray);
            let del = this.getDeleteData(data.resArray);
            this.$emit('save',data.result , false , add , del);
        },
        resetActiveName(){
            this.activeName = 'all';
        }
    }
}