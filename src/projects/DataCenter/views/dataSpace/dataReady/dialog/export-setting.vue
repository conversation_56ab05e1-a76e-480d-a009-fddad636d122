<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/7/5
-->
<template>
    <div v-loading="loading" element-loading-text="正在导出中,请稍后...">
        <el-form ref="form" :model="form" label-position="right" label-width="120px" label-suffix=" :">
            <el-form-item v-for="(item, k) in formList" :key="k" :prop="k" :label="item.label" :rules="item.rules">
                <template v-if="item.type === 'input'">
                    <el-input v-model="form[k]" v-input-limit:[item.limit]
                              :maxlength="item.maxlength" :disabled="item.disabled()">
                        <template slot="append">{{item.append}}</template>
                    </el-input>
                </template>
                <template v-if="item.type === 'numberInput'">
                    <el-input  v-model.number="form[k]"
                               v-input-limit:[item.limit]
                               :disabled="item.disabled()"
                               :maxlength="item.maxlength"
                               @input="item.onInput(k)"
                    ></el-input>
                </template>
            </el-form-item>

        </el-form>
        <dialog-footer-btn v-footer :data="buttonGroup"/>
    </div>
</template>

<script>
import DialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn.vue"

export default {
    name: "export-setting",
    components: {
        DialogFooterBtn,
    },
    props: {
        maxSize:Number,
        row:Object,
    },
    data() {
        const {maxSize} = this;
        return {
            loading : false,
            form: {
                name: "",
                size: maxSize > 1000 ? 1000 : maxSize,
            },
            formList: {
                name: {
                    label: "导出文件名",
                    type: "input",
                    disabled: () => false,
                    maxlength: 50,
                    limit: "fieldChineseCode",
                    append: ".xlsx",
                    rules:[{required:true, message: "请输入导出文件名", trigger: ["blur" ,"change"]}]
                },
                size: {
                    label: "条数",
                    type: "numberInput",
                    disabled: () => false,
                    limit: "number",
                    maxlength:10,
                    onInput: (k) => {
                        const {maxSize} = this;
                        // 限制 maxSize
                        if(this.form[k] && this.form[k] > maxSize ) {
                            this.form[k] = maxSize;
                            this.$message.warning(`条数最大限制为 ${maxSize}`);
                        }
                        if(this.form[k] !== "" && this.form[k] <= 0) {
                            this.form[k] = 1;
                            this.$message.warning("条数最小为 1")
                        }
                    },
                    rules:[{required:true, message: "请输入导出条数", trigger: ["blur" ,"change"]}]
                }
            },
            buttonGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.submitFn,
                    disabledFn: () => this.loading,
                    show: () => true
                },
            ]
        }
    },
    methods: {
        cancelFn() {
            this.$emit("close");
        },
        submitFn() {
          const vm = this, {$axios} = vm;
          vm.$refs.form.validate(valid => {
            if(valid) {
              vm.loading = true;
              const params = {
                dataSetId: vm.row.id,
                addDataSetId: '',
                page: 1,
                pageSize: 10,
                excelName: vm.form.name,
                excelLength: vm.form.size,
              };
              $axios.request({
                method: "post",
                url: "/editDataSet/logicDataExport",
                data: params,
                responseType: 'blob'
              }).then(res => {
                if (res.data.type === 'application/json'){
                  vm.loading = false;
                  let reader = new FileReader();
                  reader.readAsText(res.data,'utf-8')
                  reader.onload = e => {
                    let res = JSON.parse(e.target.result);
                    if (res.code === 1){
                      vm.$message.error(res.msg);
                    }
                  };
                } else {
                  const fileName = res.headers["content-disposition"] && res.headers["content-disposition"].includes("filename=") ?
                      res.headers["content-disposition"].split("filename=")[1] : "";
                  const name = fileName ? decodeURI(fileName): vm.form.name;
                  const xlsx =  'application/vnd.ms-excel,charset=UTF-8';
                  let blob = new Blob([res.data],{type : xlsx});
                  const url = window.URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = name;
                  link.click();
                  window.URL.revokeObjectURL(url); //释放掉blob对象
                  vm.loading = false;
                  vm.cancelFn();
                }
              })
            }
          })
        }
    }
}
</script>

<style scoped lang="less">
/deep/.el-input-number {
    .el-input__inner {
        text-align:left;
    }
    span[role='button'] {
        display: none;
    }
}
</style>
