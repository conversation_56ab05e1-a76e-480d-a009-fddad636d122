<template>
    <div>
        <el-form ref="inquiryAndVerificationForm" label-width="110px" class="demo-ruleForm">
            <el-form-item label="设置参数" v-if="tableType === '0'">
                <el-button type="primary" @click="setColumns">添加</el-button>
            </el-form-item>
            <el-form-item label="请求参数"  class='singerH'>
                <!--数据查询-->
                <common-table
                        :data="requestTableData"
                        :columns="requestTHeadData"
                        :pagination="false"
                        class="width100"
                        :border="false"
                        row-key="paramValue"
                >
                    <template slot="dataType" slot-scope="scope">
                        <dg-select v-model="scope.row.dataType" :data="dataTypeList" @change="changeInput"></dg-select>
                    </template>
                    <template slot="memo" slot-scope="scope">
                        <el-input v-model="scope.row.memo" @change="changeInput" maxlength="30"></el-input>
                    </template>
                    <template slot="example" slot-scope="scope">
                        <el-input v-model="scope.row.example" @change="changeInput" maxlength="30"></el-input>
                    </template>
                    <template slot="defaultValue" slot-scope="scope">
                        <el-input v-model="scope.row.defaultValue" @change="changeInput" maxlength="30"></el-input>
                    </template>
                    <template slot="paramCode" slot-scope="scope">
                        <el-input v-model="scope.row.paramCode" v-input-limit:fieldCode maxlength="30"></el-input>
                    </template>
                    <template slot="isMust" slot-scope="scope">
                        <el-checkbox v-model="scope.row.isMust" @change="changeInput"></el-checkbox>
                    </template>
                    <template slot="header-operate" slot-scope="{row , $index}" v-if="tableType === '1'">
                        <el-button icon="el-icon-plus" type="text" title="新增" @click="addColumn"></el-button>
                    </template>
                    <template slot="operate" slot-scope="{row , $index}">
                        <el-button type="text" @click="requestDeL(row , $index)">删除</el-button>
                    </template>
                    <template slot="paramValue" slot-scope="{row , $index}" v-if="tableType === '1'">
                        <el-button type="text" @click="bindColumn(row , $index)">{{isBind(row) ? '已绑定' : '添加绑定'}}</el-button>
                    </template>
                </common-table>
            </el-form-item>
            <el-form-item label="设置参数" v-if="regionType ==='4' && tableType === '1'">
                <el-button type="primary" @click="setColumns">添加</el-button>
            </el-form-item>
            <el-form-item label="返回参数" class='singerH'>
                <!--数据查询返回参数-->
                <div v-if="regionType === '4'" style="display: flex;">
                    <div style="width:180px;" v-if="tableType === '1'" :style="{'width': backDataSetInfo.length ? '180px' : '0px'}">
                        <div v-for="(item, k) in backDataSetInfo" class="dataSet-list" :key="k"
                             :class="{'active': selectDataSet.dataSetId == item.dataSetId }"
                             @click="changeDataSet(item, 'response')"
                        >
                            {{item.dataSetName}}
                        </div>
                    </div>
                    <common-table
                                :data="tableType === '1' ? filterResponseData : responseTableData"
                                :columns="responseTHeadData"
                                class="width100"
                                :pagination="false"
                                :border="false"
                                row-key="paramValue"
                        >
                            <template slot="example" slot-scope="scope">
                                <el-input v-model="scope.row.example" @change="changeInput" maxlength="30"></el-input>
                            </template>
                            <template slot="dataType" slot-scope="scope">
                                <dg-select v-model="scope.row.dataType" :data="dataTypeList" @change="changeInput"></dg-select>
                            </template>
                            <!--<template slot="paramCode" slot-scope="scope">-->
                                <!--<el-input v-model="scope.row.paramCode" @change="changeInput" v-input-limit:fieldCode></el-input>-->
                            <!--</template>-->
                            <template slot="desensitization" slot-scope="scope">
                                <dg-select v-model="scope.row.desensitization" :data="desenList" label-name="name" value-name="code" @change="changeInput"></dg-select>
                            </template>
                            <template slot="operate" slot-scope="{row , $index}">
                                <el-button type="text" v-if="!sortTableData.find(n=>n.paramCode == row.paramCode)" @click="addSort(row , $index)">添加为排序字段</el-button>
                                <el-button type="text" @click="responseDeL(row , $index)">删除</el-button>
                            </template>
                        </common-table>
                </div>
                <!--信息核查返回传参数-->
                <common-table
                        :data="inspectTableData"
                        :columns="inspectHead"
                        class="width100"
                        :pagination="false"
                        :border="false"
                        v-else-if="regionType === '6'"
                >
                    <template slot="paramCode" slot-scope="scope">
                        <el-input v-model="scope.row.paramCode" @change="changeInput" v-input-limit:fieldCode maxlength="30"></el-input>
                    </template>
                    <template slot="memo" slot-scope="scope">
                        <el-input v-model="scope.row.memo" @change="changeInput" maxlength="30"></el-input>
                    </template>
                    <template slot="type" slot-scope="scope">
                        <dg-select v-model="scope.row.type" :data="dataTypeList" @change="changeInput"></dg-select>
                    </template>
                    <template slot="paramName" slot-scope="scope">
                        <el-input v-model="scope.row.paramName" @change="changeInput" maxlength="30"></el-input>
                    </template>
                    <template slot="paramValue" slot-scope="scope">
                        <el-input v-model="scope.row.paramValue" @change="changeInput" maxlength="30"></el-input>
                    </template>
                </common-table>
            </el-form-item>
            <el-form-item label="排序字段"  class='singerH' v-if="regionType === '4'">
                <div style="display: flex;">
                    <div style="width:180px;" v-if="tableType === '1'" :style="{'width': moreTableSortInfo.length ? '180px' : '0px'}">
                        <div v-for="(item, k) in moreTableSortInfo" class="dataSet-list" :key="k"
                             :class="{'active': selectSortInfo.dataSetId == item.dataSetId }"
                             @click="changeDataSet(item, 'sort')"
                        >
                            {{item.dataSetName}}
                        </div>
                    </div>
                    <common-table
                            :data="tableType === '1' ? filterSortData : sortTableData"
                            :columns="sortTHeadData"
                            :pagination="false"
                            class="width100"
                            :border="false"
                            row-key="paramValue"
                    >
                        <template slot="orderType" slot-scope="scope">
                            <dg-select v-model="scope.row.orderType" :data="[{label:'升序',value:'asc'},{label:'降序',value:'desc'}]" @change="changeInput"></dg-select>
                        </template>
                        <template slot="operate" slot-scope="{row , $index}">
                            <el-button type="text" @click="moveSort( $index, 'up', row)" v-if="(tableType === '1' ? filterSortData.length > 1 : sortTableData.length > 1) && $index !== 0">上移</el-button>
                            <el-button type="text" @click="moveSort( $index, 'down', row)" v-if="(tableType === '1' ? filterSortData.length > 1 : sortTableData.length > 1) && $index !== ((tableType === '1' ? filterSortData.length - 1 : sortTableData.length - 1))">下移</el-button>
                            <el-button type="text" @click="sortDeL(row , $index)">删除</el-button>
                        </template>
                    </common-table>
                </div>
            </el-form-item>
            <el-form-item label="高级配置">
                <div v-if="regionType === '4'">
                    <span>查询条件匹配模式：</span>
                    <el-button type="text" @click="setMatchPattern">{{conditionInfo.length ? '修改匹配模式' :  '添加匹配模式'}}</el-button>
                    <el-button type="text" @click="conditionInfo = [];" v-if="conditionInfo.length">清空匹配模式</el-button>
                </div>
                <div class="senior" v-if="regionType ==='6'">
                    <span>是否启用批量查询：</span>
                    <dg-switch
                            v-model="batchQuery"
                            active-icon-class="el-icon-check"
                            inactive-icon-class="el-icon-close"
                            active-color="#52c41a"
                    ></dg-switch>
                    <div class="senior_setting" v-if="batchQuery">
                        <span>允许最大记录数：</span>
                        <el-input-number v-model="maxQueryNum" :precision="0" :step="1" :max="100" :min="0"></el-input-number> 条
                        <!--<el-input v-model="maxQueryNum"></el-input>-->
                    </div>
                </div>
                <!--<div class="senior">-->
                    <!--<span>数据结果是否缓存：</span>-->
                    <!--<dg-switch-->
                            <!--v-model="isCache"-->
                            <!--active-icon-class="el-icon-check"-->
                            <!--inactive-icon-class="el-icon-close"-->
                            <!--active-color="#52c41a"-->
                    <!--&gt;</dg-switch>-->
                    <!--<div class="senior_setting ml92" v-if="isCache">-->
                        <!--<span>缓存时效：</span>-->
                        <!--<el-input></el-input> 分钟-->
                    <!--</div>-->

                <!--</div>-->
                <!--<div v-if="regionType === '4'" class="senior">-->
                    <!--<span>返回结果是否分页：</span>-->
                    <!--<dg-switch-->
                            <!--v-model="isPage"-->
                            <!--active-icon-class="el-icon-check"-->
                            <!--inactive-icon-class="el-icon-close"-->
                            <!--active-color="#52c41a"-->
                    <!--&gt;</dg-switch>-->
                    <!--<div class="senior_setting ml78" v-if="isPage">-->
                        <!--<span>每页记录数：</span>-->
                        <!--<el-input></el-input> 条-->
                    <!--</div>-->
                    <!--<div class="ml125">当返回结果记录数大于100时请选择分页，不分页则最多返回100条记录</div>-->
                <!--</div>-->
            </el-form-item>
        </el-form>
        <setParams ref="setParams" :columnInfo="columnInfo"
                @setTableData="setTableData" :singleOrManyTable="tableType"
                   :requestTableData="requestTableData"
                   :responseTableData="responseTableData"
                   :sortTableData="sortTableData"
                   :region="regionType" :dataSetColumnList="dataSetColumnList"></setParams>
        <match-pattern ref="matchPattern" @saveConditionInfo="saveConditionInfo"></match-pattern>
    </div>
</template>

<script>
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import setParams from "./setParams"
    import MatchPattern from "./matchPattern";
    export default {
        name: "inquiryAndVerification",
        mixins: [ commonMixins, servicesMixins],
        components : {MatchPattern, setParams },
        props:{
            regionType: String,
            columnInfo: Array,//字段列表
            singleOrManyTable: String,
            dataSetColumnList: Array,//数据集列表
            desenList: Array,
            type: String,
            dataSetIds: Array, //选择的数据集id
        },
        data(){
            return{
                requestTableData : [], //请求参数数据
                responseTableData : [],//返回参数数据
                sortTableData:[],//排序字段数据
                batchQuery: false,
                maxQueryNum: 100,
                isCache: false,
                isPage: false,
                //返回参数表头
                responseTHeadData : [
                    {
                        prop: "paramCode",
                        label: "参数名",
                        minWidth: 120,
                    },{
                        prop: "paramValue",
                        label: "绑定字段",
                        minWidth: 150,
                    },{
                        prop: "memo",
                        label: "参数描述",
                        minWidth: 100,
                    },{
                        prop: "dataType",
                        label: "参数类型",
                        width: 130,
                    },{
                        prop: "desensitization",
                        label: "数据脱敏",
                        minWidth: 140,
                    },{
                        prop: 'operate',
                        label: "操作",
                        width: 180,
                    }
                ],
                //请求参数表头
                requestTHeadData : [
                    {
                        prop: "paramCode",
                        label: "参数名",
                        minWidth: 100,
                    },{
                        prop: "paramValue",
                        label: "绑定字段",
                        minWidth: 120,
                    },{
                        prop: "memo",
                        label: "参数描述",
                        minWidth: 100,
                    },{
                        prop: "dataType",
                        label: "参数类型",
                        width: 150,
                    },{
                        prop: "isMust",
                        label: "是否必须",
                        width: 120,
                    },{
                        prop: "example",
                        label: "示例值",
                        minWidth: 100,
                    },{
                        prop: "operate",
                        label: "操作",
                        width: 100,
                    },
                ],
                //信息核查返回数据
                inspectTableData: [],
                //信息核查返回参数表头
                inspectHead:[
                    {
                        prop: "datasetZhName",
                        label: "核查数据集",
                        minWidth: 130,
                    },
                    {
                        prop: "paramCode",
                        label: "参数名",
                        minWidth: 140,
                    },{
                        prop: "memo",
                        label: "参数描述",
                        minWidth: 140,
                    },{
                        prop: "type",
                        label: "参数类型",
                        minWidth: 120,
                    },{
                        prop: "paramName",
                        label: "参数值(存在记录)",
                        minWidth: 150,
                    },
                    {
                        prop: "paramValue",
                        label: "参数值(不存在记录)",
                        minWidth: 150,
                    }
                ],
                //排序字段请求头
                sortTHeadData: [
                    {
                        prop: "orderNum",
                        label: "序号",
                        width: 80,
                    },
                    {
                        prop: "paramCode",
                        label: "字段名",
                        minWidth: 140,
                    },{
                        prop: "memo",
                        label: "字段中文名",
                        minWidth: 140,
                    },{
                        prop: "dataType",
                        label: "字段类型",
                        minWidth: 120,
                    },{
                        prop: "orderType",
                        label: "排序方式",
                        minWidth: 150,
                    },
                    {
                        prop: "operate",
                        label: "操作",
                        width: 150,
                    }
                ],
                dataTypeList: [
                    {
                        label: 'String',
                        value: 'String'
                    },
                    {
                        label: 'Number',
                        value: 'Number'
                    },
                    {
                        label: 'Boolean',
                        value: 'Boolean'
                    },
                    {
                        label: 'Date',
                        value: 'Date'
                    }
                ],
                editData: '',
                filterResponseData: [],
                selectDataSet:{},
                conditionInfo: [],//匹配模式
                backDataSetInfo: [],//查询多表返回参数相关信息
                moreTableSortInfo : [], //查询多表排序参数信息
                selectSortInfo:{},
                filterSortData: [],
                tableType: this.singleOrManyTable,
            }
        },
        watch: {
            dataSetColumnList: {
                handler(val) {
                    this.initInspectData();
                },
                deep: true
            },
        },
        methods:{
            //编辑时设置参数
            setEditInitData(data, type){
                const vm = this;
                vm.tableType = data.singleOrManyTable;
                vm.editData = data;
                vm.batchQuery = data.batchQuery === '0';
                vm.maxQueryNum = data.maxQueryNum;
                //设置数据查询返回参数
                if(type === '4'){
                    vm.responseTableData = data.ginsengList || [];
                    vm.responseTableData.forEach(n=>{
                        n.name = n.paramName;
                        n.code = n.paramCode;
                        n.dataType = n.type;
                        n.isMust = n.isMust === 't' ? true : false;
                        n.desensitization = n.desensitization || 'noDesensitization';
                        n.id = n.paramId || n.id;
                        let dataSet = data.returnParamMappings.find(p=>p.datasetId == n.datasetId);
                        if(dataSet){
                            n.dataSetId = dataSet.datasetId;
                            n.dataSetTableName = dataSet.datasetName;
                            n.dataSetName = dataSet.datasetZhName;
                        }
                    })
                    vm.sortTableData = [];
                    data.orderFieldTables.forEach(n=>{
                        n.orderFields.forEach(p=>{
                            let obj = {
                                dataSetId: n.datasetId,
                                datasetId:  n.datasetId,
                                dataSetTableName:n.datasetName,
                                dataSetName:n.datasetZhName,
                                name: p.filedName,
                                memo: p.filedName,
                                code: p.fieldCode,
                                paramCode:p.fieldCode,
                                dataType: p.fieldType,
                                orderType: p.orderType || 'asc',
                                orderNum: p.orderNum,
                                paramValue: p.fieldCode
                            }
                            vm.sortTableData.push(obj)
                        })
                    })
                    vm.sortTableData = vm.sortTableData.sort((a,b)=>a.orderNum - b.orderNum);
                    vm.conditionInfo = data.encasulationJudgContion || [];
                }

                //设置信息核查返回参数
                if(type === '6'){
                    vm.inspectTableData = data.ginsengList || [];
                    vm.inspectTableData.forEach(n=>{
                        n.name = n.paramName;
                        n.code = n.paramCode;
                        n.dataType = n.type;
                        n.isMust = n.isMust === 't' ? true : false;
                        n.desensitization = n.desensitization || 'noDesensitization';
                        n.id = n.paramId || n.id;
                        let dataSet = data.tableColumnMappings.find(p=>p.datasetId == n.datasetId);
                        if(dataSet){
                            n.dataSetId = dataSet.datasetId;
                            n.dataSetTableName = dataSet.datasetName;
                            n.dataSetName = dataSet.datasetZhName;
                            n.datasetZhName = dataSet.datasetZhName;
                        }
                    })
                }

                if(vm.tableType === '0'){
                    vm.requestTableData = data.paramList || [];
                    vm.requestTableData.forEach(n=>{
                        n.code = n.paramCode;
                        n.dataType = n.type;
                        n.children = n.children ? n.children : [];
                        n.isMust = n.isMust === 't' ? true : false;
                        n.likeQuery = n.likeQuery === '1' ? true : false;
                        n.id = n.paramId || n.id ;
                        n.dataSetId = data.tableColumnMappings[0].datasetId;
                        n.datasetId =  n.dataSetId;
                        n.dataSetTableName = data.tableColumnMappings[0].datasetName;
                        n.dataSetName = data.tableColumnMappings[0].datasetZhName;
                        n.name = data.tableColumnMappings[0].datasetZhName
                    })

                }
                else if(vm.tableType === '1'){
                    data.tableColumnMappings && data.tableColumnMappings[0] && data.tableColumnMappings[0].columns.forEach(n=>{
                        let par = data.paramList.find(g=>g.paramCode == n.fieldAsCode) || {};
                        vm.requestTableData.push({
                            paramCode: n.fieldAsCode,
                            code: n.fieldAsCode,
                            memo: par.memo,
                            paramName: n.fieldAsName,
                            paramValue: n.fieldAsCode,
                            dataType: n.fieldType,
                            tableData: [],
                            isMust: par.isMust === 't',
                            example: par.example,
                        });
                    })

                    data.tableColumnMappings.forEach(n=>{
                        n.columns.forEach(p=>{
                            vm.requestTableData.forEach(y=>{
                                if(p.fieldAsCode === y.paramCode){
                                    let obj = { dataSet: n.datasetZhName, id: n.datasetId, code: n.datasetName, name: n.datasetZhName, column: [],bindColumn: p.fieldCode, dataSetTableName: n.datasetName,};
                                    y.tableData.push(obj)
                                }
                            })
                        })
                    })

                    // vm.requestTableData.forEach(y=>{
                    //     y.tableData = [];
                    //     data.tableColumnMappings.forEach(n=>{
                    //         n.columns.forEach(p=>{
                    //             let obj = { dataSet: n.datasetZhName, id: n.datasetId, code: n.datasetName, name: n.datasetZhName, column: [],bindColumn: p.fieldCode, dataSetTableName: n.datasetName,};
                    //             y.tableData.push(obj)
                    //         })
                    //     })
                    // })
                }
                if(vm.tableType === '1'){
                    vm.setDataSetGroup()
                }
            },
            initInspectData(){
                const vm = this;
                if(vm.regionType === '6' && vm.dataSetIds.length == vm.dataSetColumnList.length){
                    let arr = JSON.parse(JSON.stringify(vm.inspectTableData));
                    let data = [];
                    vm.dataSetColumnList.forEach((n,i)=>{
                        let obj = arr.find(p=>p.datasetId == n.id);
                        let info ={datasetZhName: n.name, datasetId: n.id, paramCode: 'sfcz', memo:'',type: 'String',paramName:'存在',paramValue:'不存在'};
                        data.push(obj ? obj : info)
                    })
                    vm.inspectTableData = data.map(n=>{
                        return { datasetId: n.datasetId, datasetZhName: n.datasetZhName, memo: n.memo, paramCode: n.paramCode, paramName: n.paramName, paramValue: n.paramValue, type: n.type,}
                    });
                }
                if(vm.tableType === '1' && vm.dataSetIds.length == vm.dataSetColumnList.length){
                    let info = [];
                    vm.requestTableData.forEach(n=>{
                        n.tableData = n.tableData.filter(r=>vm.dataSetIds.includes(r.id));
                        let data = {...n};
                        data.tableData = [];
                        data.code = n.paramCode;
                        vm.dataSetColumnList.forEach(g=>{
                            let hasSet = n.tableData.find(t=>g.id == t.id);
                            if(hasSet){
                                hasSet.column = g.column;
                                data.tableData.push(hasSet);
                            }
                            else{
                                let obj = {...g};
                                obj.bindColumn = '';
                                data.tableData.push(obj);
                            }
                        })
                        info.push(data);
                    })
                    vm.requestTableData = info;
                }
            },
            /**
             * 设置参数
             */
            initData(singleOrManyTable){
                const vm = this;
                if(!singleOrManyTable){
                    return
                }
                vm.tableType = singleOrManyTable;
                vm.initInspectData();
                if(vm.tableType == '1'){
                    vm.requestTableData.forEach(p=>{
                        p.tableData = p.tableData.filter(n=>vm.dataSetIds.includes(n.id))
                    })
                }
                else if(vm.tableType == '0'){
                    vm.requestTableData = vm.requestTableData.filter(n=>vm.dataSetIds.includes(n.datasetId))
                }

                if(vm.regionType === '4'){
                    vm.responseTableData = vm.responseTableData.filter(n=>vm.dataSetIds.includes(n.datasetId));
                    vm.sortTableData = vm.sortTableData.filter(n=>vm.dataSetIds.includes(n.datasetId));
                }
                if(vm.tableType === '1'){
                    vm.setDataSetGroup()
                }
            },
            changeInput(){
                this.$forceUpdate()
            },
            setParamsData(data, column , tableData){
                const vm = this;
                let info = [], list = [];
                info = data.filter((item) =>{return item[column]});
                if(vm[tableData].length){
                    info.forEach(p=>{
                        let columnInfo = vm[tableData].find(n=>n.code === p.code  && n.dataSetId == p.dataSetId);
                        p.orderType =  'asc';
                        if(column == 'setSortColumn' || column == 'setResponseColumn'){
                            p.paramCode = p.code;
                        }
                        list.push(columnInfo ? columnInfo : p)
                    })
                    vm[tableData] = list;
                }
                else{
                    vm[tableData] = info.map(n=>{
                        if(column == 'setSortColumn'){
                            n.paramCode = n.code;
                            n.orderType =  'asc';
                        }
                        if(column == 'setResponseColumn'){
                            n.paramCode = n.code;
                        }
                        return n;
                    });
                }
            },
            setTableData(data){
                const vm = this;
                if(vm.tableType === '0'){
                    vm.setParamsData(data, 'setSearchColumn', 'requestTableData');
                }
                vm.setParamsData(data, 'setResponseColumn', 'responseTableData');
                vm.setParamsData(data, 'setSortColumn', 'sortTableData');

                if(vm.tableType === '1'){
                    vm.setDataSetGroup()
                }
                if(vm.regionType === '4'){
                    vm.resetSort();
                }
            },
            setDataSetGroup(){
                const vm = this;
                vm.backDataSetInfo = [];
                vm.responseTableData.forEach(n=>{
                    let obj = {dataSetName: n.dataSetName, dataSetId: n.dataSetId, columnList: []};
                    let hasSet = vm.backDataSetInfo.find(p=>p.dataSetId == n.dataSetId)
                    if(hasSet){
                        hasSet.columnList.push(n);
                    }
                    else{
                        obj.columnList.push(n);
                        vm.backDataSetInfo.push(obj);
                    }
                })
                vm.changeDataSet(vm.responseTableData[0], 'response');

                vm.moreTableSortInfo = [];
                vm.sortTableData.forEach(n=>{
                    let obj = {dataSetName: n.dataSetName, dataSetId: n.dataSetId, columnList: []};
                    let hasSet = vm.moreTableSortInfo.find(p=>p.dataSetId == n.dataSetId)
                    if(hasSet){
                        hasSet.columnList.push(n);
                    }
                    else{
                        obj.columnList.push(n);
                        vm.moreTableSortInfo.push(obj);
                    }
                })
                vm.changeDataSet(vm.sortTableData[0], 'sort');
            },
            //切换数据集
            changeDataSet(data, type){
                if(type === 'sort'){
                    this.selectSortInfo = data;
                    this.filterSortData = this.sortTableData.filter(n=>n.dataSetId == data.dataSetId);
                    this.resetSort();
                }
                else{
                    this.selectDataSet = data;
                    this.filterResponseData = this.responseTableData.filter(n=>n.dataSetId == data.dataSetId)
                }
            },
            /**
             * 设置参数
             */
            setColumns(){
                this.columnInfo.forEach(element => {
                    element.setSearchColumn = false;
                    element.example = '';
                    element.setResponseColumn = false;
                    element.setSortColumn = false;
                    element.desensitization = 'noDesensitization';
                    this.requestTableData.forEach(item => {
                        if (item.paramValue === element.paramValue && item.dataSetId == element.dataSetId) {
                            element.setSearchColumn = true;
                            element.example = item.example;
                        }
                    });
                    this.responseTableData.forEach(e => {
                        if (e.paramValue === element.paramValue && e.dataSetId == element.dataSetId) {
                            element.example = e.example;
                            element.setResponseColumn = true;
                            element.desensitization = e.desensitization
                        }
                    });
                    this.sortTableData.forEach(e => {
                        if (e.paramValue === element.paramValue && e.dataSetId == element.dataSetId) {
                            element.example = e.example;
                            element.setSortColumn = true;
                        }
                    });
                });
                this.$refs.setParams.show();
            },
            addParaSave(){

            },
            /**
             * 新增请求参数字段
             */
            addColumn(){
                this.requestTableData.push({paramCode: '',dataType: 'String',tableData: []});
            },
            /**
             * 请求参数删除
             */
            columnDel(row, inx){
                const vm = this;
                vm.requestTableData.splice(inx, 1);
            },
            /**
             * 添加为排序字段
             */
            addSort(row){
                const vm = this;
                row.orderType =  'asc';
                this.sortTableData.push(row);

                let hasSet = vm.moreTableSortInfo.find(p=>p.dataSetId == row.dataSetId)
                if(!hasSet){
                    let obj = {dataSetName: row.dataSetName, dataSetId: row.dataSetId, columnList: []};
                    obj.columnList.push(row);
                    vm.moreTableSortInfo.push(obj);
                }
                vm.changeDataSet(vm.sortTableData[0], 'sort');
            },
            /**
             * 删除排序字段
             */
            sortDeL(row, inx){
                if(this.tableType === '1'){
                    this.filterSortData.splice(inx, 1);
                    let index = this.sortTableData.findIndex(n=>n.id == row.id);
                    if (index > -1) {
                        this.sortTableData.splice(index, 1);
                    }
                    if(!this.sortTableData.find(n=>n.dataSetId == row.dataSetId)){
                        let num = this.moreTableSortInfo.findIndex(n=>n.dataSetId == row.dataSetId)
                        this.moreTableSortInfo.splice(num, 1);
                        this.changeDataSet(this.sortTableData[0], 'sort');
                    }
                }
                else{
                    this.sortTableData.splice(inx, 1);
                }
                this.resetSort();
            },
            resetSort(){
                let table = this.tableType === '1' ? 'filterSortData' : 'sortTableData';
                this[table].forEach((n, i)=>{
                    n.orderNum = i+1;
                })
            },
            /**
             * 删除返回参数字段
             */
            responseDeL(row, inx){
                if(this.tableType === '1'){
                    this.filterResponseData.splice(inx, 1);
                    let index = this.responseTableData.findIndex(n=>n.id == row.id);
                    if (index > -1) {
                        this.responseTableData.splice(index, 1);
                    }
                    if(!this.responseTableData.find(n=>n.dataSetId == row.dataSetId)){
                        let num = this.backDataSetInfo.findIndex(n=>n.dataSetId == row.dataSetId)
                        this.backDataSetInfo.splice(num, 1);
                        this.changeDataSet(this.responseTableData[0], 'response');
                    }
                }
                else{
                    this.responseTableData.splice(inx, 1);
                }
            },
            /**
             * 删除请求参数字段
             */
            requestDeL(row, inx){
                const vm = this;
                vm.requestTableData.splice(inx, 1);
                let index = vm.conditionInfo.findIndex(n=>JSON.parse(n.judgCondition)[0].field == row.paramCode);
                vm.conditionInfo.splice(index,1);
            },
            /**
             * 排序上下移
             */
            moveSort( inx, type, row){
                let lastInx = type === 'up' ? inx - 1 : inx + 1;
                let table = this.tableType === '1' ? 'filterSortData' : 'sortTableData';
                this[table][inx] = this[table].splice(lastInx,1,this[table][inx])[0];
                if(this.tableType === '1'){
                    let allInx = this.sortTableData.findIndex(n=>n.code == row.code && n.dataSetId == row.dataSetId)
                    let allLstInx = type === 'up' ? allInx - 1 : allInx + 1;
                    this.sortTableData[allInx] = this.sortTableData.splice(allLstInx,1,this.sortTableData[allInx])[0];
                }
                this.resetSort();
            },
            isBind(row){
                return row.tableData && row.tableData.length && !row.tableData.find(n=>n.bindColumn === '')
            },
            /**
             * 绑定字段
             */
            bindColumn(row, index){
                const vm = this;
                if(!row.paramCode){
                    return vm.$message.warning('请先设置参数名');
                }
                let info = JSON.parse(JSON.stringify(vm.requestTableData));
                info.forEach(n=>{
                    n.code = n.paramCode;
                })
                let layer = vm.$dgLayer({
                    title: '添加绑定',
                    content: require("@/projects/DataCenter/views/plugin_3/metaService/components/addBind"),
                    maxmin: false,
                    props: {
                        dataSetList: vm.dataSetColumnList,
                        columnList: info,
                        currentColumn : row.paramCode
                    },
                    area: ['1000px', "60%"],
                    on: {
                        close() {
                            layer.close(layer.dialogIndex);
                        },
                        save(list){
                            vm.requestTableData = list;
                            layer.close(layer.dialogIndex);
                        }
                    },
                });
            },
            /**
            * 保存校验
            */
            validation(){
                const vm = this;
                if(vm.requestTableData.length === 0 || vm.requestTableData.filter(n=>!n.paramCode).length){
                    vm.$message.warning("请先设置请求参数");
                    return false;
                }
                let reqArr = vm.requestTableData.map(n=>n.paramCode);
                if(new Set(reqArr).size !== reqArr.length){
                    vm.$message.warning("请求参数参数名不能重复");
                    return false;
                }

                if(vm.tableType === '1' && vm.requestTableData.find(n=> !n.tableData.length)){
                    vm.$message.warning("请求参数请先添加绑定字段");
                    return false;
                }

                if((vm.regionType === "4" && (vm.responseTableData.length === 0 || vm.responseTableData.filter(n=>!n.paramCode).length)) || (vm.regionType === "6" && (vm.inspectTableData.length === 0 || vm.inspectTableData.filter(n=>!n.paramCode).length))){
                    vm.$message.warning("请先设置返回参数");
                    return false;
                }

                if(vm.regionType === "4" && vm.tableType === '1' && vm.backDataSetInfo.length != vm.dataSetIds.length){
                    vm.$message.warning("请先设置返回参数，多数据集每个数据集都需要有返回参数");
                    return false;
                }

                if(vm.regionType === "4"){
                    for(let i=0; i<= vm.dataSetColumnList.length - 1; i++){
                        let arr = vm.responseTableData.filter(p=>p.dataSetId == vm.dataSetColumnList[i].id).map(n=>n.paramCode);
                        if(new Set(arr).size !== arr.length){
                            let message = vm.tableType === '1' ? '同一个数据集' :''
                            vm.$message.warning( message +"返回参数参数名不能重复");
                            return false;
                        }

                        let sortArr = vm.sortTableData.filter(p=>p.dataSetId == vm.dataSetColumnList[i].id).map(n=>n.paramCode);
                        if(new Set(sortArr).size !== sortArr.length){
                            vm.$message.warning(vm.tableType === '1' ? '同一个数据集' :'' +"排序参数参数名不能重复");
                            return false;
                        }
                    }
                    if(vm.conditionInfo.length){
                        let list = [];
                        vm.conditionInfo.forEach(n=>{
                            let par = JSON.parse(n.judgCondition)
                            list = list.concat(par);
                        })
                        let arr = list.map(n=>n.field);
                        let mustList = vm.requestTableData.filter(n=>n.isMust).map(p=>p.paramCode);
                        for(let t = 0; t < mustList.length ; t++ ){
                            if(!arr.includes(mustList[t])){
                                vm.$message.warning(`请求参数 ${mustList[t]} 为必填字段，需设置匹配模式`);
                                return false;
                            }
                        }
                        if(arr.length > mustList.length){
                            vm.$message.warning(`匹配模式中含有非必填字段，请重新设置`);
                            return false;
                        }
                    }
                }
                else if(vm.regionType === "6"){
                    let someArr = [...vm.requestTableData].filter(x => [...vm.inspectTableData].some(y => y.paramCode === x.paramCode));
                    if(someArr.length){
                        vm.$message.warning('返回参数参数名不能和请求参数参数名一样');
                        return false;
                    }

                    let inspectArr = vm.inspectTableData.map(n=>n.paramCode);
                    if(new Set(inspectArr).size !== inspectArr.length){
                        vm.$message.warning("返回参数参数名不能重复");
                        return false;
                    }
                }
                return true;
            },
            getParamsInfo(){
                const vm = this;
                let info = {
                    requestTableData: vm.requestTableData,
                    responseTableData: vm.responseTableData,
                    inspectTableData: vm.inspectTableData,
                    tableColumnMappings: [],
                    batchQuery : vm.batchQuery ? '0' : '1',
                    maxQueryNum: vm.batchQuery ? Number(vm.maxQueryNum) : 0,
                }
                info.orderFieldTables = [];
                if(vm.regionType === "4"){
                    vm.sortTableData.forEach((n,i)=>{
                        let obj = info.orderFieldTables.find(p=>p.datasetId == n.dataSetId);
                        let orderInfo = {filedName: n.name, fieldCode: n.code, fieldType: n.dataType, orderType: n.orderType ,orderNum: n.orderNum};
                        if(obj){
                            obj.orderFields.push(orderInfo)
                        }
                        else{
                            let op = {datasetId: n.dataSetId, datasetName: n.dataSetTableName, datasetZhName: n.dataSetName, orderFields:[]};
                            op.orderFields.push(orderInfo)
                            info.orderFieldTables.push(op);
                        }
                    })
                    info.returnParamMappings = [];
                    vm.responseTableData.forEach(p=>{
                        let column = {filedName: p.name, fieldCode: p.code, fieldAsName: p.memo || p.paramCode, fieldAsCode: p.paramCode, fieldType: p.dataType};
                        let par = info.returnParamMappings.find(k=>k.datasetId == p.dataSetId);
                        if(par){
                            par.columns.push(column);
                        }
                        else{
                            let obj = {datasetId: p.dataSetId, datasetName: p.dataSetTableName, datasetZhName: p.dataSetName, columns: []}
                            obj.columns.push(column);
                            info.returnParamMappings.push(obj)
                        }
                    })
                    info.conditionInfo = vm.conditionInfo;
                }
                if(vm.tableType === '1'){
                    info.tableColumnMappings = [];
                    vm.requestTableData.forEach((n,i)=>{
                        n.tableData.forEach(p=>{
                            let column = {filedName: (p.column.find(k=>k.value === p.bindColumn) || {}).label || p.bindColumn , fieldCode: p.bindColumn, fieldAsName: n.paramCode, fieldAsCode: n.paramCode, fieldType: n.dataType};
                            let par = info.tableColumnMappings.find(k=>k.datasetId == p.id);
                            if(par){
                                par.columns.push(column);
                            }
                            else{
                                let obj = {datasetId: p.id, datasetName: p.dataSetTableName, datasetZhName: p.name, columns: []}
                                obj.columns.push(column);
                                info.tableColumnMappings.push(obj)
                            }
                        })
                    })
                }
                return info;
            },
            /**
             * 添加匹配模式
             */
            setMatchPattern(){
                const vm = this;
                let title= vm.conditionInfo.length ? '修改匹配模式' : '添加匹配模式';
                vm.$refs.matchPattern.show(vm.requestTableData, vm.conditionInfo, title);
            },
            saveConditionInfo(conditionInfo, fieldCodeList){
                this.conditionInfo = conditionInfo;
                this.requestTableData.forEach(n=>{
                    if(fieldCodeList.includes(n.paramCode)){
                        n.isMust = true;
                    }
                })
            },
            /**
             * 重置参数
             */
            resetParam(){
                const vm = this;
                vm.conditionInfo = [];
                vm.requestTableData = [];
                vm.responseTableData = [];
                vm.inspectTableData = [];
                vm.sortTableData = [];
                vm.filterSortData = [];
                vm.filterResponseData = [];
                vm.backDataSetInfo = [];
                vm.moreTableSortInfo = [];
                vm.selectSortInfo = {};
                vm.selectDataSet = {};
            },
        },
        mounted(){
            this.tableType = this.singleOrManyTable;
            this.initData();
        }
    }
</script>

<style scoped lang="less">
    .senior{
        margin-bottom: 10px;
        &_setting{
            display: inline-block;
            margin-left: 50px;
            .el-input{
                width: 100px;
            }
        }
    }
    .ml92{
        margin-left: 92px;
    }
    .ml78{
        margin-left: 78px;
    }
    .ml125{
        margin-left: 125px;
    }
    .dataSet-list{
        background-color: rgba(0,0,0,0.1);
        padding: 5px 10px;
        margin-bottom: 10px;
        white-space: nowrap; //禁止换行
        overflow: hidden;
        text-overflow: ellipsis;
        color: #333;
        cursor: pointer;
    }
    .dataSet-list.active{
        color: #1890ff;
        background-color: rgba(24,144,255,0.15);
    }
</style>