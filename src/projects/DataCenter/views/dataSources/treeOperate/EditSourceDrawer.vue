<template>
    <div class="editSourceDrawer">
        <el-drawer
                :visible.sync="dialog"
                :before-close="beforeClose"
                direction="rtl"
                ref="drawer"
                custom-class="drawer"
                :wrapperClosable="false"
                @closed="removeF"
        >
            <div class="ce-drawer__title" :title="nodeData.label + title" slot="title">{{nodeData.label + title}}</div>

            <EditSourceForm ref="form" v-if="reNew" />
            <div class="drawer__footer">
                <el-button size="mini" @click="closeDrawer">关闭</el-button>
                <el-button size="mini" @click="checkLink" >连接测试</el-button>
                <el-button type="primary" size="mini" @click="$refs.drawer.closeDrawer()" :loading="loading">{{ loading
                    ? '添加中' : '添加' }}
                </el-button>
            </div>
        </el-drawer>
    </div>
</template>

<script>
    import EditSourceForm from './EditSourceForm'
    export default {
        name: "EditSourceDrawer",
        components : {
            EditSourceForm
        },
        data() {
            return {
                dialog: false,
                title: '_数据源接入配置',
                loading: false ,
                reNew : false ,
                nodeData : {}
            }
        },
        methods: {
            openDrawer(data) {
                this.nodeData = data;
                this.dialog = true;
                this.reNew = true;
                this.$axios.get("/manage/dataWarehouse/lookDWDBInstance?elementId="+this.nodeData.id).then(res =>{
                    console.log("编辑数据源返回数据：",res);
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                })
            },
            closeDrawer() {
                this.dialog = false;
            },
            beforeClose(done) {
                this.$confirm('添加当前数据源？')
                    .then( () => {
                        this.loading = true;
                        let sourceData = this.$refs.form.getSourceData(); //表单数据
                        setTimeout(() => {
                            this.loading = false;
                            done();
                        }, 1000);
                    })
                    .catch( err => {
                    });
            },
            removeF(){ //清空操作数据
                this.reNew = false;
            },
            checkLink(){ //连接测试

            }
        }
    }
</script>

<style scoped>
    .drawer__footer {
        position: absolute;
        left: 0;
        bottom:0;
        padding:10px 0;
        text-align: center;
        width: 100%;
        border-top:1px solid #ddd;
    }
    .ce-drawer__title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

</style>
<style>
    .drawer > .el-drawer__header {
        padding:10px 16px;
        font-size: 16px;
        margin-bottom: 0;
        border-bottom: 1px solid #ddd;
    }

</style>