import UserFormPop from "@/components/common/user-form-pop";
import RoleFormPop from "@/components/common/role-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import {servicesMixins} from "../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import TreeAndList from "@/components/layout/treeAndListUi_3/index.vue"
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {time} from '@/api/time'
export default {
    name: "roles-manage",
    mixins: [commonMixins , servicesMixins , listMixins,coustTableH],
    components: {UserFormPop, RoleFormPop , TreeAndList},
    filters: {
        formatDate(ti) {
            return time.formatTimeToStr(ti, 'yyyy-MM-dd hh:mm');
        }
    },
    computed: {
        ...mapGetters(["userInfo", "systemName", "userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    data() {
        return {
            //角色管理搜索
            searchVal: "",
            //添加按钮弹窗控制显示与否
            addVisible: false,
            //编辑弹窗控制显示与否
            editVisible: false,
            //角色信息编辑
            roleFormData: {
                roleId: "",
                objCode: "",
                objName: "",
                editUser: "",
                functions: []
            },
            //是否展示上一步按钮
            showPrev: true,
            // 角色管理表格数据
            tableData: [],
            tableHead : [
                {
                    prop: "objCode",
                    label: "角色ID",
                    minWidth: "160",
                    align: "left"
                },
                {
                    prop: "objName",
                    label: "角色名称",
                    align: "left"
                },
                {
                    prop: "updatetime",
                    label: "最后编辑时间",
                    align: "center"
                },
                {
                    prop: "operate",
                    label: "操作",
                    align: "center",
                    width:220 ,
                    resizable :false
                },
            ],
            addRoleRight: $right["roleManagementAddRole"],
            //操作栏
            operator: [
                {
                    label: '编辑',
                    clickFn: this.edit,
                    condition: (row, right) => row.objCode !== this.$roleCode && right.indexOf($right["roleManagementUpdataRole"]) > -1,
                    icon: "&#xe6ad;"
                }, {
                    label: '详情',
                    clickFn: this.rowDetail,
                    condition: (row, right) => row.objCode !== this.$roleCode && right.indexOf($right["roleManagementQueryRole"]) > -1,
                    icon: "&#xe6c5;"
                }, {
                    label: '权限',
                    clickFn: this.setRight,
                    condition: (row, right) => row.objCode !== this.$roleCode && right.indexOf($right["roleManagementSetAuth"]) > -1,
                    icon: "&#xe6b5;"
                }, {
                    label: '删除',
                    condition: (row, right) => row.objCode !== this.$roleCode && right.indexOf($right["roleManagementDeleteRole"]) > -1,
                    clickFn: this.onDel,
                    icon: "&#xe847;"
                }
            ],

            //分页
            paginationProps: {
                currentPage: 1,
                pageSizes: [10, 20],
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            total: 0,
            roleForm: {
                title: "",
                label: "",
                visible: false,
                loading: false,
                state: "",
                functionCode:"" ,//功能code
            },
            //弹窗表格数据
            popTableData: [],
            isEdited: false ,
            selectedRight : []
        };
    },
    methods: {
        //更新角色
        updateRole(data) {
            const vm = this;
            let {roleForm ,rolesServices, rolesMock } = this;
            let services = vm.getServices(rolesServices, rolesMock);
            let dataVo = {
                editor: vm.userInfo.id,
                lastEditime: new Date().getTime(),
                functions: data.functions,
                roleCode: data.objCode,
                roleName: data.objName,
                roleId: data.roleId,
                addFunctions: [],
                deleteFunctions: []
            };
            vm.updateFn(dataVo, vm, roleForm, services);
        },
        updateFn(dataVo, vm, roleForm, services) {
            roleForm.loading = true;
            services.setAuth(dataVo, roleForm).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("保存成功");
                    vm.changePage(1);
                    roleForm.visible = false;
                    vm.addVisible = false;
                }
            })
        },
        updateRoleName(dataVo, vm, roleForm, services) {
            roleForm.loading = true;
            services.updataRole(dataVo, roleForm).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("保存成功");
                    vm.changePage(1);
                    roleForm.visible = false;
                    vm.addVisible = false;
                }
            })
        },
        getFunctionTree() {
            const vm = this , {rolesServices, rolesMock} = this;
            let services = vm.getServices(rolesServices, rolesMock);
            services.queryFunctionTree(process.env.VUE_APP_SECRET).then(res => {
                if (res.data.status === 0) {
                    vm.changePage(1);
                    let result = res.data.data;
                    vm.popTableData = [];
                    for (let i = 0; i < result.length; i++) {
                        let parent = result[i];
                        if (parent.children) {
                            parent.children.forEach(r => {
                                let actions = r.children ? r.children.map(child => {
                                    child.value = child.id;
                                    child.label = child.name;
                                    return child;
                                }) : [
                                    {
                                        label: r.name,
                                        value: r.id
                                    }
                                ];
                                let data = {
                                    code: r.id,
                                    system: vm.systemName,
                                    menu: parent.name,
                                    parentCode:parent.code,
                                    func: r.name,
                                    selectAll: [],
                                    actions: actions
                                };
                                vm.popTableData.push(data);
                            })
                        }
                    }

                }
            })
        },
        //权限
        async setRight(row) {
            const vm = this;
            await vm.queryRoleById(row.id, row);
            vm.setFunSelect(vm.roleFormData.functions);
            vm.addVisible = true;
            vm.showPrev = false;
            vm.isEdited = true;
        },
        //详情
        rowDetail(row) {
            this.edit(row, 'detail', "角色信息详情")
        },
        //删除行
        onDel(row) {
            const vm = this, {rolesServices, rolesMock} = this;
            let services = vm.getServices(rolesServices, rolesMock);
            this.$dgAlert(`你确定删除\"${row.objName}\"该角色? <br> (删除该角色后，关联的用户失去该角色的权限)`, "提示", {
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true,
                dangerouslyUseHTMLString: true
            }).then(() => {
                services.deleteRole(row.id).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功");
                        vm.changePage(1);
                    }
                })
            }).catch(_ => {
            })
        },
        addRole() {
            const vm = this, {rolesServices, rolesMock} = this;
            let {roleForm} = this;
            roleForm.title = "添加角色信息";
            roleForm.label = "角色";
            roleForm.state = 'add';
            roleForm.functionCode = 'roleManagementAddRole';
            let services = vm.getServices(rolesServices, rolesMock);
            services.getRoleRandom().then(res => {
                if (res.data.status === 0) {
                    vm.roleFormData = {
                        roleId: "",
                        objName: "",
                        objCode: res.data.data,
                        editUser: vm.userInfo.objName,
                        functions: []
                    };
                    vm.showPrev = true;
                    roleForm.visible = true;
                }
            });
        },
        async queryRoleById(id, row) {
            const vm = this, {rolesServices, rolesMock} = this;
            let {roleForm} = this;
            let services = vm.getServices(rolesServices, rolesMock);
            await services.queryByRoleId(id).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.roleFormData = {
                        roleId: vm.isMock ? row.id : result.roleId,
                        objName: vm.isMock ? row.objName : result.roleName,
                        objCode: vm.isMock ? row.objCode : result.roleCode,
                        editUser: result.editor,
                        functions: result.functions
                    };
                }
            })
        },
        //详情
        async queryRole(id, row) {
            const vm = this, {rolesServices, rolesMock} = this;
            let services = vm.getServices(rolesServices, rolesMock);
            await services.queryRole(id).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.roleFormData = {
                        roleId: vm.isMock ? row.id : result.roleId,
                        objName: vm.isMock ? row.objName : result.roleName,
                        objCode: vm.isMock ? row.objCode : result.roleCode,
                        editUser: result.editor,
                        functions: result.functions
                    };
                }
            })
        },
        //编辑弹窗功能显示与否
        edit(row, state = 'edit', title) {
            const vm = this;
            let {roleForm} = this;
            roleForm.title = title || "角色信息编辑";
            roleForm.label = "角色";
            roleForm.state = state;
            roleForm.visible = true;
            roleForm.functionCode = title ? 'roleManagementQueryRole' : 'roleManagementUpdataRole';
            if(state === 'detail') {
                vm.queryRole(row.id, row);
            }else {
                vm.queryRoleById(row.id, row);
            }
        },
        clearFuns() {
            this.popTableData.forEach(pop => {
                pop.selectAll = [];
            })
        },
        async setFunSelect(funs = []) {
            const vm = this;
            await vm.clearFuns();
            vm.selectedRight = [];
            vm.popTableData.forEach(pop => {
                if (funs.length) {
                    funs.forEach(f => {
                        if (f) {
                            let res = pop.actions.filter(act => {
                                return act.value === f.funcCode;
                            });
                            if (res.length) {
                                vm.selectedRight.push(res[0].value);
                                pop.selectAll.toString();
                                if (pop.selectAll.length) {
                                    pop.selectAll += ',' + res[0].value;
                                } else {
                                    pop.selectAll = res[0].value;
                                }
                            }
                        }

                    })
                }
            })
        },
        //保存权限编辑
        saveRole(funIds, isNew ,add , del) {
            const vm = this, {rolesServices, rolesMock} = this;
            let {roleForm, roleFormData} = this;
            let services = vm.getServices(rolesServices, rolesMock);
            let data = {
                editor: vm.userInfo.id,
                lastEditime: new Date().getTime(),
                functions: funIds,
                roleCode: roleFormData.objCode,
                roleName: roleFormData.objName,
                roleId: roleFormData.roleId,
                addFunctions : add ,
                deleteFunctions : del
            };
            if (isNew) {
                vm.addRight(services, vm, data, roleForm);
            } else {
                vm.updateRight(services, vm, data, roleForm);
            }

        },
        //修改权限
        updateRight(services, vm, data, roleForm) {
            vm.updateRoleName(data, vm, roleForm, services);
        },
        addRight(services, vm, data, roleForm) {
            roleForm.loading = true;
            services.addRole(data, roleForm, roleForm).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("添加成功");
                    vm.changePage(1);
                    vm.addVisible = false;
                }
            })
        },
        saveUser(data) {
            const vm = this;
            let {roleForm} = this;
            roleForm.visible = false;
            vm.addVisible = true;
            vm.$refs.roleFormPop.resetActiveName();
            vm.isEdited = false;
            vm.setFunSelect();
        },
        prevRole(data) {
            const vm = this;
            let {roleForm} = this;
            vm.addVisible = false;
            roleForm.visible = true;
        },
        async changePage(index) {
            const vm = this, {rolesServices, rolesMock} = this;
            let {settings} = this;
            settings.loading = true;
            let data = {
                code: vm.searchVal,
                name: vm.searchVal,
                pageNum: index,
                pageSize: vm.paginationProps.pageSize
            };
            let services = vm.getServices(rolesServices, rolesMock);
            await services.queryRoleList(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.tableData = res.data.data.dataList;
                    vm.total = res.data.data.totalCount;
                    vm.paginationProps.currentPage = res.data.data.pageIndex;
                }
            })
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        //mock 查询列表数据
        async searchTableData() {
            const vm = this;
            let searchVal = vm.searchVal.toLowerCase();
            await vm.changePage(1);
            vm.tableData = vm.tableData.filter(tab => {
                return tab.objName.toLowerCase().indexOf(searchVal) > -1 || tab.objCode.toLowerCase().indexOf(searchVal) > -1;
            })
        }
    },
    created() {
        this.getFunctionTree();
    }
};
