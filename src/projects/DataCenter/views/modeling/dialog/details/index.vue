
<template>
    <div class="details">
        <div class="details__close">
            <span class="ml20 details__close-title" :title="title">{{ title }}</span>
            <el-tabs class="details__close_tab" v-model="activeName" @tab-click="tabClick">
                <el-tab-pane v-for="(tab , inx) in tabPanes" :key="inx" :label="tab.label" :name="tab.value">
                </el-tab-pane>
            </el-tabs>
            <span class="details__close-i">
                <i class="el-icon-edit poi mr15" @click="editModel" v-if="editable">编辑模型</i>
                <i class="mr10" :class="['dg-iconp', !isFull ? 'icon-maximize' : 'icon-minimum']" :title="isFull? closeFullScreenTip : fullScreenTip" @click="handleFull"></i>
                <i class="dg-iconp icon-l-close poi" @click="handleClose" :title="'关闭'"></i>
            </span>
        </div>
        <div class="details__content">
            <div class="details__content_main">
                <component ref="tableList" class="list_cont" :is="activeName" :info="selectList" :type="type" :isAi="false" :isEdit="isEdit" :serviceDetail="{}" v-if="!isEdit"></component>
            </div>
        </div>
    </div>
</template>


<script>
    import {common} from "@/api/commonMethods/common";
    import modelSetting from "./detail-page/modelSetting";
    import modelResult from "./detail-page/modelResult";
    import apiServer from "./detail-page/apiServer/index";
    import runHistory from "./detail-page/runHistory";
    import modelShare from "./detail-page/modelShare";
    import $right from "@/assets/data/right-data/right-data"
    import {mapGetters} from "vuex"
    export default {
        name: "index",
        mixins : [common],
        components:{
            modelSetting,
            modelResult,
            apiServer,
            runHistory,
            modelShare
        },
        computed: {
            ...mapGetters(["userRight"]),
            rights() {
                let rights = [];
                if (this.userRight) {
                    this.userRight.forEach(r => {
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            },
            tabPanes(){
                return [
                    {label: "模型配置", value: "modelSetting"},
                    {label: "模型结果", value: "modelResult"},
                    {label: "API服务", value: "apiServer"},
                    {label: "分享情况", value: "modelShare"},
                    {label: "运行历史", value: "runHistory"},
                ].filter(t => {
                    // 他人空间 仅查看
                    if(this.isOtherUser) return ['modelSetting'].includes(t.value);
                    else return true;
                })
            },
            editable(){
                const {activeName , rights, editModelCode, isOtherUser} = this;
                return activeName === 'modelSetting' && rights.includes(editModelCode) && !isOtherUser;
            }
        },
        props : {
            row:Object,
            isOtherUser: Boolean, // 其他用户，仅查看
        },
        data(){
            return {
                title: '',
                isFull: false,
                fullScreenTip : "全屏",
                closeFullScreenTip : "退出全屏",
                selectList: {},
                activeName: "modelSetting",
                editModelCode:$right['processModelingResultReuseObj'],
                isEdit: false,
                type:'modeling'
            }
        },
        created(){
            if(this.row) this.show(this.row);
        },
        methods:{
            clearD(){
                this.serachText = "";
                this.visible = false;
            },
            show(row){
                this.title = row.modelName;
                this.selectList = row;
                this.visible = true;
            },
            editModel(){
                const vm = this;
                vm.isEdit = true;
                let row = vm.selectList;
                let page;
                if (row.taskgroup === "QUICK_SPARK") {
                    page = require("@/projects/DataCenter/views/modeling/dialog/rapid-analysis/index.vue");
                } else {
                    page = require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue");
                }

                let layer = this.$dgLayer({
                    content: page,
                    skin: "new-model",
                    maxmin: true,
                    noneBtnField: true,
                    props: {
                        dirId: row.dirParentId,
                        isCase : false,
                        container: 'containerEdit',
                        isDetails: true
                    },
                    area: ["100%", "100%"],
                    on: {
                        close() {
                            layer.close(layer.dialogIndex);
                            vm.isEdit = false;
                            vm.$nextTick(()=>{
                                vm.$refs.tableList.lookModel();
                            })
                        }
                    },
                });
                layer.$children[0].addTab(row, false);
            },
            tabClick(){
                this.isEdit = false;
            },
            //关闭
            handleClose() {
                const vm = this;
                vm.$emit("close");
            },
            /**
             * 全屏
             */
            handleFull() {
                this.isFull = !this.isFull;
                if (
                    (document.fullScreenElement !== undefined && document.fullScreenElement === null) ||
                    (document.msFullscreenElement !== undefined && document.msFullscreenElement === null) ||
                    (document.mozFullScreen !== undefined && !document.mozFullScreen) ||
                    (document.webkitIsFullScreen !== undefined && !document.webkitIsFullScreen)
                ) {
                    if (document.documentElement.requestFullScreen) {
                        document.documentElement.requestFullScreen();
                    } else if (document.documentElement.mozRequestFullScreen) {
                        document.documentElement.mozRequestFullScreen();
                    } else if (document.documentElement.webkitRequestFullScreen) {
                        document.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                    } else if (document.documentElement.msRequestFullscreen) {
                        document.documentElement.msRequestFullscreen();
                    }
                } else {
                    if (document.cancelFullScreen) {
                        document.cancelFullScreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.webkitCancelFullScreen) {
                        document.webkitCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                }
            },
        }
    }
</script>

<style scoped lang="less">
    .details {
        height:100%;
        overflow-y: hidden;
        position: relative;
        &__close {
            height:40px;
            line-height: 40px;
            background-color: #fff;
            border-bottom: 1px solid #e5e5e5;
            padding-top:5px;
            display: flex;
            justify-content: space-between;

            &-i {
                cursor: pointer;
                margin-right: 20px;
                font-size: 16px;
            }
            &-title {
                font-weight: bold;
                font-size: 16px;
                max-width:33%;
                white-space:nowrap;
                overflow:hidden;
                text-overflow:ellipsis;
            }
            &_tab{
                position: absolute;
                left: 50%;
                transform: translate(-50%,0);
            }
        }
        &__content {
            height:calc(100% - 50px);
            margin-top:10px;
            &_main {
                height: 100%;
            }
        }
    }
    .details__close_tab {
        &.el-tabs--top /deep/ .el-tabs__item.is-bottom:nth-child(2),
        &.el-tabs--top /deep/ .el-tabs__item.is-top:nth-child(2) {
            padding-left: 30px;
        }
    }

    .details__close_tab /deep/.el-tabs__nav-wrap::after {
        position: static !important;
    }
</style>
