import numerify from 'numerify'
export const mapMixins = {
    data() {
        const jsonMap = {
            "110000": "beijing",
            "110100": "beijing",
            "120000": "tianjin",
            "120100": "tianjin",
            "130000": "hebei",
            "140000": "shanxi",
            "150000": "neimenggu",
            "210000": "liaoning",
            "220000": "jilin",
            "230000": "heilongjiang",
            "310000": "shanghai",
            "310100": "shanghai",
            "320000": "jiangsu",
            "330000": "zhejiang",
            "340000": "anhui",
            "350000": "fujian",
            "360000": "jiangxi",
            "370000": "shandong",
            "410000": "henan",
            "420000": "hubei",
            "430000": "hunan",
            "440000": "guangdong",
            "450000": "guangxi",
            "460000": "hainan",
            "500000": "chongqing",
            "500100": "chongqing",
            "510000": "sichuan",
            "520000": "guizhou",
            "530000": "yunnan",
            "540000": "xizang",
            "610000": "shanxi1",
            "620000": "gansu",
            "630000": "qinghai",
            "640000": "ningxia",
            "650000": "xinjiang",
            "710000": "taiwan",
            "810000": "xianggang",
            "820000": "aomen"
        };
        return {
            jsonMap
        }
    },
    methods: {
        /**
         * 获取地图json
         * @param hasData
         * @param nodeData
         * @returns {Promise<void>}
         */
        async initMap(hasData, nodeData) {
            const vm = this;
            let mapPos = "";
            mapPos = vm.jsonMap[vm.code] || vm.code;
            if (vm.code !== 'china') {
                if (vm.jsonMap[vm.code] && mapPos) {
                    await vm.$axios({
                        method: 'get',
                        url: `http://${window.location.host}/data/map/province/${mapPos}.json`,
                        dataType: "json",
                        cache: true
                    }).then(res => {
                        vm.mapSetting(res.data, hasData, nodeData);
                    }).catch(error => {
                    })
                } else {
                    await vm.$axios({
                        method: 'get',
                        url: `http://${window.location.host}/data/map/main-city/${mapPos}.json`,
                        dataType: "json",
                        cache: true
                    }).then(res => {
                        vm.mapSetting(res.data, hasData, nodeData);
                    }).catch(error => {
                    })
                }
            } else {
                await vm.$axios({
                    method: 'get',
                    url: `http://${window.location.host}/data/map/china.json`,
                    dataType: "json",
                    cache: true
                }).then(res => {
                    vm.mapSetting(res.data, hasData, nodeData);
                }).catch(error => {
                })
            }
        },
        /**
         * 设置地图浮层
         * @param measure 度量
         */
        setMapTooltip(measure) {
            const vm = this;
            vm.extend.tooltip.formatter = function (params) {
                let funcLabel;
                let result, xValue = [], xcode, row, xString = "", xResult = "";
                row = vm.chartData.rows[params.dataIndex];
                if (row === undefined) {
                    result = `<div>\
                                <div class="f16 mb5">${params.name}  
                            </div>`;
                    return result;
                }
                let length = measure.length;
                for (let key in row) {
                    for (let index = 1; index <= length; index++) {
                        xcode = vm.chartData.columns[index];
                        xValue[index] = row[xcode];
                    }
                }
                for (let index = 1; index <= length; index++) {
                    if (xValue[index]) {
                        funcLabel = measure[index - 1].func.name;
                        let paramsVal = measure[index-1].data.numberFormat && measure[index-1].data.numberFormat !== "none" ? numerify(xValue[index] , vm.format[measure[index-1].data.numberFormat]) : xValue[index];//数据格式
                        xString = vm.chartData.columns[index] + ":" + paramsVal + "(" + funcLabel + ")";
                        xResult += "<div>" + " <span class='dib vm mr5' style='width:14px;height: 14px;border-radius:4px;background:" + vm.extend.color[index] + ";'></span>" + xString + " </div>";
                    }
                }
                result = `<div>
                               <div class="f16 mb5">${params.name}</div>
                               <div>${xResult} </div>
                          </div>`;
                return result;
            }
        }
    }
};