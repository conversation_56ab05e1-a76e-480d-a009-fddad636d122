<template>
    <div>
        <ve-histogram :data="chartData" :extend="extend" :settings="chartSetting"></ve-histogram>
    </div>
</template>

<script>
    export default {
        name: "HistogramChart",
        props: {
            histogramChart: Object,
            plugData: Object
        },
        data() {
            const vm = this;
            this.extend = {
                toolbox: {
                    feature: {
                        saveAsImage: {
                            type: 'png',
                            name: this.plugData.title,
                            connectedBackgroundColor: '#fff',
                            excludeComponents: ['toolbox'],
                            show: true,
                            title: '下载',
                        },
                    },
                    top : "20"
                },
                dataZoom: [{
                    type: 'inside',
                    start: 0,
                    maxValueSpan: 200
                }, {
                    start: 0,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '100%',
                    height: 16,
                    showDetail: false,
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }],
                legend: {
                    show: true,
                    type: 'scroll',
                    top: '0',
                },
                grid: {
                    top: 66,
                    bottom: 50,
                    right: 40,
                    left: 40
                },
                xAxis: {
                    name: this.plugData.classificationL,
                    nameLocation: 'end',
                    offset : 8,
                    nameTextStyle: {
                        align: 'right',
                        verticalAlign: 'top',
                    },
                },
                yAxis: {
                    position: "left",
                    name: "数据量",
                    nameLocation: 'end',
                    nameTextStyle: {
                        align: 'left',
                        verticalAlign: 'middle'
                    }
                },
                tooltip: {
                    trigger: "item",
                    formatter: function (params) {
                        let result;
                        if (!vm.plugData.groupingL) {
                            result = `<div>\
                                        <div class="f14 mb5">${params.name + '(' + (vm.plugData.classificationL || vm.plugData.classification)}) :</div>\
                                        <span class="dib vm mr5" style="width:10px;height: 10px;border-radius:5px;background:${params.color};"></span>\
                                        <span>${params.seriesName} : ${params.value} </span>
                                    </div>`;
                        } else {
                            result = `<div>\
                                        <div class="f14 mb5">${params.name + '(' + (vm.plugData.classificationL || vm.plugData.classification)}) :</div>\
                                        <span class="dib vm mr5" style="width:10px;height: 10px;border-radius:5px;background:${params.color};"></span>\
                                        <span>${params.seriesName}(${vm.plugData.groupingL}) : ${params.value}(数据量) </span>
                                    </div>`;

                        }
                        return result;
                    }
                }
            };
            return {
                chartData: {
                    columns: [],
                    rows: []
                },
                chartSetting: {
                   /* labelMap: {
                        'x坐标': this.plugData.classificationL,
                    },*/
                },
            }
        },
        methods: {
            init() {
                this.chartData = this.histogramChart;
                // _this.extend = this.histogramChart;
                /*if (_this.histogramChart.legend.data.length === 0) {
                    _this.chartData.columns.push('x坐标', '数据量');
                    _this.histogramChart.xAxis[0].data.forEach((item, i) => {
                        let isRepeat  = false;
                        let subscript = 0;
                        for(let k = 0; k <  _this.chartData.rows.length;k++){
                            let numberData = isNaN(item) ? item : Number(item).toFixed(4);
                            if( i !== 0 && _this.chartData.rows[k]["x坐标"] === numberData){//Number(item).toFixed(4)
                                isRepeat = true;
                                subscript = k;
                                break;
                            }
                        }
                        _this.chartData.rows.push({
                            "x坐标": isNaN(item) ? item : Number(item).toFixed(4),//Number(item).toFixed(4),
                            "数据量": isRepeat?_this.chartData.rows[subscript]["数据量"] + _this.histogramChart.series[0].data[i]:_this.histogramChart.series[0].data[i],
                        })
                    })
                } else {
                    _this.chartData.columns.push('x坐标');
                    _this.histogramChart.xAxis[0].data.forEach((item, i) => {
                        _this.chartData.rows.push({
                            "x坐标": item
                        });
                    }),
                        _this.histogramChart.series.forEach((item) => {
                            _this.chartData.columns.push(item.name);
                            _this.chartData.rows.forEach((val, i) => {
                                val[item.name] = item.data[i];
                            })
                        })
                }*/
            },
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>

</style>