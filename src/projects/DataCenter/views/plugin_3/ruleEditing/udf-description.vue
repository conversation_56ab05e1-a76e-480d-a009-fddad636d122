<script>
export default {
    name: "udf-description",
    props: {
        desc: {
            type: Object,
            default: ()=>{}
        },
        isStandard: Boolean,
    },
    data(){
        return {
            title: "参数说明",
            example: '示例',
            returnType: "返回类型 : ",
            name: '算子名 : ',
            expression: "格式 : ",
            effect: '作用 : ',
        }
    }
}
</script>

<template>
    <div class="ova height100">
        <ul class="ce-description" v-if="desc.name">
            <li class="ce-description_msg pb6"><span class="b">{{name}}</span>{{ desc.name }}</li>
            <li class="ce-description_msg b"><span>{{expression}}</span>{{ desc.expression }}</li>
            <li class="ce-description_msg pb6"><span class="b">{{effect}}</span>{{ desc.msg }}</li>
            <div v-show="!isStandard">
                <li class="ce-description_title b">{{ example }}</li>
                <li class="ce-description_msg">{{ desc.exampleCont }}</li>
                <li class="ce-description_title b"
                    v-if="desc.params && desc.params.length">
                    {{ title }}
                </li>
                <li class="ce-description_params">
                    <div class="ce-description_p" v-for="(item, inx) in desc.params" :key="inx">
                        <div class="ce-description_p-name">{{ item.name }}({{
                                item.code
                            }})
                        </div>
                        <div class="ce-description_p-type">{{ item.dataType }}</div>
                    </div>
                </li>
            </div>
            <li class="ce-description_msg"><span class="b">{{ returnType }}</span>{{ desc.returnType }}</li>
        </ul>
    </div>
</template>

<style scoped lang="less">
.ce-description {
    padding: 6px 10px 10px 10px;
    line-height: 24px;

    &_ti {
        line-height: 30px;
    }

    &_title {
        list-style: disc;
        margin-left: 20px;
    }

    &_msg {
        word-wrap: break-word;
    }

    &_params {
        display: flex;
        flex-wrap: wrap;
    }

    &_p {
        width: calc(50% - 10px);
        //flex: 1;
        margin: 5px;
        display: flex;
        padding: 5px 12px;
        background: #f2f2f2;
        border-radius: 4px;
        justify-content: space-between;
        box-sizing: border-box;

        &-name {
            color: #555;
        }

        &-type {
            color: #888;
        }

    }
}
</style>
