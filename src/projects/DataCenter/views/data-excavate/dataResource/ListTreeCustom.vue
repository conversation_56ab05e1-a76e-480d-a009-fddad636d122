<template>
    <div class="listTreeCustom">
        <div class="filter-b">
            <el-input placeholder="请输入资源名称搜索" v-model.trim="filterText" @input="queryData" size="small" class="ce-search">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
        </div>
        <div class="tree-cont selectn">
            <el-tree
                    ref="tree"
                    :data="data"
                    node-key="id"
                    default-expand-all
                    :expand-on-click-node="false"
                    :props="defaultProps"
                    :filter-node-method="filterNode"
                    v-infinite-scroll="loadData"
                    infinite-scroll-immediate="false"
                    infinite-scroll-delay="500"
            >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span class="node-label" :title="'数据源:'+data.dwname+'\n数据对象:'+node.label" draggable="true"
                              @dragend="dragend"
                              @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data  })"
                        >{{ node.label }}</span>
                    </span>
            </el-tree>
            <p style="color: #1C86EE" v-if="loading" align="center" > <i class="el-icon-loading"></i>加载中...</p>
            <p style="color: #DBDBDB" v-if="noMore" align="center">没有更多了</p>
        </div>
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";

    export default {
        name: "ListTreeCustom",
        mixins: [treeMethods, commonMixins, servicesMixins],
        props: {
            isTemplate: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                isStdlib: false,
                pageSize: 100,
                page: 0,
                noMore: false,
                loading: false,
                defaultProps: {
                    value: "id",
                    label: "name",
                    children: "children"
                },
                allDatas: []

            }
        },
        methods: {
            dragend() {
                globalBus.$emit('dragend');
            },
            filterNode(value, data, node) {
                // if (!value) return true;
                // return data[this.defaultProps.label].toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
            initTree(type = "") {
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                vm.loading = true;
                let request;
                if (vm.isTemplate) {
                    request = services.getOriginDataListByView();
                } else {
                    request = services.getOriginDataList();
                }
                request.then(res => {
                    if (res.data.status === 0) {
                        //第一次查询数据不超过20条
                        /*if (type !== "load" && res.data.data.length === 0) {
                            vm.noMore = false;
                            vm.loading = false;
                            return;
                        }
                        if (type === "load" && res.data.data.length === 0) {
                            vm.noMore = true;
                            vm.loading = false;
                            return;
                        }
                        vm.noMore = false;
                        vm.loading = false;*/
                        vm.allDatas = [];
                        if (res.data.status === 0) {
                            res.data.data.forEach(item => {
                                let keyWork = "";
                                if (item.type === 'HbaseHtable') {
                                    keyWork = 'kVInput';
                                } else if (item.type === 'RdbDataObj' || item.type === 'ElasticsearchCollection') {
                                    keyWork = 'standardSqlInput';
                                }
                                let dataName = item.code;
                                if (item.name !== null && item.name !== "null" && item.name !== "") {
                                    dataName += '(' + item.name + ')'
                                } else {
                                    dataName += '(' + item.code + ')'
                                }
                                let data = {
                                    dwname: item.dwname,
                                    name: dataName,
                                    type: "DS",
                                    keyWord: keyWork, // pluginCode
                                    classifierId: item.id, // dataObjId
                                    dbType: item.type  // db type
                                };
                                vm.allDatas.push(data);
                            });
                            vm.loadData();
                        }
                    }
                }).catch(err => {
                    vm.noMore = false;
                    vm.loading = false;
                });
            },
            loadData() {
                const vm = this;
                vm.loading = true;
                if (vm.noMore) {
                    vm.loading = false;
                    return;
                }
                vm.page ++ ;
                vm.setTreeData();
            },
            setTreeData(load){
                const vm = this , {page , pageSize , allDatas , filterText} = this;
                let list = [] , res_list = [];
                    res_list = vm.loadList(filterText , allDatas);
                res_list.forEach((item , i) => {
                    if(i < page * pageSize){
                        list.push(item);
                    }
                    if(i === res_list.length -1 && page * pageSize >= res_list.length -1){
                        setTimeout(()=> {
                            vm.noMore = true;
                        },1000);
                    }
                });
                setTimeout(()=> {
                    vm.loading = false;
                    vm.data = list;
                },500)
            },
            loadList(filterText , list ){
                const vm = this;
                if(filterText === ""){
                    return list;
                }else {
                    return list.filter(li => li[vm.defaultProps.label].toLowerCase().indexOf(filterText.toLowerCase()) !== -1)
                }
            },
            queryData(val) {
                // if(!val) return;
                this.page = 1;
                this.noMore = false;
                this.setTreeData(true);
            },
            addNode(plug) {
                globalBus.$emit('plugNode', plug);
            },
        }
    }
</script>

<style scoped>
    .listTreeCustom {
        height: 100%;
    }

    .tree-cont {
        height: calc(100% - 42px);
        overflow: auto;
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }

    .node-label {
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .filter-b {
        margin-bottom: 6px;
    }

    .ce-search {
        width: 100%;
    }
</style>
<style>
    .ce-mini_btn .el-checkbox-button--small .el-checkbox-button__inner {
        padding: 9px 6px;
        border-radius: 4px;
    }
</style>