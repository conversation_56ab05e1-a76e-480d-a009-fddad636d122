<!--
保存模型弹窗 通用
支持 建模 、 可视化 、 门户
-->
<template>
    <div v-loading="settings.loading" :style="{'--tree-wrap-h' : `${treeWrapHeight}`}">
        <el-form :model="form" :rules="rules" label-width="120px" label-position="right">
            <el-form-item v-if="showItems.includes('transName')" :label="formList.transName.label" prop="transName">
                <div class="content__name">
                    <el-input v-model.trim="form.transName" maxlength="50" v-input-limit:trim
                              @input="inputFilterSpecial($event , 'form' ,'transName')"
                              :placeholder="formList.transName.placeholder"></el-input>
                    <p>{{ formList.transName.tip }}</p>
                </div>
            </el-form-item>
            <el-form-item v-if="showItems.includes('pos')" :label="formList.pos.label" required>
                <div class="content__wrap">
                    <!--目录树-->
                    <DataTree class="content__tree" ref="tree"
                              @nodeClick="treeDir"
                              :dirId="classifyId"/>
                </div>
            </el-form-item>
            <el-form-item v-if="showItems.includes('description')" :label="formList.description.label">
                <el-input v-model="form.description" type="textarea"
                          :placeholder="formList.description.placeholder"
                          maxlength="255"
                          rows="3"
                          resize="none"
                          show-word-limit></el-input>
            </el-form-item>
        </el-form>
        <dialog-footer-btn v-footer :data="btnGroup"/>
    </div>
</template>

<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins"
import {listMixins} from "@/api/commonMethods/list-mixins";
import DataTree from "./DataTree.vue"
import DialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn"

export default {
    name: "saveModel",
    mixins: [commonMixins, listMixins],
    props: {
        // 加载树接口
        loadTreeInter: {
            type: Function,
            default: () => Promise.resolve([])
        },
        // 保存接口
        saveInter: Function,
        // 默认选中目录节点 Id
        dirId: String,
        // 初始名称
        name: String,
        // 初始描述
        description: String,
        // 模型类型 名称
        modelTypeName: String,
        showItems: {
            type: Array,
            default: () => ['transName', 'pos', 'description'],
        },
        // 确认 接口
        confirmInter: {
            type: Function,
            default: () => Promise.resolve()
        },
        // 树外层高度
        treeWrapHeight: {
            type: String,
            default: '',
        }
    },
    provide() {
        return {
            loadTreeInter: this.loadTreeInter,
        }
    },
    components: {DataTree, DialogFooterBtn},
    computed: {
        formList() {
            const {modelTypeName} = this;
            return {
                transName: {
                    label: modelTypeName + '名称 :',
                    placeholder: `请输入${modelTypeName}名称`,
                    tip: "名称最大不超过50字符，只允许除/、\\、<、>等特殊字符以外。"
                },
                pos: {
                    label: "保存到目录 :"
                },
                description: {
                    label: modelTypeName + "描述 :",
                    placeholder: `请输入对${modelTypeName}进行描述的一段说明`
                }
            };
        },
        rules() {
            const {modelTypeName} = this;
            return {
                transName: [{required: true, message: `请输入${modelTypeName}名称`, trigger: ['blur', 'change']}]
            }
        }
    },
    data() {
        return {
            btnGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.save,
                    disabledFn: () => this.settings.loading,
                    show: () => true
                },
            ],
            classifyId: "",
            form: {
                transName: "",
                description: ""
            },
        }
    },
    methods: {
        treeDir(label, value) {
            this.classifyId = value;
        },
        show() {
            this.classifyId = this.dirId || "";
            this.form.transName = this.name || "";
            this.form.description = this.description || "";
        },
        save() {
            const vm = this, {settings, form, modelTypeName} = this;
            if (vm.classifyId === "") {
                vm.$message.warning(`请选择${modelTypeName}所在目录！`)
            } else if (vm.form.transName === "") {
                vm.$message.warning(`请输入${modelTypeName}名称！`)
            } else {
                settings.loading = true;
                let params = {
                    transName: form.transName,
                    classifyId: this.classifyId,
                    memo: form.description
                }
                this.saveInter(params, settings).then( (res) => {
                    if (res.data.status === 0) {
                        vm.confirmInter().then(()=>{
                            vm.$emit('submit', params);
                            vm.$message.success("保存成功！");
                        }).catch( () => {
                            settings.loading = false;
                        })
                    }
                })
            }
        },
    },
    created() {
        this.show();
    }
}
</script>

<style scoped lang="less">
.content {
    &__name {
        p {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 21px;
            margin-top: 4px;
        }
    }

    &__tree.dataTree {
        float: none;
        margin: 0;
        border: none;
        height: 100%;
        width: 100%;
        padding: 0;
    }

    &__wrap {
        padding: 14px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        box-sizing: border-box;
        height: var(--tree-wrap-h, 250px);
    }
}
</style>
