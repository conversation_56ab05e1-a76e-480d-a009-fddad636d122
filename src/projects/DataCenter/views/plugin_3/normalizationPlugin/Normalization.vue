<template>
    <div class="RuleEditingPanel ce-plug_cont" v-loading="settings.loading">

        <div class="ce-tab_cont">
            <div class="ce-plug_attr">
                <CommonTable
                        :data="ruleTableData"
                        :columns="ruleHeader"
                        height="100%"
                >
                    <template slot="header-ruleOperate" slot-scope="scope">
                        <span>操作</span>
                        <em class="icon ce_link f14 ml5" title="添加" @click="addRuleEdit">&#xe6e1;</em>
                    </template>
                    <template slot="fieldName" slot-scope="scope">
                        <div :title="scope.row.fieldName">
                            <el-select v-model="scope.row.fieldName" size="mini">
                                <el-option v-for="opt in selectOption"
                                           :key="opt.value"
                                           :value="opt.value"
                                           :label="opt.label"
                                ></el-option>
                            </el-select>
                        </div>
                    </template>
                    <template slot="isStore" slot-scope="scope">
                        <el-switch v-model="scope.row.isStore"></el-switch>
                    </template>
                    <template slot="ruleOperate" slot-scope="scope">
                        <el-popconfirm
                            size="mini"
                            v-for="(col , inx) in ruleOperateIcon"
                            :key="inx"
                            title="确认删除?"
                            @confirm="col.clickFn(scope.row, scope.$index )"
                        >
                            <i
                                slot="reference"
                                class="icon ce_link model_status"
                                :key="inx"
                                :title="col.tip"
                                v-html="col.icon"
                            ></i>
                        </el-popconfirm>
                        <!-- <em class="icon ce_link p5"
                            v-for="( ico , key ) in ruleOperateIcon"
                            :key="key"
                            :title="ico.label"
                            v-html="ico.icon"
                            @click="ico.clickFn(scope.row , scope.$index)"
                        ></em> -->
                    </template>
                </CommonTable>
            </div>

        </div>
        <!-- <div class="attr_btn">
            <el-button
                    v-for="(btn , inx) in buttons"
                    :key="inx"
                    size="mini"
                    :type="btn.type"
                    @click="btn.clickFn"
            >{{btn.name}}
            </el-button>
        </div> -->
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>

    import CommonTable from "@/components/common/CommonTable";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"

    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";

    export default {
        name: "normalizationMeta",
        props: {
            sourceData: Object,
            rowData: Object
        },
        mixins: [common, commonMixins, servicesMixins],
        components: {
            CommonTable,
            PreView
        },
        data() {
            return {
                tableOption: [],
                selectOption: [],
                pathTrue: false,
                form: {
                    lucenePath: '',
                    indexType: '1',
                    outputCfg: []
                },
                ruleTypeOptions: [
                    {}
                ],

                outputColumn: {},
                defaultProps: {
                    value: 'value',
                    label: "label",
                    children: "children",
                },
                ruleHeader: [
                    {
                        label: '处理字段',
                        prop: 'fieldName',
                        align: 'center',
                        minWidth: '160px'
                    },
                    {
                        label: '',
                        prop: 'ruleOperate',
                        align: 'center',
                        width: '90px',
                        fixed: "right"
                    },],
                stepId: '',
                canSave: false,
                isSeniorShow: {},
                results: [],
                
                buttons: {
                    save: {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary',
                        canSave: false
                    },
                    preview: {
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                },
                ruleTableData: [],
                ruleOperateIcon: {
                    delete: {
                        label: '删除',
                        icon: '&#xe65f;',
                        clickFn: this.deleteFn
                    }
                },
                row: {
                    fieldName: ''
                },
                numType: ["double", "integer", "float", "byte", "long", "biginteger"]
            }

        },
        methods: {
            getOutId(type, serviceOrgId) {
                for (let k = 0; k < this.ruleTableData.length; k++) {
                    if (serviceOrgId === this.ruleTableData[k].ruleTableData) {
                        this.ruleTableData[k].type = type;
                    }
                }
            },

            typeCheck(val, row, target) {
                if (isNaN(val)) {
                    isNaN(parseFloat(val)) ?
                        row[target] = '' :
                        row[target] = parseFloat(val);
                }
            },
            checkColumnCode(e, scope) {
                /* let reg = new RegExp(/^[A-Za-z_][A-Za-z_0-9]*$/);
                 if (!reg.test(e)) {
                     scope.row.outputFieldName = e.replace(/[^A-Za-z_][^A-Za-z_0-9]*!/,'').toUpperCase();
                 }*/
                scope.row.outputFieldName = e.replace(/[^A-Za-z_][^A-Za-z_0-9]*/, '').toLowerCase();
            },

            deleteFn(row, index) {
                const vm = this, {pluginServices, pluginMock} = this;
                // vm.confirm('删除', '此操作将永久删除该配置, 是否继续?', () => {
                if (row.serviceOrgId !== null && row.serviceOrgId !== '' && row.serviceOrgId !== undefined) {
                    vm.graphIds.push(row.serviceOrgId);
                }
                vm.ruleTableData.splice(index, 1);
                vm.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                // })
            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            async save(saveName,showTip) { //保存后才能预览
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let fieldList = [];
                if (vm.ruleTableData.length === 0) {
                    vm.$message.warning("处理字段不能为空!");
                    return;
                }

                for (let k = 0; k < vm.ruleTableData.length; k++) {
                    for (let key in vm.ruleTableData[k]) {
                        if (key !== 'serviceOrgId' && vm.ruleTableData[k][key] === '') {
                            if (key === 'fieldName') {
                                vm.$message.warning("处理字段不能为空!");
                                return;
                            }
                        }
                    }
                }
                vm.ruleTableData.forEach((item, i) => {
                    fieldList.push({
                        fieldName: item.fieldName,
                    });
                });
                let normalizationMeta = {
                    cicadaNormalizationFields: fieldList
                };
                settings.loading = true;
                services.saveNormalizationMeta(vm.sourceData.id, normalizationMeta, settings).then(res => {
                    if (res.data.status === 0) {
                        showTip && this.$message.success("保存成功!");
                        vm.$emit("setPreviewAble");
                        if(saveName)saveName();
                    }
                });
                settings.loading = false;
            },
            addRuleEdit() {
                const vm = this;
                vm.ruleTableData.push({
                    fieldName: '',
                });

            },

            checkType(data) {
                const vm = this;
                let tmpData = [];
                data.forEach(item => {
                    if (vm.numType.includes(item.columnType.toLowerCase())) {
                            tmpData.push(item);
                        }
                    }
                );
                return tmpData;
            },

            //初始化页面
            async init(node) {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                vm.stepId = node.id;
                let services = vm.getServices(pluginServices, pluginMock);
                services.getNormalizationPage(node.id, settings).then(res => {
                    vm.tableOption = vm.checkType(res.data.data.column);
                    vm.tableOption.forEach(item => {
                        if (item.columnZhName == null) {
                            item.label = item.columnName;
                        } else item.label = item.columnZhName;
                        item.value = item.columnName;
                        return item;
                    });
                    vm.selectOption = vm.tableOption;

                    let normalizationFields = res.data.data.pluginMeta.cicadaNormalizationFields;
                    if (normalizationFields.length > 0) {
                        normalizationFields.forEach((item, i) => {
                            vm.ruleTableData.push({
                                fieldName: item.fieldName,
                            });
                        });
                    }else {
                        vm.addRuleEdit();
                    }
                })
            },
            addCon(item) {
                this.outputCfg[item].outputColumn.push(
                    {
                        column: "",
                        exp: "",
                        value: "",
                    }
                )
            },

            delCon(item, i) {
                this.outputCfg[item].outputColumn.splice(i, 1);

                if (this.expForms[item].conditions.length <= 0) {
                    this.expForms.splice(item, 1);
                }
            },


        },
        created() {
            this.init(this.sourceData);
        },
    }
</script>

<style scoped>
    .ce-exp_list {
        display: flex;
    }

    .ce-exp_item {
        flex: 1;
        padding: 0 5px;
        padding-top: 5px;
        width: 300px;
    }

    .ce-exp_btn {
        flex: auto 0 0;
        width: 50px;
    }

    .ce-plug-btn_box {
        margin-bottom: 10px;
    }

    .radio_connector {
        margin-left: 40px;
    }

    .return_input {
        width: 259px;
    }

    .ce-add_b {
        line-height: 300px;
        border: 3px dashed #ddd;
        cursor: pointer;
        text-align: center;
        color: #999;
        font-size: 18px;
        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
