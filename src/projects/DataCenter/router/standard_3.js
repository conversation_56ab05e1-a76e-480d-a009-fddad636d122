import Layout_new from "../views/Index"
import store from '../store/index.js'
import Vue from "vue";
// import {Message} from 'element-ui'
import MessageBox from "ui-component-v4/lib/message-box"
import {goToLogout} from "@/api/dids-logout";
import {getCurrentProtocol} from "@/api/global_func";

export const standardRouter = [
    {
        path: "/",
        component: Layout_new,
        redirect: "/home",
        name: "Home",
        meta: {
            title: "主页",
            icon: "icon-Homee",
            noCache: true,
            breadcrumb: false
        },
        children: [
            {
                path: "/home",
                component: () => import("../views/home"),
                name: "home",
                meta: {
                    title: "",
                    icon: "iconf-homee",
                    noCache: true,
                    breadcrumb: false
                }
            },
            {
                path: "/share",
                component: () => import("../views/home/<USER>"),
                name: "share",
                meta: {
                    title: "",
                    icon: "",
                    noCache: true,
                }
            },
            // {
            //     path: "/caseScenario",
            //     component: () => import("../views/home/<USER>"),
            //     name: "caseScenario",
            //     meta: {
            //         title: "情景案例",
            //         icon: "",
            //         noCache: true,
            //     }
            // }
        ],
    },/*{
        path: "/businessSpace",
        component: Layout_new,
        name: "businessSpace",
        hiddenChildren:true,
        redirect: "/businessSpace/overview",
        meta: { title: "业务空间", icon: "icon-computer", noCache: true, breadcrumb: true },
        children: [
            {
                path: '/businessSpace/overview',
                hidden: true,
                name: 'businessSpaceOverview',
                meta: { title: "", icon: "icon", noCache: true },
                component: () => import('../views/business-space/index.vue')
            },
            {
                path: '/businessSpace/definition',
                hidden: true,
                name: 'businessSpaceDefinition',
                meta: { title: "业务定义", icon: "icon", noCache: true },
                component: () => import('../views/business-space/definition/index.vue')
            }

        ]
    },*/
    {
        path: "/modelSpace",
        component: Layout_new,
        name: "Modeling",
        redirect: "/modelSpace/rapidAnalysis",
        meta: { title: "建模空间", icon: "icon-model-a", noCache: true, breadcrumb: false },
        children: [
            {
                path: '/modelSpace/rapidAnalysis',
                hidden: true,
                name: 'rapidAnalysis',
                meta: { title: "快速分析", icon: "icon-data-c2", noCache: true , documentCode: "fastAnalysis"},
                component: () => import('../views/modeling/rapidAnalysis/index.vue')
            },
            {
                path: '/modelSpace/modeling',
                hidden: true,
                name: 'processModeling',
                meta: { title: "数据建模", icon: "icon-model1", noCache: true , documentCode: "processModeling"},
                component: () => import('../views/modeling/index.vue')
            },
            {
                path: '/modelSpace/AIModeling',
                hidden: true,
                name: 'processAIModeling',
                meta: { title: "AI建模", icon: "icon-AImodel", noCache: true , documentCode: "AIModeling"},
                component: () => import('../views/AIModeling/index.vue')
            },
            {
                path: '/modelSpace/ImportAndExport',
                hidden: true,
                name: 'ImportAndExport',
                meta: { title: "导入导出", icon: "icon-api", noCache: true , documentCode: "modelSpaceImportAndExport"},
                component: () => import('../views/ImportAndExport/index.vue')
            },
        ],

    },
    {
        path: "/visualSpace",
        component: Layout_new,
        name: "visualSpace",
        redirect: "/visualSpace/visualizing",
        meta: { title: "可视空间", icon: "icon-f-scense", noCache: true, breadcrumb: false },
        children: [
            {
                path: "/visualSpace/visualizing",
                component: () => import('../views/visualizing/index.vue'),
                name: "dashboard",
                hidden: true,
                meta: { title: "仪表盘", icon: "iconfont icontubiao_l-kshfx", noCache: true , documentCode: "dashboard"}
            },
            {
                path: "/visualSpace/themePortal",
                component: () => import('../views/homeHu/index.vue'),
                name: "themePortal",
                hidden: true,
                meta: { title: "主题门户", icon: "iconfont iconshujuzhongxinicon_l-distributed", noCache: true , documentCode: "themePortal"}
            }
        ],
    },
    {
        path: "/serviceSpace",
        component: Layout_new,
        name: "serviceSpace",
        redirect: "/serviceSpace/serviceManage",
        meta: { title: "服务空间", icon: "icon-service", noCache: true, breadcrumb: false },
        children: [
            {
                path: '/serviceSpace/serviceManage',
                hidden: true,
                name: 'processServiceManage',
                meta: { title: "API管理", icon: "icon-api", noCache: true , documentCode: "serviceManagement"},
                component: () => import('../views/serviceManage/index.vue')
            },{
                path: '/serviceSpace/testOnline',
                hidden: true,
                name: 'testOnline',
                meta: { title: "在线测试", icon: "icon-a-numericalzippertable", noCache: true , documentCode: "testOnline"},
                component: () => import('../views/test-online/index.vue')
            },{
                path: '/serviceSpace/useCaseManage',
                hidden: true,
                name: 'useCaseManage',
                meta: { title: "用例管理", icon: "icon-a-numericalzippertable", noCache: true , documentCode: "useCaseManagement"},
                component: () => import('../views/useCaseManage/index.vue')
            },{
                path: '/serviceSpace/serviceImport',
                hidden: true,
                name: 'serviceImport',
                meta: { title: "导入导出", icon: "icon-a-numericalzippertable", noCache: true , documentCode: "serviceSpaceImportAndExport"},
                component: () => import('../views/serviceImport/index.vue')
            }
        ],
    },
    {
        path: "/dataSpace",
        component: Layout_new,
        name: "dataSpace",
        redirect: "/dataSpace/dataContact",
        meta: { title: "数据空间", icon: "icon-database", noCache: true, breadcrumb: false },
        children: [
            {
                path: '/dataSpace/dataContact',
                hidden: true,
                name: 'dataContact',
                meta: { title: "数据连接", icon: "icon-data-d", noCache: true , documentCode: "dataConnection"},
                component: () => import('../views/dataSpace/dataContact/index.vue')
            },
            {
                path: '/dataSpace/dataReady',
                hidden: true,
                name: 'dataReady',
                meta: { title: "数据准备", icon: "icon-computer1", noCache: true , documentCode: "dataSetOperation"},
                component: () => import('../views/dataSpace/dataReady/index.vue')
            }
        ],
    },
    {
        path: "/management",
        component: Layout_new,
        redirect: "/management/rolesmanage",
        name: "management",
        meta: { title: "管理空间", icon: "icon-setting", noCache: true, breadcrumb: false },
        children: [
            {
                path: "/management/rolesmanage",
                component: () => import("../views/system-manage/roles-manage"),
                name: "roleManagement",
                hidden: true,
                meta: { title: "角色管理", icon: "icon-setting", noCache: true , documentCode: "roleManagement"}
            },
            {
                path: "/management/usergroupmanage",
                component: () => import("../views/system-manage/user-group-manage"),
                name: "groupManagement",
                hidden: true,
                meta: { title: "用户组管理", icon: "icon-setting", noCache: true , documentCode: "groupManagement"}
            },
            {
                path: "/management/usermanage",
                component: () => import("../views/system-manage/user-manage"),
                name: "userManagement",
                hidden: true,
                meta: { title: "用户管理", icon: "icon-setting", noCache: true , documentCode: "userManagement"}
            },
            {
                path: "/management/operator",
                component: () => import('../views/system-manage/operator-manage/index.vue'),
                name: "udfOperator",
                hidden: false,
                meta: { title: "算子管理", icon: "iconfont icontubiao_l-xtgl", noCache: true , documentCode: "udfOperatorManage"}
            },
            // {
            //     path: "/management/serviceMediation",
            //     component: () => import('../views/system-manage/service-mediation/index.vue'),
            //     name: "serviceMediation",
            //     hidden: false,
            //     meta: {title: "服务中介管理", icon: "iconfont icontubiao_l-xtgl", noCache: true, documentCode: "serviceMediationManagement"}
            // },
            // {
            //     path: "/management/busService",
            //     component: () => import('../views/system-manage/bus-service/index.vue'),
            //     name: "busService",
            //     hidden: false,
            //     meta: {title: "服务接入管理", icon: "iconfont icontubiao_l-xtgl", noCache: true, documentCode: "busService"}
            // },
            // {
            //     path: "/datacenter/service",
            //     component: () => import('../views/system-manage/bus-manage/index.vue'),
            //     name: "accessBusManagement",
            //     hidden: false,
            //     meta: {title: "接入服务管理", icon: "iconfont icontubiao_l-xtgl", noCache: true, documentCode: "accessBusManagement"}
            // },
            {
                path : "/management/logquery" ,
                component : ()=> import("../views/system-manage/log-query"),
                name : "logQuery" ,
                hidden: false ,
                meta: { title: "日志查询", icon: "icon-list", noCache: true , documentCode: "logQuery"}
            },
            {
                path : "/management/process" ,
                component : ()=> import("../views/system-manage/process-manage"),
                name : "process" ,
                hidden: false ,
                meta: { title: "流程管理", icon: "icon-setting", noCache: true , documentCode: "process"}
            }
        ]
    },
    {
        path: "/case",
        component: Layout_new,
        name: "caseScenario",
        redirect: "/caseScenario",
        meta: { title: "场景案例", icon: "icon-model-a", noCache: true, breadcrumb: false ,hidecrumb : true },
        children: [
            {
                path: "/casescenario",
                component: () => import("../views/home/<USER>"),
                name: "caseScenarioPage",
                meta: {
                    title: "",
                    icon: "",
                    noCache: true,
                    breadcrumb: false
                }
            }
        ],

    },
    {
        path: "/datacenter/homeView",
        hidden: true,
        name: "homeView",
        component: () => import("../views/homeHu/edit-panel/body/portal-main/index.vue"),
    },
    {
        path: '/datacenter/visualview',
        hidden: true,
        name: 'visualView',
        meta: { right: "dashboard", },
        component: () => import('../views/visualizing/edit-panel/VisualizationEdit.vue')
    },
    {
        path: "/datacenter/modelView",
        hidden: true,
        name: 'modelView',
        component: () => import('@/projects/DataCenter/views/modeling/dialog/modelView.vue')
    },
    {
        path: "/datacenter/jumpView",
        hidden: true,
        name: 'jumpView',
        component: () => import('@/projects/DataCenter/views/jump-page/JumpPage.vue')
    },
    {
        path: "/datacenter/dataContact",
        hidden: true,
        name: 'dataSpaceDataContact',
        component: () => import('../views/genScreen/dataContact.vue')
    },
    {
        path: "/datacenter/dataReady",
        hidden: true,
        name: 'dataSpaceDataReady',
        component: () => import('../views/genScreen/dataReady.vue')
    },
    {
        path: "/datacenter/rapidAnalysis",
        hidden: true,
        name: 'modelSpaceRapidAnalysis',
        component: () => import('../views/genScreen/rapidAnalysis.vue')
    },
    {
        path: "/datacenter/modeling",
        hidden: true,
        name: 'modelSpaceModeling',
        component: () => import('../views/genScreen/modeling.vue')
    },
    {
        path: '*',
        hidden: true,
        name: "error",
        component: () => import('../../../components/error/Error404.vue')
    }, {
        path: "/login",
        name: "login",
        hidden: true,
        component: () => import('../views/login/Login.vue'),
        beforeEnter: (to, from, next) => {
            let axios = Vue.prototype.$axios;
            localStorage.removeItem("userInfo");
            localStorage.removeItem("userRight");
            localStorage.removeItem("userRole");
            store.dispatch("logout", null);
            axios.get("/loginWay/getSystemLoginWay").then(res => {
                if(res.data.status === 0){
                    let loginWay = res.data.data === "1" ? 'dids' : '';
                    let cdLogin = res.data.data === "2"; // 常德登录
                    const protocol = getCurrentProtocol();
                    if (loginWay === 'dids') {
                        axios.get("/didslogin/licence").then(result => {
                            let url = `${protocol}://${window.location.host}/serverConfig.json`;
                            if (!result.data.data) {
                                axios.get(url).then((result, resolve) => {
                                    let config = result.data;
                                    top.location.href = config.production + "/authorizationPage.html";
                                    resolve();
                                }).catch((error) => { })
                            } else {
                                window.location.href = result.config.baseURL + "/didslogin/ssoSysInit";//"http://**************:9080/didsserver/login?service=http%3A%2F%2F10.254.10.52%3A8888%2FdataCenter%2Flogin%2FssoSysInit&appId=010000000000284&loginName=&loginPage=&redirectUrl=http%3A%2F%2F10.254.10.52%3A8888%2FdataCenter%2Flogin%2FssoSysInit&forwardUrl="
                            }
                        });
                    } else if(cdLogin) {
                        let url = `${protocol}://${window.location.host}/serverConfig.json`;
                        axios.get(url).then((result) => {
                            let config = result.data;
                            if(to.query.userToken) {
                                sessionStorage.setItem("userToken", to.query.userToken);
                                const redirectUrl = to.query.redirectUrl ? `&redirectUrl=${to.query.redirectUrl}` : '';
                                top.location.href = config.production + `/zerotrust/login361?userToken=${to.query.userToken}${redirectUrl}`;
                            }
                            else next();
                        }).catch((error) => { })
                    } else {
                        next();
                    }
                }
            }).catch(err => {
                next();
            })

        }
    },
    {
        path: '/redirectBack/:id',
        hidden: true,
        beforeEnter: (to, from, next) => {
            let axios = Vue.prototype.$axios ;
            localStorage.setItem("sessionId", to.params.id);
            axios.defaults.headers['sessionId'] = to.params.id;
            axios.get("/didslogin/getUserInfo").then(res => {
                if (res.data.data !== undefined && res.data.data !== null) {
                    let rights = res.data.data.data.funcCodes.map(code => {
                        return {
                            funcCode: code
                        }
                    });
                    let userInfo = res.data.data.data.tSysAuthUser;
                    if(!rights.length){
                        removeStorage("该用户没有权限，请授权之后再进行登录!");
                    }else {
                        store.dispatch("login", userInfo);
                        localStorage.setItem("userRight", JSON.stringify(rights));
                        localStorage.setItem("userInfo", JSON.stringify(userInfo));
                        // let toPath = sessionStorage.getItem('next');
                        axios.get(`/user/updataLoginNumber?userId=${userInfo.id}`);

                        axios.get("/function/queryShowDocMsg").then(res => {
                            if (res.data.status === 0) {
                                let result = res.data.data;
                                localStorage.setItem("functionCodeInfo" ,JSON.stringify(result) );
                                store.dispatch("functionCodeInfo",{info : result});
                            }
                        }).catch(err => {
                            console.log(err);
                        });
                        next('/')
                    }

                } else {
                    removeStorage("获取用户信息失败!");
                }
            }).catch(err => {
                console.log(err);
            });
        }
    },
];


/**
 * 清除缓存用数据
 */
function removeStorage(msg){
    let axios = Vue.prototype.$axios;
    localStorage.removeItem("userInfo");
    localStorage.removeItem("userRight");
    localStorage.removeItem("userRole");
    localStorage.removeItem("sessionId");
    axios.get("/didslogin/loginOut").then(res => {
        if(res.data.status === 0){
            let url = res.data.data.data;
            MessageBox.alert(msg , "提示" , {type:'warning' ,showClose: false}).then(() => {
                goToLogout(url);
            });
        }
    })
}

