<template>
    <div class="newAddSource">
        <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" width="1100px"  @closed="removeF" >
            <NewDataSet v-if="reNew" @saveClose="saveClose"/>
        </el-dialog>
    </div>
</template>

<script>

    import NewDataSet from './NewDataSet'
    import {globalBus} from "@/api/globalBus";

    export default {
        name: "NewAddSource",
        data() {
            return {
                dialogTitle: '添加数据源',
                dialogVisible: false,
                reNew: false,
                nodeData: {},
                treeNode: {},
                isShow: true,
            }
        },
        components: {
            NewDataSet
        },
        methods: {
            show(data, node) {
                this.dialogVisible = true;
                this.nodeData = data;
                this.treeNode = node;
                this.reNew = true;
                if (data.isParent === false) {
                    this.dialogTitle = "查看数据源";
                    this.isShow = false;
                } else {
                    this.dialogTitle = "添加数据源";
                    this.isShow = true;
                }
            },
            saveClose(dataSet){

                const _this = this;
                let busiDirId = this.treeNode.parent.parent.data.id;
                let classifyId = this.nodeData.id;
                let sourceData ={
                    classifyId: this.nodeData.id,
                    dbType: "",
                    domainDbType: dataSet.dbType,
                    name: dataSet.name,
                    otherElementId: dataSet.schemaId,
                    softwareId:dataSet.id,
                };
                _this.$axios.post("/manage/dataWarehouse/saveDWDBInstance?classifyId="+classifyId+"&busiDirId="+busiDirId,sourceData).then(res =>{

                    if(res.data.status === 0){
                        _this.$message.success("数据源添加成功！");
                        _this.$emit("addNode",res.data.data);
                        this.close();
                    }else{
                        _this.$message.warning(res.data.msg);
                    }
                }).catch(err =>{
                    console.log(err);
                    _this.$message.error("服务器异常，请联系管理员！")
                })
            },
            submit() {
                const _this = this;
                let sourceData = this.$refs.form.getSourceData();
                if(sourceData === null){
                    return;
                }
                let busiDirId = this.treeNode.parent.parent.data.id;
                _this.$axios.post("/manage/dataWarehouse/saveDWDBInstance?classifyId="+sourceData.classifyId+"&busiDirId="+busiDirId,sourceData).then(res =>{

                    // console.log("添加数据源：",res);
                    if(res.data.status === 0){
                        _this.$message.success("数据源添加成功！");

                        _this.$emit("addNode",res.data.data);
                        this.close();
                    }else{
                        _this.$message.warning(res.data.msg);
                    }
                }).catch(err =>{
                    console.log(err);
                    _this.$message.error("服务器异常，请联系管理员！")
                })
            },
            close() {
                this.dialogVisible = false;
            },
            removeF() { //清空操作数据
                this.reNew = false;
            }
        }

    }
</script>

<style scoped>

</style>