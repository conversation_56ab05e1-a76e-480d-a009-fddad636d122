<template>
    <div class="previewList">
        <div class="preview_head">
            <div class="preview_left">
                <dg-radio-group type="button" call-off v-model="tabInfo" :data="tabOpt" @change="tabChange"></dg-radio-group>
                <div class="rules__wrap-reflash mr5" @click="renewList"><i class="dg-iconp icon-refresh"></i></div>
            </div>
            <div class="preview_right">
                <slot name="filter-cont"></slot>
            </div>
        </div>
        <div class="preview_cont" v-loading="loadParams.loading">
            <common-table
                    v-show="tabInfo === 'viewList'"
                    ref="dataList"
                    :data="previewList"
                    :height="previewList.length ? 'calc(100% - 42px)': '100%'"
                    :columns="previewColumns"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    @change-size="changeSize"
                    @change-current="isMock ?  ()=>{} : changePage($event)"
                    :pagination-total="total"
            >
            </common-table>
            <common-table
                    v-show="tabInfo === 'fieldList'"
                    ref="columnList"
                    :data="fields"
                    :height="fields.length ? 'calc(100% - 42px)' : '100%'"
                    :columns="fieldColumns"
                    paging-type="client"
                    :pagination="true"
                    :pagination-props="paginationProps"
            >
            </common-table>
        </div>
    </div>
</template>

<script>

import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "PreviewList",
    mixins:[commonMixins],
    props: {
        loadParams : {
            type : Object ,
            default : ()=> {
                return {
                    loading : false
                }
            }
        },
        previewList : {
            type : Array ,
            default : ()=>[]
        },
        previewColumns : {
            type : Array ,
            default : ()=>[]
        },
        fields : {
            type : Array ,
            default : ()=>[]
        },
        fieldColumns : {
            type : Array ,
            default : ()=>[]
        },
    },
    data() {
        return {
            tabInfo: "viewList",
            tabOpt: [
                {label: "预览数据", value: "viewList"},
                {label: "字段列表", value: "fieldList"},
            ]
        }
    },
    methods: {
        /**
         * 刷新
         */
        renewList() {
            this.changePage();
        },
        tabChange(val){
            this.$emit("tabChange" , val);
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.changePage();
        },
        changePage(inx=1){
            this.paginationProps.currentPage = inx;
            this.$emit('refresh',inx , this.paginationProps);
        },
        setPageInfo(total){
            this.total = total;
        },
        doLayout(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.dataList.$refs.table.doLayout();
                vm.$refs.columnList.$refs.table.doLayout();
            })
        }
    },
}
</script>

<style scoped lang="less">
.preview {
    &List {
        height: 100%;
        overflow: hidden;
    }
    &_cont {
        height: calc(100% - 10px - 2rem);
    }
    &_head {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
}

.rules__wrap-reflash {
    display: inline-block;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.125rem;
    margin: 0 0.25rem;
    cursor: pointer;
    vertical-align: middle;
}

</style>
