<template>
    <div class="height100">
        <batch-test-page :showLeft="showLeft" :useCaseButtonShow="useCaseButtonShow" v-on="$listeners" ref="batch"  />
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <!-- <dg-button v-footer type="primary" @click="submit" >{{ btnCheckTxt }}</dg-button> -->
    </div>
</template>

<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";
import BatchTestPage from "@/components/common/api-test-page/BatchTestPage";
import {UseCaseMixins} from "../../../test-online/dialog/useCaseMixins";
import {common} from "dc-front-plugin/src/api/commonMethods/common";
    export default {
        props : {
            row : Object,
        },
        components : {
            BatchTestPage,
        },
        mixins: [commonMixins, common,UseCaseMixins],
        data() {
            return {
                showLeft : true,
                useCaseButtonShow :false,
            };
        },
        methods: {

            close(){
                this.$emit("close");
            },
            async init() {
                await this.getUseCaseDetailInfo();
            },
            async getUseCaseDetailInfo() {
                const vm = this, {settings} = this;
                let services = this.$services('apiTest');
                settings.loading = true;
                let params = [vm.row.id];
                await services.importUseCase({params ,settings}).then(res => {
                    if(res.data.code === 0){
                        let result  = vm.setCaseData(res.data.data);
                        vm.$nextTick(()=>{
                            vm.$refs.batch.addList(result);
                            vm.$refs.batch.startTest();
                        })
                    }
                })
            }
        },
        created() {
            this.init();
        }
    };
</script>