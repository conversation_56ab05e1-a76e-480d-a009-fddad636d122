import {chartTheme} from "../../components/datasetMixins/chartTheme";
import {common} from "@/api/commonMethods/common";
import { isNumber } from "lodash";

export default {
    name: "chartStyle",
    mixins : [chartTheme , common],
    props :{
        settingForm :Object ,
        formList : Object ,
        active :Boolean ,
        isPie : {
            type : Boolean ,
            default : false
        }
    },
    data(){
        return {
            form :this.formList,
            localForm : this.settingForm,
            editOpt : [
                {
                    label : '默认' ,
                    value :'default'
                },{
                    label : '自定义' ,
                    value :'custom'
                }
            ],
            fontSizeOpt : [
                {
                    label: '12px',
                    value: '12px'
                },
                {
                    label: '14px',
                    value: '14px'
                },
                {
                    label: '16px',
                    value: '16px'
                },
                {
                    label: '18px',
                    value: '18px'
                },
                {
                    label: '20px',
                    value: '20px'
                }, {
                    label: '24px',
                    value: '24px'
                },
                {
                    label: '30px',
                    value: '30px'
                }
            ],
            fontFamilyOpt : [
                {
                    label : '默认' ,
                    value : 'sans-serif'
                },
                {
                    label : '黑体',
                    value : "SimHei"
                },{
                    label : '宋体',
                    value : "SimSun"
                },{
                    label : '仿宋',
                    value : "FangSong"
                },{
                    label : '楷体',
                    value : "KaiTi"
                },{
                    label : '微软雅黑',
                    value : "Microsoft YaHei"
                },
            ],
            fontWeightOpt : [
                {value : "normal"},
                {value : "bold"},
                {value : "bolder"},
                {value : "lighter"},
            ],
            storeSize : "12px" ,
            textStyleOpt : [
                {
                    label : 'I' ,
                    value : 'italic' ,
                    class : 'i b'
                }
            ],
            textAlignOpt : [
                {
                    label : "左对齐",
                    value : "left" ,
                    html : '&#xe6c5;'
                }, {
                    label : "居中对齐",
                    value : "center" ,
                    html : '&#xe61e;'
                }, {
                    label : "右对齐",
                    value : "right" ,
                    html : '&#xe6c6;'
                },
            ],
            color_add : "添加" ,
            color :"" ,
            gridOpt : [
                {value:"abs" , label : "绝对值"},
                {value:"per" , label : "百分比"},
            ],
            splitOpt : [
                {value : 2},
                {value : 3},
                {value : 4},
                {value : 5},
                {value : 6},
                {value : 7},
                {value : 8},
            ],
            colorOpt : [
                {value : "custom" , label : "自定义"},
                {value : "theme" , label : "配色主题"},
            ],
            colorTheme : [
                {value : "default" , label : "默认"} ,
                {value : "vintage"},
                {value : "macarons"},
                {value : "infographic"},
                {value : "shine"},
                {value : "roma"},
            ],
            isColorNew : true ,
            colorInx : 0 ,
            predefine : [],
            radarRadio  : [
                {
                    icon : "#iconleidatukexuan",
                    value : "circle",
                    label : "圆形"
                },{
                    icon : "#iconleidatuicon_click_mini",
                    value : "polygon",
                    label : "多边形"
                },
            ],
            formRadio  : [
                {
                    icon : "#iconicon_yihanglianglie-s-copy",
                    value : "oneColumn",
                    label : "单列"
                },{
                    icon : "#iconyihangsilie",
                    value : "twoColumn",
                    label : "双列"
                },
            ],
            oneColumnOptions : [{
                label:"25%:75%",
                value:"25%",
            },{
                label:"50%:50%",
                value:"50%",
            },{
                label:"75%:25%",
                value:"75%",
            },],
            twoColumnOptions: [
                {
                    label:"10%:40%:10%:40%",
                    value:"10%"
                },
                {
                    label:"25%:25%:25%:25%",
                    value:"25%"
                },
                {
                    label:"40%:10%:40%:10%",
                    value:"40%"
                }
            ],
            /**
             * 表格标记条件
             */
            markCondition : {
                tip : "当"
            },
            tip : {
                plus : "新增",
                minus : "删除"
            }
        }
    },
    watch: {
        form: {
            handler(val) {
                const vm = this;
                if(vm.canReNew){
                    vm.$emit('styleRenew', val);
                }
            },
            deep: true
        },
        formList :{
            handler(val){
                this.form = JSON.parse(JSON.stringify(val));
            },
            deep :true
        },
        settingForm :{
            handler(val){
                this.localForm = JSON.parse(JSON.stringify(val));
            },
            deep :true
        }
    },
    computed : {
        canReNew(){
            return this.active;
        },
        markLabel(){
            if(this.form.options && this.form.options.length && this.form.mark_fields){
                let item = this.form.options.find(opt => opt.code === this.form.mark_fields);
                return item ? item.label : "";
            }else {
                return  "";
            }
        }
    },
    methods : {
        numberChange(val, ele){
            if(val < 0) this.$message.warning("宽度设置不能小于0")
            if(!isNumber(val) || val < 0) ele.value = 0;
        },
        formRadioChange(value, form) {
            if (value === 'oneColumn') form.column_width = '50%';
                else form.column_width = "10%";
        },
        getPredefine(){
            this.predefine = this.themes['default'];
        },
        useConditionChange(val , key){
           if(val && this.form[key].length === 0){
                this.addCondition(key);
           }
        },
        addCondition(key){
            this.form[key].push({
                logic : "==",
                con_value : "",
                color : "#888"
            })
        },
        removeCondition(inx , key , switchKey){
            this.form[key].splice(inx ,1);
            if(this.form[key].length === 0 && this.form[switchKey] !== undefined) this.form[switchKey] = false;
        },
        limitInputNumber(val , key){
            let reg = /[^\d,]+/g;
            this.form[key] = val.replace(reg ,'');
        },
        mapTypeChange(val){
            if(val === 'country'){
                this.form.mapCode  = ["china"];
            }else {
                this.form.mapCode = this.$map_area_code;
            }
        },
        colorPickerChange(val , defaultColor , key){
            if(val === null && defaultColor){
                this.form[key] = defaultColor;
            }
        },
        upGo(fieldData , index){
            const temp = fieldData[index - 1];
            this.$set(fieldData, index - 1, fieldData[index]);
            this.$set(fieldData, index, temp);
        },
        downGo(fieldData , index){
            const i = fieldData[index + 1];
            this.$set(fieldData, index + 1, fieldData[index]);
            this.$set(fieldData, index, i);
        },
        newTabValue(value , inx){
            let new_value   , index = inx;
            const vm = this;
            let tabs = vm.form.tabList.filter(t => t.value === value + inx);
            if(tabs.length){
                index ++;
                new_value = vm.newTabValue(value , index);
            }else {
                new_value = value + inx;
            }
            return new_value;
        },
        async addTabList(){
            const vm = this;
            let inx = vm.form.tabList.length +1 , name = 'Tab';
            let tab_value , v_index = 1;
            tab_value = vm.newTabValue(name , v_index);
            vm.form.tabList.push({
                inxT : name , tip : "标题" , label : name + inx ,
                value : tab_value ,
                nodes :[]
            })
        },
        deleteTabs(inx , list){
            const vm  = this;
            vm.confirm('删除Tab' , `确认删除\'${list.value}\'` , ()=>{
                vm.form.tabList.splice(inx ,1);
            })
        },
        fixCheckAllChange(val , key){
            const vm = this;
            vm.form[key] = val ? vm.localForm[key].allCheck : [];
            vm.form.isIndeterminate = false;
        },
        fixCheckedChange(val , key){
            const vm = this;
            let checkedCount = val.length;
            vm.form.checkAll = checkedCount === vm.localForm[key].allCheck.length;
            vm.form.isIndeterminate = checkedCount > 0 && checkedCount < vm.localForm[key].allCheck.length;
        },
        changePredefine(key , isNew){
            this.predefine = this.themes[this.form[key+'_theme']];
            this.isColorNew = !!isNew;
        },
        changeColorSingle(key ,inx){
            const vm = this;
            vm.$refs[key+'_p'][0].handleTrigger();
            vm.isColorNew = false;
            vm.colorInx = inx;
            vm.color = vm.form[key][inx];
            vm.changePredefine(key);
        },
        editAlias(val , fieldId){
            if(fieldId){
                let value = val.trim();
                this.$emit("editAlias" , fieldId ,value);
                this.setFieldsOpt(fieldId , value);
            }else {
                this.$message.info("请选择字段");
            }
        },
        setFieldsOpt(fieldId , val){
            const vm = this;
            vm.form.options.forEach(item => {
                if(item.value === fieldId){
                    item.alias = val;
                }
            })
        },
        fieldChange(val , data){
            const vm = this;
            vm.form.alias = data.alias;
        },
        setPosType(){
            const {form} = this;
            form.left_type = form.top_type = form.bottom_type = form.right_type = 'abs';
        },
        radioChange(val , key){
            const vm = this , {form , isPie} = this;

            if(key === 'legend_pos' && !isPie){
                vm.setPosType();
                if (val === 'left') {
                    form.left = 100;
                    form.right = 100;
                    form.bottom = 26;
                    form.top = 46;
                } else if (val === 'right') {
                    form.left = 40;
                    form.right = 120;
                    form.bottom = 26;
                    form.top = 46;
                } else if (val === 'top') {
                    form.left = 40;
                    form.right = 100;
                    form.bottom = 26;
                    form.top = 80;
                } else if (val === 'bottom') {
                    form.left = 40;
                    form.right = 100;
                    form.bottom = 50;
                    form.top = 46;
                }else {
                    form.left = 40;
                    form.right = 100;
                    form.bottom = 30;
                    form.top = 46;
                }
            }
        },
        gridChange(val , key){
            const vm = this;
            if(vm.isPie) return;
            if(val === "abs"){
                // vm.form[key] = 40;
            }else if(vm.form[key] > 100) {
                vm.form[key] = 100;
            }

        },
        sliderGridChange(val , key){
            const vm = this;
            if(val === "per" && vm.form[key] > 100){
                vm.form[key] = 100;
            }
        },
        changeColor(val , key , colorNew ,colorInx){
            if(val){
                if(colorNew){
                    this.form[key].push(val);
                }else {
                    this.form[key][colorInx] = val;
                    this.isColorNew = true;
                    this.$emit('styleRenew', this.form);
                }
            }

        },
        deleteNode(inx , key){
            this.form[key].splice(inx , 1);
        },
        visibleChange(key){
            this.storeSize = this.form[key];
        },
        sizeChange(val, key ){
            const vm = this;
            let size = parseInt(val);
            if( isNaN(size) ){
                vm.$message.info('请输入数字');
                vm.form[key] = this.storeSize;
            }else {
                if(size < 12){
                    vm.$message.info('谷歌最小字体12px,请输入12及以上数字！');
                    vm.form[key] = this.storeSize;
                }else {
                    vm.form[key] = size +  'px' ;
                    vm.storeSize = size  +  'px';
                }
            }
        },
        checkedNumber(val ,key , min ){
            if(val === undefined){
                this.form[key] = min;
            }
        }
    },
    created (){

    }
}
