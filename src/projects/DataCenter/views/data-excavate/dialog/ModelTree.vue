<template>
    <div class="dataTree">
        <el-input placeholder="请输入名称" v-model="filterText" size="medium" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn">
            <dg-tree
                    class="filter-tree"
                    v-loading="settings.loading"
                    :data="data"
                    :props="defaultProps"
                    node-key="id"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    :highlight-current="true"
                    @node-click="nodeClick"
                    :default-expand-all="true"
                    :current-node-key="currentNode"
                    ref="tree">

                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-label el-tree-node__label"
                          :title="node.label"
                          :class="{
                            'el-icon-folder' : !node.expanded && node.level <= 2  ,
                            'el-icon-folder-opened' : node.expanded && node.level <= 2 ,
                            'el-icon-document' : !data.isParent || data.children.length
                          }"
                    >{{ node.label }}</span>
                </span>

            </dg-tree>
        </div>
    </div>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    export default {
        name: "DataTree",
        mixins : [ commonMixins  , servicesMixins],
        props:{
            hasSave:Boolean,
            isNew:Boolean,
            notFirst:Boolean,
            row : Object,
            dirId : String
        },
        data() {
            return {
                filterText: '',
                data: [],
                defaultProps: {
                    children: 'children',
                    label: 'label',
                    value : "id"
                },
                currentNode : '0'
            }
        },
        watch:
            {
                filterText(val) {
                    this.$refs.tree.filter(val);
                }
            },

        methods: {
            nodeClick(v,i){
                this.$emit('nodeClick',v.label,v.id,i);
            },
            initTree() {
                const vm = this , {modelingServices , modelingMock , settings } = this;
                let services = vm.getServices(modelingServices , modelingMock);
                let parentId = '';
                if(this.notFirst===true){
                    parentId = '-1';
                }
                settings.loading = true;
                vm.data = [];
                if(vm.row){
                    vm.currentNode = vm.row.dirParentId;
                }else if(vm.dirId){
                    vm.currentNode = vm.dirId;
                }
                services.queryTransTree(parentId , '' , 'DIG_DIR_MF' , settings)
                    .then(res => {
                        //结果
                        if(res.data.status === 0){
                            if(!res.data.data || res.data.data.length === 0) return ;
                            let result = res.data.data, curId = result[0].id;
                            for(let j = 0;j<result.length;j++){
                                let treeData ={
                                    id : result[j].id,
                                    label: '',
                                    children: [],
                                    isParent : true ,
                                    parent : ''
                                };
                                treeData.label = result[j].name;
                                let child = {};
                                if(result[j].children !== null) {
                                    result[j].children.forEach((item , i) =>{
                                        child = {
                                            label:item.name,
                                            id:item.id,
                                            isParent : true,
                                            children: [],
                                            parent:'0'
                                        };
                                        treeData.children.push(child);
                                    });
                                }
                                vm.data.push(treeData);
                            }
                            vm.$nextTick(()=>{
                                if(vm.$refs.tree && vm.dirId === "-1"){
                                    vm.$refs.tree.setCurrentKey(curId);
                                    let currentNode = vm.$refs.tree.getNode(curId);
                                    vm.nodeClick(currentNode.data , currentNode);
                                }
                            });
                        }
                    })
            },
            /*filterNode(value, data) {
                if (!value) return true;
                return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },*/
            filterNode : treeMethods.methods.filterNode ,
            initAllTreeNode(){
                const vm = this , {modelingServices , modelingMock , settings } = this;
                let services = vm.getServices(modelingServices , modelingMock);
                settings.loading = true;
                services.getTreeAllNode('DIG_DIR_MF',settings).then(res =>{
                    if(res.data.status === 0){
                        let result = res.data.data;
                        result.forEach(item=> {
                            if(item.label === "分享给我的"){
                                item.isParent = true;
                            }
                        });
                        vm.data = result;
                    }
                })
            }
        },
        created() {
            if(this.isNew === false){
                this.initTree();
            }else{
                this.initAllTreeNode();
            }
        }
    }
</script>

<style scoped lang="less">


    .dataTree {
        float: left;
        margin: 10px 0 0 10px;
        width: 230px;
        padding: 10px;
        background: #fff;
        border: 1px solid #ddd;
        height: calc(100% - 88px);
    }

    .ce-tree {
        margin-top: 12px;
        height: calc(100% - 50px);
        overflow: auto;
    }

    .ce-tree__menu {
        position: fixed;
        top: 0;
        min-width: 80px;
        text-align: left;
        border: 1px solid #ccc;
        background: #fff;
        padding: 0;
        z-index: 100;
        box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
        border-radius: 2px;
    }

    .ce-tree__menu li {
        cursor: pointer;
        list-style: none outside none;
        font-size: 12px;
        white-space: nowrap;
        border-bottom: 1px solid #eee;
        padding: 0 12px;
        height: 28px;
        line-height: 28px;
        color: #666;
    }

    .ce-tree__menu li:hover {
        color: @font-color;
    }
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }

    .node-label {
        max-width: 166px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .node-label::before {
        padding-right: 5px;
    }
</style>
