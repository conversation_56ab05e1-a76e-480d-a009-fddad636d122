<template>
    <div class="FilterCommon">
        <div class="ce-list_box">
            <template v-for="(filter , key ) in filterList">
            <div :key="key"
                 v-if="
                  jsType === 'TEXT' && (key === 'text' || key === 'number') ? true :
                  jsType === 'NUMBER' && key === 'number' ? true :
                  jsType !== 'TEXT' && jsType !== 'NUMBER' && key === 'time'
">
                <div class="ce-list_header">{{filter.title}}</div>
                <ul class="ce-list_cont">
                    <template v-for="item in filter.list">
                    <li class="ce-list_item" :key="item.value"
                        v-if="key === 'text' && dbType === 'elasticsearch' ? item.value === 'precise' : true"
                        :class="{'active_item' : activeValue === item.value}" @click="checkedItem(item)">{{item.label}}
                    </li>
                    </template>
                </ul>
            </div>
            </template>
        </div>
        <div class="ce-filter_operate">
            <div class="ce-node_filter">
                <ul class="ce-node">
                    <div class="ce-item_name">字段</div>
                    <template  v-for="item in filterNodes">
                    <li v-if="filterPage!=='issue'" class="ce-node_item" :key="item.value">
                        {{item.label}}
                    </li>
                    </template>
                    <el-select filterable v-if="filterPage==='issue'" v-model="paramValue" @change="paramsValChange" class="ce-issue" size="mini">
                        <el-option v-for="item in filterParam"
                                   :key="item.value"
                                   :value="item.value"
                                   :label="item.label"
                        ></el-option>
                    </el-select>
                </ul>
                <div class="ce-result_title mt5 mb5">过滤条件设置</div>
                <FilterDate @changeFilter="changeFilter(arguments , 'dateTimeVal')" :data="dateTimeVal"
                            :type="activeValue" :tabVal="interval_tab" :interval_date="interval_date" :tip="'请选择年'"
                            v-if="activeValue === 'year'"/>
                <FilterDate @changeFilter="changeFilter(arguments , 'dateTimeVal')" :data="dateTimeVal"
                            :type="activeValue" :tabVal="interval_tab" :interval_date="interval_date" :tip="'请选择年月'"
                            v-if="activeValue === 'month'"/>
                <FilterDate @changeFilter="changeFilter(arguments , 'dateTimeVal')" :data="dateTimeVal"
                            :type="activeValue" :tabVal="interval_tab" :interval_date="interval_date" :tip="'请选择日期'"
                            v-if="activeValue === 'date'"/>
                <FilterDate @changeFilter="changeFilter(arguments , 'dateTimeVal')" :data="dateTimeVal"
                            :type="activeValue" :tabVal="interval_tab" :interval_date="interval_date" :tip="'请选择时间'"
                            v-if="activeValue === 'datetime'"/>
                <FilterDate @changeFilter="changeFilter(arguments , 'dateTimeVal')" :tabVal="interval_tab"
                            :interval_date="interval_date" :data="dateTimeVal" :type="activeValue" :tip="'月份'"
                            v-if="activeValue === 'monthrange'"/>
                <FilterDate @changeFilter="changeFilter(arguments , 'dateTimeVal')" :tabVal="interval_tab"
                            :interval_date="interval_date" :data="dateTimeVal" :type="activeValue" :tip="'日期'"
                            v-if="activeValue === 'daterange'"/>
                <FilterText @changeFilter="changeFilter(arguments , 'textVal')" :data="textVal" :type="activeValue"
                            :tip="'完全'" v-if="activeValue === 'precise'"/>
                <FilterText @changeFilter="changeFilter(arguments , 'textVal')" :data="textVal" :type="activeValue"
                            :tip="'前缀模糊'" v-if="activeValue === 'prefix_fuzzy'"/>
                <FilterText @changeFilter="changeFilter(arguments , 'textVal')" :data="textVal" :type="activeValue"
                            :tip="'后缀模糊'" v-if="activeValue === 'suffix_fuzzy'"/>
                <FilterText @changeFilter="changeFilter(arguments , 'textVal')" :data="textVal" :type="activeValue"
                            :tip="'模糊'" v-if="activeValue === 'fuzzy'"/>
                <FilterText @changeFilter="changeFilter(arguments , 'textVal')" :data="textVal" :type="activeValue"
                            :tip="'不等于'" v-if="activeValue === 'not_equal'"/>

                <!--                <FilterSelectTree @changeFilter="changeTreeFilter(arguments , 'treeVal' , 'selectTreeNodes' )" :type="activeValue"  :tip="'下拉树'" :data="treeVal" :options="treeOptions" v-if="activeValue === 'select_tree'"/>-->
                <FilterSelectTree
                        @changeFilter="changeTreeFilter(arguments , 'enumVal' , 'selectEnumNodes' ,'enumCode' , 'enumCodeLabel')"
                        :type="activeValue" :tip="'枚举树'" :data="enumVal" :code="enumCode" :options="enumOptions"
                        v-if="activeValue === 'enum_tree'"/>
                <FilterDigital @changeFilter="changeDigital" :label="'数值'" :data="digitalVal" :type="activeValue"
                               v-if="activeValue === 'digital'"/>
                <FilterDigital @changeFilter="changeDigital" :label="'数值区间'" :data="digitalVal" :type="activeValue"
                               v-if="activeValue === 'interval_digital'"/>
            </div>
        </div>
    </div>
</template>

<script>
    // import SearchTree from '../../../component/tree/SearchTree'
    import FilterDate from './FilterDate'
    import FilterText from './FilterText'
    import FilterSelectTree from './FilterSelectTree'
    import FilterDigital from './FilterDigital'

    export default {
        name: "FilterCommon",
        components: {
            // SearchTree,
            FilterDate,
            FilterText,
            FilterSelectTree,
            FilterDigital
        },
        props: {
            filterPage: String,
            filterParam: Array,
        },
        data() {
            return {
                paramOptions: [
                    {
                        label: "字段1",
                        value: "value1"
                    }, {
                        label: "字段2",
                        value: "value2",
                    }
                ],
                paramValue: "",
                filterNodeData: {},
                activeValue: '',
                treeData: [],
                filterList: {
                    time: {
                        title: '时间过滤组件',
                        list: [
                            {
                                label: '年份',
                                value: 'year'
                            },
                            {
                                label: '年月',
                                value: 'month'
                            },
                            {
                                label: '日期',
                                value: 'date'
                            },
                            {
                                label: '时间',
                                value: 'datetime'
                            },
                            {
                                label: '年月区间',
                                value: 'monthrange'
                            },
                            {
                                label: '日期区间',
                                value: 'daterange'
                            }
                        ]
                    },
                    text: {
                        title: '文本过滤组件',
                        list: [
                            {
                                label: '精准过滤',
                                value: 'precise'
                            }, {
                                label: '前缀模糊过滤',
                                value: 'prefix_fuzzy'
                            }, {
                                label: '后缀模糊过滤',
                                value: 'suffix_fuzzy'
                            }, {
                                label: '模糊过滤',
                                value: 'fuzzy'
                            }, {
                                label: '不等于',
                                value: 'not_equal'
                            },
                        ]
                    },
                    tree: {
                        title: '树过滤组件',
                        list: [
                            /*{
                                label: '下拉树',
                                value: 'select_tree'
                            },*/ {
                                label: '枚举树',
                                value: 'enum_tree'
                            },
                        ]
                    },
                    number: {
                        title: '数值过滤组件',
                        list: [
                            {
                                label: '数值',
                                value: 'digital'
                            }, {
                                label: '数值区间',
                                value: 'interval_digital'
                            },
                        ]
                    }
                },
                filterNodes: [],
                dateTimeVal: '',
                textVal: '',
                treeVal: '',
                selectTreeNodes: [],
                treeOptions: [],
                enumVal: '',
                enumCode: "",
                enumCodeLabel:"",
                selectEnumNodes: [],
                enumOptions: [],
                digitalVal: {
                    leftV: {
                        value: '',
                        logical: '='
                    },
                    rightV: {
                        value: '',
                        logical: '>'
                    }
                },
                interval_date: {
                    range: 'before',
                    value: '',
                    year: 0,
                    month: 0,
                    day: 0
                },
                interval_tab: 'picker',
                jsType : "" ,
                dbType :"" ,
                timeChange : false
            }
        },
        methods: {
            paramsValChange(val){
                const vm = this;
                this.filterParam.forEach(item =>{
                    if(val === item.value){
                        vm.jsType = item.jsType || "";
                        vm.dbType = item.dbType || "";
                        vm.activeValue = "";
                    }
                })
            },
            initValue(data) { //储值 ， 回填数据
                //console.log(data);
                this.jsType = data.data.format;
                this.dbType = data.data.dbtype;
                this.filterNodes = data.options; //过滤的字段
                this.filterNodeData = data; // 过滤数据存储
                this.activeValue = data.filterResult.type;
                this.backdata(data.filterResult);//回填
            },
            backdata(filter) {
                switch (filter.type) {
                    case "year" :
                    case "month" :
                    case "date" :
                        this.dateTimeVal = new Date(filter.value[0]);
                        break;
                    case "datetime" :
                        this.dateTimeVal = new Date(filter.value);
                        break;
                    case "monthrange":
                    case "daterange":
                        if (filter.tab === 'picker') {
                            this.dateTimeVal = filter.value !== "" ? [new Date(filter.value[0]), new Date(filter.value[1])] : "";
                        } else {
                            filter.value ? this.interval_date = filter.value : true;
                        }
                        this.interval_tab = filter.tab;

                        break;
                    case "precise":
                    case "prefix_fuzzy":
                    case "suffix_fuzzy":
                    case "fuzzy":
                    case "not_equal":
                        this.textVal = filter.value;
                        break;
                    case "select_tree" :
                        this.setTreeData('treeVal', 'selectTreeNodes', 'treeOptions', filter);
                        break;
                    case "enum_tree" :
                        this.setTreeData('enumVal', 'selectEnumNodes', 'enumOptions', 'enumCode', filter);
                        break;
                    case "digital" :
                        if(filter.value){
                            this.digitalVal = filter.value;
                        }else {
                            this.digitalVal = {
                                leftV: {
                                    value: '',
                                    logical: '='
                                },
                                rightV: {
                                    value: '',
                                    logical: '>'
                                }
                            }
                        }
                        break;
                    case "interval_digital" :
                        if(filter.value){
                            this.digitalVal = filter.value;
                        }else {
                            this.digitalVal = {
                                leftV: {
                                    value: '',
                                    logical: '<'
                                },
                                rightV: {
                                    value: '',
                                    logical: '>'
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }
            },
            setTreeData(valueT, nodeT, optionT, codeT, filter) {
                this[valueT] = filter.value;
                this[nodeT] = filter.nodes;
                this[optionT] = filter.options;
                this[codeT] = filter.code;
            },
            checkedItem(item) { // 选择过滤器
                this.activeValue = item.value;
                if (['year', 'month', 'date', 'datetime'].indexOf(item.value) > -1 && Array.isArray(this.dateTimeVal)) {
                    this.dateTimeVal = ""; //清空由区间的数据
                }else if(item.value === 'monthrange' || item.value === 'daterange'){
                    this.dateTimeVal = [];
                    this.interval_tab = 'picker';
                    this.interval_date = {
                        range: 'before',
                            value: '',
                            year: 0,
                            month: 0,
                            day: 0
                    }
                }
                if(item.value === 'digital'){
                    this.digitalVal= {
                        leftV: {
                            value: '',
                            logical: '='
                        },
                        rightV: {
                            value: '',
                            logical: '>'
                        }
                    };
                }else if(item.value === 'interval_digital'){
                    this.digitalVal = {
                        leftV: {
                            value: '',
                            logical: '<'
                        },
                        rightV: {
                            value: '',
                            logical: '>'
                        }
                    };
                }

               /* if (item.value === 'select_tree') {
                    this.getSelectTreeOption();
                } else if (item.value === 'enum_tree') {
                    //this.getEnumTreeOption();
                }*/
            },
            getSelectTreeOption() {
                //this.filterNodeData 过滤字段 数据，请求下拉树数据
                // let id=this.filterNodeData.data.id;
                // this.$axios.post("/widget/loading", {
                //     code: this.node.code,
                //     data: JSON.stringify(this.node.widget),
                //     type: this.isView
                // }).then(res => {
                //
                //
                // }).catch(err => {
                //     _this.loading = false;
                //     this.$message.error("服务器异常，请联系管理员！" + err);
                // });
            },
            getEnumTreeOption() {
                //this.filterNodeData 过滤字段 数据，请求枚举树数据
            },
            joinDateTime(type, dateVal) { //处理 时间数据转为毫秒
                let rValue, datetime, start, end, nextM, dateTimeVal;
                dateTimeVal = dateVal ? dateVal : this.dateTimeVal;
                if (dateTimeVal) {
                    if (type === 'year') {
                        datetime = dateTimeVal.getFullYear();
                        start = new Date(datetime + '-01-01 00:00:00').getTime();
                        end = new Date(datetime + '-12-31 23:59:59').getTime();
                    } else if (type === 'month') {
                        datetime = dateTimeVal.getFullYear() + '-' + (dateTimeVal.getMonth() + 1);
                        nextM = dateTimeVal.getFullYear() + '-' + (dateTimeVal.getMonth() + 2);
                        start = new Date(datetime + ' 00:00:00').getTime();
                        end = new Date(nextM + ' 23:59:59').getTime() - 24 * 60 * 60 * 1000;
                    } else if (type === 'date') {
                        datetime = dateTimeVal.getFullYear() + '-' + (dateTimeVal.getMonth() + 1) + '-' + dateTimeVal.getDate();
                        start = new Date(datetime + ' 00:00:00').getTime();
                        end = new Date(datetime + ' 23:59:59').getTime();
                    }
                    rValue = [start, end];
                } else {
                    rValue = '';
                }
                return rValue;
            },
            getResult() { //确定 获取结果数据
                let result, type = this.activeValue;
                switch (type) {
                    case "year":
                        result = {
                            value: this.joinDateTime(type),
                            parentType: 'time'
                        };
                        break;
                    case "month":
                        result = {
                            value: this.joinDateTime(type),
                            parentType: 'time'
                        };
                        break;
                    case "date" :
                        result = {
                            value: this.joinDateTime(type),
                            parentType: 'time'
                        };
                        break;
                    case "datetime":
                        result = {
                            value: this.dateTimeVal === '' ? '' : this.dateTimeVal.getTime(),
                            parentType: 'time'
                        };
                        break;
                    case "monthrange":
                        result = this.joinIntervalData('month');
                        break;
                    case "daterange" :
                        result = this.joinIntervalData('date');
                        break;
                    case "precise":
                    case "prefix_fuzzy":
                    case "suffix_fuzzy":
                    case "fuzzy":
                    case "not_equal":
                        result = {
                            value: this.textVal,
                            parentType: 'text'
                        };
                        break;
                    case "select_tree":
                        result = this.joinTreeData('treeVal', 'selectTreeNodes', 'treeOptions');
                        break;
                    case "enum_tree" :
                        result = this.joinTreeData('enumVal', 'selectEnumNodes', 'enumOptions', 'enumCode' , 'enumCodeLabel');
                        break;
                    case "digital":
                    case "interval_digital":
                        result = {
                            value: this.joinDigitalDta(type),
                            parentType: 'number'
                        };
                        break;
                    default:
                        this.$message.info('请选择过滤插件!');
                        return ;
                }
                if (result) result.type = this.activeValue;
                if (this.filterPage === "issue") {
                    if (this.paramValue.trim() === "") {
                        this.$message.warning("请选要过滤的字段！");
                        return;
                    }
                    //服务发布使用过滤页面
                    let data = {};
                    this.filterParam.forEach(item => {
                        if (item.value === this.paramValue) {
                            data = item;
                        }
                    });
                    let res = {
                        result: result,
                        param: data,
                    };
                    return res;
                }
                return result;
            },
            joinDigitalDta(type) { //获取数值结果数据
                let condition = type === 'digital' ? this.digitalVal.leftV.value === '' :
                    this.digitalVal.leftV.value === '' && this.digitalVal.rightV.value === '';
                if (condition) return '';
                return this.digitalVal; //数值过滤只取  leftV  ， 数值区间 取 leftV rightV 两个条件
            },

            joinTreeData(treeT, nodeT, optionT, codeT ,codeLableT) { //获取树的结果数据
                return {
                    value: this[treeT],
                    nodes: this[nodeT],
                    options: this[optionT],
                    code: this[codeT],
                    parentType: 'tree',
                    codeLabel : this[codeLableT]
                }
            },
            joinIntervalData(type) {
                let result, condition;
                if (this.interval_tab === 'interval') {
                    condition = this.interval_date.value !== "" ;
                    result = {
                        value: condition ? this.interval_date : '',
                        tab: 'interval',
                        parentType: 'time'
                    }
                } else {
                    condition = this.dateTimeVal !== '' && this.dateTimeVal[0];
                    result = {
                        value: condition ? [
                            this.dateTimeVal[0].getTime(),
                            this.joinDateTime(type, this.dateTimeVal[1])[1]
                        ] : '',
                        tab: 'picker',
                        parentType: 'time'
                    };
                }
                return result;
            },

            changeFilter(params, target) { // 时间过滤 文本过滤子组件 改变传递修改值
                this.interval_tab = params[1];
                if (params[1] === 'interval') {
                    this.interval_date = this.copyArrayObj(params[2]);
                } else {
                    this[target] = params[0];
                }
            },

            changeTreeFilter(params, valueT, nodesT, codeT ,labelT) { // 树组件改变传递修改值
                this[valueT] = params[0];
                this[nodesT] = params[1];
                this[codeT] = params[2];
                this[labelT] = params[3];
            },
            changeDigital(leftV, log, rightV, log1) { //数值组件改变 传递修改值
                this.digitalVal = {
                    leftV: {
                        value: leftV,
                        logical: log
                    },
                    rightV: {
                        value: rightV,
                        logical: log1
                    }
                };
            }
        },
        created() {
            if(this.filterParam && this.filterParam.length){
                this.paramsValChange(this.filterParam[0].value);
                this.paramValue = this.filterParam[0].value;
            }
        }
    }
</script>

<style scoped>
    .FilterCommon {
        overflow: hidden;
    }

    .ce-list_box {
        border: 1px solid #ddd;
        width: 416px;
        float: left;
        height: 410px;
    }

    .ce-list_header {
        background: #e7eaf0;
        color: #656567;
        line-height: 34px;
        padding: 0 14px;
    }

    .ce-list_cont {
        padding: 10px;
        overflow: hidden;
    }

    .ce-list_item {
        float: left;
        min-width: 80px;
        text-align: center;
        color: #666;
        border: #ddd 1px solid;
        border-radius: 4px;
        padding: 5px 10px;
        cursor: pointer;
        margin: 0 6px 6px 0;
        box-sizing: border-box;
    }

    .ce-list_item:hover, .ce-list_item.active_item {
        color: #fff;
        background-color: #409EFF;
        border-color: #409EFF;
    }

    .ce-filter_operate {
        float: right;
        width: calc(100% - 428px);
        height: 410px;
        border: 1px solid #ddd;
    }

    .ce-result_title {
        color: #333;
        line-height: 28px;
        padding-left: 8px;
        font-size: 14px;
    }

    .ce-result_node {
        height: 100%;
        width: 200px;
        padding: 10px;
        box-sizing: border-box;
        float: left;
        border-right: 1px solid #ddd;
    }

    .ce-result_tree {
        height: calc(100% - 28px);
    }

    .ce-node_filter {
        padding: 10px;
    }

    .ce-node {
        border: 1px solid #ddd;
        min-height: 28px;
        border-radius: 4px;
        position: relative;
        padding-left: 50px;
    }

    .ce-item_name {
        position: absolute;
        padding: 0 10px;
        background: #e7eaf0;
        float: left;
        color: #555;
        top: 0;
        left: 0;
        bottom: 0;
        line-height: 28px;
    }

    .ce-node_item {
        float: left;
        font-size: 12px;
        color: #909399;
        background: #f4f4f5;
        height: 24px;
        padding: 0 5px;
        line-height: 19px;
        border: 1px solid #e9e9eb;
        box-sizing: border-box;
        margin: 2px 0 2px 6px;
        border-radius: 4px;
    }

    .ce-issue {
        width:100%;
    }
</style>
