.new-column-pop {
  display: flex;
  .new-column-pop-left {
    position: relative;
    width: 480px;
    border-right: 1px solid #e8e8e8;
    padding-right: 16px;
  }

  .new-column-pop-right {
    flex: 1;
    width: 360px;
    border: 1px solid #d7d7d7;
    margin-left: 16px;
    .new-column-pop-right-cont {
      height: 458px;
      overflow: auto;
    }
    .new-column-pop-right-header {
      height: 44px;
      line-height: 44px;
      padding: 0 8px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      background-color: rgba(0, 0, 0, 0.02);
    }
    .right-cont-item {
      padding: 16px 0;
      margin: 0 16px;

      .tip {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
      }
      .new-column-table th {
        color: rgba(0, 0, 0, 0.65);
        padding: 4px 16px 4px 0;
      }
      .new-column-table td {
        padding: 4px 16px 4px 0;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .right-cont-item + .right-cont-item {
      border-top: 1px solid rgba(0, 0, 0, 0.15);
    }
  }
  /deep/.el-textarea__inner {
    height: 150px;
  }
}