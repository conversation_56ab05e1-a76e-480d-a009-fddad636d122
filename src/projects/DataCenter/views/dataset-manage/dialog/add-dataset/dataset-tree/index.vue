<template>
    <div class="dataset-tree">
        <el-input
                class="mb8"
                placeholder="请输入名称搜索"
                suffix-icon="el-icon-search"
                v-model="filterText"
        ></el-input>
        <dg-tree
                v-model="addDataId"
                v-loading="settings.loading"
                node-key="id"
                class="tree"
                :data="data"
                :props="defaultProps"
                :filter-node-method="filterNode"
                radio-type="all"
                ref="tree"
                check-strictly
                check-leaf
        >
             <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                        <span class="node-label"
                              :title="data.name"
                        >{{ data.name }}</span>
                 </span>
        </dg-tree>
    </div>
</template>

<script src="./dataset-tree.js"></script>
<style scoped lang="less" src="./dataset-tree.less"></style>