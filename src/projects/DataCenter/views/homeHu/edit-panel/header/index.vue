<template>
    <div class="header selectn portal__header">
        <div class="portal__header-info">
            <i class="el-icon-arrow-left ce-portal_icon" title="返回" @click="closePanel"></i>
            <div class="comm-wrap is-require">
                <h3 v-if="!isEditPlanName" :title="planName" @click="editPlanName">
                    {{ planName }}
                </h3>
                <el-input
                        ref="plan"
                        v-show="isEditPlanName"
                        v-model.trim="planName"
                        v-input-filter="planName"
                        placeholder="请输入门户名称(限特殊字符)"
                        size="small"
                        @input="inputFilterSpecial($event , 'planName')"
                        @keyup.enter.native="isEditPlanName = false"
                        @blur="isEditPlanName = false"
                ></el-input>
            </div>
            <i class="el-icon-edit ce-portal_icon" @click="editPlanName"></i>
            <help-document :code="documentCode"></help-document>
        </div>
        <div class="mask" v-if="!isLock" @click="handleTips"></div>
        <div class="portal__header-actions">
            <div
                    :class="['portal__actions-item', 'action', 'action-lock', isLock && 'has-lock']"
                    @click="handleLock"
            >
                <img class="img-default" src="@/assets/images/portal/suoding.png" />
                <img class="img-active" src="@/assets/images/portal/lock1.png" />
                <img class="img-lock" src="@/assets/images/portal/lock.png" />
                <span>{{ isLock ? "已锁定" : "待锁定" }}</span>
            </div>
            <div class="portal__actions-item action" @click="handleSave" v-if="!isCase">
                <img class="img-default" src="@/assets/images/portal/save.png" />
                <img class="img-active" src="@/assets/images/portal/save1.png" />
                <span>保存</span>
            </div>
            <div class="portal__actions-item action" @click="handlePreview">
                <img class="img-default" src="@/assets/images/portal/xyanshi.png" />
                <img class="img-active" src="@/assets/images/portal/preview1.png" />
                <span>预览</span>
            </div>
            <!--<div class="portal__actions-item action" @click="handleReleas" v-if="!isCase">
                <img class="img-default" src="@/assets/images/portal/fabu3.png" />
                <img class="img-active" src="@/assets/images/portal/release1.png" />
                <span>发布</span>
            </div>-->
        </div>
    </div>
</template>

<script src="./header.js"></script>
<style scoped lang="less" src="../edit-panel.less"></style>
<style scoped lang="less" src="./header.less"></style>
