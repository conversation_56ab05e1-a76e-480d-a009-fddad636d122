<template>
    <div class="overview">
        <dg-scrollbar>
            <share-card ref="overview" :data="viewData" show="share" v-bind="$attrs" >
                <ce-card-item slot="item" slot-scope="{item , $index}" :data="item" @click.native="cardClick(item)"></ce-card-item>
                <no-data slot="empty" top="5%"></no-data>
            </share-card>
        </dg-scrollbar>
    </div>
</template>

<script src="./overview.js"></script>
<style scoped lang="less" src="./overview.less"></style>
