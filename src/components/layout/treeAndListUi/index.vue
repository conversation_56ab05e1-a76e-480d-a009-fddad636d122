<template>
    <div class="TreeAndList">
        <div class="ce-left_tree" :style="{width: widthPx}">
            <slot name="left"></slot>
        </div>
        <div class="split-line" v-if="leftWidth !== 'auto'"></div>
        <div class="ce-left_list" :style="{marginLeft :`calc( ${widthPx} + 21px)` } ">
            <slot></slot>
        </div>
        <slot name="other"></slot>
    </div>
</template>

<script lang="js" src="./treeAndList.js"></script>
<style scoped lang="less" src="./treeAndList.less"></style>