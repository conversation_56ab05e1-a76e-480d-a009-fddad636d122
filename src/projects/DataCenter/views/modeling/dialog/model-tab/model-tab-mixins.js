import {globalBus} from "@/api/globalBus";
import {common} from "@/api/commonMethods/common";
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import OpenModel from "@/projects/DataCenter/views/modeling/dialog/OpenModel";
import * as $ from "jquery"

export const tabMixins = {
    mixins : [common , servicesMixins , commonMixins],
    components: {
        OpenModel
    },
    data() {
        return {
            editableTabsValue: '1',
            editableTabs: [],
            tabIndex: 1,
            newId: '' ,
            isTemplate : false ,
            loading :false ,
            modelsName : [],
            saved :false ,
            addModelTxt : "新建模型",
            openModelTxt : "打开模型",

            planIndex: -1,
            isAdd: true ,
            currentRowData : {},
            addLeft : 0 ,
            fullScreenTip : "全屏",
            closeFullScreenTip : "退出全屏",
            closeTip : "关闭" ,
            changeTabTip : "切换",
            newAddTip : "新建" ,
            newAndOpenTip : "新建/打开"
        }
    },
    watch : {
        "editableTabs.length": {
            handler(len) {
                this.triggerEvent('resize' , 300);
            },
            deep: true,
        },
    },
    methods: {
        /**
         * 设置添加按钮位置
         */
        setAddBtnPos(){
            const vm = this , width1 = 50, width2 = 90;
            vm.$nextTick(()=>{
                let tabScrollW = $(".analysis-tab").find(".el-tabs__nav-scroll").width() ,
                    tabW = $(".analysis-tab").find(".el-tabs__nav").width();
                vm.addLeft =  tabW <= tabScrollW ? tabW + width1: tabScrollW + width2;
            })

        },
        checkTab(menu) {
            this.editableTabsValue = menu.id;
            this.saved = menu.rowData.saved;
            this.isTemplate = menu.rowData.isTemplate;
            this.showMore = false;
        },
        tabClick(item){
            // this.isTemplate = item.$children[0].rowData.isTemplate;
            // this.saved = item.$children[0].rowData.saved;
        },
        openSave(){
            const vm = this;
            globalBus.$emit("saveAsModel" ,null, true ,vm.editableTabsValue);
        },
        setSaved(transId){
            const vm = this;
            this.saved = true;
            this.editableTabs.forEach(tab =>{
                if(transId === tab.id){
                    tab.rowData.saved = true;
                }
            });
        },
        onListenFun(){
            globalBus.$on("modelSaveSuccess", this.setSaved);
            globalBus.$on('changeName', this.change);
        },
        offListenFun(){
            globalBus.$off("modelSaveSuccess", this.setSaved);
            globalBus.$off('changeName', this.change);
        },
        modelValidate(value ,instance , done , tip){
            const vm = this;
            if (!value) {
                instance.editorErrorMessage = tip + '不能为空!';
                return;
            }
            let result = vm.modelsName.filter(item =>{
                return item.name === value;
            });
            if(result.length > 0){
                instance.editorErrorMessage = tip + "已存在";
            }else {
                done();
            }
        },
        saveModel(){
            const vm = this;
            vm.loading = true;
            vm.$emit("openNewModel");
            this.$emit('closeTab');
            setTimeout(()=>{
                vm.loading = false;
                vm.removeTab(vm.editableTabsValue);
            },500)
        },
        reName(newName, transId) {
            if (this.editableTabs.length > 0) {
                this.editableTabs.forEach(item => {
                    if (transId === item.id) {
                        item.label = newName;
                    }
                })
            }
        },
        change(name, id) {
            this.triggerEvent('resize' , 300);
            this.editableTabs.forEach(item => {
                if (item.id === id) {
                    item.label = name;
                }
            })
        },
        openModel(type) {
            if(this.$refs.popover)this.$refs.popover.doClose();
            this.$refs.openM.open({}, 'dataCenter', 'open', type);
        },
        removeTab(targetName) {
            const vm = this;
            let tabs = this.editableTabs;
            this.isAdd = false;
            this.planIndex = tabs.findIndex(item => item.id === targetName)
            let activeName = this.editableTabsValue;
            if (activeName === targetName) {
                tabs.forEach((tab, index) => {
                    if (tab.id === targetName) {
                        let nextTab = tabs[index + 1] || tabs[index - 1];
                        if (nextTab) {
                            activeName = nextTab.id;
                        }
                    }
                });
            }
            this.editableTabsValue = activeName;
            
            this.editableTabs = tabs.filter(tab => tab.id !== targetName);
            this.editableTabs.forEach(tab =>{
                if(activeName === tab.id){
                    vm.isTemplate = tab.rowData.isTemplate;
                    vm.saved = tab.rowData.saved;
                }
            });
            if (this.editableTabs.length === 0) {
                this.$emit('closeTab');
                this.$emit("close");
            }
        },
        addNewTrans(rowData, transName) {
            const vm = this , { modelingServices , modelingMock ,} = this;
            let services = vm.getServices(modelingServices , modelingMock);
            services.saveTempTrans(transName).then(res => {
                //console.log("获取新创建的id：", res,res.data.status,res.data.data);
                if (res.data.status === 0) {
                    vm.showFlow(rowData, res.data.data, transName);
                }
            })
        },
        addTab(rowData , isTemplate=false , dirId ) {
            this.isAdd = true;
            if(this.$refs.popover)this.$refs.popover.doClose();
            let newTransName = '新建模型_' + this.tabIndex;
            this.tabIndex ++;
            this.isTemplate = isTemplate;
            rowData.isTemplate = isTemplate;
            rowData.saved = this.saved = false;
            if (rowData.transId === undefined) {
                rowData.dirId = dirId;
                this.addNewTrans(rowData, newTransName);
            } else {
                this.showFlow(rowData, rowData.transId, newTransName);
            }
            rowData.container = this.container + this.tabIndex;
            this.showAddBox = false;
        },

        showFlow(rowData, new_trans_id, newTransName) {
            let tabLabel;
            tabLabel = rowData.modelName ? rowData.modelName : newTransName;
            let rowD = rowData.modelName ? rowData : {
                dirParentId: "",
                modelName: newTransName,
                modelSpec: "",
                modelType: "",
                releaseDate: "",
                status: "",
                transId: new_trans_id,
                isNew: true ,
                ...rowData
            };
            this.currentRowData = rowD;
            let newTab = {
                id: new_trans_id,
                label: tabLabel,
                rowData: rowD,
            };
            let hasTab = false;
            hasTab = this.hasTabs(newTab, hasTab);//
            if (hasTab || this.editableTabs.length === 0) {
                this.editableTabs.push(newTab);
                this.editableTabsValue = new_trans_id;
            }
        },

        hasTabs(newTab, hasTab) {
            let tabs = this.editableTabs;
            for (let i = 0; i < tabs.length; i++) {
                if (tabs[i].id === newTab.id) {
                    this.editableTabsValue = newTab.id;
                    hasTab = false;
                    return;
                } else {
                    hasTab = true;
                }
            }
            return hasTab;
        },
        backTo() {
            this.$emit('backTo');
        },
        setTabWatch() {
            window.addEventListener(
                "resize", this.setAddBtnPos, false
            );
        },
        removeTabHWatch() {
            window.removeEventListener(
                "resize", this.setAddBtnPos, false
            );
        },
    },
    created() {
        this.onListenFun();
        this.setTabWatch();
    },
    destroyed() {
        this.offListenFun();
        this.removeTabHWatch();
    }
}
