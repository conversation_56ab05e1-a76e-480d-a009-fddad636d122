<template>
    <div @dragover.prevent="allowDrop" @drop="drop" class="ruleFlowPanel flowPanelEt" v-if="easyFlowVisible">
        <template v-for="node in data.nodeList">
            <RuleNode
                    :class="{'ce-plug_field': node.type === 'VARIABLE_OPERATOR', 'ce-plug_constant': node.type === 'CONSTANT_OPERATOR'}"
                    :id="node.id"
                    :key="node.id"
                    :node="node"
                    :ref="node.id"
                    @changeNodeSite="changeNodeSite"
                    @deleteNode="deleteNode"
                    @editAttr="editAttr"
                    @editNode="editNode"
                    v-if="node.show"
            />
        </template>
        <NodeForm @change="changeVal" ref="nodeForm" v-if="nodeFormVisible"/>
        <RuleLineLabel @changeLabel="changeLabelFn" ref="lineLabel"/>
        <editPlug ref="edit" @editNode="editNodeField"/>
        <instructions ref="instructions"></instructions>
    </div>
</template>

<script>
import {jsPlumb} from 'jsplumb'
// import {flowMixins} from "@/api/commonMethods/flow-mixins"
import RuleNode from './RuleNode'
import NodeForm from '@/components/flowPanel/flow/NodeForm'
import {globalBus} from '@/api/globalBus';
import RuleLineLabel from './RuleLineLabel'
import {common} from "@/api/commonMethods/common"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins"
import editPlug from "./dialog/index"
import FlowPanel from '@/components/flowPanel/FlowPanel'
import instructions from "./dialog/instructions/instructions.vue";
import {mapActions} from "vuex";

export default {
    name: "ExcavateRuleFlowPanel",
    mixins: [common, commonMixins, servicesMixins],
    extends: FlowPanel,
    components: {
        RuleNode,
        NodeForm,
        RuleLineLabel,
        editPlug,
        instructions
    },
    data() {
        return {
            serviceOrgId: '',
            easyFlowVisible: false,
            lineLabelOptions: {},
            edgEvt: {},
            savedEdgID: "",
            timer: null,
            allNodesName: [],
            deleteNodeWithLine: false,//删除的节点带连线
            sourceField: [],
            canDrop: false,
            typeStrs: {
                "double": ["Double", "Integer", "Float", "Byte", "Long", "BigInteger"],
                "long": ["Long", "Integer", "BigInteger"],
                "text": ["Text", "String", "Double", "Integer", "Float", "Byte", "Long", "BigInteger"],
                "string": ["Text", "String", "Double", "Integer", "Float", "Byte", "Long", "BigInteger"],
            },
            paramsInx: 0,
            variableNodes: [],
        }
    },
    props: {
        sourceData: Object,
        row: Object,
        paramsNode: Object,
        loadParams:{
            type:Object,
            default:()=>{
                return {
                    loading: false
                }
            }
        }
    },
    watch: {
        data: {
            handler(val) {
                this.reNewNodes(val);
            },
            deep: true
        }
    },
    methods: {
        ...mapActions(["reNewNodes"]),
        editNodeField(type, fieldV, field, dataType, id) {
            const vm = this;
            vm.data.nodeList.forEach(node => {
                if (node.id === id) {
                    if (type === "field") {
                        node.label = field;
                        node.value = fieldV;
                        node.dataType = dataType;
                    } else if (type === "constant") {
                        node.value = node.label = fieldV;
                        node.dataType = dataType;
                    }
                }
            })
        },
        saveEdaData(edaInfo) {//修改 eda 数据
            this.$refs.edaList.saveData(edaInfo);
        },
        addEdaData(edaInfo) {//新增 eda 数据
            this.$refs.edaList.addData(edaInfo);
        },
        addEda(stepId, stepName, row, isAdd) {//eda 操作页
            this.$refs.EdaO.show(row, stepId, stepName, isAdd);
        },
        edaFun(stepId, stepName) {//eda 日志列表
            this.$refs.edaList.show(stepId, stepName);
        },
        editAttr(node) {
            const vm = this;
            let type = node.type === "VARIABLE_OPERATOR" ? "field" : node.type === "CONSTANT_OPERATOR" ? "constant" : "UDF";
            if (type !== "UDF") {
                vm.$refs.edit.show(type, node.name, vm.sourceField, node);
            } else {
                vm.$refs.instructions.show(node.udfOperatorId);
            }
        },
        getRowByEdaCode(edaCode, callback) {
            let rowByEdaCode = this.$refs.edaList.getRowByEdaCode(edaCode);
            callback(rowByEdaCode);
            return rowByEdaCode;
        },
        initData() {
            this.data.nodeList = [];
            this.data.lineList = [];
            this.$nextTick(() => {
                this.jsPlumb = jsPlumb.getInstance();
                this.$nextTick(() => {
                    this.jsPlumbInit();
                });
            })
        },
        changeVal(node) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            vm.data.nodeList.forEach(item => {
                if (item.id === node.id) {
                    let stepVO = JSON.stringify({
                        id: node.id,
                        xcoordinate: parseInt(item.left), // x轴
                        ycoordinate: parseInt(item.top), // y轴
                        name: node.name,
                        code: item.code,
                        udfType: item.type,
                        udfOperatorId: item.udfOperatorId,
                        graphId: item.graphId,
                        value: item.value,
                        dataType: item.dataType,
                        var: item.var
                    });
                    services.saveOrUpdateUdfNode(stepVO).then(result => {
                        if (result.data.status === 0) {
                            this.$message.success('算子名称修改成功');
                            item.name = node.name;
                        }
                    })
                }
            })
        },
        getRuleNode(node) {
            this.menuNode = node;
            this.canDrop = true;
        },
        isAllow() {
            this.canDrop = false;
        },
        onGetRule() {
            globalBus.$on('ruleDragend', this.isAllow);
            globalBus.$on('ExcavateRuleNode', this.getRuleNode);
        },
        allowDrop(e) {
            if (this.canDrop) {
                e.preventDefault();
            }
        },
        drop(e) {
            if (!this.easyFlowVisible) return;
            this.addNode(e, this.menuNode);
        },
        jsPlumbInit() {
            const vm = this;
            // let services = vm.getServices(pluginServices, pluginMock);
            vm.jsPlumb.ready(function () {
                // 导入默认配置
                vm.jsPlumb.importDefaults(vm.jsplumbSetting);
                // 会使整个jsPlumb立即重绘。
                vm.jsPlumb.setSuspendDrawing(false, true);
                // 初始化节点
                vm.loadEasyFlow();

                // 双点击了连接线,
                vm.jsPlumb.bind('dblclick', function (conn, originalEvent) {
                    clearTimeout(vm.timer);
                    vm.confirm('提示', '确定删除所点击的线吗?', () => {
                        vm.deleteNodeWithLine = false;
                        vm.jsPlumb.deleteConnection(conn)
                    })
                });
                //单击修改名称
                vm.jsPlumb.bind('click', (conn, originalEvent) => {
                    clearTimeout(vm.timer);
                    vm.timer = setTimeout(() => {
                        let fromId = conn.sourceId, toId = conn.targetId;
                        let line = vm.getLinesData(fromId, toId);
                        vm.setLineLabelData(fromId, toId, line.value, line.id);
                    }, 300)
                });

                // 连线
                vm.jsPlumb.bind("connection", function (evt) {
                    vm.edgEvt = evt;
                    let fromId = evt.source.id;
                    let toId = evt.target.id;
                    //请求获取到 连线名的下拉框数据 lineLabelOptions ，如果已经请求过，有值可以不用再请求
                    vm.setLineLabelData(fromId, toId, "", "");

                });

                // 删除连线
                vm.jsPlumb.bind("connectionDetached", function (evt) {
                    vm.deleteLine(evt.sourceId, evt.targetId)
                });

                // 改变线的连接节点
                vm.jsPlumb.bind("connectionMoved", function (evt) {
                    vm.changeLine(evt.originalSourceId, evt.originalTargetId)
                });


                // contextmenu
                vm.jsPlumb.bind("contextmenu", function (evt) {

                });


                // beforeDrop
                vm.jsPlumb.bind("beforeDrop", function (evt) {
                    let from = evt.sourceId;
                    let to = evt.targetId;
                    if (from === to) {
                        vm.$message.error('不能连接自己');
                        return false;
                    }
                    let sourceType = vm.getTargetType(from);
                    if (sourceType === '0' && vm.limitLines(2, from, 'from')) {
                        vm.$message.error('判断限制两条输出');
                        return false;
                    }
                    for (let j in vm.data.nodeList) {
                        let type = vm.data.nodeList[j].type;
                        if (vm.data.nodeList[j].id === to && type !== "UDF_OPERATOR") {
                            vm.$message.error('变量、常量不能输入!');
                            return false;
                        }
                    }

                    for (let i = 0; i < vm.data.lineList.length; i++) {
                        let line = vm.data.lineList[i];
                        if (line.from === from && line.to === to) {
                            vm.$message.error('不能重复连线');
                            return false;
                        }
                        if (line.from === to && line.to === from) {
                            vm.$message.error('不能回环哦');
                            return false;
                        }
                    }

                    let node = vm.data.nodeList.filter(l => l.id === to)[0];
                    let f_node = vm.data.nodeList.filter(l => l.id === from)[0];
                    let opts = vm.lineLabelOptions[node.udfOperatorId].params;
                    let sameLine = vm.data.lineList.filter(l => l.to === to);
                    if (node.type === "UDF_OPERATOR") {
                        //参数个数限制
                        if (opts.length <= sameLine.length) {
                            vm.$message.error('暂无可输入参数!');
                            return false;
                        }
                    }
                    let selectedParams = [];
                    sameLine.forEach(it => {
                        selectedParams.push(it.value);
                    });
                    //参数类型限制
                    let typeInx = 0;
                    vm.paramsInx = 0;
                    let params_cur = opts.filter(par => selectedParams.indexOf(par.value) === -1);
                    for (let i = 0; i < params_cur.length; i++) {
                        let item = params_cur[i];
                        let aceptT = vm.typeStrs[item.dataType.toLowerCase()] || [item.dataType];
                        if (aceptT.indexOf(vm.titleCase(f_node.dataType)) > -1) {
                            typeInx = i;
                            break;
                        }
                    }
                    vm.paramsInx = typeInx;
                    let paramsType = params_cur[typeInx].dataType;
                    let aceptType = vm.typeStrs[paramsType.toLowerCase()] || [paramsType];
                    let fromT = vm.titleCase(f_node.dataType);
                    if (aceptType.indexOf(fromT) === -1) {
                        vm.$message.error(`输入类型不匹配,请输入 ${aceptType.join()}类型`);
                        return false;
                    }
                    return true;
                });
                // beforeDetach
                vm.jsPlumb.bind("beforeDetach", function (evt) {

                })
            })
        },
        titleCase(str) {
            let newStr = str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase();
            return newStr;
        },
        limitLines(n, id, line) { // n 限制输出/输入连线数量
            let lineN = 0;
            this.data.lineList.forEach(l => {
                if (l[line] === id) {
                    lineN++;
                }
            });
            return lineN >= n;
        },
        getLinesData(from, to) {
            const vm = this;
            let lineD;
            for (let i = 0; i < vm.data.lineList.length; i++) {
                let line = vm.data.lineList[i];
                if (line.from === from && line.to === to) {
                    lineD = line;
                    break;
                }
            }
            return lineD;
        },
        getSelectedParams(toId) {
            const vm = this;
            let list = vm.data.lineList, params = [];
            for (let i = 0; i < list.length; i++) {
                if (list[i].to === toId) {
                    params.push(list[i].value);
                }
            }
            return params;
        },
        reqLineOptions(udfs) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            udfs.forEach(udf => {
                services.getUdfOperatorById(udf.id).then(res => {
                    if (res.data.status === 0) {
                        let linelabels = [];
                        let result = res.data.data;
                        result.udfParameterVos.forEach(item => {
                            linelabels.push({
                                label: item.name,
                                value: item.code,
                                dataType: item.dataType
                            })
                        });
                        vm.lineLabelOptions[udf.id] = {
                            params: linelabels,
                            returnType: result.returnType
                        };
                    }
                })
            })
        },
        setLineLabelData(fromId, toId, value, id) {
            const vm = this;
            let line = vm.getLinesData(fromId, toId);
            let node = vm.data.nodeList.filter(l => l.id === toId)[0];
            let opts = JSON.parse(JSON.stringify(vm.lineLabelOptions[node.udfOperatorId].params));
            let lineLabels = [];
            let params = vm.getSelectedParams(toId);
            opts.forEach(o => {
                if (params.indexOf(o.value) > -1) {
                    o.disabled = true;
                } else {
                    lineLabels.push(o);
                }
            });
            if (vm.loadEasyFlowFinish && !line && lineLabels.length) {
                const {paramsInx} = this;
                vm.data.lineList.push({
                    id: "",
                    from: fromId,
                    to: toId,
                    label: lineLabels[paramsInx].label,
                    value: lineLabels[paramsInx].value,
                    graphId: vm.row.serviceOrgId
                });
                let lines = vm.jsPlumb.getConnections({
                    source: fromId,
                    target: toId
                });
                lines[0].setLabel({
                    label: lineLabels[paramsInx].label,
                    cssClass: 'ce-label_class'
                });
                vm.$nextTick(() => {
                    vm.changeLabel(fromId, toId, lineLabels[paramsInx].value, "", opts);
                });
            } else {
                vm.$nextTick(() => {
                    vm.changeLabel(fromId, toId, value, id, opts);
                });
            }

        },
        getServiceId(id) {
            return this.data.nodeList.filter(node => {
                return node.id === id;
            })[0].serviceId;
        },
        getTargetType(id) {
            return this.data.nodeList.filter(node => {
                return node.id === id;
            })[0].type;
        },
        checkOneRoad(from, to) {
            let lineArr = [];
            this.data.lineList.forEach(l => {
                if (lineArr.indexOf(l.from) === -1) {
                    lineArr.push(l.from);
                }
                if (lineArr.indexOf(l.to) === -1) {
                    lineArr.push(l.to);
                }
            });
            return lineArr.indexOf(to) === -1 && lineArr.indexOf(from) === -1;
        },


        // 加载流程图
        loadEasyFlow() {
            // 初始化节点
            for (var i = 0; i < this.data.nodeList.length; i++) {
                let node = this.data.nodeList[i];
                // 设置源点，可以拖出线连接其他节点
                this.jsPlumb.makeSource(node.id, this.jsplumbSourceOptions);
                // // 设置目标点，其他源点拖出的线可以连接该节点
                this.jsPlumb.makeTarget(node.id, this.jsplumbTargetOptions);
                // jsPlumb.addEndpoint(node.id)
                // 设置可拖拽
                // jsPlumb.draggable(node.id, {
                //     containment: 'parent',
                //     grid: [10, 10]
                // })

                this.jsPlumb.draggable(node.id, {
                    containment: 'parent'
                })

                // jsPlumb.draggable(node.id)
            }
            // 初始化连线
            for (let i = 0; i < this.data.lineList.length; i++) {
                let line = this.data.lineList[i];
                let lineC = this.jsPlumb.connect({
                    source: line.from,
                    target: line.to,
                }, this.jsplumbConnectOptions);
                lineC.setLabel({
                    label: line.label,
                    cssClass: 'ce-label_class'
                });
            }
            this.$nextTick(function () {
                this.loadEasyFlowFinish = true;
            });
        },
        getNodes() {

        },
        getLines() {

        },
        // 删除线
        deleteLine(fromId, to) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            let line = vm.getLinesData(fromId, to);
            services.deleteUdfEdgeById(line.id).then(res => {
                if (res.data.status === 0) {
                    vm.data.lineList = vm.data.lineList.filter(li => !(li.from === fromId && li.to === to));
                    if (!vm.deleteNodeWithLine) {
                        vm.$message.success("算子连线删除成功!");
                    }
                }
            })
        },
        // 改变连线
        changeLine(oldFrom, oldTo) {
            this.deleteLine(oldFrom, oldTo);
        },
        // 改变节点的位置
        changeNodeSite(data) {
            const vm = this, {pluginServices, pluginMock} = this;
            vm.data.nodeList.forEach(node => {
                if (node.id === data.nodeId) {
                    let x = parseInt(data.left.replace('px', ''));
                    let y = parseInt(data.top.replace('px', ''));
                    node.left = data.left;
                    node.top = data.top;
                    let stepVO;
                    stepVO = JSON.stringify({
                        id: node.id,
                        xcoordinate: x, // x轴
                        ycoordinate: y, // y轴
                        name: node.name,
                        code: node.code,
                        udfType: node.type,
                        udfOperatorId: node.udfOperatorId,
                        graphId: node.graphId,
                        value: node.value,
                        dataType: node.dataType,
                        var: node.var
                    });
                    let services = vm.getServices(pluginServices, pluginMock);
                    services.saveOrUpdateUdfNode(stepVO);
                }
            });
        },
        setNodeName(name, index, nodeMenu) {
            const vm = this;
            let nodeName = name;
            while (vm.allNodesName.includes(name)) {
                index++;
                nodeName = nodeMenu.label + '_' + index;
            }
            vm.allNodesName.push(nodeName);
            return nodeName;
        },
        setNodeValue(nodeMenu, val) {
            let value = "", label = "", data_type = "";
            if (nodeMenu.udfType === "VARIABLE_OPERATOR") {
                let field = this.sourceField.filter(so => so.value === val);
                if (val !== "" && val && field.length) {
                    value = val;
                    label = field[0].label;
                } else if (val === "") {
                    value = this.sourceField[0].value;
                    label = this.sourceField[0].label;
                    data_type = this.sourceField[0].dataType;
                } else {
                    this.variableNodes.push(nodeMenu)
                }

            } else {
                if (val !== "") {
                    value = label = val;
                } else {
                    value = "";
                    label = "";
                }

            }
            return {value, label, data_type};
        },
        // 添加新的节点
        addNode(evt, nodeMenu) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock),
                index = 1,
                x = evt.offsetX - 80,
                y = evt.offsetY - 25,
                nodeName = nodeMenu.label + '_' + index;
            while (vm.allNodesName.includes(nodeName)) {
                index++;
                nodeName = nodeMenu.label + '_' + index;
            }
            vm.allNodesName.push(nodeName);
            let {value, label, data_type} = vm.setNodeValue(nodeMenu, "");
            let udfOperatorId = "", dataType;
            let isVar = false;
            if (nodeMenu.udfType === "UDF_OPERATOR") {
                udfOperatorId = nodeMenu.id;
                dataType = vm.lineLabelOptions[udfOperatorId].returnType;
                vm.$refs.instructions.show(udfOperatorId);
            } else if (nodeMenu.udfType === "VARIABLE_OPERATOR") {
                dataType = data_type;
                isVar = true;
            } else {
                dataType = "String";
            }
            let stepVO = JSON.stringify({
                name: nodeName, // 步骤名称
                xcoordinate: x, // x轴
                ycoordinate: y, // y轴
                code: nodeName,
                value,
                udfType: nodeMenu.udfType,
                udfOperatorId,
                id: "",
                graphId: vm.row.serviceOrgId,
                dataType: dataType,
                var: isVar
            });

            if (!vm.sourceField.length) {
                vm.$message.error("请先配置输入表");
                return;
            }
            services.saveOrUpdateUdfNode(stepVO).then(result => {
                if (result.data.status === 0) {
                    let res = result.data.data;
                    vm.data.nodeList.push({
                        id: res.id,
                        code: res.code,
                        name: res.name,
                        left: x + 'px',
                        top: y + 'px',
                        showType: nodeMenu.code === "CONSTANT_OPERATOR" || nodeMenu.code === "VARIABLE_OPERATOR" ? nodeMenu.code : nodeMenu.codeType,
                        type: nodeMenu.udfType,
                        show: true,
                        graphId: res.graphId,
                        dataType: dataType,
                        udfOperatorId,
                        value,
                        label,
                        var: isVar
                    });
                    vm.$nextTick(() => {
                        vm.jsPlumb.makeSource(res.id, vm.jsplumbSourceOptions);
                        vm.jsPlumb.makeTarget(res.id, vm.jsplumbTargetOptions);
                        vm.jsPlumb.draggable(res.id, {
                            containment: 'parent'
                        })
                    })
                }
            })
        },

        nodeRightMenu(nodeId, evt) {
            this.menu.show = true;
            this.menu.curNodeId = nodeId;
            this.menu.left = evt.x + 'px';
            this.menu.top = evt.y + 'px'
        },

        // 删除方案步骤
        deleteNode(selectedNode) {
            let nodeName = selectedNode.name;
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            this.confirm('删除', '确定要删除算子节点[' + nodeName + ']?', () => {
                services.deleteUdfNodeById(selectedNode.id).then(res => {
                    if (res.data.status === 0) {
                        this.deleteEDANode(selectedNode.id);
                        vm.data.nodeList = vm.data.nodeList.filter(node => {
                            if (node.id === selectedNode.id) {
                                return false;
                            }
                            return node.id !== selectedNode.id;
                        });
                        vm.deleteNodeWithLine = true;
                        vm.data.lineList = vm.data.lineList.filter(node => !(node.from === selectedNode.id || node.to === selectedNode.id));
                        vm.jsPlumb.removeAllEndpoints(selectedNode.id);
                        vm.closeParamsPanel(selectedNode.id);
                        vm.$message.success("算子节点删除成功!");
                    }
                });
            });
        },
        /**
         * 删除变量节点
         * */
        sourceRemove(selectedNode) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            services.deleteUdfNodeById(selectedNode.id).then(res => {
                if (res.data.status === 0) {
                    vm.deleteEDANode(selectedNode.id);
                    vm.data.nodeList = vm.data.nodeList.filter(node => {
                        if (node.id === selectedNode.id) {
                            return false;
                        }
                        return node.id !== selectedNode.id;
                    });
                    vm.deleteNodeWithLine = true;
                    vm.data.lineList = vm.data.lineList.filter(node => !(node.from === selectedNode.id || node.to === selectedNode.id));
                    vm.jsPlumb.removeAllEndpoints(selectedNode.id);
                }
            });
        },
        /**
         * 清空变量节点
         * */
        clearVariableNodes() {
            const vm = this;
            if (vm.variableNodes.length) {
                vm.$message.warning("输入源更换,变量节点即被清空");
                vm.variableNodes.forEach(vari => {
                    vm.sourceRemove(vari);
                })
            }

        },
        closeParamsPanel(nodeId) { //关闭对应的入参窗口
            globalBus.$emit('closeParamsPanel', nodeId);
        },

        editNode(nodeId) {
            this.nodeFormVisible = true;
            this.$nextTick(function () {
                this.$refs.nodeForm.init(this.data, nodeId)
            });
        },

        // 初始化JsPlumb，获取后台数据
        async dataReload() {
            const vm = this, {pluginServices, pluginMock, loadParams } = this;
            let services = vm.getServices(pluginServices, pluginMock);
            vm.easyFlowVisible = false;
            vm.data.nodeList = [];
            vm.data.lineList = [];
            let vo = {
                name: vm.row.columnName,
                code: vm.row.columnCode,
                serviceOrgId: vm.row.serviceOrgId,
            };
            loadParams.loading = true;
            try {
                await vm.getSourceField();
                if (!vm.row.serviceOrgId) {
                    await services.saveOrUpdateUdfGraph(vo).then(res => {
                        if (res.data.status === 0) {
                            let result = res.data.data;
                            vm.row.serviceOrgId = result.id;
                            vm.easyFlowVisible = true;
                            vm.data.nodeList = [];
                            vm.data.lineList = [];
                            vm.$nextTick(() => {
                                vm.jsPlumb = jsPlumb.getInstance();
                                vm.$nextTick(() => {
                                    vm.jsPlumbInit();
                                })
                            })
                        }
                    })
                } else {
                    await services.getUdfGraphById(vm.row.serviceOrgId, vm.sourceData.id).then(res => {
                        if (res.data.status === 0) {
                            let result = res.data.data;
                            vm.easyFlowVisible = true;
                            if (result.udfNodeVoList != null) {
                                let nodes = result.udfNodeVoList;
                                for (let key in nodes) {
                                    let item = nodes[key];
                                    vm.allNodesName.push(item.name);
                                    let {label, value} = vm.setNodeValue(item, item.value);
                                    let node = {
                                        code: item.code,
                                        left: item.xcoordinate + 'px',
                                        top: item.ycoordinate + 'px',
                                        showType: item.showType,
                                        id: item.id,
                                        name: item.name,
                                        show: true,
                                        type: item.udfType,
                                        udfOperatorId: item.udfOperatorId || "",
                                        graphId: item.graphId,
                                        value,
                                        dataType: item.dataType,
                                        label,
                                        var: item.var
                                    };
                                    vm.data.nodeList.push(node);
                                }
                                let edges = result.udfEdgeVoList;
                                for (let k in edges) {
                                    let itemv = edges[k];
                                    let line = {
                                        from: itemv.outNodeId,
                                        to: itemv.inNodeId,
                                        id: itemv.id,
                                        label: itemv.name,
                                        value: itemv.code,
                                        graphId: itemv.graphId
                                    };
                                    vm.data.lineList.push(line);
                                }
                            }
                            vm.$nextTick(() => {
                                vm.jsPlumb = jsPlumb.getInstance();
                                vm.$nextTick(() => {
                                    vm.jsPlumbInit();
                                    vm.clearVariableNodes();
                                })
                            })
                        }
                    })
                }
            } finally {
                loadParams.loading = false;
            }

        },
        /**
         *
         * @param fromId
         * @param toId
         * @param name 线名称
         * @param id 线id
         * @param opts 线关系选项
         */
        changeLabel(fromId, toId, name, id, opts) { //type 为 ‘0’ ，即为判断输出
            const vm = this;
            vm.$nextTick(() => {
                vm.$refs.lineLabel.show(name, opts, fromId, toId, id, vm.row);
            })
        },
        changeLogicalLabel(fromId, toId, label, value, id) {
            const vm = this;
            vm.data.lineList.forEach(line => {
                if (line.from === fromId && line.to === toId) {
                    let lines = vm.jsPlumb.getConnections({
                        source: line.from,
                        target: line.to
                    });
                    line.label = label;
                    line.value = value;
                    line.id = id;
                    lines[0].setLabel({
                        label: label,
                        cssClass: 'ce-label_class'
                    });
                }
            })
        },
        changeLabelFn(fromId, toId, data, id) { //data => label ,value
            this.changeLogicalLabel(fromId, toId, data.label, data.value, id);
        },
        getAttr(result) {//获取

        },
        pushEdaTaskListTable(table) {
            this.$emit('pushEdaTaskListTable', table);
        },
        deleteEdaTaskListTable(stepId, edaId) {
            this.$emit('deleteEdaTaskListTable', stepId, edaId);
        },
        deleteEDANode(nodeId) {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            services.deleteEDANodeByNodeId(nodeId);
        },
        async getSourceField() {
            const vm = this, {pluginServices, pluginMock} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            let type = this.sourceData.keyWord;
            await services.queryPrevNodeVariables(vm.sourceData.id, type).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    result.forEach((vara) => {
                        vm.sourceField.push({
                            value: vara.code,
                            label: vara.name,
                            dataType: vara.dataType,
                            show: true,
                        });
                    });
                    if (!vm.sourceField.length) {
                        vm.$message.error("请先配置输入表");
                    }
                }
            });
        }
    },
    created() {
        this.onGetRule();
        this.dataReload();
    },
    destroyed() {
        globalBus.$off('ExcavateRuleNode', this.getRuleNode);
        globalBus.$off('ruleDragend', this.isAllow);
    }
}
</script>

<style scoped>
.flowPanelEt {
    margin-right: auto;
    transition: 300ms;
    height: 100%;
    background-color: #f5f5f5;
}
</style>
<style>
/*.ce-plug_field .flow-node-header {*/
/*background-color: #70ad47;*/
/*}*/

.ce-plug_field .flow-node-body {
    border-color: #70ad47;
}

/*.ce-plug_field .flow-shadow:hover {*/
/*box-shadow: #70ad47 0 0 12px 0;*/
/*}*/

.ce-plug_field .m-field {
    color: #70ad47;
}

/*.ce-plug_field.node:hover .flow-name, .ce-plug_field.node:hover .eda_icon {*/
/*color: #70ad47;*/
/*}*/

/*.ce-plug_constant .flow-node-header {*/
/*background-color: #86a6e4;*/
/*}*/

.ce-plug_constant .flow-node-body {
    border-color: #86a6e4;
}

.ce-plug_constant .m-field {
    color: #86a6e4;
}

/*.ce-plug_constant .flow-shadow:hover {*/
/*box-shadow: #86a6e4 0 0 12px 0;*/
/*}*/

/*.ce-plug_constant.node:hover .flow-name, .ce-plug_constant.node:hover .eda_icon {*/
/*color: #86a6e4;*/
/*}*/

.judgement .flow-node-header {
    background: #FFBB54;
}

.judgement .flow-node-body {
    border-color: #FFBB54;
}

.ce-label_class {
    background: #f4f4f4;
    padding: 2px 5px;
}
</style>
