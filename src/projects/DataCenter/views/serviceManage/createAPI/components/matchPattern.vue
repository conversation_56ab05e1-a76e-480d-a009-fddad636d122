<template>
    <common-dialog
            :title="title"
            width="1000px"
            height="60%"
            :visible.sync="visible"
            @closed="close"
            v-loading="settings.loading"
    >
        <div class="ce-filter" :class="{'ce-disabled_model' : isDetail }">
            <filter-plugin :filter-columns="filterColumns" ref="filter"
                           plug-type="service"
                           @change="filterChange"
            ></filter-plugin>
        </div>
        <div slot="footer">
            <el-button size="mini" @click="close" v-if="!isDetail">取消</el-button>
            <el-button  size="mini" @click="save" v-if="!isDetail">保存</el-button>
        </div>
    </common-dialog>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import filterPlugin from "./filterPlugin"
    export default {
        name: "matchPattern",
        components : {filterPlugin},
        mixins: [ commonMixins],
        data() {
            return {
                conditionInfo: [],
                filterColumns: [],
                filterInfo: [],
                isDetail: false,
                title:'',
                visible: false,
            }
        },
        methods: {
            /**
             * 条件更新
             * */
            filterChange(condition){
                this.conditions = condition;
            },
            close(){
                this.visible = false;
            },
            validate(){
                const vm = this;
                let valid = true;
                valid = vm.$refs.filter.validate();
                if(!valid) return;
                let conditionInfo = vm.$refs.filter.getConditions();
                if(!conditionInfo.length){
                    vm.$message.warning("请先添加配置条件");
                    return false;
                }
                let list = [];
                conditionInfo.forEach(n=>{
                    list = list.concat(n.children);
                })
                let arr = list.map(n=>n.field);
                if(new Set(arr).size !== arr.length){
                    vm.$message.warning("匹配模式字段不能重复");
                    return false;
                }
                let mustList = vm.filterColumns.filter(n=>n.isMust).map(p=>p.paramCode);
                for(let t = 0; t < mustList.length ; t++ ){
                    if(!arr.includes(mustList[t])){
                        vm.$message.warning(`请求参数 ${mustList[t]} 为必填字段，需设置匹配模式`);
                        return false;
                    }
                }
                return true;
            },
            save(){
                const vm = this;
                let valid = vm.validate();
                if(!valid) return;
                let conditionInfo = vm.$refs.filter.getConditions();
                let fieldCodeList = [];
                conditionInfo.forEach(n=>{
                    let arr =  n.children.map(p=>p.field);
                    fieldCodeList = fieldCodeList.concat(arr);
                })
                vm.setEncasulationJudgContion(conditionInfo);
                vm.$emit('saveConditionInfo',vm.filterInfo, fieldCodeList);
                vm.close();
            },
            setEncasulationJudgContion(conditionInfo) {
                const vm = this;
                vm.filterInfo = []
                for (let i = 0; i < conditionInfo.length; i++) {
                    let judgCondition = [];
                    conditionInfo[i].children.forEach(judg => {
                        let [condition, conditionType] = judg.condition.split('|');
                        let value = judg.value;
                        let otherTimeType = judg.defaultType.toLowerCase() === 'time';
                        let startTime, endTime = judg.valueEnd;
                        if (judg.valueType === 'constValue') {
                            if (otherTimeType) judg.defaultType = 'Timestamp';
                            if ((judg.defaultType === 'Timestamp' || otherTimeType)) {
                                value = judg.value.valueOf() / 1000;
                            } else if (['Date', 'date'].includes(judg.defaultType)) {
                                value = judg.value ? new Date(judg.value).Format(vm.dateFormat) : "";
                                //value = judg.value ? new Date(judg.value).Format(vm.dateFormat) :  "";
                            }

                            if (condition === "in" && (judg.defaultType === 'Timestamp' || otherTimeType)) {
                                condition = "between";
                            } else if (condition === "not in" && (judg.defaultType === 'Timestamp' || otherTimeType)) {
                                condition = "not between";
                            }
                            if ((judg.defaultType === 'Timestamp' || otherTimeType)) {
                                startTime = judg.value.valueOf() / 1000;
                                endTime = judg.valueEnd.valueOf() / 1000;
                            } else if (judg.defaultType === 'Date') {
                                startTime = judg.value ? new Date(judg.value).Format(vm.dateFormat) : "";
                                endTime = judg.valueEnd ? new Date(judg.valueEnd).Format(vm.dateFormat) : "";
                            }
                        }
                        judgCondition.push({
                            field: judg.field,
                            condition: condition,
                            value: value,
                            fieldType: judg.defaultType,
                            conditionType: conditionType,
                            valueType: judg.valueType,
                            startTime,
                            endTime
                        });
                    });

                    let outRelationContion = '';
                    if (i === conditionInfo.length - 1) {
                        outRelationContion = '';
                    } else {
                        outRelationContion = conditionInfo[i + 1].logic;
                    }

                    vm.filterInfo.push({
                        no: i,
                        relationCondition: conditionInfo[i].childrenLogic,
                        outRelationContion: outRelationContion,
                        judgCondition: JSON.stringify(judgCondition)
                    });
                }
            },
            initConditionsInfo(){
                const vm = this;
                let conditionInfo = []
                vm.filterColumns.forEach(p=>{
                    let con = vm.conditionInfo.find(n=>JSON.parse(n.judgCondition)[0].field == p.paramCode);
                    if(con){
                        con.no = conditionInfo.length;
                        let judgCondition = JSON.parse(con.judgCondition);
                        judgCondition[0].isMust = p.isMust;
                        con.judgCondition = JSON.stringify(judgCondition);
                        con.isMust = p.isMust;
                        conditionInfo.push(con)
                    }
                    else if(p.isMust && !con){
                        let judgCondition = [];
                        judgCondition.push({
                            field: p.paramCode,
                            condition: '=',
                            value: p.example || '',
                            fieldType: p.dataType,
                            conditionType: '等于',
                            valueType: 'constValue',
                            isMust: p.isMust
                        });
                        let obj = {
                            no: conditionInfo.length,
                            relationCondition: 'and',
                            outRelationContion: 'and',
                            isMust: p.isMust,
                            judgCondition: JSON.stringify(judgCondition)
                        }
                        conditionInfo.push(obj);
                    }
                })
                let currentConditions = vm.initCondition(conditionInfo);
                vm.$nextTick(()=>{
                    vm.$refs.filter.isResouceShow = vm.$refs.filter.isResouceShow + 1;
                    vm.$refs.filter.initCondition(currentConditions);
                })
            },
            //回填过滤条件
            initCondition(pluginMeta){
                const vm = this;
                if (pluginMeta){
                    let enJudgContion = pluginMeta.sort(vm.compare("no",true));
                    let currentConditions = [];
                    for (let i = 0; i < enJudgContion.length; i++) {
                        let {no} = enJudgContion[i];
                        let children = [];
                        let parse = JSON.parse(enJudgContion[i].judgCondition);
                        parse.forEach(judg => {
                            let {value , endTime ,valueType ,fieldType , condition ,conditionType } = judg;
                            if(valueType === 'constValue'){
                                if(fieldType === 'Timestamp'){
                                    value = value * 1000;
                                    if(condition === "between"){
                                        condition = "in";
                                        endTime = endTime * 1000;
                                    }else if(condition === "not between"){
                                        condition = "not in";
                                        endTime = endTime * 1000;
                                    }
                                }
                            }
                            let fieldD = vm.filterColumns.find(col =>  col.value === judg.field);
                            children.push({
                                field : judg.field,
                                condition : condition + '|' + conditionType,
                                condLabel: conditionType,
                                condValue: condition,
                                value : value,
                                defaultType : fieldD.dataType,
                                valueType  ,
                                valueEnd : endTime

                            });
                        });
                        let outRelationContion = '';
                        if (no === 0){
                            outRelationContion = 'and';
                        }else {
                            outRelationContion = enJudgContion[no-1].outRelationContion;
                        }

                        currentConditions.push({
                            no : no,
                            childrenLogic : enJudgContion[no].relationCondition,
                            logic :outRelationContion,
                            children : children,
                            isMust: enJudgContion[i].isMust
                        });
                    }
                    return currentConditions;
                }
            },
            show(requestTableData, conditionInfo, title, isDetail){
                const vm = this;
                vm.settings.loading = true;
                vm.title = title;
                vm.visible = true;
                vm.conditionInfo = conditionInfo;
                vm.filterColumns = requestTableData.map(n=>{
                    n.label = n.paramCode;
                    n.value = n.paramCode;
                    return n;
                });
                vm.initConditionsInfo();
                vm.settings.loading = false;
                vm.isDetail = isDetail;
            }
        },
    }
</script>

<style scoped lang="less">
    @mainColor: #0088ff;
    .ce-value_input {
        width: 220px;
    }

    .ce-value_type {
        width: 100px
    }

    .ce-opt_l {
        float: left;
    }

    .ce-opt_r {
        float: right;
        color: #8492a6;
    }

    .ce-filter {
        display: flex;
        align-items: stretch;
        flex-direction: column;
        min-width: 920px;

        &_group {
            color: @mainColor;
            display: flex;
            flex: 1;
            border: 1px solid #ddd;
            padding: 14px 18px;
            margin: 0 10px 24px 0;

        }

        &_logic {
            flex: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 16px;
            padding-top: 4px;

            &:after {
                flex: 1;
                content: "";
                width: 2px;
                background-color: rgba(@mainColor, 0.12);
            }
        }

        &_main {
            flex: 1;
        }

        &_header {
            display: flex;
            justify-content: space-between;

            .logic-select {
                margin: 0 12px;
            }
        }

        &_btns {
            margin-left: 14px;
            float: right;
        }

        &_form {
            display: flex;
        }

        &-item {
            margin-top: 14px;
            display: flex;
            background: #efefef;
            padding: 12px 14px;
            border-radius: 2px;
            justify-content: space-between;
        }
    }

    // 图标按钮组
    .filter-box {
        flex-grow: 1;

        /deep/ .el-card__body {
            position: relative;
            padding-top: 8px;
            padding-bottom: 0;
        }

        &__add {
            position: absolute;
            top: 8px;
            right: 32px;
            z-index: 1;
        }

    }

    .filter-item {
        display: flex;

        &__field,
        &__value {
            flex: none;
            width: 200px;
        }

        &__operator {
            flex: none;
            width: 180px;
            margin: 0 6px;
        }

        &__btns {
            margin-left: 14px;
            line-height: 1.875rem;

            .el-link {
                margin: 0 2px;
                font-size: 16px;
            }
        }

        &:not(:hover) &__btns {
            display: none;
        }
    }

    // 逻辑选择器样式
    .logic-select {
        width: 84px;

        /deep/ .el-input__inner {
            border-color: @mainColor;
            color: @mainColor;
        }

        /deep/ .el-input__icon {
            color: @mainColor;
        }

        &--item {
            background-color: rgba(@mainColor, 0.12);
        }

        &--thin {
            @thinHeight: 1.875rem;
            width: 42px;
            height: @thinHeight;
            line-height: @thinHeight;
            text-align: center;

            /deep/ .el-input__inner {
                padding: 0 12px 0 6px;
                background-color: rgba(@mainColor, 0.12);
                border: none;
                height: @thinHeight;
                line-height: @thinHeight;
            }

            /deep/ .el-input__suffix {
                right: 4px;
            }

            /deep/ .el-input__icon {
                width: 12px;
            }

        }
    }
</style>