<template>
    <div class="sourceTree">
        <h1 class="sourceTree_title mb5">{{sourceTi}}</h1>
        <ce-select-drop
                ref="selectTree"
                class="expect-select mb8"
                placeholder="请选择数据源"
                :props="defaultProps"
                filterable
                check-leaf
                check-strictly
                :tree-props="{
                    'default-expanded-keys' : [dataSource]
                }"
                :filterNodeMethod="filterNode"
                v-model="dataSource"
                @current-change="selectSchema"
                :data="dataSourceOpt"></ce-select-drop>
        <div class="sourceTree_dataset mb5">
            <h1 class="sourceTree_title">{{datasetTi}}</h1>
            <el-popover popper-class="ce-popover" v-model="showAddBox" trigger="click" width="96">
                <div class="ce-tree__pop-box">
                    <div class="ce-tree__pop-item" v-for="(menu , k) in sortMenu"
                         :class="{'active' : k === sortVal}"
                         @click="setTreeSort(k)" :key="k">
                        <i class="eda_icon" :class="menu.icon"></i>
                        <span>{{ menu.label }}</span>
                    </div>
                </div>
                <i slot="reference" class="eda_icon ce-sort_icon"
                   :title="sortVal === 'letter'? letterSort : timeSort"
                   :class="{'iconsort' : sortVal === 'letter' , 'iconshijianpaixutubiao' : sortVal === 'time' }"></i>
            </el-popover>
        </div>
        <el-input v-model.trim="search" v-input-limit:trim suffix-icon="el-icon-search" placeholder="请输入关键字搜索"/>
        <div class="sourceTree_tree">
            <dg-scrollbar native>
                <datasetList
                        ref="dataset"
                        :data="data"
                        v-on="$listeners"
                        :loadChildren="loadChildren"
                        @sortData="sortTreeData(sortVal)"
                ></datasetList>
            </dg-scrollbar>
        </div>
    </div>
</template>

<script src="./source-tree.js"></script>
<style scoped lang="less" src="./source-tree.less"></style>
