import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../service-mixins/service-mixins";

export default {
    name: "modelParams",
    mixins :[commonMixins , servicesMixins ],
    data(){
        return {
            tableData: [],
            checkList: [], //checkbox
            columns: [
                //指定表格列名
                {props: "code"},
                {props: "name"},
                {props: "type"}
            ],
            storeAllParams : [],
            storeAllParamsId : [],
            treeProps : {children: 'children'},
        }
    },
    props : {
        rowData :Object
    },
    methods :{
        getAllParams(){
            const vm = this , {checkList , storeAllParamsId , storeAllParams} = this;
            let paramsIds = checkList.filter(li => storeAllParamsId.indexOf(li) >-1);
            if(paramsIds.length){
                let params = [];
                storeAllParams.forEach(par =>{
                    if(paramsIds.indexOf( par.id) > -1){
                        params.push({
                            defaultValue : par.defaultValue,
                            isMust : par.isMust ,
                            paramCode : par.paramCode ,
                            paramName : par.paramName ,
                            paramValue : par.paramValue ,
                            type : par.type
                        })
                    }
                });
                return params;
            }else {
                return [];
            }
        },
        init(){
            const vm = this , {modelingServices , modelingMock ,settings , rowData } = this;
            let services = vm.getServices(modelingServices , modelingMock);
            settings.loading = true;
            vm.tableData = [];
            services.getModelServicePublishConfig(rowData.transId , settings).then(res =>{
                if(res.data.status === 0){
                    let result = res.data.data.sort(vm.compareEng('pluginType' , true));
                    result.forEach(par => {
                        if(par.paramList){
                            let childData = vm.setChildrenData(par.paramList , par.transId);
                            let type = par.pluginType === 'INPUT' ? '(输入插件)' :  par.pluginType === 'OUTPUT' ? '(输出插件)' : '';
                            let item = {
                                id : par.transId ,
                                code : par.transName + type ,
                                children : childData
                            };
                            vm.tableData.push(item);
                        }
                    })
                }
            })
        },
        setChildrenData(data , transId){
            const vm = this;
            return data.map(child => {
                let nameArr = child.paramName.split('-_-') , codeArr = child.paramCode.split('-_-') ,
                    name = nameArr[nameArr.length - 1] , code = codeArr[codeArr.length -1];
                child.name = name === 'null' ?  code : name;
                child.code = code;
                child.transId = transId;
                child.id = (code === 'sql' || code === 'script')? child.paramId +'_'+ transId +'_sql'  :  child.paramId +'_'+ transId;
                vm.storeAllParamsId.push(child.id);
                vm.storeAllParams.push(child);
                return child;
            })
        },
        checkChildren(val ,row){
            const vm = this , {checkList} = this;
            if(row.children && row.children.length){
                vm.pushChildId(row.children , val);
            }

            if(val && row.transId){
                if(row.code === 'sql' || row.code === 'script'){
                    vm.checkList = checkList.filter(item => item.indexOf(row.transId) === -1);
                    vm.checkList.push(row.id);
                }else {
                    vm.checkList = checkList.filter(item => item.indexOf(row.transId + '_sql') === -1 );
                }
            }

        },
        pushChildId(data ,val){
            const vm = this ;
            data.forEach(item =>{
                if(val){
                    if(item.code === 'sql' || item.code === 'script'){
                        vm.checkList = vm.checkList.filter(list => list !== item.id);
                    }
                    if( (item.code !== 'sql' && item.code !== 'script') && vm.checkList.indexOf(item.id) === -1){
                        vm.checkList.push(item.id);
                    }
                }else {
                    vm.checkList = vm.checkList.filter(list => list !== item.id);
                }
                if(item.children && item.children.length){
                    vm.pushChildId(item.children , val);
                }
            });
            vm.checkList = vm.checkList.filter(list =>  list.indexOf(data.id + '_sql') === -1);
        },
        /**
         * @effect: 合并列 计算方法
         * @param {Object} row 行数据
         */
        mergeSpanMethod({ row }) {
            const { columns } = this;
            let rowSpanAry = columns.filter((v) => {
                if (Object.keys(row).includes(v.props)) {
                    return 1;
                }
            });
            if (rowSpanAry.length === 1) {
                return {
                    rowspan: 1,
                    colspan: 3
                };
            }
            if (rowSpanAry.length === 2) {
                return {
                    rowspan: 1,
                    colspan: 2
                };
            }
            return {
                rowspan: 1,
                colspan: 1
            };
        },
        setRowClassName({ row }) {
            let flag = this.onJudge(row);
            if (flag) {
                if (row.isHead) {
                    return "row-head-table";
                }
                return "row-table";
            }
            return "row-children";
        },
        setColClassName({ row }) {
            let flag = this.onJudge(row);
            if (flag) {
                return "has-table";
            }
            return "has-children";
        },
        /**
         * @effect: 判断是否添加类名
         * @param {Object} row 当前行数据
         */
        onJudge(row) {
            return Object.keys(row).every((key) => {
                if (key !== "id" || key !== "children") {
                    return !!(Object.keys(row).length > 3 && row[key]);
                }
                return true;
            });
        },
    },
    created(){
        this.init();
    }
}