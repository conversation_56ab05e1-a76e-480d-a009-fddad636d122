<template>
    <common-dialog custom-class="publishDia" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="close"
                   v-loading="settings.loading"
    >
        <div>
            <el-form ref="form" :model="form" label-width="120px" :rules="rules" v-if="visible">
                <el-form-item label="评分："  required prop="score">
                    <el-rate v-model="form.score" allow-half>
                    </el-rate>
                </el-form-item>
                <el-form-item label="评论：" required prop="operate_content">
                    <el-input type="textarea" resize="none" :placeholder="placeholder" v-model="form.operate_content" :autosize="{ minRows: 5, maxRows: 8}" maxlength="500" show-word-limit></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @click="close" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button @click="submit" type="primary" size="mini">{{ submitTxt }}</el-button>
        </div>
    </common-dialog>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins";
    import {servicesMixins} from "../service-mixins/service-mixins";
    export default {
        name: "evaluation",
        mixins : [  commonMixins, servicesMixins],
        data(){
            let validateScore = (rule, value, callback) => {
                if (value === 0) {
                    callback(new Error('请选择评分！'));
                } else {
                    callback();
                }
            };
            return{
                btnCancelTxt:'取消',
                submitTxt:'提交',
                width: '800px',
                title:'评价',
                visible:false,
                placeholder:'写点评价吧，您的评价对其他用户很有帮助！',
                form:{
                    id : "",
                    score:0,
                    operate_content:''
                },
                rules:{
                    score: [{ required: true, validator: validateScore, trigger: 'blur' }],
                    operate_content: [{ required: true, message: '请输入评论', trigger: 'blur' }],
                },
                dataInfo:{}
            }
        },
        methods:{
            show(row){
                this.dataInfo = row;
                this.form = JSON.parse(JSON.stringify(row.modelEvaluation));
                this.form.id = row.id;
                this.visible = true;
            },
            validate(){
                let flag = true;
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                    } else {
                        flag = false;
                        return false;
                    }
                })
                return flag;
            },
            submit(){
                const vm = this;
                if(vm.validate()){
                    const vm = this, {manageServices, manageMock } = this;
                    let services = vm.getServices(manageServices, manageMock);
                    let data = {
                        score: this.form.score,
                        operateContent: this.form.operate_content,
                        id: this.form.id
                    }
                    services.evaluationModel(data).then(res=>{
                        if (res.data.status === 0) {
                            vm.$message.success('评价成功！');
                            this.visible = false;
                            this.$emit("save");
                        }
                    })
                }
            },
            close(){
                this.form = {
                    score: 0,
                    operate_content:''
                }
                this.visible = false;
            },
        }
    }
</script>

<style scoped>

</style>