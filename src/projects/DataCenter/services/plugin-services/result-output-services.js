/**
 * 图表展示插件 接口
 * @Author: wangjt
 * @Date: 2020-11-13
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const bar_root = "/barChartsPlugin/";
/*
* 图表 插件初始化
* @params
* @returns {Promise<*>}
* */

export async function getChartsPluginPage( tranStepId , settings , url) {
    return await reqUtil.get({
        url , settings
    },{tranStepId})
}

/*
* 图表 插件保存
* @params
* @returns {Promise<*>}
* */

export async function saveChartsPlugin( tranStepId , title , showType , columnCode , bmCode , url  , settings , groupBmCode , groupColumn , dimension ) {
    let params;
    if(url.indexOf("PieChartsPlugin") > -1){
        params = {tranStepId , title , chartsType : showType , columnCode , bmCode };
    }else {
        params = {tranStepId , title , chartsType : showType , columnCode , bmCode , groupBmCode , groupColumn , dimension}
    }
    return await reqUtil.get({
        url, settings
    },params)
}
/*
* 图表 插件预览
* @params
* @returns {Promise<*>}
* */

export async function chartsShowPage( tranStepId ,groupBmCode , bmCode , settings , url) {
    return await reqUtil.get({
        url ,
        settings
    },{tranStepId ,groupBmCode , bmCode})
}

export async function previewCharts(transId , tranStepId ,settings) {
    return await reqUtil.get({
        url : "/dataPreview/previewCharts" ,
        settings
    },{transId , tranStepId})
}
/*
* 图表 数据服务输入
* @params
* @returns {Promise<*>}
* */
export async function getServiceInfoList() {
    return await reqUtil.get({
        url : "/serviceRequestInfoController/getServiceInfoList" ,
    })
}
export async function getInterfaceInfoListBySId(serviceId, settings) {
    return await reqUtil.get({
        url : "/serviceRequestInfoController/getInterfaceInfoListBySId?serviceId=" + serviceId ,
        settings
    })
}
export async function getBodyAttributeByCfgId(cfgId, settings) {
    return await reqUtil.get({
        url : "/serviceRequestInfoController/getBodyAttributeByCfgId?cfgId=" + cfgId ,
        settings
    })
}
/** 
 * 保存
 */
export async function saveServiceInputPlugin(tranStepId, serviceInputMeta, settings) {
    return await reqUtil.post({
        url : "/pluginServiceInput/savePlugin?tranStepId=" + tranStepId  ,
        settings
    }, serviceInputMeta)
}
export async function serviceInputAnalyseColumn  (tranStepId, transId, settings) {
    return await reqUtil.get({
        url : "/serviceRequestInfoController/analyseColumn?transId=" + transId + "&tranStepId=" + tranStepId ,
        settings
    })
}
/** 
 * 回显
 */
export async function queryData  (tranStepId, settings) {
    return await reqUtil.get({
        url : "/pluginServiceInput/queryData?tranStepId=" + tranStepId ,
        settings
    })
}