<template>
    <div>
        <common-dialog
            class="params-dialog"
            title="设置参数"
            width="1000px"
            height="100%"
            :visible.sync="visible"
            @closed="clear_data"
        >
        <div>
            
            <el-input placeholder="请输入名称"  
                    @keyup.enter.native="columnSearch" 
                    v-input-limit:trim v-model.trim="columnCode" 
                    size="medium" 
                    @click="columnSearch" class="ce-search searchClass">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="columnSearch"></i>
            </el-input>
            <common-table
                :data="data"
                :columns="region === '6' ? tHeadData :  [...tHeadData,setResponseColumn]"
                :pagination="false"
                class="width100"
                :border="false"
                v-if="showParams"
            >
                <template slot="setSearchColumn" slot-scope="scope">
                    <el-checkbox v-model="scope.row.setSearchColumn" @change="handleCheckedChange"></el-checkbox>
                </template>
                <template slot="header-setSearchColumn"  slot-scope="{row , $index}">
                    <el-checkbox label="设为查询字段"
                        v-model="checkAll"
                        :indeterminate="isIndeterminate"
                        @change="selectAll"
                        ></el-checkbox>
                </template>
                <template slot="setResponseColumn" slot-scope="{row , $index}">
                    <el-checkbox v-model="row.setResponseColumn" @change="setResponseColumnHandleCheckedChange"></el-checkbox>
                </template>
                <template slot="header-setResponseColumn"  slot-scope="{row , $index}">
                    <el-checkbox label="设为返回字段"
                        v-model="setResponseColumnCheckAll"
                        :indeterminate="isSetResponseColumnIndeterminate"
                        @change="setResponseColumnSelectAll"
                        ></el-checkbox>
                </template>
            </common-table>
        </div>
        <div slot="footer">
            <el-button size="mini" @click="visible = false">取消</el-button>
            <el-button size="mini" @click="save">保存</el-button>
        </div>
        </common-dialog>
    </div>
</template>
<script>
export default {
    name : "columns",
    props :{
        columnInfo : Array,
        responseTableData : Array,
        requestTableData : Array,
        region:String,
    },
    data(){
        return {
            columnCode : "",//字段搜索
            checkAll: false,
            isIndeterminate: false,
            setResponseColumnCheckAll : false,
            isSetResponseColumnIndeterminate : false,
            showParams :false,
            visible :false,
            data : [],
            tHeadData :[
                {
                    prop: "paramValue",
                    label: "字段名",
                    minWidth: 150,
                },
                {
                    prop: "name",
                    label: "字段中文名",
                    minWidth: 100,
                },
                {
                    prop: "dataType",
                    label: "字段类型",
                    minWidth: 80,
                },
                {
                    prop: "setSearchColumn",
                    label: "设为查询字段",
                    minWidth: 80,
                },

            ],
            setResponseColumn: {
                prop: "setResponseColumn",
                label: "设为返回字段",
                minWidth: 80,
            },
            requestChooseList : [],//选中的查询字段
            responseChooseList : [], // 选中的返回字段
        }
    },
    methods : {
        /**
         * 字段搜索
         */
        columnSearch(){
            if (this.columnCode === '') {
                this.data = JSON.parse(JSON.stringify(this.columnInfo));
                this.requestChooseList.forEach(element => {
                    this.data.forEach(e => {
                        if (element.code === e.code ) {
                            e.setSearchColumn = true;
                        }
                    });
                });
                this.responseChooseList.forEach(element => {
                    this.data.forEach(e => {
                        if (element.code === e.code ) {
                            e.setResponseColumn = true;
                        }
                    });
                });
            }
            this.data = this.data.filter(element=>{
                return element.code&&element.code.toLowerCase().indexOf(this.columnCode.toLowerCase()) > -1 || 
                        element.name&&element.name.toLowerCase().indexOf(this.columnCode.toLowerCase()) > -1;
            })
            this.handleCheckedChange();
            this.setResponseColumnHandleCheckedChange();
        },
        selectAll(value) {
            if (value) {
                this.data.forEach(item => {
                    item.setSearchColumn = true;
                })
            } else {
                this.data.forEach(item => {
                    item.setSearchColumn = false;
                })
            }
            this.isIndeterminate = false;
            this.getRequestChooseList();
        },
        getRequestChooseList(){
            this.data.forEach(item => {
                if (item.setSearchColumn) this.requestChooseList.push(item);
            });
            for (let index = 0; index < this.requestChooseList.length; index++) {
                const element = this.requestChooseList[index];
                this.data.forEach(item => {
                    if (item.code === element.code && !item.setSearchColumn) {
                        this.requestChooseList.splice(index, 1);
                    }
                })
            }
            this.requestChooseList = this.requestChooseList.filter(item =>{
                return item.setSearchColumn;
            })
        },
        handleCheckedChange(value) {
            let i = 0;
            this.data.forEach(item => {
                if (item.setSearchColumn) i++;
            })
            this.checkAll = i === this.data.length;
            this.isIndeterminate = i > 0 && i < this.data.length;
            this.getRequestChooseList();
        },
        setResponseColumnSelectAll(value) {
            if (value) {
                this.data.forEach(item => {
                    item.setResponseColumn = true;
                })
            } else {
                this.data.forEach(item => {
                    item.setResponseColumn = false;
                })
            }
            this.isSetResponseColumnIndeterminate = false;
            this.getResponseChooseList();
        },
        getResponseChooseList(){
            this.data.forEach(item => {
                if (item.setResponseColumn) this.responseChooseList.push(item);
            });
            for (let index = 0; index < this.responseChooseList.length; index++) {
                const element = this.responseChooseList[index];
                this.data.forEach(item => {
                    if (item.code === element.code && !item.setResponseColumn) {
                        this.responseChooseList.splice(index, 1);
                    }
                })
            }
            this.responseChooseList = this.responseChooseList.filter(item =>{
                return item.setResponseColumn;
            })
        },
        setResponseColumnHandleCheckedChange(value) {
            let i = 0;
            this.data.forEach(item => {
                if (item.setResponseColumn) i++;
            })
            this.setResponseColumnCheckAll = i === this.data.length;
            this.isSetResponseColumnIndeterminate = i > 0 && i < this.data.length;
            this.getResponseChooseList();
        },
        clear_data(){
            this.visible = false;
            this.showParams = false;
        },
        show(){
            this.visible = true;
            this.showParams = true;
            this.data = JSON.parse(JSON.stringify(this.columnInfo));
        },
        save(){
            this.data.forEach(item=>{
                item.isMust = true;
            })
            this.$emit("setTableData", this.data);
            this.visible = false;
            this.showParams = false;
        }
    },
}
</script>

<style scoped lang="less" src="./index.less"></style>