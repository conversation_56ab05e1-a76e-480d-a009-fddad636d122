<template>
<!--来自分享-->
    <div class="ShareTablePage" v-loading ="settings.loading">
        <div class="height100" v-if="showStyle">
            <div :class="isEdited ? 'ShareTablePage_cont' : 'ShareTablePage_dispay'">
                <dg-scrollbar>
                    <share-card :data="shareData" v-bind="$attrs" :show="'share'" @caseShareCardClick="caseShareCardClick"></share-card>
                </dg-scrollbar>
            </div>
            <el-pagination
                    class="tr"
                    v-if="isEdited"
                    @size-change="changeSize"
                    @current-change="changePage"
                    v-bind="paginationProps"
                    :total="total">
            </el-pagination>
        </div >
        <div class="height100" v-else>
            <common-table
                    :data="shareData"
                    :columns="tableColumns"
                    :border="false"
                    :pagination-props="paginationProps"
                    :pagination-total="total"
                    stripe
                    :header-cell-class-name="({column})=>{if(column.property === 'name') return 'ce-case-source_name'}"
                    :max-height="tableBodyH"
                    @change-current="changePage($event) "
                    @change-size="changeSize"
            >
                <template slot="name" slot-scope="{row , $index}">
                    <div  class="source" @click="caseShareCardClick(row)">{{row.name}}</div>
                </template>
                <template slot="empty">
                    <no-data :src="emptyImg" :title="tip" top="5%" ></no-data>
                </template>
            </common-table>
        </div>

        <!--数据集详情页面-->
        <data-details ref="dataDetails"/>
        <!--数据服务详情页面-->
        <detailDialog ref="detailDialog"></detailDialog>
        <!--可视化详情页面-->
        <VisualizationEdit ref="visual_panel" v-if="showEditPanel" @closePanel="closeEditPanel"  />
    </div>
</template>

<script>
import ShareCard from "@/projects/DataCenter/views/home/<USER>/share/ShareCard";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "../../service-mixins/service-mixins";
import {coustTableH} from "dc-front-plugin/src/api/commonMethods/count-table-h";
import NoData from "@/components/no-data/index";
import dataDetails from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/dataDetails";
import detailDialog from "@/projects/DataCenter/views/serviceManage/dialog/detailDialog";
import VisualizationEdit from '@/projects/DataCenter/views/visualizing/edit-panel/VisualizationEdit'
import emptyImg from "@/assets/images/common/empty-img.png";
export default {
    name: "SharePage",
    mixins : [commonMixins, servicesMixins ,coustTableH],
    components : {
        ShareCard,
        NoData,
        dataDetails, 
        detailDialog,
        VisualizationEdit,
    },
    props : {
        showStyle: {
            type : Boolean ,
            default : true
        },
        pageSize: {
            type : Number ,
            default : 9
        },
        pageSizes:  {
            type : Array ,
            default: ()=>{
                return [9 ,18,27, 36]
            }
        },
        isEdited : {
            type : Boolean ,
            default : false
        },
    },
    watch : {
        showStyle (val){
            this.triggerEvent("resize",300);
        }
    },
    data(){
        return {
            showEditPanel : false,
            emptyImg,
            tip : "暂未收到其他用户的分享!",
            tableColumns:[
                {
                    prop : "name" ,
                    label : "资源名称"
                },{
                    prop : "sourceType" ,
                    label : "资源类型"
                },{
                    prop : "user" ,
                    label : "分享用户"
                },{
                    prop : "time" ,
                    label : "分享时间",
                    width: "200"
                }
            ],
            shareData : [],
            //分页
            paginationProps: {
                currentPage: 1,
                pageSizes: [9 ,18, 27,36],
                pageSize: 9,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            sourceType: "",
            sourceName : "",
            users : [],
            typeList:{
                "dataSet" : "数据集",
                "dashboard" : "仪表盘",
                "dataService" : "数据查询",
                "modelService" : "模型分析",
                "aiService" : "AI算法",
                "compareService" : "比对订阅",
                "informationVerification" : "信息核查",
                "dataCollision" : "数据碰撞",
            }
        }
    },
    methods : {
        caseShareCardClick(data) {
            if (data.sourceType === '数据集') {
                let row = Object.assign({}, data);
                row.id = row.resourceId;
                row.name = row.sourceName || row.resourceName;
                this.$refs.dataDetails.show(row);
            }else if (data.sourceType === '仪表盘'){
                this.openVisualizingPanel(data);
            }
            else {
                this.apiDetails(data);
            }
            // else if (data.sourceType === '数据查询'){
            //     this.$refs.detailDialog.show(data.resourceId);
            // }
            // else if(data.sourceType === '模型分析' || data.sourceType === 'AI算法'){
            //     this.$refs.detailDialog.show(data.serviceId, "service");
            // }
        },
        /**
         * API详情跳转
         * @param {} row
         */
        apiDetails(row){
            const vm = this;
            let modelInfo ={}
            modelInfo.transId =  row.resourceId;
            let layer = vm.$dgLayer({
                title: "api详情",
                content: require("@/projects/DataCenter/views/modeling/dialog/details/detail-page/apiServer/index"),
                move: false,
                props : {
                    info: Object.assign(modelInfo, row),
                    isAi: row.sourceType === 'AI算法' ? true : false,
                    type: row.sourceType != 'AI算法' ? 'modeling' : '',
                    isShare : true,
                    serviceDetail: {
                        serviceId: row.serviceId
                    }
                },
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
                area: ["80%", "80%"],
                btnAlign: 'r',
            });
        },
        /**
         *   可视化跳转
         * @param {*} data 
         */
        openVisualizingPanel(data){
            // this.showEditPanel = true;
            // this.$nextTick(()=>{
            //     this.$refs.visual_panel.getRouterData(  data.resourceId,"" , "" , data.id);
            // })
            let routeData = this.$router.resolve({path: '/datacenter/visualview', query: {rowid: data.resourceId, key : "firstPage"}});
            window.open(routeData.href, '_blank');
        },
        closeEditPanel(groupId , filterTxt) {
            this.showEditPanel = false;
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.changePage();
        },
        changePage(inx=1){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let params = {
                "pageNum": inx,
                "pageSize": this.paginationProps.pageSize,
                "fromUserIds": this.users || [],
                "resourceName" : this.sourceName || "",
                "resourceType":this.sourceType || ""
            }
            vm.shareData = [];
            let services = vm.getServices(homeServices ,homeMock);
            services.fromShare(params , settings).then(res=>{
                if (res.data.status === 0 && res.data.data && res.data.data.dataList) {
                    vm.shareData = [];
                    res.data.data.dataList.forEach(element => {
                        let object = {
                            name : element.resourceName,
                            user : element.createUser,
                            time : element.shareTime ? new Date(element.shareTime).Format("yyyy-MM-dd hh:mm:ss") : "",
                            sourceType : vm.typeList[element.resourceType],
                            resourceType : element.resourceType,
                            ...element
                        };
                        if(element.resourceType !== 'aiService'){
                            vm.shareData.push(object);
                        }
                        else{
                            vm.$hideAI ? '' : vm.shareData.push(object);
                        }
                    });
                    
                    vm.total = res.data.data.totalCount;
                    vm.paginationProps.pageSize = res.data.data.pageSize;
                    vm.paginationProps.currentPage = res.data.data.pageIndex;
                    vm.countHeight();
                }
            })
        },
        /**
         * 外部调用 请求数据
         *  @param sourceType 资源类型
         * @param sourceName 资源名称关键字
         * @param users 用户
         * */
        getShareData(sourceType , sourceName , users ){
            this.sourceType = sourceType;
            this.sourceName = sourceName;
            this.users  = users;
            this.changePage();
        },
        /**
         * 初始化配置
         */
        init(){
            this.paginationProps.pageSize = this.pageSize;
            this.paginationProps.pageSizes = this.pageSizes;
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
@import "./shareCard.less";
</style>
