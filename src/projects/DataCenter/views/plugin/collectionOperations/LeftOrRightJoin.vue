<template>
    <div class="leftOrRightJoin ce-plug_cont" v-loading="settings.loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div  class="ce-tab_cont" v-show="activeName === 'setting_base'">
            <div class="ce-plug_attr">
                <el-form :model="plug_data" size="mini" label-position="top">
                    <el-form-item
                            v-for="(attr , i ) in attrsList"
                            :key="i"
                    >
                        <div slot="label" class="ce-label_title">{{attr.label + ' :'}}</div>
                        <span class="formTip" v-if="attr.tip"><i class="el-icon-info"></i>{{attr.tip}}</span>
                        <div class="ce-operation_box" v-if="attr.type === 'way'">
                            <div
                                    v-for="(btn , inx ) in attr.btns"
                                    :key="inx" :class="[btn.classN ,{'active' : btnActive === inx }]"
                                    @click="btnCheck(inx , btn.value)"></div>
                        </div>
                        <template v-if="attr.type === 'data_obj'">
                            <el-row class="ce-cont_pd" :gutter="10">
                                <el-col :span="12">左表:</el-col>
                                <el-col :span="12">右表:</el-col>
                            </el-row>
                            <el-row class="ce-cont_pd mb10" :gutter="10">
                                <el-col :span="12">
                                    <el-input disabled v-model="tableInfo.left.tableName">
                                        <el-popover
                                                slot="append"
                                                placement="bottom-end"
                                                width="250"
                                                v-model="leftTable.isVisible">
                                            <common-table ref="leftCommonTable"
                                                          max-height="296px"
                                                          :data="leftTable.fields"
                                                          :columns="selectColumns"
                                                          :pagination="false"
                                                          @selection-change="handleChangeLeft">
                                            </common-table>
                                            <span slot="reference" class="icon icon__btn" title="选择输出数据项">&#xe6e7;</span>
                                        </el-popover>
                                    </el-input>
                                </el-col>
                                <el-col :span="12">
                                    <el-input disabled v-model="tableInfo.right.tableName">
                                        <el-popover
                                                slot="append"
                                                placement="bottom-end"
                                                width="250"
                                                v-model="rightTable.isVisible">
                                            <common-table ref="rightCommonTable"
                                                          max-height="296px"
                                                          :data="rightTable.fields"
                                                          :pagination="false"
                                                          :columns="selectColumns"
                                                          @selection-change="handleChangeRight">
                                            </common-table>
                                            <span slot="reference" class="icon icon__btn" title="选择输出数据项">&#xe6e7;</span>
                                        </el-popover>
                                    </el-input>
                                </el-col>
                            </el-row>
                            <el-row class="ce-cont_pd" :gutter="10">
                                <el-col :span="24">连接字段:</el-col>
                            </el-row>
                            <el-row v-for="(joinField, joinIndex) in joinFieldList"
                                    class="ce-cont_pd mb10"
                                    :gutter="10">
                                <el-col :span="11">
                                    <el-select v-model="joinField.left.code"
                                               filterable
                                               placeholder="请选择"
                                               @change="joinFieldChange(joinField.left.code , 'left' , joinIndex)"
                                    >
                                        <el-option
                                                v-for="field in leftTable.fields"
                                                :key="field.colCode"
                                                :label="field.colName"
                                                :value="field.colCode">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="11">
                                    <el-select v-model="joinField.right.code"
                                               filterable
                                               placeholder="请选择"
                                               @change="joinFieldChange(joinField.right.code , 'right' ,joinIndex)"
                                    >
                                        <el-option
                                                v-for="field in rightTable.fields"
                                                :key="field.colCode"
                                                :label="field.colName"
                                                :value="field.colCode">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="2">
                                    <i v-if="joinIndex === 0"
                                       class="icon icon__btn fl"
                                       title="新增"
                                       @click.stop="handleAddJoinField"
                                    >&#xe6e1;</i>
                                    <i v-else
                                       class="icon icon__btn icon__btn_delete fl"
                                       title="删除"
                                       @click.stop="handleDeleteJoinField(joinIndex)"
                                    >&#xe756;</i>
                                </el-col>
                            </el-row>
                        </template>
                        <div v-if="attr.type === 'result'"
                             class="ce-cont_pd">
                            <common-table max-height="407px"
                                          :columns="resultColumns"
                                          :pagination="false"
                                          :data="result">
                                <i slot="opers"
                                   slot-scope="{ row }"
                                   class="icon icon__btn icon__btn_delete pl5 pr5 f18"
                                   title="删除"
                                   @click.stop="handleClickDelete(row)"
                                >&#xe847;</i>
                            </common-table>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <div class="attr_btn">
                <el-button
                        v-for="(btn , inx) in buttons"
                        :key="inx"
                        size="mini"
                        :type="btn.type"
                        @click="btn.clickFn"
                >{{btn.name}}
                </el-button>
            </div>
        </div>
        <div  class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr_nobtn">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import SettingSenior from '../component/SettingSenior'
    import PreView from "../component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";
    export default {
        name: "LeftOrRightJoin",
        mixins: [commonMixins, servicesMixins],
        props: {
            sourceData: Object,
            rowData : Object
        },
        components: {
            SettingSenior,
            PreView
        },
        data() {
            return {
                isSeniorShow: {},
                activeName:"setting_base",
                tab_panel: [
                    {
                        label: '基础',
                        name: 'setting_base'
                    }
                    , {
                        label: '高级',
                        name: 'setting_senior'
                    }
                ],
                loading : false ,
                btnActive: 0,
                btnValue: '',
                attrsList: [
                    {
                        label: '连接方式',
                        btns: [
                            {
                                classN: 'ce-operation_l',
                                value: 'Left'
                            }, {
                                classN: 'ce-operation_r',
                                value: 'Right'
                            }
                        ],
                        type: 'way',
                        value: '',
                    },
                    {
                        label: '数据对象',
                        type: 'data_obj',
                        value: ''
                    },
                    {
                        label: '输出结果',
                        type: 'result',
                        value: '' ,
                        tip : "若来源表出现重名字段,请修改字段别名,否则默认只保留一个字段"
                    }
                ],
                // dataObj:{
                // },
                leftTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                rightTable: {
                    fields: [],
                    checked: [],
                    isVisible: false
                },
                joinFieldList: [
                    //{ left: '', right: '' }
                ],
                selectColumns: [
                    {type: 'selection' , width: 50},
                    {prop: 'colCode', label: '英文名'},
                    {prop: 'colName', label: '中文名'},
                ],
                resultColumns: [
                    {type: 'index', label: '序号', align: 'center',width: 70},
                    {prop: 'colCode', label: '英文名'},
                    {prop: 'colName', label: '中文名'},
                    {prop: 'belongName', label: '原归属'},
                    {prop: 'opers', label: '操作', align: 'center', width: 70},
                ],
                buttons: [
                    {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary'
                    },{
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                ],
                pluginMeta: {},
                tableInfo: {
                    left : {
                        tableName : ''
                    },
                    right : {
                        tableName : ''
                    }
                },
                leftSelected:[],
                rightSelected:[]
            }
        },
        computed: {
            plug_data() {

            },
            result() {
                const {leftTable, rightTable, tableInfo} = this;
                let result = [];
                if (tableInfo.left && tableInfo.right) {
                    leftTable.checked.forEach(item => {
                        result.push(Object.assign({belongName: '左表：' + tableInfo.left.tableName, belong:'left', row: item}, tableInfo.left, item));
                    });
                    rightTable.checked.forEach(item => {
                        result.push(Object.assign({belongName: '右表：' + tableInfo.right.tableName, belong:'right', row: item}, tableInfo.right, item));
                    });
                }
                return result;
            }
        },
        methods: {
            previewFn(){
                this.$refs.preview.show(this.rowData, this.sourceData.id);
            },
            joinFieldChange(val , side , inx ) {
                let id ;
                let code ;
                this[side + 'Table'].fields.forEach( data => {
                    if( data.colCode === val ){
                        id = data.id;
                        code = data.colCode;
                    }
                });
                this.joinFieldList[inx][side].id = id;
                this.joinFieldList[inx][side].code = code;
            },
            btnCheck(index, value) {
                this.btnActive = index;
                this.btnValue = value;
                // if(!this.pluginMeta.op) return;
                this.pluginMeta.op = value + "Join";
            },
            initData() {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                services.leftOrRightJoinPluginPage(vm.sourceData.id,settings).then(result => {
                    // console.log("数仓目录树：",result.data.data);
                    if (result.data.status === 0) {
                        let leftSelCol = [];
                        let rightSelCol = [];
                        result.data.data.aggregatorFieldExps.forEach(exp => {
                            if (exp.objCode.includes("_left")) {
                                leftSelCol.push(exp.newFieldName);
                            }
                            if (exp.objCode.includes("_right")) {
                                rightSelCol.push(exp.newFieldName);
                            }
                        });

                        result.data.data.columns.leftTableColumns.forEach(leftCol => {
                            let item = {
                                colCode: leftCol.columnName,
                                colName: leftCol.columnZhName === "" ? leftCol.columnName: leftCol.columnZhName,
                                id: leftCol.dataColumnId,
                                type: leftCol.columnType,
                                length: leftCol.length,
                                precsn: leftCol.precsn
                            };
                            vm.leftTable.fields.push(item);
                            if (leftSelCol.includes(leftCol.columnName) || leftSelCol.includes(leftCol.columnZhName)) {
                                vm.leftSelected.push(item)
                            }
                        });
                        result.data.data.columns.rightTableColumns.forEach(rightCol => {
                            let item = {
                                colCode: rightCol.columnName,
                                colName: rightCol.columnZhName === "" ? rightCol.columnName: rightCol.columnZhName,
                                id: rightCol.dataColumnId,
                                type: rightCol.columnType,
                                length: rightCol.length,
                                precsn: rightCol.precsn
                            };
                            vm.rightTable.fields.push(item);

                            if (rightSelCol.includes(rightCol.columnName) || rightSelCol.includes(rightCol.columnZhName)) {
                                vm.rightSelected.push(item);
                            }
                        });
                        // 设置表名
                        vm.tableInfo = {
                            left: {
                                tableName: result.data.data.pluginMeta.leftStepName,
                                tableCode: result.data.data.pluginMeta.leftStepCode,
                                tableId: result.data.data.pluginMeta.leftStepId
                            },
                            right: {
                                tableName: result.data.data.pluginMeta.rightStepName,
                                tableCode: result.data.data.pluginMeta.rightStepCode,
                                tableId: result.data.data.pluginMeta.rightStepId
                            }
                        };
                        vm.pluginMeta = result.data.data.pluginMeta;

                        // 初始化链接字段
                        vm.handleAddJoinField();

                        if (vm.pluginMeta.op.includes("Left")) {
                            vm.btnActive = 0;
                            vm.btnValue = 'Left';
                        } else {
                            vm.btnActive = 1;
                            vm.btnValue = 'Right';
                        }
                        vm.$nextTick(() =>{
                            vm.leftSelected.forEach(item => {
                                vm.$refs.leftCommonTable[0].$children[0].toggleRowSelection(item);
                            });
                            vm.rightSelected.forEach(item => {
                                vm.$refs.rightCommonTable[0].$children[0].toggleRowSelection(item);
                            });

                            vm.pluginMeta.aggregatorJoinColumns.forEach((item, index) =>{
                                if (index !== 0) {
                                    vm.handleAddJoinField();
                                }
                                vm.joinFieldChange(item.leftCol , 'left' , index);
                                vm.joinFieldChange(item.rightCol , 'right' , index);
                            });

                        })
                    }
                })
            },
            saveFn() {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                vm.pluginMeta.aggregatorFieldExps = [];
                vm.result.forEach(item => {
                    vm.pluginMeta.aggregatorFieldExps.push({
                        objId: item.tableId,
                        objCode: item.tableCode,
                        replaceFieldName: item.colCode,
                        newFieldName: item.colCode,
                        valType: item.type,
                        length: item.length,
                        precision: item.precsn,
                        columnZhName: item.colName
                    })
                });

                vm.pluginMeta.aggregatorJoinColumns = [];
                vm.joinFieldList.forEach((value, index) => {
                    vm.pluginMeta.aggregatorJoinColumns.push({
                        index:index,
                        leftCol:value.left.code,
                        leftId:value.left.id,
                        rightCol:value.right.code,
                        rightId:value.right.id,
                    })
                });
               services.saveLeftOrRightJoinPlugin(vm.sourceData.id, vm.pluginMeta ,settings).then(result => {
                    if(result.data.status === 0){
                        if(vm.$refs.settingSenior.fieldTableData.length <= 0){
                            vm.initSettingSenior();
                        }else{
                            vm.saveColumn();
                        }
                        vm.$message.success("保存成功！");
                    }
                })
            },
            initSettingSenior(){
                this.$refs.settingSenior.initValue();
            },
            saveColumn(){
                this.$refs.settingSenior.saveFieldTable();
            },
            handleChangeLeft(val) {
                this.leftTable.checked = val;
            },
            handleChangeRight(val) {
                this.rightTable.checked = val;
            },
            handleClickDelete(row) {
                const vm = this;
                let $ref = null;
                if (row.belong === 'left') {
                    $ref = vm.$refs['leftCommonTable'][0].$refs.table;
                } else {
                    $ref = vm.$refs['rightCommonTable'][0].$refs.table;
                }
                $ref.toggleRowSelection(row.row)
            },
            handleAddJoinField() {
                const {leftTable, rightTable, joinFieldList} = this;
                let item = {left: {code:'', id:''}, right: {code:'', id:''}};
                if (leftTable.fields && leftTable.fields.length > 0) {
                    // item.left = leftTable.fields[0].colCode;
                    item.left = {
                        code : leftTable.fields[0].colCode,
                        id : leftTable.fields[0].id,
                    }
                }
                if (rightTable.fields && rightTable.fields.length > 0) {
                    // item.right = rightTable.fields[0].colCode;
                    item.right = {
                        code : rightTable.fields[0].colCode,
                        id : rightTable.fields[0].id,
                    }
                }
                joinFieldList.push(item)
            },
            handleDeleteJoinField(index) {
                const {joinFieldList} = this;
                joinFieldList.splice(index, 1);
            }
        },
        created() {
            this.initData();
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: false,
                isSampleExpr: false,
                isThread: false,
                isException: false,
                notTransition:false,
            }
        }
    }
</script>

<style scoped lang="less">


    .ce-operation_box {
        display: flex;
        justify-content: center;
    }

    .ce-operation_l, .ce-operation_r {
        width: 70px;
        height: 66px;
        margin: 0 10px;
        cursor: pointer;
    }

    .ce-operation_l {
        background: url("../../../assets/images/plug-btns/nav-btn2.png") no-repeat;
    }

    .ce-operation_l.active, .ce-operation_l:hover {
        background: url("../../../assets/images/plug-btns/nav-btn2f.png") no-repeat;
    }

    .ce-operation_r {
        background: url("../../../assets/images/plug-btns/nav-btn3.png") no-repeat;
    }

    .ce-operation_r.active, .ce-operation_r:hover {
        background: url("../../../assets/images/plug-btns/nav-btn3f.png") no-repeat;
    }

    .ce-label_title {
        color: @font-color;
    }

    .ce-cont_pd {
        padding: 0 10px;
    }

    .ce-cont_pd /deep/ .el-input-group__append {
        padding: 0 10px;
    }

    .icon__btn {
        color: @font-color;
        cursor: pointer;
    }

    .icon__btn_delete {
        color: #f56c6c;
        cursor: pointer;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
