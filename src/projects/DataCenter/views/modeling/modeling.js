import {servicesMixins} from "./service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi_3/index.vue"
import Tree from "./tree"
import List from "./list"
import NewModel from "./dialog/NewModel";
import ModelTabs from "./dialog/ModelTabs";
import RenameDailog from "./dialog/RenameDailog"
import ModelEditDailogVue from "./dialog/ModelEditDailog.vue";
import OpenModel from './dialog/OpenModel'
import preview from "./dialog/preview"
import share from "./dialog/share"
import Issue from "./dialog/issue"
import treeCard from "@/components/layout/tree-card"
import newTreeDir from "@/projects/DataCenter/views/modeling/dialog/newTreeDir"
import {TreeEdit} from "@/projects/DataCenter/views/modeling/tree/tree-edit";
import SaveModel from "@/projects/DataCenter/views/modeling/flowPanel/save-model";
import {mapActions} from "vuex";
export default {
    name: "modeling" ,
    mixins : [servicesMixins , commonMixins ,TreeEdit],
    components : {
        TreeAndList ,
        Tree ,
        List,
        NewModel,
        ModelTabs,
        RenameDailog,
        ModelEditDailogVue,
        OpenModel,
        preview ,
        share,
        Issue,
        treeCard,
        newTreeDir,
        SaveModel,
    },
    data(){
        return {
            newModel: false, //新建模型
            modelTabs: false,
            treeData:[],
            cardName : "模型列表" ,
            showAddBox : false ,
            sortMenu : {
                time : {label : "按时间排序" , icon : "iconshijianpaixutubiao"} ,
                letter : {label : "按字母排序", icon : "iconsort"}
            },
            sortVal : "time",
            isCase :false,//是否场景案例跳转
        }
    },
    methods : {
        ...mapActions(['setIsCol']),
        /**
         * 另存为
         */
        saveAs(row){
            this.$refs.SaveModel.show( row, '','模型另存为',  true);
        },
        /**
         * 显示移动弹窗
         * @param data
         * @param node
         */
        moveToDir(data,node){
            let level = data.children && data.children.length || node.level >= this.treeLimitLevel  ? node.level - 1 : this.treeLimitLevel-1;
            this.$refs.treeDir.show(data , 'move' , level );
        },
        /**
         * 排序树
         * @param val
         */
        setTreeSort(val){
            this.showAddBox = false;
            this.sortVal = val;
            this.$refs.dataTree.sortTreeData(val);
        },
        /**
         * 显示添加目录弹窗
         * @param data
         */
        showAddDialog(data){
            this.$refs.treeDir.show(data);
        },
        filterTree(val){
            this.$refs.dataTree.setFilterText(val);
        },
        serveIssue(row){
            this.$refs.issue.show(row)
        },
        shareFn(row){
            this.$refs.share.show(row);
        },
        previewResult(row){
            this.$refs.preview.show(row);
        },
        moveTo(row , type , operType){
            this.$refs.move.open(row, type , operType);
        },
        setTreeData(data) {
            this.treeData = data;
        },
        treeDir(value, id , node) {
            this.$refs.modelingCenterTable.setDirType(value, id , node);
        },
        initTable(data){
            this.$refs.modelingCenterTable.deleteInit(data);
        },
        openNewModel(dirId) {
            this.addModelTab(false,dirId);
            // this.newModel = true;
        },
        closeNewModel() {
            this.newModel = false;
        },
        openModelTabs() {
            this.modelTabs = true;
        },
        closeModelTabs() {
            this.modelTabs = false;
        },
        backTo() {
            //返回
            this.closeNewModel();
            this.closeModelTabs();
            this.$refs.modelingCenterTable.changePage(1);
        },
        addModelTab(isTemplate ,dirId) {
            const vm = this;
            this.openModelTabs();
            this.closeNewModel();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab({},!!isTemplate ,dirId);
            })
        },
        addModule(data){
            const vm = this;
            this.closeNewModel();
            this.openModelTabs();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab(data);
            })

        },
        update(){
            this.$refs.modelingCenterTable.changePage(1);
        },
        dialogRenameEmit(newName, index,transId) {
            // this.$refs.modelPage.reName(newName,transId);
            this.$refs.modelingCenterTable.tDataChangeWithName(newName, index);
        },
        rename(row, index) {
            this.$refs.refRenameDialog.setName(row, index);
            this.$refs.refRenameDialog.show();
        },
        modelEdit(rowData) {
            const vm = this;
            this.openModelTabs();
            vm.$nextTick(()=>{
                vm.$refs.modelPage.addTab(rowData , false);
            })
        },
        closeSave(){
            this.$refs.modelingCenterTable.changePage(1);
        },
        getCollectionLog(){
            const vm = this;
            let services = this.$services('modeling');
            services.isCollectionLog().then(res => {
                vm.setIsCol(res.data.data);
            })
        },
    },
    created() {
        const vm = this;
        vm.getCollectionLog();
        let {rapidShowName : tab ,rowData } = this.$route.params;
        if (tab) {

            if (tab === 'rapid') {
                vm.$nextTick(()=>{
                    this.$refs.modelingCenterTable.rapidAnalysis();
                })
            }else if(tab === 'modeling') {
                vm.$nextTick(()=>{
                    rowData && rowData.transId ? this.$refs.modelingCenterTable.editModel(rowData) : this.$refs.modelingCenterTable.openNewModel();
                })
            }
        }
    }
}
