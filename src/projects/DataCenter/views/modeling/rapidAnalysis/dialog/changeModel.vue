<template>
    <div class="openModel">
        <dg-dialog :close-on-click-modal="false"
                   :width="width"
                   title="转为流程建模"
                   :visible.sync="dialogVisible"
                   @closed="close"
                   :append-to-body="true"
                   class="model-dialog"
        >
            <el-form :model="form" label-width="100px" label-position="right">
                <!--<el-form-item :label="formList.name.label" prop="transName"-->
                              <!--:rules="[{ required: true, message: '请输入模型名称', trigger: ['blur','change'] }]">-->
                    <!--<div class="content__name">-->
                        <!--<el-input v-model.trim="form.transName" maxlength="25" v-input-limit:trim-->
                                  <!--@input="inputFilterSpecial($event , 'form' ,'transName')"-->
                                  <!--:placeholder="formList.name.placeholder"></el-input>-->
                        <!--<p>{{ formList.name.tip }}</p>-->
                    <!--</div>-->
                <!--</el-form-item>-->
                <el-form-item :label="formList.pos.label" required>
                    <div class="content__wrap dataTree">
                        <el-input placeholder="请输入名称" v-model="filterText" v-input-limit:trim size="medium" class="ce-search">
                            <i slot="suffix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                        <!--目录树-->
                        <div class="ce-tree selectn">
                            <dg-tree
                                    class="filter-tree"
                                    :data="data"
                                    :props="defaultProps"
                                    node-key="id"
                                    :expand-on-click-node="false"
                                    :filter-node-method="filterNode"
                                    :highlight-current="true"
                                    @node-click="nodeClick"
                                    :default-expanded-keys="expandData"
                                    ref="tree">

                            <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                                <span class="node-label"
                                      :title="data.name"
                                      :class="[
                                      data.icon ,{
                                        'el-icon-document' : !data.isParent
                                      }]"
                                >{{ data.name }}</span>
                            </span>
                            </dg-tree>
                        </div>
                    </div>
                </el-form-item>
            </el-form>

            <div slot="footer">
                <el-button @click="close" size="mini">{{btnCancelTxt}}</el-button>
                <el-button @click="checked" type="primary" size="mini">{{btnCheckTxt}}</el-button>
            </div>
        </dg-dialog>
    </div>
</template>

<script>
    import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import {mapState} from "vuex";

    export default {
        name: "changeModel",
        mixins: [servicesMixins],
        data() {
            return {
                filterText:'',
                defaultProps: {
                    children: 'children',
                    label: 'label',
                    value: "id"
                },
                formList: {
                    name: {
                        label: '模型名称 :',
                        placeholder: '请输入模型名称',
                        tip: "名称最大不超过25字符，只允许除/、\\、<、>等特殊字符以外。"
                    },
                    pos: {
                        label: "保存到目录 :"
                    },
                },
                form: {
                    transName: "",
                    dirId:''
                },
                width: '800px',
                dialogVisible: false,
                treeNode: "",
                row: {},
                data:[],
                expandData:[],
                currentId : ""
            }
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        computed: {
            ...mapState({
                // 可编辑节点 非分享，非他人目录(不等于他人目录 Id ，没有用户 id 值)
                nodeEditable: (state) => {
                    return (data) => data.id !== state.plans.shareId && data.id !== state.plans.otherUserCatalogId && data.currentUserId === state.user.userInfo?.id;
                }
            })
        },
        mounted() {
            this.initTree();
        },
        methods: {
            ...treeMethods.methods,
            nodeClick(v, i) {
                this.form.dirId = v.id;
            },
            setTreeNode(data, level) {
                const vm = this, {editDir} = vm;
                let child_l = level + 1;
                return data.map(item => {
                    let disabled = editDir && level > 1;
                    let children = item.children && item.children.length && !disabled ? vm.setTreeNode(item.children, child_l) : [];
                    let icon = item.dirType === 'TRANS_DIR_STANDARD' ?
                        'dg-iconp icon-journal-b' : item.dirType === 'TRANS_DIR_MF_MY' ? 'dg-iconp icon-notebook' : '';
                    return {
                        id: item.id,
                        label: item.name,
                        name: item.name,
                        children,
                        icon,
                        isParent: true,
                        pId: item.pId === "-1" ? "0" : item.pId,
                        dirType: item.dirType,
                        level,
                        operateTime: item.operateTime,
                        currentUserId: item.currentUserId,
                    }

                });
            },
            initTree() {
                const vm = this, {services, settings} = this;
                settings.loading = true;
                vm.data = [];
                vm.expandData = [];
                services.queryTransTree('', '', 'TRANS_DIR_MF', settings)
                    .then(res => {
                        if (res.data.status === 0) {
                            let result = res.data.data;
                            if (!result || result.length === 0) return;
                            vm.currentId = result[0].id;
                            vm.data = vm.setTreeNode(result, 1);
                            vm.data = vm.data.filter(o => vm.nodeEditable(o));
                            vm.setCurrentNode();
                        }
                    })
            },
            setCurrentNode(){
                const vm= this;
                vm.$nextTick(() => {
                    if (vm.$refs.tree && vm.currentId) {
                        vm.$refs.tree.setCurrentKey(vm.currentId);
                        vm.expandData.push(vm.currentId);
                        let currentNode = vm.$refs.tree.getNode(vm.currentId);
                        currentNode ? vm.nodeClick(currentNode.data, currentNode):'';
                    }
                });
            },
            /**
             * 打开
             * @param row
             */
            open(row) {
                this.row = row;
                this.form.transName=row.modelName;
                this.dialogVisible = true;
                this.setCurrentNode();
            },
            close() {
                this.form = {
                    transName: "",
                    dirId:''
                };
                this.row={};
                this.filterText=""
                this.dialogVisible = false;
            },
            /**
             * 确定
             */
            checked() {
                //if(!this.form.transName) return this.$message.warning('请输入模型名称！');
                !this.form.dirId?this.$message.warning('请选择保存目录！'): this.$emit('save',this.row , this.form)
            },
        }
    }
</script>

<style scoped lang="less">
    .model-dialog {
        .dataTree {
            margin: 10px 0 0 0;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 2px;
            box-sizing: border-box;
            height: 250px;
        }

        .ce-tree {
            margin-top: 12px;
            height: calc(100% - 40px);
            overflow: auto;
        }

        .ce-tree__menu {
            position: fixed;
            top: 0;
            min-width: 80px;
            text-align: left;
            border: 1px solid #ccc;
            background: #fff;
            padding: 0;
            z-index: 100;
            box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
            border-radius: 2px;
        }

        .ce-tree__menu li {
            cursor: pointer;
            list-style: none outside none;
            font-size: 12px;
            white-space: nowrap;
            border-bottom: 1px solid #eee;
            padding: 0 12px;
            height: 28px;
            line-height: 28px;
            color: #666;
        }

        .ce-tree__menu li:hover {
            color: @font-color;
        }

        .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;
        }

        .node-label {
            max-width: 166px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .node-label::before {
            padding-right: 5px;
        }
    }


</style>
