<template>
    <div class="modelPlugIn selectn" ref="plugs">
        <PlugInGroup v-for="(plug , index) in plugIn" :key="index" :ref="'slide'+index" :plugInData="plug"
                     :addIcon="addIcon" :removeIcon="removeIcon"/>
        <div class="cl"></div>
    </div>
</template>

<script>
    import PlugInGroup from './PlugInGroup'
    import PlugIcons from '@/assets/data/plug-icons.json'
    import $ from 'jquery'
    import {getPlugins} from "@/assets/data/model-plugin";
    export default {
        name: "ModelPlugIn",
        components: {
            PlugInGroup
        },
        data() {
            return {
                plugIn: getPlugins(),
                addIcon: false,
                removeIcon: false

            }
        },
        methods: {
            initPlug() {
                // console.log("处理后插件信息：", this.plugIn);
                this.plugIn.forEach(plug => {
                    this.getIcons(plug.plugList);
                })
            },
            getIcons(plug, setAttrType) {
                plug.forEach(p => {
                    p.icon = PlugIcons[p.label];
                    p.type = 'plug'; //添加类型
                    // p.type = setAttrType ? setAttrType : 'plug'; //添加类型
                    if (p.children) {
                        this.getIcons(p.children);
                        /* if(p.label === '结果输出'){
                             this.getIcons(p.children ,'dialog');
                         }else {
                             this.getIcons(p.children);
                         }*/
                    }
                })
            },
            hasSpaceForIcon() {
                let plugs = $(this.$refs.plugs).children('.plugInGroup');
                let screenW = $(this.$refs.plugs).width(), parentW = 0;
                for (let i = 0; i < plugs.length; i++) {
                    parentW += plugs.eq(i).outerWidth();
                }
                screenW - parentW > 96 ? this.addIcon = true : this.addIcon = false;
                screenW - parentW <= 20 ? this.removeIcon = true : this.removeIcon = false;
                // console.log(screenW - parentW);
                if (this.addIcon) {
                    this.$nextTick(() => {
                        this.$refs.slide1[0].addIconFun();
                    })
                }
                if (this.removeIcon) {
                    this.$nextTick(() => {
                        this.$refs.slide1[0].removeIconFun();
                    })
                }


            },
            checkSpace() {
                if (this.addIcon) {
                    this.hasSpaceForIcon();
                }
                if (this.removeIcon) {
                    this.hasSpaceForIcon();
                }
            },
            initSpace() {
                this.hasSpaceForIcon();
                this.$nextTick(() => {
                    this.checkSpace();
                });
            },
        },
        created() {
            this.initPlug();
        },
        mounted() {
            this.initSpace();

            let $this = this;
            $(window).resize(() => {
                $this.hasSpaceForIcon();
            });
        },


    }
</script>

<style scoped>
    .modelPlugIn {
        position: relative;
        border-bottom: 1px solid #ddd;
        background: #fff;
        z-index: 150;
    }
</style>