<template>
    <div class="lookVersion">
        <common-dialog
                :title="title"
                width="800px"
                height="80vh"
                :visible.sync="visible"
                @closed="clear"
        >
            <div>
                <common-table
                        :columns="headData"
                        :data="tableData"
                        :pagination="false"
                >
                </common-table>
            </div>
            <div slot="footer">
                <el-button size="mini" @click="clear">取消</el-button>
                <el-button size="mini" type="primary" @click="save">确认</el-button>
            </div>
        </common-dialog>
    </div>
</template>

<script>
    export default {
        name: "lookVersion",
        data(){
            return {
                title: '查看历史版本',
                visible: false,
                tableData: [],
                headData:[
                    {
                        prop: 'versionCode',
                        label: '版本号'
                    },
                    {
                        prop: 'updateTime',
                        label: '更新时间'
                    },
                    {
                        prop: 'updateMemo',
                        label: '更新说明'
                    },
                ],
            }
        },
        methods: {
            show(row){
                this.visible = true;
            },
            clear(){
                this.visible = false;
            },
            save(){
                this.clear();
            },
        }
    }
</script>

<style scoped>

</style>