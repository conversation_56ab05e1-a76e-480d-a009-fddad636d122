<template>
    <el-container class="x-main-container layout" >
        <el-header class="x-main-header" :style="{'background-color' : $layoutBg }">
            <div class="x-main-logo">
                <!--<i class="dg-iconp icon-model"></i>
                <span>{{ sysName }}</span>-->
                <div class="logo"  :style="{'color' : $layoutColor }">{{systemName}}</div>
            </div>
            <div class="x-main-menu">
                <div
                    :class="['x-main-menu-item', active[0] === item.path && 'is-active']"
                    @click="handleToSubRoute(item)"
                    v-for="item in topMenus"
                    :key="item.path"
                    :style="{'color' : $layoutColor }"
                >
                    <i :class="['dg-iconp', item.meta.icon]"></i>
                    <span>{{ item.meta.title }}</span>
                </div>
            </div>
            <div class="x-main-right ce-user__list"  :style="{'color' : $layoutColor }">
                <li v-if="!hideMarket">
                    <div class="ce-link_home" @click="goMarket">{{marketName}}</div>
                </li>
                <div v-if="!hideMarket">
                    <el-divider direction="vertical"/>
                </div>
                <div>
                    <help-document :code="'cicadaDataCenter'"></help-document>
                </div>
                <div>
                    <div class="ce-user__img">
                        <i class="icon f18">&#xe690;</i>
                        <span class="ml6">{{ userName }}</span>
                    </div>
                </div>
                <li>
                    <a class="ce-logout" title="退出" :style="{'color' : $layoutColor }" href="javascript:void(0);" @click="loginOut">
                        <i class="icon">&#xe6f7;</i>
                    </a>
                </li>
            </div>
        </el-header>
        <el-container class="x-main-body">
            <el-aside class="x-main-side" :style="{'background-color' : countColor }" width="96px" v-if="subRoutes.length > 0">
                <div
                    :class="['x-main-side-item', active[1].includes(sub.path) && 'is-active']"
                    v-for="sub in subRoutes"
                    :key="sub.path"
                    @click="handleToRoute(sub)"
                    :style="{'color' : $layoutColor }"
                >
                    <i :class="['dg-iconp', sub.meta.icon]"></i>
                    <span>{{ sub.meta.title }}</span>
                </div>
            </el-aside>
            <el-main class="x-main-content">
                <div class="breadcrumb">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <template v-for="v in $route.matched">
                            <el-breadcrumb-item v-if="v.meta.title && v.meta.breadcrumb" :to="{ path: v.redirect }"  :key="v.path">{{v.meta.title }}</el-breadcrumb-item>
                            <el-breadcrumb-item v-else-if="v.meta.title && v.path && !v.meta.hidecrumb"  :key="v.path" class="document">{{v.meta.title }}<help-document :code="v.meta.documentCode"></help-document></el-breadcrumb-item>
                        </template>
                    </el-breadcrumb>
                </div>
                <div class="x-main-cont">
                    <router-view />
                </div>
            </el-main>
        </el-container>

    </el-container>
</template>

<script>
    // @ is an alias to /src
    // import sidebar from "@/components/layout/sidebar/index"
    import {getMenu} from '../router'
    // import Breadcrumb from "@/components/layout/breadcrumb";
    // import appMain from "@/components/layout/app-main"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {mapGetters , mapState} from "vuex";
    import {headerMixins} from "@/projects/DataCenter/views/header-mixins/header-mixins";
    import HelpDocument from "../../../components/signed-components/helpDocument/helpDocument";

    export default {
        name: 'Index',
        components: {
            HelpDocument,
            // sidebar,
            // Breadcrumb,
            // appMain,
        },
        mixins: [commonMixins , headerMixins],
        computed: {
            sidebar() {
                return this.$store.state.app.sidebar;
            },
            classObj() {
                return {
                    hideSidebar: !this.sidebar.opened,
                    openSidebar: this.sidebar.opened
                };
            },
            userName() {
                if (this.userInfo) {
                    return this.userInfo.objName;
                } else {
                    return "";
                }
            },
            topMenus() {
                let routerData = getMenu();
                return routerData.filter((item) => !item.hidden);
            },
            ...mapGetters(["userInfo"]),
            ...mapState({
                loginWay(state){
                    return state.user.loginWay
                }
            })
        },
        data() {
            return {
                user: {
                    img_path: '',
                    name: 'admin'
                },
                routers: [],
                userRight: [],
                activeTab : "",
                subRoutes: [],
                active: []
            }
        },
        methods: {
            /**
             * 前往页面
             */
            handleToRoute(route) {
                this.$set(this.active, 1, route.path);
                this.$router.push(route.path);
            },
            /**
             * 初始化路由路径页面
             * */
            async init() {
                this.$store.dispatch("systemName", {name: this.sysName});
                await this.getRight();
                this.setCurrentTab();
            },
            /**
             * 请求获取用户权限
             * */
            async reqRights() {
                const vm = this;
                let {loginServices, loginMock} = this;
                let services = vm.getServices(loginServices, loginMock);
                await services.queryUserFunction().then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        if(result.length){
                            vm.userRight = result;
                            vm.$store.dispatch("setRight", result);
                            localStorage.setItem("userRight",JSON.stringify(result) );
                        }else {
                            vm.$message.warning("无系统访问权限");
                            vm.$router.push("/login");
                            localStorage.removeItem("userInfo");
                        }

                    }else {
                        vm.$router.push("/login");
                        localStorage.removeItem("userInfo");
                    }
                })
            },
            /**
             * 获取缓存的权限 设置菜单
             * */
            getRight() {
                const vm = this;
                let userRight = vm.$store.getters.userRight;
                if (!userRight) {
                    userRight = vm.userRight = JSON.parse(localStorage.getItem("userRight"));
                    vm.$store.dispatch("setRight", userRight);
                }
            },
            /**
             * 设置当前 路径高亮tab
             */
            setCurrentTab(){
                let routerData = getMenu();
                const {matched} = this.$route;
                this.active = matched.map((item) => item.path || "/");
                let fitItem = routerData.find((item) => item.path === matched[0].path);
                this.subRoutes = [];
                if (fitItem) {
                    // this.handleToSubRoute(fitItem);
                    this.subRoutes =  fitItem.hiddenChildren ? [] : fitItem.children;
                }
            }
        }
    }
</script>
<style scoped>
    .logo {
        line-height: 60px;
        height: 60px;
        padding-left: 40px;
        font-weight: bold;
        font-size: 28px;
        color: #ffffff;
        background: url(../../../assets/images/header/logo.png) no-repeat left center;
    }
    .layout {
        width: 100%;
        min-width: 1360px;
    }

    .l-sidebar {
        height: calc(100vh - 4rem);
    }

    .ce-main__cont {
        margin-left: 17rem;
        height: calc(100vh - 4rem);
        overflow: auto;
        transition: 500ms;
    }

    .hideSidebar .l-sidebar {
        width: 4rem;
    }

    .hideSidebar .ce-main__cont {
        margin-left: 4rem;
    }
    /deep/.document i{
        font-size: 14px !important;
    }
</style>

<style lang="less" scoped src="./index.less"></style>

