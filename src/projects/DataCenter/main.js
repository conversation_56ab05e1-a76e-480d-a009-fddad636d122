import Vue from 'vue'
// import VueCodemirror from 'vue-codemirror'
import App from './App.vue'
import router from './router'
import store from './store'
import 'codemirror/lib/codemirror.css'

Vue.config.productionTip = false;
import Axios from 'axios';

const axios = Axios.create({
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    },
    timeout: 1000 * 60 * 60 * 10,
    withCredentials: true
});
axios.all = Axios.all;
axios.spread = Axios.spread;
Vue.prototype.$axios = axios;
Vue.prototype.$CancelToken = Axios.CancelToken;
import "@assets/images/icons"
import {interceptors} from '@/api/index';

interceptors(axios);

import removeAria from "@/api/removeAria";

Vue.directive("removeAria", removeAria);

// const IS_PROD = process.env.NODE_ENV === "production";


//element ui
import ElementUI from 'element-ui';

Vue.use(ElementUI);

const messages = ['success', 'warning', 'info', 'error'];

messages.forEach(type => {
    ElementUI.Message[type] = options => {
        if (typeof options === 'string') {
            options = {
                message: options
            };
            // 默认配置
            options.duration = 2000;
            options.offset = 70;
        }
        options.type = type;
        return ElementUI.Message(options);
    };
})
import ElementExtends from "ui-component-v4";

Vue.use(ElementExtends, {size: "small"});
// import "@/api/element-import";

//全局函数
import global_func from 'dc-front-plugin/src/api/global_func'

Vue.use(global_func);
//引入使用的插件脚本
import '@/api/global_js_file';


import Cookies from "js-cookie";

Vue.prototype.$cookies = Cookies;

//引入 右键菜单
import ContextMenu from "@/components/subGroup/ContextMenu"

Vue.use(ContextMenu);
//echarts
const VCharts = require('v-charts');
Vue.use(VCharts);


import echarts from 'echarts'

Vue.prototype.$echarts = echarts;

import VueClipboard from 'vue-clipboard2'

Vue.use(VueClipboard);
import FoldCard from "@/components/fold-card"

Vue.component('FoldCard', FoldCard)
Vue.prototype.$path = window.location.host;
import {getCurrentProtocol} from "@/api/global_func";

Vue.prototype.getCurrentProtocol = getCurrentProtocol;
import "@/api/global";
/* 服务接口 */
import {globalServices} from "@/api/globalServices";
/*配置后端路径*/

/*第一层if判断生产环境和开发环境*/
function getServerConfig() {
    return new Promise((resolve, reject) => {
        const protocol = getCurrentProtocol();
        let url = process.env.BASE_URL !== '/' ? `${protocol}://` + window.location.host + process.env.BASE_URL : `${protocol}://${window.location.host}`;
        axios.get(`${url}/serverConfig.json`).then((result) => {
            let config = result.data;
            if (process.env.NODE_ENV === 'production') {
                /*第二层if，根据.env文件中的VUE_APP_FLAG判断是生产环境还是测试环境*/
                if (process.env.VUE_APP_FLAG === 'pro') {
                    //production 生产环境
                    axios.defaults.baseURL = config.production;
                } else {
                    //test 测试环境
                    axios.defaults.baseURL = config.test;
                }
            } else {
                //dev 开发环境
                axios.defaults.baseURL = config.dev;
            }
            // 全局配置变量
            Vue.prototype.$globalConfig = config;
            Vue.prototype.isMock = config.isMock;
            Vue.prototype.$uploadFileLimit = config.uploadFileLimit; //文件上传大小限制 （M）
            Vue.prototype.$roleCode = config.roleCode;
            Vue.prototype.$userGroupCode = config.userGroupCode;
            Vue.prototype.$userCode = config.userCode;
            Vue.prototype.$pgis_url = config.pgis_url;
            Vue.prototype.$pgis_crs = config.pgis_crs;
            Vue.prototype.$pgis_type = config.pgis_type;
            Vue.prototype.$map_area_code = config.map_area_code;
            Vue.prototype.$map_center = config.map_center;
            Vue.prototype.$corner = config.map_corner;
            Vue.prototype.$systemName = config.systemName;
            Vue.prototype.$marketName = config.marketName;
            Vue.prototype.$layoutBg = config.layoutBg;
            Vue.prototype.$layoutColor = config.layoutColor;
            Vue.prototype.$edition = process.env.VUE_APP_SECRET === 'basic' ? '基础版' : '标准版';
            Vue.prototype.$loginWay = "";
            Vue.prototype.$services = globalServices(config.isMock);
            Vue.prototype.$hideMarket = config.hideMarket;
            Vue.prototype.$hideAI = config.hideAI;
            Vue.prototype.$fileUploadLimit = config.fileUploadLimit;
            Vue.prototype.$routerMenu = config.routerMenu;
            Vue.prototype.$pluginPanel = config.pluginPanel;
            Vue.prototype.$marketModelTypes = config.marketModelTypes;
            window.document.title = config.systemName;
            axios.get("/loginWay/getSystemLoginWay").then(res => {
                if (res.data.status === 0) {
                    let way = res.data.data === "1" ? 'dids' : '';
                    Vue.prototype.$loginWay = way;
                }
            }).catch(err => err)
            let rights = JSON.parse(localStorage.getItem("userRight"));
            if (rights && rights.length)
                store.dispatch("setRight", rights);
            resolve();
        }).catch((error) => {
            // console.log(error)
            reject()
        })
    })
}


async function init() {
    await getServerConfig();
    new Vue({
        router,
        store,
        render: h => h(App)
    }).$mount('#app')
}

init();

