<template>
    
        <div v-loading="settings.loading">
            <el-form :inline="true" >
                    <el-form-item label="服务方名称:" required="">
                        <el-input placeholder="请选择:" class="input_style" v-model="serviceName" :disabled="serviceName!==null"></el-input>
                    </el-form-item>
                    <el-form-item label="服务类型:" required="">
                        <el-select placeholder="请选择:" class="input_style" v-model="serviceStyle" :disabled="interfaceDetailShow">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <div>
                        <el-form-item label="接口名称       :" required="">
                            <el-input placeholder="请输入(限100个字符):" style="width:105%" v-model="interfaceName" :disabled="interfaceDetailShow" v-input-limit:trim maxlength="100"></el-input>
                        </el-form-item>
                        <el-form-item label="服务细类:" required="" style="padding-left:10px;">
                            <el-select placeholder="请选择:" class="input_style" v-model="serviceClass" :disabled="interfaceDetailShow">
                                <el-option
                                    v-for="item in optionsClass"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
            </el-form>
            <div class="ce-title_box">
                <span class="ce-form_title">请求报文配置</span>
                <dg-button type="primary" @click="add" v-if="!interfaceDetailShow">添加</dg-button>
            </div>
            <!-- <div style="">
                <dg-button type="primary" >列表</dg-button>
                <dg-button type="primary" @click="submit">源码</dg-button>
            </div>-->
            <el-table
                    :data="interfaceData"
                    border
                    :row-style="{height:'10px'}"
                    :cell-style="{padding:'5px 0'}"
                >
                    <el-table-column prop="code" label="字段名称" :span="3">
                        <template slot-scope="scope">
                            <span v-if="!scope.row.addNew&&interfaceDetailShow">
                                {{ scope.row.code }}
                            </span>
                            <el-input v-else v-model="scope.row.code">

                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="字段中文名称" :span="3">
                        <template slot-scope="scope">
                            <span v-if="!scope.row.addNew&&interfaceDetailShow">
                                {{ scope.row.name }}
                            </span>
                            <el-input v-else v-model="scope.row.name">

                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="columnType" :resizable="false"  label="字段类型" :span="3">
                        <template slot-scope="scope">
                            <span v-if="!scope.row.addNew&&interfaceDetailShow" >
                                {{ scope.row.columnType }}
                            </span>
                            <el-select v-else class="pct100" v-model="scope.row.columnType">
                                <el-option
                                    v-for="item in columnTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    
                    <!-- <el-table-column prop="defaultValue" label="默认值" :span="3">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.defaultValue" :disabled="interfaceDetailShow"></el-input>
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="deleteRow" width="90" align="center" v-if="!interfaceDetailShow" label="操作">
                        <template slot-scope="scope">
                            <span class="" >
                                <dg-button
                                        title="删除"
                                        type="text"
                                        @click="deleteLast(scope.$index, scope.row)"
                                        size="medium"
                                        icon="el-icon-delete"
                                ></dg-button>
                            </span>
                        </template>
                    </el-table-column>
            </el-table>

        </div>
</template>
<script lang="js" src="./add-interface.js"></script>
<style lang="less" scoped src="./add-interface.less"></style>
