
.dataTree {
  width: 230px;
  padding: 10px;
  background: #fff;
  border: 1px solid #ddd;
  height: calc(100% - 88px);
}

.ce-tree {
  margin-top: 12px;
  height: calc(100% - 50px);
  overflow: auto;
}

.ce-tree__menu {
  position: fixed;
  top: 0;
  min-width: 80px;
  text-align: left;
  border: 1px solid #ccc;
  background: #fff;
  padding: 0;
  z-index: 100;
  box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
  border-radius: 2px;
}

.ce-tree__menu li {
  cursor: pointer;
  list-style: none outside none;
  font-size: 12px;
  white-space: nowrap;
  border-bottom: 1px solid #eee;
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  color: #666;
}

.ce-tree__menu li:hover {
  color: @font-color;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.node-label {
  max-width: 166px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-label::before {
  padding-right: 5px;
}