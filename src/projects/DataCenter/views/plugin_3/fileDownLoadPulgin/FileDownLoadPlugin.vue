<template>
	<div class="ce-plug_cont" v-loading="settings.loading">
			<el-tabs v-model="activeName" type="" class="">
					<el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
				</el-tabs>
				<el-form class="attrForm" v-if="activeName === 'fileConfig'" label-width="160px" label-position="right">
					<el-form-item required>
						<span slot="label">输出文件类型:</span>
						<el-select v-model="fileOutputMeta.fileType" placeholder="请选择"  >
							<el-option
								v-for="item in fileTypes"
								:key="item.value"
								:label="item.label"
								:value="item.value">
							</el-option>
						</el-select>
					</el-form-item>
					<el-form-item required>
						<span slot="label">文件名称:</span>
						<el-input v-model.trim="fileOutputMeta.fileReName"
									placeholder="最多20个字符"
									maxlength="20"
									>
							<template slot="append">{{behindText}}</template>
						</el-input>
						<div class="el-upload__tip">请保存后，执行完方案再下载！{{outPutSize === 0 ? '全部下载' : `下载到本地最大不超过${outPutSize}条`}}</div>
					</el-form-item>

				</el-form>

			<div class="ce-table-tab_attr" v-else-if="activeName === 'filedColumn' ">
				<div class="mb10">
					<el-button type="primary" @click="addRuleEdit">手动添加</el-button>
					<el-button type="primary" @click="autoGet" >自动获取</el-button>
				</div>
				<common-table
						ref="leftCommonTable"
						height="calc(100% - 2rem - 10px)"
						:columns="selectColumns"
						:pagination="false"
						:data="fileOutputMeta.cicadaFieldListMetas"
					>
						<template slot="fieldName" slot-scope="scope">
							<div :title="scope.row.columnCode">
								<el-select v-model="scope.row.fieldName"
											@change="changeOptions(scope.$index, scope.row.fieldName)">
									<el-option
										v-for="(item, index) in options"
										:key="index"
										:label="item.label"
										:value="item.value"
										:disabled="item.disabled"
									>
									</el-option>
								</el-select>

							</div>
						</template>
						<template slot="fieldZhName" slot-scope="scope">
							<div :title="scope.row.columnCode">
								<el-input
									size="mini"
									v-model="scope.row.fieldZhName">
								</el-input>
							</div>
						</template>
						<template slot="operator" slot-scope="scope">
							<el-popconfirm
								size="mini"
								v-for="(col , inx) in ruleOperateIcon"
								:key="inx"
								title="确认删除?"
								@confirm="col.clickFn(scope.row, scope.$index )"
							>
								<i
									slot="reference"
									class="icon ce_link model_status"
									:key="inx"
									:title="col.label"
									v-html="col.icon"
								></i>
							</el-popconfirm>
						</template>
					</common-table>
			</div>

			<a :href="hrefs"  ref="dl" ></a>
		<pre-view ref="preview"></pre-view>
	</div>
</template>

<script>
	// import SettingSenior from '../component/SettingSenior'
	import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
	import {common} from "@/api/commonMethods/common"
	import {commonMixins} from "@/api/commonMethods/common-mixins"
	import {servicesMixins} from "../service-mixins/service-mixins";
	import CommonTable from "../../../../../components/signed-components/dg-common-table/CommonTable";
	import {globalBus} from "@/api/globalBus";

	export default {
		name: "fileOutputMeta",
		mixins: [common,commonMixins, servicesMixins],
		props: {
			sourceData: Object,
			rowData : Object
		},
		components: {
			CommonTable,
			PreView
		},
		watch: {
			"fileOutputMeta.fileType"(val) {
					if(val === "csv") {
						this.behindText = ".csv";
					}
					else {
						this.behindText = ".xlsx";
					}
			}
		},
		data() {
			return {
				copyfileOutputMeta:[],
				downIsOk: false,
				hrefs: "#",
				fileOutputMeta: {
					fileType: "",
					fileReName: "",
					cicadaFieldListMetas: []
				},
				fileTypes: [],
				behindText: ".xlsx",
				tableCopy:[],
				options: [],
				activeName : "fileConfig",
				tab_panel : [
					{
						label: '文件设置',
						name: 'fileConfig',
					},
					{
						label: "字段列表",
						name: "filedColumn",
					}
				],
				attrsList: [
					{
						label: '文件设置',
						type: 'fileConfig',
						value: ''
					},
					{
						label: "字段列表",
						type: "filedConfig",
						value: ''
					}
				],
				selectColumns: [
					{prop: 'fieldName', label: '字段名称',width:150},
					{prop: 'fieldZhName', label: '字段中文名称',},
					{prop: 'operator', label:'操作',width:70}
				],
				ruleOperateIcon: {
					delete: {
						label: '删除',
						icon: '&#xe847;',
						clickFn: this.deleteFn,
					},
				},
				btnActive: 0,
				btnValue: '',
				buttons: [
					{
						name: '保存',
						clickFn: this.saveFn,
						type: 'primary'
					},
					{
						name: '下载',
						clickFn: this.downLoad,
						type: 'primary',
					}
				],
				pluginMeta: {},
				loading: false,
				outPutSize:0,
			}
		},
		computed: {
			plug_data() {},
		},
		methods: {
			downLoad() {
				if(this.hrefs!== '#') {
					const vm = this, {pluginServices, pluginMock, settings} = this;
					let services = vm.getServices(pluginServices, pluginMock);
					services.getDownloadIsOk(vm.sourceData.id).then(res => {
						if(res.data.status === 0) {
							if(res.data.data.downloadIsOk === true) {
								this.$refs.dl.click();
							}
							else {
								this.$message.warning("请保存后，等方案执行完后再下载");
							}
						}
					})

				}
				else {
					this.$message.warning("请保存后，等方案执行完后再下载");
				}
			},
			autoGet() {
				const vm = this, {pluginServices, pluginMock, settings} = this;
				let services = vm.getServices(pluginServices, pluginMock);
				services.getFields(vm.sourceData.id).then(res => {
					if(res.data.status === 0) {
						vm.fileOutputMeta.cicadaFieldListMetas = [];
						vm.options = [];
						vm.tableCopy = [];
						for(let i = 0; i < res.data.data.inputColumn.length; i++) {
							vm.options.push({
								label: res.data.data.inputColumn[i].columnName,
								value: res.data.data.inputColumn[i].columnName,
								disabled: true,
							})
							vm.fileOutputMeta.cicadaFieldListMetas.push({
								fieldName: res.data.data.inputColumn[i].columnName,
								fieldZhName: (res.data.data.inputColumn[i].columnZhName === "" || res.data.data.inputColumn[i].columnZhName=== undefined || res.data.data.inputColumn[i].columnZhName === null)? res.data.data.inputColumn[i].columnName : res.data.data.inputColumn[i].columnZhName,
							})
							vm.tableCopy[i] = res.data.data.inputColumn[i].columnName;
						}
						vm.copyfileOutputMeta = JSON.parse(JSON.stringify(vm.fileOutputMeta.cicadaFieldListMetas));
					}
				})
			},
			changeOptions(row, type) {
				this.fileOutputMeta.cicadaFieldListMetas[row].fieldZhName = type;
				for(let i = 0; i < this.copyfileOutputMeta.length; i++) {
					if(this.copyfileOutputMeta[i].fieldName === type) {
						this.fileOutputMeta.cicadaFieldListMetas[row].fieldZhName = (this.copyfileOutputMeta[i].fieldZhName !== "" &&
								this.copyfileOutputMeta[i].fieldZhName !== undefined) ?
								this.copyfileOutputMeta[i].fieldZhName : type;

					}
				}

				if(this.tableCopy.length === 0) {
					this.tableCopy[row] = type;
				}
				else {
					for(let i = 0; i < this.options.length; i++) {
						if(this.tableCopy[row] === this.options[i].value) {
							this.options[i].disabled = !this.options[i].disabled;
						}
					}
					this.tableCopy[row] = type;
				}
				for(let i = 0; i < this.options.length; i++) {
					if(this.tableCopy[row] === this.options[i].value) {
						this.options[i].disabled = !this.options[i].disabled;
					}
				}
			},

			deleteFn(row,index) {
					if(row.fieldName !== undefined) {
						for(let i = 0; i < this.options.length; i++) {
							if(row.fieldName === this.options[i].value) {
								this.options[i].disabled = !this.options[i].disabled;
							}
						}
					}
					this.tableCopy.splice(index, 1);
					this.fileOutputMeta.cicadaFieldListMetas.splice(index, 1);
			},

			addRuleEdit() {
				if(this.fileOutputMeta.cicadaFieldListMetas.length + 1 > this.options.length) this.$message.warning("字段大于选项数量");
				else {
					this.fileOutputMeta.cicadaFieldListMetas.push({
						fieldName: '',
						fieldZhName: '',
					});
				}
			},

			preview(){
				this.$refs.preview.show(this.rowData, this.sourceData);
			},

			setFileTypes(val) {
				this.fileTypes = [];
				for(let i = 0; i < val.length; i++) {
					let d = {
						label : val[i],
						value: val[i]
					}
					this.fileTypes.push(d);
				}
			},

			setInputColumn(val) {
				let vm = this;
				vm.options = [];
				for(let i = 0; i < val.length; i++) {
					let d = {
						label : val[i].columnName,
						value: val[i].columnName,
						disabled: false
					}
					vm.options.push(d);
				}
			},
			initData2() {
				const vm = this, {pluginServices, pluginMock, settings} = this;
				let services = vm.getServices(pluginServices, pluginMock);
				services.getFileOutputPluginPage(vm.sourceData.id).then(res=> {
					if(res.data.status === 0) {
						vm.outPutSize = res.data.data.fileOutputMeta ?  res.data.data.fileOutputMeta.outPutSize : 0;
						if(res.data.data.fileOutputMeta.fileType !== undefined && res.data.data.fileOutputMeta.fileType!= null) {
							vm.fileOutputMeta = res.data.data.fileOutputMeta;
							vm.copyfileOutputMeta = JSON.parse(JSON.stringify(vm.fileOutputMeta.cicadaFieldListMetas));
							vm.fileOutputMeta.cicadaFieldListMetas.sort(function (a, b) {
								return a.fieldNo - b.fieldNo;
							})
							vm.fileOutputMeta.fileReName = vm.fileOutputMeta.fileReName.split(".")[0];
							vm.tableCopy = [];
							let a = vm.fileOutputMeta.cicadaFieldListMetas;
							for(let i = 0; i < a.length; i++) {
								for(let j = 0; j < vm.options.length; j++) {
									if(vm.options[j].value === a[i].fieldName) {
										vm.tableCopy[i] = a[i].fieldName;
										vm.options[j].disabled = true;
									}
								}
							}
						}
						else {
							vm.autoGet();
						}
					}
				})
			},
			async initData() {
				const vm = this, {pluginServices, pluginMock, settings} = this;
				let services = vm.getServices(pluginServices, pluginMock);
				await services.getFileTypeAndFields(vm.sourceData.id).then(res=> {
					if(res.data.status === 0) {
						vm.setFileTypes(res.data.data.fileType);
						vm.setInputColumn(res.data.data.inputColumn);
						vm.initData2();
					}
				})

			},
			judgeNull() {
				for(let i = 0; i < this.fileOutputMeta.cicadaFieldListMetas.length; i++) {
					if(this.fileOutputMeta.cicadaFieldListMetas[i].fieldName === "" || this.fileOutputMeta.cicadaFieldListMetas[i].fieldName === undefined ) return true;
					if(this.fileOutputMeta.cicadaFieldListMetas[i].fieldZhName === "" || this.fileOutputMeta.cicadaFieldListMetas[i].fieldZhName === undefined ) return true;
				}
				return false;
			},
			save(saveName,showTip) {
				if(this.judgeNull()) {
					this.$message.warning("请不要留有空字段")
					return;
				}
				if(this.fileOutputMeta.fileType === "" || this.fileOutputMeta.fileType === undefined || this.fileOutputMeta.fileReName === "" || this.fileOutputMeta.fileReName === undefined) {
					this.$message.warning("请选择文件类型或输入文件名称");
				}
				else {
					const vm = this, {pluginServices, pluginMock, settings} = this;
					settings.loading = true;
					let services = vm.getServices(pluginServices, pluginMock);
					for(let i = 0; i < this.fileOutputMeta.cicadaFieldListMetas.length; i++) {
						this.fileOutputMeta.cicadaFieldListMetas[i].fieldNo = i;
						this.fileOutputMeta.cicadaFieldListMetas[i].fieldZhName = this.fileOutputMeta.cicadaFieldListMetas[i].fieldZhName === "" ? this.fileOutputMeta.cicadaFieldListMetas[i].fieldName : this.fileOutputMeta.cicadaFieldListMetas[i].fieldZhName;
					}
					this.fileOutputMeta.fileReName = this.fileOutputMeta.fileReName + this.behindText;
					services.saveFileOutputPlugin(vm.sourceData.id, vm.fileOutputMeta,settings).then(res=> {
						if(res.data.status === 0) {
							vm.fileOutputMeta.fileReName = vm.fileOutputMeta.fileReName.split(".")[0];
							showTip && vm.$message.success("保存成功");
							if(saveName)saveName();
							vm.$emit("setPreviewAble");
						}
					})
				}
			},

			initSettingSenior() {
				this.$refs.settingSenior.initValue();
			},

			saveColumn() {
				this.$refs.settingSenior.saveFieldTable();
			},

			jobSuccess() {
				globalBus.$on('fileDownLoadsuccess',state => {
						let url = this.$axios.defaults.baseURL;
						this.hrefs = url + "/plugin/fileOutput/downloadFile?tranStepId=" + this.sourceData.id;
				});
			},
			destyjob() {
				globalBus.$off('fileDownLoadsuccess',state=>{})
			}
		},
		mounted() {
			this.initData();
			this.jobSuccess();
		},
		destroyed() {
			this.destyjob();
		}

	}
</script>

<style scoped lang="less">


	.ce-operation_box {
		display: flex;
		justify-content: center;
	}

	.ce-label_title {
		color: @font-color;
	}

	.ce-cont_pd {
		padding: 0 10px;
	}

	.ce-cont_pd /deep/ .el-input-group__append {
		padding: 0 10px;
	}

	.icon__btn {
		color: @font-color;
		cursor: pointer;
	}

	.icon__btn_delete {
		color: #f56c6c;
		cursor: pointer;
	}
	.upload-demo {
		display: inline-block;
		margin-left: 20px;
	}
	.fileConfig {
		float: left;
	}
	.is-require::before {
		content: '*';
		color: #F56C6C;
		margin-right: 4px;
	}
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
