<!--
    linxp
    2021.06.09
 -->
<template>
    <div class="function-detail" v-loading="loadParams.loading">
        <div class="function-detail_input">
            <el-input v-model.trim="keyword" v-input-limit:trim placeholder="搜索" suffix-icon="el-icon-search"></el-input>
        </div>
        
        <div class="function-detail__tree">
            <component ref="list" :is="active" :loadParams="loadParams" :componentType="componentType"></component>
        </div>
    </div>
</template>
<script>
import ComponentPanel from "./component-panel";
import DatasetPanel from "./dataset-panel";
import ThemePanel from "./theme-panel"
import ModelServe from "./model-serve"
import MyStore from "./my-store"
export default {
    name: "function-detail",
    components: {
        ComponentPanel,
        DatasetPanel,
        ThemePanel,
        ModelServe,
        MyStore
    },
    props: {
        active: {
            type: String,
            default: "ComponentPanel"
        },
        componentType: String
    },
    data() {
        return {
            keyword: "" ,
            loadParams : {
                loading : false
            }
        };
    },
    watch : {
        keyword(val){
            this.$refs.list && this.$refs.list.setFilterText(val);
        },
        active(val){
            this.keyword = "";
            this.loadParams.loading = false;
        }
    }
};
</script>
<style lang="less" scoped>
.function-detail {
    display: flex;
    flex-direction: column;
    padding: 12px 0;
    overflow: hidden;
    flex: 1;
    &_input {
        padding: 0 14px;
    }
    &__tree {
        padding: 14px 0;
        flex: 1;
        overflow: auto;
    }
}
</style>
