<template>
    <div v-loading="settings.loading">
        <div>
            <span>数据源:</span>
            <!--<el-select style="margin-left: 10px" v-model="selectSchema" placeholder="请选择">-->
                <!--<el-option-->
                  <!--v-for="item in schemaList"-->
                  <!--:key="item.value"-->
                  <!--:label="item.userName"-->
                  <!--:value="item.schemaId">-->
                <!--</el-option>-->
            <!--</el-select>-->
            <el-input style="width:250px;margin-left: 10px" v-model="schemaList.userName" disabled></el-input>

            <el-input
                  v-model="serachText"
                  size="mini"
                  style="width:250px;margin-left: 300px"
                  placeholder="请输入数据对象名称"
                  @change="searchCode"
            >
                <el-button type="primary" slot="append" class="ce-search_button" icon="el-icon-search" @click="searchCode"></el-button>
            </el-input>
        </div>
        <div style="margin-top: 20px">
            <common-table
              :data="tableList"
              :columns="tHeadData"
              paging-type="client"
              row-key="code"
              @selection-change="handleChangeLeft"
            >
            </common-table>
        </div>
    </div>
</template>

<script>
    import {servicesMixins} from "../service-mixins/service-mixins";
    import {commonMixins} from "@/api/commonMethods/common-mixins"

    export default {
        name: "NewDataObject",
        props: {
            treeNode: Object,
        },
        mixins : [commonMixins , servicesMixins]  ,
        data() {
            return {
                dataSetVo: {
                    additionalRecordFlag: false,
                    dataObjectClassify: "",
                    dataObjectTableList: [],
                    dataSourceId: "",//deployed_soft_id
                    dataSourceName: "",
                    dbType: "",
                    instanceId: "",
                    key: ""
                },
                tHeadData: [
                    {type: 'selection' , width: 50, reserveSelection: true},
                    {
                        prop : "code" ,
                        label : "数据对象名",
                        minWidth : "120" ,
                        align : "center"
                    },{
                        prop : "name" ,
                        label : "中文名称",
                        minWidth : "120" ,
                        align : "center"
                    },],
                tableList: [],
                tableListcopy: [],
                selectSchema: "",
                schemaList: {},
                ids : [],
                paginationProps : {
                    currentPage: 1,
                    pageSizes: [10, 20],
                    pageSize: 10,
                    layout: "total, sizes, prev, pager, next, jumper"
                },
                serachText: "",
                typeList: ['hbase','Elasticsearch','ELASTICSEARCH']
            }
        },
        watch: {
            selectSchema(val) {
                this.queryPendingRegisterDataObj(val);
            }
        },
        methods: {
            handleChangeLeft(val) {
                this.dataSetVo.dataObjectTableList = [];
                for(let i = 0; i < val.length; i++) {
                    this.dataSetVo.dataObjectTableList.push(val[i].code);
                }
            },
            searchCode() {
                this.tableList = this.filterText(this.tableListcopy);
            },
            filterText(val) {
                let res = val.filter((v) => {
                    if(v.code.toUpperCase().search(this.serachText.toUpperCase()) !== -1 ) {
                        return true;
                    }
                    else if(v.name !== null) {
                        if(v.name.toUpperCase().search(this.serachText.toUpperCase()) !== -1) return true;
                        else return false;
                    }
                    else {
                        return false;
                    }
                })
                return res;
            },
            initData() {
                const vm = this , {listApi , listMock } = this;
                let service = vm.getServices(listApi , listMock);
                let id = vm.treeNode.catalogId;
                vm.dataSetVo.dbType = vm.treeNode.instanceType;
                let flag = 1;
                for(let i = 0;i < vm.typeList.length; i++ ) {
                    if(vm.treeNode.instanceType === vm.typeList[i]){
                        id = vm.treeNode.schemaId;
                        flag = 0;
                    }
                }
                service.getSchema(id).then(res=> {
                    if(res.data.status === 0) {
                        if(res.data.data.list !== null && res.data.data.list!== undefined){
                            vm.schemaList = res.data.data.list[0];
                            vm.queryPendingRegisterDataObj(vm.schemaList.schemaId);
                        }
                        else vm.queryPendingRegisterDataObj(id);

                    }
                });

                vm.setDataSetVo();
            },
            setDataSetVo() {
                this.dataSetVo.dataSourceName = this.treeNode.code;
                this.dataSetVo.dataSourceId = this.treeNode.catalogId;
                if(this.dataSetVo.dbType === 'hbase') this.dataSetVo.dataSourceId = this.treeNode.schemaId;
                this.dataSetVo.instanceId = this.treeNode.schemaId ? this.treeNode.schemaId : this.treeNode.instanceId;
            },
            queryPendingRegisterDataObj(val) {
                const vm = this , {listApi , listMock ,settings} = this;
                let service = vm.getServices(listApi , listMock);
                settings.loading = true;
                service.queryPendingRegisterDataObj(val,vm.dataSetVo.dbType,settings).then(res=> {
                    if(res.data.status === 0) {
                        vm.dataSetVo.key = res.data.data.key;
                        vm.tableListcopy = res.data.data.tableList;
                        vm.tableList = vm.filterText(vm.tableListcopy);
                    }
                })
            },
            saveDataObj(roles){
                // let frame = document.getElementById('iframe_source');
                // frame.contentWindow.postMessage(null,'*');
                const vm = this , {listApi , listMock ,settings} = this;
                if(roles.userVos.length === 0 && roles.roleVos.length === 0){
                    vm.$message.warning("请选择角色或用户");
                }
                let service = vm.getServices(listApi , listMock);

                service.saveDataObj(vm.dataSetVo).then(res=> {
                    if(res.data.status === 0) {
                        let rtvalue = [];
                        for(let i = 0; i < res.data.data.length; i++) {
                            rtvalue.push(res.data.data[i].dataObjId);
                        }
                        vm.saveDataSetObject(rtvalue);
                    }
                })
            },
            receiveMessage(e) {
                this.saveDataSetObject(e.data.data);
            },
            saveDataSetObject(val) {
                if(val === undefined){
                    return;
                }
                if( val === null){
                    this.$message.warning("请返回上一步勾选数据对象");
                    this.$emit("closeLoading");
                    return ;
                }
                this.$emit("closeDialog" , val);
            },
        },
        mounted() {
            this.initData();
        }
    }
</script>