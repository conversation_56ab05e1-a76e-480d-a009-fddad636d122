<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/4/18
-->
<template>
    <ce-list-table
            ref="table"
            :filter-text.sync="filterText"
            :t-head-data="tHeadData"
            :table-data="tableData"
            @changePage="changePage"
            :operate-icons="operateIcons"
            :filter-placeholder="filterPlaceholder"
            :load-params="settings"
    >
        <template slot="search">
            <el-form class="list_form" inline>
                <el-form-item v-for="(item, k) in queryForm" :key="k" :label="item.label">
                    <dg-select v-model="item.value" v-if="item.type==='select'"
                               clearable
                               @change="queryList()"
                               :data="item.data()"
                               :placeholder="selPlaceholder(item.label)"
                    ></dg-select>
                </el-form-item>
            </el-form>
        </template>
        <template #status="{row}">
            <div :style="{'color' : auditStateForKey(row.audit_state , 'color')}">
                <i :class="auditStateForKey(row.audit_state , 'icon')"></i>
                <span class="ml5">{{ auditStateForKey(row.audit_state , 'label') }}</span>
            </div>
        </template>
    </ce-list-table>
</template>

<script>
import marketUse from "@store/modules/market/market-constant"
import CeListTable from "dc-front-plugin/src/components/CeListTable/CeListTable.vue"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {SaveModel} from "./save-model"
import {common} from "dc-front-plugin/src/api/commonMethods/common"
import {mapGetters} from "vuex";

CeListTable.mixins = [...CeListTable.mixins, commonMixins];
export default {
    name: "applyMessage",
    mixins: [marketUse, commonMixins , SaveModel ,common],
    components: {
        CeListTable,
    },
    computed: {
        selPlaceholder() {
            return (text) => `请选择${text}`
        },
        auditStateForKey() {
            return (val , k) => {
                let state = {};
                for (let it of this.m_market_audit_state) {
                    state[it.value] = it[k];
                }
                return state[val] || "";
            }
        },
        ...mapGetters(["userInfo"]),
    },
    data() {
        return {
            filterText: '',
            tHeadData: [
                {
                    label: '申请类型',
                    prop: 'applyType',
                    align: 'center',
                },
                {
                    label: '资源类型',
                    prop: 'resourceType',
                    align: 'center',
                }, {
                    label: '资源名称',
                    prop: 'resource_name',
                    align: 'center',
                }, {
                    label: '申请日期',
                    prop: 'apply_time',
                    align: 'center',
                }, {
                    label: '审批状态',
                    prop: 'status',
                    align: 'center',
                },
                {
                    label: '操作',
                    prop: 'operate',
                    align: 'center',
                },
            ],
            tableData: [],
            operateIcons: [
                {
                    name: '资源详情',
                    clickFn: this.detail,
                    show: (row) => row.apply_type === "0" ? row.audit_state !== "1" : true,
                    condition: (row, right) => true,
                }, {
                    name: '审批详情',
                    clickFn: this.approvalDetail, // 已审批状态才出现审批详情 0:审批中 1：审批通过 2：审批未通过
                    show: (row) => this.m_market_has_audit(row.audit_state),
                    condition: (row, right) => true,
                }, {
                    name: '结果确认',
                    clickFn: this.resultConfirm, // 结果确认按钮只有在已审批状态才出现 结果确认后不再出现结果确认按钮
                    show: (row) => this.m_market_is_audit_pass(row.audit_state) && this.m_market_not_result(row.audit_result_state) && this.userInfo && row.apply_user_id === this.userInfo.id, // 0 未确认 1 已确认
                    condition: (row, right) => true,
                },
            ],
            filterPlaceholder: "请输入资源名称搜索",
            queryForm: {
                applyType: {
                    label: '申请类型',
                    type: 'select',
                    data: () => this.m_market_apply_type,
                    value: "",
                },
                auditState: {
                    label: '审批状态',
                    type: 'select',
                    data: () => this.m_market_audit_state,
                    value: "",
                }
            }
        }
    },
    methods: {
        changePage(inx, size) {
            const vm = this, {queryForm, filterText, settings} = this;
            const params = {
                type: 'APPLY', // 申请列表
                applyType: queryForm.applyType.value,
                resourceName: filterText,
                auditState: queryForm.auditState.value,
                pageNum: inx,
                pageSize: size,
            };
            settings.loading = true;
            vm.tableData = [];
            this.$services('market').resourceApplyList(params, settings).then(res => {
                if (res.data.status === 0) {
                    const result = res.data.data;
                    vm.tableData = result.dataList.map(li => {
                        let type = vm.m_market_apply_type.find(i => i.value === li.apply_type);
                        li.applyType = type && type.label || "";
                        let rType = vm.m_market_get_model_types().find(i => i.value === li.resource_type);
                        li.resourceType = rType && rType.label;
                        return li;
                    });
                    if (vm.$refs.table) vm.$refs.table.total = result.totalCount;
                }
            })
        },
        queryList() {
            this.$refs.table.changePage();
        },
        // 资源详情
        detail(row) {
            let routeData = this.$router.resolve({path: '/market/modelMarket', query: {id: row.model_id}});
            window.open(routeData.href, '_blank');
        },
        // 审批详情
        approvalDetail(row) {
            const layer = this.$dgLayer({
                title: '审批详情',
                content: require("../dialog/applyDetail.vue"),
                maxmin: false,
                move: false,
                area: ["800px", "630px"],
                noneBtnField: true,
                props: {
                    row
                }
            })
        },
        // 结果确认
        resultConfirm(row) {
            if(row.apply_type === "0"){
                this.issueConfirm(row);
                return;
            }
            const {
                modelTypeName,
                showItems,
                loadTreeInter,
                saveInter,
                treeWrapHeight,
            } = this.getModelSaveData(row);
            const vm = this;
            const layer = this.$dgLayer({
                title : `${modelTypeName}另存为`,
                content : require("@/components/signed-components/save-model"),
                area:["800px","650px"],
                move:false,
                maxmin:false,
                props:{
                    modelTypeName,
                    saveInter,
                    showItems,
                    loadTreeInter,
                    treeWrapHeight,
                    confirmInter : ()=> this.confirmInter(row),
                },
                on:{
                    submit(){
                        layer.close(layer.dialogIndex);
                        vm.queryList();
                    }
                }
            })

        },
        // 模型发布 的确认
        issueConfirm(row){
            const vm = this;
            this.confirm("结果确认","提交结果确认?",async ()=>{
                await vm.confirmInter(row);
                vm.$message.success("已确认");
                vm.queryList();
            })
        },
        confirmInter(row){
            return this.$services('market').auditResultConfirm({id : row.id});
        },
        /**
         * 获取模型 保存的逻辑数据
         */
        getModelSaveData(row){
            let state = {
                "TRANS" : ()=> this.getTransForSaveProps(row),
                "DASHBOARDS" : ()=> this.getVisualProps(row),
                "PORTAL" : ()=> this.getPortalProps(row),
            };
            return state[row.resource_type] && state[row.resource_type]();
        }

    },
    mounted() {
        this.queryList();
    }
}
</script>

<style scoped lang="less">

</style>
