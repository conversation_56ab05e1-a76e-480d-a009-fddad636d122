<template>
    <div class="chartCard" >
        <ve-radar 
            :data="chartData"
            height="100%"
            width="100%"
            :judge-width="true"
            :extend="extend"
            :settings="chartSettings"
            :data-empty="data_empty"
            v-loading="loading"
            :events="chartEvents"
            ref="chart"
            ></ve-radar>
    </div>
</template>

<script src="./radar-card.js"></script>
<style scoped lang="less" src="../css/chart.less"></style>
<style scoped lang="less" src="./radar-card.less"></style>