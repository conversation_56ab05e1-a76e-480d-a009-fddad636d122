<template>
    <common-dialog
            custom-class="preview"
            :visible.sync="preview.isVisible"
            :fullscreen="dialogFullScreen"
            width="1200px"
            @closed="reSetLoading"
    >
        <div slot="title" class="custom-dialog__title">
            <span class="el-dialog__title">预览数据</span>
            <div class="custom-dialog__menu" @click="fullChange">
                <i :class="dialogFullScreen ? 'dg-icon-scalescreen':'dg-icon-fullscreen'"></i>
            </div>
        </div>
        <div class="preview_h" :data-full="dialogFullScreen">
            <preview-page v-if="reNew" ref="preview"></preview-page>
        </div>

    </common-dialog>
</template>

<script>

import previewPage from "./preview-page";
import {dialog} from "@/api/commonMethods/dialog-mixins";

export default {
    name: "PreView",
    components: {
        previewPage
    },
    mixins: [dialog],
    data() {
        return {
            preview: {
                isVisible: false,
                list: [],
                columns: [],
            },
            dialogFullScreen:false,
        }
    },
    methods: {
        fullChange(){
            this.dialogFullScreen = !this.dialogFullScreen;
        },
        reSetLoading() {
            this.$refs.preview.reSetLoading();
            this.clearData();
            let services = this.$services('modeling');
            let {createTimeId} = this.$refs.preview;
            services.stopJob(createTimeId);
        },
        /**
         * 所有插件统一预览方法
         */
        show(row, node, columnInfo) {
            this.preview.isVisible = true;
            this.reNew = true;
            this.$nextTick(() => {
                this.$refs.preview.show(row, node, columnInfo);
            })
            /* let services = this.$services('modeling');
             let layer = this.$dgLayer({
                 title: "预览数据",
                 content: require("./preview-page"),
                 move:false,
                 area: ["1200px", "602px"],
                 cancel(){
                     let {createTimeId , previewSuccess} = layer.$children[0];
                     services.stopJob(createTimeId);
                 },
                 noneBtnField: true,
             });
             layer.$children[0].show(row,node, columnInfo);*/
        },

    }
}
</script>

<style scoped lang="less">
.custom-dialog {
    &__menu {
        position: absolute;
        font-size:16px;
        line-height:1.5;
        cursor: pointer;
        color: @fontDesColor;
    }
}
@media screen and (max-width: 1365px) {
    .preview_h {
        height: calc(9 * (1.5rem + 16px) + 22px + 2rem);
        &[data-full=true] {
            height:calc(100vh - 42px - 36px);
        }
    }
    .custom-dialog__menu {
        top : 10px;
        right: 50px;
    }
}

@media screen and (max-width: 1680px) and (min-width: 1366px) {
    .preview_h {
        height: calc(9 * (1.5rem + 24px) + 22px + 2rem);
        &[data-full=true] {
            height:calc(100vh - 49px - 42px);
        }
    }
    .custom-dialog__menu {
        top : 14px;
        right: 54px;
    }
}

@media screen and (min-width: 1681px) {
    .preview_h {
        height: calc(9 * (1.32rem + 28px) + 22px + 2rem);
        &[data-full=true] {
            height:calc(100vh - 56px - 48px);
        }
    }
    .custom-dialog__menu {
        top : 18px;
        right: 58px;
    }
}


</style>
