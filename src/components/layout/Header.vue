<template>
    <div class="header">
        <div class="logo">{{sysName}}</div>
        <span class="edition">{{this.$edition}}</span>
        <div class="l-main-header__center">
            <div class="menus selectn">
                <div class="menus__item"
                     v-for="(menu,index) in h_menu"
                     :class="{'is-active':menuActive === menu.route}"
                     :key="index"
                     @click="handleClickMenu(menu,index)"
                >
                    <i class="iconfont" v-html="menu.icon"></i>
                    <span>{{menu.name}}</span>
                </div>
                <div class="un-line" :style="{transform: `translateX(${activeMenuInx * 110}px)`}"></div>
            </div>
        </div>
        <ul class="ce-user__list">
            <li>
                <div class="ce-user__img">
                    <i class="icon f18">&#xe690;</i>
                    <span class="ml6">{{userName}}</span>
                </div>
            </li>
            <li>
                <a class="ce-logout" title="退出" href="javascript:void(0);" @click="loginOut">
                    <i class="icon">&#xe6f7;</i>
                </a>
            </li>
        </ul>
    </div>
</template>

<script>
    import {mapGetters} from "vuex"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import sameFn from "@/api/isSameArrayObject"

    export default {
        name: "Header",
        props: {
            user: Object,
            sysName: String ,
            activeTab : String
        },
        mixins: [commonMixins],
        data() {
            let loginMock = require("../../projects/DataCenter/mock/login/login-mock"),
                loginServices = require("../../projects/DataCenter/services/login/login-services");
            return {
                loginServices, loginMock,
                timer: null,
                menus: [
                    {
                        name: "业务空间",
                        icon: "&#xe803;",
                        route: "business"
                    },
                    {
                        name: "建模空间",
                        icon: "&#xe7fd;",
                        route: "modeling"
                    },
                    {
                        name: "数据空间",
                        icon: "&#xe802;",
                        route: "dataSpace"
                    },
                    {
                        name: "管理空间",
                        icon: "&#xe7f7;",
                        route: "manage"
                    }
                ],
                h_menu :[],
                menuActive: this.activeTab
            }
        },
        computed: {
            activeMenuInx(){
                const vm = this , {h_menu} = this;
                let inx = 0;
                if(h_menu.length){
                    for(let i = 0; i< h_menu.length; i++){
                        if(h_menu[i].route === vm.menuActive){
                            inx = i;
                            break;
                        }
                    }
                }
                return inx;

            },
            userName() {
                if (this.userInfo) {
                    return this.userInfo.objCode;
                } else {
                    return "";
                }

            },
            ...mapGetters(["userInfo"]),
        },
        methods: {
            /**
             * 设置顶部菜单
             * */
            setMenus(menus){
                const vm = this;
                vm.h_menu = vm.menus.filter(m => menus.indexOf( m.route) > -1);
            },
          /**
           * 点击菜单
           * */
            handleClickMenu(menu, idx) {
                this.menuActive = menu.route;
                this.$emit('getMenus', menu.route)
            },
          /**
           * 退出登录 提示
           * */
            loginOut() {
                this.$dgAlert("确定退出系统？", "提示", {
                    type: 'warning',
                    showCancelButton: true,
                    showConfirmButton: true
                }).then(() => {
                    this.logoutFn();
                }).catch(_ => {
                });

            },
            /**
             * 退出登录 接口
             * */
            logoutFn() {
                const vm = this;
                let {loginServices, loginMock} = this;
                let services = vm.getServices(loginServices, loginMock);
                services.logout().then(res => {
                    if (res.data.status === 0) {
                        vm.clearTimer();
                        localStorage.removeItem("userInfo");
                        localStorage.removeItem("userRight");
                        localStorage.removeItem("userRole");
                        localStorage.removeItem("sessionId");
                        vm.$store.dispatch("logout", null);
                        vm.$router.push("/login");
                    }
                })
            },
          /**
           * 登录 有效是否超时
           */
            checkLogin() {
                const vm = this;
                let {loginServices, loginMock} = this;
                let services = vm.getServices(loginServices, loginMock);
                services.isOverTime(vm.userInfo.id).then(res => {
                    if (res.data.status === 0) {
                    }
                })
            },
          /**
           * 获取用户角色信息
           */
            getUserRole() {
                const vm = this;
                let {loginServices, loginMock} = this;
                let services = vm.getServices(loginServices, loginMock);
                services.getUserRole(vm.userInfo.id).then(res => {
                    if (res.data.data === "未登录") {
                        vm.$message.error("服务器异常，请重新登录");
                        localStorage.removeItem("userInfo");
                        localStorage.removeItem("userRight");
                        localStorage.removeItem("userRole");
                        vm.logoutFn();
                        return;
                    }
                    if (res.data.status === 0) {
                        vm.checkUserRole(res.data.data);
                    } else {
                        vm.clearTimer();
                        localStorage.removeItem("userInfo");
                        localStorage.removeItem("userRight");
                        localStorage.removeItem("userRole");
                        vm.$router.push("/login");
                    }
                })
            },
          /**
           * 校验权限是否变化
           * 已启用，后端校验
           */
            checkRightChange() {
                const vm = this;
                vm.timer = setInterval(vm.getUserRole, 5000);
            },
          /**
           * 获取角色
           * @param user
           * @return {[]}
           */
            getRoleData(user) {
                let result = [], role;
                if (user.tSysAuthObjRelSet) {
                    role = user.tSysAuthObjRelSet.filter(obj => {
                        return obj.relationType === "1";
                    });
                    role.forEach(r => {
                        result.push(r.toAuthObj)
                    });
                }
                return result;
            },
            checkUserRole(role) {
                const vm = this , {userInfo} = this;
                let result = false, userI = false, roleData = localStorage.getItem("userRole");
                if (roleData === null) {
                    localStorage.setItem("userRole", JSON.stringify(role));
                } else {
                    roleData = JSON.parse(roleData);
                    let store_role = vm.getRoleData(roleData), req_role = vm.getRoleData(role);
                    if (role.objCode !== roleData.objCode) {
                        userI = true;
                    } else if (!sameFn.arrSame(store_role, req_role)) {
                        result = true;
                    }else if(userInfo.objCode !== role.objCode){
                        userI = true;
                    }


                }
                if (result && !userI) {
                    vm.clearTimer();
                    vm.$alert('角色权限已变更,请重新登录', '提示', {
                        confirmButtonText: '确定', showClose: false,
                        callback: action => {
                            vm.logoutFn();
                        }
                    });
                } else if (userI) {
                    vm.clearTimer();
                    vm.$alert('已切换登录用户', '提示', {
                        confirmButtonText: '确定', showClose: false,
                        callback: action => {
                            window.location.reload();
                            vm.$router.push("/");
                            // vm.logoutFn();

                        }
                    });
                }

            },
            clearTimer() {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        created() {
            // this.checkLogin();
            // this.checkRightChange();
        },
        destroyed() {
            this.clearTimer()
        }
    }
</script>

<style lang="less" scoped>
    @height: 4rem;

    .header {
        background: #1890ff;
        height: @height;
    }

    .logo {
        font-size: 26px;
        color: #fff;
        line-height: @height;
        min-width: 160px;
        height: @height;
        padding-left: 40px;
        background: url(../../assets/images/header/logo.png) no-repeat left center;
        margin-left: 30px;
        float: left;
    }
    .edition {
        font-size: 6px;
        color: #fff;
        line-height: @height;
        min-width: 16px;
        height: @height;
        padding-top:10px;
        float: left;
        box-sizing: border-box;
    }

    .ce-user__list {
        float: right;
        font-size: 14px;
        margin-right: 26px;
    }

    .ce-user__img {
        height: @height;
        line-height: @height;
        float: left;
    }

    .ce-logout {
        font-size: 14px;
        color: #fff;
    }

    .ce-user__img > img {
        width: 28px;
        vertical-align: top;
    }

    .ce-user__list > li {
        float: left;
        height: @height;
        line-height: @height;
        margin-left: 12px;
        color: #fff;
    }

    .menus {
        display: flex;
        position: relative;
        line-height: @height;

        .un-line {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 110px;
            height: 2px;
            background-color: #ffe00d;
            z-index: 2;
            transition: all 0.2s;
        }

        &__item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 110px;
            color: rgba(#ffffff, 0.85);
            font-size: 14px;
            cursor: pointer;

            span {
                padding-left: 4px;
            }

            &:hover {
                background-color: rgba(#ffffff, 0.09);
            }
        }

        .is-active {
            color: #ffe00d;
        }
    }

    .l-main-header__center {
        float: left;
        margin:0 50px;
    }
</style>
