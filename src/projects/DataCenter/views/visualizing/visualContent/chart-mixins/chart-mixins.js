import {globalBus} from "@/api/globalBus";
import numerify from 'numerify'
import {chartTheme} from "../../components/datasetMixins/chartTheme";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "../../service-mixins/service-mixins";

export const chartMxins = {
    data() {
        const vm = this;
        let colors = [
            "#2ec7c9",
            "#b6a2de",
            "#5ab1ef",
            "#ffb980",
            "#d87a80",
            "#8d98b3",
            "#e5cf0d",
            "#97b552",
            "#95706d",
            "#dc69aa",
            "#07a2a4",
            "#9a7fd1",
            "#588dd5",
            "#f5994e",
            "#c05050",
            "#59678c",
            "#c9ab00",
            "#7eb00a",
            "#6f5553",
            "#c14089",
            '#6ac9ff',
            '#42acff',
            '#0c84fc',
            '#3666ee',
            '#7978fa',
            '#00dbe3',
            '#f97da1',
            '#ff9e85'];
        let format = {
            "#,##0": "0,0",
            "#,##0.0": "0,0.0",
            "#,##0.00": "0,0.00",
            "#,##0.000": "0,0.000",
            "#,##0.0000": "0,0.0000",
        };
        return {
            format,
            colors: colors,
            data_empty: true,
            width: '100%',
            height: '100%',
            drillHeight: "calc(100% - 30px)",
            chartData: {},
            chartSettings: {},
            loading: false,
            showDrill: false,
            dimsName: "",
            extend: {
                title: {
                    show: true,
                    top: 'top',
                    left: 'center',
                    textStyle: {
                        color: '#666'
                    },

                },
                tooltip: {
                    show: true,
                    trigger: 'item',
                    appendToBody: true
                },
                grid: {
                    top: 66,
                    bottom: 50,
                    right: 40,
                    left: 40
                },
                legend: {},
                /*dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        filterMode: "filter",
                        maxValueSpan: 200
                    }, {
                        show: false,
                        filterMode: "filter",
                        start: 0,
                        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                        handleSize: '100%',
                        height: 16,
                        showDetail: false,
                        handleStyle: {
                            color: '#fff',
                            shadowBlur: 3,
                            shadowColor: 'rgba(0, 0, 0, 0.6)',
                            shadowOffsetX: 2,
                            shadowOffsetY: 2
                        }
                    }],*/
                series: {
                    label: {
                        show: true,
                        position: 'top'
                    }
                },
                xAxis: {
                    nameLocation: 'end',
                    nameTextStyle: {
                        align: 'left',
                        verticalAlign: 'bottom'
                    },
                    nameRotate: 0,
                    axisLine: {
                        lineStyle: {}
                    },
                    axisLabel: {
                        interval: 0,
                    },
                    position: 'bottom'

                },
                yAxis: {
                    nameLocation: 'end',
                    nameRotate: 0,
                    axisLine: {
                        lineStyle: {}
                    },
                    axisLabel: {
                        interval: 0,
                    },
                    position: 'left'
                }
            },
            timer: null,
            socket: null,
            path: "",
            lockReconnect: false, //避免ws重复连接
            heartCheck: {
                timeout: 30000,        //1分钟发一次心跳
                timeoutObj: null,
                serverTimeoutObj: null,
                reset: function () {
                    clearTimeout(this.timeoutObj);
                    clearTimeout(this.serverTimeoutObj);
                    return this;
                },
                start: function () {
                    let self = this;
                    this.timeoutObj = setInterval(function () {
                        //这里发送一个心跳，后端收到后，返回一个心跳消息，
                        //onmessage拿到返回的心跳就说明连接正常
                        if (vm.socket.readyState === 1) {
                            vm.socket.send("ping");
                        }
                        /* self.serverTimeoutObj = setTimeout(function(){//如果超过一定时间还没重置，说明后端主动断开了
                             vm.socket.close();     //如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
                         }, self.timeout)*/
                    }, this.timeout)
                },
                sendParams: () => {
                    if (this.params) {
                        vm.socket.send(vm.params);
                    }
                }
            },
            params: null,
            chartEvents: {
                click: this.chartClickFn
            },
            hasDimen: false,
            tipWidth: "80px",
            yData: [],
            xData: [],
            drillLevel: 0,
            drillFilter: [],
            showTitle: false,
            title: '',
            filterDimsName: "" ,
            filterDims : null
        }
    },
    mixins: [chartTheme, commonMixins, servicesMixins],
    props: {
        id: String,
        node: Object,
        isView: Boolean
    },
    computed: {
        titleH() {
            let height = 40;
            if (this.titleStyle && parseInt(this.titleStyle.fontSize) * 2 > 40) {
                height = parseInt(this.titleStyle.fontSize) * 2;
            }
            if (!this.showTitle) {
                height = 0;
            }
            return height;
        },
        getCode() {
            return this.node.code;
        }
    },
    watch : {
        getCode : {
            handler(val){
                this.preview();
            }
        }
    },
    methods: {
        getDataMinMax(chartData, params) {
            const vm = this;
            let res;
            if (params === 'all') {
                res = vm.forChartData(chartData);
            } else {
                let allKeys = [];
                for (let i in params.selected) {
                    if (params.selected[i] === true) {
                        allKeys.push(i);
                    }
                }
                res = vm.forChartData(chartData, allKeys);
            }
            let {min, max} = res;
            if (min < 0) {
                min -= Math.abs(Math.ceil(max / 100));
            }
            max += Math.abs(Math.ceil(max / 100));
            if (max < 200) max = 200;
            return {min, max}
        },
        backToDims() {
            const vm = this, {drillFilter} = this;
            let widget = JSON.parse(vm.node.widget);
            let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
            vm.drillLevel--;
            if (vm.drillLevel > 0 && (!nodeData.drill[vm.drillLevel - 1] || !nodeData.drill[vm.drillLevel - 1].options.length)) {
                vm.showDrill = false;
                vm.drillLevel = 0;
                vm.preview();
                return;
            }
            if (vm.drillLevel > 0) {
                vm.dimsName = drillFilter[vm.drillLevel - 1].name;
                widget.drillFilter = drillFilter[vm.drillLevel - 1].filter;
                widget.widgetDataset.widgetDatasetDimsDrill = nodeData.drillDataset[vm.drillLevel - 1];
            } else {
                vm.showDrill = false;
                vm.dimsName = "";
                widget.drillFilter = "";
                widget.widgetDataset.widgetDatasetDimsDrill = [];
            }
            vm.node.widget = JSON.stringify(widget);
            vm.preview();
        },
        setDimension(yAxis) {
            const vm = this;
            vm.hasDimen = !!yAxis.length;
        },
        /**
         * 图表点击事件(联动，钻取)
         * @param e
         * dimsCodes 用于查询周期时间 替换的字段
         */
        chartClickFn(e) {
            const vm = this, {node} = this;
            let {beforeData, linkage , widgetDataset} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
            let target = e;
            if (node.code === "MapChartWidget" || node.code === "ColourMap" || node.code === "BubbleMap") {
                target.name = vm.chartData.codeMap[target.name];
            }
            if (linkage && !vm.showDrill) {
                let linkage_p = JSON.parse(linkage);
                let dims = null;
                if(vm.filterDims) dims = vm.filterDims;
                vm.$emit("setLinkage", linkage_p, e, nodeData.classifierStatId, nodeData, node ,dims , vm.chartData.dimsCodes ,vm.chartData.formatMapping );
            }
            if (nodeData.drill) {
                vm.setDrillQuery(nodeData, e, node);
            }
        },
        setDrillQuery(nodeData, e, node) {
            let drillVal;
            const vm = this, {drillLevel} = this;
            if (!nodeData.drill[drillLevel] || !nodeData.drill[drillLevel].value.length) return;
            let d_inx = nodeData.yAxis.indexOf(nodeData.drill[0].id);
            if (node.code === "TableChartWidget") {
                drillVal = e;
            } else if (d_inx === 0) {
                drillVal = e.name;
            } else {
                drillVal = e.seriesName;
            }
            if (node.code === "RingPieChartWidget" && nodeData.drill[0].code !== e.seriesName) {
                return;
            }
            vm.showDrill = true;
            let widget = JSON.parse(vm.node.widget);
            if (drillLevel > 0) {
                widget.drillFilter = JSON.stringify([
                    ...JSON.parse(vm.drillFilter[drillLevel - 1].filter),
                    {
                        value: drillVal,
                        type: "precise",
                        parentType: "text",
                        field: nodeData.drill[drillLevel].field
                    }
                ]);
            } else {
                widget.drillFilter = JSON.stringify([{
                    value: drillVal,
                    type: "precise",
                    parentType: "text",
                    field: nodeData.drill[drillLevel].field
                }]);
            }
            widget.widgetDataset.widgetDatasetDimsDrill = nodeData.drillDataset[drillLevel];
            if (!nodeData.drillDataset[drillLevel]) return;
            vm.storeDrill(drillLevel, widget);
            vm.node.widget = JSON.stringify(widget);
            vm.preview();
        },
        storeDrill(drillLevel, widget) {
            const vm = this;
            let filter = JSON.parse(widget.drillFilter), name = [];
            filter.forEach(f => {
                name.push(f.value);
            });
            vm.dimsName = name.join(' > ');
            vm.drillFilter[drillLevel] = {
                name: name.join(' > '),
                filter: widget.drillFilter
            };
            vm.drillLevel++;
        },
        setChartExtend(settings) {
            this.setTitle(settings.title);
            this.setLegend(settings.legend);
            this.setAxis(settings.xAxis, 'xAxis');
            this.setAxis(settings.yAxis, 'yAxis');
            this.setAlias(settings.fieldSetting);
            this.setTooltip();
        },
        setAlias(field) {
            const vm = this;
            let labelMap = {};
            if (!field.options) return;
            field.options.forEach(item => {
                if (item.alias) {
                    labelMap[item.label] = item.alias;
                }
            });
            vm.chartSettings.labelMap = labelMap;
        },
        setAxis(data, axis) {
            if (!data) return;
            const vm = this, {extend, drillLevel} = this;
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);
            let transverseCode = ['TransverseStackBarChartWidget', 'TransverseBarChartWidget'];
            let axisName = transverseCode.indexOf(vm.node.code) > -1 ? "yAxis" : "xAxis";
            if (data.showName) {
                if ((transverseCode.indexOf(vm.node.code) === -1 && axis === 'xAxis' && drillLevel > 0) || (transverseCode.indexOf(vm.node.code) > -1) && axis === 'yAxis' && drillLevel > 0) {
                    extend[axis].name = nodeData.drill[drillLevel - 1].options[0].label;
                } else if (vm.node.code !== "HeatMap" && vm.filterDimsName && axis === axisName) {
                    if(vm.chartData && vm.chartData.dimsCodes&& vm.chartData.dimsCodes.length) extend[axis].name = vm.chartData.dimsCodes.join(">");
                } else {
                    extend[axis].name = data.text;
                }
                /*if(vm.node.code === "HeatMap" ){
                    if(vm.chartData && vm.chartData.dimsCodes&& vm.chartData.dimsCodes.length) {
                        let inx = axis === "xAxis" ? 0 : 1;
                        extend[axis].name = vm.chartData.dimsCodes[inx];
                    }
                }*/
            } else {
                extend[axis].name = "";
            }
            extend[axis].nameLocation = data.nameLocation;
            extend[axis].axisLine.show = data.show_axisLine;
            extend[axis].axisLine.lineStyle = {
                color: data.line_color,
                width: data.line_width,
                type: data.line_type
            };
            extend[axis].axisLabel.rotate = data.axisLabel;
            data.nameRotate !== undefined ? extend[axis].nameRotate = data.nameRotate : true;
        },
        setTooltip() {
            const node = this.node, vm = this;
            this.extend.tooltip.formatter = function (params) {
                let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
                let yField = nodeData.yData.filter((item, i) => vm.yData.indexOf(item.data.code) > -1 && vm.yData.indexOf(item.data.code) === i);
                let xField = nodeData.xData.filter((item, i) => vm.xData.indexOf(item.data.code) > -1 && vm.xData.indexOf(item.data.code) === i);
                if (nodeData.xData.length === 0 || nodeData.yData.length === 0) {
                    vm.data_empty = true;
                    vm.showDrill = false;
                    return;
                }
                if (xField.length !== nodeData.xData.length || yField.length !== nodeData.yData.length || yField.length !== vm.yData.length || xField.length !== vm.xData.length) {
                    vm.preview();
                    return;
                }
                if (vm.drillLevel > 0 && (!nodeData.drill[vm.drillLevel - 1] || !nodeData.drill[vm.drillLevel - 1].options.length)) {
                    vm.showDrill = false;
                    vm.drillLevel = 0;
                    vm.preview();
                    return;
                }
                let funcLabel = nodeData.xData[0].isRatio ? '占比' : nodeData.xData[0].func.name;
                let yLength = nodeData.yData.length - 1;
                let yLabel = nodeData.xData[0].alias || nodeData.xData[0].label,
                    xLabel = "(" + (nodeData.yData[yLength].alias || nodeData.yData[yLength].label) + ")",
                    xAxis = "(" + (nodeData.yData[0].alias || nodeData.yData[0].label) + ")";
                let result = '';
                let paramsVal;
                if(vm.chartData && vm.chartData.dimsCodes && vm.chartData.dimsCodes.length){
                    let dimInx = vm.chartData.dimsCodes.length;
                    if(dimInx === 1){
                        xLabel = `(${vm.chartData.dimsCodes[0]})`;
                    }else {
                        xAxis = `(${vm.chartData.dimsCodes[0]})`;
                        xLabel = `(${vm.chartData.dimsCodes[1]})`;
                    }
                }
                if (vm.drillLevel > 0) {
                    xLabel = "(" + nodeData.drill[vm.drillLevel - 1].options[0].label + ")";
                }
                if (node.code === "BarChartWidget" || node.code === "StackBarChartWidget" || node.code === "TransverseBarChartWidget" || node.code === "TransverseStackBarChartWidget") {
                    paramsVal = params.value;
                } else if (node.code === "LineChartWidget" || node.code === "AreaGraphWidget") {
                    paramsVal = params.value[1];
                }
                if (params.componentType !== "markPoint") {
                    if (nodeData.yAxis.length === 1 || vm.drillLevel > 0) {
                        result = `<div>\
                                        <div class="f16 mb5">${params.name + xLabel} :</div>\
                                        <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                        <span>${params.seriesName || params.name} : ${paramsVal} (${funcLabel})</span>
                                    </div>`;
                    } else if (nodeData.yAxis.length === 2) {
                        result = `<div>\
                                        <div class="f16 mb5">${params.name + xAxis} :</div>\
                                        <span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${params.color};"></span>\
                                        <span>${params.seriesName || params.name} ${xLabel} : ${paramsVal + "(" + yLabel + "-" + funcLabel + ")"}</span>
                                    </div>`;
                    }
                }
                return result;
            }
        },
        setMarklabel(setting) {
            let labelShow = setting.labelShow;
            if (labelShow === "all") {
                this.extend.series.markPoint = {};
                this.setDataLabel(setting.dataLabel);
            } else {
                this.setDataLabel(false);
                if (setting.dataLabel) {
                    this.extend.series.markPoint = {
                        symbol: 'pin',
                        symbolSize: 30,
                        data: [
                            {
                                name: '最小值',
                                type: 'min'
                            },
                            {
                                name: '最大值',
                                type: 'max'
                            }
                        ]
                    };
                } else {
                    this.extend.series.markPoint = {};
                }

            }
        },
        setLegendStyle(setting) {
            let style = setting.legendStyle;
            if (!style) return;
            if (setting.legendStyle.type !== 'default') {
                this.extend.legend = Object.assign(this.extend.legend, {
                    textStyle: {
                        color: style.color ? style.color : '#000',
                        fontSize: style.fontSize,
                        fontFamily: style.fontFamily,
                        fontWeight: style.fontWeight ? 'bolder' : 'normal',
                        fontStyle: style.fontStyle ? 'italic' : 'normal',
                    }
                });
            } else {
                this.setLegend(setting.legend);
            }
        },
        setTitleStyle(setting) {
            let style = setting.titleStyle;
            if (!style) return;
            if (style.type !== 'default') {
                this.extend.title = Object.assign(this.extend.title, {
                    textStyle: {
                        color: style.color ? style.color : '#000',
                        fontSize: style.fontSize,
                        fontFamily: style.fontFamily,
                        fontWeight: style.fontWeight ? 'bolder' : 'normal',
                        fontStyle: style.fontStyle ? 'italic' : 'normal',
                    },
                    left: style.textAlign,
                });
            } else {
                this.setTitle(setting.title);
            }

        },
        setColor(color, theme, type) {
            if (type === "theme") {
                this.extend.color = JSON.parse(JSON.stringify(this.themes[theme])) ;
            } else if (type === "custom") {
                if (color && color.length > 0) {
                    this.extend.color = JSON.parse(JSON.stringify(color)) ;
                } else {
                    this.extend.color = JSON.parse(JSON.stringify(this.colors)) ;
                }
            }
        },
        setTitleTxt(title) {
            if (!title) return;
            this.showTitle = title.show;
            this.title = title.text;
            if (title.custom_style === "default") {
                this.titleStyle = {};
            } else {
                this.titleStyle = {
                    "fontFamily": title.fontFamily,
                    "fontSize": title.fontSize,
                    "fontWeight": title.fontWeight ? title.fontWeight : "normal",
                    "fontStyle": title.fontStyle && title.fontStyle.length ? "italic" : "normal",
                    "color": title.color,
                    "textAlign": title.align
                }
            }
        },
        setTitle(title) {
            const vm = this, {extend} = this;
            extend.title.show = title.show;
            extend.title.text = title.text;

            if (title.custom_style === 'custom') {
                let fStyle = title.fontStyle.length ? title.fontStyle[0] : "normal";
                extend.title.textStyle = {
                    color: title.color,
                    fontFamily: title.fontFamily,
                    fontSize: parseInt(title.fontSize),
                    fontWeight: title.fontWeight,
                    fontStyle: fStyle
                };
                extend.title.left = title.align;
                // extend.title.textStyle.fontStyle = title.fontStyle.length ? title.fontStyle[0] : true;
            } else {
                extend.title.textStyle = {};
                extend.title.left = 'center';
                extend.title.textStyle.fontStyle = "normal";

            }
        },
        setName(name) {
            if (name === '') return;
            if (this.chartData.columns) {
                if (name) {
                    this.extend.xAxis.name = this.chartData.columns[0];
                    this.chartSettings.yAxisName = [this.node.xData[0].label];
                } else {
                    this.extend.xAxis.name = '';
                    this.chartSettings.yAxisName = [];
                }

            }
        },
        setDataLabel(show, pos) {
            this.extend.series.label.show = show;
            this.extend.series.label.position = pos; //top inside insideBottom
        },
        legendAuto(legend) {
            const vm = this;
            let show_l = legend.legend_pos !== 'none';
            let left = '', top = '', zoomBottom;
            if (legend.legend_pos !== 'none') {
                if (legend.legend_pos === 'left') {
                    left = 'left';
                    top = 'middle';
                    zoomBottom = 6;
                } else if (legend.legend_pos === 'right') {
                    left = 'right';
                    top = 'middle';
                    zoomBottom = 6;
                } else if (legend.legend_pos === 'top') {
                    left = 'center';
                    top = 26;
                    zoomBottom = 6;
                } else if (legend.legend_pos === 'bottom') {
                    left = 'center';
                    top = 'bottom';
                    zoomBottom = 30;
                }
            } else {
                zoomBottom = 6;
            }
            if (vm.extend.dataZoom) {
                vm.extend.dataZoom[1].bottom = zoomBottom;
            }
            vm.extend.legend = {
                type: 'scroll',
                orient: legend.orient,
                show: show_l,
                left: left,
                top: top,
                align: 'auto',
                height: "80%",
                width: "80%"
            };
        },
        setLegend(legend) {
            const vm = this;
            vm.setColor(legend.color, legend.color_theme, legend.color_type);
            vm.setDataLabel(legend.label_show, legend.label_pos);
            vm.setGrid(legend);
            vm.legendAuto(legend);
            vm.setDataZoom(legend);
        },
        setDataZoom(legend) {
            // if(!legend.show_dataZoom ) return;
            this.extend.dataZoom[1].show = legend.show_dataZoom;
            if(this.extend.dataZoom[2]) this.extend.dataZoom[2].show =  legend.show_dataZoom;
            this.extend.dataZoom[0].zoomLock = legend.zoomLock;
            this.extend.dataZoom[1].zoomLock = legend.zoomLock;
            if (legend.zoomLock) {
                this.extend.dataZoom[0].start = legend.start;
                this.extend.dataZoom[1].start = legend.start;
                this.extend.dataZoom[0].end = legend.end;
                this.extend.dataZoom[1].end = legend.end;
            } else {
                this.extend.dataZoom[0].start = 0;
                this.extend.dataZoom[1].start = 0;
            }
            if (legend.setSpan === "custom") {
                this.extend.dataZoom[0].minSpan = legend.minSpan;
                this.extend.dataZoom[1].minSpan = legend.minSpan;
            } else {
                this.extend.dataZoom[0].minSpan = 0;
                this.extend.dataZoom[1].minSpan = 0;
            }
        },
        setGrid(legend) {
            let top = legend.top_type === "abs" ? legend.top : legend.top + '%',
                bottom = legend.bottom_type === "abs" ? legend.bottom : legend.bottom + '%',
                left = legend.left_type === "abs" ? legend.left : legend.left + '%',
                right = legend.right_type === "abs" ? legend.right : legend.right + '%';
            this.extend.grid = {
                top: top,
                bottom: bottom,
                right: right,
                left: left
            };
            this.tipWidth = legend.left_type === "abs" ? legend.left + 50 + 'px' : legend.left + '% + 50px';
        },
        setSeriesM(legend) {
            let top = legend.top_type === "abs" ? legend.top : legend.top + '%',
                bottom = legend.bottom_type === "abs" ? legend.bottom : legend.bottom + '%',
                left = legend.left_type === "abs" ? legend.left : legend.left + '%',
                right = legend.right_type === "abs" ? legend.right : legend.right + '%';
            this.extend.series.top = top;
            this.extend.series.bottom = bottom;
            this.extend.series.left = left;
            this.extend.series.right = right;
            this.tipWidth = legend.left_type === "abs" ? legend.left + 100 + 'px' : legend.left + '% + 100px';
        },
        chartResize() {
            if (this.$refs.chart) {
                this.$refs.chart.resize();
            }
        },
        storeAxisData(nodeData, axis) {
            const vm = this;
            vm[axis] = [];
            nodeData[axis].forEach(node => {
                vm[axis].push(node.data.code);
            })
        },
        /**
         * 获取数据格式
         * @param dims
         * @return {*|string}
         */
        getVisualTimeFormat(dims){
            let format = "";
            for(let item of dims){
                if(item.visualTimeFormat){
                    format = item.visualTimeFormat;
                }
            }
            return format;
        },
        /**
         * 获取数值对齐
         * @param dims 维度数据
         * @param code 周期时间字段code
         * @return {string}
         */
        getDataPolish(dims , code){
            let polish = "" , polishObj = {
                n_w : "year",
                y_w : "month",
                r_w : "day",
                jd_w:"quarter",
                dnz_w:"week",
                z_w:"week"
            };
            for(let item of dims){
                if(item.dataPolishingDateGranularity && code){
                    return polishObj[code];
                }
            }
            return polish;
        },
        /**
         *
         * @param id
         * @return {Promise<void>}
         */
        async setHistogramPanel(id) {
            let vm = this;
            if (vm.node.id !== id && id !== undefined) return;
            if (id) {
                vm.showDrill = false;
                vm.drillLevel = 0;
                vm.drillFilter = [];
            }
            vm.filterDimsName = "";
            vm.filterDims = null;
            let widget = JSON.parse(vm.node.widget), beforeData = JSON.parse(widget.beforeData);
            if (beforeData.filterList && beforeData.filterList.length > 0) {
                let dims = null;
                let query = beforeData.filterList.filter(list => {
                    if (list.dim) dims = list.dim;
                    return list.filter && list.filter.addVal !== "";
                });
                widget.query = JSON.stringify(query);
                let dimsIndex = widget.widgetDataset.widgetDatasetDims.length;
                let visualTimeFormat = await vm.getVisualTimeFormat(widget.widgetDataset.widgetDatasetDims),
                dataPolishingDateGranularity = await vm.getDataPolish(widget.widgetDataset.widgetDatasetDims , dims && dims.data.code);
                if (dims) {
                    dims.index = dimsIndex;
                    dims.visualTimeFormat = visualTimeFormat || "";
                    dims.dataPolishingDateGranularity = dataPolishingDateGranularity || "";
                    widget.widgetDataset.widgetDatasetDims.push(dims);
                    vm.filterDimsName = dims.filedCode;
                    vm.filterDims = dims;
                }
            }
            vm.loading = true;
            vm.data_empty = true;
            vm.chartData = {};
            vm.setDimension(beforeData.yAxis);
            vm.storeAxisData(beforeData, 'xData');
            vm.storeAxisData(beforeData, 'yData');
            //id相同再渲染数据
            /*vm.send(JSON.stringify({
                code: vm.node.code,
                data: widget,
                type: vm.isView
            }))*/
            vm.getChartData({
                code: vm.node.code,
                data: widget,
                type: vm.isView
            });
        },
        getChartData(data) {
            const vm = this, {visualServices, visualMock } = this;
            let services = vm.getServices(visualServices, visualMock);
            services.loadChart(data , this).then(res => {
                if (res.data.code === 0) {
                    vm.getMessage({data: JSON.stringify(res.data)});
                }
            })
        },
        preview() {
            const vm = this;
            vm.data_empty = true;
            if (!vm.node.widget) {
                return;
            }
            let {beforeData, widgetDataset} = JSON.parse(vm.node.widget),
                nodeData = beforeData ? JSON.parse(beforeData) : {};

            if (nodeData.xAxis && nodeData.xAxis.length === 0) {
                return;
            }
            if (vm.node.code !== "IndicatorCardWidget" && nodeData.yAxis && nodeData.yAxis.length === 0) {
                return;
            }
            if (!widgetDataset) {
                return;
            }
            if (!widgetDataset.tableCode) {
                return;
            }
            if (!widgetDataset.widgetDatasetDims && widgetDataset.widgetDatasetDims.length === 0) {
                return;
            }
            if (!widgetDataset.widgetDatasetMeasures && widgetDataset.widgetDatasetMeasures.length === 0) {
                return;
            }

            vm.setHistogramPanel();
        },
        intervalGetData() { //定时 不为0才刷新
            const vm = this;
            vm.$nextTick(() => {
                let widget = JSON.parse(vm.node.widget), beforeData = JSON.parse(widget.beforeData);
                let time = beforeData.refreshVal * beforeData.refreshUnit;
                if (!isNaN(time) && time !== 0 && vm.isView) {
                    vm.timer = setInterval(vm.preview, time);
                }
            })

        },
        clearTimer() {
            clearInterval(this.timer);
            this.timer = null;
        },
        createWebSocket(params) {
            const vm = this;
            if (params) {
                vm.params = params;
            }
            try {
                if ('WebSocket' in window) {
                    if (!vm.socket) {
                        vm.socket = new WebSocket(vm.$ws);
                        vm.initEventHandle();
                    } else {
                        vm.socket.close();
                        vm.reconnect();
                    }
                }
            } catch (e) {
                vm.reconnect();
            }
        },
        initEventHandle(params) {
            const vm = this;
            vm.socket.onclose = function () {
                vm.heartCheck.reset();
                vm.socket = null;
                // console.log("llws连接关闭!"+new Date().toLocaleString());
            };
            vm.socket.onerror = function () {
                vm.reconnect();
                // console.log("llws连接错误!");
            };
            vm.socket.onopen = function () {
                vm.heartCheck.reset().start();      //心跳检测重置
                vm.heartCheck.sendParams();
                // console.log("llws连接成功!"+new Date().toLocaleString());
            };
            vm.socket.onmessage = vm.getMessage;
        },
        reconnect() {
            const vm = this;
            if (vm.lockReconnect) return;
            vm.lockReconnect = true;
            setTimeout(function () {     //没连接上会一直重连，设置延迟避免请求过多
                vm.createWebSocket();
                vm.lockReconnect = false;
            }, 2000);
        },
        setZoom(data) {
            const vm = this;
            if (vm.extend.dataZoom) {
                vm.setZoomMax(data.rows);
                if (data.rows.length >= 5000) {
                    vm.extend.dataZoom[0].end = 500 / data.rows.length;
                    vm.extend.dataZoom[1].end = 500 / data.rows.length;
                } else if (data.rows.length >= 1000) {
                    vm.extend.dataZoom[0].end = 0.5;
                    vm.extend.dataZoom[1].end = 0.5;
                } else if (data.rows.length >= 500) {
                    vm.extend.dataZoom[0].end = 3;
                    vm.extend.dataZoom[1].end = 3;
                } else if (data.rows.length >= 400) {
                    vm.extend.dataZoom[0].end = 4;
                    vm.extend.dataZoom[1].end = 4;
                } else if (data.rows.length >= 300) {
                    vm.extend.dataZoom[0].end = 5;
                    vm.extend.dataZoom[1].end = 5;
                } else if (data.rows.length >= 200) {
                    vm.extend.dataZoom[0].end = 7;
                    vm.extend.dataZoom[1].end = 7;
                } else if (data.rows.length >= 100) {
                    vm.extend.dataZoom[0].end = 15;
                    vm.extend.dataZoom[1].end = 15;
                } else if (data.rows.length >= 50) {
                    vm.extend.dataZoom[0].end = 30;
                    vm.extend.dataZoom[1].end = 30;
                } else if (data.rows.length >= 20) {
                    vm.extend.dataZoom[0].end = 60;
                    vm.extend.dataZoom[1].end = 60;
                } else {
                    vm.extend.dataZoom[0].end = 100;
                    vm.extend.dataZoom[1].end = 100;
                }
            }
        },
        /**
         *
         */
        setZoomMax(rows){
            rows && rows.length >200 ?
                this.extend.dataZoom[0].maxValueSpan = 200 :
                delete this.extend.dataZoom[0].maxValueSpan;
        },
        setZoomTrans(data) {
            const vm = this;
            if (vm.extend.dataZoom && vm.extend.dataZoom[2]) {
                vm.extend.dataZoom[2].end = 100;
                if (data.rows.length >= 5000) {
                    vm.extend.dataZoom[2].start = 100 - 500 / data.rows.length;
                } else if (data.rows.length >= 1000) {
                    vm.extend.dataZoom[2].start = 99.5;
                } else if (data.rows.length >= 500) {
                    vm.extend.dataZoom[2].start = 97;
                } else if (data.rows.length >= 400) {
                    vm.extend.dataZoom[2].start = 96;
                } else if (data.rows.length >= 300) {
                    vm.extend.dataZoom[2].start = 95;
                } else if (data.rows.length >= 200) {
                    vm.extend.dataZoom[2].start = 93;
                } else if (data.rows.length >= 100) {
                    vm.extend.dataZoom[2].start = 85;
                } else if (data.rows.length >= 50) {
                    vm.extend.dataZoom[2].start = 70;
                } else if (data.rows.length >= 20) {
                    vm.extend.dataZoom[2].start = 40;
                } else {
                    vm.extend.dataZoom[2].start = 0;
                }
            }
        },
        getMessage(msg) {
            const vm = this;
            let result = JSON.parse(msg.data), data = result.data;
            if (vm.socket) vm.socket.close();
            if (result.status === 0 && data) {
                vm.chartData = data;
                let chartCode = vm.node.code;
                let staceArray = data.columns.slice(1);
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                vm.chartSettings.stack = {};
                vm.chartSettings.area = false;
                if (chartCode === 'StackBarChartWidget' || chartCode === 'TransverseStackBarChartWidget') {
                    vm.chartSettings.stack = {'stace': staceArray};
                } else if (chartCode === 'AreaGraphWidget') {
                    vm.chartSettings.stack = {'stack': staceArray};
                    vm.chartSettings.area = true;
                }
                if (chartCode === 'TransverseBarChartWidget' || chartCode === 'TransverseStackBarChartWidget') {
                    vm.chartSettings.type = "bar";
                    vm.setZoomTrans(data);
                }else if(chartCode === "BarChartWidget" || chartCode === "StackBarChartWidget") {
                    vm.chartSettings.type = "histogram";
                    vm.setZoom(data);
                }
                vm.set_digital_format(nodeData.xData);
                vm.setChartExtend(nodeData.style);
                vm.data_empty = false; //没有数据为true
                vm.loading = false;
            } else {
                vm.data_empty = true; //没有数据为true
                vm.loading = false;
                vm.chartData = {};
            }
        },
        /**
         * 设置数据格式
         * @param measure
         */
        set_digital_format(measure) {
            const vm = this;
            let format = {
                "#,##0": "0,0",
                "#,##0.0": "0,0.0",
                "#,##0.00": "0,0.00",
                "#,##0.000": "0,0.000",
                "#,##0.0000": "0,0.0000",
            };
           /* measure.forEach(item => {
                if(item.data.numberFormat){
                   vm.extend.yAxis.axisLabel.formatter = function(v){
                       return numerify(v , format[item.data.numberFormat])
                   };

                }else {
                    delete vm.extend.yAxis.axisLabel.formatter;
                }
            });*/
        },
        send(params) {
            const vm = this;
            vm.createWebSocket(params);
        },
    },
    created() {
        this.getNodeData();
        this.preview();
        this.intervalGetData();
    },
    beforeDestroy() {
        if (this.socket) {
            this.socket.close(3001, true);
        }
    },
    destroyed() {
        globalBus.$off('setTablePanel', this.setTablePanel);
        globalBus.$off('setMapPanel', this.setHistogramPanel);
        globalBus.$off('setBubbleMapPanel', this.setHistogramPanel);
        globalBus.$off('setPiePanel', this.setHistogramPanel);
        globalBus.$off('setLinePanel', this.setHistogramPanel);
        globalBus.$off('setHistogramPanel', this.setHistogramPanel);
        globalBus.$off('setIndicatorPanel', this.setHistogramPanel);
        globalBus.$off('setHeatMapPanel', this.setHistogramPanel);
        globalBus.$off('setPgisPanel', this.setHistogramPanel);
        globalBus.$off('setWordCloudWidgetPanel', this.setHistogramPanel);
        globalBus.$off('setCombinationWidgetPanel', this.setHistogramPanel);
        globalBus.$off('setRadarWidgetPanel', this.setHistogramPanel);
        globalBus.$off('setGraphPanel', this.setHistogramPanel);
        globalBus.$off('setBuisinessRelationPanel', this.setHistogramPanel);
        globalBus.$off('setFormPanel', this.setHistogramPanel);
        this.clearTimer();
    }
};
