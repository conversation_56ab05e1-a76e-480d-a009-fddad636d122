<template>
    <div>
        <common-dialog
                       v-loading="settings.loading"
                       :width="width"
                       :title="title"
                       :visible.sync="dialogFormVisible"
                       @closed="clearD"
        >
            <BaseSettingCont ref="form_cont" v-if="openDialog" @setActive="setActive" @cornPanelShow="cornPanelShow"
                             @closeLoading="closeLoading"
                             :rowData="rowData"/>
            <div slot="footer">
                <dg-button @click="save" type="primary" size="mini">{{saveBtnTxt}}</dg-button>
                <!--<dg-button type="primary" v-show="!active" @click="activate" size="mini">{{startBtnTxt}}</dg-button>
                <dg-button type="danger" v-show="active" @click="stop" size="mini">{{stopBtnTxt}}</dg-button>-->
                <!--                <el-button type="primary" v-show="true" @click="activate" size="mini">激活</el-button>-->
                <!--                <el-button type="danger" v-show="true" @click="stop" size="mini">停用</el-button>-->
            </div>
        </common-dialog>
        <CornExpression ref="cornPanel" @save="saveCorn"/>
    </div>
</template>


<script>
    import BaseSettingCont from './BaseSettingCont'
    import CornExpression from '../corn/CornExpression'
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";

    export default {
        name: "ModelEditDailog",
        components: {
            BaseSettingCont,
            CornExpression
        },
        mixins: [commonMixins, servicesMixins],
        data() {
            return {
                dialogFormVisible: false,
                width: '800px',
                active: false,
                openDialog: false,
                rowData: {},
                isActivate: false,
                title : "配置"
            };
        },
        methods: {
            closeLoading(){
                this.settings.loading = false;
            },
            setActive(val) {
                this.active = val;
            },
            show(rowData) {
                const _this = this;
                _this.rowData = rowData;
                this.openDialog = true;
                this.settings.loading = true;
                this.dialogFormVisible = !this.dialogFormVisible;
            },
            clearD() {
                this.openDialog = false;
                this.$emit('update');
            },
            closeDialog() {
                this.dialogFormVisible = false;
            },
            //格式化日期
            dateFmt(fmt, date) {
                let allDate = {
                    "M+": date.getMonth() + 1,
                    "d+": date.getDate(),
                    "h+": date.getHours(),
                    "m+": date.getMinutes(),
                    "s+": date.getSeconds(),
                    "q+": Math.floor((date.getMonth() + 3) / 3),
                    "S": date.getMilliseconds()
                };
                if (/(y+)/.test(fmt))
                    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                for (let k in allDate)
                    if (new RegExp("(" + k + ")").test(fmt))
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (allDate[k]) : (("00" + allDate[k]).substr(("" + allDate[k]).length)));
                return fmt;
            },
            save: function () {
                let form = this.$refs.form_cont.getFormData();
                //let isShowProgramme=this.$refs.form_cont.getIsShowProgramme();
                //console.log("保存的数据：", form, "方案信息：", this.rowData);
                let schemeScheduleMode;
                if (form.dispatchPlan === "手动") {
                    // schemeScheduleMode = 0;
                    schemeScheduleMode = 'hand';
                } else if (form.dispatchPlan === "定时") {
                    schemeScheduleMode = 1;
                } else if (form.dispatchPlan === "一次性") {
                    schemeScheduleMode = 2;
                } else if (form.dispatchPlan === "间隔") {
                    schemeScheduleMode = 3;
                } else if (form.dispatchPlan === "cron表达式") {
                    // schemeScheduleMode = 5;
                    schemeScheduleMode = 'cron';
                }
                let month = "0";
                let day = "0";
                let hour = "0";
                let min = "0";

                //定时：
                let timing = form.carryTime.split("/");
                let scheduleInterval;
                if (timing[0] === "每日") {
                    hour = timing[1];
                    min = timing[2];
                    scheduleInterval = 1;
                } else if (timing[0] === "每周") {
                    scheduleInterval = 2;
                    this.$message.warning("暂支持每周定时执行，请使用cron表达式进行配置！");
                    return;
                } else {
                    day = timing[1];
                    hour = timing[2];
                    min = timing[3];
                    scheduleInterval = 3;
                }
                //开始时间和结束时间的格式化
                let beginTime = null;
                let endTime = null;
                if ((schemeScheduleMode === 3 || schemeScheduleMode === 1) && (form.timeRange === null || form.timeRange === "" || form.timeRange.length === 0)) {
                    this.$message.warning("请选择时间！");
                    return;
                }
                if (schemeScheduleMode === 2 && form.startTime === "") {
                    this.$message.warning("请选择时间！");
                    return;
                }
                if (schemeScheduleMode === 2) {
                    beginTime = this.dateFmt("yyyy-MM-dd hh:mm:ss", new Date(form.startTime));
                } else if (schemeScheduleMode === 3 || schemeScheduleMode === 1) {
                    beginTime = this.dateFmt("yyyy-MM-dd", new Date(form.timeRange[0]));
                    endTime = this.dateFmt("yyyy-MM-dd", new Date(form.timeRange[1]));
                }
                //判断cron表达式不能为空
                if (schemeScheduleMode === 5 && form.cronInput === "") {
                    this.$message.warning("请输入cron表达式！");
                    return;
                }
                //间隔
                let interval = form.intervalTime.split("/");
                if (form.dispatchPlan === "间隔") {
                    month = interval[0];
                    day = interval[1];
                    hour = interval[2];
                    min = interval[3];
                }
                let requestData = {
                    id: form.jobId,
                    jobStrategyId: this.rowData.transId,
                    scheduleId: form.instance,//实例
                    schemeScheduleMode: schemeScheduleMode,//手动、定时等
                    scheduleInterval: scheduleInterval,//定时中的每日、每周还是每月
                    scheduleMonth: month,//月
                    scheduleDay: day,//日
                    scheduleHour: hour,//时
                    scheduleMinute: min,//分
                    beginTime: beginTime,//开始时间
                    endTime: endTime,//结束时间
                    cronExpression: form.cronInput,//表达式
                    memo: form.state//调度说明
                };
                let dataMiningData = {
                    transId: this.rowData.transId,
                    scheduleType: schemeScheduleMode,
                    cron: form.cronInput,//表达式
                    declare: form.state,//调度说明,
                    startPrograms:form.startPrograms,
                    /*subtransId: form.selectTreeValue,
                    subtransDelayTime: form.delayExecute,
                    subtransIsenforce: form.isAlways,*/
                }
                let week = [
                    "周一", "周二"
                ];
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                /*                services.updateTransSchedule(week, requestData).then(res => {
                                    if (res.data.status === 0) {//保存成功
                                        vm.updateTransDistribute(form, services);//更新运行模式
                                        vm.updateTransLogLevel(form, services);//更新日志
                                        vm.updateHandleMode(form, services);//更新处理模式
                                        vm.$message.success("保存成功！");
                                        vm.isActivate = false;
                                        vm.active = true;
                                        vm.closeDialog();
                                    }
                                })*/
                let isReturn=false;
                for(let i=0;i<form.startPrograms.length;i++){
                    if (form.startPrograms[i].subtransId === "" || form.startPrograms[i].subtransId === undefined) {
                        this.$message.warning("请选择流程方案！");
                        isReturn=true;
                        break;
                    }

                    if(form.startPrograms[i].subtransDelayTime === "" || form.startPrograms[i].subtransDelayTime === undefined){
                        this.$message.warning("请输入延迟时间！");
                        isReturn=true;
                        break;
                    }

                    if (!(/^\d+$/).test(form.startPrograms[i].subtransDelayTime)) {
                        this.$message.warning("延迟时间必须输入正整数！");
                        isReturn=true;
                        break;
                    }
                   if(form.startPrograms[i].subtransId==this.rowData.transId){
                       //console.log(this.rowData.transId)
                       this.$message.warning("不允许嵌套配置子方案！");
                       isReturn=true;
                       break;
                   }
                }
                if(isReturn==true){
                    return;
                }

                services.updateDataMiningScheduleWithSubTrans(dataMiningData).then(res => {
                    if (res.data.status === 0) {//保存成功
                        vm.$message.success("保存成功！");
                        vm.isActivate = false;
                        vm.active = true;
                        vm.closeDialog();
                    }
                })

            },
            //是否分布式
            updateTransDistribute(form, services) {
                services.updateTransDistribute(this.rowData.transId, form.mode);
            },
            //错误级别
            updateTransLogLevel(form, services) {
                services.updateTransLogLevel(this.rowData.transId, form.logLevel);
            },
            //处理模式
            updateHandleMode(form, services) {
                services.updateHandleMode(this.rowData.transId, form.processMode);
            },
            activate() {
                const vm = this, {modelingServices, modelingMock ,settings} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                settings.loading = true;
                services.activeJob(this.rowData.transId , settings).then(res => {
                    // console.log("激活功能响应信息：",res);
                    if (res.data.status === 0) {
                        this.$message.success("激活成功！");
                        this.active = !this.active;
                    }
                })
            },
            stop() {
                const vm = this, {modelingServices, modelingMock , settings} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                settings.loading = true;
                services.stopJob(this.rowData.transId , settings).then(res => {
                    console.log(res);
                    if (res.data.status === 0) {
                        this.$message.success("已停用！");
                        this.active = !this.active;
                    }
                })
            },
            cornPanelShow(data) {
                this.$refs.cornPanel.show(data);
            },
            saveCorn(data) {
                this.$refs.form_cont.setCornValue(data);
            },
        },
    };
</script>
<style scoped>

</style>