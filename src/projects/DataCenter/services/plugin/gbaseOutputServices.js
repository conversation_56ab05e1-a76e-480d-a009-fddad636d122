/**
 * gbase输出 插件 接口
 * @Author: wangjt
 * @Date: 2020-06-15
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/gbaseTableOutputPlugin/";

/*
* 初始化
* @params tranStepId
* @returns {Promise<*>}
* */

export async function drdTableOutputPage( tranStepId , settings) {
    return await reqUtil.get({
        url: root + 'gbaseTableOutputPage' ,
        settings : settings
    }, {tranStepId});
}

/*
* 初始化
* @params datavo
* @returns {Promise<*>}
* */

export async function saveDrdTableOutput( dataVo , settings) {
    return await reqUtil.get({
        url: root + 'saveGbaseTableOutput' ,
        settings : settings
    }, dataVo);
}
/*
* 获取字段
* @params tableName , schemaId
* @returns {Promise<*>}
* */

export async function getSortedRdbColumn( tableName , schemaId) {
    return await reqUtil.get({
        url: root + 'getSortedRdbColumn' ,
    }, {tableName , schemaId});
}

