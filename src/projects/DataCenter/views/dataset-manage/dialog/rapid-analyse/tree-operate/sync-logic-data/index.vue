<template>
    <common-dialog custom-class="syncLogic"
                   v-loading="settings.loading"
                   :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <sync-page ref="syncPage" v-if="reNew" :datasetId="datasetId" ></sync-page>
        <span slot="footer" class="dialog-footer">
            <dg-button  @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button v-if="hideCheck" type="primary" @click="submit">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>

<script src="./sync-logic.js"></script>
