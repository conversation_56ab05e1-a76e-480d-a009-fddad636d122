<template>
    <common-dialog custom-class="log" :width="width" title="查看指标"
                   :visible.sync="dialogVisible"
                   v-loading="settings.loading"
    >
        <div>
            <div class="mb10">
                <el-button type="primary" @click="getIndicatorsInfo">刷新</el-button>
            </div>
            <common-table
                    height="calc(75vh - 112px)"
                    :data="tableData"
                    :span-method="objectSpanMethod"
                    :columns="tHeadData"
                    :pagination="false"
            >
            </common-table>
        </div>

    </common-dialog>
</template>

<script>
    export default {
        name: "indicatorsInfo",
        data() {
            return {
                width: '1000px',
                dialogVisible: false,
                settings: {
                    loading: false
                },
                tableData: [],
                tHeadData: [
                    {
                        prop: "stepCode",
                        label: "步骤code",
                        minWidth: '150',
                        align: "center"
                    },
                    {
                        prop: "parallelism",
                        label: "并行度",
                        align: "center"
                    },
                    {
                        prop: "numRecordsIn",
                        label: "处理数据量(输入)",
                        align: "center"
                    },
                    {
                        prop: "numRecordsOut",
                        label: "处理数据量(输出)",
                        align: "center"
                    },
                    {
                        prop: "numRecordsInPerSecond",
                        label: "瞬时速度(输入)",
                        align: "center"
                    },
                    {
                        prop: "numRecordsOutPerSecond",
                        label: "瞬时速度(输出)",
                        align: "center"
                    }
                ],
                row: {},
                spanArr: [],
                pos: 0,
                nodes: [],
            }
        },
        methods: {
            getSpanArr(data) {
                this.spanArr = [];
                for (let i = 0; i < data.length; i++) {
                    if (i === 0) {
                        this.spanArr.push(1);
                        this.pos = 0
                    } else {
                        // 判断当前元素与上一个元素是否相同
                        if (data[i].stepCode === data[i - 1].stepCode) {
                            this.spanArr[this.pos] += 1;
                            this.spanArr.push(0);
                        } else {
                            this.spanArr.push(1);
                            this.pos = i;
                        }
                    }
                }
            },
            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
                if (columnIndex === 0) {
                    const _row = this.spanArr[rowIndex];
                    const _col = _row > 0 ? 1 : 0;
                    return {
                        rowspan: _row,
                        colspan: _col
                    }
                }
            },
            show(row, nodes) {
                this.dialogVisible = true;
                this.row = row;
                this.nodes = nodes || [];
                this.getIndicatorsInfo()
            },
            getIndicatorsInfo(){
                const vm = this;
                let services = vm.$services('modeling');
                vm.settings.loading = true;
                services.queryMetricByJobId({jobId: vm.row.id, settings: vm.settings}).then(res => {
                    vm.tableData = [];
                    let result = res.data.data.data;
                    for(let p in result){
                        let info = result[p];
                        let length = Object.keys(info).length
                        for(let k in info){
                            let obj = {...info[k]};
                            let name = (vm.nodes.find(h=>h.code === p) || {}).name || p;
                            obj.parallelism = k;
                            obj.stepCode = name;
                            obj.code = p;
                            obj.length = length;
                            vm.tableData.push(obj);
                        }
                    }
                    vm.getSpanArr(vm.tableData);
                    vm.settings.loading = false;
                })
            }
        }
    }
</script>

<style scoped>

</style>
