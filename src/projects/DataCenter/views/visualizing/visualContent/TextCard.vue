<template>
    <div  class="textCard" :style="style" >
        <pre v-if="!moving" class="text_cont" v-html="content"></pre>
        <div v-else class="ce-data-none">{{moveTxt}} <em class="el-icon-loading"></em></div>
    </div>
    <!--<textarea type="text" class="textCard" v-model="content" :style="style" ></textarea>-->
</template>

<script>
    import {globalBus} from "@/api/globalBus"
    export default {
        name: "TextCard" ,
        props : {
            id : String ,
            node : Object ,
            moving : Boolean
        } ,
        data(){
            return {
                content : '' ,
                style : {
                    fontSize : '12px',
                    color : '#555',
                    lineHeight : '24px',
                    textAlign : 'left' ,
                    whiteSpace: 'normal'
                },
                moveTxt : "移动中"
            }
        },
        methods : {
            chartResize(){}, //不能删
            getNodeData() {
                globalBus.$on('setTextPanel', this.setTextPanel);
            },
            setTextPanel( id , resultData ) {
                const vm = this;
                if (vm.id !== id && id !== undefined) return;
                //id相同再渲染数据
                let widget = JSON.parse(vm.node.widget) , nodeData = JSON.parse(widget.beforeData);
                let result = resultData || nodeData;
                vm.content = result.textarea;
                vm.style.fontSize = result.fontSize;
                vm.style.color = result.color;
                vm.style.textAlign = result.textAlign;
                vm.style.lineHeight = result.lineHeight;
            },
            preview(){
                this.setTextPanel();
            }
        },
        created() {
            this.getNodeData();
            this.setTextPanel();
        },
        destroyed() {
            globalBus.$off('setTextPanel', this.setTextPanel);
        }
    }
</script>

<style scoped>
    .textCard {
        width: 100%;
        height: 100%;
        display: block;
        border: none;
        padding:8px;
        box-sizing: border-box;
        overflow: auto;
        cursor: default;
    }
    .text_cont {
        width: 100%;
        white-space: pre-line;
    }
</style>