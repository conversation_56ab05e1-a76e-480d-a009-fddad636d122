<template>
    <dg-dialog :title="title" v-bind="$attrs" v-on="$listeners" @open="reNewData" @closed="reNewData" top="8vh">
        <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document code="roleManagementSetAuth"></help-document></div>
        <div class="add-pop-header">
            <div class="a-p-h-search">
                <el-input
                    size="mini"
                    placeholder="请输入功能名称搜索"
                    suffix-icon="el-icon-search"
                    v-model.trim="addSearchVal"
                    @input="searchFn"
                ></el-input>
            </div>
        </div>
        <div class="add-pop-table">
            <el-tabs type="card" v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="全部" name="all"></el-tab-pane>
                <el-tab-pane label="建模空间" name="modelSpace"></el-tab-pane>
                <!-- 暂时隐藏3.7.0 -->
                <el-tab-pane label="可视空间" name="visualSpace"></el-tab-pane>
                <el-tab-pane label="服务空间" name="serviceSpace"></el-tab-pane>
                <el-tab-pane label="数据空间" name="dataSpace"></el-tab-pane>
                <el-tab-pane label="管理空间" name="management"></el-tab-pane>
                <!-- 暂时隐藏3.7.0 -->
<!--                <el-tab-pane label="场景案例" name="scenarioCase"></el-tab-pane>-->
            </el-tabs>
            <dg-table
                    :data="tableData"
                    :show-overflow-tooltip="false"
                    border
                    stripe
                    height="calc(75vh - 225px)"
                    :pagination="false"
                    style="width: 100%"
            >
                <dg-table-column label="菜单名称" prop="menu" width="120"></dg-table-column>
                <dg-table-column prop="func" width="120" label="功能名称"></dg-table-column>
                <dg-table-column label="权限项">
                    <template slot-scope="{row}">
                        <div class="pop-table-check">
                            <dg-checkbox-group
                                    check-all-text="全选"
                                    v-model="row.selectAll"
                                    :data="row.actions"
                                    check-all
                                    row
                            ></dg-checkbox-group>
                        </div>
                    </template>
                </dg-table-column>
            </dg-table>
        </div>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="onCancel">取 消</dg-button>
            <dg-button v-if="showPrev" @click="prev">上一步</dg-button>
            <dg-button type="primary" v-if="isEdited" @click="updateFn">确 定</dg-button>
            <dg-button type="primary" v-else  @click="onSubmit">确 定</dg-button>
        </span>
    </dg-dialog>
</template>
<script lang="js" src="./role-form-pop.js"></script>
<style scoped lang="less" src="./role-form-pop.less"></style>
