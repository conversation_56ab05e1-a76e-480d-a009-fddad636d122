import {chartMxins} from "../chart-mixins/chart-mixins";
import {globalBus} from "@/api/globalBus";
import numerify from 'numerify'
import "@static/pgis/leaflet.map.min.js"
import "@static/pgis/mapConfig.js"

export default {
    name: "pgisCard",
    mixins: [chartMxins],
    data() {
        const vm = this;
        window[vm.$pgis_type] = Object.assign(window[vm.$pgis_type], {
            mapUrl : vm.$pgis_url ,
            minZoom: 4,
            maxZoom: 18,
            level: 11,//级别
            centerPoint: vm.$map_center,
        })
        return {
            titleStyle: {},
            map: null,
            map_center: vm.$map_center,
            marker_style: {
                weight: 1.5,
                color: "red"
            },
            corner: this.$corner ,
            type: "vue",
            config: window[vm.$pgis_type],
            //地图初始定位
            initPosition: {
                level: 11,//级别
                centerPoint: vm.$map_center//中心点
            },
            mapLayers : [] ,
            isGetMapData : true, //是否要获取数据
        }
    },
    computed: {
        chartId() {
            return 'map' + this.node.id;
        },
    },
    components: {
        'map-component': httpVueLoader(`http://${window.location.host}/static/pgis/vue-map-component-min/leaflet/map-component.vue`, 'frontEnd')
    },
    methods: {
        getNodeData() {
            globalBus.$on('setPgisPanel', this.setHistogramPanel);
        },
        setHistogramPanel(id) {
            const vm = this, {node, isView} = this;
            if (node.id !== id && id !== undefined) return;
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);
            let code = nodeData.style && nodeData.style.funcSettings.mapCode && nodeData.style.funcSettings.mapCode.length ? nodeData.style.funcSettings.mapCode.slice(-1)[0] : 'china';
            vm.filterDimsName = "";
            vm.filterDims = null;
            if (nodeData.filterList && nodeData.filterList.length > 0) {
                let dims = null;
                let query = nodeData.filterList.filter(list => {
                    if (list.dim) dims = list.dim;
                    return list.filter && list.filter.addVal !== "";
                });
                widget.query = JSON.stringify(query);
                let dimsIndex = widget.widgetDataset.widgetDatasetDims.length;
                if (dims) {
                    dims.index = dimsIndex;
                    widget.widgetDataset.widgetDatasetDims.push(dims);
                    vm.filterDimsName = dims.filedCode;
                    vm.filterDims = dims;
                }
            }
            widget.mapCode = code;
            vm.loading = true;
            vm.getChartData({
                code: vm.node.code,
                data: widget,
                type: vm.isView
            });
        },
        getChartData(data) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            services.loadChart(data, this).then(res => {
                if (res.data.code === 0) {
                    vm.getMessage({data: JSON.stringify(res.data)});
                } else {
                    vm.reloadMap();
                    vm.loading = false;
                }
            })
        },
        async getMessage(msg) {
            const vm = this;
            let result = JSON.parse(msg.data), data = result.data;
            let {beforeData} = JSON.parse(vm.node.widget),
                nodeData = JSON.parse(beforeData);
            if (vm.socket) vm.socket.close();
            if (result.status === 0 && data) {
                if (data.regionLongitude && data.regionLatitude) {
                    vm.map_center = [data.regionLongitude, data.regionLatitude];
                }
                vm.setMapExtend(nodeData.style);
                await vm.reloadMap();
                vm.setMarker(nodeData, data);
                vm.loading = false;
            } else {
                vm.reloadMap();
                vm.loading = false;
            }
        },
        /**
         * 添加标记
         * @param nodeData
         * @param data
         */
        setMarker(nodeData, data) {
            const vm = this;
            let numFormat = nodeData.xData.length && nodeData.xData[0].data.numberFormat;
            let result = vm.getLabelAndFun(nodeData);
            let level = nodeData.style.funcSettings.mapCode[0] === "china" ? 4 : 13;
            if (nodeData.markType === "icon") {
                vm.setAllIcons(data.rows, "icon", "", numFormat, result, level);
            } else if (nodeData.markType === "circle") {
                vm.setAllIcons(data.rows, "icon", "", numFormat, result, level);
                vm.setAllIcons(data.rows, "circle", nodeData.circle_radius, numFormat, result, level);
            } else if (nodeData.markType === "polygon") {
                vm.setAllPolygon(data.rows, numFormat, result, level);
            }
        },
        getLabelAndFun(nodeData) {
            let xLabel = "", yLabel = "", funcLabel = "";
            if (nodeData.xData.length) {
                yLabel = nodeData.xData[0].alias || nodeData.xData[0].label;
                let fast = nodeData.xData[0].fastCount && nodeData.xData[0].fastCount.value && nodeData.xData[0].fastCount.value !== "none" ?
                    '-' + nodeData.xData[0].fastCount.name : "";
                funcLabel = "[" + nodeData.xData[0].func.name + fast + "]";
            }
            xLabel = "(" + (nodeData.yData[0].alias || nodeData.yData[0].label) + ")";
            return {xLabel, yLabel, funcLabel};
        },
        setAllIcons(rows, type, radius, numFormat, result, level) {
            const vm = this;
            for (let i = 0; i < rows.length; i++) {
                let item = rows[i], latAndLon = item.latAndLon && JSON.parse(item.latAndLon);
                if(!latAndLon) continue;
                let latlng = [latAndLon.coordinates[1], latAndLon.coordinates[0]];
                let paramsVal = numFormat && numFormat !== "none" ? numerify(item.value, vm.format[numFormat]) : item.value;
                if (type === "icon") {
                    vm.addMarker(latlng, item.dim, item.value, paramsVal, result);
                } else if (type === "circle") {
                    vm.addCircle(latlng, radius, item.dim, item.value, paramsVal, result);
                }
            }
            vm.map.flyTo(vm.map_center, level);
        },
        /**
         * 绘制多边形
         * @param rows
         * @param numFormat
         * @param xLabel
         * @param yLabel
         * @param funcLabel
         * @param level
         */
        setAllPolygon(rows, numFormat, {xLabel, yLabel, funcLabel}, level) {
            const vm = this , {marker_style} = vm;
            for (let i = 0; i < rows.length; i++) {
                let item = rows[i], latAndLon = JSON.parse(item.latAndLon);
                let paramsVal = numFormat && numFormat !== "none" ? numerify(item.value, vm.format[numFormat]) : item.value;
                let layer = {};
                layer.xys = latAndLon;
                layer.option = {...marker_style};
                let polygon = vm.map.addPolygon(layer);
                polygon.name = item.dim;
                let dom = item.value ? `<h4>${item.dim + xLabel} :<br>${yLabel}: ${paramsVal + funcLabel} </h4>` : `<h4>${item.dim + xLabel} </h4>`;
                vm.bindTooltip(polygon , dom);
                vm.map.onLayerEvent("click",polygon, this.mapLinkage,"clickname");
                vm.mapLayers.push(polygon);
            }
            vm.map.flyTo(vm.map_center, level);
        },
        setMapExtend(data) {
            this.setTitleTxt(data.title);
            this.setMapBounds(data.funcSettings);
        },
        setMapBounds(data) {
            if (data.mapCode[0] === "china") {
                this.corner = [[3.86, 73.66], [53.55, 135.05]];
            } else {
                this.corner = this.$corner;
            }
        },
        loadEnd(){
            const vm = this;
            vm.map = vm.$refs.map;
            vm.map.map.setMaxBounds(vm.corner);
            if(vm.isGetMapData) vm.preview();
        },
        async reloadMap(){
            const vm = this;
            vm.isGetMapData = false;
            await vm.destroyMap();
            await vm.$refs.map.loadMap();
        },
        destroyMap() {
            const vm = this;
            if (vm.map == null) return;
            this.map.offMapEvent();
            vm.mapLayers.forEach(layer => {
                vm.map.removeLayer(layer);
            })
            vm.map = null;
            vm.mapLayers = [];
        },
        chartResize() {
            let m_event = new Event('resize');
            window.dispatchEvent(m_event);
        },
        createHtmlIcon(name) {
            return `<div class="ce-icon_marker"><span title="${name}" class="ce-icon_value">${name}</span></div>`;
        },
        mapLinkage(e) {
            let name = e.target.name;
            this.chartClickFn({name, seriesName: name});
        },
        /**
         * 配置 标记属性及事件
         * @param latlng
         * @param name
         * @param value
         * @param paramsVal
         * @param xLabel
         * @param yLabel
         * @param funcLabel
         */
        addMarker(latlng, name, value, paramsVal, {xLabel, yLabel, funcLabel}) {
            const vm = this;
            let layer = {};
            layer.xy = latlng;
            layer.option = {};
            layer.option.iconAnchor=[11,0];
            layer.option.html = vm.createHtmlIcon(paramsVal);
            let marker = vm.map.addDiv(layer);
            marker.name = name;
            let dom = value ? `<h4>${name + xLabel} :<br>${yLabel}: ${paramsVal + funcLabel} </h4>` : `<h4>${name + xLabel} </h4>`;
            vm.bindTooltip(marker , dom);
            vm.map.onLayerEvent("click",marker, vm.mapLinkage,"clickname");
            vm.mapLayers.push(marker);
        },
        bindTooltip(layer , content){
            let option={direction:'top'};
            this.map.bindTooltip(layer , content , option);
        },
        /**
         * 画圈
         * @param latlng
         * @param radius
         * @param name
         * @param value
         * @param paramsVal
         * @param xLabel
         * @param yLabel
         * @param funcLabel
         */
        addCircle(latlng, radius, name, value, paramsVal, {xLabel, yLabel, funcLabel}) {
            const vm = this, {marker_style} = this;
            let layer = {value, name};
            layer.xy= latlng;
            layer.option={...marker_style};
            //圆半径
            layer.option.radius = radius;//单位米
            let marker = vm.map.addCircle(layer);
            marker.name = name;
            let dom = value ? `<h4>${name + xLabel} :<br>${yLabel}: ${paramsVal + funcLabel} </h4>` : `<h4>${name + xLabel} </h4>`;
            vm.bindTooltip(marker , dom);
            vm.map.onLayerEvent("click",marker, vm.mapLinkage,"clickname");
            vm.mapLayers.push(marker);
        },
        preview() {
            const vm = this;
            vm.isGetMapData = true;
            if (!vm.node.widget) return;
            if(!vm.map) return;
            let {beforeData, widgetDataset} = JSON.parse(vm.node.widget),
                nodeData = beforeData ? JSON.parse(beforeData) : {};
            if (nodeData.yAxis && nodeData.yAxis.length === 0 || nodeData.zAxis && nodeData.zAxis.length === 0) {
                return;
            }
            if ((nodeData.markType === "circle" || nodeData.markType === "icon") && nodeData.uAxis && nodeData.uAxis.length === 0) {
                return;
            }
            if (!widgetDataset) {
                return;
            }
            if (!widgetDataset.tableCode) {
                return;
            }
            if (!widgetDataset.widgetDatasetDims && widgetDataset.widgetDatasetDims.length === 0) {
                return;
            }
            if (!widgetDataset.widgetDatasetMeasures && widgetDataset.widgetDatasetMeasures.length === 0) {
                return;
            }
            vm.setHistogramPanel();
        },
    }
}
