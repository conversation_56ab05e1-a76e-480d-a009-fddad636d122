import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common,listMixins],
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        }
    },
    data() {
        return {
            inputValueTable: "",
            buttonTxt: "新建模型",
            tableData: [],
            tHeadData: [
                {
                    prop: "modelName",
                    label: "名称",
                    minWidth: 200,
                    align: "left"
                },
                {
                    prop: "userName",
                    label: "创建人",
                    width: "",
                    align: "center"
                },
                {
                    prop: "editorAndDate",
                    label: "最后修改时间",
                    minWidth: 160,
                    align: "center" ,
                    resizable :false
                }, {
                    prop: 'operate',
                    label: '操作',
                    width: 200,
                    align: "center" ,
                    resizable :false
                }
            ],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe650;",
                    tip: "启动",
                    clickFn: this.carryOut,
                    condition: (right) => right.indexOf($right["dataMiningTaskRun"]) > -1
                },
                {
                    icon: "&#xe6c0;",
                    tip: "编辑",
                    clickFn: this.editModel,
                    condition: (right) => right.indexOf($right["dataMiningUpdateTrans"]) > -1
                },
                {
                    icon: "&#xe656;",
                    tip: "配置",
                    clickFn: this.baseSetting,
                    condition: (right) => right.indexOf($right["dataMiningUpdateSchedule"]) > -1
                },
                {
                    icon: "&#xe605;",
                    tip: "重命名",
                    clickFn: this.reName,
                    condition: (right) => right.indexOf($right["dataMiningUpdateTransName"]) > -1
                },
                /*{
                    icon: "<i class='el-icon-share'></i>",
                    tip: "分享",
                    clickFn: this.share,
                    condition: (right) => true
                },*/
                {
                    icon: "&#xe6f4;",
                    tip: "复制方案",
                    clickFn: this.copySolution,
                    condition: (right) => right.indexOf($right["dataMiningCopyTrans"]) > -1
                },{
                    icon: "&#xe790;",
                    tip: "预览结果集",
                    clickFn: this.previewResult,
                    condition: (right) => right.indexOf($right["dataMiningGetDataSets"]) > -1
                },
                {
                    icon: "&#xe6bf;",
                    tip: "移动",
                    clickFn: this.moveTo,
                    condition: (right) => right.indexOf("dataMiningMoveModel") > -1
                },/*{
                    icon: "&#xe856;",
                    tip: "移交",
                    clickFn: this.transfer,
                    condition: (right) => true
                },*/
                // {
                //     icon: "&#xe886;",
                //     tip: "发布",
                //     clickFn: this.serveIssue,
                //     condition: (right) => right.indexOf($right["processModelingTestService"]) > -1
                // },
                {
                    icon: "&#xe65f;",
                    tip: "删除",
                    clickFn: this.deleteModel,
                    condition: (right) => right.indexOf($right["dataMiningDeleteTrans"]) > -1
                },
            ],
            dirId: "-1",
            dirType: "DIG_DIR_MF",
            addModelR :$right['dataMiningCreateTrans'] ,
        }
    },
    methods: {
        serveIssue(row){
            this.$emit("serveIssue" , row)
        },
        share(row){
            this.$emit("share" , row);
        },
        transfer(row){},
        previewResult(row){
            this.$emit("previewResult" , row);
        },
        menuCommand(command) {
            command.m_icon.clickFn(command.row, command.$index);
        },
        deleteInit(data) {
            this.dirId = data.id;
            this.dirType = data.name === "我的" ?  "DIG_DIR_MF" : data.dirType;
            this.changePage(1);
        },
        update(){
            this.changePage(this.currentPage);
        },
        changePage(index) {
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                pageSize: vm.paginationProps.pageSize,
                pageNum: index,
                condition: vm.inputValueTable,
                dirId: vm.dirId,
                dirType: vm.dirType
            };
            vm.tableData = [];
            services.queryTransList(data, settings).then(res => {
                if (res.data.status === 0) {
                    let callbackData = res.data.data;
                    vm.tableData = [];
                    if (callbackData.data.length) {
                        callbackData.data.forEach(item => {
                            let data = {
                                modelName: item.name,
                                userName: item.userName,
                                editorAndDate: item.releasetime,
                                releaseDate: item.releasetime,
                                modelType: item.classifyname,
                                modelSpec: item.memo,
                                statusCode: 0,//&#xe66b
                                transId: item.id,
                                dirParentId: item.parentid,
                                isNew: false
                            };
                            vm.tableData.push(data);
                        });
                        vm.total = callbackData.totalCount;
                    }
                }
            })

        },
        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        openNewModel() {
            this.$emit('openNewModel' , this.dirId);
        },
        //操作
        carryOut(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.confirm('提示', '此操作将执行任务, 是否继续?', () => {
                vm.$message.success("开始执行！");
                services.runJob(row.transId).then(res => {
                    console.log(res.data.data);
                })
            })
        },
        startJob(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            services.startJob(row.transId).then(res => {
                if (res.data.status === 0) {
                    vm.$message({
                        type: 'success',
                        message: "开始执行",
                    })
                }
            })
        },
        editModel(row) {
            this.$emit("modelEdit", row);
        },
        baseSetting(row) {
            this.$emit("tableItemEvent", row);
        },
        copySolution(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.confirm("复制" , `确认复制 \'${row.modelName}\' 方案？` , ()=>{
                services.copyTrans(row.transId, row.dirParentId).then(res => {
                    if (res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: "复制成功",
                        })
                        this.changePage(1);
                    }
                })
            })

        },
        reName(row, index) {
            this.$emit("reName", row, index);
        },
        moveTo(row) {
            this.$emit("moveTo" , row ,"dataCenter","move");
        },
        deleteModel(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.confirm( '提示' ,`此操作将删除 \"${row.modelName}\" 方案, 是否继续?`, ()=>{
                services.deleteTrans(row.transId).then(res => {
                        if (res.data.status === 0) {
                            vm.$message({
                                type: 'success',
                                message: '删除成功!',
                            });
                            vm.changePage(1);
                        }
                    })
            })
        },
        setDirType(value, id , node) {
            if(id === "0"){
                this.dirId = "-1";
                this.dirType = "DIG_DIR_MF";
            }else if ( (value === "我的" && node.data.pId === "0" )) {
                this.dirId = id;
                this.dirType = "DIG_DIR_MF";
            }else if(value === "标准模型"){
                this.dirId = id;
                this.dirType = node.data.dirType;
            } else {
                this.dirId = id;
                this.dirType = value;
            }
            this.changePage(1);
        },
        tDataChangeWithName(newName, index) {
            const vm = this;
            vm.tableData.forEach((tab , i) => {
                if(i === index){
                    tab.modelName = newName
                }
            });
        },
        getJobStatus() {
            this.tableData.forEach((item, i) => {
                this.$axios.get("/transSchedule/jobStatus?transId=" + item.transId)
                    .then(res => {
                        // console.log("状态码：",res.data,this.tableData.length);
                        // item.statusCode = res.data.status;
                        if (res.data.status === 0) {
                            if (res.data.data.status === "none") {
                                item.statusCode = 0;
                            } else if (res.data.data.status === "success") {
                                item.statusCode = 1;
                            } else if (res.data.data.status === "execing") {
                                item.statusCode = 5;
                            } else {
                                item.statusCode = 2;
                            }
                        }

                    })
                    .catch(err => {
                        console.log(err);
                    })
            });
        },
    },
    created() {
        // this.changePage(1);
    }
}
