import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
export default {
    name: "add-service",
    mixins : [commonMixins , servicesMixins, listMixins, common],
    components: {  },
    computed : {
    } ,
    props: {
        rows:Object,
        rowData: Object,
        interfaceDetailShow:Boolean
    }   ,
    data() {
        return {
            // 角色管理表格数据
            tableData: [],
            visible:false,
            serviceName:"",
            serviceStyle:"",
            options:[],
            optionsClass:[],
            interfaceName:"",
            serviceClass:"",
            interfaceData:[],
            columnTypeOptions:[{
                value:"TEXT",
                label:"文本"
            },{
                value:"NUMBER",
                label:"数字"
            },{
                value:"DATE",
                label:"时间"
            }],
            deleteParameterLists: [],
        };
    },
    methods: {
        serviceStyleChange(value, label){
            if (value === "88bf745f0a0b46238d627c08ea51f5d6") {
                this.optionsClass = [

                    {
                        value: 'easyQuery',
                        label: '简单查询'
                    }, {
                        value: 'pageQuery',
                        label: '分页查询'
                    }]
                this.serviceClass = "easyQuery"
            }
        },
        getServiceStyle() {
            const vm = this , {busServices , groupMock} = this;
            let services = vm.getServices(busServices );
            services.getServiceTypeList().then(res =>{
                if (res.data.status === 0) {
                    res.data.data.forEach(element => {
                        let option = {
                            value: element.id,
                            label: element.name
                        }
                        this.options.push(option);
                        let optionC = {
                            value: element.childrenTypes[0].id,
                            label: element.childrenTypes[0].name,
                        }
                        this.optionsClass.push(optionC)
                    });
                    this.serviceClass = res.data.data[0].childrenTypes[0].id;
                    this.serviceStyle = res.data.data[0].id;
                }
            });
        },
        submit(){
            const vm = this , {busServices , groupMock,settings} = this;
            let services = vm.getServices(busServices ), conditionList = [];
            if (this.serviceStyle === "" || this.serviceClass === "" || this.interfaceName === "" ) {
                this.$message({
                    message: '请输入完整信息',
                    type: 'warning'
                });
                return;
            }
            let isInterfaceInso = true;
            this.interfaceData.forEach(element => {
                if (element.name !=="" && element.code !== "" && element.columnType !== undefined) {
                    let oneRow = {
                        id: element.id,
                        name: element.name,
                        code: element.code,
                        columnType: element.columnType,
                        defaultValue: element.defaultValue,
                        parameterType: 0
                    }
                    conditionList.push(oneRow);
                }else {
                    isInterfaceInso = false;
                }
            });
            if (!isInterfaceInso) {
                this.$message({
                    message: '请输入完整请求参数信息',
                    type: 'warning'
                });
                return;
            };
            let isDuplicate = false;
            for (let i = 0; i < this.interfaceData.length; i++) {
                for (let j = i+1; j < this.interfaceData.length; j++) {
                    if (this.interfaceData[i].name === this.interfaceData[j].name) isDuplicate = true;
                }
            }
            if (isDuplicate) {
                this.$message({
                    message: '请勿输入相同的字段中文名称',
                    type: 'warning'
                });
                return;
            };
            let interfaceCfgVo = {
                serviceType: this.serviceStyle,
                interfaceType: this.serviceClass,
                serviceInfoId: this.rows.id,
                name:this.interfaceName,
                interfaceParameterCfgVoList: conditionList,
                id:this.rowData.id,
                deleteParameterLists:this.deleteParameterLists
            };
            settings.loading = true;
            services.saveInterfaceBody(interfaceCfgVo, settings).then(res =>{
                if (res.data.status === 0) {
                    this.$message({
                        message: '保存成功',
                        type: 'success'
                    });
                    this.$emit("closeDialog")
                    this.$emit("changePage");
                }
            });
        },
        show(row) {
            this.serviceName = this.rows.name
        },
        add(){
            this.interfaceData.push({
                code:"",
                name:"",
                serviceType:"",
                isRequired:false,
                defaultValue:"",
                addNew:true
            })
        },
        deleteLast(index, row) {
            const vm = this;
            vm.confirm("删除" , "确定删除该条件" , ()=>{
                this.interfaceData.splice(index, 1);
                this.deleteParameterLists.push(row.id)
            });
        },
        async reNew() {
            if (this.rowData.id === undefined) return;
            this.reNewId = this.rowData.id;
            this.interfaceName = this.rowData.name;
            this.serviceStyle = this.rowData.serviceType;
            const vm = this , {busServices , groupMock, settings} = this;
            settings.loading = true;
            let services = vm.getServices(busServices );
            await services.getBodyAttributeByCfgId(this.rowData.id, settings).then(async res => {
                if (res.data.status === 0) {
                    this.interfaceData = res.data.data.interfaceParameterCfgVoList,
                    this.serviceClass = res.data.data.interfaceType;
                }
            });
        }
    },
    created(){
        this.getServiceStyle();
        if (this.rowData.id !== undefined) this.reNew();
    }
};