<template>
    <div class="portal-header"
         :style="{backgroundColor: currTheme.headerBgColor}"
    >
        <div class="portal-header__logo">
            <div class="portal-header__logo-image">
                <img v-show="config.logo" :src="config.logo"/>
                <div v-show="!config.logo" class="portal-header__logo-image-placeholder">Logo</div>
            </div>
            <h3 :title="config.title">{{ config.title }}</h3>
        </div>
        <div class="portal-header__navs">
            <div class="ce-menu_box">
                <span class="ce-arrow_left" v-if="showArrow && navs.length" @click="goLeft"><em class="el-icon-arrow-left"></em></span>
                <span class="ce-arrow_right" v-if="showArrow && navs.length" @click="goRight"><em class="el-icon-arrow-right"></em></span>
                <auto-container class="ce-contain_w" ref="container" hidden-btn :child-tag="children" @showBtn="showBtn">
                    <template slot="contain">
                        <portal-menu
                                ref="menu"
                                :menus="navs"
                                :default-active="active || ''"
                                mode="horizontal"
                                :background-color="currTheme.headerBgColor"
                                text-color="#fff"
                                :active-text-color="currTheme.headerTextActive"
                                @select="handleSelect"
                        ></portal-menu>
                    </template>
                </auto-container>
            </div>
        </div>
        <div class="portal-header__admin">
            <i class="icon f18">&#xe690;</i>
            <span>{{ userInfo.objName }}</span>
            <i class="el-icon-switch-button poi" v-if="isPreview" @click="handleLogOut"></i>
        </div>
    </div>
</template>

<script src="./portal-header.js"></script>
<style scoped lang="less" src="./portal-header.less"></style>
