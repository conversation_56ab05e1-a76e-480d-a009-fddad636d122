<template>
    <common-dialog
            :title="title"
            width="1000px"
                :visible.sync="visible"
                @close="closeDialog"
                v-loading="settings.loading">
        <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="functionCode"></help-document></div>
            <div v-if="renew">
                <addService ref="addNewService" v-if="serviceShow" @closeDialog="closeDialog"
                            @changePage="changePage" :rowData="rowData" :serviceDetailShow="serviceDetailShow" :busId="busId"
                            ></addService>
                <addInterface ref="addNewInterface" v-else @closeDialog="closeDialog" :rows="rows" @changePage="changePage" :rowData="rowData" :interfaceDetailShow="interfaceDetailShow"></addInterface>
            </div>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false" v-if="submitShow">{{btnCancelTxt}}</dg-button>
            <dg-button @click="visible = false" v-if="!submitShow">关闭</dg-button>
            <dg-button type="primary" @click="submit" v-if="submitShow">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>
<script lang="js" src="./dialog.js"></script>
<style lang="less" scoped src="./dialog.less"></style>

