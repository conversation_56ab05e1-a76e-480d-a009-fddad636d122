<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <template v-if="!isOtherUser">
                <dg-button @click="addVisualPanel" v-if="ifHasRight(addDashboard)" type="primary">{{ buttonTxt }}</dg-button>
                <dg-button @click="share">{{ batchShareTxt }}</dg-button>
                <dg-button @click="linkToShare">{{ myShareTxt }}</dg-button>
            </template>
            <div class="d-s-r-t-serach">
                <el-input
                    size="mini"
                    placeholder="请输入名称搜索"
                    v-model.trim="inputValueTable"
                    v-input-limit:trim
                    @input="inputFilterSpecial($event , 'inputValueTable')"
                    @keyup.enter.native="searchTableEvent"
                >
                    <i
                        class="el-icon-search el-input__icon poi"
                        slot="suffix"
                        @click="searchTableEvent">
                    </i>
                </el-input>
            </div>
        </div>
        <div class="d-s-r-table">
            <common-table
                :data="tableData"
                :columns="tHeadData"
                :paging-type="isMock ? 'client' : 'server'"
                :pagination-props="paginationProps"
                class="width100"
                :max-height="tableBodyH"
                :border="false"
                noneImg
                :pagination-total="total"
                @change-current="isMock ? ()=>{} : changePage($event) "
                @change-size="changeSize"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <span class="r-c-action"
                          v-for="(item,index) in hasRightOptIcon"
                          :key="index"
                          v-if="index < 3"
                    >
                        <dg-button type="text"
                                   :class="item.class"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.name"
                                   v-html="item.name"
                        ></dg-button>
                    </span>
                    <dir-edit-action v-if="countHasRightMenu > 3"
                                     placement="bottom"
                                     @command="menuCommand($event , row , $index)" :data="hasRightOptIcon.slice(3)"
                                     :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>{{ moreTxt }}</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
            </common-table>
        </div>
        <save-dialog ref="move" @reLoadList="moveToSave" @newModel="newModel"></save-dialog>
        <advanced ref="advanced"></advanced>
        <AuthDialog ref="auth" @success="reNewTable"/>
    </div>
</template>

<script src="./list.js"></script>

<style scoped lang="less" src="./list.less"></style>
