<template>
    <div class="tree">
        <el-input placeholder="请输入名称" v-model="filterText" size="medium" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn">
            <el-tree
                    class="filter-tree"
                    v-loading="loading"
                    :data="data"
                    :props="defaultProps"
                    node-key="id"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    :highlight-current="true"
                    @node-click="getCenterTable"
                    @node-contextmenu="rightMenu"
                    ref="tree"
                    :default-expand-all="true"
            >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-label el-tree-node__label"
                          :title="node.label"
                          :class="{
                            'el-icon-folder' : !node.expanded,
                            'el-icon-folder-opened' : node.expanded,
                          }"
                    >{{ node.label }}</span>
                </span>
            </el-tree>
        </div>
        <ul id="ce_menu" class="ce-tree__menu" v-show="menuVisible">
            <li v-for="(item , i) in treeMenu"
                :key="i" @click="item.fn"
            >{{item.name}}
            </li>
        </ul>
    </div>
</template>

<script>
    import LeftTree from '../../component/tree/LeftTree'

    export default {
        name: "BeatLibraryModelingTree",
        extends: LeftTree,
        props:{
            isMove:Boolean,
        },
        data(){
            return{
                loading:false,
                menu1: [
                    {
                        name: '新增子目录',
                        fn: this.addChildren
                    }
                ],
                treeMenu: [],
                menu2: [
                    {
                        name: '修改目录',
                        fn: this.editNode
                    }, {
                        name: '删除目录',
                        fn: this.deleteNode
                    }
                ],
                defaultProps:{
                    parent: 'parentId',
                    id: 'id',
                    label: 'name',
                    children: 'children',
                }
            }
        },
        methods: {
            updateDirName(val){
                //this.initTree();
                let node = this.getTreeNode();
                node.children.forEach((child , i)=>{
                    if(child.id === node.data.id){
                        child.name = val.chName;
                        child.code = val.engName;
                    }
                })



            },
            addChildren(){
                this.$emit("showNewNamePanel",'newDir',this.selectedData);
            },
            editNode(){

                this.$emit("showNewNamePanel",'reNameDir',this.selectedData);
            },

            initTree() {
                this.loading = true;
                this.data = [];
                this.loading = false;
                this.$axios.get("/manage/sql/queryTree").then(res => {
                    this.loading  =false;
                    if(res.data.status === 0){
                        // this.data = res.data.data;
                        this.data = [];
                        res.data.data.forEach(item =>{
                            let editable = false;
                            if(item.name === "我的"){
                                editable  = true;
                            }
                           let parentNode = {
                               id:item.id,
                               code:item.code,
                               name:item.name,
                               children:[],
                               parentId:item.parentId,
                               editable:editable,
                           };
                           item.children.forEach(val =>{
                              let childNode = {
                                  id:val.id,
                                  code:val.code,
                                  name:val.name,
                                  children:[],
                                  parentId:val.parentId,
                                  editable:editable,
                              };
                               parentNode.children.push(childNode);
                           });
                            this.data.push(parentNode);
                        });
                    }else {
                        this.$message.error("获取目录树失败！");
                    }
                }).catch(err => {
                    console.log("错误信息：",err);
                    this.$message.error("服务器异常，请联系管理员！");
                });
            },
            editTree(value) {
                const _this = this;
                let node = this.getTreeNode();
                this.selectedData.label = value;
            },
            addTreeNode(value) {
                let newTreeNode = {
                    label: value,
                    id: this.nodeId++,
                    editable: true
                };
                if (!this.selectedData.children) {
                    this.$set(this.selectedData, 'children', []);
                }
                this.selectedData.children.unshift(newTreeNode);
            },
            deleteNode() {
                let node = this.getTreeNode();
                const index = node.children.findIndex(d => d.id === node.data.id);
                this.$confirm('确定要删除该目录及目录下的模型吗?', '删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$axios.get("/manage/sql/delSqlTreeNodeAndSonNodes?id="+this.selectedData.id).then(res =>{
                        if(res.data.status === 0){
                            node.children.splice(index, 1);
                            this.$emit("initTable");
                            this.$message({
                                type: 'success',
                                message: res.data.data,
                            });
                        }else {
                            this.$message({
                                type: 'error',
                                message: '删除失败!'
                            });
                        }

                    });

                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            rightMenu(event, object, node, element) {
                // console.log("右击：",this.hasSave);
                if (this.hasSave === false) {
                    if (object.editable) {
                        this.showMenu(node, event, object);
                    } else {
                        this.menuVisible = false;
                    }
                    if (node.level === 1) {
                        this.treeMenu = this.menu1;
                    } else if (node.level === 2) {
                        this.treeMenu = this.menu2;
                    }
                }
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
        }
    }
</script>

<style scoped lang="less">

    .tree {
        float: left;
        margin: 10px 0 0 10px;
        width: 230px;
        padding: 10px;
        background: #fff;
        border: 1px solid #ddd;
        height: calc(100% - 88px);
    }

    .ce-tree {
        margin-top: 12px;
        height: calc(100% - 50px);
        overflow: auto;
    }

    .ce-tree__menu {
        position: fixed;
        top: 0;
        min-width: 80px;
        text-align: left;
        border: 1px solid #ccc;
        background: #fff;
        padding: 0;
        z-index: 100;
        box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
        border-radius: 2px;
    }

    .ce-tree__menu li {
        cursor: pointer;
        list-style: none outside none;
        font-size: 12px;
        white-space: nowrap;
        border-bottom: 1px solid #eee;
        padding: 0 12px;
        height: 28px;
        line-height: 28px;
        color: #666;
    }

    .ce-tree__menu li:hover {
        color: @font-color;
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }

    .node-label {
        max-width: 166px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .node-label::before {
        padding-right: 5px;
    }
</style>
