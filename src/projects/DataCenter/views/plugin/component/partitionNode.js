export const partitionNode = {
    data(){
        return {
            partitionNode: false,//分区字段
            partitionFieldName : [],
            columns:[]
        }
    },
    methods : {
        setAllColumns(columns){
            return columns.map(i => {
                return {
                    name: i.columnZhName || i.columnName,
                    code: i.columnName,
                    type: i.columnType
                };
            })
        },
        setPartitionColumns(columns){
            this.columns = columns;
        },
        setPartitionName(name){
            this.partitionFieldName = name;
        },
        /**
         * 条件字段 联动 删除 分区字段
         */
        removeValue(val){
            this.partitionFieldName = this.partitionFieldName.filter(name => val.indexOf(name) > -1);
        }
    }
}
