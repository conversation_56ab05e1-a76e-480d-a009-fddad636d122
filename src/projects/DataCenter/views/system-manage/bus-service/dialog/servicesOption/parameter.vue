<template>
    <div class="param mb10">
        <div class="mb10">
            <span class="mr20">{{title}}</span>
            <el-button size="mini">{{jsonTxt}}</el-button>
        </div>
        <div>
            <el-form>
                <common-table
                        ref="tableTree"
                        class="ce-table"
                        :data="tableData"
                        row-key="id"
                        :columns="tHeadData"
                        :pagination="false"
                        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                        size="mini"
                >
                    <template slot="header-operate">
                        <el-button type="text" title="新增" icon="el-icon-circle-plus" @click="customAdd"></el-button>
                    </template>
                    <template v-for="(item ,inx) in tHeadData" :slot="item.prop" slot-scope="{row , $index}"  >
                        <el-form-item :prop="`list.${$index}[${item.prop}]`" :key="inx">
                            <el-checkbox v-model="row[item.prop]" v-if="item.prop === 'required'"></el-checkbox>
                            <el-input v-model="row[item.prop]" v-if="['name','desc','length','example'].includes(item.prop)"></el-input>
                            <dg-select  v-model="row[item.prop]" :data="[]" v-if="item.prop === 'dataType'"></dg-select>
                            <div v-if="item.prop === 'operate'">
                                <el-button slot="reference" type="text" icon="el-icon-circle-plus-outline" class="mr10" title="添加子行" @click="addChild(row ,$index)"></el-button>
                                <el-popconfirm
                                        size="mini"
                                        title="确认删除该行及其所有子行?"
                                        @confirm="customDel(row ,$index)">
                                    <el-button slot="reference" type="text" icon="el-icon-remove-outline"></el-button>
                                </el-popconfirm>
                            </div>
                        </el-form-item>
                    </template>
                </common-table>
            </el-form>
        </div>
    </div>
</template>

<script>
    export default {
        name: "parameter",
        props:{
            title: String,
            type: String,
        },
        data(){
            return {
                jsonTxt: this.type != 'look' ? 'JSON导入' : 'JSON查看',
                tableData: [
                    {id:'1',name:'123',children:[{name:'456',id:'3',}]},
                    {id:'2',name:'dgdg',children:[{name:'tr',id:'4',}]},
                ],
                tHeadData: [
                    {
                        prop: 'name',
                        label: "参数名",
                        minWidth: 150,
                        'show-overflow-tooltip': false ,
                        'table-column-overflow': 'origin',
                    }, {
                        prop: 'desc',
                        label: "参数描述",
                        minWidth: 120
                    }, {
                        prop: 'dataType',
                        label: "数据类型",
                        minWidth: 80,
                    },{
                        prop: 'length',
                        label: "长度",
                        minWidth: 80,
                    },{
                        prop: 'example',
                        label: "示例值",
                        minWidth: 100,
                    },{
                        prop: 'required',
                        label: "是否必填",
                        minWidth: 70,
                        align : "center",
                    },{
                        prop: 'operate',
                        label: "操作",
                        width: 80,
                        align : "center",
                    },
                ]
            }
        },
        methods:{
            guid() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                    let r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                })
            },
            /**
             * 添加列表
             */
            customAdd(){
                this.tableData.unshift({
                    id:this.guid(),
                    name:'',
                    children:[],
                })
            },
            /**
             * 添加子行
             */
            addChild(row, inx){
                const vm = this;
                let id = vm.guid();
                row.children.unshift({
                    id:id,
                    name:'',
                    children:[],
                })
                vm.$refs.tableTree.$refs.table.toggleRowExpansion(row, true);//展开
            },
            deleteTree(data,id){
                let newTree = data.filter(x => x.id !== id)
                newTree.forEach(x => x.children && (x.children = this.deleteTree(x.children,id)))
                return newTree
            },
            /**
             * 删除列表
             */
            customDel(row, inx){
                const vm = this;
                vm.tableData = vm.deleteTree(vm.tableData, row.id);
            }
        }
    }
</script>

<style scoped lang="less">
    .param{
        /deep/ .cell{
            display: flex;
        }
    }
</style>