<template>
    <div class="dataSource">
        <div class="left-arrow" :class="{'left-arrow_show' : slide }" @click="slideSource">{{moduleName}}</div>
        <div class="custom-leftNav" :class="{'leftNav_hide' : slide}">
            <em class="icon data_a" @click="slideSource">&#xe71c;</em>
            <div class="card-title">
                <span>{{moduleName}}</span>
                <!--<div class="r" v-if="!isTemplate">
                    <el-switch size="mini" v-model="general"></el-switch><span class="pl5 n g6">通用</span>
                </div>-->
            </div>
            <!--<ul class="list-tab" v-if="!isTemplate">
                <li class="list-item"
                    v-for="(item , index) in listTabs"
                    :key="index"
                    :class="{'active':index === tabActive}"
                    @click="listTabClick(index)"
                >{{item}}</li>
            </ul>-->
            <!--<ul class="tab-cont" v-if="!isTemplate">
                <li class="tab-child" v-for="(item , index) in listTabs" :key="index" v-show="index === tabActive">
                    <LocalData :general="general" v-if="index === 0" />
                    <ListTreeCustom :general="general" v-if="index === 1"/>
                    <ResultReuse :general="general" v-if="index === 2"/>
                </li>
            </ul>-->
            <div class="ce-tree_cont">
<!--                <ListTreeCustom  :isTemplate="isTemplate" />-->
<!--                <DataSourceTree/>-->
                <DataSetTree :hasDataObj="hasDataObj" :isLogic="isLogic"/>
            </div>
        </div>
    </div>
</template>

<script>
    import LocalData from './LocalData'
    import ExternalFile from './ExternalFile'
    import ResultReuse from './ResultReuse'
    import {globalBus} from '@/api/globalBus';
    import ListTreeOriginal from './ListTreeOriginal'
    import ListTreeCustom from './ListTreeCustom'
    import DataSourceTree from './dataSourceTree/index'
    import DataSetTree from '../../../views/dataset-manage/tree/index'


    export default {
        name: "DataSource",
        components : {
            LocalData,
            ExternalFile,
            ResultReuse ,
            ListTreeOriginal ,
            ListTreeCustom,
            DataSourceTree,
            DataSetTree
        },
        props : {
            tabId : String ,
            isTemplate :Boolean
        },
        computed : {
            moduleName(){
                return this.isTemplate ? "数据视图" : "数据资源";
            }
        },
        data(){
            return {
                isLogic:true,
                listTabs : [
                    '自助数据集', '原始数据集' ,'SQL数据集'
                ],
                tabActive:0 ,
                slide : true ,
                general : true,
                hasDataObj: true
            }
        },
        methods : {
            listTabClick(index){
                this.tabActive = index;
            },
            slideSource(){
                this.slide = !this.slide;
                globalBus.$emit('slideS', this.slide);
            },
            hideSource(){
                globalBus.$on('hideSource',this.hideFn);
            },
            hideFn(hide , tabId){
                if(this.tabId !== tabId) return ;
                this.slide = hide;
            },
            showSource(){
                globalBus.$on('showSource',this.showFn);
            },
            showFn(show){
                this.slide = !show;
            }
        },
        created() {
            this.hideSource();
            this.showSource();
        },
        destroyed() {
            globalBus.$off('hideSource',this.hideFn);
            globalBus.$off('showSource',this.showFn)
        }
    }
</script>

<style scoped>
    .dataSource {
        float: left;
        height: calc(100% - 1.5rem - 76px);
    }
    .left-arrow {
        position: fixed;
        line-height: 1.2;
        font-size: 12px;
        color: #888;
        left:-28px;
        top: 218px;
        width: 16px;
        height: 68px;
        padding:6px 8px 0 4px;
        cursor: pointer;
        transition: 500ms;
        background:url("../../../assets/images/arrow/left-arrow.png") no-repeat center;
        z-index: 5;
    }
    .left-arrow:hover {
        background:url("../../../assets/images/arrow/left-arrowF.png") no-repeat center;
        color: #fff;
    }
    .left-arrow_show {
        left: 0;
    }
    .custom-leftNav {
        position: relative;
        margin:5px 0 0 5px;
        border: 1px solid #ddd;
        width: 220px;
        height: calc(100% - 14px);
        background: #fff;
        float: left;
        transition: 500ms;
        padding:0 5px;
    }
    .leftNav_hide {
        margin-left: -230px;
    }
    .data_a {
        position: absolute;
        left: 0;
        top: 3px;
        cursor: pointer;
        color: #777;
        padding: 4px 6px;
        text-align: right;
    }
    .card-title {
        border-bottom: 1px solid #ddd;
        font-weight: bold;
        padding-left: 20px;
        font-size:1rem;
        line-height: 38px;
        color: #555;
    }
    .list-tab {
        padding:8px 0;
        overflow: hidden;
        text-align: center;
    }
    .list-item {
        padding:4px 0;
        margin:0 2px;
        cursor: pointer;
        width:calc(33.3% - 4px);
        float: left;
        text-align: center;
        color: #555;
        border-radius: 2px;
        font-size: 12px;
    }
    .list-item:hover , .list-item.active {
        background-color: #289bf1;
        color: #fff;
    }
    .tab-cont {
        padding:0 6px;
        height: calc(100% - 82px);
    }
    .tab-child {
        height: 100%;
    }
    .ce-tree_cont {
        margin-top:10px;
        padding:0 6px;
        height: calc(100% - 55px);
    }
</style>