<template>
    <div class="baseSettingCont">
        <el-form :label-width="formLabelWidth" :model="form" size="mini">
            <!--            &lt;!&ndash; 运行实例 &ndash;&gt;
                        <el-form-item :label="formList[0].label">
                            <el-select v-model="form.instance" :placeholder="formList[0].placeholder">
                                <el-option
                                        v-for="item in optInstanceData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        &lt;!&ndash; 运行模式 &ndash;&gt;
                        <el-form-item :label="formList[1].label">
                            <el-select v-model="form.mode" :placeholder="formList[1].placeholder">
                                <el-option
                                        v-for="item in optProcessModeData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        &lt;!&ndash; 日志级别 &ndash;&gt;
                        <el-form-item :label="formList[2].label">
                            <el-select v-model="form.logLevel" :placeholder="formList[2].placeholder">
                                <el-option
                                        v-for="item in optLogLevelData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        &lt;!&ndash; 处理模式 &ndash;&gt;
                        <el-form-item :label="formList[3].label">
                            <el-select v-model="form.processMode" :placeholder="formList[3].placeholder">
                                <el-option
                                        v-for="item in optSolveModeData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>-->

            <!-- 调度计划 -->
            <el-form-item :label="formList[10].label">
                <el-radio-group @change="radPlanChange" class="planGroup" v-model="form.dispatchPlan">
                    <el-radio-button
                            :key="item.value"
                            :label="item.label"
                            v-for="item in radioDispatchPlan"
                    ></el-radio-button>
                </el-radio-group>
            </el-form-item>

            <div>
                <el-form-item
                        :label="planLeftTitle"
                        v-if="typeTiming || typeInterval">
                    <!-- 每日，周，月!-->
                    <el-select
                            :placeholder="time_placeholder"
                            @change="selectDWMChange"
                            class="execTime"
                            v-if="typeTiming"
                            v-model="execTime"
                    >
                        <el-option
                                :key="i"
                                :label="item.label"
                                :value="item.label"
                                v-for="(item , i ) in optExecTime"
                        ></el-option>
                    </el-select>
                    <!-- 1到12月!-->
                    <el-select
                            :placeholder="monthTxt"
                            class="execTime"
                            v-if="typeInterval"
                            v-model="intervalMonth"
                    >
                        <el-option :key="i" :label="item.label" :value="item.label"
                                   v-for="(item , i ) in optIntervalMonth"></el-option>
                    </el-select>
                    <label class="lable_time" v-if="typeInterval">{{monthTxt}}</label>
                    <!-- 周一到周天!-->
                    <el-checkbox-group
                            class="check_week"
                            v-if="weekShow"
                            v-model="checkWeekList">
                        <el-checkbox-button
                                :key="week"
                                :label="week"
                                v-for="week in checkWeekData"
                        >
                        </el-checkbox-button>
                    </el-checkbox-group>
                    <!--每月几号 !-->
                    <el-select
                            :placeholder="dayTxt"
                            class="time_select"
                            v-if="typeTiming && monthDayShow"
                            v-model="everyMonthDay"
                    >
                        <el-option
                                :key="i"
                                :label="item.label"
                                :value="item.label"
                                v-for="(item , i ) in optEveryMonthDay"
                        ></el-option>
                    </el-select>
                    <el-select
                            :placeholder="dayTxt"
                            class="time_select"
                            v-if="typeInterval"
                            v-model="intervalDay"
                    >
                        <el-option
                                :key="i"
                                :label="item.label"
                                :value="item.label"
                                v-for="(item , i ) in optIntervalDay"
                        ></el-option>
                    </el-select>
                    <label class="lable_time" v-if="monthDayShow">{{dayTxt}}</label>
                    <!-- 24小时!-->
                    <el-select :placeholder="hourTxt" class="time_select" v-model="everyDayHour">
                        <el-option
                                :key="i"
                                :label="item.label"
                                :value="item.label"
                                v-for="(item , i ) in optEveryDayHour"
                        ></el-option>
                    </el-select>
                    <label class="lable_time">{{hourTxt}}</label>
                    <!-- 60分钟!-->
                    <el-select :placeholder="minTxt" class="time_select" v-model="everyDayMin">
                        <el-option
                                :key="i"
                                :label="item.label"
                                :value="item.label"
                                v-for="(item , i ) in optEveryDayMin"
                        ></el-option>
                    </el-select>
                    <label class="lable_time">{{minTxt}}</label>
                </el-form-item>
            </div>
            <!-- 执行时间start!-->
            <!-- 日期开始，结束时间!-->
            <el-form-item
                    :label="formList[5].label"
                    v-show="typeOnce"
            >
                <!--                一次性-->
                <el-date-picker
                        :placeholder="date_placeholder"
                        type="datetime"
                        v-model="form.startTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
            <el-form-item
                    :label="formList[6].label"
                    v-show="typeTiming || typeInterval"
            >
                <!--                定时-->
                <el-date-picker
                        :end-placeholder="formList[9].label"
                        :picker-options="pickerOptions"
                        :start-placeholder="formList[8].label"
                        range-separator="-"
                        type="daterange"
                        v-model="form.timeRange"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item
                    :label="formList[11].label"
                    v-if="typeCron"
                    required
            >
                <el-input
                        :placeholder="formList[11].placeholder"
                        class="textarea"
                        v-model="form.cronInput"
                ></el-input>
            </el-form-item>
            <!-- 执行时间end!-->
            <!-- 调度方案说明-->
            <el-form-item :label="formList[12].label">
                <el-input
                        :placeholder="formList[12].placeholder"
                        :rows="4"
                        class="textarea"
                        resize="none"
                        type="textarea"
                        v-model="form.state"
                ></el-input>
            </el-form-item>


            <div>
                <el-form-item>
                    <el-button @click="add" size="mini" type="primary">添加方案</el-button>
                </el-form-item>
            </div>

            <!--<div v-if="isShowProgramme">

                <div>
                    <el-form-item :label-width="labelWidth" label="流程方案:" prop="selectTreeValue">
                        <ce-select-drop
                                :data="selectTreeOption"
                                :filterNodeMethod="filterNode"
                                :placeholder="selectTreePlaceholder"
                                :props="defaultProps"
                                :tree-props="treeBind"
                                @current-change="selectTreeChange"
                                check-leaf
                                check-strictly
                                filterable
                                scrollbar-class="dataset-drop"
                                v-model="form.selectTreeValue"
                        ></ce-select-drop>
                    </el-form-item>
                </div>

                <div>
                    <el-form-item :label-width="labelWidth" label="延迟(秒)执行:" prop="delayExecute">
                        <el-input @input="form.delayExecute = form.delayExecute.replace(/\D/g,'')"
                                  v-model="form.delayExecute">
                            <template slot="append"> 秒</template>
                        </el-input>
                    </el-form-item>
                </div>

                <div>
                    <el-form-item :label-width="labelWidth" label="是否始终执行:">
                        <el-checkbox v-model="form.isAlways"></el-checkbox>
                    </el-form-item>
                </div>

                <div>
                    <el-form-item>
                        <el-button @click="clearSubtrans" class="r" size="mini">确定删除</el-button>
                    </el-form-item>
                </div>
            </div>-->
            <el-form-item v-show="form.startPrograms.length">
                <el-table
                        border
                        :show-header="status"
                        :data="form.startPrograms"
                        size="mini"
                >
                    <el-table-column type="index" align="center" width="50px"></el-table-column>
                    <el-table-column align="center">
                        <template slot-scope="{row}">
                            <el-row class="mb5" :gutter="10">
                                <el-col class="ce-normal_item is-require" :span="6">
                                    <span>流程方案:</span>
                                </el-col>
                                <el-col :span="18">
                                    <ce-select-drop
                                            :data="selectTreeOption"
                                            :filterNodeMethod="filterNode"
                                            :placeholder="selectTreePlaceholder"
                                            :props="defaultProps"
                                            :tree-props="treeBind"
                                            @current-change="selectTreeChange"
                                            check-leaf
                                            check-strictly
                                            filterable
                                            scrollbar-class="dataset-drop"
                                            v-model="row.subtransId"
                                    ></ce-select-drop>
                                </el-col>
                            </el-row>
                            <el-row class="mb5" :gutter="10">
                                <el-col class="ce-normal_item is-require" :span="6">
                                    <span>延迟(秒)执行:</span>
                                </el-col>
                                <el-col :span="18">
                                    <el-input @input="row.subtransDelayTime = row.subtransDelayTime.replace(/\D/g,'')"
                                              v-model="row.subtransDelayTime">
                                        <template slot="append"> 秒</template>
                                    </el-input>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col class="ce-normal_item" :span="6">
                                    <span>是否始终执行:</span>
                                </el-col>
                                <el-col class="tl lh32" :span="18">
                                    <el-checkbox v-model="row.subtransIsenforce"></el-checkbox>
                                </el-col>
                            </el-row>


                        </template>
                    </el-table-column>
                    .
                    <el-table-column width="50px" align="center">
                        <template slot-scope="scope">
                            <i
                                    class="icon ce_link model_status"
                                    v-for="(col , inx) in operateIcons"
                                    :key="inx"
                                    :title="col.tip"
                                    v-html="col.icon"
                                    @click="col.clickFn(scope.row,scope.$index)"
                            ></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>


        </el-form>

    </div>
</template>


<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";
    import {treeMethods} from "@/api/treeNameCheck/treeName"

    export default {
        name: "BaseSettingCont",
        mixins: [commonMixins, servicesMixins],
        data() {
            const weekData = ["周一", "周二", "周三", "周四", "周五", "周六", "周天"];
            let pickerMinDate = '', endPicker = '';
            return {

                pickerOptions: {
                    onPick: ({maxDate, minDate}) => {
                        console.log('maxDate=' + maxDate);
                        console.log('minDate=' + minDate);
                        if (pickerMinDate === '') {
                            pickerMinDate = minDate;
                        }
                        if (maxDate !== null) {
                            endPicker = maxDate;
                            pickerMinDate = "";
                        } else {
                            pickerMinDate = minDate;
                        }

                    },
                    disabledDate(time) {
                        if (pickerMinDate !== '') {
                            let maxTime = new Date(pickerMinDate.getTime() + 3600 * 1000 * 24 * 60);
                            return time.getTime() >= maxTime.getTime() || time.getTime() < pickerMinDate.getTime();
                        } else {
                            return time.getTime() < new Date(new Date().toLocaleDateString()).getTime() - 1;
                        }
                    }
                },
                form: {
                    jobId: "",
                    instance: "",
                    mode: "",
                    logLevel: "",
                    processMode: "",
                    dispatchPlan: "手动",
                    carryTime: '',
                    startTime: '',
                    timeRange: [],
                    cronInput: "",
                    state: "",
                    intervalTime: "",
                    startPrograms: [],
                    transMetaId: "",
                },
                status: false,
                initExecTime: '每日',
                execTime: "",
                everyDayHour: 0,
                everyDayMin: 0,
                checkWeekList: [],
                everyDayStartTime: "",
                everyDayEndTime: "",
                everyMonthDay: 1,
                intervalDay: 0,
                onceStartTime: "",
                intervalMonth: 0,

                formList: [
                    {
                        label: '运行实例 :',
                        placeholder: '请选择运行实例'
                    }, {
                        label: '运行模式 :',
                        placeholder: '请选择运行模式'
                    }, {
                        label: '日志级别 :',
                        placeholder: '请选择日志级别'
                    }, {
                        label: '处理模式 :',
                        placeholder: '请选择处理模式'
                    }, {
                        label: '执行时间 :'
                    }, {
                        label: '开始时间 :'
                    }, {
                        label: '时间范围 :'
                    }, {
                        label: '间隔时间 :'
                    }, {
                        label: '开始时间'
                    }, {
                        label: '结束时间'
                    }, {
                        label: '调度计划 :'
                    }, {
                        label: 'cron表达式 :',
                        placeholder: '请填写cron表达式'
                    }, {
                        label: '调度方案说明 :',
                        placeholder: '请输入调度方案说明'
                    }
                ],
                radioDispatchPlan: [
                    // {id: 0, value: "0", label: "定时"},
                    {id: 1, value: "1", label: "手动"},
                    // {id: 2, value: "2", label: "一次性"},
                    // {id: 3, value: "3", label: "间隔"},
                    {id: 4, value: "4", label: "cron表达式"}
                ],
                optInstanceData: [
                    {
                        value: "",
                        label: ""
                    }
                ],
                optProcessModeData: [
                    {
                        value: "0",
                        label: "单机"
                    },
                    {
                        value: "1",
                        label: "分布式"
                    },
                    {
                        value: "3",
                        label: "Spark"
                    },

                ],
                optLogLevelData: [
                    {
                        value: "",
                        label: ""
                    }
                ],
                optSolveModeData: [
                    {
                        value: "",
                        label: ""
                    }
                ],
                optExecTime: [
                    {
                        label: "每日",
                        value: "everyday"
                    },
                    {
                        label: "每周",
                        value: "everyweek"
                    },
                    {
                        label: "每月",
                        value: "everymonth"
                    }
                ],
                typeTiming: false,
                typeOnce: false,
                typeCron: false,
                typeInterval: false,
                dayWMShow: false,

                monthShow: false,
                monthTxt: '月',
                dayTxt: '日',
                hourTxt: '时',
                minTxt: '分',

                planLeftTitle: "",
                time_placeholder: "",
                date_placeholder: "选择日期",

                weekShow: false,
                monthDayShow: false,

                optEveryDayHour: [],
                optEveryDayMin: [],
                optEveryMonthDay: [],
                optIntervalDay: [],
                optIntervalMonth: [],
                checkWeekData: weekData,
                formLabelWidth: "105px",

                dialogTableVisible: true,
                cornResult: [],
                selectTreePlaceholder: "请选择流程方案",
                selectTreeOption: [],
                treeProps: {
                    id: 'id',
                    label: 'label',
                    children: 'children',
                    parent: 'parentId',
                },
                labelWidth: "120px",
                defaultProps: {
                    value: 'id',
                    label: "label",
                    children: "children",
                },
                treeBind: {
                    "default-expand-all": true
                },
                isShowProgramme: false,
                operateIcons: [
                    {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteData}
                ],

            }
        },
        props: {
            rowData: Object,
        },
        methods: {
            initSelectValue() { // 没有值的时候初始化第一条数据， 有值再从接口读取值
                const _this = this;
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                services.baseSettingPage(this.rowData.transId).then(res => {
                    if (res.data.status === 0) {
                        //设置按钮的状态
                        vm.$emit("setActive", res.data.data.active);
                        vm.form.jobId = res.data.data.jobId;
                        //处理模式
                        vm.optSolveModeData = [];
                        res.data.data.handleModes.forEach((item, i) => {
                            if (i < 2) {
                                let model = {
                                    value: item.code,
                                    label: item.name,
                                };
                                vm.optSolveModeData.push(model);
                            }
                        });
                        vm.form.processMode = res.data.data.mode;
                        // 日志级别
                        vm.optLogLevelData = [];
                        res.data.data.logLevels.forEach((item, i) => {
                            if (item === res.data.data.logLevel) {
                                vm.form.logLevel = i;
                            }
                            let logLevelNode = {
                                value: i,
                                label: item
                            };
                            // console.log(logLevelNode);
                            vm.optLogLevelData.push(logLevelNode);
                        });
                        //运行模式
                        vm.form.mode = res.data.data.distributed;
                        //运行实例
                        vm.optInstanceData = [];
                        res.data.data.instanceList.forEach(item => {
                            let instance = {
                                value: item,
                                label: item,
                            };
                            vm.optInstanceData.push(instance);
                        });
                        // vm.form.instance = res.data.data.scheduleMeta.scheduleId;
                        //运行模式
                        vm.form.mode = res.data.data.distributed;
                        //运行实例
                        vm.optInstanceData = [];
                        res.data.data.instanceList.forEach(item => {
                            let instance = {
                                value: item,
                                label: item,
                            };
                            vm.optInstanceData.push(instance);
                        });
                        // vm.form.instance = res.data.data.scheduleMeta.scheduleId;
                        vm.form.state = vm.rowData.modelSpec;
                        // vm.changeMode(res.data.data.scheduleMeta);
                        // vm.radPlanChange(res.data.data.scheduleMeta);

                        let services = vm.getServices(modelingServices, modelingMock);
                        services.getSchedule(vm.rowData.transId).then(res => {
                            let data = res.data.data;
                            vm.form.cronInput = data.cronExpression;
                            vm.form.dispatchPlan = data.scheduleType;
                            vm.form.state = data.scheduleDeclare;
                            vm.radPlanChange2();
                        })
                    }
                })
            },
            changeMode(data) {
                switch (data.schemeScheduleMode) {
                    case '0':
                        this.form.dispatchPlan = "手动";
                        break;
                    case '1':
                        this.form.dispatchPlan = "定时";
                        break;
                    case '2':
                        this.form.dispatchPlan = "一次性";
                        break;
                    case '3':
                        this.form.dispatchPlan = "间隔";
                        break;
                    case '5':
                        this.form.dispatchPlan = "cron表达式";
                        break;
                    default:
                        break;

                }
            },
            formattingDate(date) {
                // return new Date(date.slice(0, 4), date.slice(4, 6), date.slice(6, 8));
                return date.slice(0, 4) + "-" + date.slice(4, 6) + "-" + date.slice(6, 8);
            },
            carryTimeFn() {
                let result = '';
                switch (this.execTime) {
                    case "每日":
                        this.form.carryTime = this.execTime + '/' + this.everyDayHour + '/' + this.everyDayMin;
                        break;
                    case "每周" :
                        this.form.carryTime = this.execTime + '/' + this.checkWeekList.join(',') + '/' + this.everyDayHour + '/' + this.everyDayMin;
                        break;
                    case "每月" :
                        this.form.carryTime = this.execTime + '/' + this.everyMonthDay + '/' + this.everyDayHour + '/' + this.everyDayMin;
                        break;
                    default :
                        break;
                }
                return result;
            },
            getFormData() {
                this.carryTimeFn();
                this.form.intervalTime = this.intervalMonth + "/" + this.intervalDay + "/" + this.everyDayHour + "/" + this.everyDayMin;
                return this.form;
            },
            getIsShowProgramme() {
                return this.isShowProgramme;
            },
            radPlanChange2() {
                switch (this.form.dispatchPlan) {
                    case "手动":
                        this.switchLayout(false, false, false, false, false, '');
                        break;
                    case "cron表达式" :
                        this.form.dispatchPlan = "cron表达式";
                        this.switchLayout(false, false, true, false, false, '');
                }
            },
            radPlanChange(scheduleMeta) {
                switch (this.form.dispatchPlan) {
                    case '定时' :
                        this.switchLayout(true, false, false, false, true, this.formList[4].label);
                        this.monthShow = false;
                        this.execTime = this.initExecTime;
                        if (scheduleMeta.scheudleInfo !== undefined) {
                            // this.execTime + '/' + this.everyDayHour + '/' + this.everyDayMin;
                            let currentTime = scheduleMeta.scheudleInfo.split(' ');
                            this.execTime = currentTime[1];
                            if (this.execTime.indexOf("月") > -1) {
                                this.execTime = "每月";
                                let monthDay = currentTime[1].slice(2, currentTime[1].length);
                                this.everyMonthDay = monthDay.length === 2 ? monthDay.slice(0, 1) : monthDay.slice(0, 2);

                            }
                            let hour = currentTime[2].split("时");
                            this.everyDayHour = hour[0];
                            let min = hour[1].split('分');
                            this.everyDayMin = min[0];
                            let beginTime = new Date(this.formattingDate(scheduleMeta.beginTime)),
                                endTime = new Date(this.formattingDate(scheduleMeta.endTime));
                            this.form.timeRange = [beginTime, endTime];
                            //this.form.timeRange[1] = ;
                        }
                        this.selectDWMChange();
                        break;
                    case "手动":
                        this.switchLayout(false, false, false, false, false, '');
                        break;
                    case  "一次性" :
                        this.switchLayout(false, true, false, false, false, '');
                        if (scheduleMeta.scheudleInfo !== undefined) {
                            let date = scheduleMeta.beginTime;
                            let time = date.slice(0, 4) + "-" + date.slice(4, 6) + "-" + date.slice(6, 8) +
                                " " + date.slice(8, 10) + ":" + date.slice(10, 12) + ":" + date.slice(12, 14);
                            this.form.startTime = new Date(time);
                        }
                        break;
                    case "间隔" :
                        this.switchLayout(false, false, false, true, false, this.formList[7].label);
                        this.weekShow = false;
                        this.monthDayShow = true;
                        this.monthShow = true;
                        if (scheduleMeta.scheudleInfo !== undefined) {
                            this.intervalMonth = scheduleMeta.scheduleMonth;
                            this.intervalDay = scheduleMeta.scheduleDay;
                            this.everyDayHour = scheduleMeta.scheduleHour;
                            this.everyDayMin = scheduleMeta.scheduleMinute;
                            let beginTime = new Date(this.formattingDate(scheduleMeta.beginTime)),
                                endTime = new Date(this.formattingDate(scheduleMeta.endTime));
                            this.form.timeRange = [beginTime, endTime];

                        }
                        break;
                    case "cron表达式" :
                        this.form.dispatchPlan = "cron表达式";
                        this.switchLayout(false, false, true, false, false, '');
                        if (scheduleMeta.scheudleInfo !== undefined) {
                            this.form.cronInput = scheduleMeta.scheduleMinute;
                        }
                        break;
                    default:
                        break;
                }
            },
            switchLayout(timing, once, cron, interval, dayWMShow, planTitle) { //时间 ， 一次性 ， cron表达式 , 时间 , 日期
                this.typeTiming = timing;
                this.typeOnce = once;
                this.typeCron = cron;
                this.typeInterval = interval;
                this.dayWMShow = dayWMShow;
                this.planLeftTitle = planTitle;
            },
            selectDWMChange() {
                switch (this.execTime) {
                    case "每日":
                        this.weekShow = false;
                        this.monthDayShow = false;
                        this.form.carryTime = this.everyDayHour + '/' + this.optEveryDayMin;
                        break;
                    case "每周" :
                        this.weekShow = true;
                        this.monthDayShow = false;
                        break;
                    case "每月" :
                        this.weekShow = false;
                        this.monthDayShow = true;
                        break;
                    default :
                        break;
                }
            },
            ergodicData(start, end, data) {
                for (let i = start; i <= end; i++) {
                    let d = {label: i};
                    data.push(d);
                }
            },
            cronInputFocus() {
                this.$emit('cornPanelShow', {
                    value: this.form.cronInput,
                    result: this.cornResult
                });
            },
            setCornValue(data) {
                this.form.cronInput = data.value;
                this.cornResult = data.result;
            },
            /**
             * 启动方案
             */
            filterNode: treeMethods.methods.filterNode,
            selectTreeChange(data) {
                this.form.transMetaId = data.id;
                this.form.selectTreeValue = data.id;
            },
            async initSubTransForm() {
                const vm = this, {modelingServices, modelingMock, settings} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                let dirType = vm.$route.name === 'processModeling' ? 'TRANS_DIR_MF' : vm.$route.name === 'dataMining' ? 'DIG_DIR_MF' : '';
                await services.getTreeAllNode(dirType, settings).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        result.forEach(item => {
                            item.children = item.children.filter(child => child.children.length);
                        });
                        vm.selectTreeOption = result;
                    }
                });

                await vm.$axios.get("/dataMining/getSubTrans/?transId=" + vm.rowData.transId).then(res => {
                    if (res.data.status === 1) {
                        vm.$message.error(res.data.msg);
                    } else {
                        let d = res.data.data;
                        d.forEach(item => {
                            vm.form.startPrograms.push({
                                subtransId:item.subtrans_id === undefined ? "" : item.subtrans_id,
                            subtransDelayTime: item.subtrans_delay_time === undefined ? "" : item.subtrans_delay_time,
                                subtransIsenforce:item.subtrans_isenforce === 1
                            });
                        });
                    }
                }).catch(err => {
                    vm.$message.error("服务器发生异常，请联系管理员")
                })
                vm.$emit("closeLoading");
            },

       /*     clearSubtrans() {
                this.isShowProgramme = false;
                this.form.selectTreeValue = "";
                this.form.delayExecute = "";
                this.form.isAlways = false;
            },*/
            add() {
                this.form.startPrograms.push({
                    subtransId: "",
                    subtransDelayTime: "",
                    subtransIsenforce: false,

                });
            },
            deleteData(rowData, index) {
                this.$confirm('此操作将永久删除该方案, 是否继续?', '删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.form.startPrograms.splice(index, 1);
                    this.$axios.get("/dataMining/deletaSubTrans/?transId=" + this.rowData.transId+"&subTransId="+rowData.subtransId).then(res => {
                        if (res.data.status === 1) {
                            vm.$message.error(res.data.msg);
                        } else {

                        }
                    }).catch(err => {
                        vm.$message.error("服务器发生异常，请联系管理员")
                    })
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });

            }


        },
        created() {
            this.ergodicData(0, 23, this.optEveryDayHour);
            this.ergodicData(0, 59, this.optEveryDayMin);
            this.ergodicData(1, 31, this.optEveryMonthDay);
            this.ergodicData(0, 31, this.optIntervalDay);
            this.ergodicData(0, 12, this.optIntervalMonth);
            this.initSubTransForm();
            this.initSelectValue();

            // this.initScheduleValue();
        }
    }
</script>

<style scoped>

    .ce-normal_item {
        line-height: 32px;
        text-align: right;
    }

    .execTime {
        width: 70px;
        float: left;
        margin-right: 5px;
    }

    .check_week {
        float: left;
        margin-left: 5px;
    }

    .time_select {
        width: 60px;
        float: left;
        margin-left: 10px;
        margin-right: 5px;
    }

    .lable_time {
        float: left;
    }

    .textarea {
        width: 472px;
    }

    .planGroup {
        width: 472px;
    }
    .is-require::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }
</style>
