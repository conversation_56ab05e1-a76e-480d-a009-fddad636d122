<template>
    <common-dialog custom-class="publishDia" :width="width" :title="title" :visible.sync="visible" @closed="close"
        v-loading="settings.loading">
        <div>
            <div class="steps">
                <div :class="['steps__item', active === 1 && 'is-active']">
                    <span class="dg-iconp icon-f-circle-check iconCheck" v-show="active > 1"></span>
                    <span class="steps__number" v-show="active === 1">1</span>
                    <span class="steps__text">填写基础信息</span>
                </div>
                <div :class="['steps__item', active === 2 && 'is-active']">
                    <span class="dg-iconp icon-f-circle-check iconCheck" v-show="active > 2"></span>
                    <span class="steps__number" v-show="active <= 2">2</span>
                    <span class="steps__text">填写业务信息</span>
                </div>
                <div v-if="showApply" :class="['steps__item', active === 3 && 'is-active']">
                    <span class="steps__number">3</span>
                    <span class="steps__text">填写申请信息</span>
                </div>
            </div>
            <div v-if="visible">
                <!--弹窗内容页-->
                <basic-info ref="basicInfo" v-show="active === 1" :openType="openType"
                    :infoList="infoList"></basic-info>
                <business-info ref="businessInfo" v-show="active === 2" @queryLabel="getLableList" :openType="openType"
                    :labelList="labelList" :infoList="infoList" :typeDataList="typeDataList"></business-info>
                <template v-if="showApply">
                    <apply-for-model ref="apply" :show-btn="false" :userInfo="userInfo" applyType="0"
                        :model-data-info="modelDataInfo" v-show="active === 3" />
                </template>
            </div>

        </div>
        <div slot="footer">
            <el-button @click="close" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button @click="last" type="primary" size="mini" v-if="showPrev">{{ btnLastTxt }}</el-button>
            <el-button @click="next" type="primary" size="mini" v-if="showNext">{{ btnNextTxt }}</el-button>
            <el-button @click="publish()" type="primary" size="mini" v-if="showPublish">{{ publishTxt }}</el-button>
            <el-button @click="submitAndPublish" type="primary" size="mini" v-if="showSubmit">{{ submitPublishTxt
                }}</el-button>
        </div>
    </common-dialog>
</template>

<script>
import { commonMixins } from "@/api/commonMethods/common-mixins";
import { servicesMixins } from "../service-mixins/service-mixins";
import basicInfo from "../management/components/basicInfo";
import businessInfo from "../management/components/businessInfo";
import ApplyForModel from "../dialog/applyForModel.vue"
import { mapGetters } from "vuex";

export default {
    name: "publish",
    mixins: [commonMixins, servicesMixins],
    props: {
        type: String,
        showApply: Boolean
    },
    computed: {
        ...mapGetters(["userInfo"]),
        // 显示上一步
        showPrev() {
            return this.active > 1;
        },
        // 显示下一步
        showNext() {
            return this.active < (this.showApply ? 3 : 2);
        },
        // 显示发布
        showPublish() {
            return this.active === 2 && !this.showApply;
        },
        // 显示提交申请 并 发布
        showSubmit() {
            return this.active === 3 && this.showApply;
        }
    },
    components: {
        basicInfo,
        businessInfo,
        ApplyForModel
    },
    data() {
        return {
            btnCancelTxt: '取消',
            btnNextTxt: '下一步',
            btnLastTxt: '上一步',
            publishTxt: '提交发布',
            submitPublishTxt: '提交申请并发布',
            width: '800px',
            title: '发布模型',
            active: 1,
            visible: false,
            infoList: {},
            labelList: [], //标签
            typeDataList: {},// 模型分类
            openType: 'add',
            modelDataInfo: {},
        }
    },
    methods: {
        //上一步
        last() {
            const { active } = this;
            if (active > 1) this.active--;
        },
        //下一步
        next() {
            const { active } = this;
            if (active === 1 && this.$refs.basicInfo.validate()) {
                this.active = 2;
            } else if (active === 2 && this.$refs.businessInfo.validate()) {
                this.active = 3;
            }
        },
        // 提交申请并发布
        submitAndPublish() {
            const vm = this;
            this.publish(() => {
                return vm.$refs.apply.saveFn();
            })
        },
        /**
         * //发布
         * @param callback 回调 提交申请 请求
         * @returns {Promise<void>}
         */
        async publish(callback) {
            const vm = this, { showApply, manageServices, manageMock, settings } = this;
            let validateBasic = vm.$refs.basicInfo.validate();
            let validateBusiness = vm.$refs.businessInfo.validate();
            let validateApply = showApply ? vm.$refs.apply.validate() : true;

            if (validateBasic && validateBusiness && validateApply) {
                settings.loading = true;
                let services = vm.getServices(manageServices, manageMock);
                let basicInfo = vm.$refs.basicInfo.form;
                let businessInfo = vm.$refs.businessInfo.form;

                let data = {
                    applicationTypeCode: businessInfo.applyType,
                    introduction: basicInfo.desc,
                    labels: [],
                    logo: vm.$refs.basicInfo.files,
                    objectTypeCode: businessInfo.objectType,
                    pics: vm.$refs.businessInfo.files,
                    policeTypeCode: businessInfo.policeType,
                    transId: vm.infoList.transId,
                    type: vm.type,
                    version: basicInfo.version,
                    caseTypeCode: businessInfo.caseTypeCode,
                    areaTypeCode: businessInfo.areaTypeCode,
                    controlTypeCode: businessInfo.controlTypeCode,
                    hyflSjTypeCode: businessInfo.mxflSjType,
                    yylxSjTypeCode: businessInfo.yylxSjType,
                }

                await new Promise((resolve) => {
                    vm.addModelLabel(businessInfo.label, services).then( info => {
                        resolve(info)
                    })
                }).then(item => {
                    data.labels = item;
                    if (vm.openType === 'edit') {
                        data.id = vm.infoList.id;
                        services.updateMarkModel(data, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.close();
                                vm.$emit('refresh');
                                vm.$message.success('修改成功！')
                            }
                        })
                    } else {

                        (showApply ?
                            services.publishModel(data) :
                            services.publishModel(data, settings)
                        ).then(async res => {
                            if (res.data.status === 0) {
                                if (callback) {
                                    await callback();
                                }
                                vm.close();
                                vm.$emit('refresh');
                                // vm.$message.success(`发布成功！`)
                            } else settings.loading = false;
                        }).catch(() => {
                            settings.loading = false;
                        })
                    }
                })
            } else {
                vm.$message.warning("存在必填项尚未填写！")
            }
        },
        //新增label
        async addModelLabel(list, services) {
            let reg = /^[0-9a-fA-F]{32}$/;
            let info = [];
            for (let i = 0; i < list.length; i++) {
                if (typeof list[i] === 'object' && reg.test(list[i].value)) {
                    info.push({ labelName: list[i].label, labelId: list[i].value });
                } else {
                    let labelName = { labelName: list[i] };
                    await services.addModelLabel(labelName).then(res => {
                        if (res.data.status === 0) {
                            info.push({ labelName: list[i], labelId: res.data.data.id });
                        }
                    })
                }
                if (info.length === list.length) {
                    return info
                }
            }
        },
        show(type, row) {
            this.openType = type;
            if (type === 'add') {
                this.title = "发布模型";
                this.infoList = {
                    id: row.id,
                    name: row.name,
                    transId: row.transId,
                    memo: row.memo,
                };
                this.getModelDataInfo(row);
            } else {
                this.infoList = {
                    id: row.id,
                    name: row.transName,
                    transId: row.transId,
                    introduction: row.introduction,
                    version: row.version,
                    logo: row.logo ? [{ value: row.logo }] : [],
                    objectType: row.objectTypeCode,
                    applyType: row.applicationTypeCode,
                    policeType: row.policeTypeCode,
                    caseTypeCode: row.caseTypeCode,
                    areaTypeCode: row.areaTypeCode,
                    controlTypeCode: row.controlTypeCode,
                    label: row.labels ? row.labels : [],
                    upload: row.pics ? row.pics.map(n => {
                        return { name: n.pictureName, value: n.picture }
                    }) : [],
                    mxflSjType: row.hyflSjTypeCode,
                    yylxSjType: row.yylxSjTypeCode,
                };
                this.title = "编辑模型";
            }
            this.getLableList();
            this.getTypeList();
            this.visible = true;
        },
        // 申请信息
        getModelDataInfo(row) {
            const { userInfo } = this;
            if (!this.showApply) return;
            this.modelDataInfo.type = this.type;
            this.modelDataInfo.transId = row.transId;
            this.modelDataInfo.transName = row.name;
            this.modelDataInfo.transUserId = userInfo.id;
            this.modelDataInfo.publishUser = {
                userName: userInfo.objName,
            };
        },
        close() {
            this.active = 1;
            this.visible = false;
        },
        //获取标签
        getLableList(name) {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                labelName: name
            }
            services.queryModelLabelList(data).then(res => {
                settings.loading = false;
                if (res.data.status === 0) {
                    vm.labelList = res.data.data.map(n => {
                        return { label: n.name, value: n.id }
                    });
                }
            })
        },
        //获取模型信息
        getTypeList() {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            vm.typeDataList = {
                objectType: [],
                applyType: [],
                policeType: [],
                caseTypeCode: [],
                areaTypeCode: [],
                controlTypeCode: [],
                mxflSjType:[],
                yylxSjType:[]
            }
            services.initPublishModel().then(res => {
                if (res.data.status === 0) {
                    const result = res.data.data;
                    vm.typeDataList.objectType = (result.objTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    });
                    vm.typeDataList.applyType = (result.appTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })
                    vm.typeDataList.policeType = (result.polTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })
                    vm.typeDataList.caseTypeCode = (result.caseTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })
                    vm.typeDataList.areaTypeCode = (result.areaTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })
                    vm.typeDataList.controlTypeCode = (result.controlTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })
                    vm.typeDataList.mxflSjType = (result.hyflSjTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })
                    vm.typeDataList.yylxSjType = (result.yylxSjTypeCodeList || []).map(n => {
                        return { label: n.name, value: n.code }
                    })

                }
            })
        },
    },
}
</script>

<style scoped lang="less">
.publishDia {
    .iconCheck {
        font-size: 32px;
        height: 32px;
        line-height: 32px;
        color: #1890ff;
        margin-right: 14px;
    }

    .steps {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        &__item {
            display: flex;
            align-items: center;

            &.is-active {
                .steps__number {
                    color: #fff;
                    background-color: #1890ff;
                    border-color: #1890ff;
                    font-weight: bold;
                }

                .steps__text {
                    color: rgba(0, 0, 0, 0.85);
                    font-weight: bold;
                }
            }

            &:not(:last-child) {
                &::after {
                    content: "";
                    display: inline-block;
                    width: 77px;
                    height: 1px;
                    background-color: rgba(0, 0, 0, 0.15);
                    margin: 0 16px;
                }
            }
        }

        &__number {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.15);
            margin-right: 14px;
        }

        &__text {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }
    }

}
</style>
