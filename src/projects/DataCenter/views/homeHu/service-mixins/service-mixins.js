export const servicesMixins = {
    data(){
        const {isMock} = this;
        let portalServices = require("@/projects/DataCenter/services/portal-services/portal-services") ,
            portalMock = isMock ? require("@/projects/DataCenter/mock/portal-mock/portal-mock"):{},
            rolesServices = require("@/projects/DataCenter/services/system-manage-services/roles-manage-services"),
            rolesMock = isMock ? require("@/projects/DataCenter/mock/system-manage-mock/roles-manage-mock"):{};
        return {
            rolesServices,
            rolesMock,
            portalServices ,
            portalMock ,
        }
    }
}
