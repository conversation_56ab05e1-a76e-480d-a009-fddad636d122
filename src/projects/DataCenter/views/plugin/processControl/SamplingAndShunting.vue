<template>
    <div class="samplingAndShuntingClass ce-plug_cont" v-loading="loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont" v-show="activeName === 'setting_base'">
            <div class="ce-plug_attr">
                <div>
                    <el-form :rules="rules">
                        <el-form-item label="输入字段：" label-width="100px" prop="inputRule">
                            <el-select size="mini" filterable v-model="inputField" placeholder="请选择">
                                <el-option v-for="item in inputFieldOption"
                                           :label="item.label"
                                           :value="item.value"
                                           :key="item.value"
                                >

                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item size="mini" label="默认输出：" label-width="100px" prop="defaultRule">
                            <el-select v-model="defaultOutput" filterable placeholder="请选择">
                                <el-option v-for="item in defaultOutputOption"
                                           :label="item.label"
                                           :value="item.value"
                                           :key="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <div style="padding-bottom: 30px">
                    <el-button style="margin-top: 5px"
                               type="primary"
                               size="mini"
                               @click="add"
                               class="btn_new_model"
                               plain
                    >添加
                    </el-button>
                </div>
                <div>
                    <el-form size="mini" class="ce-form__m0">
                        <el-form-item v-for="(item,i) in formData" :key="i">
                            <el-table
                                    border
                                    :show-header="status"
                                    :data="item.tableData"
                                    size="mini"
                            >
                                <el-table-column prop="num" align="center" width="50px">{{i+1}}</el-table-column>
                                <el-table-column align="center">
                                    <template slot-scope="scope">
                                        <el-row :gutter="5" >
                                            <el-col class="ce-normal_item is-require" :span="9">
                                                <span>匹配操作:</span>
                                            </el-col>
                                            <el-col :span="15">
                                                <el-select v-model="item.matchingOper" size="mini">
                                                    <el-option v-for=" optItem in operations"
                                                               :key="optItem.value"
                                                               :label="optItem.label"
                                                               :value="optItem.value"
                                                    >

                                                    </el-option>
                                                </el-select>
                                            </el-col>
                                        </el-row>
                                        <el-row :gutter="5">
                                            <el-col class="ce-normal_item is-require" :span="9">
                                                <span>匹配内容:</span>
                                            </el-col>
                                            <el-col :span="15">
                                                <el-input v-model="item.content" size="mini"></el-input>
                                            </el-col>
                                        </el-row>
                                        <el-row :gutter="5">
                                            <el-col class="ce-normal_item is-require" :span="9">
                                                <span>匹配内容类型:</span>
                                            </el-col>
                                            <el-col :span="15">
                                                <el-select v-model="item.contentType" size="mini"
                                                           style="margin-top: 3px">
                                                    <el-option v-for="optItem in item.contentTypeOpt"
                                                               :key="optItem.value"
                                                               :value="optItem.value"
                                                               :label="optItem.label"
                                                    ></el-option>
                                                </el-select>
                                            </el-col>
                                        </el-row>
                                        <el-row :gutter="5">
                                            <el-col class="ce-normal_item " :span="9">
                                                <span>是否忽略大小写:</span>
                                            </el-col>
                                            <el-col :span="15">
                                                <el-checkbox v-model="item.isNeglect" size="mini"></el-checkbox>
                                            </el-col>
                                        </el-row>
                                        <el-row :gutter="5" >
                                            <el-col class="ce-normal_item is-require" :span="9">
                                                <span>目标对象:</span>
                                            </el-col>
                                            <el-col :span="15">
                                                <el-select v-model="item.inputStep" size="mini">
                                                    <el-option v-for="optItem in followSteps"
                                                               :key="optItem.value"
                                                               :label="optItem.label"
                                                               :value="optItem.value"
                                                    >

                                                    </el-option>
                                                </el-select>
                                            </el-col>
                                        </el-row>
                                    </template>
                                </el-table-column>
                                <el-table-column width="50px" align="center">
                                    <template slot-scope="scope">
                                        <i
                                                class="icon ce_link model_status"
                                                v-for="(col , inx) in item.operateIcons"
                                                :key="inx"
                                                :title="col.tip"
                                                v-html="col.icon"
                                                @click="col.clickFn(scope.row,i)"
                                        ></i>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <div class="attr_btn">
                <el-button
                        v-for="(btn , inx) in buttons"
                        :key="inx"
                        size="mini"
                        :type="btn.type"
                        @click="btn.clickFn"
                >{{btn.name}}
                </el-button>
            </div>
        </div>

        <div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior :sourceData="sourceData" :isShow="isSeniorShow" ref="settingSenior"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import SettingSenior from '../component/SettingSenior'
    import PreView from "../component/PreView"
    export default {
        name: "SamplingAndShunting",
        components: {
            SettingSenior,
            PreView
        },
        props: {
            sourceData: Object,
            rowData : Object
        },
        data() {
            let _this = this;
            return {
                preview: {
                    isVisible: false,
                    list: [],
                    columns: [],
                    loading : true
                },
                loading : false ,
                isSeniorShow:{},
                activeName: 'setting_base',
                tab_panel: [
                    {
                        label: '基本设置',
                        name: 'setting_base'
                    }, {
                        label: '高级设置',
                        name: 'setting_senior'
                    }
                ],
                buttons: [
                    {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary'
                    }
                    , {
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                ],

                //输入字段
                inputField: "",
                defaultOutput: "",
                inputFieldOption: [],
                defaultOutputOption: [],
                operations: [],
                followSteps: [],

                //formData
                formData: [],
                status: false,

                //表单校验
                rules: {
                    inputRule: [
                        {required: true, message: '请选择输入字段', trigger: 'blur'}
                    ],
                    defaultRule: [
                        {required: true, message: '请选择输入字段', trigger: 'blur'}
                    ]
                },
            }
        },
        methods: {
            reSetLoading() {
                this.preview.loading = true;
                this.preview.columns = [];
                this.preview.list = [];
            },
            init() {
                let _this = this;
                this.stepId = this.sourceData.id;
                this.$axios.get("/plugin/sampleShunt/view?stepId=" + this.stepId)
                    .then(res => {
                        if (res.data.status === 1) {
                            _this.$message.error(res.data.msg);
                        } else {
                            let pluginMeta = res.data.data;
                            // 回显字段选项
                            this.inputFieldOption = [];
                            for (let i = 0; i < pluginMeta.columns.length; i++) {
                                let d = pluginMeta.columns[i];
                                // let name = d.columnName;
                                // if (name == null || typeof name == "undefined" || name.length == 0)
                                //     name = d.columnName;
                                this.inputFieldOption.push({
                                    "label": d.columnName,
                                    "value": d.columnName
                                })

                            }

                            // 回显操作选项
                            this.operations = [];
                            for (let k in pluginMeta.operations) {
                                if (!(pluginMeta.operations[k] === "身份证匹配"
                                        || pluginMeta.operations[k] === "采样"
                                        || pluginMeta.operations[k] === "公式"
                                        || pluginMeta.operations[k] === "正则匹配")) {
                                            this.operations.push({
                                                "label": pluginMeta.operations[k],
                                                "value": k
                                            })
                                }
                            }

                            // 回显接下来的步骤
                            this.defaultOutputOption = [];
                            this.followSteps = [];
                            for (let k in pluginMeta.followSteps) {
                                this.followSteps.push({
                                    "label": pluginMeta.followSteps[k],
                                    "value": k
                                });
                                this.defaultOutputOption.push({
                                    "label": pluginMeta.followSteps[k],
                                    "value": k
                                })
                            }
                            let outputFlag = false;
                            _this.defaultOutputOption.forEach(item =>{
                                if(item.value === pluginMeta.attrs.defaultTargetStep){
                                    outputFlag  = true;
                                }
                            }) ;
                            _this.inputField = pluginMeta.attrs.sourceColumn ? pluginMeta.attrs.sourceColumn :"" ;

                            _this.defaultOutput = outputFlag ? pluginMeta.attrs.defaultTargetStep : '';

                            // 回显采样分流配置
                            _this.formData = [];
                            if(!pluginMeta.exprs.length){
                                this.add();
                            }else {
                                for (let i = 0; i < pluginMeta.exprs.length; i++) {
                                    let f = pluginMeta.exprs[i];
                                    _this.formData.push({
                                        "matchingOper": f.operation,
                                        "content": f.operateTargetValue,
                                        "contentType": f.operateTargetType,
                                        "inputStep": outputFlag ? f.targetStep : '',
                                        "isNeglect": f.ignoreCase === "true",
                                        tableData: [
                                            {
                                                //num: i + 1,
                                            }
                                        ],
                                        contentTypeOpt: [
                                            {
                                                label: "整型",
                                                value: "Long"
                                            }, {
                                                label: "浮点",
                                                value: "Double"
                                            }, {
                                                label: "字符串",
                                                value: "String"
                                            }
                                        ],
                                        operateIcons: [
                                            {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteData}
                                        ],
                                    })
                                }
                            }

                        }
                    }).catch(err => {
                    console.log(err);
                    _this.$message.error("服务器发生异常，请联系管理员")
                })
            },
            saveFn() {
                let _this = this;
                let exprs = [];
                if(typeof _this.inputField == 'undefined' || _this.inputField === "") {
                    _this.$message.warning("输入字段不能为空！");
                    return ;
                }
                if(typeof _this.defaultOutput == 'undefined' || _this.defaultOutput === "") {
                    _this.$message.warning("默认输出必选！");
                    return ;
                }
                if(this.formData.length === 0){
                    _this.$message.warning("至少添加一个采样分流动作！");
                    return ;
                }
                for (let i = 0; i < this.formData.length; i++) {
                    let d = this.formData[i];
                    if(d.matchingOper === ''){
                        _this.$message.warning("请选择匹配操作");
                        return;
                    }
                    if( d.content === ''){
                        _this.$message.warning("请输入匹配内容");
                        return;
                    }
                    if( d.contentType === ''){
                        _this.$message.warning("请选择匹配内容类型");
                        return;
                    }
                    if( d.inputStep === '') {
                        _this.$message.warning("请选择目标对象");
                        return ;
                    }
                    exprs.push({
                        "operation": d.matchingOper,
                        "operateTargetValue": d.content,
                        "operateTargetType": d.contentType,
                        "targetStep": d.inputStep,
                        "ignoreCase": d.isNeglect
                    })
                }
                _this.loading = true;
                this.$axios.post("/plugin/sampleShunt/save?stepId=" + this.stepId
                    + "&sourceColumn=" + _this.inputField
                    + "&defaultTargetStep=" + _this.defaultOutput, exprs).then(res => {
                    if (res.data.status === 1) {
                        _this.$message.error(res.data.msg);
                    } else {
                        if(this.$refs.settingSenior.fieldTableData.length <= 0){
                            this.initSettingSenior();
                        }else{
                            this.saveColumn();
                        }
                        _this.$message.success("保存成功");
                        // this.$refs.settingSenior.initValue();
                    }
                    _this.loading = false;
                }).catch(err => {
                    _this.loading = false;
                    _this.$message.error("服务器发生异常，请联系管理员")
                })
            },
             initSettingSenior(){
                this.$refs.settingSenior.initValue();
            },
             saveColumn(){
                this.$refs.settingSenior.saveFieldTable();
            },
            previewFn() {
                this.$refs.preview.show(this.rowData , this.sourceData.id);
            },
            add() {
                let num = this.formData.length + 1;
                let _this = this;
                this.formData.push({
                    tableData: [
                        {
                            num: num,
                        }
                    ],
                    //匹配操作
                    matchingOper: "",

                    //匹配内容
                    content: "",

                    //匹配内容类型
                    contentType: "",
                    //匹配内容类型下拉框
                    contentTypeOpt: [
                        {
                            label: "整型",
                            value: "Long"
                        }, {
                            label: "浮点",
                            value: "Double"
                        }, {
                            label: "字符串",
                            value: "String"
                        }
                    ],

                    //是否忽略大小写
                    isNeglect: false,

                    //输出步骤
                    inputStep: "",
                    operateIcons: [
                        {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteData}
                    ],

                });
            },
            deleteData(rowData, index) {
                this.formData.splice(index, 1);
            }
        },
        created() {
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut:true,
                isCopyOrDispense:true,
                isSampleExpr:true,
                isThread:true,
                isException:true,
            };
            this.init();
        }
    }
</script>

<style scoped>
    .formClass {
        height: 5px;
        margin-top: 3px;
    }

    .btn_new_model {
        float: right;
        margin-bottom: 5px;
        margin-top: -27px;
        margin-left: 3px;
    }

</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>