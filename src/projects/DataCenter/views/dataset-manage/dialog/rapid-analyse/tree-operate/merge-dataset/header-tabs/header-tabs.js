import leftRightJoin from "../left-right-join/index.vue"
import upDownUnion from "../up-down-union/index.vue"
export default {
    name: "headerTabs" ,
    props : {
        datasetId :String
    },
    components : {leftRightJoin , upDownUnion},
    data() {
        return {
            mergeTabActive : "join" ,
            tabs : [
                {
                    label :"左右连接",
                    value : "join",
                    disabled : false
                },{
                    label : "上下合并",
                    value : "union" ,
                    disabled : false
                }
            ],
            isStep:false
        }
    },
    methods : {
        submit(){
            const vm = this;
            vm.$refs[vm.mergeTabActive].submit();
        },
        join(data){
            this.$emit("join" ,data );
        },
        merge(data) {
            this.$emit("merge",data);
        },
        setTabDisabled(dis){
            const vm = this;
            vm.tabs.forEach(tab => {
                tab.disabled = dis;
            });
        },
        showPage(tab , data){
            const vm = this;
            vm.setTabDisabled(!!data);
            if (tab === "union" && data) {
                vm.mergeTabActive = "union";
                vm.$refs[tab].stepBackDataShow(false, data);
                vm.isStep = true;
            }else if(tab === "join") {
                vm.$refs[tab].initEditData(data);
            }else {
                vm.mergeTabActive = "union";
            }
        }
    },
    created() {
        this.isStep = true
    }
}