<template>
    <div class="cornPanel">
        <el-tabs type="border-card" tab-position="top">
            <el-tab-pane
                    v-for="(tab , i) in tabsTitle"
                    :key="i"
                    :label="tab.label"
            >
                <CornTimeData :tabData="tab" :inx="i" @getTimeType="getTimeType" />
            </el-tab-pane>
        </el-tabs>
        <CornResult :tabData="tabsTitle" ref="result" />
    </div>

</template>

<script>
    import CornTimeData from './CornTimeData'
    import CornResult from './CornResult'
    export default {
        name: "CornPanel",
        components: {
            CornTimeData ,
            CornResult
        },
        data() {
            return {
                tabsTitle: [
                    {
                        label: '秒',
                        rangeMin : 0 ,
                        rangeMax : 59 ,
                        cycleStart : 0,
                        wildcard : '' ,
                        distance : true ,

                    }, {
                        label: '分钟',
                        rangeMin : 0 ,
                        rangeMax : 59 ,
                        cycleStart : 0,
                        wildcard : '' ,
                        distance : true ,
                    }, {
                        label: '小时',
                        rangeMin : 0 ,
                        rangeMax : 23 ,
                        cycleStart : 0,
                        wildcard : '' ,
                        distance : true ,
                    }, {
                        label: '日',
                        rangeMin : 1 ,
                        rangeMax : 31,
                        cycleStart : 1,
                        wildcard : 'L W',
                        noAssign : true ,
                        nearWorkDay : true ,
                        lastDay :true ,
                        distance : true ,
                    }, {
                        label: '月',
                        rangeMin : 1 ,
                        rangeMax : 12 ,
                        cycleStart : 1,
                        wildcard : '',
                        noAssign : true ,
                        distance : true ,
                    }, {
                        label: '周',
                        rangeMin : 1 ,
                        rangeMax : 7 ,
                        cycleStart : 1,
                        wildcard : 'L #',
                        noAssign : true,
                        distance : false ,
                        length : 4 ,
                        lastWeek : true ,

                    }, {
                        label: '年',
                        cycleStart : 2019 ,
                        cycleEnd : 3000 ,
                        wildcard : '',
                        noAssign : true ,

                    },
                ],

            }
        },
        methods : {
            getTimeType(i , data){
                this.$refs.result.dataChange( i , data );
            },
            getResult(){
                return this.$refs.result.cornResult();
            }
        }
    }
</script>

<style scoped>

</style>