import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

export default {
    name: "styleList",
    components: {
        chartStyle
    },
    mixins: [attrStyle],
    props: {
        node: Object,
        dir_dataSetId: String
    },
    data() {
        return {
            activeTabs: ["title", "tableStyle", "fieldSetting"],
            styleList: {
                title: {
                    label: "标题内容",
                    show: true,
                    settingForm: {
                        show: {
                            label: "显示标题",
                            type: "checkbox",
                        },
                        custom_style: {
                            label: "字体样式",
                            type: "sty_radio"
                        },
                        fontFamily: {
                            type: "select_font",
                            isLine: true
                        },
                        fontSize: {
                            type: "select_size",
                            isLine: true
                        },
                        fontWeight: {
                            type: "select_w",
                            isLine: true
                        },
                        fontStyle: {
                            type: "checkBtn",
                            isLine: true
                        },
                        color: {
                            type: "font_color",
                            isLine: true
                        },
                        align: {
                            type: "font_align",
                            isLine: true
                        },
                        text: {
                            type: "input_txt",
                            placeholder: "请輸入图表标题"
                        },
                    },
                    form: {
                        show: true,
                        custom_style: "default",
                        fontFamily: "sans-serif",
                        fontSize: "12px",
                        fontWeight: "normal",
                        fontStyle: [],
                        color: "#666",
                        text: "",
                        align: "center"
                    }
                },
                tableStyle: {
                    label: "展示配置",
                    show: true,
                    settingForm: {
                        showHeader: {
                            type: "checkbox",
                            label: "显示表头",
                        },
                        showIndex: {
                            type: "checkbox",
                            label: "显示序号",
                        },
                        fixTable: {
                            type: "checkbox_group_fix",
                            checkAllText: "冻结",
                            options: [
                                {label: "智能(表头)", value: "header"}
                            ],
                            allCheck: ['header', 'row'],
                            rowOptions: [],
                            lastTxt: "列"
                        },
                        showPage: {
                            label: "显示分页",
                            type: "checkbox_page",
                            lastTxt: "条/页",
                            options: [
                                {value: 10},
                                {value: 20},
                                {value: 30},
                                {value: 40},
                                {value: 50},
                                {value: 60},
                                {value: 70},
                                {value: 80},
                                {value: 90},
                                {value: 100},
                            ]
                        },
                        tableWidth: {
                            label: '表格列宽配置',
                            type: 'widthSetting',
                            options: [
                                {label: '固定宽度', value: 'width'},
                                {label: '最小宽度', value: 'min-width'},
                            ],
                            columns: []
                        }
                    },
                    form: {
                        showHeader: true,
                        showIndex: false,
                        fixTable: ["header"],
                        fixedRow: [],
                        isIndeterminate: true,
                        checkAll: false,
                        showPage: true,
                        pageSize: 10,
                        pageSizes: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
                        tableWidth: {},
                    }
                },
                fieldSetting: {
                    label: "功能配置",
                    show: true,
                    settingForm: {
                        mark_fields: {
                            label: "选择字段",
                            type: "form_select",
                            width: "120px",
                            pos: "right",
                            options: [],
                        },
                        useCondition: {
                            type: "mark_checkbox",
                            label: "启用条件格式",
                            changeFn: this.useConditionChange
                        },
                        marker: {
                            label: "标记选择",
                            type: "form_select",
                            width: "120px",
                            pos: "right",
                            options: [
                                {value: "background-color", label: "背景颜色"},
                                {value: "color", label: "文字颜色"},
                            ]
                        },
                        markCondition: {
                            type: "mark_condition",
                            options: [
                                {
                                    label: ">",
                                    value: ">"
                                },
                                {
                                    label: "≥",
                                    value: ">="
                                },
                                {
                                    label: "<",
                                    value: "<"
                                },
                                {
                                    label: "≤",
                                    value: "<="
                                },
                                {
                                    label: "=",
                                    value: "=="
                                },
                                {
                                    label: "≠",
                                    value: "!="
                                }
                            ]
                        }
                    },
                    form: {
                        mark_fields: "",
                        condition: [],
                        options: [],
                        useCondition: false,
                        marker: "background-color"
                    }

                },
                /*  fieldSetting : {
                      show :false,
                      form : {
                          options : []
                      }
                  }*/
            },
            emitStyle: {}
        }
    },
    watch: {
        rowOptions() {
            this.setRowOption();
            this.setMarkOptions();
            this.setTableWidthOption();
        }
    },
    computed: {
        rowOptions() {
            return this.styleList.fieldSetting.form.options;
        }
    },
    methods: {
        setTableWidthOption() {
            const {tableWidth} = this.styleList.tableStyle.form;
            for (let opt of this.rowOptions) {
                this.styleList.tableStyle.form.tableWidth[opt.code] = {
                    prop: tableWidth[opt.code]?.prop || "min-width",
                    value: tableWidth[opt.code]?.value || "",
                }
            }
            this.styleList.tableStyle.settingForm.tableWidth.columns = this.rowOptions;
        },
        setFields(data) {
            const vm = this;
            if (vm.styleList.fieldSetting) {
                vm.styleList.fieldSetting.form.options = vm.getAllFields(data);
            }

        },
        clearCondition() {
            const vm = this;
            if (vm.styleList.fieldSetting) {
                vm.styleList.fieldSetting.form.mark_fields = "";
                vm.styleList.fieldSetting.form.condition = [];
                vm.styleList.fieldSetting.form.useCondition = false;
            }
            if (vm.emitStyle.fieldSetting) {
                vm.emitStyle.fieldSetting.mark_fields = "";
                vm.emitStyle.fieldSetting.condition = [];
                vm.emitStyle.fieldSetting.useCondition = false;
            }
        },
        /**
         * 设置标记选项
         */
        setMarkOptions() {
            const vm = this;
            let options = vm.styleList.fieldSetting.form.options.map(item => {
                return {
                    label: item.label,
                    value: item.alias || item.code || item.label
                }
            });
            let selectedN = options.filter(no => no.value === this.emitStyle.fieldSetting.mark_fields);
            if (selectedN.length === 0) {
                vm.clearCondition();
            } else {
                vm.styleList.fieldSetting.form.mark_fields = vm.emitStyle.fieldSetting.mark_fields;
                vm.styleList.fieldSetting.form.condition = vm.emitStyle.fieldSetting.condition;
                vm.styleList.fieldSetting.form.useCondition = vm.emitStyle.fieldSetting.useCondition;
            }
            this.styleList.fieldSetting.settingForm.mark_fields.options = [...options];
        },
        getAllStyle() {
            const {emitStyle} = this;
            let styles = {};
            for (let k in emitStyle) {
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        setRowOption() {
            const vm = this;
            vm.styleList.tableStyle.settingForm.fixTable.rowOptions = vm.styleList.fieldSetting.form.options.map(item => {
                return {
                    label: item.label,
                    value: item.code
                }
            });
            let codes = vm.styleList.tableStyle.settingForm.fixTable.rowOptions.map(opt => opt.value);
            vm.styleList.tableStyle.form.fixedRow = vm.emitStyle.tableStyle ? vm.emitStyle.tableStyle.fixedRow.filter(row => codes.indexOf(row) > -1) : [];
        },

        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData);
                let {title, tableStyle, fieldSetting} = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                if(tableStyle) {
                    Object.assign(vm.styleList.tableStyle.form , tableStyle);
                }
                vm.emitStyle.tableStyle = {};
                Object.assign(vm.emitStyle.tableStyle, vm.styleList.tableStyle.form);
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form;
                vm.setRowOption();
                vm.setMarkOptions();
                vm.setTableWidthOption();
                vm.node.setData = false;
            })

        }
    },

}
