import Layout_new from "../views/Index"
export const basicRouter = [
    {
        path: "/datacenter/rolesmanage",
        component: Layout_new,
        redirect: "/datacenter/rolesmanage",
        name: "management",
        alwaysShow: true,
        hidden: true,
        meta: {title: "权限管理", icon: "iconfont icontubiao_l-qx", noCache: true , breadcrumb : false},
        children: [
            {
                path: "/datacenter/rolesmanage",
                component: () => import("../views/system-manage/roles-manage"),
                name: "roleManagement",
                hidden: true,
                meta: {title: "角色管理", icon: "icon-file", noCache: true}
            },
            {
                path: "/datacenter/usergroupmanage",
                component: () => import("../views/system-manage/user-group-manage"),
                name: "groupManagement",
                hidden: true,
                meta: {title: "用户组管理", icon: "icon-file", noCache: true}
            },
            {
                path: "/datacenter/usermanage",
                component: () => import("../views/system-manage/user-manage"),
                name: "userManagement",
                hidden: true,
                meta: {title: "用户管理", icon: "icon-file", noCache: true}
            }
        ]
    },
    {
        path: "/datacenter/datasource",
        component: Layout_new,
        hidden: true,
        name: "Warehouse",
        redirect: "/datacenter/datasource",
        meta: {title: "数据源管理", icon: "icon-floor", noCache: true, breadcrumb : false},
        children: [
            {
                path: "/datacenter/datasource",
                component: () => import('../views/dataSources/index.vue'),
                name: "dataWarehouse",
                hidden: false,
                meta: {title: "数据源管理", icon: "iconfont icontubiao_l-sjygl", noCache: true}
            }
        ],

    },
    {
        path: "/",
        component: Layout_new,
        hidden: true,
        name: "dataSet",
        redirect: "/datacenter/dataset",
        meta: {title: "数据集管理", icon: "icon-union", noCache: true , breadcrumb : false},
        children: [
            {
                path: "/datacenter/dataset",
                component: () => import('../views/dataset-manage/index.vue'),
                name: "dataSetOperation",
                hidden: false,
                meta: {title: "数据集管理", icon: "iconfont icontubiao_l-sjjgl", noCache: true}
            }, {
                path: "/datacenter/analysis",
                hidden: true,
                component: () => import("../views/dataset-manage/dialog/rapid-analyse/index.vue"),
                name: "analysis",
                meta: {title: "快速分析", layout: 'page', belong: '/datacenter/dataset'}
            }
        ]
    },
    {
        path: "/datacenter/modeling",
        component: Layout_new,
        hidden: true,
        name: "Modeling",
        redirect: "/datacenter/modeling",
        meta: {title: "数据建模", icon: "icon-zhmxfx", noCache: true , breadcrumb : false},
        children: [
            {
                path: '/datacenter/modeling',
                hidden: true,
                name: 'processModeling',
                meta: {title: "数据建模", icon: "iconfont icontubiao_l-sjjm", noCache: true},
                component: () => import( '../views/modeling/index.vue')
            }
        ],

    },
    {
        path: "/datacenter/visualizing",
        component: Layout_new,
        hidden: true,
        name: "board",
        redirect: "/datacenter/visualizing",
        meta: {title: "数据可视化", icon: "icon-kshfx", noCache: true , breadcrumb : false},
        children: [
            {
                path: "/datacenter/visualizing",
                component: () => import('../views/visualizing/index.vue'),
                name: "dashboard",
                hidden: false,
                meta: {title: "数据可视化", icon: "iconfont icontubiao_l-kshfx", noCache: true}
            },
        ],

    },
    {
        path: "/datacenter/homeHu",
        component: Layout_new,
        hidden: true,
        name: "Portal",
        redirect: "/datacenter/homeHu",
        meta: {title: "主题门户", icon: "icon-kshfx", noCache: true , breadcrumb : false},
        children: [
            {
                path: "/datacenter/homeHu",
                component: () => import('../views/homeHu/index.vue'),
                name: "themePortal",
                hidden: false,
                meta: {title: "主题门户", icon: "iconfont iconshujuzhongxinicon_l-distributed", noCache: true}
            }
        ],
    },
    {
        path: '*',
        hidden: true,
        component: () => import('../../../components/error/Error404.vue')
    }, {
        path: "/login",
        hidden: true,
        component: () => import('../views/login/Login.vue')
    },
    {
        path: "/datacenter/homeView",
        hidden: true,
        name: "homeView",
        component: () => import("../views/homeHu/edit-panel/body/portal-main/index.vue"),
    },
    {
        path: '/datacenter/visualview',
        hidden: true,
        name: 'visualView',
        meta: {right: "dashboard",},
        component: () => import('../views/visualizing/edit-panel/VisualizationEdit.vue')
    },
];
