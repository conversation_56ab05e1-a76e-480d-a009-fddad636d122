export const RequestDetail = {
    data() {
        return {
            batchQuery: false,
        }
    },
    methods: {
        //设置paramName
        setParamName(info, row) {
            info.paramName = info.memo || info.paramName || info.name || info.paramCode;
            info.paramcode = info.paramCode || info.code;
            if (!['map', 'list'].includes(info.type.toLowerCase())) {
                info.defaultValue = "";
                info.showInput = true;
            }
            info.defaultValue = info.type === 'Time' && info.example ? new Date(info.example) : info.example;
            if (row && row.paramObject && row.paramObject.useCaseApiParams && row.paramObject.useCaseApiParams.length && row.paramObject.apiType === 'postRequest') {
                info.defaultValue = row.paramObject.useCaseApiParams[0].params[0] && row.paramObject.useCaseApiParams[0].params[0][info.paramCode] || "";
            }
            if (info.children && info.children.length) {
                info.children.forEach(n => {
                    this.setParamName(n, row);
                })
            }
        },
        /**
         * 获取模型服务信息
         * showTime : boolean 比对订阅 订阅测试显示时间参数
         */
        async getModelServicesInfo(id, row) {
            const vm = this;
            let result = await vm.getRequestDetail(id);
            if (!result) return;
            let interName = result.interName || "postRequest";
            vm.setReqTableData(result, false, interName, row);
        },
        async getRequestDetail(id) {
            const vm = this;
            let services = vm.$services('servicesServices');
            return await services.queryServiceDetails(id, vm.loadParams).then(res => {
                if (res.data.code === 0) {
                    return res.data.data;
                }
            })
        },
        /**
         * 设置请求参数表格数据
         * @param result
         * @param showTime
         */
        setReqTableData(result, showTime, interName, row) {
            let requestTableData = []
            const vm = this;
            row.paramObject = {
                apiType: interName,
                servicePublicationId: row.id,
                useCaseApiParams: [],
            }
            let paramList = result.paramList || [];
            let arrayInfo = paramList.sort((a, b) => {
                return b.paramCode.localeCompare(a.paramCode)
            });
            let {batchQuery} = result;
            if ((result.serviceType === '数据查询' || (result.serviceType === '信息核查' && result.singleOrManyTable !== '2')) && batchQuery === '0') {
                arrayInfo.forEach(li => {
                    vm.setParamName(li, row)
                });
                vm.batchQuery = true;
                let obj = {};
                arrayInfo.forEach(h => {
                    if (h.defaultValue) {
                        obj[h.paramCode] = h.defaultValue;
                    }
                })
                let item = {
                    paramName: '批量查询参数',
                    paramCode: '批量查询参数',
                    childrenCopy: arrayInfo,
                    setDataList: [obj],
                    batchQuery: true
                };
                if (row.paramObject.useCaseApiParams && row.paramObject.apiType === 'postRequest') {
                    let defList = row.paramObject.useCaseApiParams;
                    item.setDataList = defList.length && defList[0].params || [{}];
                }
                requestTableData.push(item);
            } else {
                arrayInfo.forEach(n => {
                    vm.setParamName(n, row);
                    if (result.serviceType === '比对订阅' && !showTime) {
                        if (n.paramCode !== 'startTime' && n.paramCode !== 'endTime' && n.paramCode !== 'requestId') {
                            requestTableData.push(n)
                        }
                    } else {
                        n.paramCode !== 'requestId' ? requestTableData.push(n) : '';
                    }
                })
            }
            let operatorDataSet = requestTableData.find(n => n.paramCode === "operatorDataSet");
            if(result.serviceType === '数据查询' || (result.serviceType === '信息核查' && result.singleOrManyTable !== '2') || result.serviceType === 'AI算法'){
                if(requestTableData.find(n=>n.isMust === 't' && !n.defaultValue)){
                    row.paramObject = null;
                }
            }
            else if(operatorDataSet && operatorDataSet.children) {
                operatorDataSet.children.forEach(n => {
                    if (n.type === 'List') {
                        n.childrenCopy = n.children ? n.children : [];
                        if(n.childrenCopy.find(n=>n.isMust === 't' && !n.defaultValue)){
                            row.paramObject = null;
                        }
                        n.children = [];
                        n.setDataList = [];
                        let obj = {};
                        n.childrenCopy.forEach(h => {
                            if (h.defaultValue) {
                                obj[h.paramCode] = h.defaultValue;
                            }
                        })
                        n.setDataList.push(obj);
                    }
                })
            }
            if(row.paramObject){
                vm.setParamObject(requestTableData, result, row.paramObject, interName)
            }
        },
        setParamObject(requestTableData, result, row, interName){
            const vm = this;
            if (interName === 'getValue') {
                let requestId = requestTableData[0].defaultValue;
                this.setGetParams(requestId, row);
            }
            if (['模型分析', '数据碰撞', '比对订阅', '数据查询', 'AI算法', '信息核查'].includes(result.serviceType)) {
                vm.setTreeParams(requestTableData, row);

                if (['AI算法'].includes(result.serviceType)) {
                    vm.setOtherParams(requestTableData, row);
                } else if (['信息核查', '数据查询'].includes(result.serviceType)) {
                    if (vm.batchQuery && (result.serviceType === '数据查询' || (result.serviceType === '信息核查' && result.singleOrManyTable !== '2'))) {
                        vm.setBatchQueryParams(requestTableData, row);
                    } else if (result.serviceType !== '信息核查' || result.singleOrManyTable !== '2') {
                        vm.setOtherParams(requestTableData, row);
                    }
                }
            }
        },
        setTreeParams(data, row, isClear = true) {
            const vm = this;
            if (isClear) row.useCaseApiParams = [];
            data.forEach(item => {
                if (item.paramCode === 'params') {
                    let object = {}, objectList = [];
                    item.children.forEach(e => {
                        object[e.paramCode] = e.defaultValue;
                        objectList.push(object);
                    })
                    let itemObject = {
                        "parentParamCode": 'params',
                        "params": objectList
                    };
                    row.useCaseApiParams.push(itemObject);
                } else {
                    if (item.children && item.children.length > 0) vm.setTreeParams(item.children, row, false);
                    else if (['startTime', 'endTime'].includes(item.paramCode)) {
                        row.useCaseApiParams.push({
                            "parentParamCode": item.paramcode,
                            "params": item.defaultValue
                        });
                    } else {
                        row.useCaseApiParams.push({
                            "parentParamCode": item.paramcode,
                            "params": item.setDataList
                        });
                    }
                }
            })
        },
        //设置批量查询参数
        setBatchQueryParams(requestData, row) {
            let paramObject = {};
            row.useCaseApiParams = [];
            paramObject.parentParamCode = "";
            paramObject.params = requestData[0].setDataList;
            row.useCaseApiParams.push(paramObject);
        },
        //获取getValue 参数
        setGetParams(requestId, row){
            let paramObject = {
                parentParamCode:"",
                params : [{requestId}]
            };
            row.useCaseApiParams = [paramObject];
        },
        //其他默认的参数
        setOtherParams(requestData, row) {
            let paramObject = {};
            let itemObject = {};
            row.useCaseApiParams = [];
            requestData.forEach(item => {
                itemObject[item.paramCode] = item.defaultValue;
            })
            paramObject.parentParamCode = "";
            paramObject.params = [itemObject];
            row.useCaseApiParams.push(paramObject);
        },
    }
}