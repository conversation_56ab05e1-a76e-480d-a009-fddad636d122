<template>
    <div class="ce-list" v-loading="settings.loading">
        <div class="ce-list_filter">
            <el-radio-group v-model="activeN" @change="tabChange">
                <el-radio-button v-for="(item, key) in tabData" :key="key" :label="item.value" :value="item.value">{{
        item.label }}</el-radio-button>
            </el-radio-group>
            <div class="search-bar">
                <span class="search-bar_title">启用状态:</span>
                <dg-select v-model="enabledState" :data="enabledStateData" @change="changePage(1)"
                    class="wid20"></dg-select>
                <span class="search-bar_title">上架状态:</span>
                <dg-select v-model="selectType" :data="selectData" @change="changePage(1)" class='wid20'> </dg-select>
                <el-input class="ce-list_input" size="mini" placeholder="请输入模型名称查询" v-model.trim="filterTxt"
                    v-input-limit:trim @keyup.enter.native="changePage(1)">
                    <i class="el-icon-search el-input__icon poi" slot="suffix" @click="changePage(1)">
                    </i>
                </el-input>
            </div>
        </div>
        <div class="ce-list_table">
            <common-table v-show="tableData.length" :data="tableData" :columns="tableColumns" :border="false"
                :paging-type="isMock ? 'client' : 'server'" :pagination-props="paginationProps"
                :pagination-total="totalCount" noneImg :max-height="tableBodyH"
                @change-current="isMock ? () => { } : changePage($event)" @change-size="changeSize">
                <template slot="name" slot-scope="{row , $index}">
                    <div class="source poi" @click="detailsLook(row)">{{ row.name }}</div>
                </template>
                <template slot="state" slot-scope="{row}">
                    <span class="status" :class="{ 'upStatus': row.state === '0' }">{{ row.state === "1" ? '已下架' :
        '已上架' }}</span>
                </template>
                <template slot="enabledstate" slot-scope="{row}">
                    <i :class="['enabled-icon', row.enabledstate === '0' ? 'el-icon-success' : 'el-icon-error']" :title="row.enabledstate === '0' ? '启用' :
        '停用'"></i>
                </template>
                <template slot="operate" slot-scope="{row}">
                    <span class="r-c-action" v-for="(opt, inx) in operator" :key="inx" v-if="opt.condition(row)">
                        <dg-button type="text" :title="opt.label" @click="opt.clickFn(row)">{{ opt.label }}</dg-button>
                    </span>
                </template>
            </common-table>
            <no-data v-if="tableData.length === 0" :src="emptyImg" :title="emptyTxt" top="5%">
            </no-data>
        </div>
        <publish ref="publish" :type="activeN" @refresh="changePage(1)"></publish>
        <!--仪表盘详情页面-->
        <VisualizationEdit ref="visual_panel" v-if="showEditPanel" @closePanel="closeEditPanel" :isCase="true" />
        <!--主题门户详情页面-->
        <edit-panel v-if="showHomehuEditPanel" ref="protal_panel" @closePanel="closeHomehuEditPanel"
            :isCase="true"></edit-panel>
    </div>
</template>

<script>
import { common } from "@/api/commonMethods/common";
import { commonMixins } from "@/api/commonMethods/common-mixins"
import emptyImg from "@/assets/images/common/empty-img.png";
import NoData from "@/components/no-data";
import { coustTableH } from "@/api/commonMethods/count-table-h";
import publish from "../../dialog/publish"
import { servicesMixins } from "../../service-mixins/service-mixins";
import VisualizationEdit from '@/projects/DataCenter/views/visualizing/edit-panel/VisualizationEdit';
import EditPanel from "@/projects/DataCenter/views/homeHu/edit-panel"

export default {
    name: "publishedList",
    mixins: [common, coustTableH, servicesMixins, commonMixins],
    components: {
        NoData,
        publish,
        VisualizationEdit,
        EditPanel
    },
    props: {
        tabData: Array
    },
    data() {
        return {
            activeN : this.tabData.length ? this.tabData[0].value : "",
            filterTxt: "",
            shareData: [],
            selectData: [
                { label: '全部', value: '' },
                { label: '已上架', value: "0" },
                { label: '已下架', value: "1" }
            ],
            selectType: '',
            enabledState: '',
            enabledStateData: [
                { label: '全部', value: '' },
                { label: '启用', value: "0" },
                { label: '停用', value: "1" }
            ],
            pageSizes: [],
            totalCount: 0,
            emptyImg,
            showEditPanel: false,
            emptyTxt: "暂未发布相关模型",
            tableData: [],
            //操作栏
            operator: [
                {
                    label: '查看',
                    clickFn: this.onLook,
                    condition: (row) => true,
                }, {
                    label: '编辑',
                    clickFn: this.onEdit,
                    condition: (row) => true,
                },
                {
                    label: '下架',
                    clickFn: this.onUpDown,
                    condition: (row) => row.state === "0",
                }, {
                    label: '上架',
                    condition: (row) => row.state === "1",
                    clickFn: this.onUpDown,
                },
                {
                    label: '停用',
                    clickFn: this.onEnabled,
                    condition: (row) => row.enabledstate === "0",
                }, {
                    label: '启用',
                    condition: (row) => row.enabledstate === "1",
                    clickFn: this.onEnabled,
                },
                {
                    label: '删除',
                    condition: (row) => row.state === "1",
                    clickFn: this.onDelete,
                }
            ],
            tableColumns: [
                {
                    prop: "name",
                    label: "模型名称",
                    minWidth: 180,
                    align: "left"
                },
                /*{
                    prop: "objtypename",
                    label: "对象类型",
                    align: "center"
                }, {
                    prop: "apptypename",
                    label: "应用类型",
                    align: "center"
                }, {
                    prop: "poltypename",
                    label: "警种类型",
                    align: "center"
                }, */{
                    prop: "label",
                    label: "标签",
                    align: "center"
                }, {
                    prop: "userName",
                    label: "创建人",
                    minWidth: 150,
                    align: "center"
                },
                {
                    prop: "enabledstate",
                    label: "启用状态",
                    align: "center"
                }, {
                    prop: "state",
                    label: "上架状态",
                    align: "center",
                    minWidth: 90,
                }, {
                    prop: "publishtime",
                    label: "发布日期",
                    width: "180",
                    align: "center"
                }, {
                    prop: "operate",
                    label: "操作",
                    width: "250",
                    align: "center"
                }
            ],
            showHomehuEditPanel: false,
        }
    },
    methods: {
        changePage(index) {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            settings.loading = true;
            vm.tableData = [];
            let data = {
                type: vm.activeN,
                pageNum: index,
                pageSize: vm.paginationProps.pageSize,
                condition: vm.filterTxt,
                state: vm.selectType,
                enabledState: vm.enabledState,
            }
            vm.paginationProps.currentPage = index;
            services.publishedModelList(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.tableData = res.data.data.dataList;
                    vm.totalCount = res.data.data.totalCount;
                }
            })
        },
        //查看ai模型
        lookAI(row) {
            const vm = this;
            row.transId = row.transid;
            row.modelName = row.name;
            let layer = this.$dgLayer({
                title: "查看模型",
                content: require("@/projects/DataCenter/views/AIModeling/dialog/new-model/index.vue"),
                maxmin: false,
                props: {
                    isLook: true,
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].show(row);
        },
        //查看模型
        lookModel(row) {
            const vm = this;
            let data = {
                transId: row.transid,
                modelName: row.name,
                caseId: row.transid,
                isNew: false,
            }
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: "-1",
                    isCase: true,
                    isLook: true,
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab(data, false);
        },
        //查看仪表盘
        lookDash(row) {
            this.showEditPanel = true;
            this.$nextTick(() => {
                this.$refs.visual_panel.getRouterData(row.transid, '', '', '');
            })
        },
        closeEditPanel(groupId, filterTxt) {
            this.showEditPanel = false;
        },
        //查看
        onLook(row) {
            if (this.activeN === 'TRANS') {
                this.lookModel(row)
            }
            else if (this.activeN === 'DASHBOARDS') {
                this.lookDash(row)
            }
            else if (this.activeN === 'PORTAL') {
                this.openHomehuPanel(row);
            }
            else {
                this.lookAI(row);
            }
        },
        closeHomehuEditPanel() {
            this.showHomehuEditPanel = false;
        },
        //查看主题门户
        openHomehuPanel(row) {
            this.showHomehuEditPanel = true;
            row.id = row.transid;
            this.$nextTick(() => {
                this.$refs.protal_panel.panelInit("", row.transid, row, "");
            })
        },
        //查看模型市场详情
        detailsLook(row) {
            let routeData = this.$router.resolve({ path: '/market/modelMarket', query: { id: row.id } });
            window.open(routeData.href, '_blank');
        },
        //编辑
        onEdit(row) {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            services.initUpdateMarkModel({ id: row.id }).then(res => {
                if (res.data.status === 0) {
                    this.$refs.publish.show('edit', res.data.data);
                }
            })
        },
        //上下架
        onUpDown(row) {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            let title = '', tip = '';
            if (row.state === '0') {
                title = '下架模型';
                tip = '模型下架后还在已发布模型列表中，您可以重新上架。确认要下架当前模型吗？';
            }
            else {
                title = '上架模型';
                tip = '模型上架后在市场中可直接查看和使用。确认要上架当前模型吗？';
            }
            vm.confirm(title, tip, () => {
                let data = {
                    id: row.id,
                    state: row.state === '0' ? '1' : '0',
                }
                settings.loading = true;
                services.updateModelState(data, settings).then(res => {
                    if (res.data.status === 0) {
                        let info = row.state === '0' ? '下架成功！' : '上架成功！'
                        vm.$message.success(info);
                        this.changePage(1);
                    }
                })
            })
        },
        // 启、停用
        onEnabled(row) {
            const vm = this, { $services, settings } = this;

            let title = '', tip = '', info = '';
            if (row.enabledstate === '1') {
                title = '启用模型';
                tip = '确认要启用当前模型吗？';
                info = '启用成功！';
            }
            else {
                title = '停用模型';
                tip = '确认要停用当前模型吗？';
                info = '停用成功！';
            }
            vm.confirm(title, tip, () => {
                let data = {
                    id: row.id,
                    enabledState: row.enabledstate === '0' ? '1' : '0',
                }
                settings.loading = true;
                $services('market').updateEnabledState(data, settings).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success(info);
                        vm.changePage(1);
                    }
                })
            },)
        },
        //删除
        onDelete(row) {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            vm.confirm('删除模型', `模型删除后从已发布模型列表中移除，如需上架，请重新发布。确认要删除当前模型吗？`, () => {
                settings.loading = true;
                services.deleteModel({ id: row.id }, settings).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功！");
                        this.changePage(1);
                    }
                })
            })
        },

        tabChange(val, tab) {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        searchTable() {
            this.changePage(1);
        },
    },
    mounted() {
        this.triggerEvent('resize', 300);
    },
    created() {
        this.changePage(1);
    }
}
</script>

<style scoped lang="less">
.wid20 {
    width: 120px;
}

.search-bar {
    &_title {
        padding: 0 8px 0 16px;
    }
}

.red {
    color: red;
}

.alertClass {
    width: 700px;
}

.ce-list {
    height: 100%;

    .source {
        color: #0088FF;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .r-c-action:last-child::after {
        width: 0;
    }

    &_filter {
        height: 32px;
        text-align: right;
        padding: 10px 0;
        display: flex;
        justify-content: space-between;
    }

    &_input {
        width: 270px;
        margin-left: 10px;
    }

    &_table {
        overflow: hidden;
        height: calc(100% - 52px);

        .ce-table-h__auto {
            overflow-y: hidden;
        }


    }

    &_com {
        width: 320px;
        margin-left: 10px;
    }

    &_preSel {
        width: 100px;
    }
}

.status {
    padding: 0px 10px;
    border: 1px solid #dcdcdc;
    border-radius: 8px;
    color: rgba(0, 0, 0, 0.45);
}

.upStatus {
    border: 1px solid rgba(82, 196, 26, 0.60);
    color: #52C41A;
}

.enabled-icon {
    font-size: 16px;

    &.el-icon-success {
        color: #52C41A;
    }

    &.el-icon-error {
        color: #F5222D;
    }
}
</style>
