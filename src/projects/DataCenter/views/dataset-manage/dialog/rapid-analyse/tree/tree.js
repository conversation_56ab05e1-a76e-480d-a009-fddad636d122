import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import {common} from "@/api/commonMethods/common"
import {mapGetters} from "vuex"
import MenuItem from "../menu-item/index"
import treeEdit from "../tree-operate/edit/index.vue"
import addColumn from "../tree-operate/add-column/index.vue"
import DropdownOpt from "@/components/common/dropdown-opt/DropdownOpt"
import * as $ from "jquery"

export default {
    name: "tree",
    components: {
        MenuItem,
        treeEdit,
        addColumn,
        DropdownOpt
    },
    mixins: [treeMethods, commonMixins, servicesMixins, common],
    props: {
        datasetId: String
    },
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    data() {
        const vm = this,
            edit_menu = [{
                name: "编辑",
                code: 'edit',
                clickFn: vm.editColumn
            }, {
                name: "新增列",
                code: "add",
                clickFn: vm.addColumn
            }],
            type_menu = [{
                name: "文本",
                code: 'txt',
                clickFn: vm.setType
            }, {
                name: "数字",
                code: 'number',
                clickFn: vm.setType
            }],
            date_format = [
                {
                    name: "日期",
                    code: "date_format",
                    clickFn: vm.setDateFormat,
                }
            ],
            delete_menu = [
                {
                    name: "删除",
                    code: 'delete',
                    clickFn: vm.deleteColumn
                }
            ],
            dimension_m = [{
                name: "转为度量",
                code: 'dimension',
                to: "MEASURE",
                toP: '_m',
                clickFn: vm.setDataType
            }],
            measure_m = [{
                name: "转为维度",
                code: 'measure',
                to: "DIMENSION",
                toP: '_d',
                clickFn: vm.setDataType
            }],
            num_format = [
                {
                    name: '数字格式化',
                    code: "num_format",
                    children: [
                        {
                            name: "#,##0",
                            code: '#,##0',
                            clickFn: vm.setNumFormat
                        }, {
                            name: "#,##0.0",
                            code: '#,##0.0',
                            clickFn: vm.setNumFormat
                        }, {
                            name: "#,##0.00",
                            code: '#,##0.00',
                            clickFn: vm.setNumFormat
                        }, {
                            name: "#,##0.000",
                            code: '#,##0.000',
                            clickFn: vm.setNumFormat
                        }, {
                            name: "#,##0.0000",
                            code: '#,##0.0000',
                            clickFn: vm.setNumFormat
                        }, {
                            name: "无",
                            code: 'none',
                            clickFn: vm.setNumFormat
                        },
                    ]
                }
            ];
        return {
            activeMenu: "",
            all_columns: [],
            addBtnTxt: ['添加维度计算字段', '添加度量计算字段'],
            treeMenu: [],
            menu1: [
                ...edit_menu,
                {
                    name: "维度类型转换",
                    code: "d_to",
                    children: [
                        // ...date_format,
                        ...type_menu,
                    ]
                },
                ...delete_menu,
                ...dimension_m

            ],
            menu2: [
                ...edit_menu,
                {
                    name: "维度类型转换",
                    code: "d_to",
                    children: [
                        // ...date_format,
                        ...type_menu,
                    ]
                },
                ...delete_menu,
            ],
            menu3: [
                ...edit_menu,
                ...num_format,
                {
                    name: "度量类型转换",
                    code: "m_to",
                    children: [
                        ...type_menu,
                    ]
                },
                ...delete_menu,
                ...measure_m
            ],
            reNew: true,
            activeColor: "#1890ff",
            menuVisible: false,
            activeTab: ""
        }
    },
    methods: {
        addColumnStep(data) {
            this.$refs.addColumn.showHistory({step: true, ...data}); //indexType
        },
        editColumnStep(data) {
            const {result, canEdit, stepId} = data;
            let field = {
                id: result.columnId,
                code: result.columnCode,
                label: result.showColumnName,
                memo: result.comment,
                indexType: result.indexType,
                columnAlias: result.columnAlias,
                canEdit,
                step: true,
                stepId
            };
            this.$refs.treeEdit.show(field);
        },
        getTree(id = "") {
            const vm = this, {datasetServices, datasetMock, settings} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            services.getLogicDataColumn(id, vm.datasetId, settings).then(res => {
                if (res.data.status === 0) {
                    vm.initTree(res.data.data);
                }
            })
        },
        hideMenu() {
            this.$refs.other.click();
        },
        d_to_fn() {//维度类型转换
            const vm = this, {selectedData} = this;
            if (selectedData.format === 'TEXT') {
                vm.activeTab = 'txt';
            } else if (selectedData.format === 'NUMBER') {
                vm.activeTab = 'number';
            } else {
                vm.activeTab = "date_format";
            }
        },
        num_to() {
            const vm = this, {selectedData} = this;
            if (selectedData.numberFormat) {
                vm.activeTab = selectedData.numberFormat;
            } else {
                vm.activeTab = "none";
            }

        },
        date_to() {
            const vm = this, {selectedData} = this;
            if (selectedData.format) {
                vm.activeTab = selectedData.format;
            } else {
                vm.activeTab = "yyyyMMdd hh:mi:ss";
            }

        },
        openFn(data) {
            switch (data.code) {
                case "d_to" :
                case "m_to" :
                    this.d_to_fn();
                    break;
                case "num_format":
                    this.num_to();
                    break;
                case "date_format":
                    this.date_to();
                    break;
                default:
                    break;
            }
        },
        changeVal(data) {
            if (!data.clickFn) return;
            data.clickFn(data);
        },
        moveDataTo(to, pId, data) {
            const vm = this;
            let col = to === '_m' ? "1" : "0";
            vm.data[col].children.forEach(item => {
                if (item.id === pId + to) {

                    item.children.push(data);
                    setTimeout(() => {
                        vm.reNew = true;
                    }, 300);

                }
            })

        },
        setDataType(data) {
            const vm = this, {datasetServices, datasetMock, selectedData, selectedNode, datasetId, userInfo} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            vm.confirm(data.name, `确认将\"${selectedData.label}\"${data.name}`, () => {
                let req_data = {
                    columnId: selectedData.id,
                    dataSetId: datasetId,
                    indexType: data.to,
                    userId: userInfo.id
                };
                services.changeIndexType(req_data).then(res => {
                    if (res.data.status === 0) {
                        vm.reNew = false;
                        selectedData.indexType = data.to;
                        let pData = selectedNode.parent.data, pId = pData.id.slice(0, -2);
                        pData.children = pData.children.filter(no => no.id !== selectedData.id);
                        vm.moveDataTo(data.toP, pId, selectedData);
                        vm.$message.success(`成功${data.name}`);
                        // vm.$emit("preview");
                    }
                })

            })
        },
        deleteColumn(data) {
            const vm = this, {datasetServices, datasetMock, selectedData, selectedNode, datasetId, userInfo} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            vm.confirm(data.name, `确认删除\"${selectedData.label}\"`, () => {
                let req_data = {
                    columnId: selectedData.id,
                    dataSetId: datasetId,
                    name: selectedData.name,
                    userId: userInfo.id,
                    columnCode: selectedData.code,
                    dataTypeId: selectedData.dataTypeId,
                    format: selectedData.format,
                    id: selectedData.id,
                    indexType: selectedData.indexType,
                    memo: selectedData.memo,
                    numberFormat: selectedData.numberFormat,
                    belongParentId: selectedData.belongParentId,
                    columnExpression: selectedData.funcExp,
                    columnAlias: selectedData.columnAlias
                };

                services.deleteColumn(req_data).then(res => {
                    if (res.data.status === 0) {
                        let pData = selectedNode.parent.data;
                        pData.children = pData.children.filter(no => no.id !== selectedData.id);
                        vm.$message.success("删除成功");
                        // vm.$emit("preview");
                    }
                })

            })
        },
        editColumn(data) {
            this.$refs.treeEdit.show(this.selectedData);
        },
        addColumn(data, type, field) {
            const {selectedData} = this;
            let indexType = type ? type.toUpperCase() : selectedData.indexType;
            let nodeData = field ? field : selectedData;
            this.$refs.addColumn.show(indexType, nodeData);
        },
        setNumFormat(data) {
            const vm = this;
            if (data.code === vm.activeTab) {
                this.hideMenu();
                return;
            }
            let format = data.code;
            vm.confirm("数字格式化", `确认设置为\"${data.name}\"数字格式`, () => {
                vm.set_digital_format(format);
            })
        },
        set_digital_format(format) {
            const vm = this, {datasetServices, datasetMock, selectedData, datasetId, userInfo} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            let req_data = {
                columnId: selectedData.id,
                dataSetId: datasetId,
                numberFormat: format,
                userId: userInfo.id,
                metaNumberFormat: selectedData.numberFormat
            };
            services.numberFormat(req_data).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("设置成功");
                    selectedData.numberFormat = format;
                    // vm.$emit("preview");
                }
            })
        },
        setDateFormat(data) {
            const vm = this;
            if (data.code === vm.activeTab) {
                this.hideMenu();
                return;
            }
            let format = "yyyyMMdd";
            vm.confirm("类型转换", `确认转为日期(${data.name})`, () => {
                vm.req_data_type(format);
            })
        },
        setType(data) {
            const vm = this;
            if (data.code === vm.activeTab) {
                this.hideMenu();
                return;
            }
            let format = data.code === "txt" ? "TEXT" : "NUMBER";
            vm.confirm("类型转换", `确认转为${data.name}`, () => {
                vm.req_data_type(format);
            })

        },
        req_data_type(format) {
            const vm = this, {datasetServices, datasetMock, selectedData, datasetId, userInfo} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            let req_data = {
                columnId: selectedData.id,
                dataSetId: datasetId,
                format: format,
                userId: userInfo.id,
                metaFormat: selectedData.format,
                indexType: selectedData.indexType
            };
            services.format(req_data).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("类型转换成功");
                    selectedData.format = format;
                    // vm.$emit("preview");
                }
            })
        },
        initTree(data, addDatasetId) {
            if (!data || data.length === 0) return;
            const vm = this;
            vm.settings.loading = true;
            let dimension = [], //维度
                measure = []; // 度量
            let p_datasetId = "";
            data.columns.forEach((item, i) => {
                item.dimension ? item.dimension.forEach(di => {
                    di.label = di.columnAlias || di.name || di.code;
                    di.belongDataSetId = item.id;
                }) : true;
                item.measure ? item.measure.forEach(me => {
                    me.label = me.columnAlias || me.name || me.code;
                    me.belongDataSetId = item.id;
                }) : true;
                if (i === 0 && item.code === item.code) {
                    p_datasetId = item.id;
                }
                let dimension_item = {
                        id: item.id + '_d',
                        label: item.name || item.code,
                        children: item.dimension || []
                    },
                    measure_item = {
                        id: item.id + '_m',
                        label: item.name || item.code,
                        children: item.measure || []
                    };
                dimension.push(dimension_item);
                measure.push(measure_item);
            });
            vm.data = [
                {
                    id: "1",
                    label: "维度",
                    code: 'dimension',
                    belongDataSetId: p_datasetId,
                    children: dimension
                },
                {
                    id: "2",
                    label: "度量",
                    code: 'measure',
                    belongDataSetId: p_datasetId,
                    children: measure
                }
            ];
            vm.getAllColumns(vm.data);
            setTimeout(() => {
                vm.settings.loading = false
            }, 500);
            vm.$emit("preview", vm.all_columns, addDatasetId);
        },
        mergeDataset(data) {
            const vm = this;
            vm.settings.loading = true;
            let dimension_item = {
                    id: data.id + '_d',
                    label: data.name || data.code,
                    children: data.dimension
                },
                measure_item = {
                    id: data.id + '_m',
                    label: data.name || data.code,
                    children: data.measure
                };
            vm.data[0].children.push(dimension_item);
            vm.data[1].children.push(measure_item);
            setTimeout(() => {
                vm.settings.loading = false;
            }, 500);

        },
        appendColumns(data, isAdd) {
            const vm = this;
            vm.settings.loading = true;
            data.forEach(item => {
                let d_ids = [], m_ids = [], d_c = [], m_c = [];
                item.dimension.forEach(item => d_ids.push(item.code));
                item.measure.forEach(item => m_ids.push(item.code));
                vm.data[0].children.forEach(di => {
                    if (di.id === item.id + '_d') {
                        d_c = di.children ? di.children.filter(d => d_ids.indexOf(d.code) === -1) : [];
                        di.children = d_c.concat(item.dimension);
                    }
                });
                vm.data[1].children.forEach(me => {
                    if (me.id === item.id + "_m") {
                        m_c = me.children ? me.children.filter(m => m_ids.indexOf(m.code) === -1) : [];
                        me.children = m_c.concat(item.measure);
                    }
                });
            });
            setTimeout(() => {
                vm.settings.loading = false;
                let msg = isAdd === true ? "新增字段成功" : "同步成功";
                if (msg) {
                    vm.$message.success(msg);
                    vm.getTree();
                    // vm.$emit("preview");
                }
            }, 500);
        },
        rightMenu(event, node, object) {
            if(this.selectedData.id && this.selectedData.id !== object.id) this.hideMenu();
            this.showMenu(node, object);
            if (object.indexType === 'DIMENSION') {
                if (object.format === "TEXT" || object.format === "NUMBER") {
                    this.treeMenu = this.menu1;
                } else {
                    this.treeMenu = this.menu2;
                }
            } else if (object.indexType === 'MEASURE') {
                this.treeMenu = this.menu3;
            }

        },
        getAllColumns(data) {
            const vm = this;
            data.forEach(item => {
                if (item.indexType === 'DIMENSION' || item.indexType === 'MEASURE') {
                    vm.all_columns.push(item);
                }
                if (item.children && item.children.length) {
                    vm.getAllColumns(item.children);
                }
            })
        },
        setColumns() {
            const vm = this;
            let columns = [];
            vm.all_columns.forEach(item => {
                columns.push({
                    name: item.name,
                    code: item.code,
                    id: item.id,
                    format: item.format, //字段类型 TEXT , NUMBER , 时间 “""
                    indexType: item.indexType,
                    memo: item.memo,
                    dataTypeId: item.dataTypeId
                })
            });
            return columns;
        },
        setColumn(data) {
            const vm = this, {selectedData, dataSetId} = this;
            vm.$message.success("修改成功");
            selectedData.name = selectedData.label = data.columnAlias || data.name || data.code;
            selectedData.memo = data.memo;
            selectedData.columnAlias = data.columnAlias;
            // vm.$emit("editSuccess");
        }
    }
}
