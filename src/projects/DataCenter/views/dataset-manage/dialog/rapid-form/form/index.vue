<template>
    <el-form
            ref="form"
            :model="analyse_data"
            label-position="right"
            label-width="80px"
            class="demo-form-inline"
            :rules="rules"
            v-loading="settings.loading"
    >
        <el-form-item  v-for="(item , key) in analyse_form"
                       :key="key"
                       :label="item.label + ':'"
                       :prop="key"
        >
            <el-input v-if="item.type === 'input'"
                      v-model.trim="analyse_data[key]"
                      v-input-filter="analyse_data[key]"
                      :maxlength="item.maxlength"
                      :placeholder="item.placeholder"></el-input>
            <ce-select-drop
                    v-if="item.type === 'select_drop'"
                    class="expect-select"
                    ref="pos_tree"
                    :props="defaultProps"
                    filterable
                    :tree-props="treeBind"
                    :filterNodeMethod="filterNode"
                    v-model="analyse_data[key]"
                    :data="siteDatas"></ce-select-drop>

        </el-form-item>
    </el-form>
</template>

<script src="./form.js"></script>
<!--<style scoped lang="less" src="./form.less"></style>-->
