<template>
    <div class="columnPage new-column-pop">
        <div class="new-column-pop-left" v-loading="settings.loading">
            <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    label-width="80px"
                    class="demo-ruleForm"
                    :hide-required-asterisk="disabled"
                    @submit.native.prevent
            >
                <el-form-item label="名称" prop="name">
                    <el-input v-model="ruleForm.name" v-input-filter="ruleForm.name" maxlength="100" :disabled="disabled" :placeholder="name_placeholder"></el-input>
                </el-form-item>
                <el-form-item label="code" prop="columnCode" v-if="showCode">
                  <el-input v-model="ruleForm.columnCode" v-input-limit:trim maxlength="100" :placeholder="code_placeholder"></el-input>
                </el-form-item>
                <el-form-item label="表达式" prop="expression" >
                    <div :style="expStyle">
                        <el-autocomplete type="textarea" class="width100"
                                         :trigger-on-focus="false"
                                         :disabled="disabled"
                                         @input="expChange"
                                         resize="none"
                                         @select="selectNode"
                                         :fetch-suggestions="querySearchAsync"
                                         :placeholder="exp_placeholder" v-model="ruleForm.expression"></el-autocomplete>
                    </div>

                </el-form-item>
                <el-form-item label="数据类型" prop="dataType" required>
                    <el-radio-group v-model="ruleForm.dataType" :disabled="disabled">
                        <el-radio v-for="radio in typeOpts" :key="radio.value" v-if="radio.show(indexType)" :label="radio.value">{{radio.label}}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注信息" prop="markInfo">
                    <el-input type="textarea" v-model="ruleForm.markInfo" :placeholder="memo_placeholder" :disabled="disabled"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="new-column-pop-right">
            <div class="new-column-pop-right-header">
                <el-input
                        size="mini"
                        placeholder="请输入关键词搜索"
                        suffix-icon="el-icon-search"
                        v-model="searchVal"
                        @input="filterFun"
                ></el-input>
            </div>
            <div class="new-column-pop-right-cont demo-scroll-bar">
                <dg-scrollbar>
                    <div
                            class="right-cont-item dg-scroll-content"
                            v-for="(item , i) in columnTable"
                            :key="i"
                    >
                        <div class="tip">{{item.name}}</div>
                        <table class="new-column-table">
                            <tr>
                                <th>{{columnTips.use}} :</th>
                                <td>{{item.use}}</td>
                            </tr>
                           <!-- <tr>
                                <th>{{columnTips.explain}} :</th>
                                <td>{{item.explain}}</td>
                            </tr>-->
                            <tr>
                                <th>{{columnTips.example}} :</th>
                                <td>{{item.example}}</td>
                            </tr>
                        </table>
                    </div>
                </dg-scrollbar>
            </div>
        </div>
    </div>
</template>

<script src="./column-page.js"></script>

<style scoped lang="less" src="./column-page.less"></style>
