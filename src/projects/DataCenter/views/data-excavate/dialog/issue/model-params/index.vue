<template>
    <div class="modal-push modelParams" v-loading="settings.loading">
        <el-checkbox-group v-model="checkList">
            <el-table
                    ref="table"
                    :data="tableData"
                    class="width100"
                    row-key="id"
                    :show-header="false"
                    border
                    :span-method="mergeSpanMethod"
                    :indent="20"
                    :row-class-name="setRowClassName"
                    :cell-class-name="setColClassName"
                    :tree-props="treeProps"
                    :default-expand-all="true"
            >
                <el-table-column v-for="col in columns" :key="col.props" :prop="col.props">
                    <template slot-scope="{row}">
                        <el-checkbox
                                @change="checkChildren($event , row)"
                                v-if="col.props === columns[0].props"
                                :label="row.id"
                        >{{row[col.props]}}</el-checkbox>
                        <span v-else>{{row[col.props]}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-checkbox-group>
    </div>
</template>

<script src="./model-params.js"></script>
<style scoped lang="less" src="./model-params.less"></style>