<template>
    <div class="ce-list" v-loading="settings.loading">
        <div class="ce-list_top">
            <div class="ce-table__search ml10 mr10">
                <el-input
                        size="mini"
                        placeholder="请输入请求方名搜索"
                        v-model.trim="inputValueTable"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'inputValueTable')"
                        @keyup.enter.native="searchTableEvent"
                >
                    <i
                            class="el-icon-search el-input__icon poi"
                            slot="suffix"
                            @click="searchTableEvent">
                    </i>
                </el-input>
            </div>
            <el-popover popper-class="ce-popover"
                        placement="bottom-end"
                        trigger="click"
                        v-model="showSenior"
                        width="564"
            >
                <service-query ref="senior" @serviceQuery="serviceQuery"/>
                <el-button type="primary" @click="seniorInit" slot="reference">{{ showSenior ? hideM : queryM }} <em
                        :class="showSenior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></em></el-button>
            </el-popover>

        </div>
        <div class="ce-list_table">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    :max-height="tableBodyH"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                <template slot="operate" slot-scope="{row}">
                    <span v-for="(opt , inx) in operator" :key="inx"
                          v-if="opt.condition(row , rights)">
                        <dg-button type="text" v-html="opt.label" :title="opt.label"
                                   @click="opt.clickFn(row)"></dg-button>
                    </span>
                </template>
            </common-table>
        </div>
    </div>
</template>

<script>
    import {listMixins} from "@/api/commonMethods/list-mixins";
    import {coustTableH} from "@/api/commonMethods/count-table-h";
    import {common} from "@/api/commonMethods/common"
    import $right from "@/assets/data/right-data/right-data";
    import {mapGetters} from "vuex";
    import serviceQuery from "@/projects/DataCenter/views/system-manage/log-query/dialog/service-query"
    import {servicesMixins} from "@/projects/DataCenter/views/system-manage/log-query/services-mixins/services-mixins";
    import {commonMixins} from "@/api/commonMethods/common-mixins";
    import {createComponent} from "@/api/commonMethods/createComponent";
    import helpDocument from "@components/signed-components/helpDocument/helpDocument";
    export default {
        name: "serviceLog",
        mixins: [listMixins, coustTableH, common, commonMixins, servicesMixins],
        components: {serviceQuery},
        computed: {
            ...mapGetters(["userInfo", "userRight"]),
            rights() {
                let rights = [];
                if (this.userRight) {
                    this.userRight.forEach(r => {
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            },
        },
        data() {
            return {
                inputValueTable: "",
                type: "",
                typePlace: "请选择操作类型",
                queryM: "高级检索",
                hideM: "收起检索",
                tableData: [],
                tHeadData: [
                    {
                        prop: "numId",
                        label: "记录标识",
                        align: "left",
                        minWidth: "100"
                    }, {
                        prop: "requester",
                        label: "请求方名称",
                        align: "left",
                        minWidth: "120"
                    }, {
                        prop: "regId",
                        label: "应用系统标识",
                        align: "left",
                        minWidth: "80"
                    }, {
                        prop: "terminalId",
                        label: "终端标识",
                        align: "left",
                        minWidth: "80"
                    }, {
                        prop: "interfaceName",
                        label: "接口名称",
                        align: "center",
                        minWidth: "80"
                    }, {
                        prop: "startTime",
                        label: "接口服务开始时间",
                        align: "center",
                        minWidth: "120"
                    }, {
                        prop: "interfaceCondition",
                        label: "接口服务条件",
                        align: "left",
                        minWidth: "100"
                    },  {
                        prop: "stateName",
                        label: "接口服务结果",
                        align: "left",
                        minWidth: "80"
                    },{
                        prop: "operate",
                        label: "操作",
                        align: "center",
                        minWidth: "100"
                    },
                ],
                operator: [
                    {
                        label: '查看详情',
                        clickFn: this.detail,
                        condition: (row, right) => right.includes($right["logQueryViewDetails"]),
                    }
                ],
                showSenior: false,
                queryForm: {
                    requester: "",
                    interfaceName: "" ,
                    interfaceTime: "" ,
                    resultTime:'',
                    result:''
                },
                operateTypeOpt: [],//操作类型
            }
        },
        methods: {
            /**
             * 高级筛选初始化
             */
            seniorInit() {
                const vm = this, {showSenior, queryForm} = vm;
                if (showSenior) return;
                this.$refs.senior.init(queryForm);
            },
            serviceQuery({requester , interfaceName, interfaceTime, resultTime , result}) {
                this.queryForm = {
                    requester , interfaceName, interfaceTime, resultTime , result
                };
                this.inputValueTable = requester;
                this.showSenior = false;
                this.changePage();
            },
            /**
             * 详情
             * @param row
             */
            detail(row) {
                const vm = this;
                let rowData = {
                    ...row ,
                    numId: row.numId || "-",
                    regId: row.regId || "-",
                    interfaceName: row.interfaceName || "-",
                    requester: row.requester || "-",
                    userId: row.userId || "-",
                    organization: row.organization || "-",
                    organizationId: row.organizationId || "-",
                    userName: row.userName || "-",
                    startTime : row.startTime || "-",
                    endTime : row.endTime || "-",
                    terminalId : row.terminalId || "-",
                    stateName : row.stateName || "-",
                    resultCount : row.resultCount || "-",
                    errorCode : row.errorCode || "-",
                    interfaceCondition : row.interfaceCondition || "-",
                };
                vm.$dgLayer({
                    title: "日志详情",
                    content: require("../dialog/detail"),
                    maxmin: false,
                    move: false,
                    props: {
                        row: rowData,
                        type:'service'
                    },
                    area: ["800px", "450px"],
                    success: function (layero){
                        createComponent(helpDocument , {code : "logQueryViewDetails"} , layero.find(".layui-layer-title")[0])
                    }
                })
            },
            /**
             * 搜索
             * @param val
             */
            searchTableEvent() {
                this.queryForm = {
                    requester: "",
                    interfaceName: "" ,
                    interfaceTime: "" ,
                    resultTime:'',
                    result:''
                };
                this.queryForm.requester = this.inputValueTable;
                this.changePage();
            },
            setTimeFormat(pattern , replace ,val){
                let formatedDate = val.replace(pattern, replace);
                return new Date(formatedDate);
            },
            async changePage(inx = 1) {
                const vm = this, {logServices, queryForm, paginationProps ,settings} = vm;
                vm.paginationProps.currentPage = inx;
                let pageIndex = inx, {pageSize} = paginationProps;
                vm.tableData = [];
                let param = {
                    ...queryForm,
                    pageIndex,
                    pageSize
                }
                settings.loading = true;
                let {data} = await logServices.queryPublishedInterfaceExecLog(param ,settings);
                if (data.code !== 0) return;
                let result = data.data.data;
                vm.tableData = result.dataList.map(list => {
                    list.stateName = list.interfaceResult === '1' ? '成功' : '失败';
                    list.startTime = list.interfaceTime ? vm.setTimeFormat(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/ , '$1/$2/$3 $4:$5:$6' ,list.interfaceTime).Format('yyyy-MM-dd hh:mm:ss') :'';
                    list.endTime = list.resultTime ? vm.setTimeFormat(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/ , '$1/$2/$3 $4:$5:$6' ,list.resultTime).Format('yyyy-MM-dd hh:mm:ss') :'';
                    return list;
                });

                vm.total = result.totalCount;
            },
            changeSize(val) {
                this.paginationProps.pageSize = val;
                this.changePage();
            },
            /**
             * 获取模块
             * @return {Promise<void>}
             */
            async init() {
                const vm = this;
                vm.changePage();
            },

        },
        created() {
            this.init();

        }
    }
</script>

<style scoped lang="less">
    .ce-list {
        height: 100%;

        &_top {
            display: flex;
            justify-content: flex-end;
        }

        &_table {
            padding: 15px 0;
            height: calc(100% - 60px);
            display: flex;
            overflow: hidden;
            align-items: center;
        }

    }
</style>
