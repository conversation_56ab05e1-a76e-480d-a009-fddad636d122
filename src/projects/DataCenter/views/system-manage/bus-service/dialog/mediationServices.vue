<template>
    <ce-list-table
            ref="table"
            :filter-text.sync="filterText"
            :t-head-data="tHeadData"
            :table-data="tableData"
            @changePage="changePage"
            :operate-icons="operateIcons"
            :filter-placeholder="filterPlaceholder"
    >
    </ce-list-table>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {mapGetters} from "vuex";
    import $right from "@/assets/data/right-data/right-data"
    import {coustTableH} from "@/api/commonMethods/count-table-h";
    import {servicesMixins} from "../../service-mixins/service-mixins";
    export default {
        name: "mediationServices",
        mixins: [commonMixins, servicesMixins, coustTableH],
        data() {
            return {
                filterText: "",
                tHeadData: [
                    {
                        prop: 'ID',
                        label: "服务ID",
                        minWidth: 120
                    }, {
                        prop: 'address',
                        label: "服务名称",
                        minWidth: 120
                    }, {
                        prop: 'name',
                        label: "中介名称",
                        minWidth: 100,
                    },{
                        prop: 'version',
                        label: "中介版本",
                        minWidth: 100,
                    },{
                        prop: 'updateTime',
                        label: "更新时间",
                        minWidth: 120,
                    },{
                        prop: 'operate',
                        label: "操作",
                        width: 220,
                        align : "center",
                    },
                ],
                tableData: [],
                operateIcons: [
                    {
                        name: "编辑",
                        clickFn: this.edit,
                        show: () => true,
                        condition: (right) => true
                    }, {
                        name: "查看",
                        clickFn: this.look,
                        show: () => true,
                        condition: (right) => true
                    },{
                        name: "接口",
                        clickFn: this.port,
                        show: () => true,
                        condition: (right) => true
                    },{
                        name: "删除",
                        clickFn: this.delete,
                        show: () => true,
                        condition: (right) => true
                    },
                ],
                filterPlaceholder: "请输入服务名称",
            }
        },
        methods:{
            tableChange() {
                this.tableData = [{name:'11',}];
                this.$refs.table.changePage();
            },
            edit(row){
                this.$emit('optionInfo','edit',row);
            },
            look(row){
                this.$emit('optionInfo','look',row);
            },
            port(row){
                this.$emit('optionInfo','port',row);
            },
            delete(row){

            },
            changePage(inx, size) {
                // const vm = this, {mediationServices , mediationMock, settings} = this;
                // vm.tableData = [];
                // settings.loading = true;
                // let services = vm.getServices(mediationServices , mediationMock);
                // services.getMediationList({
                //     keyWord: vm.filterText,
                //     pageSize : size,
                //     pageIndex : inx,
                //     settings
                // }).then(res => {
                //     vm.tableData = res;
                //     vm.$refs.table.total = res.totalCount;
                //     vm.triggerEvent();
                // });
            },
        }
    }
</script>

<style scoped>

</style>