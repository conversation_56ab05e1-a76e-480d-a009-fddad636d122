import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import * as dataSourcesServices from "@/projects/DataCenter/views/dataSources/service-mixins/service-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataset-manage/service-mixins/service-mixins";
import {globalBus} from "@/api/globalBus";
import {mapGetters} from "vuex"
import * as $ from "jquery";
import $right from "@/assets/data/right-data/right-data"

export default {
    name: "tree" ,
    mixins : [treeMethods , dataSourcesServices.servicesMixins,commonMixins , servicesMixins],
    computed : {
        ...mapGetters(["userRight"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    props:{
        hasDataObj:<PERSON><PERSON><PERSON>,
        isLogic:<PERSON><PERSON><PERSON>
    },
    watch: {
        filterText(val) {
            this.$refs.dgtree.filter(val);
            this.$refs.tree.filter(val);
        }
    },
    data(){
        return {
            treeMenu : [] ,
            addBtnTxt : '新增子目录' ,
            menu: [
                {
                    name: '修改目录',
                    fn: this.editNode ,
                    show : () => true
                }, {
                    name: '删除目录',
                    fn: this.deleteNode ,
                    show : () => true
                },
                {
                    name: '添加数据集',
                    fn: this.addDataSet ,
                    show : () => true
                }
            ],
            btnTxt : "授权数据集" ,
            rightOfAdd : $right["dataSetOperationAccreditLogicDataObj"],
            activeName: 'custom_dataset',
            tab_panel: [
                {
                    label: '自定义',
                    name: 'custom_dataset'
                }
                , {
                    label: '源数据集',
                    name: 'source_dataset'
                }
            ],
            sourceDatasetTree:[],
            allDataObj:[],
            redata:[],
            expandedKeys : []
        }
    },
    methods : {
        addNode(plug) {
            globalBus.$emit('plugNode', plug);
        },
        dragend() {
            globalBus.$emit('dragend');
        },
        addChildren(event , node, object) {//新增子目录
            this.showMenu(node, object);
            this.prompt('新增子目录', '', this.addTreeNode , '目录名称' , 'child' );
        },
        editNode(value) {//编辑
            this.prompt('修改目录名', this.selectedData.label, this.editTree , '目录名称' , 'child' );
        },
        addTreeNode(value){
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let pId = vm.selectedData.id;
            services.addDataSetTreeNode(pId , value).then(res => {
                if(res.data.status === 0){
                    vm.successAddTip(value);
                    let newTreeNode = res.data.data;
                    vm.selectedData.children.unshift(newTreeNode);
                }
            })
        },
        editTree(value){
            const vm = this , {datasetServices , datasetMock ,selectedData } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let nodeId = selectedData.id ,oldName = selectedData.label;
            services.reNameDataSetTreeNode(nodeId , value , oldName).then(res => {
                if(res.data.status === 0){
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.label = value;
                }
            })

        },
        deleteNode(){
            const vm = this , {datasetServices , datasetMock ,selectedData } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let node = this.getTreeNode();
            let currentNode = vm.$refs.tree.getCurrentNode();
            let nodeId = selectedData.id , nodeN = selectedData.label;
            const index = node.children.findIndex(d => d.id === node.data.id);
            vm.confirm(`确认删除\"${nodeN}\"及同步删除\"${nodeN}\"下的数据集吗` , '删除' , ()=>{
                services.deleteDataSetTreeNode(nodeId).then(res => {
                    if(res.data.status === 0){
                        vm.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        node.children.splice(index,1);
                       /* vm.data[0].children.forEach(item => {
                            if(item.name === '我的'){
                                item.children = item.children.filter(child => child.id !== nodeId);
                            }
                        });*/
                        vm.initTree();
                    }
                })
            });

        },
        async initTree(){
            const vm = this , {listApi, listMock,datasetServices , datasetMock , settings } = this;
            let services = vm.getServices(datasetServices , datasetMock),listServices = vm.getServices(listApi, listMock);
            settings.loading = true;
            let has = false;
            if (this.hasDataObj){
                has = true;
            }
            let getSuccess = false;
           await services.queryDataSetTree(has).then(res => {
                if(res.data.status === 0){
                    getSuccess = true;
                    let result =  res.data.data;
                    let curData = result[0].children.filter(d => d.code === "DATASET_DIR_MY")[0] , curId = curData? curData.id : "";
                    vm.data = result;
                    vm.$nextTick(()=>{
                        let {treeNodeId , treeNode} = vm.$route.params;
                        if(treeNode && treeNode.treeType !== "sourceDataSetTree" && treeNodeId){
                            curId = treeNodeId;
                            vm.nodeClickByKey(treeNodeId);
                            vm.activeName = "custom_dataset";
                        } else if(!treeNode) {
                            vm.$emit("getTableData" , curData);
                        }
                        vm.$nextTick(()=>{
                            if(vm.$refs.tree){
                                vm.$refs.tree.setCurrentKey(curId);
                            }
                        });
                    });
                }

            });
           if(!getSuccess) return;
            //初始化源数据集的树
            listServices.getDataSourceTree(settings,vm.isLogic).then(res =>{
                if (res.data.status === 0){
                    vm.sourceDatasetTree = res.data.data;
                   // console.log(vm.sourceDatasetTree.label);
                    let {treeNodeId , treeNode} = vm.$route.params;
                    if(treeNode && treeNode.treeType === "sourceDataSetTree" && treeNodeId){
                        vm.activeName = "source_dataset";
                        vm.$nextTick(()=>{
                            vm.$refs.dgtree.setCurrentKey(treeNodeId);
                            let node = vm.$refs.dgtree.getNode(treeNodeId);
                            vm.dgNodeClick(node.data);
                            vm.expandedKeys = [treeNodeId];
                        })
                    }
                }
            });
        },
        nodeClickByKey(key){
            let node = this.$refs.tree.getNode(key);
            this.nodeClick(node.data);
        },
        addDataSet(){
            this.$emit("empowerShow",this.selectedData.id);
        },

        empowerShow(){
            this.$emit("empowerShow");
        },
        nodeClick(data){
            let curData = {}; const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.dgtree.setCurrentKey(null);
            });
            if(data.id){
                curData = data;
                vm.$emit('getTableData',curData);
            }else {
                vm.$nextTick(()=>{
                    if(vm.$refs.tree){
                        curData = this.$refs.tree.getNode(data);
                        vm.$refs.tree.setCurrentKey(data);
                        vm.$emit('getTableData',curData.data);
                    }
                });
            }
        },
        dgNodeClick(data){
            const vm = this;
            vm.redata=data;
            if(data.isParent === true)return;
            vm.$nextTick(()=>{
                vm.$refs.tree.setCurrentKey(null);
            });
            vm.$emit('getTableData',data);
        },
        refreshSourceDataset(){
            const vm = this , {listApi, listMock,datasetServices , datasetMock , settings } = this;
            let services = vm.getServices(datasetServices , datasetMock),listServices = vm.getServices(listApi, listMock);
            settings.loading = true;
            //初始化源数据集的树
            listServices.getDataSourceTree(settings).then(res =>{
                if (res.data.status === 0){
                    vm.sourceDatasetTree = res.data.data;
                    // console.log(vm.sourceDatasetTree.label);
                        if(vm.redata.length!== 0){
                        vm.$nextTick(()=>{
                            vm.$emit("getTableData" , vm.redata);
                        });
                    }

                }
            });
        }


    }
}
