<template>
    <tree-and-list :sizeLeft="15.8" draggable>

        <tree-card slot="left" :card-name="cardName" @filterData="filterTree"  :class="{'disabled':listLoad}">
            <i slot="add" class="el-icon-folder-add poi" @click="showAddDialog('')" v-show="tabActive == 'myData'" title="新建目录"></i>
            <div class="data-ready__tabs" slot="tabs">
                <span @click="changeTabs('myData')" :class="tabActive === 'myData' && 'is-active'">数据集</span>
                <span @click="changeTabs('theme')" :class="tabActive === 'theme' && 'is-active'" v-if="isShowHouse && rights.indexOf(warehouse) > -1">数据仓库</span>
            </div>
            <tree
                    slot="tree"
                    ref="dataTree"
                    :hasSave="false"
                    :active="tabActive"
                    show-tree-type
                    @addTreeDir="showAddDialog"
                    @setTreeData="setTreeData"
                    @nodeClick="treeDir"
                    @initTable="initTable"
                    @moveToDir="moveToDir"
                    @reName="addFirstNode"
                    @closeAddFirst="closeAddFirst"
            ></tree>
        </tree-card>

         <list ref="readyCenterTable"
               @editContact="editContact"
               :treeAllInfo="treeAllInfo"
               :active="tabActive"
               :renewListLoad="renewListLoad"
               @authShow="authShow"
               @batchData="batchData"
               @previewDialog="previewDialog"
               @syncDataset="syncDataset"
               @moveTo="moveTo"
               @closeMove="closeMove"
               @detailShow="detailShow"
               @saveOtherShow="saveOtherShow"
               @saveOtherClose="saveOtherClose"
               @createAPI="createAPI"
        >
        </list>
        <div slot="other">
            <!--新建树 目录-->
            <add-tree-dir ref="treeDir" @addTreeDir="addTreeDir" @moveTreeDir="moveTreeDir" @filterData="filterEditTree" >
                <tree slot="tree" ref="editTree" :active="tabActive"  slot-scope="scope" v-bind="scope" @nodeClick="nodeClick"/>
            </add-tree-dir>
            <!--移动到-->
            <move-tree ref="move" @moveUpdate="moveUpdate"/>

            <!--另存為-->
            <save-model ref="saveOther" show-type @save="saveOtherSure"/>
            <!--预览-->
            <preview ref="preview"/>

            <!--生成api-->
            <create-api ref="createAPI" @changePage="changePageAPI" :modelInfo="{serviceType:'4'}" :entrance="'dataSet'"/>

            <!--详情-->
            <data-details ref="dataDetails" :active="tabActive"/>
            <!--同步数据结构-->
            <!--<sync-logic ref="syncLogic"></sync-logic>-->
            <!--权限-->
            <AuthDialog ref="auth" @success="reNewTable" />
            <!--批量处理-->
            <BatchDialog ref="batch" :treeData="treeData" @success="reNewTable" />
            <!--新增/重命名目录-->
            <add-first-tree ref="addFirstTree" @addFirstTree="addFirstTree" @reName="reName"/>
            <!--SQL另存为-->
            <sql-save-model ref="saveModel" @sqlSetSuccess="reNewTable"></sql-save-model>

        </div>
    </tree-and-list>
</template>
<script src="./dataReady.js"></script>
<style scoped lang="less">
    .data-ready {
        height: 100%;

        &__tabs {
            display: flex;
            align-items: center;
            padding: 20px 20px 0 20px;

            span {
                font-size: .8rem;
                color: rgba(0, 0, 0, 0.44);
                margin-right: 20px;
                cursor: pointer;
                line-height: 1;

                &.is-active {
                    font-weight: bold;
                    color: #0088ff;
                }
            }
        }
    }
</style>
