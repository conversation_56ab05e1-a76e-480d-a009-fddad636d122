<template>
    <div class="searchTree">
        <el-input :placeholder="placeholder" v-model.trim="filterText" :size="size" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn" :style="{'height' : height}">
            <dg-tree
                    ref="tree"
                    :filter-node-method="filterNode"
                    v-on="$listeners"
                    v-bind="Object.assign({} , $attrs)"
            >
            </dg-tree>
        </div>
    </div>
</template>

<script>
    import dgTree from "ui-component-v4/lib/tree"
    export default {
        name: "SimpleSearchTree",
        components :{
            dgTree
        },
        props: {
            size: {
                type: String,
                default() {
                    return 'mini';
                }
            },
            height: {
                type: String,
                default: 'auto'
            },
            placeholder: {
                type: String,
                default: '名称过滤'
            }
        },
        watch:
            {
                filterText(val) {
                    this.$refs.tree.filter(val);
                }
            },
        data() {
            return {
                filterText: '',

            }
        },
        methods: {
            filterNode(value, data) {
                if (!value) return true;
                return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
        }
    }
</script>

<style scoped lang="less">


    .searchTree {
        padding: 0 5px;
        height: 100%;
    }

    .ce-tree {
        margin-top: 12px;
        height: calc(100% - 40px);
        overflow: auto;
    }
</style>
