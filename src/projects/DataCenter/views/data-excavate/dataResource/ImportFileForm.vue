<template>
    <div class="importFileForm">
        <el-form :model="fileForm" status-icon ref="ruleForm" label-width="110px" size="small">
            <el-form-item label="是否有标题行 :" prop="title">
                <el-switch v-model="fileForm.hasTitle"></el-switch>
            </el-form-item>
            <el-form-item label="文件 :">
                <el-upload
                        ref="upload"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel,.csv,.xlsx"
                        :action="uploadUrl"
                        :on-remove="remove"
                        :before-remove="beforeRemove"
                        :file-list="fileForm.fileList"
                        :auto-upload="false"
                        :limit="fileLimit"
                        :on-exceed="fileExceed"
                        :on-change="fileChange"
                        :before-upload="beforeAvatarUpload"
                        :data="fileForm"
                        :on-progress="progress"
                        :on-success="successResave"
                        :on-error="error"

                >
                    <el-button size="mini" type="primary">文件浏览</el-button>
                    <div slot="tip" class="el-upload__tip">支持xls,xlsx,csv类型的文件,带标题行的excel文件头只能以字母开头包含数字或下划线!</div>
                </el-upload>
            </el-form-item>
            <el-form-item label="文件名 :">
                <el-input v-model="fileForm.fileName" placeholder="请输入文件名，默认使用上传文件名!"></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "ImportFileForm",
        data(){
            return {
                fileForm : {
                    hasTitle : false ,
                    fileList : [],
                    fileName :''
                },
                uploadUrl : '' , //上传地址 ,暂未有 上传会报错。
                fileLimit : 2,
                scanDisabled : true

            }
        },
        methods : {
            submitUpload(){ // 提交
                this.$refs.upload.submit();
            },
            remove(file, fileList) { //移除
                //console.log(file, fileList);
            },
           
            beforeRemove(file, fileList) { //移除前
                return this.$confirm(`确定移除 ${ file.name }？`);
            },
            fileExceed(file ,fileList){ //上传超出时
                this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
            },
            fileChange( file , fileList ){
                if (!this.fileType(file)) {
                    this.$message.error('上传文件只能是 xls/xlsx/csv 格式!');
                    this.fileForm.fileList = fileList.slice(0,-1);
                }else {
                    this.fileForm.fileList = fileList.slice(-1);
                }
            },
            fileType (file ){ //验证文件类型
                const type = ['application/vnd.ms-excel','application/kset'];
                return type.indexOf(file.type || file.raw.type) !== -1;
            },
            progress(e , file ,fileList){ //文件上传
                // console.log(e,fileList)
            },
            beforeAvatarUpload(file){ //上传前

            },
            successResave(response, file, fileList){

            },
            error(err, file, fileList){
                console.log(err)
            }
        }
    }
</script>

<style scoped>
    .importFileForm {
        height: 260px;
    }
</style>