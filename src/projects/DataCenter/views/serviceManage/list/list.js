import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {
    transList
} from "@/projects/DataCenter/views/modeling/dialog/rapid-analysis/model/model-panel/trans-list-mixins";
import detailDialog from "../dialog/detailDialog"
import createAPI from "../dialog/createAPI"
import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins"
import testDialog from "../dialog/testDialog"
import subscribeDialog from '../dialog/subscribeDialog';
import AuthDialog from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/auth-dialog";
import successDialog from "../dialog/successDialog"
import {createComponent} from '@/api/commonMethods/createComponent';
import helpDocument from "@/components/signed-components/helpDocument/helpDocument";
import lookVersion from "../dialog/lookVersion"

export default {
    name: "list",
    mixins: [commonMixins, common, listMixins, coustTableH, transList, servicesMixins],
    components: {
        detailDialog,
        createAPI,
        testDialog,
        AuthDialog,
        successDialog,
        helpDocument,
        subscribeDialog,
        lookVersion
    },
    props: {
        renewListLoad: Function,
    },
    watch: {
        "settings.loading": {
            handler(val) {
                this.renewListLoad(val);
            },
            deep: true
        }
    },
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        },
        // 是其他用户
        isOtherUser() {
            return this.currentUserId !== this.userInfo?.id;
        },
        tHeadData() {
            const operate = this.isOtherUser ? [] : [
                {
                    prop: 'operate',
                    label: '操作',
                    width: 300,
                    align: "center",
                    resizable: false
                }
            ];
            return [
                /*{
                    prop: "choose",
                    label: "",
                    minWidth: 200,
                    type : "selection"
                },*/
                {
                    prop: "interfaceChineseName",
                    label: "API名称",
                    minWidth: 120,
                    align: "left"
                },
                {
                    prop: "dirAllName",
                    label: "所属目录",
                    minWidth: 100,
                    resizable: false
                },
                {
                    prop: "serviceType",
                    label: "API类型",
                    width: "",
                    align: "center"
                },
                {
                    prop: "publishStatus",
                    label: "发布状态",
                    width: "130",
                    resizable: false
                },
                // {
                //     prop: "sourceName",
                //     label: "资源名称",
                //     minWidth: 120,
                //     align: "",
                //     // sortable: true,
                //     resizable: false
                // },
                {
                    prop: "status",
                    label: "服务状态",
                    width: "130",
                    align: "center"
                },
                {
                    prop: "time",
                    label: "更新时间",
                    width: "",
                    align: "center"
                },
                ...operate,
            ]
        }
    },
    data() {
        return {
            createR: $right['serviceManagementGenerateAPI'],
            batchShareR: $right['serviceManagementBatchSharing'],
            lookOverR: $right['serviceManagementLookOver'],
            activeN: "",
            tabData: [],
            statusData: [
                {
                    label: "全部状态",
                    value: ""
                },
                {
                    label: "已启用",
                    value: "1"
                },
                {
                    label: "已停用",
                    value: "3"
                },
            ],
            statusQuery: '',
            tabDataInfo: [
                {
                    label: "全部类型",
                    value: ""
                },
                {
                    label: "数据查询",
                    value: "4"
                },
                {
                    label: "信息核查",
                    value: "6"
                },
                {
                    label: "比对订阅",
                    value: "5"
                },
                {
                    label: "数据碰撞",
                    value: "7"
                },
                {
                    label: "模型分析",
                    value: "1"
                },
            ],
            inputValueTable: "",
            buttonTxt: "批量分享",
            myShareTxt: "我的分享",
            tableData: [],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe650;",
                    name: "发布",
                    clickFn: this.publish,
                    show: (row) => {
                        return row.publishStatus === '编辑中' && row.serviceType != 'AI算法'
                    },
                    condition: (right) => true
                },
                {
                    icon: "&#xe650;",
                    name: "编辑",
                    clickFn: this.edit,
                    show: (row) => true,
                    condition: (right) => right.indexOf($right['serviceManagementedit']) > -1
                },
                {
                    icon: "&#xe6c0;",
                    name: "停用",
                    clickFn: this.outOfService,
                    show: (row) => this.getButton(row),
                    condition: (right) => right.indexOf($right['serviceManagementDeactivate']) > -1
                },
                {
                    icon: "&#xe6c0;",
                    name: "启用",
                    clickFn: this.reStart,
                    show: (row) => {
                        return row.status === '已停用' && row.publishStatus === '已发布'
                    },
                    condition: (right) => right.indexOf($right['serviceManagementStart']) > -1
                },
                {
                    icon: "&#xe6c0;",
                    name: "测试",
                    clickFn: this.test,
                    show: () => true,
                    condition: (right) => right.indexOf($right['serviceManagementTest']) > -1
                },
                /*{
                    icon: "&#xe6c0;",
                    name: "订阅测试",
                    clickFn: this.subscribeTest,
                    show: (r,row) => { return row.serviceType === '比对订阅'},
                    condition: (right) => right.indexOf($right['serviceManagementDeactivate']) > -1
                },*/
                {
                    icon: "&#xe6c0;",
                    name: "分享",
                    clickFn: this.share,
                    show: () => true,
                    condition: (right) => right.indexOf($right['serviceManagementSharing']) > -1
                },
                // {
                //     icon: "&#xe6c0;",
                //     name: "查看版本",
                //     clickFn: this.lookVersion,
                //     show: () => true,
                //     condition: (right) => true
                // },
                {
                    icon: "&#xe6bf;",
                    name: "移动到",
                    clickFn: this.moveTo,
                    show: () => true,
                    condition: (right) => right.indexOf($right['serviceManagementMove']) > -1
                },
                {
                    icon: "&#xe650;",
                    name: "删除",
                    clickFn: this.uninstall,
                    show: (row) => true,
                    condition: (right) => right.indexOf($right['serviceManagementUninstallg']) > -1
                },
            ],
            createAPI: "生成API",
            apiName: "",
            moreTxt: "更多",
            apiOptions: [],
            servicesStatus: [], //服务状态
            multipleSelection: [],
            modelInfo: {},//模型详情信息
            isServiceManage: true,//服务管理生成API入口
            isServiceManageUpdate: false, //服务管理编辑入口
            addOrEdit: 'add',
            dirId: "-1",
            currentUserId: "",
            loading: true,
        }
    },
    methods: {
        linkToShare() {
            this.$router.push({
                name: "share",
                params: {
                    type: "serviceAll",
                    tab: "ShareTableList",
                }
            })
        },
        operateIconShow(row) {
            return this.hasRightOptIcon.filter(li => li.show(row))
        },
        showDY(row) {
            return row.serviceType === '比对订阅'
        },
        setDir(value, id, data) {
            this.dirId = id;
            this.currentUserId = data.currentUserId;
            this.changePage(1);
        },
        tabChange(value) {
            this.activeN = value;
            this.changePage(1);
        },
        /**
         * 创建成功api弹窗显示
         */
        async showSuccess(data) {
            const vm = this;
            await this.changePage(1);
            let dataInfo;
            vm.tableData.forEach(item => {
                if (item.interfaceChineseName === data.interfaceChineseName) dataInfo = item;
            })
            vm.$nextTick(() => {
                this.$refs.successDialog.show(dataInfo);
            })
        },
        share(row) {
            if (row.status === '已停用') {
                this.$message.warning("请先启用服务");
                return;
            }
            const vm = this;
            let list = [];
            list.push(row);
            vm.$nextTick(() => {
                this.$refs.auth.show(list, "", "serviceManage");
            })
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        batchShare() {
            // if (this.multipleSelection.length === 0) {
            //     this.$message.warning("请选择要分享的服务");
            //     return;
            // }
            // this.$refs.auth.show(this.multipleSelection,"", "serviceManage");
            const vm = this;
            let layer = this.$dgLayer({
                title: "批量分享",
                content: require("../dialog/addShare"),
                move: false,
                btn: ['确定', '取消'],
                props: {
                    activeN: vm.activeN,
                },
                on: {
                    refresh() {
                        vm.changePage(1);
                    },
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
                yes: function (index, layero) {
                    layer.$children[0].save(index);
                },
                success: function (layero, index) {
                    createComponent(helpDocument, {code: 'serviceManagementBatchSharing'}, layero.find(".layui-layer-title")[0])
                },
                cancel: function (index, layero) {
                    // 关闭对应弹窗的ID
                    layer.close(index);
                    return false;
                },
                area: ["1000px", "600px"],
                btnAlign: 'r',

            });
        },
        reNewTable() {
            this.changePage(1);
        },
        /**
         * 服务编辑
         * @param {*} row
         */
        async edit(row) {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            settings.loading = true;
            await services.queryServiceDetails(row.serviceMetaId, settings).then(res => {
                if (res.data.status === 0) {
                    res.data.data.serviceType = res.data.data.serviceType === 'AI算法' ? '2' : (res.data.data.serviceType === '模型分析' ? '1' : '4')
                    this.modelInfo = res.data.data;
                }
            })
            this.isServiceManageUpdate = true;
            this.APICreate('edit', row);
        },
        /**
         * 清空服务编辑入口获取的数据
         */
        clear() {
            this.isServiceManageUpdate = false;
            this.modelInfo = {};
        },
        /**
         * 测试
         */
        test(row) {
            if (row.status === '已停用') {
                this.$message.warning("请先启用服务");
                return;
            }
            const vm = this;
            vm.$nextTick(() => {
                vm.$refs.testDialog.show(row, 'serviceManagementTest');
            })
        },
        /**
         * 订阅测试
         */
        subscribeTest(row) {
            if (row.status === '已停用') {
                this.$message.warning("请先启用服务");
                return;
            }
            const vm = this;
            vm.$nextTick(() => {
                vm.$refs.subscribeDialog.show(row);
            })
        },
        /**
         * 卸载
         */
        uninstall(row) {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            vm.confirm('提示', `确定要删除该服务?`, () => {
                settings.loading = true;
                if (this.activeN === '2') {
                    services.unInstall(row.serviceMetaId, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("删除成功!");
                            this.changePage(vm.paginationProps.currentPage);
                        }
                    })
                } else {
                    services.offlineServiceByServiceMetaId(row.serviceMetaId, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("删除成功!");
                            this.changePage(vm.paginationProps.currentPage);
                        }
                    })
                }
            })
        },
        /**
         * 停用
         */
        outOfService(row) {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            vm.confirm('提示', `确定要停用该服务?`, () => {
                settings.loading = true;
                if (this.activeN === '2') {
                    services.offLine(row.serviceMetaId, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("API停用中!");
                            this.changePage(vm.paginationProps.currentPage);
                        }
                    })
                } else {
                    services.disableService(row.serviceMetaId, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("API停用中！");
                            this.changePage(vm.paginationProps.currentPage);
                        }
                    })
                }
            })
        },
        /**
         * 启用
         */
        reStart(row) {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            vm.confirm('提示', `确定要启用该服务?`, () => {
                settings.loading = true;
                if (this.activeN === '2') {
                    services.aiExport(row.serviceMetaId, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("API启用中!");
                            this.changePage(vm.paginationProps.currentPage);
                        }
                    })
                } else {
                    services.redistributionService(row.serviceMetaId, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("API启用中！");
                            this.changePage(vm.paginationProps.currentPage);
                        }
                    })
                }
            })
        },
        getButton(row) {
            return row.status === '已启用';
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
        /**
         * API详情跳转
         * @param {} row
         */
        detailInfo(row, index) {
            if (this.rights.indexOf("serviceManagementLookOver") === -1) {
                this.$message.warning("无功能访问权限");
                return;
            }
            const vm = this, {isOtherUser} = vm;
            vm.modelInfo.transId = row.serviceType === "AI算法" ? row.transId : row.modelId;
            let layer = this.$dgLayer({
                title: "api详情",
                content: require("@/projects/DataCenter/views/modeling/dialog/details/detail-page/apiServer/index"),
                move: false,
                props: {
                    info: Object.assign(vm.modelInfo, row),
                    isAi: row.serviceType === 'AI算法',
                    type: row.serviceType !== 'AI算法' ? 'modeling' : '',
                    serviceDetail: {
                        serviceId: row.serviceId
                    },
                    isService: true,
                    isOtherUser,
                },
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                        vm.modelInfo = {};
                    },
                    changePage() {
                        vm.changePage();
                    }
                },
                cancel: function (index, layero) {
                    vm.modelInfo = {};
                    //vm.changePage();
                },
                area: ["80%", "80%"],
                btnAlign: 'r',
            });
        },
        /**
         * 新建API
         */
        APICreate(type, row) {
            const vm = this;
            vm.addOrEdit = type;
            vm.$nextTick(() => {
                this.$refs.createAPI.show({}, '', row || {}, vm.dirId);
            })
        },
        /**
         * 获取下拉类别
         */
        getOptions() {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            // settings.loading = true;
            return services.getQueryType().then(res => {
                if (res.data.status === 0) {
                    this.apiOptions = res.data.data.map((item) => {
                        item.value = item.code;
                        item.label = item.name;
                        return item;
                    })
                    this.apiName = res.data.data[0].code;
                }
            })
        },
        async changePage(index = 1) {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                size: vm.paginationProps.pageSize,
                index: index,
                name: vm.inputValueTable,
                queryType: vm.apiName,
                servicetype: vm.activeN,
                classifyId: vm.dirId,
                status: vm.statusQuery,
                operatorId: vm.currentUserId,
            };
            vm.tableData = [];
            await services.queryServicePage(data, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data || {};
                    result.dataList && result.dataList.forEach(e => {
                        e.status = e.status === '1' ? "已启用" : "已停用";
                        e.publishStatus = e.serviceType !== 'AI算法' ? (e.saveType === '0' ? '已发布' : '编辑中') : '已发布';
                    })
                    vm.tableData = result.dataList;
                    vm.total = result.totalCount;
                }
            })

        },
        getServicesStatus() {
            const vm = this, {serviceManageServices, serviceManageMock} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            return services.getServiceStatus().then(res => {
                if (res.data.status === 0) {
                    this.servicesStatus = res.data.data;
                }
            })
        },
        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        moveTo(row) {
            this.$emit("moveTo", row);
        },
        //查看版本
        lookVersion(row) {
            this.$refs.lookVersion.show(row);
        },
        //发布
        async publish(row) {
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            settings.loading = true;
            let serviceId = '';

            if (row.serviceType === '数据查询' || (row.serviceType === '信息核查' && row.singleOrManyTable != '2')) {
                await services.publishServiceNoSaveMeta(row.serviceMetaId, settings).then(res => {
                    if (res.data.status === 0) {
                        serviceId = res.data.data;
                    }
                })
            } else if (['AI算法'].includes(row.serviceType)) {
                // services.publishServiceNoSaveMeta(row.serviceMetaId, settings).then(res => {
                //     if (res.data.status === 0) {
                //                         serviceId = res.data.data;
                //                     }
                // })
            } else {
                await services.publishModelServiceCreateJar(row.serviceMetaId, settings).then(res => {
                    if (res.data.status === 0) {
                        serviceId = res.data.data;
                    }
                })
            }
            if (!serviceId)
                return
            setTimeout(() => {
                vm.changePage(vm.paginationProps.currentPage);
            }, 1000)

        },
        init() {
            this.tabData = this.$hideAI ? this.tabDataInfo : [...this.tabDataInfo, {label: "AI算法", value: "2"}];
            this.loading = true;
            Promise.all([
                this.getServicesStatus(),
                this.getOptions(),
            ]).finally(() => {
                this.loading = false;
            })
        }
    },
    created() {
        this.init()
    }
}
