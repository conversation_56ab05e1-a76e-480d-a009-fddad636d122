<template>
<div>
    <common-dialog custom-class="custom" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   v-loading="settings.loading"
                   @closed="clearD">
        <div>
            <div class="ce-description_tip">{{ tip }} &nbsp; &nbsp;{{ tip2 }}&nbsp; &nbsp;{{ tip3 }}</div>
            <el-form
                    ref="form"
                    :model="{ list: tableData }"
                    @submit.native.prevent>
                <common-table
                        ref="list"
                        height="calc(75vh - 154px)"
                        :pagination="false"
                        :data="tableData"
                        :columns="tHeadData"
                >
                    <template slot="operate" slot-scope="{row , $index}">
                        <el-form-item>
                               <span class="p5" v-for="(col , inx) in operateIcons"
                                     :key="inx">
                            <el-popconfirm
                                    v-if="col.type === 'delete'"
                                    title="确认删除?"
                                    @confirm="col.clickFn(row,$index)"
                            >
                                <el-button type="text"
                                           slot="reference"
                                           :title="col.tip">
                                    <span v-html="col.tip"></span>
                                </el-button>
                            </el-popconfirm>
                            <el-button type="text"
                                       v-else
                                       :title="col.tip"
                                       @click="col.clickFn(row,$index)">
                                <span v-html="col.tip"></span>
                            </el-button>

                        </span>
                        </el-form-item>
                    </template>
                    <template slot="header-operate">
                        <el-button type="text" :title="headerIcon.title" @click="headerIcon.clickFn">
                            <i class="model_status" :class="headerIcon.icon"></i>
                        </el-button>
                    </template>
                    <template slot="memo" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.memo`">
                            <el-input v-model.trim="row.memo"
                                    maxlength="20"
                                    placeholder="请输入描述"
                            ></el-input>
                        </el-form-item>
                    </template>
                    <template slot="name" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.name`"
                                      :rules="[...rules.name ,...checkSameName($index , '请勿输入相同参数名' , tableData , 'name')]">
                            <el-input v-model.trim="row.name"
                                      v-input-limit:fieldCode
                                      maxlength="20"
                                      :placeholder="namePlaceholder"
                            ></el-input>
                        </el-form-item>
                    </template>
                    <template slot="header-name" slot-scope="{row , $index}">
                        <div>{{tHeadData[0].label}}<span class="red">*</span></div>
                    </template>
                    <template slot="header-varValue" slot-scope="{row , $index}">
                        <div>{{tHeadData[5].label}}<span class="red">*</span></div>
                    </template>
                    <template slot="varValue" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.varValue`"
                                    :rules="rules.varValue">
                            <el-input  v-model="row.varValue" readonly v-if="row.valueType === 'variable'">
                                <i slot="suffix" class="el-icon-edit" @click="showExpression(row,$index)"></i>
                            </el-input>
                            <!-- <dg-select class="width100" v-if="row.valueType === 'variable'" v-model="row.varValue" :data="variableList"></dg-select> -->
                            <el-input v-else-if="row.dataType.toLowerCase() === 'string'" v-model="row.varValue" v-input-limit:trim ></el-input>
                            <el-input-number v-else-if="row.dataType.toLowerCase() === 'double'"
                                            controls-position="right"
                                            class="ce-number"
                                            v-model="row.varValue" :precision="2" :step="0.1" :min="0"></el-input-number>
                            <el-input v-else-if="row.dataType.toLowerCase() === 'integer'"
                                    v-model.number="row.varValue" v-input-limit:number ></el-input>
                            <el-date-picker
                                    v-else-if="row.dataType.toLowerCase() === 'date'"
                                    v-model="row.varValue"
                                    class="ce-date"
                                    :value-format="dataFormat"
                                    type="date"
                                    placeholder="选择日期">
                            </el-date-picker>
                            <el-date-picker
                                    v-else-if="row.dataType.toLowerCase() === 'time'"
                                    v-model="row.varValue"
                                    class="ce-date"
                                    :value-format="dataFormat"
                                    type="datetime"
                                    placeholder="选择日期时间">
                            </el-date-picker>
                        </el-form-item>
                    </template>
                    <template slot="parameterScope" slot-scope="{row, $index}">
                        <el-form-item :prop="`list.${$index}.parameterScope`"
                                    :rules="rules.varValue">
                                <el-select v-model="row.parameterScope" placeholder="请选择">
                                    <el-option
                                        v-for="item in scopeOption"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                        </el-form-item>
                    </template>
                    <template slot="valueType" slot-scope="{row, $index}">
                        <el-form-item :prop="`list.${$index}.valueType`"
                                    :rules="rules.varValue">
                                <el-select v-model="row.valueType" placeholder="请选择" @change="valueTypeChange(row)">
                                    <el-option
                                        v-for="item in valueTypeOption"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                        </el-form-item>
                    </template>
                    <template slot="dataType" slot-scope="{row , $index}">
                        <el-form-item :prop="`list.${$index}.dataType`">
                            <dg-select class="width100" v-model="row.dataType" :data="dataTypeOpt"
                                       @change="varChange($event , row)"></dg-select>
                        </el-form-item>
                    </template>
                </common-table>
            </el-form>
            <el-button type="text" @click="showGlobal(false)" v-if="!isShow">显示全局参数</el-button>
            <el-button type="text" @click="showGlobal(true)" v-else>不显示全局参数</el-button>
        </div>
        <span slot="footer">
            <dg-button @click="visible = false">{{ btnCancelTxt }}</dg-button>
            <dg-button type="primary" @click="save">{{ saveBtnTxt }}</dg-button>
        </span>
    </common-dialog>
    <expressiongDialog ref="expressionDialog" :rowData="rowData" @getFunc="getFunc"></expressiongDialog>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import expressiongDialog from "./dialog/index"
export default {
    name: "globalVariable",
    mixins: [commonMixins, servicesMixins, listMixins],
    components : {expressiongDialog},
    props: {
        rowData: Object,
    },
    data() {
        return {
            rules: {
                name: [
                    {required: true, message: "请输入参数名称", trigger: ['blur', 'change']},
                    {
                        validator: this.checkedCommon,
                        reg: /^[A-Za-z_][A-Za-z_0-9]*$/,
                        message: "参数名称支持字母数字下划线(非数字开头)",
                        trigger: ["blur", "change"]
                    },

                ],
                varValue: [
                    {required: true, message: "请输入参数值", trigger: ['blur', 'change']},
                ],
            },
            namePlaceholder: "请输入参数名(限20个字符)",
            title: "参数设置",
            width: "1000px",
            visible: false,
            tip: "动态参数可在建模组件的参数配置中使用，引用方式",
            tip2: "@{data}",
            tip3: "比如自定义SQL过滤。",
            tHeadData: [
                {
                    prop: "name",
                    label: "参数名称",
                    align: 'left'
                },{
                    prop: "memo",
                    label: "参数描述",
                    align: 'left'
                }, {
                    prop: "dataType",
                    align: "left",
                    label: "参数类型",
                }, {
                    prop: "parameterScope",
                    label: "作用域",
                }, {
                    prop: "valueType",
                    label: "值类型",
                }, {
                    prop: "varValue",
                    label: "参数值",
                }, {
                    prop: "operate",
                    align: "center",
                    width: "120"
                }
            ],
            scopeOption :[
                {
                    value : "local",
                    label : "局部"
                },{
                    value : "global",
                    label : "全局"
                }
            ],
            valueTypeOption : [
                {
                    value : "constant",
                    label : "常量"
                },{
                    value : "variable",
                    label : "变量"
                }
            ],
            dataTypeOpt: [],
            tableData: [],
            operateIcons: [
                {
                    icon: "&#xe6f4;",
                    tip: "复制",
                    type: "copy",
                    clickFn: this.copyVar
                },
                {
                    icon: "&#xe65f;",
                    tip: "删除",
                    type: "delete",
                    clickFn: this.deleteVar
                },
            ],
            headerIcon: {
                title: "添加参数",
                icon: "el-icon-circle-plus",
                clickFn: this.addVar
            },
            deleteTransVariableList: [],
            dataFormat : "yyyy-MM-dd HH:mm:ss",
            variableList : [] ,//算子集合
            isShow : false, //是否展示全局变量
            dataSetId : null,//保存后的表ID（相当于rowData.transId)
        }
    },
    methods: {
        showGlobal(isShow){
            this.init(this.dataSetId);
            this.isShow = !isShow;
        },
        /**
         * 值类型变化
         */
        valueTypeChange(row){
            row.varValue = "";
        },
        showExpression(row,index){
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.expressionDialog.showExpression(row, index);
            })
        },
        /**
         * 类型变化 重置变量值
         * */
        varChange(val, row) {
            row.varValue = "";
        },
        addVar() {
            this.tableData.push({
                name: "",
                dataType: "String",
                varValue: "",
                parameterScope : "local",
                valueType : "constant"
            })
        },
        /**
         * 删除变量
         * @param row
         * @param inx
         */
        deleteVar(row, inx) {
            this.tableData.splice(inx, 1);
            if (row.id) this.deleteTransVariableList.push(row.id);
        },

        copyVar(row, inx) {
            let newRow = this.copyArrayObj(row);
            newRow.id = "";
            this.tableData.push(newRow);
        },
        show() {
            this.visible = true;
        },
        clearD() {

        },
        changePage() {
        },
        async getType() {
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            settings.loading = true;
            vm.dataTypeOpt = [];
            await services.getVariableParameterTypes().then(res => {
                if (res.data.status === 0) {
                    vm.dataTypeOpt = res.data.data.map(opt => {
                        return {
                            label: opt.label,
                            value: opt.label
                        }
                    });
                }
            })
        },
        /**
         * 参数名称输入校验
         */
        checkSelectIndex(val, index, key) {
            let reg = new RegExp(/^[A-Za-z_][A-Za-z_0-9]*$/);
            if (!reg.test(val)) {
                this.$message.warning("索引由数字、字母、下划线组成且不以数字开头");
                this.tableData[index].name = "";
            }
        },
        validate(...arg) {
            return this.$refs.form.validate(...arg);
        },
        /**
         * 变量类型时获取表达式
         */
        getFunc(data, index){
            this.tableData[index].varValue = data.name;
            this.tableData[index].expressionScript = data.expressionScript;
        },
        save() {
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            let transVariables = [];
            vm.validate(valid => {
                if (valid) {
                    let unSave = false;//判断是否可以保存
                    this.tableData.forEach(item => {
                        if (item.parameterScope === 'local' && !vm.dataSetId) {
                            unSave = true;
                        }
                        transVariables.push({
                            id: item.id || "",
                            "paramCode": "@{" + item.name+"}",
                            "paramValue": item.valueType === 'variable' ? item.expressionScript : item.varValue,
                            "paramType": item.dataType,
                            "variable"  : item.valueType === "variable",
                            "global" : item.parameterScope === 'global',
                            "name" : item.valueType === 'variable' ? item.varValue : "",
                            "memo" : item.memo,
                        })
                    })
                    if (unSave) {
                        this.$message.warning('设置局部变量请先保存！');
                        return;
                    }
                    let params = {
                        transId: vm.dataSetId,
                        transVariables: transVariables,
                        deleteTransVariable: this.deleteTransVariableList,
                        relationType : 1
                    }
                    settings.loading = true;
                    services.saveTransVariable(params, settings).then(res => {
                        if (res.data.status === 0) {
                            this.$message.success("保存成功");
                            this.$emit('variableSaveSuccess');
                            this.visible = false;
                        }
                    })
                }
            })
        },
        async init(dataSetId) {
            await this.getType();
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            settings.loading = true;
            this.show();
            vm.tableData = [];
            this.dataSetId = dataSetId || "";
            services.queryTransByTransId(this.dataSetId, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    result.forEach(item => {
                        let fString = item.paramCode.split("@")[1];
                        fString = fString.substring(1);
                        fString = fString.substring(0,fString.length - 1);
                        // let secondString = fString.split("}")[0];
                        vm.tableData.push({
                            id: item.id,
                            name: fString,
                            dataType: item.paramType,
                            varValue: item.variable ? item.name : item.paramValue,
                            expressionScript : item.variable ? item.paramValue : "",
                            valueType : item.variable ? "variable" : "constant",
                            parameterScope : item.global ? "global" : "local",
                            memo : item.memo ,
                        })
                    })
                    if (!vm.isShow) {
                        this.tableData = this.tableData.filter(item => {return item.parameterScope !== 'global'});
                    }
                    vm.$nextTick(()=>{
                        vm.$refs.list.$refs.table.doLayout();
                    })
                }
            })
            this.queryUdfVariableList();

        },
        queryUdfVariableList(){
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            settings.loading = true;
            vm.variableList = [];
            services.queryUdfVariableList(settings).then(res => {
                let result = res.data.data;
                result.forEach(item=>{
                    vm.variableList.push({
                        label : item.name,
                        value : item.expression,
                    })
                })
            })
        }
    },
    created() {
        // this.init();
    }
}
</script>

<style scoped lang="less">
.model_status {
    padding: 0 5px;
    font-size: 18px;
}
.ce-date {
    width: 100%;
}
.ce-number {
    width: 100%;
    &.el-input-number/deep/ .el-input__inner {
        text-align: left;
    }
}
    .red{
        color:red;
    }

</style>
