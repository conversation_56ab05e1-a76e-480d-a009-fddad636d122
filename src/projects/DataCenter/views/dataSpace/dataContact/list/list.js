import { commonMixins } from "@/api/commonMethods/common-mixins"
import { common } from "@/api/commonMethods/common"
import { servicesMixins } from "../../service-mixins/service-mixins";
import { mapGetters } from "vuex"
import $right from "@/assets/data/right-data/right-data";
import { listMixins } from "@/api/commonMethods/list-mixins";
import { coustTableH } from "@/api/commonMethods/count-table-h";

export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common, listMixins, coustTableH],
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;

        }
    },
    watch: {
        "settings.loading": {
            handler(val) {
                this.$emit("listLoad", val)
            },
            deep: true
        }
    },
    data() {
        return {
            resultAnalysisTxt: "结果分析库",
            treeNode: {},
            selectType: '',
            selectData: [],
            dataSourceTip: "通过对接元数据管理系统，获取数据库的连接配置信息，帮助用户快速的完成数据源的添加。",
            inputValueTable: "",
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "数据源名称",
                    minWidth: 150,
                    align: "left"
                },
                {
                    prop: "instanceType",
                    label: "数据源类型",
                    minWidth: 150,
                    align: "center"
                },
                /*{
                    prop: "source",
                    label: "来源",
                    minWidth: 150,
                    align: "center"
                },*/
                {
                    prop: "path",
                    label: "所属目录",
                    minWidth: 150,
                    align: "left",
                    resizable: false
                },
                {
                    prop: "user",
                    label: "创建人",
                    minWidth: 150,
                    align: "center",
                },
                {
                    prop: 'operateTime',
                    label: '更新时间',
                    minWidth: 150,
                    align: "center",
                    resizable: false
                }, {
                    prop: 'connectStatus',
                    label: '状态',
                    width: 60,
                    align: "center",
                    resizable: false
                }, {
                    prop: 'operate',
                    label: '操作',
                    width: 300,
                    align: "center",
                    resizable: false
                }
            ],
            total: 0,
            operateIcons: [
                {
                    icon: "&#xe6b6;",
                    name: "测试连接",
                    clickFn: this.testLink,
                    show: () => true,
                    condition: (right) => right.indexOf($right["dataConnectionTestConnection"]) > -1
                },
                {
                    icon: "&#xe61d;",
                    name: "添加数据表",
                    clickFn: this.addContactTable,
                    show: () => true,
                    condition: (right) => right.indexOf($right["dataConnectionAddDataTable"]) > -1
                },
                {
                    icon: "&#xe6c0;",
                    name: "编辑",
                    clickFn: this.editContact,
                    show: (row) => {
                        return row.source != '大数据管家'
                    },
                    condition: (right, node) => right.indexOf($right["dataConnectionEditDataSource"]) > -1
                },
                {
                    name: "另存为",
                    clickFn: this.saveOther,
                    show: () => true,
                    condition: (right) => right.indexOf($right["dataConnectionSaveAs"]) > -1
                },
                {
                    name: "移动到",
                    clickFn: this.moveTo,
                    show: () => true,
                    condition: (right) => right.indexOf($right["dataConnectionMove"]) > -1
                },
                //分割线
                /*{
                    name: "divider",
                    show: () => true,
                    condition: (right) => (right.indexOf("dataConnectionSaveAs") > -1||right.indexOf("dataConnectionMove") > -1)&&right.indexOf("dataConnectionDeleteDataSource") > -1
                },*/
                {
                    icon: "&#xe65f;",
                    name: "删除",
                    show: () => true,
                    clickFn: this.deleteModel,
                    condition: (right) => right.indexOf($right["dataConnectionDeleteDataSource"]) > -1
                },
            ],
            dirId: "-1",
            dirType: "TRANS_DIR_MF",
            addDataSourceTxt: "添加数据源",
            fromDataSourceTxt: "从元数据导入",
            addDataObj: $right['dataConnectionAddDataSource'],//新建数据源权限
            addDataObjImport: $right['dataConnectionMetadata'],//从元数据导入权限
            typeMap: {},
        }
    },
    methods: {
        /**
         * 设置结果分析库
         */
        showResultAnalysis() {
            const layer = this.$dgLayer({
                title: "结果分析库",
                content: require("../dialog/setAnalysePage.vue"),
                maxmin: false,
                move: false,
                area: ["800px", "320px"],
                on: {
                    success() {
                        layer.close(layer.dialogIndex);
                    }
                }
            })
        },
        //表格编辑
        editContact(row) {
            this.$emit("editContact", row);
        },
        //添加数据源
        addContact(type) {
            this.$emit("addContact", type);
        },
        //数据源详情
        detailShow(row) {
            //this.$emit("dataDetails", row);
            if (!row) return
            const vm = this;
            let url = "";
            url = require("@/projects/DataCenter/views/dataSpace/dataContact/dialog/detailTables/index.vue");
            let layer = this.$dgLayer({
                content: url,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    row: row,
                },
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            //layer.$children[0].addTab({},false ,vm.nodeId);
        },
        //添加数据表
        addContactTable(row) {
            this.$emit("addContactTable", row);
        },
        //编辑后修改数据源名称
        changeDbName() {
            this.changePage(1);
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },

        update() {
            this.changePage(this.currentPage);
        },
        changePage(index) {
            if (this.treeNode.treeType === "sourceDataSetTree") {
                this.sourcePageChange(index);
            } else {
                this.myDataChange(index);
            }
        },
        //我的数据源
        myDataChange(index) {
            const vm = this, { dataSpaceServices, dataSpaceMock, settings } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                pageSize: vm.paginationProps.pageSize,
                pageIndex: index,
                id: vm.dirId,
                name: vm.inputValueTable,
                instanceType: vm.selectType,
                sortField: 'operateTime',
                sortOrder: 'desc'
            };
            vm.tableData = [];
            services.queryDirTreeChidren(data, settings).then(res => {
                if (res.data.status === 0) {
                    let callbackData = res.data.data;
                    vm.tableData = [];
                    if (callbackData.dataList && callbackData.dataList.length) {
                        callbackData.dataList.forEach(item => {
                            vm.tableData.push(item);
                        });
                        vm.total = callbackData.totalCount;
                    }
                }
            })
        },
        //数据仓库表格数据
        sourcePageChange(index) {
            const vm = this;
            const { dataSpaceServices, dataSpaceMock, settings } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            vm.paginationProps.currentPage = index;
            let data = {
                pageSize: vm.paginationProps.pageSize,
                pageIndex: index,
                id: vm.dirId,
                name: vm.inputValueTable,
                instanceType: vm.selectType,
                sortField: 'operateTime',
                sortOrder: 'desc'
            };
            settings.loading = true;
            vm.tableData = [];
            services.queryDirTreeChidren(data, settings).then(res => {
                if (res.data.status === 0) {
                    let callbackData = res.data.data;
                    vm.tableData = [];
                    if (callbackData.dataList && callbackData.dataList.length) {
                        callbackData.dataList.forEach(item => {
                            vm.tableData.push(item);
                        });
                        vm.total = callbackData.totalCount;
                    }
                }
            })
        },
        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        //测试连接
        testLink(row) {
            const vm = this, { dataSpaceServices, dataSpaceMock } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            services.checkOut(row.catalogId).then(res => {
                if (res.data.status === 0 && res.data.data == true) {
                    vm.$message({
                        type: 'success',
                        message: "数据源连接成功！",
                    })
                } else {
                    vm.$message.warning('数据源连接失败！')
                }
            })
        },
        //删除
        deleteModel(row) {
            const vm = this, { dataSpaceServices, dataSpaceMock } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            let url = "";
            if (row.source === '大数据管家') {
                url = "deleteDwFromMetdata" + "?id=" + row.id;
            } else {
                url = "deleteDWDBInstance" + "?elementId=" + row.id + "&classifyId=" + row.pId;
            }
            vm.confirm('提示', `此操作将删除 \"${row.name}\" 数据源, 是否继续?`, () => {
                services.dataObject(url).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功");
                        this.changePage(1);
                    }
                })
            })
        },
        selectFirsrtInit(data) {
            this.treeNode = data;
            this.dirId = data.id;
            this.changePage(1);
        },
        //获取数据源类型
        allType() {
            const vm = this, { dataSpaceServices, dataSpaceMock } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            services.queryAllTypes().then(res => {
                if (res.data.status === 0) {
                    vm.selectData = [{ label: '所有数据源类型', value: '' }];
                    vm.typeMap = {};
                    Object.keys(res.data.data).forEach(n => {
                        if (n.toUpperCase() === "LIBRA") {
                            vm.typeMap.HWMPP = 'Libra';
                        }
                        if (n !== '' && n.toUpperCase() !== "LIBRA") {
                            vm.selectData.push({ label: n, value: res.data.data[n] });
                            vm.typeMap[n.toUpperCase()] = n;
                        }
                    })
                }
            })

        },
        setDirType(value, id, node, type) {
            const vm = this;
            vm.dirId = id;
            vm.treeNode = node;
            vm.treeNode.treeType = type;
            this.changePage(1);
        },
        //另存为
        saveOther(row) {
            this.$emit('saveOther', row);
        },
        getParentId(list, id) {
            for (let i in list) {
                if (list[i].id == id) {
                    return [list[i]]
                } else if (list[i].children) {
                    let node = this.getParentId(list[i].children, id);
                    if (node !== undefined) {
                        return list[i]
                    }
                }
            }
        },
        saveOtherSure(value, id, type, rowData) {
            const vm = this, { dataSpaceServices, dataSpaceMock } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            let data = {
                name: value,
                dwbId: rowData.id,
                schemaId: rowData.schemaId,
                dbType: rowData.instanceType,
                classifyId: id,
                busiDirId: this.getParentId(this.$parent.$parent.treeNode, id).id
            }
            services.saveAs(data).then(res => {
                if (res.data.status === 0) {
                    vm.$emit('closeSave', id);
                    vm.$message.success("保存成功");
                    vm.changePage(1);
                } else {
                    vm.$emit('closeLoading');
                }
            })
        },
        //移动到
        moveTo(row) {
            this.$emit('moveTo', row);
        },
        moveUpdate(dwbId, dirId, node) {
            if (node.level == 1) {
                this.$message.warning('不能移动到该目录！');
                return
            }
            const vm = this, { dataSpaceServices, dataSpaceMock, settings } = this;
            let services = vm.getServices(dataSpaceServices, dataSpaceMock);
            let data = {
                dirId: dirId,
                dwbId: dwbId
            }
            settings.loading = true;
            services.moveDwbInstance(data, settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success('移动成功！');
                    vm.$emit('closeMove', dirId);
                    vm.changePage(1);
                }
            })
        }
    },
    created() {
        this.allType();
        //this.changePage(1);
    }
}
