import {tabMixins} from "@/projects/DataCenter/views/modeling/dialog/model-tab/model-tab-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
import sqlPanel from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/sql-analyse/sql-panel"
import CaseTabPanel from "@/components/common/case-tab-panel/CaseTabPanel";
import saveModel from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/sql-analyse/save-model"
import {mapState, mapActions} from "vuex";

export default {
    name: "sqlAnalyse" ,
    mixins : [tabMixins ,commonMixins,servicesMixins],
    components: {
        sqlPanel,
        CaseTabPanel,
        saveModel
    },
    props: {
        dirId:String,
        dsTypeId:String,
    },
    data(){
        return {
            tabIndex: 1,
            curRowData : {},
            modelTxt:"SQL数据集" ,
            newRowProps :["id" , "name" ,"code" , "parentid", "dsTypeId"]
        }
    },
    computed: {
        ...mapState({
            planId: (state) => state.plans.planId,
            planRowData : (state) => state.plans.plans.find( pl => pl.id === state.plans.planId).rowData,
        }),
    },
    mounted() {
        const {$services} = this;
        // 获取类型分类
        this.getTypeList({$services});
    },
    methods : {
        ...mapActions(["getTypeList"]),
        save(){
            const vm = this , {dirId, curRowData , planId} = vm;
            let schemaId = vm.$refs['sql_' + planId].$refs.source.schemaId ,
                sql =vm.$refs['sql_' + planId].funcCode;
            if(!schemaId){
                vm.$message.warning("请选择数据源!");
                return;
            }
            vm.$refs.saveModel.show(dirId ,schemaId , sql , curRowData , '保存');
        },
        /**
         * 另存为
         */
        saveOther(){
            const vm = this , {curRowData , planId, dirId, dsTypeId} = vm ,{name ,code,parentId} = curRowData;
            let schemaId = vm.$refs['sql_' + planId].$refs.source.schemaId ,
                sql =vm.$refs['sql_' + planId].funcCode;
            const setDirId = dirId || parentId , setTypeId = curRowData.dsTypeId || dsTypeId;
            vm.$refs.saveModel.show(setDirId ,schemaId , sql , {name ,code} , '另存为',setTypeId);
        },
        /**
         * 显示另存为 可设置的方法、也可是 true /false
         * @return {*}
         */
        showSaveOther(){
            return !(this.curRowData && this.curRowData.isNew);
        },
        tabChange(rowData){
            this.curRowData = rowData;
        },
        /**
         * 编辑
         * @param row
         */
        addTab(row){
            this.$refs.tabPanel.addTab(row);
        }
    }
}
