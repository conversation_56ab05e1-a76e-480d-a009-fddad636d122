/**
 * 数据建模插件 接口
 * @Author: wangjt
 * @Date: 2020-07-14
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/plugin/"  ;

/*
* sql输入插件
* @params
* @returns {Promise<*>}
* */

export async function sqlView(transStepId ) {
    return await reqUtil.get({
        url : root + "sql/" + "view"
    } , {transStepId})
}
/*
* sql 参数
* @p
* rams
* @returns {Promise<*>}
* */

export async function sqlColumns(transStepId , dataObjId ,customDataSet ,settings,logicDataSetId ) {
    return await reqUtil.get({
        url : root + "sql/" + "columns" ,
        settings : settings
    } , {transStepId , dataObjId ,customDataSet,logicDataSetId})
}
/*
* sql 预览
* @params
* @returns {Promise<*>}
* */

export async function previewCondition(transStepId , dataObjId ,conditions ,settings ) {
    return await reqUtil.post({
        url : root + "sql/preview/" + "conditions?transStepId=" + transStepId + "&dataObjId=" + dataObjId +"&cnt=10",
        settings : settings
    } , conditions )
}
export async function previewSql(transStepId , dataObjId , sql , settings , cnt = 10  ) {
    return await reqUtil.post({
        url : root + "sql/preview/" + "sql" ,
        settings : settings
    } , {transStepId , dataObjId ,cnt , sql} )
}


/*
* SQL增量信息
* @params
* @returns {Promise<*>}
* */
export async function sql_incr(params ) {
    return await reqUtil.get({
        url : root + "sql/incr"
    },params)
}
/*
* SQL取消增量信息
* @params
* @returns {Promise<*>}
* */
export async function sql_no_incr(transStepId ) {
    return await reqUtil.get({
        url : root + "sql/no_incr"
    },{transStepId })
}
/*
* SQL保存
* @params
* @returns {Promise<*>}
* */
export async function save_sql(transStepId , dataObjId ,sql ,fetchSize ,customDataSet ,settings) {
    return await reqUtil.post({
        url : root + "sql/save/sql",
        settings : settings
    },{transStepId , dataObjId ,sql ,fetchSize ,customDataSet })
}
/*
* SQL保存
* @params
* @returns {Promise<*>}
* */
export async function save_conditions(transStepId , dataObjId ,fetchSize ,customDataSet,conditions , settings ) {
    return await reqUtil.post({
        url : root + `sql/save/conditions?transStepId=${transStepId}&dataObjId=${dataObjId}&fetchSize=${fetchSize}&customDataSet=${customDataSet}` ,
        settings : settings
    }, conditions)
}

/**
 * 获取数据集sql
 * @param dataSetId
 * @return {Promise<*>}
 */
export async function getLogicDataObjSQL(dataSetId){
    return await reqUtil.get({
        url : "/editDataSet/getLogicDataObjSQL"
    },{dataSetId})
}

/*高级*/
const root1 = "/dataModeling/";

/*
* 初始化
* @params
* @returns {Promise<*>}
* */

export async function advanceConfig(tranStepId) {
    return await reqUtil.get({
        url : root1 +  "advanceConfig"
    },{tranStepId})
}

/*
* 获取输出字段
* @params
* @returns {Promise<*>}
* */

export async function showOutputColumn(tranStepId) {
    return await reqUtil.get({
        url : root1 +  "showOutputColumn"
    },{tranStepId})
}
/*
* 获取分区信息
* @params
* @returns {Promise<*>}
* */

export async function partitionPage(url) {
    return await reqUtil.get({
        url : url
    })
}

/*
* 保存 变更
* @params  url , tranStepId   , dynamicParamJson
* @returns {Promise<*>}
* */
export async function saveDynamic( url  ,tranStepId , dynamicParamJson) {
    return await reqUtil.get({
        url: url ,
        successMsg : "保存成功"
    }, {tranStepId, dynamicParamJson} );
}
/*
* 获取 变更
* @params  url , tranStepId   , dynamicParamJson
* @returns {Promise<*>}
* */
export async function getDynamic( url , tranStepId ) {
    return await reqUtil.get({
        url: url
    }, {tranStepId} );
}

/*
* 保存输出字段
* @params
* @returns {Promise<*>}
* */
export async function modifyColumn(  tranStepId , field ,length ,precsn , valType ,uniqueValue, filterExpress ) {
    return await reqUtil.get({
        url: root1 + "modifyColumn"
    }, {tranStepId , field ,length ,precsn , valType ,uniqueValue, filterExpress} );
}
/*
* 保存抽样设置
* @params
* @returns {Promise<*>}
* */
export async function saveAdvanceConfig(  tranStepId , isCopy ,threadCount ,sampleExpr , sampleDistinct ,exceptionMode ) {
    return await reqUtil.get({
        url: root1 + "save/advanceConfig"
    }, {tranStepId , isCopy ,threadCount ,sampleExpr , sampleDistinct ,exceptionMode} );
}
/*
* 保存排序设置
* @params
* @returns {Promise<*>}
* */
export async function saveSort(  tranStepId , sortColumn ,sortType ) {
    return await reqUtil.get({
        url: root + "fulltext/input/saveSort"
    }, {tranStepId , sortColumn ,sortType});
}

const w_root = "/plugin/window/";
/*
*  初始化
 * @param tranStepId
 * @returns {Promise<*>}
* */
export async function getPlugin( transtepId , settings) {
    return await reqUtil.get({
        url: w_root + 'getPlugin' ,
        settings : settings
    }, {transtepId} );
}

/*
*  保存
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function savePlugin( params ,settings ) {
    return await reqUtil.post({
        url: w_root + 'savePlugin' ,
        settings : settings
    }, params );
}

//初始化全文库输入的分区类型
/*
*  分区类型
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_columns( tranStepId) {
    return await reqUtil.get({
        url: root + 'fulltext/input/columns'
    }, {tranStepId} );
}
/*
*  排序字段
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_getSort( tranStepId) {
    return await reqUtil.get({
        url: root + 'fulltext/input/getSort'
    }, {tranStepId} );
}
/*
*  保存排序字段
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_saveSort( tranStepId , sortColumn , sortType) {
    return await reqUtil.get({
        url: root + 'fulltext/input/saveSort'
    }, {tranStepId , sortColumn , sortType} );
}
/*
*  保存分区
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function savePartition( url , params ) {
    return await reqUtil.post({
        url: url
    },params);
}

/*
*  全文库初始化
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_view( tranStepId , settings ) {
    return await reqUtil.get({
        url: root + 'fulltext/input/query' ,
        settings : settings
    },{tranStepId});
}
/*
*  全文库 预览
 * @param
 * @returns {Promise<*>}
* */
export async function fulltext_preview( params , settings ) {
    return await reqUtil.post({
        url: root + 'fulltext/input/preview' ,
        settings : settings
    },params);
}

/*
*  全文库 保存
 * @param
 * @returns {Promise<*>}
* */
export async function fulltext_save( params , settings ) {
    return await reqUtil.post({
        url: root + 'fulltext/input/save' ,
        settings : settings
    },params);
}
/*
*  全文库输出初始化
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_outView( tranStepId , settings ) {
    return await reqUtil.get({
        url: root + 'fulltext/output/query' ,
        settings : settings
    },{tranStepId});
}
/**
 * 全文库 输出 导入字段
 * @param tranStepId
 * @param schemaId
 * @param dataObjId
 * @param createTable
 * @return {Promise<*>}
 */
export async function fulltext_getColumn(tranStepId , schemaId , dataObjId , createTable){
    return await reqUtil.get({
        url : root + "fulltext/output/getColumn"
    },{tranStepId , schemaId , dataObjId , createTable})
}

/**
 * 全文 获取输入字段
 * @param transStepId
 * @return {Promise<*>}
 */
export async function fulltext_inputCols(transStepId){
    return await reqUtil.get({
        url : root + "fulltext/output/inputCols"
    }, {transStepId})
}

/**
 * 获取输出字段
 * @param dataObjId
 * @return {Promise<*>}
 */
export async function fulltext_outputCols(dataObjId){
    return await reqUtil.get({
        url : root + "fulltext/output/outputCols"
    },{dataObjId})
}

const kv_root = "/kvInput/";

/*
*  kv 获取列簇
 * @param
 * @returns {Promise<*>}
* */
export async function getColumnByColumnFamily( tableId , columnFamily ) {
    return await reqUtil.get({
        url: kv_root + 'getColumnByColumnFamily' ,
    },{tableId , columnFamily});
}
/*
*  kv 初始化
 * @param
 * @returns {Promise<*>}
* */
export async function kvInputPage( tableId , tranStepId ,settings ) {
    return await reqUtil.get({
        url: kv_root + 'kvInputPage' ,
        settings : settings
    },{tableId , tranStepId});
}
/*
*  kv 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveKVInputPlugin( tranStepId  , params , settings ) {
    return await reqUtil.post({
        url: kv_root + 'saveKVInputPlugin?tranStepId='+tranStepId ,
        settings : settings
    },params );
}
/*
*  kv 预览
 * @param
 * @returns {Promise<*>}
* */
export async function kvInputPreview(tableId , tranStepId  , params , settings ) {
    return await reqUtil.post({
        url: kv_root + `saveKVInputPlugin?tableId=${tableId}&tranStepId=${tranStepId}` ,
        settings : settings
    },params );
}

const p_root = "/dataPreview/";
/*
*  插件 预览
 * @param
 * @returns {Promise<*>}
* */
export async function pluginPreview(transId , tranStepId ,settings){
    return await reqUtil.get({
        url : p_root + "preview",
        settings : settings
    }, {transId , tranStepId})
}
const f_root = "/aggregator/";


export async function saveCollisionPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "collision/saveCollisionPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

/*
*  碰撞插件
 * @param
 * @returns {Promise<*>}
* */
export async function collisionPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "collision/collisionPluginPage",
        settings : settings
    }, {tranStepId})
}



/*
*  全连接
 * @param
 * @returns {Promise<*>}
* */
export async function fullJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "fullJoin/fullJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  全连接 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveFullJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "fullJoin/saveFullJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  交集
 * @param
 * @returns {Promise<*>}
* */
export async function innerJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "innerJoin/innerJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  交集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveInnerJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "innerJoin/saveInnerJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  左右连接
 * @param
 * @returns {Promise<*>}
* */
export async function leftOrRightJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "leftOrRightJoin/leftOrRightJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  左右连接 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveLeftOrRightJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "leftOrRightJoin/saveLeftOrRightJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  左右排除
 * @param
 * @returns {Promise<*>}
* */
export async function subtractByKeyPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "subtractByKey/subtractByKeyPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  左右排除 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveSubtractByKeyPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "subtractByKey/saveSubtractByKeyPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  并集
 * @param
 * @returns {Promise<*>}
* */
export async function unionJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "unionJoinPlugin/unionJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  并集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveUnionJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "unionJoinPlugin/saveUnionJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

const s_root ="/serviceOrganization/";

/*
*  算子编排
 * @param
 * @returns {Promise<*>}
* */
export async function operatorPage( tranStepId ,settings){
    return await reqUtil.get({
        url :s_root + "edit",
        settings : settings
    }, {tranStepId})
}

/*
*  算子编排 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveOperator( tranStepId , params , settings){
    return await reqUtil.post({
        url :s_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  算子编排 删除
 * @param
 * @returns {Promise<*>}
* */
export async function deleteGraph( graphIds){
    return await reqUtil.post({
        url :s_root + "deleteGraph",
    },graphIds)
}

/*
*  算子编排 画布 步骤信息修改
 * @param
 * @returns {Promise<*>}
* */
export async function updataServiceOrchestrationNode( params ){
    return await reqUtil.post({
        url :s_root + "updataServiceOrchestrationNode" ,
    } , params )
}
/*
*  算子编排 画布 保存端点
 * @param
 * @returns {Promise<*>}
* */
export async function savaServiceOrchestrationEdge( params ){
    return await reqUtil.post({
        url : s_root +"savaServiceOrchestrationEdge" ,
    } , params )
}

/*
*  算子编排 获取 连线option
 * @param
 * @returns {Promise<*>}
* */
export async function serviceParams( serviceId ){
    return await reqUtil.get({
        url : s_root +"serviceParams" ,
    } , {serviceId} )
}

/*
*  算子编排 删除 连线
 * @param
 * @returns {Promise<*>}
* */
export async function deleteServiceOrchestrationEdge( fromNodeId , toNodeId ){
    return await reqUtil.post({
        url : s_root +`deleteServiceOrchestrationEdge?fromNodeId=${fromNodeId}&toNodeId=${toNodeId}` ,
    })
}


/*
*  算子编排 新增
 * @param
 * @returns {Promise<*>}
* */
export async function savaServiceOrchestrationNode( params ){
    return await reqUtil.post({
        url : s_root +"savaServiceOrchestrationNode" ,
    } , params )
}
/*
*  全领公式算子 新增
 * @param
 * @returns {Promise<*>}
* */
export async function saveAmountMath( params ){
    return await reqUtil.post({
        url : "/amountMathOperator/saveAmountMath" ,
    } , params )
}

/*
*  算子编排 删除步骤
 * @param
 * @returns {Promise<*>}
* */
export async function deleteServiceOrchestrationNode( nodeId ){
    return await reqUtil.post({
        url : s_root +`deleteServiceOrchestrationNode?nodeId=${nodeId}` ,
    })
}

/*
*  表达式插件 删除eda节点
 * @param
 * @returns {Promise<*>}
* */
export async function deleteEDANodeByNodeId( nodeId ){
    return await reqUtil.get({
        url :`/amountMathOperator/deleteEDANode?nodeId=${nodeId}` ,
    })
}

/*
*  算子编排 回显
 * @param
 * @returns {Promise<*>}
* */
export async function getRuleGraph( params ){
    return await reqUtil.post({
        url : s_root +"getRuleGraph" ,
    } , params )
}
/*
*  算子编排 连线
 * @param
 * @returns {Promise<*>}
* */
export async function saveEdge( serviceOrgId , params ){
    return await reqUtil.post({
        url : s_root +`saveEdge?serviceOrgId=${serviceOrgId}` ,
    } , params )
}

/*
*  新建表注册
 * @param param id
 * @returns {Promise<*>}
* */
export async function register( id ) {
    return await reqUtil.get({
        url: '/spark/register'
    } , {id});
}

/*
*  关系库输出初始化
 * @param saveVo
 * @returns {Promise<*>}
* */
export async function sqlOutView( transStepId ,settings ) {
    return await reqUtil.get({
        url: '/plugin/sqlout/view',
        settings : settings
    } , {transStepId});
}

/*
*  关系库输出保存
 * @param saveVo
 * @returns {Promise<*>}
* */
export async function sqlOutSave( saveVo ,settings ) {
    return await reqUtil.post({
        url: '/plugin/sqlout/save',
        settings : settings
    } , saveVo);
}
/*
*  关系库输出 导入字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutCreateTableMeta( transStepId ,schemaId ) {
    return await reqUtil.get({
        url: '/plugin/sqlout/createTableMeta',
    } , {transStepId ,schemaId});
}

/*
*  关系库输出 获取目标字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutColumns( transStepId ,dataObjId ) {
    return await reqUtil.get({
        url: '/plugin/sqlout/columns',
    } , { transStepId ,dataObjId});
}
/*
*  关系库输出 获取来源字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutMeta( transStepId ) {
    return await reqUtil.get({
        url: '/plugin/sqlout/meta/src',
    } , { transStepId});
}
/*
*  数据排序
 * @param
 * @returns {Promise<*>}
* */
export async function dataSortPluginPage( tranStepId ,settings ) {
    return await reqUtil.get({
        url: '/datasort/dataSortPluginPage',
        settings : settings
    } , { tranStepId });
}

/*
*  数据排序
 * @param
 * @returns {Promise<*>}
* */
export async function getInputColumn( tranStepId ) {
    return await reqUtil.get({
        url: '/datasort/getInputColumn',
    } , { tranStepId });
}
/*
*  数据排序保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveDataSortPlugin( tranStepId, pluginMeta ,settings) {
    return await reqUtil.post({
        url: '/datasort/saveDataSortPlugin?tranStepId='+tranStepId,
        settings : settings
    } , pluginMeta);
}

const exp_root ="/expressionExcuter/";

/**
 * 表达式插件回选
 * @param tranStepId
 * @param settings
 * @returns {Promise<*>}
 */
export async function queryDataExp(tranStepId ,settings){
    return await reqUtil.get({
        url :exp_root + "queryData",
        settings : settings
    }, {tranStepId})
}

export async function saveOperatorExp( tranStepId , params , settings){
    return await reqUtil.post({
        url :exp_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}


export async function getOutputColumnExp(groupId ,settings){
    return await reqUtil.get({
        url :exp_root + "getOutputColumn",
        settings : settings
    }, {groupId})
}

const script_root ="/script/";
export async function saveScriptExp( tranStepId , params , settings){
    return await reqUtil.post({
        url :script_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
export async function queryScriptData(tranStepId ,settings){
    return await reqUtil.get({
        url :script_root + "queryData",
        settings : settings
    }, {tranStepId})
}
export async function runScript( params){
    return await reqUtil.post({
        url :"/mlsql/runScript" ,
    }, params)
}
export async function getSchema(param){
    return await reqUtil.get({
        url :"/mlsql/getSchema?tableName="+param ,
    })
}
export async function transStandardType(params){
    return await reqUtil.post({
        url :"/script/transStandardType" ,
    }, params)
}

export async function getAfterTableName(transId,tranStepId){
    return await reqUtil.get({
        url :"/mlsql/getAfterTableName?id="+transId+"&stepId="+tranStepId ,
    })
}

export async function inintLabelPluginPage(tranStepId){
    return await reqUtil.get({
        url :"/label/labelPluginPage" ,
    },{tranStepId})
}


export async function initEffective(tranStepId){
    return await reqUtil.get({
        url :"/effective/pageEffective" ,
    },{tranStepId})
}
/*
*  获取字段类型
 * @param
 * @returns {Promise<*>}
* */
export async function getColumnType() {
    return await reqUtil.get({
        url: 'udfOperatorManage/getParameterTypes',
    });
}
export async function saveLabelPluginPage(tranStepId,params){
    return await reqUtil.post({
        url :"/label/saveLabel?stepId=" + tranStepId ,
    },params)
}export async function saveEffectivePluginPage(tranStepId,params){
    return await reqUtil.post({
        url :"/effective/saveEffective?stepId=" + tranStepId ,
    },params)
}

/*
*  表达式处理 获取输入字段
* @param
* @returns {Promise<*>}
* */
export async function queryPrevNodeVariables(tranStepId , type , nodeId ="" ) {
    return await reqUtil.get({
        url : "serviceOrganization/queryPrevNodeVariables"
    },{tranStepId ,nodeId , type})
}

/*
*  表达式处理 获取算子树
* @param
* @returns {Promise<*>}
* */
export async function getOperatorTree(version , settings) {
    return await reqUtil.get({
        url : "udfOperatorManage/getOperatorTree",
        settings:settings
    },{version})
}
/*udf 画布接口*/
let udf_root = "/udfGraphManage/";

/*
*  表达式处理 新增udf节点
* @param udfNodeVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfNode(udfNodeVo) {
    return await reqUtil.post({
        url : udf_root + "saveOrUpdateUdfNode",
    },udfNodeVo)
}

/*
*  表达式处理 获取图
* @param udfGraphVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfGraph(udfGraphVo) {
    return await reqUtil.post({
        url : udf_root + "saveOrUpdateUdfGraph",
    },udfGraphVo)
}

const denormaliser_root ="/denormaliser/";
export async function saveDenormaliser( tranStepId , params , settings){
    return await reqUtil.post({
        url :denormaliser_root + "savePluginMeta?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

const jsonParsing_root ="/jsonParsing/";
export async function saveJsonParsing( tranStepId , params , settings){
    return await reqUtil.post({
        url :jsonParsing_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  表达式处理 删除节点
* @param nodeId
* @returns {Promise<*>}
* */
export async function deleteUdfNodeById(nodeId) {
    return await reqUtil.get({
        url : udf_root + "deleteUdfNodeById",
    },{nodeId})
}

/*
*  表达式处理 获取节点信息
* @param nodeId
* @returns {Promise<*>}
* */
export async function getUdfNodById(nodeId) {
    return await reqUtil.get({
        url : udf_root + "getUdfNodById",
    },{nodeId})
}

/*
*  表达式处理 保存边
* @param udfEdgeVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfEdge(udfEdgeVo) {
    return await reqUtil.post({
        url : udf_root + "saveOrUpdateUdfEdge",
    },udfEdgeVo)
}

/*
*  表达式处理 删除边
* @param edgeId
* @returns {Promise<*>}
* */
export async function deleteUdfEdgeById(edgeId) {
    return await reqUtil.get({
        url : udf_root + "deleteUdfEdgeById",
    },{edgeId})
}

/*
*  表达式处理 删除图
* @param graphId
* @returns {Promise<*>}
* */
export async function deleteUdfGraphById(graphId) {
    return await reqUtil.get({
        url : udf_root + "deleteUdfGraphById",
    },graphId)
}

/*
*  表达式处理 删除图
* @param graphId
* @returns {Promise<*>}
* */
export async function getUdfGraphById(graphId,tansId) {
    return await reqUtil.get({
        url : udf_root + "getUdfGraphById?graphId="+graphId+"&tansId="+tansId,
    })
}
/*
*  表达式处理 获取输入类型
* @param graphId
* @returns {Promise<*>}
* */
export async function getParameterTypes() {
    return await reqUtil.get({
        url : "udfOperatorManage/getParameterTypes",
    })
}
/*
*  表达式处理 获取填入的信息
* @param nodeId
* @returns {Promise<*>}
* */
export async function getUdfParameterByNodeId(nodeId) {
    return await reqUtil.get({
        url :udf_root + "getUdfParameterByNodeId",
    },{nodeId})
}
export async function getUdfOperatorById(operatorId , settings) {
    return await reqUtil.get({
        url :"udfOperatorManage/getUdfOperatorById",
        settings
    },{operatorId})
}

/**
 * 字段过滤插件 获取上流所有字段
 * @param tranStepId
 * @returns {Promise<*>}
 */
export async function getFieldFilteringFields(tranStepId) {
    return await reqUtil.get({
        url :"fieldfiltering/getFields",
    },{tranStepId})
}

/**
 * 警务区计算插件
 * @param tranStepId
 * @returns {Promise<*>}
 */
export async function saveJwqMapper(stepId , params , settings) {
    return await reqUtil.post({
        url :"jwqMapper/saveMeta?stepId=" + stepId,
        settings : settings
    }, params)
}
export async function getJwqMapperPlugin(tranStepId) {
    return await reqUtil.get({
        url :"jwqMapper/getJwqMapperPluginPage",
    },{tranStepId})
}


const lucene_root ="/luceneBuilder/";
export async function saveLuceneBuilder(stepId , params , settings){
    return await reqUtil.post({
        url :lucene_root + "saveMeta?stepId=" + stepId,
        settings : settings
    }, params)
}

/**
 * 条件 过滤插件
 * @param 
 * @returns {Promise<*>}
 */
const con_root = "/conditionFilter/";
export async function conditionFilterQueryData(tranStepId , settings) {
    return await reqUtil.get({
        url : con_root + "queryData" ,
        settings
    } ,{tranStepId})
}
//保存
export async function conditionSavePlugin(tranStepId , pluginMeta , settings) {
    return await reqUtil.post({
        url : con_root + "savePlugin?tranStepId=" + tranStepId,
        settings
    },pluginMeta)
}


/**
 * 归一化插件
 * @param tranStepId
 * @returns {Promise<*>}
 */
const nor_root = "/normalization/";
export async function getNormalizationPage(tranStepId , settings) {
    return await reqUtil.get({
        url : nor_root + "getNormalizationPage" ,
        settings
    } ,{tranStepId})
}
//保存
export async function saveNormalizationMeta(tranStepId , pluginMeta , settings) {
    return await reqUtil.post({
        url : nor_root + "saveNormalizationMeta?stepId=" + tranStepId,
        settings
    },pluginMeta)
}



/**
 * 文件上传插件
 * */
const file_root = "/plugin/fileInput/";
//保存
export async function saveFileInputPlugin(tranStepId, file, settings) {
    return await reqUtil.post({
        url : file_root + "savePlugin?tranStepId=" + tranStepId,
        settings
    },file)
}


export async function getFileInputPluginPage(tranStepId, settings) {
    return await reqUtil.get({
        url : file_root +"queryData?tranStepId=" +tranStepId,
        settings
    })
}

export async function getFiledWithRealtime(tranStepId, sheetName, startRow ,startColumn,useHeader,settings) {
    return await reqUtil.get({
        url : file_root + "getFiledWithRealtime?tranStepId=" +
            tranStepId + "&sheetName=" + sheetName+ "&startRow=" +startRow + "&startColumn=" +startColumn + "&useHeader=" +useHeader,
        settings
    })
}
/**
 * 文件输出插件
 * */
const fileout_root = "/plugin/fileOutput/";
export async function saveFileOutputPlugin(tranStepId, fileOutputMeta, settings) {
    return await reqUtil.post({
        url : fileout_root + "savePlugin?tranStepId=" + tranStepId,
        settings
    },fileOutputMeta)
}

export async function getFileTypeAndFields(tranStepId, settings) {
    return await reqUtil.get({
        url : fileout_root +"getFileTypeAndFields?tranStepId=" +tranStepId,
        settings
    })
}

export async function getFileOutputPluginPage(tranStepId, settings) {
	return await reqUtil.get({
		url : fileout_root +"queryData?tranStepId=" +tranStepId,
		settings
	})
}
export async function getFields(tranStepId, settings) {
	return await reqUtil.get({
		url : fileout_root +"getFields?tranStepId=" +tranStepId,
			settings
	})
}

export async function getDownloadIsOk(tranStepId, settings) {
    return await reqUtil.get({
        url : fileout_root +"getDownloadIsOk?tranStepId=" +tranStepId,
        settings
    })
}

/**
 * 同行插件
 **/

/**
 * 初始化
 * @param tranStepId
 * @return {Promise<*>}
 */
export async function getPeerView(tranStepId){
    return await reqUtil.get({
        url : "/peer/view"
    },{tranStepId})
}

/**
 * 保存
 * @param tranStepId
 * @param pluginMeta
 * @param settings
 * @return {Promise<*>}
 */
export async function savePeer(tranStepId , pluginMeta ,settings){
    return await reqUtil.post({
        url : "/peer/save?tranStepId="+tranStepId,
        settings
    },pluginMeta)
}

/*
* 时间打标
* */

/**
 * 初始化
 * @param tranStepId
 * @param settings
 * @return {Promise<*>}
 */
const markTimeRoot = "/markingtime/";
export async function markingTimeView(tranStepId , settings){
    return await reqUtil.get({
        url : markTimeRoot + "view",
        settings
    },{tranStepId})
}

export async function saveMarkingTime(tranStepId , pluginMeta , settings){
    return await reqUtil.post({
        url :markTimeRoot + "save?tranStepId="+tranStepId,
        settings
    },pluginMeta)
}

/*
*  字段转对象插件
* */
export async function queryObjectJsonData(tranStepId,settings) {
    return await reqUtil.get({
        url : "objectToJson/queryObjectJsonData?tranStepId=" + tranStepId,
        settings
    })
}
export async function getObjectJsonSelectData(settings) {
    return await reqUtil.get({
        url : "objectToJson/getConversionType",
        settings
    })
}
export async function objectToJsonSavePlugin(tranStepId,objectToJsonMeta, settings) {
    return await reqUtil.post({
        url : "objectToJson/savePlugin?tranStepId="+tranStepId,
        settings
    }, objectToJsonMeta)
}

/*
*  RAY执行插件
* */
export async function queryAllTask(tranStepId,settings) {
    return await reqUtil.get({
        url : "pyTask/queryAllTask?tranStepId=" + tranStepId,
        settings
    })
}
export async function rayRunnerSavePlugin(tranStepId,pluginMeta, settings) {
    return await reqUtil.post({
        url : "pyTaskRunner/save?tranStepId="+tranStepId,
        settings
    }, pluginMeta)
}

export async function rayRunnerPreview(tranStepId,settings) {
    return await reqUtil.post({
        url : "pyTaskRunner/view?tranStepId="+tranStepId,
        settings
    })
}
/**
 * 数据去重插件
 */
export async function queryData(tranStepId,settings) {
    return await reqUtil.get({
        url : "dataDistinct/queryData?tranStepId=" + tranStepId,
        settings
    })
}
export async function removeDistinctSave(tranStepId,dataDistinctMeta,settings) {
    return await reqUtil.post({
        url : "dataDistinct/savePlugin?tranStepId=" + tranStepId,
        settings
    }, dataDistinctMeta)
}
