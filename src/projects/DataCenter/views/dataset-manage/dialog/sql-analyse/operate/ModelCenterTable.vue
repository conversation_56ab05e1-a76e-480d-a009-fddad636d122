<template>
    <div>
        <el-table
                :data="tableData"
                borde
                size="mini"
                :height="tableHeight"
                tooltip-effect="light"
                ref="table"

        >
            <el-table-column
                    v-for="(item,index) in tHeadData"
                    :key="index"
                    :prop="item.prop"
                    :width="item.width"
                    :label="item.label"
                    :align="item.align"
                    :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    <label v-if="item.type==='tableCell'">{{scope.row[item.prop]}}</label>
                    <el-select filterable size="mini" v-model="scope.row[item.prop]" v-if="item.type==='select'">
                        <el-option v-for="opt in item.options "
                                   :label="opt.label"
                                   :value="opt.value"
                        ></el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
    export default {
        name: "ModelCenterTable",
        components: {},
        data() {
            return {
                dialogVisible: false,
                tableHeight: "0",
                tHeadData: [
                    {
                        prop: "name",
                        label: "字段名称",
                        width: "",
                        align: "left",
                        type: "tableCell"
                    },
                    {
                        prop: "type",
                        label: "字段类型",
                        width: "",
                        align: "center",
                        type: "tableCell"
                    }
                ],
                tableData: [],
                codeSetting: {},
            };
        },
        methods: {
            selectCode(){},
            getTableData() {
                return this.tableData;
            },
            getAdaptiveControl(dispId) {
                //获取适配控件的label
                // let tempDisp = {};
                // this.tHeadData[2].options.forEach(val => {
                //     if (val.value === dispId) {
                //         tempDisp = {getCodeData
                //             label: val.label,
                //             value: dispId,
                //         }
                //     }
                // });
                // return tempDisp
            },
            setTableData(rows) {
                const vm = this;
                this.getCodeData();
                this.tableData = [];

                let adaptive = "";

                if (rows !== null) {
                    rows.forEach(item => {
                        vm.tableData.push({
                            name: item.name,
                            type: item.dataType,
                        });
                    })
                }
            },
            refSearchModel(value) {
                this.$message(value);
                this.tableData = [
                    {
                        date: "2016-05-02",
                        name: value,
                        operation: "上海",
                        explain: "普陀区",
                        sort: "上海市普陀区金沙江路 1518 弄",
                        zip: 200333
                    }
                ];
            },
            refPageChange(index) {
                this.$message("当前页 " + index);
            },
            resizeTableHeight(height) {
                this.tableHeight = height;
            },
            showCodeTable() {
                this.dialogVisible = true;
            },
            getCodeData() {

            }
        }
    };
</script>
<style scoped>
    i {
        padding: 5px;
    }
</style>