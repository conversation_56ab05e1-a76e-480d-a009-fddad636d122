<template>
    <div class="saveTreeView">
        <el-input placeholder="请输入名称" v-model="filterText" v-input-limit:trim class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn">
            <dg-tree
                    class="filter-tree"
                    v-on="$listeners"
                    :data="data"
                    :props="defaultProps"
                    node-key="id"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    :highlight-current="true"
                    :default-expanded-keys="expandData"
                    ref="tree">
                <slot name="treeNode">
                     <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                        <span class="node-label"
                              :title="data.label"
                              :class="{
                                'el-icon-folder' : !node.expanded ,
                                'el-icon-folder-opened' : node.expanded
                              }"
                        >{{ data.label }}</span>
                     </span>
                </slot>
            </dg-tree>
        </div>
    </div>
</template>

<script>
import {treeMethods} from "@/api/treeNameCheck/treeName"

export default {
    name: "saveTreeView",
    props: {
        data : {
            type : Array ,
            default:()=>[]
        },
        defaultProps: {
           type : Object ,
            default : ()=>{
                return {
                    children: 'children',
                    label: 'label',
                    value: "id"
                }
            }
        },
        expandData : {
            type : Array,
            default:()=>[]
        },
        dirId : String
    },
    data() {
        return {
            filterText : ""
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    methods : {
        filterNode : treeMethods.methods.filterNode,
        setCurrentNode(id){
            this.$refs.tree.setCurrentKey(id);
        }
    }
}
</script>

<style scoped>
    .saveTreeView {
        height: 100%;
    }
    .ce-tree {
        margin-top: 12px;
        height: calc(100% - 44px);
        overflow: auto;
    }
</style>
