<template>
    <div class="slidePlugIn" v-if="plugList.length" v-show="showSlide">
        <ul  class="slideGroup">
            <li class="module-icon"
                v-for="(plug , index) in plugList"
                :key="index"
                draggable="true"
                @dragstart="addNode(plug)"
            >
                <div class="m-icon">
                    <svg class="eda_icon" aria-hidden="true">
                        <use :xlink:href="plug.icon"></use>
                    </svg>
                </div>
                <p class="m-name">{{plug.label}}</p>
            </li>
        </ul>
    </div>
</template>

<script>
    import { globalBus } from '@/api/globalBus';
    export default {
        name: "SlidePlugIn",
        props : {
            plugList : Array ,
            showSlide : Boolean
        },
        data(){
          return {

          }
        },
        methods :{
            addNode(plug){
                globalBus.$emit('plugNode', plug);
            },
        },
        created() {
        }
    }
</script>

<style scoped lang="less">

    .slidePlugIn {
        background: #fff;
        padding: 10px;
        border: 1px solid #ddd;
        box-shadow: 0 1px 3px 0 rgba(0,0,0,0.2);
        position: absolute;
        top: 100%;
        max-width: 272px;
        transform: translate( calc(100% - 16px) ,0 );
        right: 0;
        z-index: 10;
    }
    .slideGroup {
        position: relative;
        float: left;
    }

    .module-icon {
        padding: 5px 7px 8px 7px;
        cursor: pointer;
        text-align: center;
        float: left;
        position: relative;
    }

    .m-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin: auto;
        text-align: center;
    }

    .m-icon .eda_icon {
        width: 40px;
        height: 36px;
    }

    .m-name {
        font-size: 1rem;
        display: block;
        max-width: 5rem;
        min-width: 3.4rem;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: @main-font;
    }
    .module-icon:hover {
        background: rgba(40, 155, 241, .1);
        border-radius: 2px;
    }
</style>
