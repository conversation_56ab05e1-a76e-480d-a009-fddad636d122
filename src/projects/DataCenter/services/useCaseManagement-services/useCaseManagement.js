
/**
 * 用例管理 接口
 * @Author: chenzt
 * @Date: 2022-10-25
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/useCaseManagement/";

/**
 * 获取用例目录树
 * @param {*} settings 
 */
export async function getUseCaseClassify( settings,) {
    return await reqUtil.get({
        url : root + "getUseCaseClassify",
        settings : settings
    },{})
}

/**
 * 新增用例目录树
 * @param {*} parentId 
 * @param {*} name 
 * @param {*} settings 
 */
export async function newCaseClassify( {parentId, name, settings,}) {
    return await reqUtil.post({
        url : root + "newCaseClassify",
        settings : settings
    }, {parentId, name})
}

/**
 * 编辑目录（重命名、移动）
 * @param {*} id 
 * @param {*} parentId 
 * @param {*} name 
 * @param {*} settings 
 */
export async function editCaseClassify( id, parentId, name, settings,) {
    return await reqUtil.post({
        url : root + "editCaseClassify",
        settings : settings
    }, id, parentId, name,)
}

/**
 * 删除用例目录树
 * @param {*} classifyId 
 * @param {*} settings 
 */
export async function deleteCaseClassify( classifyId, settings,) {
    return await reqUtil.get({
        url : root + "deleteCaseClassify?classifyId=" + classifyId,
        settings : settings
    },)
}

/**
 * 右侧查询列表
 * @param {*} classifyId 
 * @param {*} useCaseName 
 * @param {*} pageIndex 
 * @param {*} pageSize 
 * @param {*} settings 
 */
export async function getUseCaseList( param, settings,) {
    return await reqUtil.post({
        url : root + "getUseCaseList" ,
        settings : settings
    }, param )
}

/**
 * 
 * @param {*} useCaseId 
 * @param {*} settings 
 */
export async function deleteUseCase( useCaseId, settings,) {
    return await reqUtil.get({
        url : root + "deleteUseCase?useCaseId=" + useCaseId ,
        settings : settings
    }, useCaseId )
}

/**
 * 新增用例
 * @param {*} params 
 * @param {*} settings 
 */
export async function newUseCase( params, settings,) {
    return await reqUtil.post({
        url : root + "newUseCase"  ,
        settings : settings
    }, params )
}

/**
 * 更新用例
 * @param {*} params 
 * @param {*} settings 
 */
export async function editUseCase( params, settings,) {
    return await reqUtil.post({
        url : root + "editUseCase"  ,
        settings : settings
    }, params )
}

/**
 * 用例详情
 * @param {*} useCaseId 
 * @param {*} settings 
 */
export async function getUseCaseDetail( useCaseId, settings,) {
    return await reqUtil.get({
        url : root + "getUseCaseDetail?useCaseId=" + useCaseId ,
        settings : settings
    } )
}
