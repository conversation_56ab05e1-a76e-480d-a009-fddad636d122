<template>
    <el-dialog
        title="生成API成功"
        :visible.sync="dialogVisible"
        width="30%"
        :before-close="handleClose">
        <span>您可能需要进一步进行如下操作:</span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="goToTest">前往测试API</el-button>
            <el-button type="primary" @click="goToShare">分享API</el-button>
            <el-button type="primary" @click="goToDetail">查看API详情</el-button>
        </span>
    </el-dialog>
</template>
<script>
export default {
    name: "successDialog",
    mixins : [ ],
    components: {},
    props:{
    },
    data(){
        return {
            dialogVisible :false,
            apiInfo : [],
        }
    },
    methods: {
        handleClose(){
            this.dialogVisible = false;
        },
        show(data){
            this.apiInfo = data;
            this.dialogVisible = true;
        },
        goToTest(){
            this.handleClose();
            this.$emit("test", this.apiInfo);
        },
        goToShare(){
            this.handleClose();
            this.$emit("share", this.apiInfo);
        },
        goToDetail(){
            this.handleClose();
            this.$emit("detailInfo", this.apiInfo);
        }
    },
}
</script>

<style scoped>
</style>
