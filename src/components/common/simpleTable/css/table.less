.ce-simple_table {
  table-layout: fixed;
  width: 100%;
}
.ce-simple__header {
  background: #e7eaf0;
}
.ce-simple_table th {
  padding: 6px 0;
  background: #e7eaf0;
  color: #555;
  border-bottom: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
}
.ce-simple__body {
  overflow: auto;
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  border-bottom: 1px solid #EBEEF5;
}
.ce-simple_table td {
  padding: 6px;
  text-align: center;
  border-bottom: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ce-simple_table tr:nth-child(2n) {
  background: #FAFAFA;
}
.ce-simple_table tr:nth-child(2n + 1) {
  background: #fff;
}
.ce-simple_table tbody tr:hover td {
  background-color: #d4ebfc;
}