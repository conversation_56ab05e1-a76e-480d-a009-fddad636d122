<template>
    <div class="excelImport" v-loading="loading">
        <!-- <el-form label-width="80px" label-position="right">
            <el-form-item label="选择文件:"> -->
                <dg-upload
                        class="upload-demo"
                        ref="upload"
                        :accept="'.model'"
                        action=""
                        :on-preview="handlePreview"
                        :on-remove="handleRemove"
                        :on-change="fileChange"
                        v-model="fileList"
                        :show-file-list="false"
                        :auto-upload="false"
                >
                    <!-- <el-button slot="trigger" size="small" icon="el-icon-upload2">{{ chooseFile }}</el-button> -->
                    <div class="ShareItem" slot="trigger" >
                        <i class="dg-iconp iconC" >&#xecd3;</i>
                                    <h1>点击上传文件进行导入</h1>

                    </div>
                    <!-- <el-link :href="downLoadAddress"  target="_blank" >{{ download }}</el-link> -->
                    <!-- <a :href="downLoadAddress" :download="'业务元模块' + '.xlsx'" target="_blank" class="el-link" @click.stop>{{ download }}</a> -->
                    <!-- <div slot="tip" class="el-upload__tip">
                        <span>{{tipMsg[0]}}</span>
                        <span class="tip-red">{{fileLen}}</span>
                        <span>{{tipMsg[1]}}</span>
                        <span>{{tipMsg[2]}}</span>
                    </div>
                    <template slot="fileList" slot-scope="scope">
                        <div class="import-file" :title="scope.name + (scope.size/1024)+'KB'">
                            <i class="el-icon-paperclip"></i>
                            {{ `${scope.name} (${scope.size/1024})KB` }}
                        </div>
                    </template> -->
                </dg-upload>
            <!-- </el-form-item>
        </el-form> -->
    </div>
</template>

<script>
import {mapGetters} from "vuex"
export default {
    name: "importV",
    mixins:[],
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        fileLen(){
            return this.fileList.length;
        }
    },
    props: {
        
    },
    data() {
        return {
            loading : false,
            tableData : [],
            tableHead : [],
            logQuery : "查看日志",
            chooseFile: "选取文件",
            fileList: [],
            tipMsg : ["共上传", "个文件,点击相应文件可查看详情","支持xls,xlsx类型的文件"],
            limit : 1 ,

        }
    },
    methods: {
        logView(){
            this.$emit("logView");
        },
        async downLoad(){
            let msg = await businessMetaApi.downLoad({moduleId : this.moduleData.id, templateType: "1" });
            if (msg) {
                this.$message.success("下载成功");
            }
        },
        // showPreview(tableData , tableHead , file){

        //     this.tableData = tableData;
        //     this.tableHead = tableHead;
        // },
        handlePreview(file) {
            this.importFileData(file.raw);
        },
        async importFileData(row){
            const vm = this;
            const reader = new FileReader();
            reader.readAsArrayBuffer(row);
            reader.onload = await function (){
                let tableData = [] , tableHead = [];
                const buffer = reader.result;
                const bytes = new Uint8Array(buffer);
                const length = bytes.byteLength;
                let binary = '';
                for (let i = 0; i < length; i++) {
                    binary += String.fromCharCode(bytes[i]);
                }
                const XLSX = require('xlsx');
                const wb = XLSX.read(binary, {
                    type: 'binary'
                });
                const outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
                tableData = [...outdata];
                let head = {};
                tableData.map((item) => {
                    for(let k in item){
                        if(!head[k]){
                            tableHead.push({
                                prop : k ,
                                label: k ,
                                minWidth : '100px'
                            })
                            head[k] = item[k];
                        }
                    }
                });
                if(!vm.isBusinessMete) vm.showPreview(tableData , tableHead , row);
            }
        },
        handleRemove() {
            this.tableData = [];
            this.tableHead = [];
        },
        fileChange(file , fileList){
            const isLt2M = file.size / 1024 / 1024 < 10;     //这里做文件大小限制
            if(!isLt2M) {
                this.$message({
                    message: '上传文件大小不能超过 100MB!',
                    type: 'warning'
                });
                this.fileList = [];
                return;
            }
            if(fileList.length > this.limit){
                fileList.splice(0, 1);
            }else {
                this.fileList = fileList;
            }
            // this.handlePreview(file);
            // this.loading = true;
            this.excelAnalysis(this.type, file);
        },
        /**
         * 业务元 + 论元解析excel文件表格
         */
        excelAnalysis(type, file){
            const vm = this;
            let services = this.$services("serviceImport");
            let format = new window.FormData();
            vm.loading = true;
            format.append("file",file.raw);
            services.parseDataCenterServiceModel({file : format, settings :{loading : vm.loading}}).then(res =>{
                vm.loading = false;
                vm.showPreview(res.data.data);
            })
        },
        showPreview(res){
            const vm=this;
            let layer = vm.$dgLayer({
                title : "导入预览" ,
                content : require("./preview/index.vue"),
                maxmin:false,
                move:false,
                props : {
                    res : res,
                },
                area : ["804px" , "620px"],
                on : {
                    submit(result){
                        vm.importServices(result,  res.importType);
                    }
                }
            })
        },
        importServices(result, importType) {
            const vm = this, {settings} = this;
            let services = this.$services("modeling");
            vm.loading = true;
            services.importDataCenterModel({filterIds : result, filterType :importType,  userId : this.userInfo.id, settings}).then(res => {
                vm.loading = false;
                if (res.data.status === 0) { 
                    this.$message.success('导入成功');
                    vm.showImportResult(res.data.data)
                }
            })
        },
        showImportResult(res) {
            const vm=this;
            let layer = vm.$dgLayer({
                title : "导入结果" ,
                content : require("./result.vue"),
                maxmin:false,
                move:false,
                props : {
                    res
                },
                area : ["804px" , "620px"],
                on : {
                    submit(result){
                        vm.importServices(result);
                    }
                }
            })
        },
    }
}
</script>

<style scoped lang="less">
    .excelImport {
        /deep/.el-upload {
            width: 100%;
        }
        width: 100%;
    }
    .tip-red {
        color: #F56C6C;
        padding: 0 2px;
    }
    .import-file {
        color: rgba(0,0,0,.45);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:hover {
            color: #1890FF;
        }
    }
    .iconC {
        font-size: 82px;
        color: rgba(0,0,0,.45);
    }
    .ShareItem {
        // border: 1px solid rgba(0,0,0,0.09);
        display: flex;
        flex-direction: column;
        margin: 0 30px 0 20px;
        justify-content: center;
        height: 340px;
        width: calc(100% - 50px);
        border-radius: 2px;
        cursor: pointer;
        &:hover{
            border: 1px solid #0088FF;
            box-shadow: 0 5px 10px 0 rgba(24,144,255,0.30);
        }
    }
</style>
