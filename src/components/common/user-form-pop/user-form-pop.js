import {treeMethods} from "@/api/treeNameCheck/treeName"
export default {
    name: "user-form-pop",
    inheritAttrs: false,
    props: {
        formData: {
            type: Object,
            default() {
                return {};
            }
        },
        title:{
            type: String,
            default: ''
        },
        label : String ,
        state : String,
        functionCode : String,
    },
    computed:{
        data(){
            return this.formData;
        }
    },
    data(){
        return {
            rules : {
                objName :[
                    {required :true , message: "名称不能为空" , trigger : ["blur" , "change"]} ,
                    { validator : this.checkedCommon , trigger : "blur" , reg : /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/ , message : "请输入中文、字母、数字、下划线" }
                ],
                fromAuthObj : [
                    {required : true , message : "所属上级不能为空" , trigger : 'change'}
                ]
            },
            defaultProps :{
                value: 'id',
                label: 'name',
                children: 'children'
            },
            showF : false
        }
    },
    methods:{
        filterNode : treeMethods.methods.filterNode ,
        validate(...arg){
            this.$refs.form.validate(...arg);
        },
        onCancel(){
            this.$emit('update:visible', false)
        },
        onSubmit(){
            const vm = this;
            vm.validate(valid =>{
                if(valid){
                    vm.$emit('save',this.data);
                }
            })

        },
        closed(){
            this.showF = false;
        },
        reNewData(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.form.clearValidate();
                vm.showF = true;
            })

        },
        update(){
            const vm = this;
            vm.validate(valid =>{
                if(valid){
                    vm.$emit('update',this.data);
                }
            })
        }
    }
};
