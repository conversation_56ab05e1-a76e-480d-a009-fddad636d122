<!--
    linxp
    2021.06.09
 -->
<template>
    <div class="dataset-panel">
        <div class="tree-type">
            <dg-select v-model="treeType" :data="typeOpt" @change="treeTypeChange"></dg-select>
        </div>
        <div class="tree-content">
            <dg-tree
                :data="data"
                node-key="id"
                default-props="defaultProps"
                :filter-node-method="filterNode"
                ref="tree"
                :highlight-current="true"
                :default-expanded-keys="expandData"
        >
            <span class="custom-tree-node" slot-scope="{ node, data }">
                <template v-if="!data.belongType">
                    <div class="custom-tree-node__file">
                         <div class="custom-tree-node__name">
                    <i
                            :class="{
                            'el-icon-folder' : !node.expanded,
                            'el-icon-folder-opened' : node.expanded
                          }"
                    ></i>
                    <span :title="data.label">{{ data.label }}</span>
                      </div>
                        <!--  <div>({{data.children.length}})</div> -->
                    </div>
                </template>
                <template v-else>
                    <div class="custom-tree-node__leaf"
                         draggable="true"
                         @dblclick="nodeDblClick(data)"
                         @dragend="dragend()"
                         @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data  })"
                    >
                      <!-- <svg class="eda_icon" >
                            <use xlink:href="#iconshuju_huaban"></use>
                        </svg>-->
                        <span :title="getSourceTitle(data)">{{ data.label }}</span>
                    </div>
                </template>
            </span>
        </dg-tree>
        </div>
        <data-details ref="details">
            <template #button="{row}">
                <el-button type="primary" @click="showExportSetting(row)">{{ exportBtnTxt }}</el-button>
            </template>
        </data-details>
    </div>
</template>
<script>
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/dataset-manage/service-mixins/service-mixins";
import {sourceMixins} from "@/projects/DataCenter/views/modeling/dialog/newModel/model/function-detail/source-mixins";
import dataDetails from "@views/dataSpace/dataReady/dialog/dataDetails.vue";
import {DatasetMixins} from "@views/dataSpace/dataReady/dialog/dataset-tree-panel/dataset-mixins"
export default {
    name: "dataset-panel",
    mixins: [treeMethods, commonMixins, servicesMixins, sourceMixins,DatasetMixins],
    components: {
        dataDetails,
    },
    props: {
        loadParams: Object
    },
    watch: {
        data:{
            handler(){
                this.$nextTick(()=>{
                    if(this.filterText) this.filterTreeNode(this.filterText);
                })
            },
            deep: true
        }
    },
    data() {
        return {
            exportBtnTxt: "Excel导出",
            data: [],
            defaultProps: {
                children: "children",
                label: "label"
            },
        };
    },
    methods: {
        getExportLimit(){
            return this.$services("dataReady").getExportLimit().then(res => {
                if(res.data.code === 0) {
                    return res.data.data;
                }
            })
        },
        async showExportSetting(row) {
            let res = await this.getExportLimit();
            if(res === undefined) return;
            this.$dgLayer({
                title: "EXCEL导出",
                content: require("@views/dataSpace/dataReady/dialog/export-setting.vue"),
                move: false,
                maxmin: false,
                area: ["800px", "auto"],
                props: {
                    maxSize: res,
                    row: row
                },
            })
        },
        nodeDblClick(data) {
            this.$refs.details.show(data);
        },
        initTree() {
            this.treeTypeChange();
        },
        filterChildren(data) {
            const vm = this;
            return data.filter(child => {
                if (child.children && child.children.length) child.children = vm.filterChildren(child.children);
                return child.children && child.children.length || child.belongType;
            })
        },
        getDatasetByCatalog(){
            const vm = this, {loadParams, $services} = this;
            loadParams.loading = true;
            vm.data = [];
            $services("dataReady").queryDataSetTree(true, loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result && result.length) {
                        vm.data = vm.filterChildren(result[0].children);
                    }
                }
            });
        },
        getDatasetBySource(){
            const vm = this, {loadParams, $services} = this;
            loadParams.loading = true;
            vm.data = [];
            $services("dataReady").queryDataSetTreeByDsType({
                hasDataObj: true
            }, loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (result && result.length) {
                        vm.data = vm.filterChildren(result);
                        vm.setExpandData(vm.data,'expandData');
                    }
                }
            });
        },
        /**
         * 设置默认展开层级
         * @param {*} data
         * @param key
         */
        setExpandData(data, key) {
            this[key].push(...data.map(item => item.id))
        },
    }
};
</script>
<style lang="less" scoped src="../source-style.less"></style>
<style lang="less" scoped>
.dataset-panel {
    height: 100%;
}
.tree-type {
    padding:0 14px 10px;
}
.tree-content {
    height:calc(100% - 2rem - 10px);
    overflow: auto;
}
</style>
