<template>
    <div class="BusinessManage">
        <ce-list-table
                :border="false"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                @changePage="changePage"
                :filter-placeholder="filterPlaceholder"
        >
            <template slot="button">
                <el-button type="primary" @click="addData" >{{dataInput}}</el-button>
            </template>
        </ce-list-table>
    </div>
</template>

<script>

export default {
    name: "BusinessManage",
    data(){
        return {
            filterPlaceholder : "请输入中文名称搜索" ,
            dataInput:"excel导入",
            filterText : "" ,
            tHeadData:[
                {
                    prop: "internalIdentifier" ,
                    label : "内部标识符",
                    minWidth:"100px"
                },{
                    prop: "name" ,
                    label : "中文名称",
                    minWidth:"100px"
                },{
                    prop: "spelling" ,
                    label : "中文全拼",
                    minWidth:"100px"
                },{
                    prop: "identifier" ,
                    label : "标识符",
                    minWidth:"100px"
                },{
                    prop: "edition" ,
                    label : "版本",
                    minWidth:"100px"
                },{
                    prop: "synonymous" ,
                    label : "同义名称",
                    minWidth:"100px"
                },{
                    prop : "description",
                    label:"说明",
                    minWidth: "100px",
                },{
                    prop : "objWord",
                    label:"对象类词",
                    minWidth: "100px",
                },{
                    prop : "dataType",
                    label:"数据类型",
                    minWidth: "100px",
                },{
                    prop : "format",
                    label:"表示格式",
                    minWidth: "100px",
                },{
                    prop : "range",
                    label:"值域",
                    minWidth: "100px",
                },{
                    prop : "relation",
                    label:"关系",
                    minWidth: "100px",
                },{
                    prop : "unit",
                    label:"计量单位",
                    minWidth: "100px",
                },{
                    prop : "unitType",
                    label:"融合单位类型",
                    minWidth: "140px",
                },{
                    prop : "unitCode",
                    label:"融合单位数据元编码",
                    minWidth: "160px",
                },{
                    prop : "status",
                    label:"状态",
                    minWidth: "100px",
                },{
                    prop : "agency",
                    label:"提交机构",
                    minWidth: "100px",
                },{
                    prop : "date",
                    label:"批准日期",
                    minWidth: "100px",
                },{
                    prop : "memo",
                    label:"备注",
                    minWidth: "100px",
                }
            ],
            tableData:[]
        }
    },
    methods : {
        changePage(inx){

        },
        addData(){
            const vm = this;
            let layer = vm.$dgLayer({
                title : 'Excel导入',
                content : require('@views/business-space/dialog/baseSettings/components/excelImport.vue'),
                maxmin:false,
                move:false,
                area : ["640px" , "260px"],
                on : {
                    submit(list){

                    }
                }
            })
        }
    }
}
</script>

<style scoped>

</style>
