<template>
    <div>
        <ce-list-table
                ref="table"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                @changePage="changePage"
                :operate-icons="operateIcons"
                :filter-placeholder="filterPlaceholder"
        >
            <template slot="button">
                <el-button type="primary" @click="publish('publish')">{{ allTxt }}</el-button>
                <el-button type="primary" @click="publish('check')" >{{ importTxt }}</el-button>
                <el-button type="primary" @click="publish('check')" >{{ exportTxt }}</el-button>
            </template>
            <template slot="search">
                <div class="formPadding">
                    <dg-select v-model="style" :data="styleData" class="formPadding"></dg-select>
                    <dg-select v-model="result" :data="resultData" class="formPadding"></dg-select>
                    <el-date-picker
                        v-model="updateDate"
                        unlink-panels
                        type="datetimerange"
                        range-separator="至"
                        @change="tableChange()"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
                    
            </template>
        </ce-list-table>
    </div>
</template>
<script>
export default {
    name: "log",
    mixins:[],
    computed: {

    },
    props: {
        
    },
    data()  {
        return  {
            style : "model",
            styleData : [
                {
                    label : "模型",
                    value : "model",
                },{
                    label : "作业",
                    value : "work",
                }
            ],
            result : "success",
            resultData : [
                {
                    label : "成功",
                    value : "success",
                },{
                    label : "失败",
                    value : "failed",
                }
            ],
            filterPlaceholder : "请输入文件名称进行搜索",
            allTxt : "全部",
            importTxt : "导入",
            exportTxt : "导出",
            updateDate : "",//日期范围
            filterText: "",
            tableData : [{memo : '0'}],
            tHeadData: [
                {
                    prop: 'memo',
                    label: "版本描述",
                    minWidth: 120
                }, 
            ],
            operateIcons: [
                {
                    name: "详情",
                    clickFn: this.detailShow,
                    show: (row) => true,
                    condition: (right) => true
                },
            ]
        }
    },
    methods : {
        publish() {

        },
        tableChange() {

        },
        detailShow() {

        },
        changePage() {

        },
    }
}
</script>

<style scoped>
.formPadding {
    padding-right: 5px;
}
</style>