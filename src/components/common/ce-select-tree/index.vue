<template>
    <div class="dg-tree-select">
        <l-select
                v-bind="selectProp"
                v-model="val"
                reserve-keyword
                :tree-data="data"
                :scrollbar-class="scrollbarCls"
                @visible-change="handleSelectFilerClose"
                @remove-tag="handleRemoveTag"
                @clear="clearDeaulf"
        >
            <dg-tree
                    ref="tree"
                    v-bind="treeBind"
                    class="ell_tree_node"
                    node-key="id"
                    @check-change="ceCheckChange"
                    v-on="$listeners"
                    @current-change="currentChange"
                    @check="checkNode"
                    :data="treeData"
            >
            </dg-tree>
        </l-select>
    </div>
</template>

<script>
    import TreeDrop from "ui-component-v4/lib/tree-drop"
    import LOption from './option.vue';
    import {checkTree} from "@/api/commonMethods/check-tree-mixins";

    export default {
        name: "selectTree",
        extends: TreeDrop,
        mixins : [checkTree],
        methods: {
            /**
             * 渲染 option 到 tree
             * 节点添加title
             * */
            renderContent(h, {node, data, store}) {
                const {label, value, disabled} = this.defaultProp;
                return h(LOption, {
                    props: {
                        key: data[value],
                        label: data[label],
                        value: data[value],
                        disabled: data[disabled],
                        isHighLight: node.store.isHighLight,
                        filter: node.filter
                    },
                    attrs: {
                        title: data[label]
                    }
                });
            },
            /**
             * 单选事件
             * */
            currentChange(data, node) {
                this.$emit("singleSelected", data.id, node);
            },

        }
    }
</script>

<style scoped>

</style>