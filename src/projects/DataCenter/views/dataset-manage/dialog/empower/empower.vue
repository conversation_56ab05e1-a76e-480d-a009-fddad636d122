<template>
    <div class="empower" v-loading="settings.loading">
        <el-input
                class="mb8"
                placeholder="请输入名称搜索"
                suffix-icon="el-icon-search"
                v-model.trim="filterText"
        ></el-input>
        <dg-tree
                v-model="val"
                node-key="id"
                class="filter-tree"
                :data="treeData"
                :props="defaultProps"
                :filter-node-method="filterNode"
                :show-checkbox="multiple"
                ref="tree"
                :check-on-click-node="true"
                :expand-on-click-node="false"
                :default-expand-all="false"
                @check-change="ceCheckChange"
                @check="checkNode"
        >
              <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                        <span class="node-label"
                              :title="data.label"
                        >{{ data.label }}</span>
                 </span>
        </dg-tree>
  <!--      <div class="empower-box-footer">
            <el-form class="ce-form__m0" label-width="90px" label-position="right">
                <el-form-item :label="labelP + ':'" >
                    <ce-select-drop
                            class="expect-select"
                            ref="pos_tree"
                            :props="defaultProps"
                            filterable
                            :tree-props="treeBind"
                            :filterNodeMethod="filterNode"
                            v-model="savePosition"
                            :data="savePositionDatas"></ce-select-drop>
                </el-form-item>
            </el-form>
        </div>-->
    </div>
</template>

<script src="./empower.js"></script>
<style scoped lang="less" src="./empower.less"></style>