<template>
    <div class="FieldFilteringPlugin">
       <!-- <el-form style="width: 100%;" label-position="top">
            <el-form-item>
                <div slot="label"> <span style="color: red"> * </span>选择输出列</div>
                <el-button style="width:100%;" type="primary" @click="show" v-if="realMultipleColumn.length">已选择{{realMultipleColumn.length}}个字段</el-button>
                <el-button style="width:100%;" type="primary" @click="show" v-else>选择输出列</el-button>
            </el-form-item>
        </el-form>
        <common-dialog
                custom-class=""
                :width="width"
                title="选择输出列"
                :visible.sync="visible"
                @closed="closeEditDialog"
                v-loading="loading"
        >-->
            <!--参数配置弹窗内容页-->
            <el-transfer
                    filterable
                    :titles="['源字段列表', '已选字段列表']"
                    v-model="multipleSelection"
                    :data="tableData">
            </el-transfer>
        <!--    &lt;!&ndash; 弹窗按钮&ndash;&gt;
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveEditDialog">确 定</el-button>
                <el-button @click="closeEditDialog">取 消</el-button>
            </span>

        </common-dialog>-->
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    export default {
        name: "FieldFilteringPlugin",
        mixins : [pluginMixins , servicesMixins],
        components: {preView},
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return{
                width:"725px",
                loadData:false ,
                loading:false,
                inputColumn:[],
                tableData: [],
                multipleSelection: [],
                realMultipleColumn:[],
            }
        },
        methods:{
            save(...args){
              this.saveFn(...args);
            },
            preview(){
              this.previewFn();
            },
            saveEditDialog(){
                if (this.multipleSelection.length < 1){
                    this.$message.warning("请配置要过滤的字段！");
                    return;
                }
                this.realMultipleColumn = [];
                this.multipleSelection.forEach(item => {
                    this.inputColumn.forEach(item1 => {
                       if (item === item1.dataColumnId){
                           this.realMultipleColumn.push({
                               columnName: item1.columnName,
                               columnCode:item1.columnCode,
                               columnId:item1.dataColumnId,
                               length:item1.length,
                               type:item1.columnType,
                               precision:item1.precsn,
                               columnZhName:item1.columnZhName
                           });
                       }
                    });
                });

                this.close();
            },
            closeEditDialog(){
                this.multipleSelection = [];
                this.realMultipleColumn.forEach(item => {
                    this.multipleSelection.push(item.columnId);
                });
                this.clear_data();
                this.close();
            },
            init(){
                this.stepId = this.sourceData.id;
                const vm = this, {pluginServices, pluginMock,loadParams} = this;
                //loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                services.queryFieldFilteringData(this.sourceData.id).then(res => {
                    /*   this.$axios.get("cicada/fieldfiltering/queryData?tranStepId=" + this.stepId)
                           .then(res => {*/
                    if(res.data.status === 0){
                            vm.inputColumn = res.data.data.inputColumn;
                            res.data.data.inputColumn.forEach(item => {

                                    let object = {
                                        label: item.columnZhName||item.columnName,
                                        key: item.dataColumnId,
                                        attribute: item
                                    };
                                    vm.tableData.push(object);
                            })
                            let itemMultiCol = [];
                            let itemColumn = [];
                            if (res.data.data.fieldFilteringMeta.cicadaFieldFilteringOutPuts.length > 0){
                                res.data.data.fieldFilteringMeta.cicadaFieldFilteringOutPuts.forEach(item => {
                                    itemMultiCol.push({
                                        columnName: item.columnName,
                                        columnCode:item.columnCode,
                                        columnId:item.columnId,
                                        length:item.length,
                                        type:item.type,
                                        precision:item.precision,
                                        columnZhName:item.columnZhName
                                    });
                                    vm.multipleSelection.push(item.columnId);
                                });
                            }
                            //let itemColumn = vm.multipleSelection;
                            vm.multipleSelection = [];
                            vm.inputColumn.forEach(item => {
                                itemMultiCol.forEach(item1 => {
                                    if (item.columnName === item1.columnName){
                                        vm.multipleSelection.push(item.dataColumnId);
                                        vm.realMultipleColumn.push({
                                            columnName: item1.columnName,
                                            columnCode:item1.columnCode,
                                            columnId:item.dataColumnId,
                                            length:item1.length,
                                            type:item1.type,
                                            precision:item1.precision,
                                            columnZhName:item1.columnZhName
                                        })
                                    }
                                })
                            });
                        }
                        loadParams.loading = false;
                    })
            },
            getFields(node){
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                //填充表格前先清空，防止再点击一次按钮，表格一直叠加数据
                vm.tableData=[];
                services.getFieldFilteringFields(node.id).then(res => {
                    if(res.data.status === 0){

                        if(res.data.data.inputColumn.length===0){
                            this.$message.error("获取字段为空！");
                        }
                        res.data.data.inputColumn.forEach(item => {

                            vm.tableData.push({
                                columnName: item.columnName,
                                columnZhName:item.columnZhName,
                                dataColumnId:item.dataColumnId,
                                length:item.length,
                                columnType:item.columnType,
                                metaColumnId:item.metaColumnId,
                                precsn:item.precsn
                            })
                        })

                    }
                })
            },
            saveFn(saveName) {
                if (this.multipleSelection.length < 1){
                    this.$message.warning("请配置要过滤的字段！");
                    return;
                }
                this.realMultipleColumn = [];
                this.multipleSelection.forEach(item => {
                    this.inputColumn.forEach(item1 => {
                        if (item === item1.dataColumnId){
                            this.realMultipleColumn.push({
                                columnName: item1.columnName,
                                columnCode:item1.columnCode,
                                columnId:item1.dataColumnId,
                                length:item1.length,
                                type:item1.columnType,
                                precision:item1.precsn,
                                columnZhName:item1.columnZhName
                            });
                        }
                    });
                });
                const vm = this, {pluginServices, pluginMock,loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                /* services.saveFieldFilteringFlugin(this.sourceData.id,vm.tableData).then(res => {

                 })*/
                loadParams.loading = true;
                let pluginMeta={};
                pluginMeta['cicadaFieldFilteringOutPuts']=vm.realMultipleColumn;
                services.saveFieldFiltering(this.sourceData.id,pluginMeta,loadParams).then(res => {
                //this.$axios.post("cicada/fieldfiltering/savePlugin?tranStepId=" + this.sourceData.id, pluginMeta).then(res => {
                    if (res.data.status === 0) {
                        this.$message.success("保存成功");
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                    } else{
                        this.$message.error("保存失败！");
                    }
                    //loadParams.loading = false;
                })
            },
            deleteFields() {
                let  multData = this.multipleSelection;
                let  tableData =this.tableData;
                let  multDataLen = multData.length;
                let  tableDataLen = tableData.length;
                if(multDataLen===0){
                    this.$message.success("请选择要过滤的字段！");
                    return;
                }
                for(let i = 0; i < multDataLen ;i++){
                    for(let y=0;y < tableDataLen;y++){
                        if(JSON.stringify(tableData[y]) == JSON.stringify(multData[i])){  //判断是否相等，相等就删除
                            this.tableData.splice(y,1)
                            console.log("aa")
                        }
                    }
                }
            },

            previewFn() {
                this.$refs.preview.show(this.rowData , this.sourceData);
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
        },
        created() {
            this.init();
        }

    }
</script>

<style lang="less" scoped>
.FieldFilteringPlugin {
    height: 100%;

}
    /deep/ .el-transfer {
        height:100%;
        background: #fff;
    &-panel {
         height: 100%;
         width:calc(50% - 80px);
         background: #fff;

    &__header {
         background: fade(#1890ff, 20%);
     }
    &__body {
         height: calc(100% - 51px);
         border-top: 1px solid transparent;
     }
    &__list {
         height: calc(100% - 56px);
         box-sizing: border-box;
     }
    }

    &__button {
        width: 100px;
         height: 24px;
         line-height: 22px;
         text-align: center;
         padding: 0;
         min-width: auto;
         display: block;
         margin-left:0;
     }
    }


    .tree-transfer {
    /deep/ .el-transfer {
    &-panel {
         height: 100%;
         box-sizing: border-box;

    &__body {
         height: calc(100% - 40px);
         box-sizing: border-box;
     }

    &__list {
         height: 100%;
         overflow: hidden;
     }
    }
    }

    /deep/ .el-tree {
        height: 386px;
        overflow-y: auto;
    }
    }
</style>
