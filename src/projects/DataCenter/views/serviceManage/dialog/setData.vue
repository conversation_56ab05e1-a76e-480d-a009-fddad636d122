<template>
    <dg-dialog title="设置数据"
                   :visible.sync="visibleSetData"
                   :append-to-body="true"
               :close-on-click-modal="false"
                   :width="'70%'"
                    :top="'10vh'"
                    class="setData"
        >
            <div  class="height100">
                <el-form
                        ref="setDataForm"
                        class="height100"
                        :model="{ list: setDataList }"
                        @submit.native.prevent>
                    <common-table
                            ref="setTableTable"
                            height="calc(75vh - 154px)"
                            :pagination="false"
                            :show0verflowTooltip='false'
                            :data="setDataList"
                            :draggable="false"
                            :columns="[...setDataHead, operate]"
                    >
                        <template slot="header-operate" slot-scope="{row , $index}">
                            <el-button v-for="(btn , i) in operateIcon.head"
                                       :key="i"
                                       :title="btn.title"
                                       :class="btn.icon" type="text"
                                       class="dg-form-column__button" @click="btn.clickFn"></el-button>
                        </template>
                        <template slot="operate" slot-scope="{row , $index}">
                            <el-form-item>
                             <span v-for="(btn , i) in operateIcon.body"
                                   :key="i">
                                 <el-button type="text"
                                            slot="reference"
                                            class="dg-form-column__button"
                                            :title="btn.title"
                                            @click="btn.clickFn(row,$index)"
                                 >
                                        {{ btn.title }}
                                 </el-button>
                                </span>
                            </el-form-item>
                        </template>
                        <template v-for="(item ,inx) in setDataHead" :slot="item.prop" slot-scope="{row , $index}"  >
                            <el-form-item :prop="`list.${$index}[${item.prop}]`" :key="inx" :rules="rules[item.prop]">
                                <el-input v-model.number="row[item.prop]" @input="forceUpdate" v-if="item.dataType === 'Long'" type="number" onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"></el-input>
                                <el-input v-model.number="row[item.prop]" @input="forceUpdate" v-if="item.dataType === 'Double'" type="number"></el-input>
                                <!--<el-input-number v-model.number="row[item.prop]" @input="forceUpdate" :controls="false" v-if="item.dataType === 'Double'" :precision="2" type="number" :step="0.1" :min="0"></el-input-number>-->
                                <el-checkbox v-model="row[item.prop]" v-if="item.dataType === 'Boolean'">{{row[item.prop]}}</el-checkbox>
                                <el-input v-model="row[item.prop]" @input="forceUpdate" v-if="item.dataType === 'String'"></el-input>
                            </el-form-item>
                        </template>

                        <template :slot="'header-'+item.prop" slot-scope="{row , $index}" v-for="(item ,inx) in setDataHead" >
                            <span :key="inx">{{item.label}}<span v-if="isMustList.includes(item.prop)" class="red">*</span></span>
                        </template>
                    </common-table>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <dg-button @click="visibleSetData = false;">取 消</dg-button>
                <dg-button type="primary" @click="setDataSave">确 定</dg-button>
            </span>
        </dg-dialog>

</template>

<script>
    import {coustTableH} from "@/api/commonMethods/count-table-h";
    export default {
        name: "setData",
        mixins : [coustTableH],
        data() {
            return {
                visibleSetData: false,
                setDataHead: [],
                setDataList: [],
                isMustList: [],
                rules: {},
                operateIcon: {
                    head: [
                        {
                            title: "新增",
                            icon: "el-icon-circle-plus",
                            clickFn: this.addTestColumn
                        }
                    ],
                    body: [
                        {title: "删除", type: "delete", icon: 'el-icon-delete', clickFn: this.deleteTestColumn},
                    ]
                },
                operate: {
                    prop: "operate", align: 'center', width:"90",fixed:"right"
                },
                addColumnInfo: [],
                longList: ["BYTE","SHORT","INTEGER","BIGINTEGER","LONG"],
                doubleList: ["FLOAT","BIGDECIMAL","DOUBLE"],
            }
        },
        methods:{
            show(row){
                this.visibleSetData = true;
                this.setData(row);
            },
            forceUpdate(){
                this.$forceUpdate()
            },
            setDataType(n){
                if(this.longList.includes(n.toLocaleUpperCase())){
                    return 'Long';
                }
                else if(this.doubleList.includes(n.toLocaleUpperCase())){
                    return 'Double';
                }
                else if(n.toLocaleUpperCase() === "BOOLEAN"){
                    return 'Boolean';
                }
                else{
                    return 'String';
                }
            },
            setData(row){
                const vm = this;
                vm.selectTable = row;
                vm.setDataHead = [];
                vm.setDataList = [];
                vm.setDataList = [...row.setDataList];
                row.childrenCopy.forEach(n=>{
                    let type = n.type ? vm.setDataType(n.type) : 'String';
                    let info = {
                        prop : n.paramCode,
                        label : n.memo || n.paramName || n.paramCode,
                        minWidth: n.paramName && n.paramName.length && n.paramName.length * 20 > 100 ? n.paramName.length * 20 : '100',
                        dataType: type
                    }
                    vm.setDataHead.push(info);
                    if(n.isMust === 't'){
                        vm.isMustList.push(info.prop);
                        vm.$nextTick(()=>{
                            vm.rules[info.prop] = [{ required: true, message: `${info.label}不能为空`, trigger: ['blur', 'change'] }];
                        })
                    }
                })
                vm.addColumnInfo = {}
                vm.setDataHead.forEach(n=>{
                    vm.addColumnInfo[n.prop] = n.dataType === 'Boolean' ? true : null;
                })
                vm.$nextTick(()=>{
                    vm.$refs.setTableTable.$refs.table.doLayout();
                })
            },
            /**
             * 添加字段列
             * */
            addTestColumn() {
                this.setDataList.push(JSON.parse(JSON.stringify(this.addColumnInfo)) );
                this.visibleSetData = true;
            },
            deleteTestColumn(row, inx){
                this.setDataList.splice(inx, 1);
            },
            //添加数据确认
            setDataSave(){
                const vm = this;
                if (vm.isMustList.length!==0 && vm.setDataList.length===0) {
                    this.$message.warning('必填项请填写完整');
                    return;
                }
                let isValidate = true;
                vm.isMustList.forEach(item =>{
                    if(vm.setDataList.find(e => e[item] === '')) isValidate = false;
                })
                if (!isValidate) {
                    this.$message.warning('必填项请填写完整');
                    return;
                }
                vm.$refs['setDataForm'].validate((valid, err) => {
                    if (valid) {
                        vm.selectTable.setDataList = [];
                        vm.setDataList && vm.setDataList.length && vm.setDataList.forEach(p=>{
                            if(Object.keys(p).length !== 0){
                                vm.selectTable.setDataList.push(p)
                            }
                        })
                        vm.visibleSetData = false;
                    } else {
                        vm.visibleSetData = true;
                    }
                });
            }
        }
    }
</script>

<style scoped lang="less">
    .setData{
        .red{
            color: red;
        }
    }


</style>
