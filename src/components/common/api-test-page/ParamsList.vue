<template>
    <div class="params-list" v-loading="showFooter? loadParams.loading : false">
        <el-form label-position="right" label-width="100px" >
            <el-form-item v-for="(item , k) in formList"
                          :key="k"
                          :label="`${item.label}:`"
                          label-position="top"
            >
                <div class="ell" :title="form[k]" v-if="item.type === 'text'" v-html="form[k]"></div>
                <dg-select class="api-left-select" v-if="item.type === 'select'"
                           v-model="form[k]"
                           :data="getInterOption()"
                           :placeholder="item.placeholder"
                           @change="interChange"
                ></dg-select>
            </el-form-item>
        </el-form>
        <div class="api-params-list">
            <common-table
                    :data="requestTableData"
                    :columns="requestTHeadData"
                    :pagination="false"
                    class="width100"
                    height="100%"
                    row-key="paramName"
                    default-expand-all
            >
                <template slot="paramName" slot-scope="scope">
                        <span :class="{'is-require':scope.row.isMust ==='t' || scope.row.is_must==='t'}">
                            {{ scope.row.paramName }}
                        </span>
                </template>
                <template slot="defaultValue" slot-scope="scope">
                    <dg-button v-if="scope.row.type === 'List' && scope.row.paramCode !== 'params'"
                               @click="setData(scope.row)">{{setDataTxt}}</dg-button>
                    <el-button v-else-if="scope.row.batchQuery" @click="setData(scope.row)">{{setDataTxt}}</el-button>
                    <div v-else>
                        <el-date-picker
                                type="date"
                                v-model="scope.row.defaultValue"
                                v-if="scope.row.type === 'Time' && scope.row.paramCode ==='startTime'"
                                value-format="yyyy-MM-dd"
                                :picker-options="pickerOptionsStart"
                                @change="changeStartTime"
                                placeholder="请选择开始时间"
                        >
                        </el-date-picker>
                        <el-date-picker
                                type="date"
                                v-model="scope.row.defaultValue"
                                v-else-if="scope.row.type === 'Time' && scope.row.paramCode ==='endTime'"
                                value-format="yyyy-MM-dd"
                                :picker-options="pickerOptionsEnd"
                                placeholder="请选择结束时间"
                        >
                        </el-date-picker>
                        <el-input  v-model="scope.row.defaultValue" :placeholder="placeholder_p"
                                   @input="changeInput($event)" v-else-if="scope.row.showInput"></el-input>
                    </div>
                </template>
            </common-table>
        </div>
        <dialogFooterBtn v-if="showFooter" v-footer :data="btnGroup" />
        <setData ref="setData"></setData>
    </div>
</template>

<script>
import setData from "@/projects/DataCenter/views/serviceManage/dialog/setData";
import {RequestDetail} from "./requestDetail";
import dialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn";
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
export default {
    name: "ParamsList",
    mixins : [RequestDetail ,commonMixins],
    components : {
        setData,
        dialogFooterBtn,
    },
    props : {
        apiRow : Object ,
        loadParams : {
            type : Object,
            default :()=>{
                return {loading : true}
            }
        } ,
        showFooter : {
            type : Boolean ,
            default : false
        },
        row : Object, //用例管理设置参数页面--- rowData
    },
    watch : {
        loadParams:{
            handler(val){
                this.$emit("loadChange" , val)
            },
            deep : true,
        }
    },
    data(){
        return {
            btnGroup : [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.submit,
                    disabledFn : ()=> this.loadParams.loading,
                    show: () => true
                },
            ],
            setDataTxt : '设置数据',
            pickerOptionsStart:{   //禁用当前日期之前的日期
                disabledDate(time) {
                    //Date.now()是javascript中的内置函数，它返回自1970年1月1日00:00:00 UTC以来经过的毫秒数。
                    return time.getTime() < Date.now() - 8.64e7;
                },
            },
            pickerOptionsEnd:{   //禁用当前日期之前的日期
                disabledDate:(time)=>{
                    let startTime =  (this.requestTableData.find(n=>n.paramCode == "startTime") || {}).defaultValue;
                    let endTime = startTime ? new Date(startTime).getTime() : '';
                    if(endTime){
                        return (time.getTime()) <= endTime;
                    }
                    else{
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                },
            },
        }
    },
    methods : {
        changeStartTime(val){
            let time = this.requestTableData.find(n=>n.paramCode === "endTime");
            let endTime =  new Date(time.defaultValue).getTime();
            if(new Date(val).getTime() >= endTime){
                time.defaultValue = '';
            }
        },
        changeInput(e) {
            this.$forceUpdate()
        },
        //模型分析中设置数据
        setData(row) {
            this.$refs.setData.show(row)
        },
        submit(){
            let testVo = this.setTestVo(true);
            if(!testVo) return;
            this.$emit("setTestVo" , testVo , this.requestTableData , this.form , this.useCaseApiParams);
        }
    }
}
</script>

<style scoped>
    .params-list {
        height: 100%;
    }
</style>
<style scoped lang="less" src="./testPage.less"></style>
