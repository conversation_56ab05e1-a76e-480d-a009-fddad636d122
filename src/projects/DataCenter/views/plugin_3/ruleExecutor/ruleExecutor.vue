<template>
    <div class="height100">
        <rulePage :ruleData="ruleData" :ruleCode="ruleCode" :rowData="rowData" :groupOpt="groupOpt" v-if="showRulePage"></rulePage>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import rulePage from './RulePage'
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    export default {
        name: "ruleExecutor",
        components: {
            rulePage,
            preView
        },
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data() {
            return {
                ruleData: [],
                showRulePage: false,
                groupOpt: [],
                ruleCode: '',
            }
        },
        methods: {
            createGroupList(){
                return this.groupOpt.map(li => {
                    return {
                        ...li,
                        show: true,
                    };
                });
            },
            getExpression(item) {
                return item.ruleOperateMappingType === "CalculationExpression" ? item.calculationExpression : "";
            },
            init() {
                const vm = this, {loadParams} = this;
                let services = vm.$services("servicesServices");
                loadParams.loading = true;
                services.ruleExectorDetailView(vm.sourceData.id,  loadParams).then((res) => {
                    vm.ruleData = [];
                    vm.groupOpt = [];
                    let result = res.data.data;
                    let isConflict = false;

                    vm.ruleCode = result.transCode;
                    result.pluginMeta.forEach(n=>{
                        if(!vm.groupOpt.find(k=>k.value === n.groupById)){
                            vm.groupOpt.push({label: n.groupByName, value: n.groupById})
                        }
                    })

                    //遍历 展现折叠使用
                    let groupList = vm.createGroupList();
                    result.pluginMeta.forEach(element => {
                        let hasChange = element.hasArgument === '1' && element.exactInputParameter.extractMappingType === 'simpleMapping'
                        element.inputSingletonParameters.forEach(i =>{
                            i.outFieldCode = i.columnCode;
                            i.hasChange = hasChange;
                        })
                        vm.funcCode = element.serverExp;
                        if(element.isConflict) isConflict = true;

                        let initInputFields = [{
                            columnName: '无需入参',
                            featureName: '空',
                            columnCode: '',
                            isInput: true,
                            hasChange: false,
                        }]
                        if(vm.ruleCode.includes('AddFeature') && element.hasArgument === '1'){
                            initInputFields = element.inputSingletonParameters;
                        }

                        let inputFields = vm.ruleCode.includes('AddFeature') ? initInputFields : element.inputSingletonParameters;
                        let outputFields = vm.ruleCode.includes('Extract') ? element.outSingletonParameters.filter(n=> n.isOutput) : element.outSingletonParameters;

                        let arr = inputFields.length > outputFields.length ? inputFields : outputFields;
                        let fieldsList = [];
                        arr.forEach((n,i)=>{
                            fieldsList.push({inputLength: inputFields.length, inputFields: inputFields[i] ? {...inputFields[i]} : {}, outputFields: {...outputFields[i]}})
                        })
                        let calculationExpression = vm.ruleCode.includes('AddFeature') ? element.calculationExpression : vm.getExpression(element);
                        let groupId = vm.ruleCode.includes('AddFeature') ? element.operateDataSetName : function (li) {
                            return `${li.sourceRuleId}_${li.groupById}_${li.operateDataSetName}`
                        }(element);

                        let groupItem = vm.ruleData.find(it => it.id === groupId);
                        let mapField = {
                            mapId: element.id,
                            isConflict: element.isConflict,
                            isRebuildParam: element.isRebuildParam,
                            calculationExpression,
                            rule: element.sourceRuleName,
                            fieldsList
                        };
                        if (groupItem) {
                            vm.ruleData.forEach(e =>{
                                if (e.id === groupId) {
                                    groupItem.mapFields.push(mapField)
                                }
                            })
                        }else {
                            vm.ruleData.push({
                                id: groupId,
                                rule: element.sourceRuleName,
                                datasetName: element.operateDataSetName || "",
                                groupOpt: vm.groupOpt || [],
                                exec : element.exec,
                                groupByName : element.groupByName,
                                groupByCode: element.groupByCode,
                                mapFields :[mapField]
                            })
                        }
                    });
                    vm.ruleData.sort((a, b) => {
                        return (a.exec !== undefined && b.exec !== undefined) ? +b.exec - +a.exec : 1;
                    });
                    if(isConflict) vm.$message.error("存在重复的输出字段");
                    vm.showRulePage = true;
                });
            },
            preview() {
                const vm = this;
                vm.$nextTick(()=>{
                    vm.$refs.preview.show(this.rowData, this.sourceData);
                });
            },
        },
        mounted() {
            this.init();
        }
    }
</script>

<style scoped>

</style>
