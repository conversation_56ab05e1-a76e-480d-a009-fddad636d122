<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <el-popover placement="bottom-start"
                        ref="btnPop"
                        class="mr5"
                        width="300" :visible-arrow="false" trigger="click"
                        v-if="active === 'myData'&& !isShare && !isOtherUser && ifHasRight(addDataSetObj) && nodeId">
                <div class="actions">
                    <div
                        class="actions__item"
                        v-for="(item, idx) in actions"
                        :key="idx"
                        @click="addNewModel(item)"
                    >
                                    <span class="actions__item-icon">
                                        <i :style="{ color: item.color }" :class="['dg-iconp', item.icon]"></i>
                                    </span>
                        <div class="actions__item-info">
                            <div>{{ item.title }}</div>
                            <p>{{ item.desc }}</p>
                        </div>
                    </div>
                </div>
                <dg-button slot="reference" type="primary">{{addDataSourceTxt}}</dg-button>
            </el-popover>
            <el-button @click="batchAddFn" v-if="isCatalog && active === 'myData' && !isShare && !isOtherUser && ifHasRight(addDataSetObj) && nodeId">{{batchAddTxt}}</el-button>
            <!--<dg-button  type="primary" @click="handleNewModel(actions[0])" v-show="active === 'myData' && !isShare&& ifHasRight(addDataSetObj)">添加数据集</dg-button>-->
            <!--<dg-button type="primary" v-if=" active === 'myData'&&!isShare" @click="sqlAnalyse()">{{buttonTxt[0]}}</dg-button>-->
            <dg-button v-if="!isShare && !isOtherUser && ifHasRight(volumeLicensing)" @click="batchFn" >{{batchShareTxt}}</dg-button>
            <el-button @click="linkToShare">{{myShareTxt}}</el-button>
            <div class="data-contact__search">
                <el-row type="flex" :gutter="10">
                    <el-col :span="8">
                        <dg-select v-model="selectType" :data="selectData" @change="changePage(1)"> </dg-select>
                    </el-col>
                    <el-col :span="16">
                        <el-input
                                size="mini"
                                placeholder="请输入数据集名称搜索"
                                v-model.trim="inputValueTable"
                                v-input-limit:trim
                                @input="inputFilterSpecial($event , 'inputValueTable')"
                                @keyup.enter.native="searchTableEvent"
                        >
                            <i
                                    class="el-icon-search el-input__icon poi"
                                    slot="suffix"
                                    @click="searchTableEvent">
                            </i>
                        </el-input>
                    </el-col>
                </el-row>

            </div>
        </div>
        <div class="d-s-r-table" v-if="!settings.loading">
            <common-table
                :data="tableData"
                :columns="tHeadData"
                :paging-type="isMock ? 'client' : 'server'"
                :pagination-props="paginationProps"
                class="width100 ce-table-h__auto"
                :border="false"
                noneImg
                :max-height="tableBodyH"
                :pagination-total="total"
                @change-current="isMock ? ()=>{} : changePage($event) "
                @change-size="changeSize"
            >
                <template slot="name" slot-scope="{row }">
                    <div class="detailName poi" @click="detailShow(row)">{{ row.name }}</div>
                </template>
                <!--<template slot="db_type" slot-scope="{row }">-->
                    <!--<span>{{ row.db_type ? typeMap[row.db_type.toUpperCase()] :'' }}</span>-->
                <!--</template>-->
                <template slot="belong_type" slot-scope="{row }">
                    <span>{{ row.belong_type === 'PHYSICAL' ? '源数据集' : row.belong_type === 'SELF_HELP'? '自助数据集' : 'SQL数据集' }}</span>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                     <!--<span class="r-c-action"-->
                           <!--v-for="(item,index) in hasRightOptIcon"-->
                           <!--:key="index"-->
                     <!--&gt;-->
                        <!--<el-button :title="item.name" type="text" @click="item.clickFn( row , $index)">{{item.name}}</el-button>-->
                    <!--</span>-->
                    <span class="r-c-action"
                          v-for="(item,index) in hasRightOptIcon"
                          :key="index"
                          v-if="index < 3"
                    >
                        <el-button type="text"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.name"
                        >{{ item.name }}</el-button> <!--v-html="item.icon"-->
                    </span>
                    <dir-edit-action v-if="countHasRightMenu > 3"
                                     placement="bottom"
                                     @command="menuCommand($event , row , $index)" :data="hasRightOptIcon.slice(3)"
                                     :node="row"
                                     :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>更多</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
            </common-table>
        </div>
<!--        <new-model ref="sql_form" @showPanel="editSqlPage"></new-model>
        <sql-analyse ref="sql_panel" @closePlane="closeSqlPanel" v-if="showSqlPanel" :rowData="sqlRowData"></sql-analyse>-->
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>
