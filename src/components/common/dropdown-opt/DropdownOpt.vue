<template>
    <ul class="DropdownOpt selectn">
        <li v-for="(item , key) in data" :key="key" v-if="item.name"
            :class="{'active_li':activeKey === item.code}"
            @click.stop="changeVal(item)"
            @mouseenter="showOption($event , item , item.code )"
            @mouseleave="hideOption(item)">
            <span>{{item.name}}</span>
            <i class="el-icon-arrow-right ce-item_right" v-if="item.children"></i>
            <DropdownOpt :class="{'showOpt': showOpt}" :style="style" :value="activeKey" @changeVal="changeVal" @open="open" :can-checked="true" :data="option" v-if="showOpt === item.code && item.children" />
        </li>

    </ul>
</template>

<script>
    import * as $ from "jquery"
    export default {
        name: "DropdownOpt" ,
        inheritAttrs : true,
        props: {
            data : [Object , Array] ,
            canChecked : {
                type : <PERSON><PERSON>an ,
                default : false
            },
            value : {
                type : String ,
                default: ''
            }
        },
        data(){
            return {
                option : {} ,
                showOpt : '' ,
                left: 0 ,
                activeKey : this.value ,
                style : {}
            }
        },
        watch : {
            value(val){
                this.activeKey = val;
            }
        },
        methods : {
            showOption(e , item ,key){
                if(!item.children) return;
                let pageHeight = document.documentElement.clientHeight,
                    y = e.clientY,
                    h = 34 * Object.keys(item.children).length ,
                    pageWidth = document.documentElement.clientWidth;
                this.showOpt = key;
                this.option = item.children;
                this.$emit("open" , item);
                let n , left , l;
                l = $(e.currentTarget).parents(".el-popover").position().left;
                if(pageWidth - 300 > l){
                    left = "200px";
                }else {
                    left = "-100px"
                }
                if(pageHeight - y > h ){
                    this.style = {
                        top : '-5px' ,
                        left : left
                    };
                }else {
                    n = Math.floor(( pageHeight - y - h) / 34 ) * 34;
                    this.style = {
                        top : n + 'px' ,
                        left : left
                    };
                }

            },
            hideOption(item){
                this.showOpt = '';
            },
            changeVal(params ){
                this.$emit('changeVal' ,params);
            },
            open(params){
                this.$emit("open" , params);
            }
        }
    }
</script>

<style scoped>
    .DropdownOpt {
        padding:5px 0;
        position: relative;
        z-index: 10;
    }
    .DropdownOpt li {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 30px 0 20px;
        height: 34px;
        line-height: 34px;
        outline: none;
        color: #666;
        cursor: pointer;
        width:150px;
    }
    .DropdownOpt li:hover ,.DropdownOpt li.active_li {
        background: #F5F7FA;
        color: #409EFF;
    }
    .ce-item_right {
        position: absolute;
        right: 10px;
        font-size: 14px;
    }
    .showOpt {
        position: absolute;
        top: -5px;
        left: 200px;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        border-radius: 4px;
    }
</style>