import {commonMixins} from "@/api/commonMethods/common-mixins"
import empower from "./empower.vue"
import {servicesMixins} from "../../service-mixins/service-mixins";

export default {
    name: "empower-dialog",
    mixins : [commonMixins , servicesMixins],
    components : {empower},
    data(){
        return {
            title : "添加数据集" ,
            width :"360px" ,
            visible : false ,
            reNew : false,
            selectDataId:"",
        }
    },
    methods :{
        clear_data(){
            this.reNew = false;
        },
        show(selectDataId){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.selectDataId=selectDataId;
        },
        submit(){
            const vm = this , {datasetServices , datasetMock , settings} = this;
            let services = vm.getServices(datasetServices , datasetMock);
            let result = vm.$refs.empower.getAllDataObjsAndSite();
            //console.log("=================="+vm.selectDataId);
            if(result.ids.length === 0){
                vm.$message.warning("请选择数据对象");
                return;
            }
            settings.loading = true;
            services.accreditLogicDataObj(result.ids , vm.selectDataId , settings).then(res => {
                if(res.data.status === 0){
                    if(res.data.data === ''){
                        vm.$message.success("授权成功");
                    }else {
                        vm.$message.warning(res.data.data)
                    }
                    vm.$emit("reNewList" , vm.selectDataId);
                    vm.visible = false;
                }
            })
        }
    }
}