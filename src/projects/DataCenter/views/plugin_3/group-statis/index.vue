<!--
    linxp
    2021.06.17
 -->
<template>
    <div class="group-statis">
            <div class="group-statis__content">
                <multicondition-filter ref="multicondition" :loadParams="loadParams" :tableHeight="true" :sourceData="sourceData"  v-bind="$attrs" :rowData="rowData" v-on="$listeners" @getVo="getVo"></multicondition-filter>
            </div>
        <!-- <el-card style="margin-top: 14px;flex: 1" shadow="never" :body-style="{ padding: 0 }">
            <div class="group-statis__content">
                <div class="group-statis__wrap-table">
                    <div class="group-statis__wrap-header">
                        <div class="group-statis__wrap-actions">
                            <el-radio-group v-model="btnActive">
                                <el-radio-button label="预览数据"></el-radio-button>
                                <el-radio-button label="字段列表"></el-radio-button>
                            </el-radio-group>
                            <div class="group-statis__wrap-reflash"><i class="dg-iconp icon-refresh"></i></div>
                        </div>
                        <div class="group-statis__wrap-show">
                            <el-checkbox v-model="show">显示别名</el-checkbox>
                        </div>
                    </div>
                    <div class="group-statis__wrap-list">
                        <dg-table
                            :data="data"
                            border
                            row-key="id"
                            paging-type="client"
                            :pagination-props="{ pageSize: 5 }"
                        >
                            <dg-table-column type="index" width="55" label="#"></dg-table-column>
                            <dg-table-column prop="name" width="80" label="姓名"></dg-table-column>
                            <dg-table-column prop="idNo" label="身份证号"></dg-table-column>
                            <dg-table-column prop="birthday" label="出生日期"></dg-table-column>
                            <dg-table-column prop="sex" label="性别" width="80"></dg-table-column>
                            <dg-table-column prop="addr" label="户籍地"></dg-table-column>
                        </dg-table>
                    </div> -->
                    <!-- <div class="group-statis__wrap-list">
                        <el-table
                            :data="tableData"
                            style="width: 100%;margin-bottom: 20px;"
                            :span-method="arraySpanMethod"
                            row-key="id"
                            border
                            default-expand-all
                            :row-style="{height: '50px'}"
                            :cell-style="{padding: '0'}"
                            :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                                <el-table-column
                                    prop="column"
                                    label="已选字段">
                                    <template slot-scope="scope">
                                        <span  style="color:DeepSkyBlue;" v-if="scope.row.column === '分组' ||scope.row.column === '统计' ">{{scope.row.column}}</span>
                                        <span v-else>{{scope.row.column}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="type"
                                    label="操作类型">
                                </el-table-column>
                                <el-table-column
                                    prop="resultColumn"
                                    label="结果字段名">
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.resultColumn"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="resultName"
                                    label="结果字段别名"
                                    width="180">
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.resultName"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="dataType"
                                    label="数据类型">
                                </el-table-column>
                                <el-table-column
                                    prop="columnLength"
                                    label="字段长度">
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.columnLength"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="columnjingdu"
                                    label="字段精度">
                                </el-table-column>
                                <el-table-column label="操作">
                                    <template slot-scope="scope">
                                        <el-button
                                            size="mini"
                                            type="text"
                                            @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                        </el-table>
                    </div> -->
                <!-- </div>
            </div>
        </el-card> -->
        <pre-view ref="preview"></pre-view>
    </div>
</template>
<script>
import MulticonditionFilter from "./components/multicondition-filter";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins"
import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
export default {
    name: "",
    mixins: [commonMixins, servicesMixins],
    components: {
        MulticonditionFilter,
        PreView
    },
    props:{
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    data() {
        return {
            btnActive: "预览数据",
            data:[],
            options: [],
            show: false,
            saveVo:{},
            tableData: [{
                id: 3,
                column: '分组',
                children: [
                    {
                        id: 31,
                        column: '姓名',
                        type: '类型',
                        resultColumn: 'xm',
                        resultName: '姓名',
                        dataType:'字符型',
                        columnLength:32,
                        columnjingdu:"31",
                        parentId:"0"
                    },
                    {
                        id: 1,
                        column: '姓名',
                        type: '类型',
                        resultColumn: 'xm',
                        resultName: '姓名',
                        dataType:'字符型',
                        columnLength:32,
                        columnjingdu:"1",
                        parentId:"0"
                    },
                    {
                        id: 2,
                        column: '姓名',
                        type: '类型',
                        resultColumn: 'xm',
                        resultName: '姓名',
                        dataType:'字符型',
                        columnLength:32,
                        columnjingdu:"2",
                        parentId:"0"
                    },
                ]
            },{
                id: 4,
                column: '统计',
                children: [
                    {
                        id: 32,
                        column: '姓名',
                        type: '类型',
                        resultColumn: 'xm',
                        resultName: '姓名',
                        dataType:'字符型',
                        columnLength:32,
                        columnjingdu:"无",
                        parentId:"1"
                    },
                    {
                        id: 11,
                        column: '姓名',
                        type: '类型',
                        resultColumn: 'xm',
                        resultName: '姓名',
                        dataType:'字符型',
                        columnLength:32,
                        columnjingdu:"11",
                        parentId:"1"
                    },
                    {
                        id: 22,
                        column: '姓名',
                        type: '类型',
                        resultColumn: 'xm',
                        resultName: '姓名',
                        dataType:'字符型',
                        columnLength:32,
                        columnjingdu:"22",
                        parentId:"1"
                    },
                ]
            }],
        };
    },
    methods:{
        handleDelete(index, row) {
            if (row.parentId === '0') {
                this.tableData[0].children.splice(index-1,1);
            }else {
                let length = this.tableData[0].children.length;
                this.tableData[1].children.splice(index - 2 - length, 1);
            }
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            if (row.column === '分组' || row.column === '统计') {
                if (columnIndex === 0) {
                    return [1, 8];
                } else if (columnIndex === 1) {
                    return [0, 0];
                }
            }
        },
        getVo(value){
            this.saveVo = value;
        },
        saveFn(){
            this.$refs.multicondition.save();
        },
        save(saveName,showTip) { //保存后才能预览
            let value = this.saveVo;
            value.havingCondition =  this.$refs.multicondition.checked ? this.$refs.multicondition.groupFilterInfo : '';
            const vm = this, {pluginServices, pluginMock,} = this;
            if (this.saveVo.cicadaReduceFieldExps === undefined) {
                this.$message.warning("请配置分组统计信息");
                return;
            }
            if (this.saveVo.cicadaReduceFieldExps.length === 0 ) {
                this.$message.warning("请至少选择一个统计字段");
                return;
            }

            if (value.cicadaReduceFieldExps.length === 0 ) {
                this.$message.warning("请至少选择一个统计字段");
                return;
            }
            let isHaveRowNumber = false;//统计字段是否有组内排名函数
            value.cicadaReduceFieldExps.forEach(element => {
                if (element.mergeExpression === "row_number") isHaveRowNumber = true;
            });
            let isHaveSameValueGroup = false;//统计字段有组内排名函数时分组字段是否都是相同值为一组函数
            if (isHaveRowNumber) {
                value.groupFieldExps.forEach(item =>{
                    if (item.mergeExpression !== "sameValueGroup") isHaveSameValueGroup = true;
                })
            }
            if (isHaveRowNumber && isHaveSameValueGroup) {
                this.$message.warning("统计字段组内排名函数仅支持分组字段为相同值为一组的函数");
                return;
            }
            let flag = false, isSectionValueGroup = false;//判断区间分组是否填写有用信息
            this.saveVo.groupFieldExps.forEach(item =>{
                if (item.mergeExpression === "intervalGroup"){
                    isSectionValueGroup = true;
                    if(item.sectionExpression) {
                        flag = true;
                    }
                }
            })
            if (isSectionValueGroup && !flag ) {
                this.$message.warning("分组字段区间分组请配置至少一条完整信息");
                return;
            }
            this.loadParams.loading = true;
            let services = vm.getServices(pluginServices, pluginMock);
            services.groupSave(this.sourceData.id, this.saveVo , this.loadParams).then(res => {
                if (res.data.status === 0) {
                    showTip && this.$message.success("保存成功！");
                    if(saveName) saveName();
                    vm.$emit("setPreviewAble");
                } else {
                    this.$message.error("保存失败！");
                }
            });
        },
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
    }
};
</script>
<style lang="less" scoped>
.group-statis {
    min-height: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    /deep/.el-card__header {
        padding: 0 !important;
    }
    .comm__header {
        padding: 16px 28px;
    }

    &__content {
        height: 100%;
        padding: 14px 30px;
        box-sizing:border-box;
    }
    &__wrap {
        &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        &-actions {
            display: flex;
            align-items: center;
        }
        &-reflash {
            text-align: center;
            //line-height: 1.875rem;
            //width: 1.875rem;
            //height: 1.875rem;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 2px;
            margin-left: 4px;
            cursor: pointer;
            i {
                color: rgba(0, 0, 0, 0.45);
            }
            &:hover {
                i {
                    color: #0088ff;
                }
            }
        }

        &-list {
            padding-top: 14px;
        }
    }

    &__item {
        display: flex;
    }
    &__fs {
        width: 100%;
        &-header {
            width: 100%;
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #0088ff;
            justify-content: space-between;
        }
        &-content {
            padding: 14px 0;
        }
    }
}
</style>
