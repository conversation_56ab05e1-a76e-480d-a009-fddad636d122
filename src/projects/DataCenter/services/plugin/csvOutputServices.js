/**
 * csv输出 插件 接口
 * @Author: wangjt
 * @Date: 2020-06-15
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/csvOutputPlugin/";

/*
* 初始化
* @params tranStepId
* @returns {Promise<*>}
* */

export async function csvOutputPage( tranStepId , settings) {
    return await reqUtil.get({
        url: root + 'csvOutputPage' ,
        settings : settings
    }, {tranStepId});
}

/*
* 获取字段
* @params tranStepId
* @returns {Promise<*>}
* */

export async function getSourceOutputColumn( stepId ) {
    return await reqUtil.get({
        url: root + 'getSourceOutputColumn' ,
    }, {stepId});
}


/*
* 保存
* @params tranStepId
* @returns {Promise<*>}
* */

export async function save( stepId , dataVo ,settings ) {
    return await reqUtil.post({
        url: root + 'save?stepId='+stepId ,
        settings :settings
    }, dataVo);
}

/*
* 获取字段
* @params tableName , schemaId
* @returns {Promise<*>}
* */

export async function getSortedRdbColumn( tableName , schemaId) {
    return await reqUtil.get({
        url: root + 'getSortedRdbColumn' ,
    }, {tableName , schemaId});
}

/*
* 获取字段
* @params stepId , targetTableId
* @returns {Promise<*>}
* */

export async function getLibraryMapping( stepId , targetTableId ) {
    return await reqUtil.get({
        url: root + 'getLibraryMapping' ,
    }, {stepId , targetTableId });
}
