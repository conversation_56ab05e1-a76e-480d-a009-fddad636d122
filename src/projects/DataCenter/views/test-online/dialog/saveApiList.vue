<!--保存用例-->
<template>
    <div class="saveApiList" v-loading="settings.loading">
        <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="100px">
            <el-form-item v-for="(item , i) in formList"
                          :key="i"
                          :prop="i"
                          :label="item.label ? `${item.label}:` : ''"
                          v-if="item.show()"
            >
                <template v-if="item.type === 'radio'">
                    <dg-radio-group v-model="form[i]" :data="item.data" @change="saveChange"></dg-radio-group>
                </template>
                <template v-else-if="item.type === 'input'">
                    <el-input v-model="form[i]" :maxlength="item.maxlength" :placeholder="item.placeholder"
                              v-input-limit:fieldName></el-input>
                </template>
                <template v-else-if="item.type === 'selectDrop'">
                    <ce-select-drop
                            :placeholder="item.placeholder"
                            :props="item.props"
                            filterable
                            :check-leaf="item.checkLeaf"
                            :check-strictly="item.checkStrictly"
                            :filterNodeMethod="filterNode"
                            v-model="form[i]"
                            :data="item.data"
                            @current-change="item.change"
                    >
                    </ce-select-drop>
                </template>
                <template v-else-if="item.type === 'textarea'">
                    <el-input type="textarea" v-model="form[i]"
                              :maxlength="item.maxlength"
                              :rows="4"
                              show-word-limit></el-input>
                </template>
            </el-form-item>
        </el-form>
        <dialogFooterBtn v-show="!settings.loading" v-footer :data="btnGroup"/>
    </div>
</template>

<script>
import dialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn";
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import CeSelectDrop from "dc-front-plugin/src/components/CeSelectDrop/CeSelectDrop";
import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";
import {UseCaseMixins} from "./useCaseMixins";
import {editUseCase} from "../../../services/useCaseManagement-services/useCaseManagement";

export default {
    name: "saveApiList",
    mixins: [commonMixins, UseCaseMixins],
    components: {
        dialogFooterBtn,
        CeSelectDrop,
    },
    data() {
        return {
            rules: {
                caseName: [{required: true, message: "请输入用例名称", trigger: ['blur', 'change']}],
                saveDir: [{required: true, message: '请选择保存目录', trigger: 'change'}],
                updateCase: [{required: true, message: '请选择更新用例', trigger: 'change'}],
            },
            formList: {
                saveType: {
                    label: '',
                    type: 'radio',
                    data: [
                        {label: '新建保存', value: 'newSave'},
                        {label: '更新保存', value: 'updateSave'},
                    ],
                    show: () => true
                },
                caseName: {
                    label: '用例名称',
                    type: 'input',
                    maxlength: 30,
                    placeholder: '请输入用例名称',
                    show: () => this.form.saveType === 'newSave'
                },
                saveDir: {
                    label: '保存到目录',
                    type: 'selectDrop',
                    placeholder: '请选择保存目录',
                    checkStrictly: false,
                    checkLeaf: false,
                    show: () => this.form.saveType === 'newSave',
                    props: {
                        children: "children",
                        label: "name",
                        value: "id"
                    },
                    data: [],
                    change : ()=>{}
                },
                memo: {
                    label: '用例描述',
                    type: 'textarea',
                    placeholder: '请输入用例描述',
                    maxlength: 200,
                    show: () => this.form.saveType === 'newSave'
                },
                updateCase: {
                    label: '用例名称',
                    type: 'selectDrop',
                    show: () => this.form.saveType === 'updateSave',
                    placeholder: '请选择用例',
                    checkStrictly: true,
                    checkLeaf: true,
                    props: {
                        children: "children",
                        label: "name",
                        value: "id"
                    },
                    data: [],
                    change : this.selectTreeChange
                }

            },
            form: {
                saveType: 'newSave',
                caseName: "",
                saveDir: "",
                memo: "",
                updateCase: "",
            },
            btnGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.submit,
                    show: () => true
                },
            ],
            allApi: [],
            useCaseApiParams: [],
        }
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        saveChange(){
            this.form.caseName = "";
            this.form.memo = "";
            this.form.saveDir = "";
        },
        async getUserCaseInfo(row) {
            const vm = this, {settings} = this;
            settings.loading = true;
            let services = vm.$services("userCaseManagement");
            return await services.getUseCaseDetail(row.id, settings).then(res => {
                if(res.data.code === 0){
                    return res.data.data;
                }
            })
        },
        async selectTreeChange(data){
            let caseInfo = await this.getUserCaseInfo(data);
            if(!caseInfo) return this.saveChange();
            this.form.caseName = caseInfo.useCaseName;
            this.form.saveDir = caseInfo.classifyId;
            this.form.memo = caseInfo.memo;
        },
        submit() {
            const vm = this, {settings, form} = this;
            let services = this.$services("userCaseManagement");
            let isUpdate = false, interName = "";
            if (form.saveType === 'newSave') {
                interName = 'newUseCase';
                isUpdate = false;
            } else {
                interName = 'editUseCase';
                isUpdate = true;
            }
            let saveVo = this.getSaveVo(isUpdate);
            if(isUpdate && !saveVo.useCaseName) return this.$message.warning("更新用例异常!");
            vm.$refs.form.validate(valid => {
                if(valid){
                    services[interName](saveVo, settings).then(res => {
                        if (res.data.code === 0) {
                            vm.$message.success("保存成功");
                            vm.$emit('close');
                        }
                    })
                }
            })

        },
        getSaveVo(isUpdate) {
            let {form} = this;
            let useCaseId = form.updateCase;
            let res = {
                useCaseName: form.caseName,
                classifyId: form.saveDir,
                memo: form.memo,
                useCaseApis: this.setUseCaseApi()
            };
            return isUpdate ? {...res, useCaseId} : res;
        },
        setUseCaseApi() {
            const vm = this;
            let res = [];
            this.allApi.forEach(li => {
                if (li.isApi) {
                    res.push({
                        servicePublicationId: li.id,
                        apiType: li.interName,
                        useCaseApiParams: li.useCaseApiParams
                    });
                } else {
                    res.push({
                        servicePublicationId: li.id,
                        apiType: li.apiType,
                        useCaseApiParams: li.useCaseApiParams
                    })
                }
            })
            return res;
        },

        getUseCaseContent() {
            const vm = this;
            let services = this.$services("userCaseManagement");
            return services.getUseCaseClassify().then(res => {
                if (res.data.code === 0) {
                    vm.formList.saveDir.data = res.data.data;
                }
            })
        },
        getUseCases() {
            const vm = this;
            let services = this.$services("apiTest");
            return services.getCaseTreeWithElement({}).then(res => {
                if (res.data.code === 0) {
                    vm.formList.updateCase.data = vm.filterEmpty(res.data.data);
                }
            })
        },
        initProps({apis , subApis}) {
            this.allApi = [...apis , ...subApis];
        },
        async initGetCase() {
            this.settings.loading = true;
            await this.$axios.all([
                this.getUseCaseContent(),
                this.getUseCases()
            ]);
            this.settings.loading = false;
        }
    },
    created() {
        this.initGetCase();
    }
}
</script>

<style scoped>

</style>
