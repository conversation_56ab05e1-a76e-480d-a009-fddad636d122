<template>
    <div class="RelationBase">
        <common-dialog
                class="params-dialog"
                :title="title"
                width="1100px"
                height="100%"
                :visible.sync="visible"
                @closed="clear_data"
                v-loading="settings.loading"
        >
            <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="functionCode"></help-document></div>
            <template v-if="!isCreated">
                <service-type ref="serviceType" :type="type" :region="region" :showTabs="showTabs" :entrance='entrance'></service-type>
            </template>
            <div v-if="isCreated">
                <div class="steps mb10">
                    <div :class="['steps__item', (active === 1 || active === 2 || active === 3) && 'is-active']">
                        <span class="dg-iconp icon-f-circle-check iconCheck" v-show="active === 2 || active === 3"></span>
                        <span class="steps__number" v-show="active === 1">1</span>
                        <span class="steps__text">基本信息</span>
                    </div>
                    <div :class="['steps__item', (active === 2 || active === 3) && 'is-active']">
                        <span class="dg-iconp icon-f-circle-check iconCheck" v-show="active === 3"></span>
                        <span class="steps__number" v-show="active === 2 || active === 1">2</span>
                        <span class="steps__text">参数定义</span>
                    </div>
                    <div :class="['steps__item', active === 3 && 'is-active']">
                        <span class="steps__number">3</span>
                        <span class="steps__text">API测试</span>
                    </div>
                </div>
                <div v-if="showPage">
                    <!--参数配置弹窗内容页-->
                    <configuration ref="group" v-show="active === 1" :activeName="activeName" :region="region" v-bind="$props" v-on="$listeners" :editDataInfo="editDataInfo" :selectDataSet="selectDataSet" :dirId="dirId" :catalogList="catalogList" :active="active" @changeDataSet="changeDataSet"></configuration>
                    <parameter ref="secondStep" v-show="active === 2" :nextTipData="nextTipData" :selectDataSet="selectDataSet"  v-bind="$props"></parameter>
                </div>

            </div>

            <div slot="footer">
                <el-button size="mini" @click="clear_data">取消</el-button>
                <el-button size="mini" type="primary" @click="immediatelyCreate" v-if="!isCreated">立即创建</el-button>
                <el-button size="mini" v-if="active != 1 && isCreated" @click="lastStep">上一步</el-button>
                <el-button size="mini" v-if="active == 1 && isCreated" @click="nextStep">下一步</el-button>
                <el-button size="mini" type="primary" v-if="active == 2 && isCreated && region !== '2'" @click="save('0')">发布并测试</el-button>
                <el-button size="mini" type="primary" v-if="active == 2 && isCreated && region !== '2'" @click="save('1')">保存并退出</el-button>
                <el-button size="mini" type="primary" v-if="active == 2 && isCreated && region === '2'" @click="save">保存</el-button>
            </div>
        </common-dialog>
        <testDialog ref="testDialog" @refreshTable="refreshTable"></testDialog>
    </div>
</template>

<script>
    import configuration from "../createAPI/index"
    import parameter from "../createAPI/second"
    import serviceType from "../createAPI/serviceType"
    import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import testDialog from "./testDialog"
    import {mapGetters} from "vuex"
    import $right from "@/assets/data/right-data/right-data"
    export default {
        name: "createAPI",
        components: {configuration, parameter, testDialog, serviceType},
        mixins: [ commonMixins, servicesMixins],
        computed: {
            ...mapGetters(["userRight"]),
            rights() {
                let rights = [];
                if (this.userRight) {
                    this.userRight.forEach(r => {
                        rights.push(r.funcCode)
                    });
                }
                return rights;
            },
        },
        props:{
            modelInfo : {
                type: Object,
                default:()=>({})
            },
            entrance: String,
            type: String,

            scriptId :String,
            isAi : {
                type : Boolean,
                default  : false
            },
            isServiceManageUpdate : {
                type : Boolean,
                default  : false
            },
            aiServiceId : String,

        },
        data(){
            return {
                active:1,
                title : "生成API",
                visible :false,
                showPage : false,
                selectDataSet : {},
                region : "",
                serviceInputId: '',
                nextTipData:{},//AI建模根据版本号获取下一步数据
                functionCode:'',
                serviceTypeList:{
                    '数据查询':'4',
                    '信息核查':'6',
                    '比对订阅':'5',
                    '数据碰撞':'7',
                    '模型分析':'1',
                    'AI算法':'2',
                },
                editDataInfo:{},
                dirId:'',
                catalogList:[],//目录树
                showTabs: [],
                isCreated: false,
                activeName: 'basedDataSet',
                permissionList:{
                    "4" : "serviceTypeDataQuery",
                    "6" : "serviceTypeInformationVerification",
                    "5" : "serviceTypeCompareService",
                    "7" : "serviceTypeDataCollision",
                    "1" : "serviceTypeModelAnalysis",
                    "2" : "serviceTypeAiService",
                },
            }
        },
        methods: {
            /**
             * 资源变更
             */
            changeDataSet(){
                const vm = this;
                vm.$refs.secondStep.resetParam();
            },

            /**
             * 设置functionCode 和showTabs
             */
            async setFunctionCode(){
                const vm = this;
                let showTabs = [];
                if(vm.entrance === 'service'){
                    vm.functionCode = vm.type === 'edit' ? 'serviceManagementedit' : 'serviceManagementGenerateAPI';
                    showTabs = ['4','6','5','7','1','2'];
                }
                else if(vm.entrance === 'model'){
                    vm.functionCode = 'modelServiceGenerateAPI';
                    let hasTabs = ['5','7','1'];
                    if(vm.modelInfo.transId && vm.type != 'edit'){
                        hasTabs = await vm.checkPlugin(vm.modelInfo.transId);
                    }
                    if(vm.modelInfo.dataSetList && !vm.modelInfo.dataSetList.length){
                        showTabs = hasTabs;
                    }
                    else{
                        showTabs = hasTabs;
                    }
                }
                else if(vm.entrance === 'ai'){
                    vm.functionCode = 'AIModelingBuildApi';
                    showTabs = ['2'];
                }
                else if(vm.entrance === 'dataSet'){
                    vm.functionCode = 'dataSetOperationCreateAPI';
                    showTabs = ['4','6'];
                }
                vm.showTabs = showTabs;
                vm.setPermission(showTabs);
                if(vm.type != 'edit' && !vm.region){
                    vm.region = vm.showTabs.length ? vm.showTabs[0] : '';
                }
            },
            setPermission(list){
                const vm = this;
                let showTabs = [];
                list.forEach(n=>{
                    let code = $right[vm.permissionList[n]];
                    if(vm.rights.indexOf(code) > -1){
                        showTabs.push(n);
                    }
                })
                vm.showTabs = showTabs;
            },
            async checkPlugin(id){
                const vm = this;
                let services = vm.$services("serviceManage");
                let param = {
                    transId: id
                }
                let check = [];
                await services.checkPlugin(param).then(res=>{
                    check = res.data.data;
                })
                return check
            },
            /**
             * 立即创建
             */
            immediatelyCreate(){
                const vm = this;
                vm.region = vm.$refs.serviceType.selectType;
                vm.activeName = vm.$refs.serviceType.activeName;
                if(!vm.region){
                    return vm.$message.warning("请选择服务类型");
                }
                vm.isCreated = true;
                vm.active = 1;
                vm.$nextTick(()=>{
                    vm.$refs.group.ruleForm.region = vm.region;
                    vm.$refs.group.ruleForm.singleOrManyTable = '0';
                    vm.$refs.group.init(vm.editDataInfo);
                    vm.$refs.group.$refs.ruleForm.clearValidate();
                    vm.$refs.secondStep.clearInfo();
                })
            },
            //发布并测试刷新列表
            refreshTable(){
                this.$emit("changePage");
            },
            /**
             * 服务测试
             */
            testService(data){
                const vm = this;
                data.refresh = true;
                vm.$refs.testDialog.show(data);
            },
            /**
             * 保存
             */
            save(saveType){
                const vm = this;
                if(['1','5','7'].includes(vm.region) || (vm.region === '6' && vm.$refs.group.ruleForm.singleOrManyTable === '2')){ //模型分析、比对订阅、数据碰撞保存
                    vm.modelSave(saveType);
                }
                else if (vm.region === "2") { //AI建模保存
                    this.AISave();
                }
                else{ //数据查询、信息核查保存
                    vm.dataQuerySave(saveType);
                }
            },
            dataQuerySave(saveType){
                const vm = this;
                if(!vm.$refs.secondStep.validationInquiryAndVerification()){
                    return
                }
                let paramsInfo = vm.$refs.secondStep.getInquiryAndVerificationParamsInfo();
                let params = vm.setVo(paramsInfo, saveType);
                let {serviceManageServices, serviceManageMock, settings} = this;
                let services = vm.getServices(serviceManageServices, serviceManageMock);

                if(vm.type === 'edit') {
                    params.serviceMetaId = vm.editDataInfo.serviceMetaId //|| vm.modelInfo.apiServiceId;
                }
                settings.loading = true;
                if(vm.region === "4"){
                    if(vm.type === 'edit') {
                        params.serviceMetaId = vm.editDataInfo.serviceMetaId //|| vm.modelInfo.apiServiceId;
                        params.serviceId = vm.editDataInfo.serviceId;
                        services.editDataQueryService(params, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("编辑成功！");
                                setTimeout(()=>{
                                    vm.$emit("changePage");
                                    vm.$emit("getApiList",res.data.data);
                                    saveType === '0' ? vm.testService({serviceId : res.data.data}) : null;
                                },1000)
                                vm.clear_data();
                            }
                        });
                    }else {
                        services.createDataQueryService(params, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("API已创建！");
                                setTimeout(()=>{
                                    vm.$emit("changePage");
                                    vm.$emit("getApiList",res.data.data);
                                    saveType === '0' ? vm.testService({serviceId : res.data.data}) : null;
                                },1000)
                                vm.clear_data();
                                //this.$emit("showSuccess", params);
                            }
                        });
                    }
                }
                else{
                    if(vm.type === 'edit') {
                        params.serviceMetaId = vm.editDataInfo.serviceMetaId;//|| vm.modelInfo.apiServiceId;
                        params.serviceId = vm.editDataInfo.serviceId;
                        services.editInformationVerificationService(params, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("编辑成功！");
                                setTimeout(()=>{
                                    vm.$emit("changePage");
                                    vm.$emit("getApiList",res.data.data);
                                    saveType === '0' ? vm.testService({serviceId : res.data.data}) : null;
                                },1000)
                                vm.clear_data();
                            }
                        });
                    }
                    else{
                        services.createInformationVerificationService(params, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("API已创建！");
                                setTimeout(()=>{
                                    vm.$emit("changePage");
                                    vm.$emit("getApiList",res.data.data);
                                    saveType === '0' ? vm.testService({serviceId : res.data.data}) : null;
                                },1000)
                                vm.clear_data();
                                //this.$emit("showSuccess", params);
                            }
                        });
                    }
                }

            },
            /**
             * 数据查询、核查保存设置入参
             */
            setVo(paramsInfo, saveType){
                const vm = this;
                let paramList = [], ginsengList = [];
                let firstInfo = vm.$refs.group.ruleForm, second = vm.$refs.secondStep;
                paramsInfo.requestTableData.forEach(element => {
                    let par = {
                        paramId : element.id,
                        paramCode : element.paramCode || element.code,
                        paramName : element.name || element.code,
                        paramValue : element.paramValue || element.code,
                        type : element.dataType,
                        isMust : element.isMust ? "t" : "f",
                        defaultValue : element.defaultValue,
                        memo : element.memo,
                        example : element.example
                    };
                    if(vm.region === "4"){
                        par.likeQuery = element.likeQuery ? "1" : "0";
                    }
                    paramList.push(par)
                });
                if(vm.region === "4"){
                    paramsInfo.responseTableData.forEach(element => {
                        ginsengList.push({
                            paramId : element.id,
                            paramCode : element.paramCode || element.code,
                            paramName : element.name || element.code,
                            paramValue : element.paramValue || element.code,
                            type : element.dataType,
                            isMust : element.isMust  ? "t" : "f",
                            defaultValue : element.defaultValue,
                            memo : element.memo,
                            desensitization: element.desensitization,
                            datasetId: element.dataSetId
                            // example : element.example
                        })
                    });
                }
                else{
                    ginsengList = paramsInfo.inspectTableData;
                }
                let ParamConfigVo = {
                    interfaceEnglishName: vm.modelInfo.interfaceEnglishName || second.apiInfo.interfaceEnglishName || (vm.type === 'edit' ? vm.editDataInfo.interfaceEnglishName :''),
                    interfaceChineseName: firstInfo.name,//api名称
                    implChineseName: second.nodeTableName ||  vm.$refs.group.ruleForm.dataSetNodeData.name,
                    implEnglishName: vm.modelInfo.implEnglishName || second.apiInfo.implEnglishName || (vm.type === 'edit' ? vm.editDataInfo.implEnglishName :''),
                    serviceType: firstInfo.region,
                    implVersion: firstInfo.version,
                    paramList:paramList,
                    ginsengList: ginsengList,
                    filterJson: "[]",
                    memo: firstInfo.desc,
                    classifyId: firstInfo.catalogId,
                    saveOrUpdate : vm.type === 'edit' ? 'update' : 'save',
                    singleOrManyTable: firstInfo.singleOrManyTable,//单表or多表 //0:单表，1：多表
                    manyJoinMetas:[],
                    encasulationJudgContion:[],//匹配模式
                    saveType: saveType,//  0 保存并测试 保存并发布, 1 保存并退出 保存不发布
                    batchQuery : paramsInfo.batchQuery,
                    maxQueryNum: paramsInfo.maxQueryNum,
                    tableColumnMappings: paramsInfo.tableColumnMappings,
                }
                let moreDataSet = [];
                if(firstInfo.singleOrManyTable === '1'){
                    moreDataSet = vm.$refs.group.$refs.selectArr_drop.getCheckedNodes(true) || [];
                }

                let resourceIds = firstInfo.singleOrManyTable === '0' ? [firstInfo.dataSetValue] : moreDataSet.map(n=>n.id);
                ParamConfigVo.resourceIds =  resourceIds;
                ParamConfigVo.modelId =  resourceIds[0];
                if(firstInfo.region === '4'){
                    ParamConfigVo.orderFieldTables = paramsInfo.orderFieldTables;
                    ParamConfigVo.returnParamMappings = paramsInfo.returnParamMappings;
                    ParamConfigVo.encasulationJudgContion = paramsInfo.conditionInfo;
                }

                let selectNode = firstInfo.singleOrManyTable === '0' ?  vm.$refs.group.$refs.select_model.getCurrentNode() : moreDataSet[0];
                ParamConfigVo.dbType = selectNode.dbType;
                ParamConfigVo.tableName = selectNode.name;
                ParamConfigVo.modelId = selectNode.id;
                if(firstInfo.singleOrManyTable === '0'){
                    ParamConfigVo.tableColumnMappings = [
                        {
                            datasetId: selectNode.id,
                            datasetName: selectNode.code,
                            columns: paramList.map(n=>{ return {filedName:n.paramValue,fieldCode:n.paramValue,fieldAsName:n.paramCode,fieldAsCode:n.paramCode}}),
                            dbType: selectNode.dbType,
                            datasetZhName: selectNode.name || selectNode.code
                        }]
                }
                return ParamConfigVo;
            },

            /**
             * 模型分析保存
             */
            modelSave(saveType){
                const vm = this, { settings} = this;
                let group = vm.$refs.group;
                let secondStep = vm.$refs.secondStep;
                if(vm.region === '5' && !vm.$refs.secondStep.validation()){
                    return
                }
                let paramInfo = secondStep.reqTableData.find(n=>n.paramCode === 'operatorDataSet') || {};
                if (paramInfo && paramInfo.children && paramInfo.children.length === 0) {
                    this.$message.warning("暂无API请求入参组件或暂无数据！");
                    return;
                }
                else if(vm.region === '6' && paramInfo.children[0].children && paramInfo.children[0].children.find(n=>n.isMust === 'f')){
                    return vm.$message.warning("API请求入参组件参数需必填！");
                }

                let repInfo = secondStep.backTableData.find(n=>n.paramCode === 'operatorDataSet') || {};
                if (repInfo && repInfo.children && repInfo.children.length === 0) {
                    this.$message.warning("暂无API响应结果组件或暂无数据！");
                    return;
                }
                let services = vm.$services('servicesServices');
                let data= {
                    "paramList": secondStep.reqTableData,
                    "ginsengList": secondStep.backTableData,
                    "implVersion": group.ruleForm.version,
                    "interfaceChineseName": group.ruleForm.name,
                    "interfaceEnglishName": vm.modelInfo.interfaceEnglishName || secondStep.apiInfo.interfaceEnglishName || (vm.type === 'edit' ? vm.editDataInfo.interfaceEnglishName :''),
                    "implChineseName": secondStep.nodeTableName || group.ruleForm.dataSetNodeData.name || group.ruleForm.dataSetNodeData.label,
                    "implEnglishName": vm.modelInfo.implEnglishName || secondStep.apiInfo.implEnglishName || (vm.type === 'edit' ? vm.editDataInfo.implEnglishName :''),
                    "memo": group.ruleForm.desc,
                    "modelId": group.ruleForm.dataSetValue,
                    "serviceType": vm.region,
                    "saveOrUpdate" : vm.type === 'edit' ? 'update' : group.ruleForm.saveOrUpdate,
                    "classifyId": group.ruleForm.catalogId,
                     "saveType": saveType,//  0 保存并测试 保存并发布, 1 保存并退出 保存不发布
                }
                if(vm.region === '6'){
                    data.singleOrManyTable = group.ruleForm.singleOrManyTable;//单表or多表 //0:单表，1：多表
                }
                if(vm.region === '5'){
                    data.compareType = secondStep.contrastType; //比对方式，增量:0，全量:1
                    data.incrementalColumn = secondStep.deltaTime;//比对字段
                    data.incrementalZhColumn = '';//比对字段中文名
                    data.incrementalStepId = secondStep.busiDataSet;//业务数据集id
                }
                if(vm.$refs.group.ruleForm.serviceMetaId && vm.$refs.group.ruleForm.saveOrUpdate == 'update'){
                    data.serviceMetaId = vm.$refs.group.ruleForm.serviceMetaId;
                    data.interfaceChineseName = (vm.$refs.group.serviceMetaList.find(n=>n.value == data.serviceMetaId)).label || '';
                }
                vm.type === 'edit' && vm.entrance === 'service' ?  data.serviceMetaId = vm.modelInfo.serviceMetaId : '';
                vm.type === 'edit' && vm.entrance === 'model' ? data.serviceMetaId = vm.modelInfo.apiServiceId : '';
                settings.loading = true;
                if(vm.type === 'edit'){
                    services.editModelService(data, settings).then(res=> {
                        if (res.data.status === 0) {
                            vm.$message.success("编辑成功！");
                            setTimeout(()=>{
                                vm.$emit("changePage");
                                vm.$emit("getApiList",res.data.data);
                                saveType === '0' ? vm.testService({serviceId : res.data.data}) : null;
                            },1000)
                            vm.clear_data();
                        }
                    })
                }
                else{
                    services.createModelService(data, settings).then(res=> {
                        if (res.data.status === 0) {
                            vm.$message.success("API已创建！");
                            setTimeout(()=>{
                                vm.$emit("changePage");
                                vm.$emit("getApiList",res.data.data);
                                saveType === '0' ? vm.testService({serviceId : res.data.data}) : null;
                            },1000)
                            vm.clear_data();
                        }
                    })
                }
            },

            /**
             * AI建模保存
             */
            AISave(){
                const vm = this, {settings} = this;
                let params = vm.AISetVo(),
                    service = this.$services("serviceManage");
                settings.loading = true;
                if (this.$refs.group.ruleForm.saveOrUpdate === 'update' || this.isServiceManageUpdate || this.type === 'edit') {
                    params.serviceId = this.$refs.group.aiServiceId || this.modelInfo.aiServiceId || this.modelInfo.serviceId || this.modelInfo.apiServiceId;
                    service.updateAiService(params, settings).then(res =>{
                        if (res.data.status === 0) {
                            this.$message.success("编辑成功！");
                            setTimeout(()=>{
                                vm.$emit("changePage");
                                vm.$emit("getApiList",res.data.data.serviceId);
                            },1000)
                            this.clear_data();
                            // this.$emit("showSuccess", params);
                        }
                    })
                }else {
                    service.publishAiService(params, settings).then(res =>{
                        if (res.data.status === 0) {
                            this.$message.success("API已创建！");
                            setTimeout(()=>{
                                vm.$emit("changePage");
                                vm.$emit("getApiList",res.data.data.serviceId);
                            },1000)
                            this.clear_data();
                            // this.$emit("showSuccess", params);
                            let row = {
                                serviceType : 'AI算法',
                                serviceId : res.data.data.serviceId,
                                interfaceChineseName : res.data.data.name,
                                requestPath : res.data.data.requestPath,
                            };
                            this.aiSuccess(row);
                        }
                    })
                }
            },
            /**
             * AI建模保存设置入参
             */
            AISetVo(){
                let paramList = [], ginsengList = [];
                this.$refs.secondStep.reqTableData.forEach(element => {
                    paramList.push({
                        "paramName": element.paramName || element.paramCode,
                        "paramMemo": element.memo,
                        "paramType": element.type,
                        "isMust": element.isMust === 't' ? true : false  ,
                        "exampleValue":  element.example,
                        "children":[]
                    })
                });
                this.$refs.secondStep.backTableData.forEach(element => {
                    ginsengList.push({
                        "paramName": element.paramName || element.paramCode,
                        "paramMemo": element.memo,
                        "paramType": element.type,
                        "isMust": element.isMust === 't' ? true : false  ,
                        "exampleValue":  element.defaultValue,
                        "children":[]
                    })
                });
                return {
                    "apiName": this.$refs.group.ruleForm.saveOrUpdate === 'save' ? this.$refs.group.ruleForm.name : this.$refs.group.aiUpdateName,
                    "serviceType": this.$refs.group.ruleForm.region,
                    "sourceId": this.$refs.group.ruleForm.serviceVersion,
                    "version": this.$refs.group.ruleForm.version,
                    "memo": this.$refs.group.ruleForm.desc,
                    "requestParams": paramList,
                    "responseParams": ginsengList,
                    "classifyId": this.$refs.group.ruleForm.catalogId,
                }
            },

            aiSuccess(row){
                const vm = this;
                if(vm.rights.indexOf($right["AIModelingTestApi"]) <= -1) {
                    this.$message.warning('无测试权限,无法跳转测试');
                    return;
                }
                this.$confirm('跳转测试？', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success',
                    closeOnClickModal: false
                }).then(() => {
                    vm.$nextTick(()=>{
                        vm.$refs.testDialog.show(row);
                    })
                }).catch(() => {
                    // vm.$emit("changePage");
                })
            },

            /**
             * 下一步
             */
            nextStep(){
                const vm = this;
                if(vm.active === 1){
                    let flag =  vm.$refs.group.validationDataQuery();
                    if(!flag){
                        this.$message.warning("信息未全部填写或填写不正确");
                        return
                    }
                    if(vm.region === '4' || vm.region === '6'){ //数据查询、信息核查
                        let info = [];
                        if(vm.$refs.group.ruleForm.singleOrManyTable === '1'){
                            info = vm.$refs.group.$refs.selectArr_drop.getCheckedNodes(true) || [];
                            if(info.length < 2){
                                this.$message.warning("请选择多个数据集");
                                return
                            }
                            vm.$refs.secondStep.getDataSetColumnLoop(info,vm.region, vm.$refs.group.ruleForm.singleOrManyTable);
                        }
                        else if(vm.$refs.group.ruleForm.singleOrManyTable === '0'){
                            info = [vm.$refs.group.ruleForm.dataSetNodeData];
                            vm.$refs.secondStep.getDataSetColumnLoop(info,vm.region, vm.$refs.group.ruleForm.singleOrManyTable);
                        }
                        else{
                            vm.$refs.secondStep.getInputInfo(vm.$refs.group.ruleForm.dataSetValue, vm.region, vm.$refs.group.ruleForm.singleOrManyTable)
                        }
                    }
                    else if(vm.region === '1' || vm.region === '5' || vm.region === '7'){ //模型分析
                        vm.$refs.secondStep.getInputInfo(vm.$refs.group.ruleForm.dataSetValue, vm.region, vm.$refs.group.ruleForm.singleOrManyTable)
                    }
                    else if (vm.region === '2') { //Ai算法
                        vm.nextTipData = vm.$refs.group.nextTipData;
                        vm.$refs.secondStep.setAIModelingTableData(vm.nextTipData, vm.modelInfo, vm.region, vm.$refs.group.ruleForm.singleOrManyTable);
                    }else {
                    }
                    vm.active = 2;

                }
                else if(vm.active === 2){

                }
            },
            /**
             * 上一步
             */
            lastStep(){
                this.$refs.group.$refs.ruleForm.clearValidate();
                this.active = this.active -1 ;
            },
            clear_data(){
                this.visible = false;
                this.showPage = false;
                this.title = "服务发布";
                this.$emit("clear");
            },
            /**
             * api类型为模型分析或数据查询 根据服务id获取服务详情 更新时
             */
            async queryServiceDetails(id){
                const vm = this;
                let services = vm.$services("servicesServices");
                await services.queryServiceDetails(id).then(res=> {
                    if (res.data.status === 0) {
                        vm.editDataInfo = res.data.data;
                        vm.dirId = res.data.data.classifyId;
                        if (['4', '6', '1','5','7'].includes(vm.region)) {
                            vm.$refs.secondStep.setTableInfo(vm.editDataInfo, vm.region);
                        }
                    }
                });
            },
            /**
             * 获取保存的目录树
             */
            getCatalogList(dirId){
                const vm = this;
                let services = vm.$services("serviceManage");
                services.queryServiceClassifyTree().then(res => {
                    vm.catalogList = res.data.data || [];
                    vm.dirId = dirId || (vm.catalogList.length ? vm.catalogList[0].id : '');
                })
            },
            async init(row){
                const vm = this;
                vm.region = vm.serviceTypeList[row.serviceType];
                if(vm.region !== '2' && vm.type === 'edit'){
                    await vm.queryServiceDetails(row.serviceMetaId);
                }
            },

            async show(selectDataSet,serviceInputId,row, dirId){
                const vm = this;
                if ((vm.modelInfo && vm.modelInfo.implChineseName) || vm.type === 'edit') this.title = '编辑API';
                vm.selectDataSet = selectDataSet; //数据准备生成api
                vm.visible = true;
                vm.showPage = true;
                vm.isCreated = false;
                if(vm.type === 'edit' || vm.entrance === 'ai'){
                    vm.isCreated = true;
                }
                vm.serviceInputId = serviceInputId;//服务输入组件id
                vm.region = '';
                await vm.setFunctionCode();
                await vm.getCatalogList(dirId);
                vm.active = 1;
                if(vm.type === 'edit')
                    vm.$refs.group.settings.loading = true;
                vm.editDataInfo = {};
                if(vm.entrance === 'ai' && vm.type != 'edit') vm.region = '2';
                row && row.serviceType ? await vm.init(row) : '';
                if(vm.region && vm.type === 'edit'){
                    vm.showTabs = [vm.region];
                    vm.$refs.group.init(vm.editDataInfo);
                }
                if(vm.region === '2' && vm.entrance === 'ai'){
                    vm.$refs.group.init(vm.editDataInfo);
                }
            },
        },
    }
</script>

<style scoped lang="less">
    .steps {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: 10px;
        &__item {
            display: flex;
            align-items: center;
            &.is-active {
                .steps__number {
                    color: #fff;
                    background-color: #1890ff;
                    border-color: #1890ff;
                }
                .steps__text {
                    color: rgba(0, 0, 0, 0.85);
                    font-weight: bold;
                }
            }
            &:not(:last-child) {
                &::after {
                    content: "";
                    display: inline-block;
                    width: 150px;
                    height: 1px;
                    background-color: rgba(0, 0, 0, 0.15);
                    margin: 0 16px;
                }
            }
        }
        &__number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.15);
            margin-right: 14px;
        }
        &__text {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }

        .iconCheck{
            font-size: 32px;
            height: 32px;
            line-height: 32px;
            color: #1890ff;
        }

    }
</style>
