<template>
    <div class="sqlAnalyse div_main" v-loading="settings.loading">
        <div class="ce-panel_title-bg">
            <div class="ce-panel_title">
                <!--<input disabled @keyup="titleChange(title)" ref="title" maxlength="30" class="ce-visual_title"
                       type="text" v-model="title">-->
                <div class="ce-visual_title name_txt" v-if="showTxt" @click="toEdit" :title="title"> <i class="icon pr6">&#xe604;</i>{{title}}</div>
                <el-input class="ce-visual_title" ref="name_input" :maxlength="maxlength" @blur="toRead" v-model.trim="title"
                          v-input-filter="title"
                          v-else></el-input>
            </div>
            <div class="ce-buttons_group">
                 <span v-for="btn in btns" :key="btn.txt">
                    <dg-button type="text" @click="btn.fn" icon="el-icon-back">{{btn.txt}}</dg-button>
                </span>
            </div>
        </div>
        <div class="right" ref="divRight">
            <el-input
                    type="textarea"
                    :rows="4"
                    resize="none"
                    placeholder="请输入SQL语句"
                    v-model="textarea"
                    class="ta_selet_content"
            ></el-input>
            <div ref="getTableH" class="getTableH">
                <ModelingCenterTable ref="modelingCenterTable" class="right_table"/>
            </div>

            <div class="div_bottom">
                <el-button type="primary" size="mini" class="btn" @click="btnAnalyze">分析</el-button>
                <el-button type="primary" size="mini" class="btn" @click="btnDataSetEvent">数据对象</el-button>
                <el-button type="primary" size="mini" class="btn" @click="query">预览数据集</el-button>
                <el-button type="primary" size="mini" class="btn" @click="btnSaveEvent">保存</el-button>
            </div>
        </div>
        <common-dialog
                custom-class="preview"
                title="预览数据"
                :visible.sync="preview.isVisible"
                width="1000px"
                @closed="reSetLoading"
        >
            <div>
                <div class="mb10">
                    <el-row>
                        <el-col style="margin-right: 5px" :span="5">
                            <el-input @input="changeNum" v-model="tableSize" size="mini"
                                      placeholder="请输入预览数量"></el-input>
                        </el-col>
                        <el-col :span="5" class="pt2">
                            <el-button type="primary" @click="query" size="mini">刷新</el-button>
                        </el-col>
                    </el-row>
                </div>
                <transition name="el-fade-in-linear">
                    <keep-alive>
                        <common-table v-if="preview.reNew"
                                      :data="preview.list"
                                      :columns="preview.columns"
                                      v-loading="settings.loading"
                                      :highlight-current-row="true"
                                      height="398px"
                                      :pagination="false"
                        >
                        </common-table>
                    </keep-alive>
                </transition>
            </div>

        </common-dialog>
        <common-dialog
                :visible.sync="spark_dialog.visible"
                title="数据对象"
                fullscreen
                custom-class="spark"
                @closed="clear_dialog"
        >
            <PreviewSparkTable :schemaId="schemaId" v-if="spark_dialog.reNew"/>
        </common-dialog>
    </div>
</template>

<script src="./sql-analyse.js"></script>
<style scoped lang="less" src="./sql-analyse.less"></style>