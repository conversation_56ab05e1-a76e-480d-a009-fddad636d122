<template>
  <div class="scatterAttr ce-plug_cont" v-loading="settings.loading">
    <div class="ce-plug_attr">
      <el-form :model="plug_data"
               size="mini"
               label-position="top"
               :label-width="width"
               class="attrForm"
               ref="form"
      >
        <el-form-item
            v-for="(attr , i ) in attrsList"
            :key="i"
            :label="attr.label + ' :'"
            :prop="attr.props"
            :rules="attr.rule"
        >
          <el-input class="ce-attr_item"
                    :placeholder="attr.placeholder"
                    v-if="attr.type ==='input'" v-model="attr.value"
          ></el-input>
          <div v-if="attr.type ==='select'">
            <i class="el-icon-info ce-tip" v-if="attr.hasTip" :title="attr.tip"></i>
            <el-select filterable class="ce-attr_item" v-model="attr.value"
                       @change="axisLabel($event , attr.option , i)" :placeholder="attr.placeholder">
              <el-option
                  v-for="item in attr.option"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </div>

        </el-form-item>
      </el-form>
      <FormTip/>
    </div>
    <el-dialog
        :title="plug_data.title"
        :visible.sync="dialogVisible"
        :modal-append-to-body="true"
        :append-to-body="true"
        :close-on-click-modal="false"
        @closed="clearData"
    >
      <div class="ce-chart_box" v-loading="loadChart.loading">
        <ScatterChart v-if="reNew" :scatterChartData="chartData" :plugData="plug_data"></ScatterChart>
        <div v-else class="ce-data-none p30">{{ data_none }}</div>
      </div>

    </el-dialog>
    <!-- <div class="attr_btn">
      <el-button
          v-for="(btn , inx) in buttons"
          :key="inx"
          size="mini"
          :type="btn.type"
          @click="btn.clickFn"
      >{{ btn.name }}
      </el-button>
    </div> -->
  </div>
</template>

<script>
import FormTip from './FormTip'
import ScatterChart from './charts/ScatterChart'
import {chartMixins} from "./chart-mixins/chart-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "./service-mixins";

export default {
  name: "ScatterAttr",
  components: {
    FormTip,
    ScatterChart
  },
  mixins: [chartMixins, commonMixins, servicesMixins],
  props: {
    sourceData: Object,
    rowData: Object
  },
  computed: {
    plug_data() {
      return {
        title: this.attrsList[0].value,
        xNode: this.attrsList[1].value,
        yNode: this.attrsList[2].value,
        xName: this.attrsList[1].axisLabel,
        yName: this.attrsList[2].axisLabel
      }
    }
  },
  data() {
    return {
      colType: ["LONG", "DOUBLE", "FLOAT", "BIGINTEGER", "BININT", "SMALLINT", "DECIMAL",
        "NUMERIC", "REAL", "DOUBLEPRECISION", "SMALLSERIAL", "SERIAL", "BIGSERIAL",
        "NUMBER",
        "DECIMAL",
        "INT", "INT2", "INT4", "INT8",
        "TINYINT",
        "INTEGER",
        "DECIMAL",
        "BIGINTEGER",
        "BIGDECIMAL",
        "FLOAT4",
        "FLOAT8",

      ],
      dialogVisible: false,
      scatterChartData: {},
      width: '100px',
      attrsList: [
        {
          label: '图表标题',
          type: 'input',
          value: '散点图图表展示',
          placeholder: '请输入图表标题',
          props: 'title',
          rule: [
            {required: true, message: '标题名称不能为空', trigger: ['blur', 'change']},
          ]
        },
        {
          label: 'x轴分析字段',
          type: 'select',
          value: '',
          axisLabel: "",
          option: [],
          placeholder: '请选择x轴分析字段',
          props: 'xNode',
          hasTip: true,
          tip: '分析字段请选择带有连续性的数值型字段',
          rule: [
            {required: true, message: 'x轴分析字段不能为空', trigger: ['blur', 'change']},
          ]
        },
        {
          label: 'y轴分析字段',
          type: 'select',
          value: '',
          axisLabel: "",
          option: [],
          props: 'yNode',
          placeholder: '请选择y轴分析字段',
          rule: [
            {required: true, message: 'y轴分析字段不能为空', trigger: ['blur', 'change']},
          ]
        }
      ],
    }
  },
  methods: {
    init() {
      const vm = this, {resultServices, resultMock, settings, scatter_root} = this;
      let services = vm.getServices(resultServices, resultMock);
      settings.loading = true;
      let path = scatter_root + "scatterChartsPluginPage";
      services.getChartsPluginPage(vm.sourceData.id, settings, path).then(res => {
        if (res.data.status === 0) {
          let result = res.data.data;
          result.columns.forEach(item => {
            if (vm.colType.indexOf(item.columnType.toUpperCase()) > -1) {
              let node = {
                label: item.columnZhName,
                value: item.columnName,
                columnType: item.columnType,
                length: item.length,
                precsn: item.presn,
                stepCode: item.stepCode,
                stepId: item.stepId,
                stepName: item.stepName,
              };
              vm.attrsList[1].option.push(node);
              vm.attrsList[2].option.push(node);
            }
          });
          if (vm.scatterChartData.title != null) {
            vm.attrsList[0].value = result.scatterChartsPlugin.title;
          }
          vm.attrsList[1].value = result.scatterChartsPlugin.columnCode || "";
          vm.attrsList[2].value = result.scatterChartsPlugin.groupColumn || "";
          vm.savedChange(vm.attrsList[2].value);
          vm.axisLabel(vm.attrsList[1].value, vm.attrsList[1].option, 1);
          vm.axisLabel(vm.attrsList[2].value, vm.attrsList[2].option, 2);
        } else {
          vm.$message.error("服务器异常，请联系管理员!");
        }
      })
    },
    save(saveName,showTip) {
      const vm = this, {resultServices, resultMock, settings, scatter_root} = this;
      let services = vm.getServices(resultServices, resultMock);
      vm.$refs.form.validate(valid => {
        if (valid) {
          let showType = "scatter";
          let dimension = '';
          settings.loading = true;
          let path = scatter_root + "saveScatterChartsPlugin";
          services.saveChartsPlugin(vm.sourceData.id, vm.plug_data.title, showType, vm.plug_data.xNode, "", path, settings, "", vm.plug_data.yNode, dimension)
              .then(res => {
                if (res.data.status === 0) {
                  vm.saved = true;
                    showTip && vm.$message.success(res.data.data);
                    if(saveName)saveName();
                    vm.$emit("setPreviewAble");
                } else {
                  vm.$message.error("保存失败！");
                }
              })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped ></style>
