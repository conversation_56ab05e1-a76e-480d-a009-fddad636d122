<template>
    <div style="background-color : white;height: 100%;padding-left:15px;">
        <el-tabs v-model="actTab">
            <el-tab-pane name="importV" v-if="rights.indexOf('serviceSpaceImport') > -1">
                <span slot="label">导入 <help-document code="serviceSpaceImport"></help-document></span>
            </el-tab-pane>
            <el-tab-pane name="exportV" v-if="rights.indexOf('serviceSpaceExport') > -1">
                <span slot="label">导出 <help-document code="serviceSpaceExport"></help-document></span>
            </el-tab-pane>
            <!--<el-tab-pane label="导入" name="importV"></el-tab-pane>-->
            <!--<el-tab-pane label="导出" name="exportV"></el-tab-pane>-->
            <!-- <el-tab-pane label="历史日志" name="log"></el-tab-pane> -->
        </el-tabs>
        <div class="manage-cont">
            <keep-alive>
                <component class="manage-page" ref="cont" :is="actTab" :data="row" ></component>
            </keep-alive>
        </div>
    </div>
</template>

<script>
import importV from './dialog/importV.vue'
import exportV from './dialog/exportV.vue'
import log from './dialog/log.vue'
import $right from "@/assets/data/right-data/right-data"
import {mapGetters} from "vuex"
export default {
    name: "index",
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
    },
    components: {
        importV,
        exportV,
        log,
    },
    props: {

    },
    data(){
        return {
            row : [],
            actTab : "importV",
            tabs : [
            ]
        }
    }
}
</script>

<style scoped lang="less">
.manage {
    height: 100%;
    &-cont {
        height: calc(100% - 33px);
    }
    &-page {
        height: calc(100% - 14px);
        padding-top: 14px;
    }
}

</style>
