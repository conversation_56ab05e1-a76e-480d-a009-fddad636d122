<template>
    <div class="node modelNode selectn" ref="node" :style="flowNodeContainer" :title="node.name"
        @contextmenu.prevent.stop="showNodeMenu(node)" @mouseenter="showDelete" @mouseleave="hideDelete"
        @click.prevent.stop="showDetail(node)" @dblclick.prevent.stop="editAttr(node)">
        <span class="node__icon">
            <svg class="eda_icon">
                <use :xlink:href="nodeIco"></use>
            </svg>
        </span>
        <p class="node-content">
            <span class="node-name" v-if="!edits[node.id]">{{ node.name }}</span>
            <span v-else @click.stop.prevent>
                <el-input class="" ref="input" v-model="node.name" maxlength="50"
                    @keydown.native.enter="onSubmitName(node)" size="mini" v-input-limit:fieldName
                    @blur="onSubmitName(node)"></el-input>
            </span>
        </p>
        <span class="node--tip-icon" v-if="mouseEnter && !rowData.caseId">
            <i class="dg-iconp icon-cross" @click.stop="deleteNode"></i>
        </span>
        <span class="node--tip-icon node--tip">
            <i class="dg-iconp" :class="node.status ? 'icon-tick' : 'icon-exclamatory'"></i>
        </span>
        <span :class="{ 'dc-link_dot': !rowData.caseId }" class="ce-link_source ce-link_dot_pos "
            v-if="outputPlugins.indexOf(node.keyWord) === -1 && (node.canLink !== undefined ? node.canLink : true)"
            title="连线">
            <i :class="{ 'dc-link_dot': !rowData.caseId }" class="ce-link_point"></i>
        </span>
        <span v-if="collision.includes(node.keyWord)">
            <i class="ce-link_left ce-link_point ce-link_dot_pos"></i>
            <i class="ce-link_right ce-link_point ce-link_dot_pos"></i>
        </span>
        <span v-else-if="doubleInputPlugins.includes(node.keyWord)">
            <i class="ce-link_left ce-link_point ce-link_dot_pos"></i>
            <i class="ce-link_right ce-link_diamond ce-link_dot_pos" style="border:1px solid #52c41a"></i>
        </span>
        <i v-else-if="multiline.includes(node.keyWord)"
            class="ce-link-diamond_center ce-link_diamond ce-link_dot_pos"></i>
        <i v-else-if="!inputPlugins.includes(node.keyWord)" :word="node.keyWord"
            class="ce-link_center ce-link_point ce-link_dot_pos 22"></i>
    </div>
</template>

<script>
import { getPluginColor } from "@/assets/data/model-plugin/plugin-name-description";
import { listMixins } from "@/api/commonMethods/list-mixins";
import { globalBus } from "@/api/globalBus";

export default {
    name: "ModelNode",
    props: {
        node: Object,
        stopEvent: {
            type: Boolean,
            default: false
        },
        rowData: Object
    },
    mixins: [listMixins],
    data() {
        return {
            // 控制节点操作显示
            mouseEnter: false,
            timer: null,
            edits: {},
            storeName: '',
            outputPlugins: getPluginColor().outputPlugins, //输出插件
            collision: getPluginColor().collision, //碰撞 keyword 两个输入点
            inputPlugins: getPluginColor().inputPlugins, //输入插件
            multiline: getPluginColor().multiline, //多线组件
            doubleInputPlugins: getPluginColor().doubleInputPlugins, //两个输入点 一个输出点
        }
    },
    methods: {
        showDetail(node) {
            const vm = this;
            if (vm.stopEvent) return;
            vm.timer && clearTimeout(vm.timer);
            vm.timer = setTimeout(() => {
                vm.$emit("showDetail", node);
            }, 300)
        },
        /**
         * 触发重命名
         * */
        onResetName(node) {
            this.timer && clearTimeout(this.timer);
            this.$set(this.edits, node.id, true);
            this.storeName = node.name;
            this.$nextTick(() => {
                this.$refs.input.focus();
                this.$refs.input.select();
            });
        },
        /**
         * 重命名
         **/
        onSubmitName(node) {
            if (node.name === "") {
                this.$message.warning("插件名不能为空");
                return;
            }
            this.$set(this.edits, node.id, false);
            if (node.name === this.storeName) return;
            this.$emit("editName", node, this.storeName);
        },
        /**
         *  显示右键菜单
         * */
        showNodeMenu(node) {
            if (this.rowData.caseId) return;
            globalBus.$emit("showNodeMenu", node);
        },
        editAttr(node) {
            const vm = this;
            vm.timer && clearTimeout(vm.timer);
            vm.$emit('editAttr', node);
        },
        // 删除节点
        deleteNode() {
            this.$emit('deleteNode', this.node)
        },
        // 编辑节点
        editNode() {
            this.$emit('editNode', this.node.id)
        },
        // 鼠标进入
        showDelete() {
            this.mouseEnter = true
        },
        // 鼠标离开
        hideDelete() {
            this.mouseEnter = false
        },
    },
    computed: {
        flowNodeContainer: {
            get() {
                return {
                    position: 'absolute',
                    top: this.node.top,
                    left: this.node.left,
                }
            }
        },
        nodeIco() {
            return this.node.ico;
        }
    }

}
</script>

<style scoped lang="less">
.modelNode {
    z-index: 1;
}

.ce-link_dot_pos {
    width: 9px;
    height: 9px;
    position: absolute;
    padding: 2px;
    z-index: 1;
}

.ce-link_point {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    border: 1px solid #1890ff;
    display: block;
    box-sizing: border-box;
    background: #fff;
}

.ce-link_diamond {
    width: 8px;
    height: 8px;
    border: 1px solid #1890ff;
    display: block;
    box-sizing: border-box;
    background: #fff;
    transform: rotate(45deg);
}

.ce-link-diamond_center {
    top: -6px;
    left: 50%;
    margin-left: -4px;
}

.ce-link_source {
    bottom: -7px;
    left: 50%;
    margin-left: -6px;
}

.ce-link_center {
    top: -6px;
    left: 50%;
    margin-left: -5px;
}

.ce-link_left {
    top: -7px;
    left: 33%;
    margin-left: -4px;
}

.ce-link_right {
    top: -7px;
    left: 66%;
    margin-left: -4.5px;
}

.node-name {
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 20px;
}

.main-content {
    .node {
        display: flex;
        align-items: center;
        border-radius: 2px;
        cursor: pointer;
        position: relative;
        background-color: #fff;

        /deep/ input {
            border: none;
            height: auto;
            line-height: 1;
            background-color: transparent;
        }

        span.node__icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 38px;
            height: 48px;
        }

        &-content {
            padding: 0 30px 0 10px;
            box-sizing: border-box;
            width: 194px;
            // white-space: nowrap;
            // overflow: hidden;
            // text-overflow: ellipsis;
        }

        .eda_icon {
            width: 20px;
            height: 20px;
        }

        &--primary {
            border: 1px solid rgba(0, 136, 255, 0.35);

            span.node__icon {
                background: rgba(#0088ff, 0.12);

                i {
                    color: #1890ff;
                }
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(0, 136, 255, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(0, 136, 255, 0.45);
            }

            .icon-cross {
                color: #1890ff;
            }
        }

        &--info {
            border: 1px solid rgba(250, 173, 20, 0.35);

            span.node__icon {
                background-color: rgba(#faad14, 0.12);

                i {
                    color: #faad14;
                }
            }

            .ce-link_point {
                border-color: #faad14;
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(250, 173, 20, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(250, 173, 20, 0.45);
            }

            .icon-cross {
                color: #faad14;
            }
        }

        &--success {
            border: 1px solid rgba(82, 196, 26, 0.35);

            span.node__icon {
                background-color: rgba(#52c41a, 0.12);

                i {
                    color: #52c41a;
                }
            }

            .ce-link_point {
                border-color: #52c41a;
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(82, 196, 26, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(82, 196, 26, 0.45);
            }

            .icon-cross {
                color: #52c41a;
            }
        }

        &--chart {
            border: 1px solid rgba(109, 135, 241, 0.35);

            span.node__icon {
                background-color: rgba(#6D87F1, 0.12);

                i {
                    color: #6D87F1;
                }
            }

            .ce-link_point {
                border-color: #6D87F1;
            }

            &:hover {
                box-shadow: 0 0 6px 0 rgba(109, 135, 241, 0.45);
            }

            &.node--active {
                box-shadow: 0 0 6px 0 rgba(109, 135, 241, 0.45);
            }

            .icon-cross {
                color: #6D87F1;
            }
        }

        &--tip-icon {
            display: flex;
            align-items: center;
            position: absolute;
            top: -7px;
            right: -7px;
            width: 14px;
            height: 14px;
            //z-index: 9;
            background-color: #fff;
            border-radius: 50%;

            i {
                font-size: 14px;
            }

            .icon-tick {
                color: #52c41a;
            }

            .icon-exclamatory {
                color: #faad14;
            }

        }

        &--tip {
            position: absolute;
            right: 8px;
            top: 50%;
            margin-top: -7px;
        }
    }
}
</style>
