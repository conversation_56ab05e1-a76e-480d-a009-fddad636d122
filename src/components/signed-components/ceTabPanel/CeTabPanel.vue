<template>
    <div class="ce-tab">
        <div v-for="tab in tabList" :key="tab.value" class="ce-tab_item" @click="tabClick(tab.value , tab)"
             :class="{'active':activeVal === tab.value}">
            <span class="ce-tab_label" :title="tab.label">{{ tab.label }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: "CeTabPanel",
    props: {
        data: {
            type: Array,
            default: () => {
                return []
            }
        },
        value: {
            type: String,
            default: ""
        }
    },
    computed: {
        tabList() {
            return this.data.reverse();
        },
        activeVal: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('update:value', val)
            }
        }
    },
    methods: {
        tabClick(val, tab) {
            this.activeVal = val;
            this.$emit("tabClick", val, tab);
        }
    },
    data() {
        return {}
    },
}
</script>

<style scoped lang="less">
.ce-tab {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    flex-direction: row-reverse;
    justify-content: flex-end;
    overflow: visible;
    position: relative;

    &_item {
        padding: 0 36px 0 26px;
        border-radius: 2px 2px 0 0;
        height: 32px;
        line-height: 32px;
        text-align: center;
        position: relative;
        clip-path: polygon(0 0,
        0 100%,
        100% 100%,
        calc(100% - 20px) 0);
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.15);

        &::after {
            content: '';
            position: absolute;
            left: 1px;
            top: 1px;
            width: calc(100% - 2px);
            height: 100%;
            background-color: #fff;
            clip-path: polygon(0 0,
            0 100%,
            100% 100%,
            calc(100% - 20px) 0);
        }

        &:hover {
            color: #0088FF;
        }
    }

    &_item:not(:last-child) {
        margin-left: -12px;
    }

    &_item.active {
        background-color: #0088FF;
        color: #fff;
        z-index: 99;
        &::after {
            background-color: #0088FF;
        }
    }

    &_label {
        min-width: 56px;
        display: block;
        position: relative;
        z-index: 10;
    }
}
</style>
