<template>
    <div class="rankCard">
        <h1 class="rankCard_title" :title="title">{{ title }}</h1>
        <ul class="rankCard_list">
            <li class="rankCard_item" v-for="(item , inx) in data" :key="inx">
                <span class="rankCard_item_info">
                    <span class="rankCard_item_info_level" :class="'bgColor'+(inx + 1)">{{ inx + 1 }}</span>
                </span>
                <span class="rankCard_item_user ml10 overLineClamp1" :title="item.username">{{item.username}}</span>
                <span class="rankCard_item_progress"><el-progress :percentage="setCount(item.modelcount)" :stroke-width="8" :format="format"></el-progress></span>
            </li>
            <no-data v-if="data.length === 0" :title="emptyTxt" top="5%">
            </no-data>
        </ul>
        <el-link :underline="false" class="rankCard_more poi" @click="modelUserPublishRankingList(1)" v-if="data.length">查看全部<i class="el-icon-arrow-right"></i></el-link>

        <el-dialog :title="title" :visible.sync="visible" :top="'5vh'" custom-class="dialog_style">
            <common-table
                    :data="dataList"
                    :columns="tableColumns"
                    :border="false"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    :pagination-total="totalCount"
                    :max-height="tableBodyH"
                    @change-current="isMock ? ()=>{} : modelUserPublishRankingList($event) "
                    @change-size="changeSize"
            >
            </common-table>
        </el-dialog>
    </div>
</template>

<script>
    import {common} from "@/api/commonMethods/common";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {coustTableH} from "@/api/commonMethods/count-table-h";
    import NoData from "@/components/no-data";
    import {servicesMixins} from "../../service-mixins/service-mixins";
export default {
    name: "rankCard",
    mixins: [ coustTableH ,common ,commonMixins ,servicesMixins],
    props: {
        data: {
            type: Array,
            default: () => []
        },
        title: {
            type: String,
            default: ""
        },
        userType:{
            type: String,
            default: "userPublish"
        },
        num:{
            type: String,
            default: "0"
        }
    },
    components: {
        NoData ,
    },
    data(){
        return{
            visible:false,
            dataList:[],
            totalCount:0,
            emptyTxt:'暂无数据',
            tableColumns:[
                {
                    prop: "username",
                    label: "姓名",
                    align: "center"
                },
                {
                    prop: "modelcount",
                    label: "数量",
                    align: "center"
                }
            ],
        }
    },
    methods:{
        format(percentage){
            let info = percentage /100 * parseFloat(this.num);
            info = info.toFixed(0);
            return `${info}`;
        },
        setCount(count){
            let num = 0;
            if(parseFloat(this.num) !== 0){
                num = parseInt(100/(parseFloat(this.num))*(parseFloat(count)))
            }
            return num;
        },
        //查询模型用户排行关注榜列表
        modelUserPublishRankingList(index){
            const vm = this, {manageServices, manageMock ,settings} = this;
            let services = vm.getServices(manageServices, manageMock);
            vm.paginationProps.currentPage = index;
            let data = {
                rankType: vm.userType,
                pageNum: index,
                pageSize: vm.paginationProps.pageSize,
            }
            services.modelUserPublishRankingList(data).then(res => {
                if (res.data.status === 0) {
                    vm.dataList = res.data.data.dataList;
                    vm.totalCount = res.data.data.totalCount;
                    vm.visible = true;
                }
            })
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.modelUserPublishRankingList(1);
        },
    }
}
</script>

<style scoped lang="less">
@textEll: {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.rankCard {
    background: #fff;
    margin-bottom:15px;

    /deep/.dialog_style{
        .el-dialog__body{
            padding-top:0px;
        }
    }

    .overLineClamp1 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    &_title {
        font-family: MicrosoftYaHei-Bold;
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.85);
        line-height: 18px;
        padding: 16px 21px;
        @textEll();
    }

    &_list{
        padding-bottom:20px;
        min-height:180px;
    }

    &_item {
        display: flex;
        align-items: center;
        margin-bottom: 14px;
        padding: 0 2rem;

        &:last-child {
            margin-bottom: 0;
        }
        &_info{
            width:20px;
            &_level {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                margin-right: 14px;
                color: rgba(0, 0, 0, 0.45);
                background: rgba(0, 0, 0, 0.04);
            }
        }
        &_user{
            width:80px;
            opacity: 0.85;
            font-size: 14px;
            color: #000000;
            line-height: 22px;
        }

        &_progress{
            flex:1;
        }
    }

    &_more{
        display: block;
        background: rgba(0,0,0,0.02);
        border-radius: 0 0 2px 2px;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: rgba(0,0,0,0.45);
    }

    .bgColor1{
        background: #EA705E;
        color: #FFFFFF;
    }
    .bgColor2{
        background: #F6C02E;
        color: #FFFFFF;
    }
    .bgColor3{
        background: #BCD782;
        color: #FFFFFF;
    }
}
</style>
