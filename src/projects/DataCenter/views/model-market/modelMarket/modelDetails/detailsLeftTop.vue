<template>
    <div class="detailsLeftTop">
        <div class="detailsLeftTop_info">
            <div class="iconLeft">
                <img :src="modelInfo.logo" v-if="isBase64.test(modelInfo.logo)">
                <svg class="list-card_ic" aria-hidden="true" v-else>
                    <use class="list-card_color" :xlink:href="setIcon(modelInfo.type)"></use>
                </svg>
            </div>
            <div class="model-details-top">
                <div class="model-details-top_title">
                    <div class="model-details-top_name mr10">
                        <span :title="modelInfo.modelName">{{ modelInfo.modelName }}</span>
                        <dg-button class="model-details-top_name_btn" type="primary"
                            :icon="modelInfo.focusType === '0' ? '' : 'el-icon-plus'" size="mini" @click="focusClick">{{
                    modelInfo.focusType === '0' ? '取消关注' : '关注' }}</dg-button>
                    </div>
                    <div>
                        <el-button v-if="showApply" type="primary" class="mr10" @click="onApplyFor">复用申请</el-button>
                        <el-link :underline="false" class="model-details-top_lookBtn" v-if="modelInfo.focusType === '0'"
                            @click="onLook">查看模型<i class="el-icon-arrow-right"></i></el-link>
                    </div>
                    <!--<dg-button class="model-details-top_lookBtn" type="text" v-if="modelInfo.focusType === '0'" @click="onLook">查看模型<i class="el-icon-arrow-right"></i></dg-button>-->
                </div>
                <div class="model-details-top_desc overLineClamp2" :title="modelInfo.desc">{{ modelInfo.desc }}</div>
                <div class="model-details-top_label">
                    <span v-for="(item, index) in modelInfo.label " :key="index">{{ item }}</span>
                    <div v-if="!modelInfo.label.length">无标签</div>
                </div>
            </div>
        </div>
        <div class="detailsLeftTop_content">
            <div class="detailsLeftTop_item" v-for="(item, index) in modelInfo.otherInfo" :key="index">
                <div class="infoNum" :style="{ color: item.color }">
                    <span :class="item.tip && item.numClass">{{ item.num }}</span>
                    <span class="infoNumTip" v-if="item.type === 'rate' && item.tip">{{ scoreLevel(item.tip) }}</span>
                </div>
                <div class="infoTitle">{{ item.title }}</div>
                <div>
                    <el-rate v-if="item.type === 'rate'" v-model="item.num" disabled text-color="#ff9900">
                    </el-rate>
                    <i v-if="item.type === 'icon'" class="dg-iconp" :class="item.icon"></i>
                </div>
            </div>
        </div>
        <!--仪表盘详情页面-->
        <VisualizationEdit ref="visual_panel" v-if="showEditPanel" @closePanel="closeEditPanel" :isCase="true" />
        <!--主题门户详情页面-->
        <edit-panel v-if="showHomehuEditPanel" ref="protal_panel" @closePanel="closeHomehuEditPanel"
            :isCase="true"></edit-panel>
    </div>
</template>

<script>
import VisualizationEdit from '@/projects/DataCenter/views/visualizing/edit-panel/VisualizationEdit';
import EditPanel from "@/projects/DataCenter/views/homeHu/edit-panel";
import { mapGetters } from "vuex"
export default {
    name: "detailsLeftTop",
    props: {
        modelInfo: Object,
        modelDataInfo: Object,
    },
    components: {
        VisualizationEdit,
        EditPanel
    },
    computed: {
        // 显示申请
        // 不能申请创建者为自己的模型
        // 如果已经有审批中的模型，无法再申请
        showApply() {
            const { modelDataInfo, applyAble } = this;
            return applyAble && modelDataInfo.publishUser && this.userInfo.id !== modelDataInfo.publishUser.userId && modelDataInfo.canApply;
        },
        ...mapGetters(["userInfo"]),
        scoreLevel() {
            return (val) => {
                return val ? `(${val})` : '';
                /* if (val <= 2) {
                    return '低'
                } else if (val <= 4) {
                    return '中'
                } else {
                    return '高'
                } */
            }
        }
    },
    data() {
        return {
            isBase64: /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,
            showEditPanel: false,
            showHomehuEditPanel: false,
            applyAble: true, // 添加个变量控制申请按钮显示的逻辑，提交申请后，则改为 false
        }
    },
    methods: {
        setIcon(type) {
            let info = 'DASHBOARDS';
            switch (type) {
                case "DASHBOARDS":
                    info = "#icon-dashboard";
                    break;
                case "TRANS":
                    info = "#icon-model1";
                    break;
                case "SCRIPT":
                    info = "#icon-AImodel";
                    break;
                case "PORTAL":
                    info = "#icon-theme";
                    break;
                default:
                    info = "#icon-dashboard";
                    break;
            }
            return info;
        },
        //查看
        onLook(row) {
            if (this.modelInfo.type === 'TRANS') {
                this.lookModel()
            }
            else if (this.modelInfo.type === 'DASHBOARDS') {
                this.lookDash(this.modelInfo)
            }
            else if (this.modelInfo.type === 'PORTAL') {
                this.openHomehuPanel(this.modelInfo);
            }
            else {
                this.lookAI(this.modelInfo)
            }
        },
        onApplyFor() {
            const vm = this, { modelDataInfo, userInfo } = this;
            let layer = this.$dgLayer({
                title: '申请',
                content: require("@/projects/DataCenter/views/model-market/dialog/applyForModel.vue"),
                maxmin: false,
                props: {
                    modelDataInfo,
                    userInfo,
                },
                area: ["800px", "600px"],
                on: {
                    submit() {
                        layer.close(layer.dialogIndex);
                        vm.applyAble = false;
                        vm.$emit("reload");
                    }
                }
            })
        },
        //查看ai模型
        lookAI(row) {
            const vm = this;
            row.transId = row.transId;
            row.modelName = row.modelName;
            let layer = this.$dgLayer({
                title: "查看模型",
                content: require("@/projects/DataCenter/views/AIModeling/dialog/new-model/index.vue"),
                maxmin: false,
                props: {
                    isLook: true,
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    },
                },
            });
            layer.$children[0].show(row);
        },
        //查看仪表盘
        lookDash() {
            this.showEditPanel = true;
            this.$nextTick(() => {
                this.$refs.visual_panel.getRouterData(this.modelInfo.transId, '', '', '');
            })
        },
        closeEditPanel(groupId, filterTxt) {
            this.showEditPanel = false;
        },
        //查看模型
        lookModel() {
            const vm = this;
            let data = {
                transId: vm.modelInfo.transId,
                modelName: vm.modelInfo.modelName,
                isNew: false,
                caseId: vm.modelInfo.transId,
            }
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: "-1",
                    isCase: true,
                    isLook: true,
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        vm.showModelingEditPanel = false;
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab(data, false);
        },
        closeHomehuEditPanel() {
            this.showHomehuEditPanel = false;
        },
        //查看主题门户
        openHomehuPanel(row) {
            this.showHomehuEditPanel = true;
            row.id = row.transId;
            row.name = row.modelName;
            this.$nextTick(() => {
                this.$refs.protal_panel.panelInit("", row.transId, row, "");
            })
        },
        //关注
        focusClick() {
            this.$emit('focusClick');
        }
    }
}
</script>

<style scoped lang="less">
.detailsLeftTop {
    .list-card_color {
        fill: rgba(0, 136, 255, 0.8);
    }

    .model-details-top {
        margin-top: 20px;
        width: calc(100% - 125px);
        padding-left: 30px;

        &_title {
            display: flex;
            justify-content: space-between;

            span {
                font-family: MicrosoftYaHei-Bold;
                font-size: 24px;
                color: rgba(0, 0, 0, .85);
                font-weight: bold;
                margin-right: 20px;
            }
        }

        &_name {
            display: flex;
            align-items: center;

            span {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            &_btn {
                min-width: auto;
            }
        }

        &_lookBtn {
            width: 100px;
            white-space: nowrap;
        }

        &_label {
            span {
                border: 1px solid rgba(24, 144, 255, 0.60);
                border-radius: 13px;
                padding: 2px 10px;
                margin-right: 10px;
                font-family: MicrosoftYaHei;
                font-size: 12px;
                color: #1890FF;
                text-align: center;
            }

            div {
                color: rgba(0, 0, 0, 0.45);
                font-size: 12px;
            }
        }

        &_desc {
            margin-top: 16px;
            font-family: MicrosoftYaHei;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 21px;
            margin-bottom: 5px;
        }
    }

    .overLineClamp2 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    &_info {
        height: 150px;
        display: flex;

        .iconLeft {
            width: 120px;
            height: 120px;
            min-width: 120px;
            background: rgba(0, 136, 255, 0.09);
            border-radius: 8px;
            text-align: center;
            margin-top: 30px;
            align-items: center;
            display: flex;
            justify-content: center;
        }

        .iconLeft i {
            font-size: 120px;
            background: rgba(0, 136, 255, 0.09);
            border-radius: 8px;
            color: #0088FF;
        }

        img {
            border-radius: 8px;
            width: 120px;
            height: 120px;

        }

        .list-card_ic {
            width: 100px;
            height: 100px;
        }

    }

    &_content {
        display: flex;
        padding-bottom: 20px;
        color: rgba(0, 0, 0, .24);



    }

    &_item {
        border-right: 1px solid rgba(0, 0, 0, 0.09);
        flex: 1;
        text-align: center;
        position: relative;
    }

    &_item:last-child {
        border-right: 0;
    }
}

.infoNumTip {
    font-size: 14px;
    padding: 0 6px;
}

.num-rate {
    padding-left: 30px;
}

.infoNum {
    font-family: ArialMT;
    font-size: 30px;
    color: rgba(0, 0, 0, 0.85);

}

.infoTitle {
    font-family: MicrosoftYaHei;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
}
</style>
