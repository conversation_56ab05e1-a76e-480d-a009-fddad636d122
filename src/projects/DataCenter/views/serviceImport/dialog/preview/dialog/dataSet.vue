<template>
    <div class="height100">
        <common-table
                :data="tableData"
                :columns="tHeadData"
                tooltip-effect="light"
                cell-class-name="ce-ellipsis"
                size="mini"
        >
            <template slot="belongType" slot-scope="{row }">
                <span>{{ row.belongType === 'PHYSICAL' ? '源数据集' : row.belongType === 'SELF_HELP'? '自助数据集' : 'SQL数据集' }}</span>
            </template>
        </common-table>
    </div>
</template>

<script>
    import CommonTable from '@/components/common/CommonTable'

    export default {
        name: "dataSet",
        components: {
            CommonTable
        },
        props: {
            res : Object,
        },
        data() {
            return {
                tableData: [],
                tHeadData: [
                    {
                        label: '数据集名称',
                        prop: 'name',
                        align: 'center',
                    },
                    {
                        label: '数据集类型',
                        prop: 'belongType',
                        align: 'center'
                    },/* {
                        label: '创建人',
                        prop: 'operateUserName',
                        align: 'center'
                    },*/{
                        label : '更新时间',
                        prop : 'operateTime'
                    }
                ]
            }
        },
        methods: {
            init() {
                this.tableData = this.res.logicInfos;
            }
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>

</style>