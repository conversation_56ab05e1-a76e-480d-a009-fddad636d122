<template>
    <div class="RuleEditingPanel ce-plug_cont" v-loading="settings.loading">

        <div class="ce-tab_cont">
            <div class="ce-plug_attr" >
                <div style="text-align: left;padding-bottom: 10px">
                    <span style="font-size:16px;line-height: 20px;">输入参数配置</span>
                </div>
                <el-form
                        ref="form"
                        size="small"
                        :model="form"
                        status-icon
                        label-width="100px"
                        class="demo-ruleForm"
                        :rules="rules"
                >
                    <el-form-item label="分组字段" prop="groupField">
                        <el-select v-model="form.groupField" size="mini">
                            <el-option v-for="opt in tableOption"
                                       :key="opt.value"
                                       :value="opt.value"
                                       :label="opt.label"
                                       filterable
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="转换字段" prop="conversionField">
                        <el-select v-model="form.conversionField" size="mini">
                            <el-option v-for="opt in tableOption"
                                       :key="opt.value"
                                       :value="opt.value"
                                       :label="opt.label"
                                       filterable
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="转换值字段" prop="conversionValueField">
                        <el-select v-model="form.conversionValueField" size="mini">
                            <el-option v-for="opt in tableOption"
                                       :key="opt.value"
                                       :value="opt.value"
                                       :label="opt.label"
                                       filterable
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div style="text-align: left;padding-bottom: 10px">
                    <span style="font-size:16px;line-height: 20px;">输出参数配置</span>
                </div>
                <CommonTable
                        :data="ruleTableData"
                        :columns="ruleHeader"
                        height="55%"
                >
                    <template slot="header-ruleOperate" slot-scope="scope">
                        <span>操作</span>
                        <em class="icon ce_link f14 ml5" title="添加" @click="addRuleEdit">&#xe6e1;</em>
                    </template>
                    <template slot="conversionContent" slot-scope="scope">
                        <div :title="scope.row.conversionContent">
                            <el-input
                                    size="mini"
                                    v-model="scope.row.conversionContent"

                            >
                            </el-input>
                        </div>
                    </template>
                    <template slot="targetField" slot-scope="scope">
                        <el-input size="mini" v-model.trim.number="scope.row.targetField"
                                  :disabled="scope.row.disabled"
                                  @input="checkTargetField($event , scope)"
                        ></el-input>
                    </template>
                    <template slot="ruleOperate" slot-scope="scope">
                        <em class="icon ce_link p5"
                            v-for="( ico , key ) in ruleOperateIcon"
                            :key="key"
                            :title="ico.label"
                            v-html="ico.icon"
                            @click="ico.clickFn(scope.row , scope.$index)"
                        ></em>
                    </template>
                </CommonTable>
            </div>

        </div>
        <!--<div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>-->
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import TaskLog from "../../modeling/flowPanel/TaskLog"

    //import SettingSenior from '../component/SettingSenior'
    import CommonTable from "@/components/common/CommonTable";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import MlSqlEdit from "./RowDenormaliser"
    import {servicesMixins} from "../service-mixins/service-mixins"

    export default {
        name: "rowDenormaliserMeta",
        props: {
            sourceData: Object,
            rowData: Object
        },
        mixins: [common, commonMixins, servicesMixins],
        components: {
            //SettingSenior,
            CommonTable,
            PreView,
            MlSqlEdit,
            TaskLog
        },
        data() {
            return {
                tableOption: [],
                form: {
                    groupField: "",
                    conversionField: "",
                    conversionValueField: "",
                    outputParameterCfgJson: ""
                },
                outputParameterCfgMap: {},
                defaultProps: {
                    value: 'value',
                    label: "label",
                    children: "children",
                },
                listsData: {
                    list: [],
                    columns: [{
                        label: "转换字段",
                        prop: "转换字段",
                        align: 'center',
                        minWidth: '160px'
                    }, {
                        label: "目标字段",
                        prop: "目标字段",
                        align: 'center',
                        minWidth: '160px'
                    }],
                },
                ruleHeader: [
                    {
                        label: '转换内容',
                        prop: 'conversionContent',
                        align: 'center',
                        minWidth: '140px'
                    }, {
                        label: '目标字段',
                        prop: 'targetField',
                        align: 'center',
                        minWidth: '140px'
                    },
                    {
                        label: '',
                        prop: 'ruleOperate',
                        align: 'center',
                        width: '90px',
                        fixed: "right"
                    },],
                cacheTableId: '',
                groupId: '',
                stepId: '',
                canSave: false,
                isSeniorShow: {},
                results: [],
                ruleTableData: [],
                ruleTypeOptions: [
                    {}
                ],
                ruleOperateIcon: {
                    delete: {
                        label: '删除',
                        icon: '&#xe65f;',
                        clickFn: this.deleteFn
                    }
                },
                graphIds: [],
                row: {
                    columnCode: 'scriptMeta',
                    columnId: '',
                    columnName: 'scriptMeta',
                    length: '',
                    precision: '',
                    serviceOrgId: '',
                    type: ''
                },
                cacheTableList: [],
                stepTableName: '',
                rules: {
                    groupField: [
                        {required: true, message: "分组字段不能为空", trigger: 'change'}
                    ],
                    conversionField: [
                        {required: true, message: "转换字段不能为空", trigger: 'change'}
                    ],
                    conversionValueField: [
                        {required: true, message: "转换值字段不能为空", trigger: 'change'}
                    ],
                }
            }

        },
        methods: {
            save(...args){
              this.saveFn(...args);
            },
            preview(){
                this.previewFn();
            },
            getOutId(type, serviceOrgId) {
                for (let k = 0; k < this.ruleTableData.length; k++) {
                    if (serviceOrgId === this.ruleTableData[k].ruleTableData) {
                        this.ruleTableData[k].type = type;
                    }
                }
            },
            typeCheck(val, row, target) {
                if (isNaN(val)) {
                    isNaN(parseFloat(val)) ?
                        row[target] = '' :
                        row[target] = parseFloat(val);
                }
            },
            editLabelMark(data) {
                this.getStepTableName();
                this.$refs.mlSqlEdit.show(this.rowData);
            },
            async getStepTableName(data) {
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                let transId = vm.rowData.transId;
                let res = await vm.$axios.get("/mlsql/getAfterTableName?id=" + transId + "&stepId=" + vm.stepId);
                if (res.data.status === 0) {
                    vm.stepTableName = data.name;
                }
            },
            deleteFn(row, index) {
                const vm = this, {pluginServices, pluginMock} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                vm.confirm('删除', '此操作将永久删除该配置内容, 是否继续?', () => {
                    if (row.serviceOrgId !== null && row.serviceOrgId !== '' && row.serviceOrgId !== undefined) {
                        vm.graphIds.push(row.serviceOrgId);
                    }
                    vm.ruleTableData.splice(index, 1);
                    vm.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                })
            },
            checkConversionContent(e,scope){
                let s = e.charAt(0);
                let numReg = /^[0-9]*$/
                let numRe = new RegExp(numReg);
                if(numRe.test(s)){
                    e = e.replace(s,'');
                }
                scope.row.conversionContent = e.replace(/[\u4e00-\u9fa5]/ig,'');
            },
            checkTargetField(e,scope){
                let s = e.charAt(0);
                let numReg = /^[0-9]*$/
                let numRe = new RegExp(numReg);
                if(numRe.test(s)){
                    e = e.replace(s,'');
                }
                scope.row.targetField = e.replace(/[\u4e00-\u9fa5]/ig,'');
            },
            async saveFn(saveName,showTip) { //保存后才能预览
                this.$refs.form.validate(value =>{
                    if(value){
                        const vm = this, {pluginServices, pluginMock, settings} = this;
                        let services = vm.getServices(pluginServices, pluginMock);
                        if(vm.ruleTableData.length === 0){
                            vm.$message.warning("输出配置不能为空!");
                            return;
                        }
                        for (let k = 0; k < vm.ruleTableData.length; k++) {
                            for (let key in vm.ruleTableData[k]) {
                                if (key !== 'serviceOrgId' && key !== 'columnName' && vm.ruleTableData[k][key] === '') {
                                    if (key === 'conversionContent') {
                                        vm.$message.warning("输出内容不能为空!");
                                        return;
                                    } else if (key === 'targetField') {
                                        vm.$message.warning("目标字段不能为空!");
                                        return;
                                    }
                                }
                            }
                        }
                        let outputParameterCfgMap = [];
                        vm.ruleTableData.forEach((item, i) => {
                            outputParameterCfgMap.push({
                                conversionContent: item.conversionContent,
                                type: item.type === "double" ? "Double" : item.type,
                                length: 200,
                                targetField: item.targetField,
                                columnCode: item.targetField,
                                columnId: item.columnId,
                            });
                        });
                        let rowDenormaliserMeta = {
                            groupField: vm.form.groupField,
                            conversionField: vm.form.conversionField,
                            conversionValueField: vm.form.conversionValueField,
                            outputParameterCfgJson: JSON.stringify(outputParameterCfgMap)
                        };
                        settings.loading = true;
                        services.saveDenormaliser(vm.sourceData.id, rowDenormaliserMeta, settings).then(res => {
                            if (res.data.status === 0) {
                                showTip && this.$message.success("保存成功!");
                                if(saveName)saveName();
                                vm.$emit("setPreviewAble");

                            }
                        });
                        settings.loading = false;
                    }
                });
            },

            getTableName(id, option) {
                for (let i = 0; i < option.length; i++) {
                    if (option[i].id === id) {
                        let tableInfo = {
                            tableId: id,
                            tableName: option[i].code
                        };
                        this.cacheTableList.push(tableInfo);
                    } else if (option[i].children !== null && option[i].children.length !== 0) {
                        this.getTableName(id, option[i].children);
                    }
                }
            },
            previewFn() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            addRuleEdit() {
                const vm = this;
                if (vm.form.conversionField === '' || vm.form.conversionField === undefined){
                    return vm.$message.info("请选择转换字段");
                }else{
                    vm.ruleTableData.push({
                        columnName: '',
                        type: this.getType(),
                        length: '',
                        precision: '',
                        columnCode: '',
                        columnId: this.ruleTableData.length + 1,
                        conversionContent: '',
                        targetField: '',
                    });
                }
            },
            getType() {
                let type = null;
                const vm = this;
                vm.tableOption.forEach(item => {
                    if (item.value === vm.form.conversionField) {
                        type = item.type;
                    }
                });
                return type;
            },
            querySearch(queryString, cb) {
                let filterResult = queryString ? this.results.filter(this.createStateFilter(queryString)) : this.results;
                cb(filterResult);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            nameSelect(item, row) {
                // console.log(item);
                row.length = item.length;
                row.precision = item.precision;
                row.columnName = item.columnName;
                row.type = this.ruleTypeOptions.filter(rule => {
                    return rule.label === item.type;
                })[0].value;
                row.columnCode = item.columnCode;
                row.columnId = item.columnId;
                row.serviceOrgId = item.serviceOrgId;
                // console.log(row);
            },

            //初始化页面
            async init(node) {
                const vm = this;
                vm.stepId = node.id;
                await this.$axios.get("/denormaliser/queryData?tranStepId=" + node.id).then(res => {
                    vm.tableOption = res.data.data.inputColumn;
                    vm.tableOption.forEach(item => {
                        if (item.columnName == null) {
                            item.label = item.columnCode;
                        } else item.label = item.columnName;
                        item.value = item.columnCode;
                        // item.type = item.type;
                        return item;
                    });
                    vm.form.groupField = res.data.data.groupField || "";
                    vm.form.conversionField = res.data.data.conversionField || "";
                    vm.form.conversionValueField = res.data.data.conversionValueField || "";
                    vm.form.outputParameterCfgJson = res.data.data.outputParameterCfgJson || "";
                    let outputParameterCfgMap = JSON.parse(res.data.data.outputParameterCfgJson);
                    outputParameterCfgMap && outputParameterCfgMap.forEach((item, i) => {
                        vm.ruleTableData.push({
                            columnName: item.targetField,
                            type: item.type,
                            length: item.length,
                            precision: '',
                            columnCode: item.targetField,
                            columnId: item.columnId,
                            conversionContent: item.conversionContent,
                            targetField: item.targetField,
                        });
                    });
                })
            },

            saveScript(script) {
                const vm = this;
                vm.mlSqlScript = script;

            }

        },
        created() {
            this.init(this.sourceData);
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: false,
                isSampleExpr: false,
                isThread: false,
                isException: false,
            }
        },
    }
</script>

<style scoped>
    .ce-add_b {
        line-height: 400px;
        border: 3px dashed #ddd;
        cursor: pointer;
        text-align: center;
        color: #999;
        font-size: 18px;
        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>
