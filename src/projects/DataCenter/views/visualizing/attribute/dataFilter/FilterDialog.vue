<template>
    <div class="filterDialog">
        <el-dialog
                :title="dialogTitle"
                :visible.sync="dialogVisible"
                :width="width"
                :append-to-body="true"
                :modal-append-to-body="true"
                :close-on-click-modal="false"
                @closed="clear"
        >
            <div v-if="reNew">
<!--                <FilterTime ref="filter" v-if="dataType === 'time'" />-->
<!--                <FilterNumber ref="filter" v-if="dataType === 'number'" />-->
<!--                <FilterString ref="filter" v-if="dataType === 'string'" />-->
                <FilterCommon ref="filter" />
            </div>

            <div slot="footer" >
                <el-button @click="save" size="mini" type="primary">确定</el-button>
                <el-button @click="close" size="mini">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    // import FilterTime from './FilterTime'
    // import FilterString from './FilterString'
    // import FilterNumber from './FilterNumber'
    import FilterCommon from './FilterCommon'
    export default {
        name: "FilterDialog",
        components : {
            // FilterTime ,
            // FilterString ,
            // FilterNumber ,
            FilterCommon
        } ,
        data() {
            return {
                dialogVisible: false,
                dialogTitle: '过滤条件设置',
                width: '1060px',
                reNew: false,
                dataType: '' ,
                filterSource : {} ,
                filterIndex : '' ,
                isMenuEvent : false
            }
        },
        methods: {
            open(data , inx , isMenu) {
                this.dataType = data.data.format;
                this.dialogVisible = true;
                this.reNew = true;
                this.init(data , inx);
                this.isMenuEvent = !!isMenu;
            },
            init(data , index){
                this.filterSource = data;
                this.filterIndex = index;
                this.$nextTick(() =>{
                    this.$refs.filter.initValue(data);
                })
            },
            close() {
                this.dialogVisible = false;
            },
            save() {
                let result = this.$refs.filter.getResult();
                //console.log(result);
                if( result ){
                    this.$emit('filterData' , result , this.filterIndex , this.isMenuEvent , this.filterSource );
                    this.close();
                }

            },
            clear() {
                this.reNew = false;
            },
            isPass(val){
                this.verification = val;
            }
        }
    }
</script>

<style scoped>

</style>
