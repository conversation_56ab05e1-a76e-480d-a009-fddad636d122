<!--自助数据集-->
<script>

import panzoom from "panzoom";

class ItemModel {
    constructor(props) {
        this.id = props.id;
        this.name = props.name || props.label || props.code;
        this.code = props.code;
        this.tableId = props.tableId;
        this.sourceId = props.sourceId;
        this.left = props.left;
        this.top = props.top;
        this.sourceLeft = props.sourceLeft;
        this.sourceTop = props.sourceTop;
        this.targets = props.targets || [];
        this.joinType = props.joinType;
        this.joinOption = props.joinOption;
        this.sourceTableId = props.sourceTableId;
        this.dbType = props.dbType;
    }
}


export default {
    name: "associationDataset",
    render() {
        const {
            tableList,
            itemWidth,
            linkWidth,
            dragOver,
            ghostList,
            showGhost,
            itemDrop,
            ghostTip,
            addDrop,
            addDragover,
            activeGhostItem,
            removeItem,
            joinSet,
            emptyTxt,
            showList,
        } = this, {filterClass} = this.$options.filters;
        return (
                <div class="as-dataset-outer"
                     onDragover={dragOver}
                     onDrop={itemDrop}
                >
                    { !tableList.length && <empty class="as-dataset-empty" value={emptyTxt}></empty>}
                    <div class="as-dataset-drag" ref="drag">

                    {
                        showList && tableList.map(item => {
                            return (
                                    <ul class="as-dataset-wrapper">
                                        <li class="as-dataset-item" style={
                                            {
                                                'width': `${itemWidth}px`,
                                                'left': `${item.left}px`,
                                                'top': `${item.top}px`
                                            }}>
                                            <div class="as-dataset-item-icon">
                                                <i class="dg-iconp icon-database"/>
                                            </div>
                                            <div class="as-dataset-item-input" title={item.name}>
                                                <div class="as-dataset-item-name">
                                                    <span>{item.name}</span>
                                                </div>
                                            </div>
                                            {item.targets && !item.targets.length && <div
                                                    onClick={() => removeItem(item)}
                                                    class="as-dataset-item-close">
                                                <i class="el-icon-remove-outline"/>
                                            </div>}
                                        </li>
                                        {item.joinType && <li class={['as-dataset-link' , {'as-dataset-link-warning' : !item.joinOption.joinVo}]}
                                                              onClick={()=>joinSet(item)}
                                                              style={{
                                                                  'width': `${linkWidth}px`,
                                                                  'left': `${item.left - linkWidth}px`,
                                                                  'top': `${item.top}px`
                                                              }}>
                                            <i class={['as-dataset-link-icon', filterClass(item.joinType)]}/>
                                            <div class="as-dataset-link-line horizontal" style={{
                                                'width': `${item.top === item.sourceTop ? '110px' : '90px'}`,
                                            }}/>
                                            <div class="as-dataset-link-line vertical" style={{
                                                'height': `${item.top - item.sourceTop}px`,
                                            }}/>
                                        </li>}
                                    </ul>
                            )
                        })
                    }
                    {
                        showGhost && ghostList.map(li => {
                            return (
                                    <ul class={['as-dataset-wrapper', {'as-dataset-ghost-active': activeGhostItem === li.id}]}
                                        onDragover={addDragover}
                                        onDrop={addDrop}
                                        data-id={li.id}
                                    >
                                        <li class="as-dataset-ghost-item"
                                            style={
                                                {
                                                    'width': `${itemWidth}px`,
                                                    'left': `${li.left}px`,
                                                    'top': `${li.top}px`
                                                }}
                                        >{ghostTip}</li>
                                        <li class="as-dataset-ghost-link"
                                            style={{
                                                'width': `${linkWidth}px`,
                                                'left': `${li.left - linkWidth}px`,
                                                'top': `${li.top}px`
                                            }}
                                        >
                                            <div class="as-dataset-link-line horizontal" style={{
                                                'width': `${li.top === li.sourceTop ? '110px' : '90px'}`,
                                            }}/>
                                            <div class="as-dataset-link-line vertical" style={{
                                                'height': `${li.top - li.sourceTop}px`,
                                            }}/>
                                        </li>
                                    </ul>
                            )
                        })
                    }
                    </div>
                </div>
        )
    },
    props: {
        createItemInter: {
            type: Function,
            default() {
                return Promise.resolve(new Date().getTime().toString());
            }
        },
        // 数据模型
        newItemModel: {
            type: Function,
            default(props) {
                return new ItemModel(props);
            }
        },
        data: {
            type: Array,
            default: () => {
                return [];
            }
        },
        checkStrictly : {
            type : Boolean ,
            default : false
        },
    },
    computed: {
        tableList: {
            get() {
                return this.data.map(li => this.newItemModel(li));
            },
            set(val) {
                this.$emit("update:data", val);
            }
        }
    },
    data() {
        return {
            itemWidth: 220,
            linkWidth: 110,
            itemDistance: 60,
            showGhost: false,
            ghostList: [],
            ghostTip: '拖拽左侧表至此添加关联表',
            ghostTimer: null,
            addTimer: null,
            activeGhostItem: "",
            emptyTxt : '请从左侧拖拽数据表开始创建',
            showList : true
        }
    },
    filters: {
        // 过滤icon
        filterClass(relation) {
            switch (relation) {
                case "INNER":
                    return "dg-iconp icon-union-b";
                case "LEFT":
                    return "dg-iconp icon-union-d";
                case "RIGHT":
                    return "dg-iconp icon-union-c";
                case "FULL OUTER":
                    return "dg-iconp icon-union-a";
                default:
                    return "dg-iconp";
            }
        },
    },
    mounted() {
        this.initPanZoom();
    },
    methods: {
        checkDataFn(data){
            return this.tableList.some(li => li.tableId === data.id);
        },
        joinSet(item){
            let source = this.tableList.find(li => li.id === item.sourceId);
            this.$emit("joinSettings" , item , source);
        },
        initPanZoom() {
            const mainContainer = this.$refs.drag;
            const vm = this;
            const mainContainerWrap = mainContainer.parentNode;
            const pan = panzoom(mainContainer, {
                smoothScroll: false,
                bounds: true,
                // autocenter: true,
                zoomDoubleClickSpeed: 1,
                minZoom: 0.5,
                maxZoom: 2,
                //设置滚动缩放的组合键，默认不需要组合键
                beforeWheel: (e) => {
                    // let shouldIgnore = !e.ctrlKey
                    // return shouldIgnore
                },
                beforeMouseDown: function (e) {
                    // allow mouse-down panning only if altKey is down. Otherwise - ignore
                    let shouldIgnore = e.ctrlKey;
                    return shouldIgnore;
                }
            });
            // 缩放时设置jsPlumb的缩放比率
            pan.on("zoom", e => {
                const {x, y, scale} = e.getTransform();

            });
            pan.on("panend", (e) => {
                const {x, y, scale} = e.getTransform();
            })

            // 平移时设置鼠标样式
            mainContainerWrap.style.cursor = vm.tableList.length ? "grab" : 'auto';
            mainContainerWrap.addEventListener("mousedown", function wrapMousedown() {
                this.style.cursor = vm.tableList.length ? "grabbing" : 'auto';
                mainContainerWrap.addEventListener("mouseout", function wrapMouseout() {
                    this.style.cursor = vm.tableList.length ? "grab" : 'auto';
                });
            });
            mainContainerWrap.addEventListener("mouseup", function wrapMouseup() {
                this.style.cursor = vm.tableList.length ? "grab" : 'auto';
            });
        },
        /**
         * 删除节点
         */
        removeItem(item) {
            const {tableList} = this;
            this.tableList = tableList.filter(li => li.id !== item.id);
            if (item.sourceId === undefined) return;
            let source = tableList.find(li => li.id === item.sourceId);
            source.targets = source.targets.filter(ta => ta !== item.id);
            this.resetNodePos();
        },
        /**
         * 节点位置重置
         */
        resetNodePos() {
            let vm = this, {tableList, itemDistance , itemWidth , linkWidth} = vm;
            vm.tableList.forEach((item, idx) => {
                if (idx === 0) {
                    item.left = 0;
                    item.top = 0;
                }
                if (item.targets.length) {
                    let targetNodes = tableList.filter(li => item.targets.includes(li.id));
                    let boxDis = 0;
                    targetNodes.forEach((no, i) => {
                        no.sourceTop = item.top;
                        no.sourceLeft = item.left;
                        if (i) {
                            no.top = no.sourceTop + (boxDis + i) * itemDistance;
                        } else {
                            no.top = no.sourceTop;
                        }
                        no.left = item.left + itemWidth + linkWidth;
                        boxDis += vm.getMaxTargetLen(no.targets);
                    })
                }
            })
        },
        getMaxTargetLen(targets) {
            let len = targets.length;
            const {tableList} = this;
            if (len) {
                let nodes = tableList.filter(li => targets.includes(li.id));
                for (let node of nodes) {
                    let nLen = this.getMaxTargetLen(node.targets);
                    len += nLen;
                }
            }
            return len;
        },
        async addDrop(e) {
            this.showGhost = false;
            let gItem = e.currentTarget.dataset.id ?
                    this.ghostList.find(li => li.id === e.currentTarget.dataset.id) : null;
            if (!gItem) return;
            let data = e.dataTransfer.getData("drag_data");
            if (!data) return;
            data = JSON.parse(data);
            if(this.checkStrictly && this.checkDataFn(data)) return;
            let id = await this.createItemInter();
            let source = this.tableList.find(it => it.id === gItem.sourceId);
            source.targets.push(id);
            let newItem = {
                ...gItem,
                ...data,
                id,
                name: data.name,
                tableId: data.id,
                targets: [],
                joinType: 'INNER',
                joinOption: {}
            };
            let itemModel = this.newItemModel(newItem);
            this.tableList.push(itemModel);
            this.tableList = this.tableList;
            this.$nextTick(()=>{
                this.joinSet(itemModel);
            })
            this.resetNodePos();
        },
        addDragover(e) {
            this.activeGhostItem = e.currentTarget.dataset.id;
            if (this.addTimer !== null) {
                clearTimeout(this.addTimer);
            }
            this.addTimer = setTimeout(() => {
                this.activeGhostItem = "";
                this.addTimer = null;
            }, 100);
            e.preventDefault();
            e.stopPropagation();
        },
        async itemDrop(e) {
            if (this.tableList.length) return;
            let data = e.dataTransfer.getData("drag_data");
            if (!data) return;
            data = JSON.parse(data);
            let id = await this.createItemInter();
            let newItem = {
                ...data,
                id,
                left: 0,
                top: 0,
                name: data.name,
                tableId: data.id,
                targets: [],
                joinType: '',
                joinOption: {}
            };
            this.tableList.push(this.newItemModel(newItem));
            this.tableList = this.tableList; //触发compute
        },
        dragOver(e) {
            this.showGhost = true;
            if (this.ghostTimer !== null) {
                clearTimeout(this.ghostTimer);
            }
            this.ghostTimer = setTimeout(() => {
                if (!this.addTimer) {
                    this.showGhost = false;
                    this.ghostTimer = null;
                }
            }, 100);
            this.createGhostList();
            e.preventDefault();
            e.stopPropagation();
        },
        createGhostList() {
            const vm = this, {countItemPos} = vm;
            this.ghostList = [];
            this.tableList.forEach((tab, i) => {
                let {left, top} = countItemPos(tab);
                let id = new Date().getTime().toString() + i;
                let item = {
                    id,
                    sourceId: tab.id,
                    sourceLeft: tab.left,
                    sourceTop: tab.top,
                    sourceTableId: tab.tableId,
                    left, top,
                };
                vm.ghostList.push(item);
            });
        },
        countItemPos(tab) {
            const {getMaxAttr, itemWidth, linkWidth} = this, {targets} = tab;
            let left = 0, top = 0;
            let targetNodes = this.tableList.filter(tab => targets.includes(tab.id));
            if (targetNodes.length) {
                left = getMaxAttr(targetNodes, 'left');
                top = getMaxAttr(targetNodes, 'top') + 60;
            } else {
                left = tab.left + itemWidth + linkWidth;
                top = tab.top;
            }
            return {left, top};
        },
        getMaxAttr(targetNodes, key) {
            if (targetNodes.length === 1) {
                return targetNodes[0][key];
            }
            return targetNodes.reduce((a, b) => a[key] > b[key] ? a[key] : b[key]);
        }
    }
}
</script>

<style scoped lang="less" src="./dataset.less"></style>
