
!function(t,e,i){function a(t,e){for(;(t=t.parentElement)&&!t.classList.contains(e););return t}GAMap.drawVersion="1.0.0+9e103aa",GAMap.Draw={},GAMap.drawLocal={draw:{toolbar:{actions:{title:"取消绘制",text:"取消"},finish:{title:"结束绘制",text:"结束"},undo:{title:"删除最后一点",text:"删除最后一点"},buttons:{polyline:"绘制线",polygon:"绘制面",rectangle:"绘制矩形",circle:"绘制圆",marker:"绘制地标"}},handlers:{circle:{tooltip:{start:"点击并拖拽绘制圆"},radius:"半径"},circlemarker:{tooltip:{start:"点击放置地标"}},marker:{tooltip:{start:"点击地图放置一个地标"}},polygon:{tooltip:{start:"点击开始绘制图形",cont:"继续点击绘制图形",end:"点击第一个点结束绘制"}},polyline:{error:"<strong>错误:</strong> 图形边界不能相交!",tooltip:{start:"点击开始绘制线",cont:"点击继续绘制线",end:"点击第一个点结束绘制"}},rectangle:{tooltip:{start:"点击拖拽绘制矩形"}},simpleshape:{tooltip:{end:"释放鼠标结束绘制"}}}},edit:{toolbar:{actions:{save:{title:"保存更改",text:"保存"},cancel:{title:"取消编辑, 放弃所有更改",text:"取消"},clearAll:{title:"清空所有图层",text:"清空"}},buttons:{edit:"编辑图层",editDisabled:"没有可编辑图层",remove:"删除图层",removeDisabled:"没有图层可删除"}},handlers:{edit:{tooltip:{text:"拖拽控制点编辑图元",subtext:"点击取消按钮放弃更改"}},remove:{tooltip:{text:"点击要删除的图元"}}}}},GAMap.Draw.Event={},GAMap.Draw.Event.CREATED="draw:created",GAMap.Draw.Event.EDITED="draw:edited",GAMap.Draw.Event.DELETED="draw:deleted",GAMap.Draw.Event.DRAWSTART="draw:drawstart",GAMap.Draw.Event.DRAWSTOP="draw:drawstop",GAMap.Draw.Event.DRAWVERTEX="draw:drawvertex",GAMap.Draw.Event.EDITSTART="draw:editstart",GAMap.Draw.Event.EDITMOVE="draw:editmove",GAMap.Draw.Event.EDITRESIZE="draw:editresize",GAMap.Draw.Event.EDITVERTEX="draw:editvertex",GAMap.Draw.Event.EDITSTOP="draw:editstop",GAMap.Draw.Event.DELETESTART="draw:deletestart",GAMap.Draw.Event.DELETESTOP="draw:deletestop",GAMap.Draw.Event.TOOLBAROPENED="draw:toolbaropened",GAMap.Draw.Event.TOOLBARCLOSED="draw:toolbarclosed",GAMap.Draw.Event.MARKERCONTEXT="draw:markercontext",GAMap.Draw=GAMap.Draw||{},GAMap.Draw.Feature=GAMap.Handler.extend({initialize:function(t,e){this._map=t,this._container=t._container,this._overlayPane=t._panes.overlayPane,this._popupPane=t._panes.popupPane,e&&e.shapeOptions&&(e.shapeOptions=GAMap.Util.extend({},this.options.shapeOptions,e.shapeOptions)),GAMap.setOptions(this,e);var i=GAMap.version.split(".");1===parseInt(i[0],10)&&parseInt(i[1],10)>=2?GAMap.Draw.Feature.include(GAMap.Evented.prototype):GAMap.Draw.Feature.include(GAMap.Mixin.Events)},enable:function(){this._enabled||(GAMap.Handler.prototype.enable.call(this),this.fire("enabled",{handler:this.type}),this._map.fire(GAMap.Draw.Event.DRAWSTART,{layerType:this.type}))},disable:function(){this._enabled&&(GAMap.Handler.prototype.disable.call(this),this._map.fire(GAMap.Draw.Event.DRAWSTOP,{layerType:this.type}),this.fire("disabled",{handler:this.type}))},addHooks:function(){var t=this._map;t&&(GAMap.DomUtil.disableTextSelection(),t.getContainer().focus(),this._tooltip=new GAMap.Draw.Tooltip(this._map),GAMap.DomEvent.on(this._container,"keyup",this._cancelDrawing,this))},removeHooks:function(){this._map&&(GAMap.DomUtil.enableTextSelection(),this._tooltip.dispose(),this._tooltip=null,GAMap.DomEvent.off(this._container,"keyup",this._cancelDrawing,this))},setOptions:function(t){GAMap.setOptions(this,t)},_fireCreatedEvent:function(t){this._map.fire(GAMap.Draw.Event.CREATED,{layer:t,layerType:this.type})},_cancelDrawing:function(t){27===t.keyCode&&(this._map.fire("draw:canceled",{layerType:this.type}),this.disable())}}),GAMap.Draw.Polyline=GAMap.Draw.Feature.extend({statics:{TYPE:"polyline"},Poly:GAMap.Polyline,options:{allowIntersection:!0,repeatMode:!1,drawError:{color:"#b00b00",timeout:2500},icon:new GAMap.DivIcon({iconSize:new GAMap.Point(8,8),className:"gamap-div-icon gamap-editing-icon"}),touchIcon:new GAMap.DivIcon({iconSize:new GAMap.Point(20,20),className:"gamap-div-icon gamap-editing-icon gamap-touch-icon"}),guidelineDistance:20,maxGuideLineLength:4e3,shapeOptions:{stroke:!0,color:"#3388ff",weight:4,opacity:.5,fill:!1,clickable:!0},metric:!0,feet:!0,nautic:!1,showLength:!0,zIndexOffset:2e3,factor:1,maxPoints:0},initialize:function(t,e){GAMap.Browser.touch&&(this.options.icon=this.options.touchIcon),this.options.drawError.message=GAMap.drawLocal.draw.handlers.polyline.error,e&&e.drawError&&(e.drawError=GAMap.Util.extend({},this.options.drawError,e.drawError)),this.type=GAMap.Draw.Polyline.TYPE,GAMap.Draw.Feature.prototype.initialize.call(this,t,e)},addHooks:function(){GAMap.Draw.Feature.prototype.addHooks.call(this),this._map&&(this._markers=[],this._markerGroup=new GAMap.LayerGroup,this._map.addLayer(this._markerGroup),this._poly=new GAMap.Polyline([],this.options.shapeOptions),this._tooltip.updateContent(this._getTooltipText()),this._mouseMarker||(this._mouseMarker=GAMap.marker(this._map.getCenter(),{icon:GAMap.divIcon({className:"gamap-mouse-marker",iconAnchor:[20,20],iconSize:[40,40]}),opacity:0,zIndexOffset:this.options.zIndexOffset})),this._mouseMarker.on("mouseout",this._onMouseOut,this).on("mousemove",this._onMouseMove,this).on("mousedown",this._onMouseDown,this).on("mouseup",this._onMouseUp,this).addTo(this._map),this._map.on("mouseup",this._onMouseUp,this).on("mousemove",this._onMouseMove,this).on("zoomlevelschange",this._onZoomEnd,this).on("touchstart",this._onTouch,this).on("zoomend",this._onZoomEnd,this))},removeHooks:function(){GAMap.Draw.Feature.prototype.removeHooks.call(this),this._clearHideErrorTimeout(),this._cleanUpShape(),this._map.removeLayer(this._markerGroup),delete this._markerGroup,delete this._markers,this._map.removeLayer(this._poly),delete this._poly,this._mouseMarker.off("mousedown",this._onMouseDown,this).off("mouseout",this._onMouseOut,this).off("mouseup",this._onMouseUp,this).off("mousemove",this._onMouseMove,this),this._map.removeLayer(this._mouseMarker),delete this._mouseMarker,this._clearGuides(),this._map.off("mouseup",this._onMouseUp,this).off("mousemove",this._onMouseMove,this).off("zoomlevelschange",this._onZoomEnd,this).off("zoomend",this._onZoomEnd,this).off("touchstart",this._onTouch,this).off("click",this._onTouch,this)},deleteLastVertex:function(){if(!(this._markers.length<=1)){var t=this._markers.pop(),e=this._poly,i=e.getLatLngs(),a=i.splice(-1,1)[0];this._poly.setLatLngs(i),this._markerGroup.removeLayer(t),e.getLatLngs().length<2&&this._map.removeLayer(e),this._vertexChanged(a,!1)}},addVertex:function(t){if(this._markers.length>=2&&!this.options.allowIntersection&&this._poly.newLatLngIntersects(t))return void this._showErrorTooltip();this._errorShown&&this._hideErrorTooltip(),this._markers.push(this._createMarker(t)),this._poly.addLatLng(t),2===this._poly.getLatLngs().length&&this._map.addLayer(this._poly),this._vertexChanged(t,!0)},completeShape:function(){this._markers.length<=1||!this._shapeIsValid()||(this._fireCreatedEvent(),this.disable(),this.options.repeatMode&&this.enable())},_finishShape:function(){var t=this._poly._defaultShape?this._poly._defaultShape():this._poly.getLatLngs(),e=this._poly.newLatLngIntersects(t[t.length-1]);if(!this.options.allowIntersection&&e||!this._shapeIsValid())return void this._showErrorTooltip();this._fireCreatedEvent(),this.disable(),this.options.repeatMode&&this.enable()},_shapeIsValid:function(){return!0},_onZoomEnd:function(){null!==this._markers&&this._updateGuide()},_onMouseMove:function(t){var e=this._map.mouseEventToLayerPoint(t.originalEvent),i=this._map.layerPointToLatLng(e);this._currentLatLng=i,this._updateTooltip(i),this._updateGuide(e),this._mouseMarker.setLatLng(i),GAMap.DomEvent.preventDefault(t.originalEvent)},_vertexChanged:function(t,e){this._map.fire(GAMap.Draw.Event.DRAWVERTEX,{layers:this._markerGroup}),this._updateFinishHandler(),this._updateRunningMeasure(t,e),this._clearGuides(),this._updateTooltip()},_onMouseDown:function(t){if(!this._clickHandled&&!this._touchHandled&&!this._disableMarkers){this._onMouseMove(t),this._clickHandled=!0,this._disableNewMarkers();var e=t.originalEvent,i=e.clientX,a=e.clientY;this._startPoint.call(this,i,a)}},_startPoint:function(t,e){this._mouseDownOrigin=GAMap.point(t,e)},_onMouseUp:function(t){var e=t.originalEvent,i=e.clientX,a=e.clientY;this._endPoint.call(this,i,a,t),this._clickHandled=null},_endPoint:function(e,i,a){if(this._mouseDownOrigin){var o=GAMap.point(e,i).distanceTo(this._mouseDownOrigin),n=this._calculateFinishDistance(a.latlng);this.options.maxPoints>1&&this.options.maxPoints==this._markers.length+1?(this.addVertex(a.latlng),this._finishShape()):n<10&&GAMap.Browser.touch?this._finishShape():Math.abs(o)<9*(t.devicePixelRatio||1)&&this.addVertex(a.latlng),this._enableNewMarkers()}this._mouseDownOrigin=null},_onTouch:function(t){var e,i,a=t.originalEvent;!a.touches||!a.touches[0]||this._clickHandled||this._touchHandled||this._disableMarkers||(e=a.touches[0].clientX,i=a.touches[0].clientY,this._disableNewMarkers(),this._touchHandled=!0,this._startPoint.call(this,e,i),this._endPoint.call(this,e,i,t),this._touchHandled=null),this._clickHandled=null},_onMouseOut:function(){this._tooltip&&this._tooltip._onMouseOut.call(this._tooltip)},_calculateFinishDistance:function(t){var e;if(this._markers.length>0){var i;if(this.type===GAMap.Draw.Polyline.TYPE)i=this._markers[this._markers.length-1];else{if(this.type!==GAMap.Draw.Polygon.TYPE)return 1/0;i=this._markers[0]}var a=this._map.latLngToContainerPoint(i.getLatLng()),o=new GAMap.Marker(t,{icon:this.options.icon,zIndexOffset:2*this.options.zIndexOffset}),n=this._map.latLngToContainerPoint(o.getLatLng());e=a.distanceTo(n)}else e=1/0;return e},_updateFinishHandler:function(){var t=this._markers.length;t>1&&this._markers[t-1].on("click",this._finishShape,this),t>2&&this._markers[t-2].off("click",this._finishShape,this)},_createMarker:function(t){var e=new GAMap.Marker(t,{icon:this.options.icon,zIndexOffset:2*this.options.zIndexOffset});return this._markerGroup.addLayer(e),e},_updateGuide:function(t){var e=this._markers?this._markers.length:0;e>0&&(t=t||this._map.latLngToLayerPoint(this._currentLatLng),this._clearGuides(),this._drawGuide(this._map.latLngToLayerPoint(this._markers[e-1].getLatLng()),t))},_updateTooltip:function(t){var e=this._getTooltipText();t&&this._tooltip.updatePosition(t),this._errorShown||this._tooltip.updateContent(e)},_drawGuide:function(t,e){var i,a,o,n=Math.floor(Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))),s=this.options.guidelineDistance,r=this.options.maxGuideLineLength,h=n>r?n-r:s;for(this._guidesContainer||(this._guidesContainer=GAMap.DomUtil.create("div","gamap-draw-guides",this._overlayPane));h<n;h+=this.options.guidelineDistance)i=h/n,a={x:Math.floor(t.x*(1-i)+i*e.x),y:Math.floor(t.y*(1-i)+i*e.y)},o=GAMap.DomUtil.create("div","gamap-draw-guide-dash",this._guidesContainer),o.style.backgroundColor=this._errorShown?this.options.drawError.color:this.options.shapeOptions.color,GAMap.DomUtil.setPosition(o,a)},_updateGuideColor:function(t){if(this._guidesContainer)for(var e=0,i=this._guidesContainer.childNodes.length;e<i;e++)this._guidesContainer.childNodes[e].style.backgroundColor=t},_clearGuides:function(){if(this._guidesContainer)for(;this._guidesContainer.firstChild;)this._guidesContainer.removeChild(this._guidesContainer.firstChild)},_getTooltipText:function(){var t,e,i=this.options.showLength;return 0===this._markers.length?t={text:GAMap.drawLocal.draw.handlers.polyline.tooltip.start}:(e=i?this._getMeasurementString():"",t=1===this._markers.length?{text:GAMap.drawLocal.draw.handlers.polyline.tooltip.cont,subtext:e}:{text:GAMap.drawLocal.draw.handlers.polyline.tooltip.end,subtext:e}),t},_updateRunningMeasure:function(t,e){var i,a,o=this._markers.length;1===this._markers.length?this._measurementRunningTotal=0:(i=o-(e?2:1),a=GAMap.GeometryUtil.isVersion07x()?t.distanceTo(this._markers[i].getLatLng())*(this.options.factor||1):this._map.distance(t,this._markers[i].getLatLng())*(this.options.factor||1),this._measurementRunningTotal+=a*(e?1:-1))},_getMeasurementString:function(){var t,e=this._currentLatLng,i=this._markers[this._markers.length-1].getLatLng();return t=GAMap.GeometryUtil.isVersion07x()?i&&e&&e.distanceTo?this._measurementRunningTotal+e.distanceTo(i)*(this.options.factor||1):this._measurementRunningTotal||0:i&&e?this._measurementRunningTotal+this._map.distance(e,i)*(this.options.factor||1):this._measurementRunningTotal||0,GAMap.GeometryUtil.readableDistance(t,this.options.metric,this.options.feet,this.options.nautic,this.options.precision)},_showErrorTooltip:function(){this._errorShown=!0,this._tooltip.showAsError().updateContent({text:this.options.drawError.message}),this._updateGuideColor(this.options.drawError.color),this._poly.setStyle({color:this.options.drawError.color}),this._clearHideErrorTimeout(),this._hideErrorTimeout=setTimeout(GAMap.Util.bind(this._hideErrorTooltip,this),this.options.drawError.timeout)},_hideErrorTooltip:function(){this._errorShown=!1,this._clearHideErrorTimeout(),this._tooltip.removeError().updateContent(this._getTooltipText()),this._updateGuideColor(this.options.shapeOptions.color),this._poly.setStyle({color:this.options.shapeOptions.color})},_clearHideErrorTimeout:function(){this._hideErrorTimeout&&(clearTimeout(this._hideErrorTimeout),this._hideErrorTimeout=null)},_disableNewMarkers:function(){this._disableMarkers=!0},_enableNewMarkers:function(){setTimeout(function(){this._disableMarkers=!1}.bind(this),50)},_cleanUpShape:function(){this._markers.length>1&&this._markers[this._markers.length-1].off("click",this._finishShape,this)},_fireCreatedEvent:function(){var t=new this.Poly(this._poly.getLatLngs(),this.options.shapeOptions);GAMap.Draw.Feature.prototype._fireCreatedEvent.call(this,t)}}),GAMap.Draw.Polygon=GAMap.Draw.Polyline.extend({statics:{TYPE:"polygon"},Poly:GAMap.Polygon,options:{showArea:!1,showLength:!1,shapeOptions:{stroke:!0,color:"#3388ff",weight:4,opacity:.5,fill:!0,fillColor:null,fillOpacity:.2,clickable:!0},metric:["km","m"],feet:!0,nautic:!1,precision:{}},initialize:function(t,e){GAMap.Draw.Polyline.prototype.initialize.call(this,t,e),this.type=GAMap.Draw.Polygon.TYPE},_updateFinishHandler:function(){var t=this._markers.length;1===t&&this._markers[0].on("click",this._finishShape,this),t>2&&(this._markers[t-1].on("dblclick",this._finishShape,this),t>3&&this._markers[t-2].off("dblclick",this._finishShape,this))},_getTooltipText:function(){var t,e;return 0===this._markers.length?t=GAMap.drawLocal.draw.handlers.polygon.tooltip.start:this._markers.length<3?(t=GAMap.drawLocal.draw.handlers.polygon.tooltip.cont,e=this._getMeasurementString()):(t=GAMap.drawLocal.draw.handlers.polygon.tooltip.end,e=this._getMeasurementString()),{text:t,subtext:e}},_getMeasurementString:function(){var t=this._area,e="";return t||this.options.showLength?(this.options.showLength&&(e=GAMap.Draw.Polyline.prototype._getMeasurementString.call(this)),t&&(e+="<br>"+GAMap.GeometryUtil.readableArea(t,this.options.metric,this.options.precision)),e):null},_shapeIsValid:function(){return this._markers.length>=3},_vertexChanged:function(t,e){var i;!this.options.allowIntersection&&this.options.showArea&&(i=this._poly.getLatLngs(),this._area=GAMap.GeometryUtil.geodesicArea(i)),GAMap.Draw.Polyline.prototype._vertexChanged.call(this,t,e)},_cleanUpShape:function(){var t=this._markers.length;t>0&&(this._markers[0].off("click",this._finishShape,this),t>2&&this._markers[t-1].off("dblclick",this._finishShape,this))}}),GAMap.SimpleShape={},GAMap.Draw.SimpleShape=GAMap.Draw.Feature.extend({options:{repeatMode:!1},initialize:function(t,e){this._endLabelText=GAMap.drawLocal.draw.handlers.simpleshape.tooltip.end,GAMap.Draw.Feature.prototype.initialize.call(this,t,e)},addHooks:function(){GAMap.Draw.Feature.prototype.addHooks.call(this),this._map&&(this._mapDraggable=this._map.dragging.enabled(),this._mapDraggable&&this._map.dragging.disable(),this._container.style.cursor="crosshair",this._tooltip.updateContent({text:this._initialLabelText}),this._map.on("mousedown",this._onMouseDown,this).on("mousemove",this._onMouseMove,this).on("touchstart",this._onMouseDown,this).on("touchmove",this._onMouseMove,this),e.addEventListener("touchstart",GAMap.DomEvent.preventDefault,{passive:!1}))},removeHooks:function(){GAMap.Draw.Feature.prototype.removeHooks.call(this),this._map&&(this._mapDraggable&&this._map.dragging.enable(),this._container.style.cursor="",this._map.off("mousedown",this._onMouseDown,this).off("mousemove",this._onMouseMove,this).off("touchstart",this._onMouseDown,this).off("touchmove",this._onMouseMove,this),GAMap.DomEvent.off(e,"mouseup",this._onMouseUp,this),GAMap.DomEvent.off(e,"touchend",this._onMouseUp,this),e.removeEventListener("touchstart",GAMap.DomEvent.preventDefault),this._shape&&(this._map.removeLayer(this._shape),delete this._shape)),this._isDrawing=!1},_getTooltipText:function(){return{text:this._endLabelText}},_onMouseDown:function(t){this._isDrawing=!0,this._startLatLng=t.latlng,GAMap.DomEvent.on(e,"mouseup",this._onMouseUp,this).on(e,"touchend",this._onMouseUp,this).preventDefault(t.originalEvent)},_onMouseMove:function(t){var e=t.latlng;this._tooltip.updatePosition(e),this._isDrawing&&(this._tooltip.updateContent(this._getTooltipText()),this._drawShape(e))},_onMouseUp:function(){this._shape&&this._fireCreatedEvent(),this.disable(),this.options.repeatMode&&this.enable()}}),GAMap.Draw.Rectangle=GAMap.Draw.SimpleShape.extend({statics:{TYPE:"rectangle"},options:{shapeOptions:{stroke:!0,color:"#3388ff",weight:4,opacity:.5,fill:!0,fillColor:null,fillOpacity:.2,showArea:!0,clickable:!0},metric:!0},initialize:function(t,e){this.type=GAMap.Draw.Rectangle.TYPE,this._initialLabelText=GAMap.drawLocal.draw.handlers.rectangle.tooltip.start,GAMap.Draw.SimpleShape.prototype.initialize.call(this,t,e)},disable:function(){this._enabled&&(this._isCurrentlyTwoClickDrawing=!1,GAMap.Draw.SimpleShape.prototype.disable.call(this))},_onMouseUp:function(t){if(!this._shape&&!this._isCurrentlyTwoClickDrawing)return void(this._isCurrentlyTwoClickDrawing=!0);this._isCurrentlyTwoClickDrawing&&!a(t.target,"gamap-pane")||GAMap.Draw.SimpleShape.prototype._onMouseUp.call(this)},_drawShape:function(t){this._shape?this._shape.setBounds(new GAMap.LatLngBounds(this._startLatLng,t)):(this._shape=new GAMap.Rectangle(new GAMap.LatLngBounds(this._startLatLng,t),this.options.shapeOptions),this._map.addLayer(this._shape))},_fireCreatedEvent:function(){var t=new GAMap.Rectangle(this._shape.getBounds(),this.options.shapeOptions);GAMap.Draw.SimpleShape.prototype._fireCreatedEvent.call(this,t)},_getTooltipText:function(){var t,e,i,a=GAMap.Draw.SimpleShape.prototype._getTooltipText.call(this),o=this._shape,n=this.options.showArea;return o&&(t=this._shape._defaultShape?this._shape._defaultShape():this._shape.getLatLngs(),e=GAMap.GeometryUtil.geodesicArea(t),i=n?GAMap.GeometryUtil.readableArea(e,this.options.metric):""),{text:a.text,subtext:i}}}),GAMap.Draw.Marker=GAMap.Draw.Feature.extend({statics:{TYPE:"marker"},options:{icon:new GAMap.Icon.Default,repeatMode:!1,zIndexOffset:2e3},initialize:function(t,e){this.type=GAMap.Draw.Marker.TYPE,this._initialLabelText=GAMap.drawLocal.draw.handlers.marker.tooltip.start,GAMap.Draw.Feature.prototype.initialize.call(this,t,e)},addHooks:function(){GAMap.Draw.Feature.prototype.addHooks.call(this),this._map&&(this._tooltip.updateContent({text:this._initialLabelText}),this._mouseMarker||(this._mouseMarker=GAMap.marker(this._map.getCenter(),{icon:GAMap.divIcon({className:"gamap-mouse-marker",iconAnchor:[20,20],iconSize:[40,40]}),opacity:0,zIndexOffset:this.options.zIndexOffset})),this._mouseMarker.on("click",this._onClick,this).addTo(this._map),this._map.on("mousemove",this._onMouseMove,this),this._map.on("click",this._onTouch,this))},removeHooks:function(){GAMap.Draw.Feature.prototype.removeHooks.call(this),this._map&&(this._map.off("click",this._onClick,this).off("click",this._onTouch,this),this._marker&&(this._marker.options.icon.options.canvas||(this._marker.off("click",this._onClick,this),this._map.removeLayer(this._marker),delete this._marker)),this._mouseMarker.off("click",this._onClick,this),this._map.removeLayer(this._mouseMarker),delete this._mouseMarker,this._map.off("mousemove",this._onMouseMove,this))},_onMouseMove:function(t){var e=t.latlng;this._tooltip.updatePosition(e),this._mouseMarker.setLatLng(e),this._marker?(e=this._mouseMarker.getLatLng(),this._marker.setLatLng(e)):(this._marker=this._createMarker(e),this._marker.on("click",this._onClick,this),this._map.on("click",this._onClick,this).addLayer(this._marker))},_createMarker:function(t){return new GAMap.Marker(t,{icon:this.options.icon,zIndexOffset:this.options.zIndexOffset})},_onClick:function(){this._fireCreatedEvent(),this.disable(),this.options.repeatMode&&this.enable()},_onTouch:function(t){this._onMouseMove(t),this._onClick()},_fireCreatedEvent:function(){var t=new GAMap.Marker.Touch(this._marker.getLatLng(),{icon:this.options.icon});GAMap.Draw.Feature.prototype._fireCreatedEvent.call(this,t)}}),GAMap.Draw.CircleMarker=GAMap.Draw.Marker.extend({statics:{TYPE:"circlemarker"},options:{stroke:!0,color:"#3388ff",weight:4,opacity:.5,fill:!0,fillColor:null,fillOpacity:.2,clickable:!0,zIndexOffset:2e3},initialize:function(t,e){this.type=GAMap.Draw.CircleMarker.TYPE,this._initialLabelText=GAMap.drawLocal.draw.handlers.circlemarker.tooltip.start,GAMap.Draw.Feature.prototype.initialize.call(this,t,e)},_fireCreatedEvent:function(){var t=new GAMap.CircleMarker(this._marker.getLatLng(),this.options);GAMap.Draw.Feature.prototype._fireCreatedEvent.call(this,t)},_createMarker:function(t){return new GAMap.CircleMarker(t,this.options)}}),GAMap.Draw.Circle=GAMap.Draw.SimpleShape.extend({statics:{TYPE:"circle"},options:{shapeOptions:{stroke:!0,color:"#3388ff",weight:4,opacity:.5,fill:!0,fillColor:null,fillOpacity:.2,clickable:!0},showRadius:!0,metric:!0,feet:!0,nautic:!1},initialize:function(t,e){this.type=GAMap.Draw.Circle.TYPE,this._initialLabelText=GAMap.drawLocal.draw.handlers.circle.tooltip.start,GAMap.Draw.SimpleShape.prototype.initialize.call(this,t,e)},_drawShape:function(t){if(GAMap.GeometryUtil.isVersion07x())var e=this._startLatLng.distanceTo(t);else var e=this._map.distance(this._startLatLng,t);this._shape?this._shape.setRadius(e):(this._shape=new GAMap.Circle(this._startLatLng,e,this.options.shapeOptions),this._map.addLayer(this._shape))},_fireCreatedEvent:function(){var t=new GAMap.Circle(this._startLatLng,this._shape.getRadius(),this.options.shapeOptions);GAMap.Draw.SimpleShape.prototype._fireCreatedEvent.call(this,t)},_onMouseMove:function(t){var e,i=t.latlng,a=this.options.showRadius,o=this.options.metric;if(this._tooltip.updatePosition(i),this._isDrawing){this._drawShape(i),e=this._shape.getRadius().toFixed(1);var n="";a&&(n=GAMap.drawLocal.draw.handlers.circle.radius+": "+GAMap.GeometryUtil.readableDistance(e,o,this.options.feet,this.options.nautic)),this._tooltip.updateContent({text:this._endLabelText,subtext:n})}}}),GAMap.Edit=GAMap.Edit||{},GAMap.Edit.Marker=GAMap.Handler.extend({initialize:function(t,e){this._marker=t,GAMap.setOptions(this,e)},addHooks:function(){var t=this._marker;t.dragging.enable(),t.on("dragend",this._onDragEnd,t),this._toggleMarkerHighlight()},removeHooks:function(){var t=this._marker;t.dragging.disable(),t.off("dragend",this._onDragEnd,t),this._toggleMarkerHighlight()},_onDragEnd:function(t){var e=t.target;e.edited=!0,this._map.fire(GAMap.Draw.Event.EDITMOVE,{layer:e})},_toggleMarkerHighlight:function(){var t=this._marker._icon;t&&(t.style.display="none",GAMap.DomUtil.hasClass(t,"gamap-edit-marker-selected")?(GAMap.DomUtil.removeClass(t,"gamap-edit-marker-selected"),this._offsetMarker(t,-4)):(GAMap.DomUtil.addClass(t,"gamap-edit-marker-selected"),this._offsetMarker(t,4)),t.style.display="")},_offsetMarker:function(t,e){var i=parseInt(t.style.marginTop,10)-e,a=parseInt(t.style.marginLeft,10)-e;t.style.marginTop=i+"px",t.style.marginLeft=a+"px"}}),GAMap.Marker.addInitHook(function(){GAMap.Edit.Marker&&(this.editing=new GAMap.Edit.Marker(this),this.options.editable&&this.editing.enable())}),GAMap.Edit=GAMap.Edit||{},GAMap.Edit.Poly=GAMap.Handler.extend({initialize:function(t){this.latlngs=[t._latlngs],t._holes&&(this.latlngs=this.latlngs.concat(t._holes)),this._poly=t,this._poly.on("revert-edited",this._updateLatLngs,this)},_defaultShape:function(){return GAMap.Polyline._flat?GAMap.Polyline._flat(this._poly._latlngs)?this._poly._latlngs:this._poly._latlngs[0]:this._poly._latlngs},_eachVertexHandler:function(t){for(var e=0;e<this._verticesHandlers.length;e++)t(this._verticesHandlers[e])},addHooks:function(){this._initHandlers(),this._eachVertexHandler(function(t){t.addHooks()})},removeHooks:function(){this._eachVertexHandler(function(t){t.removeHooks()})},updateMarkers:function(){this._eachVertexHandler(function(t){t.updateMarkers()})},_initHandlers:function(){this._verticesHandlers=[];for(var t=0;t<this.latlngs.length;t++)this._verticesHandlers.push(new GAMap.Edit.PolyVerticesEdit(this._poly,this.latlngs[t],this._poly.options.poly))},_updateLatLngs:function(t){this.latlngs=[t.layer._latlngs],t.layer._holes&&(this.latlngs=this.latlngs.concat(t.layer._holes))}}),GAMap.Edit.PolyVerticesEdit=GAMap.Handler.extend({options:{icon:new GAMap.DivIcon({iconSize:new GAMap.Point(8,8),className:"gamap-div-icon gamap-editing-icon"}),touchIcon:new GAMap.DivIcon({iconSize:new GAMap.Point(20,20),className:"gamap-div-icon gamap-editing-icon gamap-touch-icon"}),drawError:{color:"#b00b00",timeout:1e3}},initialize:function(t,e,i){GAMap.Browser.touch&&(this.options.icon=this.options.touchIcon),this._poly=t,i&&i.drawError&&(i.drawError=GAMap.Util.extend({},this.options.drawError,i.drawError)),this._latlngs=e,GAMap.setOptions(this,i)},_defaultShape:function(){return GAMap.Polyline._flat?GAMap.Polyline._flat(this._latlngs)?this._latlngs:this._latlngs[0]:this._latlngs},addHooks:function(){var t=this._poly,e=t._path;t instanceof GAMap.Polygon||(t.options.fill=!1,t.options.editing&&(t.options.editing.fill=!1)),e&&t.options.editing&&t.options.editing.className&&(t.options.original.className&&t.options.original.className.split(" ").forEach(function(t){GAMap.DomUtil.removeClass(e,t)}),t.options.editing.className.split(" ").forEach(function(t){GAMap.DomUtil.addClass(e,t)})),t.setStyle(t.options.editing),this._poly._map&&(this._map=this._poly._map,this._markerGroup||this._initMarkers(),this._poly._map.addLayer(this._markerGroup))},removeHooks:function(){var t=this._poly,e=t._path;e&&t.options.editing&&t.options.editing.className&&(t.options.editing.className.split(" ").forEach(function(t){GAMap.DomUtil.removeClass(e,t)}),t.options.original.className&&t.options.original.className.split(" ").forEach(function(t){GAMap.DomUtil.addClass(e,t)})),t.setStyle(t.options.original),t._map&&(t._map.removeLayer(this._markerGroup),delete this._markerGroup,delete this._markers)},updateMarkers:function(){this._markerGroup.clearLayers(),this._initMarkers()},_initMarkers:function(){this._markerGroup||(this._markerGroup=new GAMap.LayerGroup),this._markers=[];var t,e,i,a,o=this._defaultShape();for(t=0,i=o.length;t<i;t++)a=this._createMarker(o[t],t),a.on("click",this._onMarkerClick,this),a.on("contextmenu",this._onContextMenu,this),this._markers.push(a);var n,s;for(t=0,e=i-1;t<i;e=t++)(0!==t||GAMap.Polygon&&this._poly instanceof GAMap.Polygon)&&(n=this._markers[e],s=this._markers[t],this._createMiddleMarker(n,s),this._updatePrevNext(n,s))},_createMarker:function(t,e){var i=new GAMap.Marker.Touch(t,{draggable:!0,icon:this.options.icon});return i._origLatLng=t,i._index=e,i.on("dragstart",this._onMarkerDragStart,this).on("drag",this._onMarkerDrag,this).on("dragend",this._fireEdit,this).on("touchmove",this._onTouchMove,this).on("touchend",this._fireEdit,this).on("MSPointerMove",this._onTouchMove,this).on("MSPointerUp",this._fireEdit,this),this._markerGroup.addLayer(i),i},_onMarkerDragStart:function(){this._poly.fire("editstart")},_spliceLatLngs:function(){var t=this._defaultShape(),e=[].splice.apply(t,arguments);return this._poly._convertLatLngs(t,!0),this._poly.redraw(),e},_removeMarker:function(t){var e=t._index;this._markerGroup.removeLayer(t),this._markers.splice(e,1),this._spliceLatLngs(e,1),this._updateIndexes(e,-1),t.off("dragstart",this._onMarkerDragStart,this).off("drag",this._onMarkerDrag,this).off("dragend",this._fireEdit,this).off("touchmove",this._onMarkerDrag,this).off("touchend",this._fireEdit,this).off("click",this._onMarkerClick,this).off("MSPointerMove",this._onTouchMove,this).off("MSPointerUp",this._fireEdit,this)},_fireEdit:function(){this._poly.edited=!0,this._poly.fire("edit"),this._poly._map.fire(GAMap.Draw.Event.EDITVERTEX,{layers:this._markerGroup,poly:this._poly})},_onMarkerDrag:function(t){var e=t.target,i=this._poly;if(GAMap.extend(e._origLatLng,e._latlng),e._middleLeft&&e._middleLeft.setLatLng(this._getMiddleLatLng(e._prev,e)),e._middleRight&&e._middleRight.setLatLng(this._getMiddleLatLng(e,e._next)),i.options.poly){var a=i._map._editTooltip;if(!i.options.poly.allowIntersection&&i.intersects()){var o=i.options.color;i.setStyle({color:this.options.drawError.color}),0!==GAMap.version.indexOf("0.7")&&e.dragging._draggable._onUp(t),this._onMarkerClick(t),a&&a.updateContent({text:GAMap.drawLocal.draw.handlers.polyline.error}),setTimeout(function(){i.setStyle({color:o}),a&&a.updateContent({text:GAMap.drawLocal.edit.handlers.edit.tooltip.text,subtext:GAMap.drawLocal.edit.handlers.edit.tooltip.subtext})},1e3)}}this._poly._bounds._southWest=GAMap.latLng(1/0,1/0),this._poly._bounds._northEast=GAMap.latLng(-1/0,-1/0);var n=this._poly.getLatLngs();this._poly._convertLatLngs(n,!0),this._poly.redraw(),this._poly.fire("editdrag")},_onMarkerClick:function(t){var e=GAMap.Polygon&&this._poly instanceof GAMap.Polygon?4:3,i=t.target;this._defaultShape().length<e||(this._removeMarker(i),this._updatePrevNext(i._prev,i._next),i._middleLeft&&this._markerGroup.removeLayer(i._middleLeft),i._middleRight&&this._markerGroup.removeLayer(i._middleRight),i._prev&&i._next?this._createMiddleMarker(i._prev,i._next):i._prev?i._next||(i._prev._middleRight=null):i._next._middleLeft=null,this._fireEdit())},_onContextMenu:function(t){var e=t.target;this._poly;this._poly._map.fire(GAMap.Draw.Event.MARKERCONTEXT,{marker:e,layers:this._markerGroup,poly:this._poly}),GAMap.DomEvent.stopPropagation},_onTouchMove:function(t){var e=this._map.mouseEventToLayerPoint(t.originalEvent.touches[0]),i=this._map.layerPointToLatLng(e),a=t.target;GAMap.extend(a._origLatLng,i),a._middleLeft&&a._middleLeft.setLatLng(this._getMiddleLatLng(a._prev,a)),a._middleRight&&a._middleRight.setLatLng(this._getMiddleLatLng(a,a._next)),this._poly.redraw(),this.updateMarkers()},_updateIndexes:function(t,e){this._markerGroup.eachLayer(function(i){i._index>t&&(i._index+=e)})},_createMiddleMarker:function(t,e){var i,a,o,n=this._getMiddleLatLng(t,e),s=this._createMarker(n);s.setOpacity(.6),t._middleRight=e._middleLeft=s,a=function(){s.off("touchmove",a,this);var o=e._index;s._index=o,s.off("click",i,this).on("click",this._onMarkerClick,this),n.lat=s.getLatLng().lat,n.lng=s.getLatLng().lng,this._spliceLatLngs(o,0,n),this._markers.splice(o,0,s),s.setOpacity(1),this._updateIndexes(o,1),e._index++,this._updatePrevNext(t,s),this._updatePrevNext(s,e),this._poly.fire("editstart")},o=function(){s.off("dragstart",a,this),s.off("dragend",o,this),s.off("touchmove",a,this),this._createMiddleMarker(t,s),this._createMiddleMarker(s,e)},i=function(){a.call(this),o.call(this),this._fireEdit()},s.on("click",i,this).on("dragstart",a,this).on("dragend",o,this).on("touchmove",a,this),this._markerGroup.addLayer(s)},_updatePrevNext:function(t,e){t&&(t._next=e),e&&(e._prev=t)},_getMiddleLatLng:function(t,e){var i=this._poly._map,a=i.project(t.getLatLng()),o=i.project(e.getLatLng());return i.unproject(a._add(o)._divideBy(2))}}),GAMap.Polyline.addInitHook(function(){this.editing||(GAMap.Edit.Poly&&(this.editing=new GAMap.Edit.Poly(this),this.options.editable&&this.editing.enable()),this.on("add",function(){this.editing&&this.editing.enabled()&&this.editing.addHooks()}),this.on("remove",function(){this.editing&&this.editing.enabled()&&this.editing.removeHooks()}))}),GAMap.Edit=GAMap.Edit||{},GAMap.Edit.SimpleShape=GAMap.Handler.extend({options:{moveIcon:new GAMap.DivIcon({iconSize:new GAMap.Point(8,8),className:"gamap-div-icon gamap-editing-icon gamap-edit-move"}),resizeIcon:new GAMap.DivIcon({iconSize:new GAMap.Point(8,8),
className:"gamap-div-icon gamap-editing-icon gamap-edit-resize"}),touchMoveIcon:new GAMap.DivIcon({iconSize:new GAMap.Point(20,20),className:"gamap-div-icon gamap-editing-icon gamap-edit-move gamap-touch-icon"}),touchResizeIcon:new GAMap.DivIcon({iconSize:new GAMap.Point(20,20),className:"gamap-div-icon gamap-editing-icon gamap-edit-resize gamap-touch-icon"})},initialize:function(t,e){GAMap.Browser.touch&&(this.options.moveIcon=this.options.touchMoveIcon,this.options.resizeIcon=this.options.touchResizeIcon),this._shape=t,GAMap.Util.setOptions(this,e)},addHooks:function(){var t=this._shape;this._shape._map&&(this._map=this._shape._map,t.setStyle(t.options.editing),t._map&&(this._map=t._map,this._markerGroup||this._initMarkers(),this._map.addLayer(this._markerGroup)))},removeHooks:function(){var t=this._shape;if(t.setStyle(t.options.original),t._map){this._unbindMarker(this._moveMarker);for(var e=0,i=this._resizeMarkers.length;e<i;e++)this._unbindMarker(this._resizeMarkers[e]);this._resizeMarkers=null,this._map.removeLayer(this._markerGroup),delete this._markerGroup}this._map=null},updateMarkers:function(){this._markerGroup.clearLayers(),this._initMarkers()},_initMarkers:function(){this._markerGroup||(this._markerGroup=new GAMap.LayerGroup),this._createMoveMarker(),this._createResizeMarker()},_createMoveMarker:function(){},_createResizeMarker:function(){},_createMarker:function(t,e){var i=new GAMap.Marker.Touch(t,{draggable:!0,icon:e,zIndexOffset:10});return this._bindMarker(i),this._markerGroup.addLayer(i),i},_bindMarker:function(t){t.on("dragstart",this._onMarkerDragStart,this).on("drag",this._onMarkerDrag,this).on("dragend",this._onMarkerDragEnd,this).on("touchstart",this._onTouchStart,this).on("touchmove",this._onTouchMove,this).on("MSPointerMove",this._onTouchMove,this).on("touchend",this._onTouchEnd,this).on("MSPointerUp",this._onTouchEnd,this)},_unbindMarker:function(t){t.off("dragstart",this._onMarkerDragStart,this).off("drag",this._onMarkerDrag,this).off("dragend",this._onMarkerDragEnd,this).off("touchstart",this._onTouchStart,this).off("touchmove",this._onTouchMove,this).off("MSPointerMove",this._onTouchMove,this).off("touchend",this._onTouchEnd,this).off("MSPointerUp",this._onTouchEnd,this)},_onMarkerDragStart:function(t){t.target.setOpacity(0),this._shape.fire("editstart")},_fireEdit:function(){this._shape.edited=!0,this._shape.fire("edit")},_onMarkerDrag:function(t){var e=t.target,i=e.getLatLng();e===this._moveMarker?this._move(i):this._resize(i),this._shape.redraw(),this._shape.fire("editdrag")},_onMarkerDragEnd:function(t){t.target.setOpacity(1),this._fireEdit()},_onTouchStart:function(t){if(GAMap.Edit.SimpleShape.prototype._onMarkerDragStart.call(this,t),"function"==typeof this._getCorners){var e=this._getCorners(),i=t.target,a=i._cornerIndex;i.setOpacity(0),this._oppositeCorner=e[(a+2)%4],this._toggleCornerMarkers(0,a)}this._shape.fire("editstart")},_onTouchMove:function(t){var e=this._map.mouseEventToLayerPoint(t.originalEvent.touches[0]),i=this._map.layerPointToLatLng(e);return t.target===this._moveMarker?this._move(i):this._resize(i),this._shape.redraw(),!1},_onTouchEnd:function(t){t.target.setOpacity(1),this.updateMarkers(),this._fireEdit()},_move:function(){},_resize:function(){}}),GAMap.Edit=GAMap.Edit||{},GAMap.Edit.Rectangle=GAMap.Edit.SimpleShape.extend({_createMoveMarker:function(){var t=this._shape.getBounds(),e=t.getCenter();this._moveMarker=this._createMarker(e,this.options.moveIcon)},_createResizeMarker:function(){var t=this._getCorners();this._resizeMarkers=[];for(var e=0,i=t.length;e<i;e++)this._resizeMarkers.push(this._createMarker(t[e],this.options.resizeIcon)),this._resizeMarkers[e]._cornerIndex=e},_onMarkerDragStart:function(t){GAMap.Edit.SimpleShape.prototype._onMarkerDragStart.call(this,t);var e=this._getCorners(),i=t.target,a=i._cornerIndex;this._oppositeCorner=e[(a+2)%4],this._toggleCornerMarkers(0,a)},_onMarkerDragEnd:function(t){var e,i,a=t.target;a===this._moveMarker&&(e=this._shape.getBounds(),i=e.getCenter(),a.setLatLng(i)),this._toggleCornerMarkers(1),this._repositionCornerMarkers(),GAMap.Edit.SimpleShape.prototype._onMarkerDragEnd.call(this,t)},_move:function(t){for(var e,i=this._shape._defaultShape?this._shape._defaultShape():this._shape.getLatLngs(),a=this._shape.getBounds(),o=a.getCenter(),n=[],s=0,r=i.length;s<r;s++)e=[i[s].lat-o.lat,i[s].lng-o.lng],n.push([t.lat+e[0],t.lng+e[1]]);this._shape.setLatLngs(n),this._repositionCornerMarkers(),this._map.fire(GAMap.Draw.Event.EDITMOVE,{layer:this._shape})},_resize:function(t){var e;this._shape.setBounds(GAMap.latLngBounds(t,this._oppositeCorner)),e=this._shape.getBounds(),this._moveMarker.setLatLng(e.getCenter()),this._map.fire(GAMap.Draw.Event.EDITRESIZE,{layer:this._shape})},_getCorners:function(){var t=this._shape.getBounds();return[t.getNorthWest(),t.getNorthEast(),t.getSouthEast(),t.getSouthWest()]},_toggleCornerMarkers:function(t){for(var e=0,i=this._resizeMarkers.length;e<i;e++)this._resizeMarkers[e].setOpacity(t)},_repositionCornerMarkers:function(){for(var t=this._getCorners(),e=0,i=this._resizeMarkers.length;e<i;e++)this._resizeMarkers[e].setLatLng(t[e])}}),GAMap.Rectangle.addInitHook(function(){GAMap.Edit.Rectangle&&(this.editing=new GAMap.Edit.Rectangle(this),this.options.editable&&this.editing.enable())}),GAMap.Edit=GAMap.Edit||{},GAMap.Edit.CircleMarker=GAMap.Edit.SimpleShape.extend({_createMoveMarker:function(){var t=this._shape.getLatLng();this._moveMarker=this._createMarker(t,this.options.moveIcon)},_createResizeMarker:function(){this._resizeMarkers=[]},_move:function(t){if(this._resizeMarkers.length){var e=this._getResizeMarkerPoint(t);this._resizeMarkers[0].setLatLng(e)}this._shape.setLatLng(t),this._map.fire(GAMap.Draw.Event.EDITMOVE,{layer:this._shape})}}),GAMap.CircleMarker.addInitHook(function(){GAMap.Edit.CircleMarker&&(this.editing=new GAMap.Edit.CircleMarker(this),this.options.editable&&this.editing.enable()),this.on("add",function(){this.editing&&this.editing.enabled()&&this.editing.addHooks()}),this.on("remove",function(){this.editing&&this.editing.enabled()&&this.editing.removeHooks()})}),GAMap.Edit=GAMap.Edit||{},GAMap.Edit.Circle=GAMap.Edit.CircleMarker.extend({_createResizeMarker:function(){var t=this._shape.getLatLng(),e=this._getResizeMarkerPoint(t);this._resizeMarkers=[],this._resizeMarkers.push(this._createMarker(e,this.options.resizeIcon))},_getResizeMarkerPoint:function(t){var e=this._shape._radius*Math.cos(Math.PI/4),i=this._map.project(t);return this._map.unproject([i.x+e,i.y-e])},_resize:function(t){var e=this._moveMarker.getLatLng();GAMap.GeometryUtil.isVersion07x()?radius=e.distanceTo(t):radius=this._map.distance(e,t),this._shape.setRadius(radius),this._map.editTooltip&&this._map._editTooltip.updateContent({text:GAMap.drawLocal.edit.handlers.edit.tooltip.subtext+"<br />"+GAMap.drawLocal.edit.handlers.edit.tooltip.text,subtext:GAMap.drawLocal.draw.handlers.circle.radius+": "+GAMap.GeometryUtil.readableDistance(radius,!0,this.options.feet,this.options.nautic)}),this._shape.setRadius(radius),this._map.fire(GAMap.Draw.Event.EDITRESIZE,{layer:this._shape})}}),GAMap.Circle.addInitHook(function(){GAMap.Edit.Circle&&(this.editing=new GAMap.Edit.Circle(this),this.options.editable&&this.editing.enable()),this.on("add",function(){this.editing&&this.editing.enabled()&&this.editing.addHooks()}),this.on("remove",function(){this.editing&&this.editing.enabled()&&this.editing.removeHooks()})}),GAMap.Map.mergeOptions({touchExtend:!0}),GAMap.Map.TouchExtend=GAMap.Handler.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane},addHooks:function(){GAMap.DomEvent.on(this._container,"touchstart",this._onTouchStart,this),GAMap.DomEvent.on(this._container,"touchend",this._onTouchEnd,this),GAMap.DomEvent.on(this._container,"touchmove",this._onTouchMove,this),this._detectIE()?(GAMap.DomEvent.on(this._container,"MSPointerDown",this._onTouchStart,this),GAMap.DomEvent.on(this._container,"MSPointerUp",this._onTouchEnd,this),GAMap.DomEvent.on(this._container,"MSPointerMove",this._onTouchMove,this),GAMap.DomEvent.on(this._container,"MSPointerCancel",this._onTouchCancel,this)):(GAMap.DomEvent.on(this._container,"touchcancel",this._onTouchCancel,this),GAMap.DomEvent.on(this._container,"touchleave",this._onTouchLeave,this))},removeHooks:function(){GAMap.DomEvent.off(this._container,"touchstart",this._onTouchStart,this),GAMap.DomEvent.off(this._container,"touchend",this._onTouchEnd,this),GAMap.DomEvent.off(this._container,"touchmove",this._onTouchMove,this),this._detectIE()?(GAMap.DomEvent.off(this._container,"MSPointerDowm",this._onTouchStart,this),GAMap.DomEvent.off(this._container,"MSPointerUp",this._onTouchEnd,this),GAMap.DomEvent.off(this._container,"MSPointerMove",this._onTouchMove,this),GAMap.DomEvent.off(this._container,"MSPointerCancel",this._onTouchCancel,this)):(GAMap.DomEvent.off(this._container,"touchcancel",this._onTouchCancel,this),GAMap.DomEvent.off(this._container,"touchleave",this._onTouchLeave,this))},_touchEvent:function(t,e){var i={};if(void 0!==t.touches){if(!t.touches.length)return;i=t.touches[0]}else{if("touch"!==t.pointerType)return;if(i=t,!this._filterClick(t))return}var a=this._map.mouseEventToContainerPoint(i),o=this._map.mouseEventToLayerPoint(i),n=this._map.layerPointToLatLng(o);this._map.fire(e,{latlng:n,layerPoint:o,containerPoint:a,pageX:i.pageX,pageY:i.pageY,originalEvent:t})},_filterClick:function(t){var e=t.timeStamp||t.originalEvent.timeStamp,i=GAMap.DomEvent._lastClick&&e-GAMap.DomEvent._lastClick;return i&&i>100&&i<500||t.target._simulatedClick&&!t._simulated?(GAMap.DomEvent.stop(t),!1):(GAMap.DomEvent._lastClick=e,!0)},_onTouchStart:function(t){if(this._map._loaded){this._touchEvent(t,"touchstart")}},_onTouchEnd:function(t){if(this._map._loaded){this._touchEvent(t,"touchend")}},_onTouchCancel:function(t){if(this._map._loaded){var e="touchcancel";this._detectIE()&&(e="pointercancel"),this._touchEvent(t,e)}},_onTouchLeave:function(t){if(this._map._loaded){this._touchEvent(t,"touchleave")}},_onTouchMove:function(t){if(this._map._loaded){this._touchEvent(t,"touchmove")}},_detectIE:function(){var e=t.navigator.userAgent,i=e.indexOf("MSIE ");if(i>0)return parseInt(e.substring(i+5,e.indexOf(".",i)),10);if(e.indexOf("Trident/")>0){var a=e.indexOf("rv:");return parseInt(e.substring(a+3,e.indexOf(".",a)),10)}var o=e.indexOf("Edge/");return o>0&&parseInt(e.substring(o+5,e.indexOf(".",o)),10)}}),GAMap.Map.addInitHook("addHandler","touchExtend",GAMap.Map.TouchExtend),GAMap.Marker.Touch=GAMap.Marker.extend({_initInteraction:function(){return this.addInteractiveTarget?GAMap.Marker.prototype._initInteraction.apply(this):this._initInteractionLegacy()},_initInteractionLegacy:function(){if(this.options.clickable){var t=this._icon,e=["dblclick","mousedown","mouseover","mouseout","contextmenu","touchstart","touchend","touchmove"];this._detectIE?e.concat(["MSPointerDown","MSPointerUp","MSPointerMove","MSPointerCancel"]):e.concat(["touchcancel"]),GAMap.DomUtil.addClass(t,"gamap-clickable"),GAMap.DomEvent.on(t,"click",this._onMouseClick,this),GAMap.DomEvent.on(t,"keypress",this._onKeyPress,this);for(var i=0;i<e.length;i++)GAMap.DomEvent.on(t,e[i],this._fireMouseEvent,this);GAMap.Handler.MarkerDrag&&(this.dragging=new GAMap.Handler.MarkerDrag(this),this.options.draggable&&this.dragging.enable())}},_detectIE:function(){var e=t.navigator.userAgent,i=e.indexOf("MSIE ");if(i>0)return parseInt(e.substring(i+5,e.indexOf(".",i)),10);if(e.indexOf("Trident/")>0){var a=e.indexOf("rv:");return parseInt(e.substring(a+3,e.indexOf(".",a)),10)}var o=e.indexOf("Edge/");return o>0&&parseInt(e.substring(o+5,e.indexOf(".",o)),10)}}),GAMap.LatLngUtil={cloneLatLngs:function(t){for(var e=[],i=0,a=t.length;i<a;i++)Array.isArray(t[i])?e.push(GAMap.LatLngUtil.cloneLatLngs(t[i])):e.push(this.cloneLatLng(t[i]));return e},cloneLatLng:function(t){return GAMap.latLng(t.lat,t.lng)}},function(){var t={km:2,ha:2,m:0,mi:2,ac:2,yd:0,ft:0,nm:2};GAMap.GeometryUtil=GAMap.extend(GAMap.GeometryUtil||{},{geodesicArea:function(t){var e,i,a=t.length,o=0,n=Math.PI/180;if(a>2){for(var s=0;s<a;s++)e=t[s],i=t[(s+1)%a],o+=(i.lng-e.lng)*n*(2+Math.sin(e.lat*n)+Math.sin(i.lat*n));o=6378137*o*6378137/2}return Math.abs(o)},formattedNumber:function(t,e){var i=parseFloat(t).toFixed(e),a=GAMap.drawLocal.format&&GAMap.drawLocal.format.numeric,o=a&&a.delimiters,n=o&&o.thousands,s=o&&o.decimal;if(n||s){var r=i.split(".");i=n?r[0].replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+n):r[0],s=s||".",r.length>1&&(i=i+s+r[1])}return i},readableArea:function(e,i,a){var o,n,a=GAMap.Util.extend({},t,a);return i?(n=["ha","m"],type=typeof i,"string"===type?n=[i]:"boolean"!==type&&(n=i),o=e>=1e6&&-1!==n.indexOf("km")?GAMap.GeometryUtil.formattedNumber(1e-6*e,a.km)+" km²":e>=1e4&&-1!==n.indexOf("ha")?GAMap.GeometryUtil.formattedNumber(1e-4*e,a.ha)+" ha":GAMap.GeometryUtil.formattedNumber(e,a.m)+" m²"):(e/=.836127,o=e>=3097600?GAMap.GeometryUtil.formattedNumber(e/3097600,a.mi)+" mi²":e>=4840?GAMap.GeometryUtil.formattedNumber(e/4840,a.ac)+" acres":GAMap.GeometryUtil.formattedNumber(e,a.yd)+" yd²"),o},readableDistance:function(e,i,a,o,n){var s,n=GAMap.Util.extend({},t,n);switch(i?"string"==typeof i?i:"metric":a?"feet":o?"nauticalMile":"yards"){case"metric":s=e>1e3?GAMap.GeometryUtil.formattedNumber(e/1e3,n.km)+" km":GAMap.GeometryUtil.formattedNumber(e,n.m)+" m";break;case"feet":e*=3.28083,s=GAMap.GeometryUtil.formattedNumber(e,n.ft)+" ft";break;case"nauticalMile":e*=.53996,s=GAMap.GeometryUtil.formattedNumber(e/1e3,n.nm)+" nm";break;case"yards":default:e*=1.09361,s=e>1760?GAMap.GeometryUtil.formattedNumber(e/1760,n.mi)+" miles":GAMap.GeometryUtil.formattedNumber(e,n.yd)+" yd"}return s},isVersion07x:function(){var t=GAMap.version.split(".");return 0===parseInt(t[0],10)&&7===parseInt(t[1],10)}})}(),GAMap.Util.extend(GAMap.LineUtil,{}),GAMap.Polyline.include({intersects:function(){var t,e,i,a=this._getProjectedPoints(),o=a?a.length:0;if(this._tooFewPointsForIntersection())return!1;for(t=o-1;t>=3;t--)if(e=a[t-1],i=a[t],this._lineSegmentsIntersectsRange(e,i,t-2))return!0;return!1},newLatLngIntersects:function(t,e){return!!this._map&&this.newPointIntersects(this._map.latLngToLayerPoint(t),e)},newPointIntersects:function(t,e){var i=this._getProjectedPoints(),a=i?i.length:0,o=i?i[a-1]:null,n=a-2;return!this._tooFewPointsForIntersection(1)&&this._lineSegmentsIntersectsRange(o,t,n,e?1:0)},_tooFewPointsForIntersection:function(t){var e=this._getProjectedPoints(),i=e?e.length:0;return i+=t||0,!e||i<=3},_lineSegmentsIntersectsRange:function(t,e,i,a){var o,n,s=this._getProjectedPoints();a=a||0;for(var r=i;r>a;r--)if(o=s[r-1],n=s[r],GAMap.LineUtil.segmentsIntersect(t,e,o,n))return!0;return!1},_getProjectedPoints:function(){if(!this._defaultShape)return this._originalPoints;for(var t=[],e=this._defaultShape(),i=0;i<e.length;i++)t.push(this._map.latLngToLayerPoint(e[i]));return t}}),GAMap.Polygon.include({intersects:function(){var t,e,i,a,o=this._getProjectedPoints();return!this._tooFewPointsForIntersection()&&(!!GAMap.Polyline.prototype.intersects.call(this)||(t=o.length,e=o[0],i=o[t-1],a=t-2,this._lineSegmentsIntersectsRange(i,e,a,1)))}}),GAMap.Control.Draw=GAMap.Control.extend({options:{position:"topleft",draw:{},edit:!1},initialize:function(t){if(GAMap.version<"0.7")throw new Error("GAMap.draw 0.2.3+ requires GMap 0.7.0+. Download latest from https://github.com/GMap/GMap/");GAMap.Control.prototype.initialize.call(this,t);var e;this._toolbars={},GAMap.DrawToolbar&&this.options.draw&&(e=new GAMap.DrawToolbar(this.options.draw),this._toolbars[GAMap.DrawToolbar.TYPE]=e,this._toolbars[GAMap.DrawToolbar.TYPE].on("enable",this._toolbarEnabled,this)),GAMap.EditToolbar&&this.options.edit&&(e=new GAMap.EditToolbar(this.options.edit),this._toolbars[GAMap.EditToolbar.TYPE]=e,this._toolbars[GAMap.EditToolbar.TYPE].on("enable",this._toolbarEnabled,this)),GAMap.toolbar=this},onAdd:function(t){var e,i=GAMap.DomUtil.create("div","gamap-draw"),a=!1;for(var o in this._toolbars)this._toolbars.hasOwnProperty(o)&&(e=this._toolbars[o].addToolbar(t))&&(a||(GAMap.DomUtil.hasClass(e,"gamap-draw-toolbar-top")||GAMap.DomUtil.addClass(e.childNodes[0],"gamap-draw-toolbar-top"),a=!0),i.appendChild(e));return i},onRemove:function(){for(var t in this._toolbars)this._toolbars.hasOwnProperty(t)&&this._toolbars[t].removeToolbar()},setDrawingOptions:function(t){for(var e in this._toolbars)this._toolbars[e]instanceof GAMap.DrawToolbar&&this._toolbars[e].setOptions(t)},_toolbarEnabled:function(t){var e=t.target;for(var i in this._toolbars)this._toolbars[i]!==e&&this._toolbars[i].disable()}}),GAMap.Map.mergeOptions({drawControlTooltips:!0,drawControl:!1}),GAMap.Map.addInitHook(function(){this.options.drawControl&&(this.drawControl=new GAMap.Control.Draw,this.addControl(this.drawControl))}),GAMap.Toolbar=GAMap.Class.extend({initialize:function(t){GAMap.setOptions(this,t),this._modes={},this._actionButtons=[],this._activeMode=null;var e=GAMap.version.split(".");1===parseInt(e[0],10)&&parseInt(e[1],10)>=2?GAMap.Toolbar.include(GAMap.Evented.prototype):GAMap.Toolbar.include(GAMap.Mixin.Events)},enabled:function(){return null!==this._activeMode},disable:function(){this.enabled()&&this._activeMode.handler.disable()},addToolbar:function(t){var e,i=GAMap.DomUtil.create("div","gamap-draw-section"),a=0,o=this._toolbarClass||"",n=this.getModeHandlers(t);for(this._toolbarContainer=GAMap.DomUtil.create("div","gamap-draw-toolbar gamap-bar"),this._map=t,e=0;e<n.length;e++)n[e].enabled&&this._initModeHandler(n[e].handler,this._toolbarContainer,a++,o,n[e].title);if(a)return this._lastButtonIndex=--a,this._actionsContainer=GAMap.DomUtil.create("ul","gamap-draw-actions"),i.appendChild(this._toolbarContainer),i.appendChild(this._actionsContainer),i},removeToolbar:function(){for(var t in this._modes)this._modes.hasOwnProperty(t)&&(this._disposeButton(this._modes[t].button,this._modes[t].handler.enable,this._modes[t].handler),this._modes[t].handler.disable(),this._modes[t].handler.off("enabled",this._handlerActivated,this).off("disabled",this._handlerDeactivated,this));this._modes={};for(var e=0,i=this._actionButtons.length;e<i;e++)this._disposeButton(this._actionButtons[e].button,this._actionButtons[e].callback,this);this._actionButtons=[],this._actionsContainer=null},_initModeHandler:function(t,e,i,a,o){var n=t.type;this._modes[n]={},this._modes[n].handler=t,this._modes[n].button=this._createButton({type:n,title:o,className:a+"-"+n,container:e,callback:this._modes[n].handler.enable,context:this._modes[n].handler}),this._modes[n].buttonIndex=i,this._modes[n].handler.on("enabled",this._handlerActivated,this).on("disabled",this._handlerDeactivated,this)},_detectIOS:function(){return/iPad|iPhone|iPod/.test(navigator.userAgent)&&!t.MSStream},_createButton:function(t){var e=GAMap.DomUtil.create("a",t.className||"",t.container),i=GAMap.DomUtil.create("span","sr-only",t.container);e.href="#",e.appendChild(i),t.title&&(e.title=t.title,i.innerHTML=t.title),t.text&&(e.innerHTML=t.text,i.innerHTML=t.text);var a=this._detectIOS()?"touchstart":"click";return GAMap.DomEvent.on(e,"click",GAMap.DomEvent.stopPropagation).on(e,"mousedown",GAMap.DomEvent.stopPropagation).on(e,"dblclick",GAMap.DomEvent.stopPropagation).on(e,"touchstart",GAMap.DomEvent.stopPropagation).on(e,"click",GAMap.DomEvent.preventDefault).on(e,a,t.callback,t.context),e},_disposeButton:function(t,e){var i=this._detectIOS()?"touchstart":"click";GAMap.DomEvent.off(t,"click",GAMap.DomEvent.stopPropagation).off(t,"mousedown",GAMap.DomEvent.stopPropagation).off(t,"dblclick",GAMap.DomEvent.stopPropagation).off(t,"touchstart",GAMap.DomEvent.stopPropagation).off(t,"click",GAMap.DomEvent.preventDefault).off(t,i,e)},_handlerActivated:function(t){this.disable(),this._activeMode=this._modes[t.handler],GAMap.DomUtil.addClass(this._activeMode.button,"gamap-draw-toolbar-button-enabled"),this._showActionsToolbar(),this.fire("enable")},_handlerDeactivated:function(){this._hideActionsToolbar(),GAMap.DomUtil.removeClass(this._activeMode.button,"gamap-draw-toolbar-button-enabled"),this._activeMode=null,this.fire("disable")},_createActions:function(t){var e,i,a,o,n=this._actionsContainer,s=this.getActions(t),r=s.length;for(i=0,a=this._actionButtons.length;i<a;i++)this._disposeButton(this._actionButtons[i].button,this._actionButtons[i].callback);for(this._actionButtons=[];n.firstChild;)n.removeChild(n.firstChild);for(var h=0;h<r;h++)"enabled"in s[h]&&!s[h].enabled||(e=GAMap.DomUtil.create("li","",n),o=this._createButton({title:s[h].title,text:s[h].text,container:e,callback:s[h].callback,context:s[h].context}),this._actionButtons.push({button:o,callback:s[h].callback}))},_showActionsToolbar:function(){var t=this._activeMode.buttonIndex,e=this._lastButtonIndex,i=this._activeMode.button.offsetTop-1;this._createActions(this._activeMode.handler),this._actionsContainer.style.top=i+"px",0===t&&(GAMap.DomUtil.addClass(this._toolbarContainer,"gamap-draw-toolbar-notop"),GAMap.DomUtil.addClass(this._actionsContainer,"gamap-draw-actions-top")),t===e&&(GAMap.DomUtil.addClass(this._toolbarContainer,"gamap-draw-toolbar-nobottom"),GAMap.DomUtil.addClass(this._actionsContainer,"gamap-draw-actions-bottom")),this._actionsContainer.style.display="block",this._map.fire(GAMap.Draw.Event.TOOLBAROPENED)},_hideActionsToolbar:function(){this._actionsContainer.style.display="none",GAMap.DomUtil.removeClass(this._toolbarContainer,"gamap-draw-toolbar-notop"),GAMap.DomUtil.removeClass(this._toolbarContainer,"gamap-draw-toolbar-nobottom"),GAMap.DomUtil.removeClass(this._actionsContainer,"gamap-draw-actions-top"),GAMap.DomUtil.removeClass(this._actionsContainer,"gamap-draw-actions-bottom"),this._map.fire(GAMap.Draw.Event.TOOLBARCLOSED)}}),GAMap.Draw=GAMap.Draw||{},GAMap.Draw.Tooltip=GAMap.Class.extend({initialize:function(t){this._map=t,this._popupPane=t._panes.popupPane,this._visible=!1,this._container=t.options.drawControlTooltips?GAMap.DomUtil.create("div","gamap-draw-tooltip",this._popupPane):null,this._singleLineLabel=!1,this._map.on("mouseout",this._onMouseOut,this)},dispose:function(){this._map.off("mouseout",this._onMouseOut,this),this._container&&(this._popupPane.removeChild(this._container),this._container=null)},updateContent:function(t){return this._container?(t.subtext=t.subtext||"",0!==t.subtext.length||this._singleLineLabel?t.subtext.length>0&&this._singleLineLabel&&(GAMap.DomUtil.removeClass(this._container,"gamap-draw-tooltip-single"),this._singleLineLabel=!1):(GAMap.DomUtil.addClass(this._container,"gamap-draw-tooltip-single"),this._singleLineLabel=!0),this._container.innerHTML=(t.subtext.length>0?'<span class="gamap-draw-tooltip-subtext">'+t.subtext+"</span><br />":"")+"<span>"+t.text+"</span>",t.text||t.subtext?(this._visible=!0,this._container.style.visibility="inherit"):(this._visible=!1,this._container.style.visibility="hidden"),this):this},updatePosition:function(t){var e=this._map.latLngToLayerPoint(t),i=this._container;return this._container&&(this._visible&&(i.style.visibility="inherit"),GAMap.DomUtil.setPosition(i,e)),this},showAsError:function(){return this._container&&GAMap.DomUtil.addClass(this._container,"gamap-error-draw-tooltip"),this},removeError:function(){return this._container&&GAMap.DomUtil.removeClass(this._container,"gamap-error-draw-tooltip"),this},_onMouseOut:function(){this._container&&(this._container.style.visibility="hidden")}}),GAMap.DrawToolbar=GAMap.Toolbar.extend({statics:{TYPE:"draw"},options:{polyline:{},polygon:{},rectangle:{},circle:{},marker:{},circlemarker:{}},initialize:function(t){for(var e in this.options)this.options.hasOwnProperty(e)&&t[e]&&(t[e]=GAMap.extend({},this.options[e],t[e]));this._toolbarClass="gamap-draw-draw",GAMap.Toolbar.prototype.initialize.call(this,t)},getModeHandlers:function(t){return[{enabled:this.options.polyline,handler:new GAMap.Draw.Polyline(t,this.options.polyline),title:GAMap.drawLocal.draw.toolbar.buttons.polyline},{enabled:this.options.polygon,handler:new GAMap.Draw.Polygon(t,this.options.polygon),title:GAMap.drawLocal.draw.toolbar.buttons.polygon},{enabled:this.options.rectangle,handler:new GAMap.Draw.Rectangle(t,this.options.rectangle),title:GAMap.drawLocal.draw.toolbar.buttons.rectangle},{enabled:this.options.circle,handler:new GAMap.Draw.Circle(t,this.options.circle),title:GAMap.drawLocal.draw.toolbar.buttons.circle},{enabled:this.options.marker,handler:new GAMap.Draw.Marker(t,this.options.marker),title:GAMap.drawLocal.draw.toolbar.buttons.marker},{enabled:this.options.circlemarker,handler:new GAMap.Draw.CircleMarker(t,this.options.circlemarker),title:GAMap.drawLocal.draw.toolbar.buttons.circlemarker}]},getActions:function(t){return[{enabled:t.completeShape,title:GAMap.drawLocal.draw.toolbar.finish.title,text:GAMap.drawLocal.draw.toolbar.finish.text,callback:t.completeShape,context:t},{enabled:t.deleteLastVertex,title:GAMap.drawLocal.draw.toolbar.undo.title,text:GAMap.drawLocal.draw.toolbar.undo.text,callback:t.deleteLastVertex,context:t},{title:GAMap.drawLocal.draw.toolbar.actions.title,text:GAMap.drawLocal.draw.toolbar.actions.text,callback:this.disable,context:this}]},setOptions:function(t){GAMap.setOptions(this,t);for(var e in this._modes)this._modes.hasOwnProperty(e)&&t.hasOwnProperty(e)&&this._modes[e].handler.setOptions(t[e])}}),GAMap.EditToolbar=GAMap.Toolbar.extend({statics:{TYPE:"edit"},options:{edit:{selectedPathOptions:{dashArray:"10, 10",fill:!0,fillColor:"#fe57a1",fillOpacity:.1,maintainColor:!1}},remove:{},poly:null,featureGroup:null},initialize:function(t){t.edit&&(void 0===t.edit.selectedPathOptions&&(t.edit.selectedPathOptions=this.options.edit.selectedPathOptions),t.edit.selectedPathOptions=GAMap.extend({},this.options.edit.selectedPathOptions,t.edit.selectedPathOptions)),t.remove&&(t.remove=GAMap.extend({},this.options.remove,t.remove)),t.poly&&(t.poly=GAMap.extend({},this.options.poly,t.poly)),this._toolbarClass="gamap-draw-edit",GAMap.Toolbar.prototype.initialize.call(this,t),this._selectedFeatureCount=0},getModeHandlers:function(t){var e=this.options.featureGroup;return[{enabled:this.options.edit,handler:new GAMap.EditToolbar.Edit(t,{featureGroup:e,selectedPathOptions:this.options.edit.selectedPathOptions,poly:this.options.poly}),title:GAMap.drawLocal.edit.toolbar.buttons.edit},{enabled:this.options.remove,handler:new GAMap.EditToolbar.Delete(t,{featureGroup:e}),title:GAMap.drawLocal.edit.toolbar.buttons.remove}]},getActions:function(t){var e=[{title:GAMap.drawLocal.edit.toolbar.actions.save.title,text:GAMap.drawLocal.edit.toolbar.actions.save.text,callback:this._save,context:this},{title:GAMap.drawLocal.edit.toolbar.actions.cancel.title,text:GAMap.drawLocal.edit.toolbar.actions.cancel.text,callback:this.disable,context:this}];return t.removeAllLayers&&e.push({title:GAMap.drawLocal.edit.toolbar.actions.clearAll.title,text:GAMap.drawLocal.edit.toolbar.actions.clearAll.text,callback:this._clearAllLayers,context:this}),e},addToolbar:function(t){var e=GAMap.Toolbar.prototype.addToolbar.call(this,t);return this._checkDisabled(),this.options.featureGroup.on("layeradd layerremove",this._checkDisabled,this),e},removeToolbar:function(){this.options.featureGroup.off("layeradd layerremove",this._checkDisabled,this),GAMap.Toolbar.prototype.removeToolbar.call(this)},disable:function(){this.enabled()&&(this._activeMode.handler.revertLayers(),GAMap.Toolbar.prototype.disable.call(this))},_save:function(){this._activeMode.handler.save(),this._activeMode&&this._activeMode.handler.disable()},_clearAllLayers:function(){this._activeMode.handler.removeAllLayers(),this._activeMode&&this._activeMode.handler.disable()},_checkDisabled:function(){var t,e=this.options.featureGroup,i=0!==e.getLayers().length;this.options.edit&&(t=this._modes[GAMap.EditToolbar.Edit.TYPE].button,i?GAMap.DomUtil.removeClass(t,"gamap-disabled"):GAMap.DomUtil.addClass(t,"gamap-disabled"),t.setAttribute("title",i?GAMap.drawLocal.edit.toolbar.buttons.edit:GAMap.drawLocal.edit.toolbar.buttons.editDisabled)),this.options.remove&&(t=this._modes[GAMap.EditToolbar.Delete.TYPE].button,i?GAMap.DomUtil.removeClass(t,"gamap-disabled"):GAMap.DomUtil.addClass(t,"gamap-disabled"),t.setAttribute("title",i?GAMap.drawLocal.edit.toolbar.buttons.remove:GAMap.drawLocal.edit.toolbar.buttons.removeDisabled))}}),GAMap.EditToolbar.Edit=GAMap.Handler.extend({statics:{TYPE:"edit"},initialize:function(t,e){if(GAMap.Handler.prototype.initialize.call(this,t),GAMap.setOptions(this,e),this._featureGroup=e.featureGroup,!(this._featureGroup instanceof GAMap.FeatureGroup))throw new Error("options.featureGroup must be a GAMap.FeatureGroup");this._uneditedLayerProps={},this.type=GAMap.EditToolbar.Edit.TYPE;var i=GAMap.version.split(".");1===parseInt(i[0],10)&&parseInt(i[1],10)>=2?GAMap.EditToolbar.Edit.include(GAMap.Evented.prototype):GAMap.EditToolbar.Edit.include(GAMap.Mixin.Events)},enable:function(){!this._enabled&&this._hasAvailableLayers()&&(this.fire("enabled",{handler:this.type}),this._map.fire(GAMap.Draw.Event.EDITSTART,{handler:this.type}),GAMap.Handler.prototype.enable.call(this),this._featureGroup.on("layeradd",this._enableLayerEdit,this).on("layerremove",this._disableLayerEdit,this))},disable:function(){this._enabled&&(this._featureGroup.off("layeradd",this._enableLayerEdit,this).off("layerremove",this._disableLayerEdit,this),GAMap.Handler.prototype.disable.call(this),this._map.fire(GAMap.Draw.Event.EDITSTOP,{handler:this.type}),this.fire("disabled",{handler:this.type}))},addHooks:function(){var t=this._map;t&&(t.getContainer().focus(),this._featureGroup.eachLayer(this._enableLayerEdit,this),this._tooltip=new GAMap.Draw.Tooltip(this._map),this._tooltip.updateContent({text:GAMap.drawLocal.edit.handlers.edit.tooltip.text,subtext:GAMap.drawLocal.edit.handlers.edit.tooltip.subtext}),t._editTooltip=this._tooltip,this._updateTooltip(),this._map.on("mousemove",this._onMouseMove,this).on("touchmove",this._onMouseMove,this).on("MSPointerMove",this._onMouseMove,this).on(GAMap.Draw.Event.EDITVERTEX,this._updateTooltip,this))},removeHooks:function(){this._map&&(this._featureGroup.eachLayer(this._disableLayerEdit,this),this._uneditedLayerProps={},this._tooltip.dispose(),this._tooltip=null,this._map.off("mousemove",this._onMouseMove,this).off("touchmove",this._onMouseMove,this).off("MSPointerMove",this._onMouseMove,this).off(GAMap.Draw.Event.EDITVERTEX,this._updateTooltip,this))},revertLayers:function(){this._featureGroup.eachLayer(function(t){this._revertLayer(t)},this)},save:function(){var t=new GAMap.LayerGroup;this._featureGroup.eachLayer(function(e){e.edited&&(t.addLayer(e),e.edited=!1)}),this._map.fire(GAMap.Draw.Event.EDITED,{layers:t})},_backupLayer:function(t){var e=GAMap.Util.stamp(t);this._uneditedLayerProps[e]||(t instanceof GAMap.Polyline||t instanceof GAMap.Polygon||t instanceof GAMap.Rectangle?this._uneditedLayerProps[e]={latlngs:GAMap.LatLngUtil.cloneLatLngs(t.getLatLngs())}:t instanceof GAMap.Circle?this._uneditedLayerProps[e]={latlng:GAMap.LatLngUtil.cloneLatLng(t.getLatLng()),radius:t.getRadius()}:(t instanceof GAMap.Marker||t instanceof GAMap.CircleMarker)&&(this._uneditedLayerProps[e]={latlng:GAMap.LatLngUtil.cloneLatLng(t.getLatLng())}))},_getTooltipText:function(){return{text:GAMap.drawLocal.edit.handlers.edit.tooltip.text,subtext:GAMap.drawLocal.edit.handlers.edit.tooltip.subtext}},_updateTooltip:function(){this._tooltip.updateContent(this._getTooltipText())},_revertLayer:function(t){var e=GAMap.Util.stamp(t);t.edited=!1,this._uneditedLayerProps.hasOwnProperty(e)&&(t instanceof GAMap.Polyline||t instanceof GAMap.Polygon||t instanceof GAMap.Rectangle?t.setLatLngs(this._uneditedLayerProps[e].latlngs):t instanceof GAMap.Circle?(t.setLatLng(this._uneditedLayerProps[e].latlng),t.setRadius(this._uneditedLayerProps[e].radius)):(t instanceof GAMap.Marker||t instanceof GAMap.CircleMarker)&&t.setLatLng(this._uneditedLayerProps[e].latlng),t.fire("revert-edited",{layer:t}))},_enableLayerEdit:function(t){var e,i,a=t.layer||t.target||t;this._backupLayer(a),this.options.poly&&(i=GAMap.Util.extend({},this.options.poly),a.options.poly=i),this.options.selectedPathOptions&&(e=GAMap.Util.extend({},this.options.selectedPathOptions),e.maintainColor&&(e.color=a.options.color,e.fillColor=a.options.fillColor),
a.options.original=GAMap.extend({},a.options),a.options.editing=e),a instanceof GAMap.Marker?(a.editing&&a.editing.enable(),a.dragging.enable(),a.on("dragend",this._onMarkerDragEnd).on("touchmove",this._onTouchMove,this).on("MSPointerMove",this._onTouchMove,this).on("touchend",this._onMarkerDragEnd,this).on("MSPointerUp",this._onMarkerDragEnd,this)):a.editing.enable()},_disableLayerEdit:function(t){var e=t.layer||t.target||t;e.edited=!1,e.editing&&e.editing.disable(),delete e.options.editing,delete e.options.original,this._selectedPathOptions&&(e instanceof GAMap.Marker?this._toggleMarkerHighlight(e):(e.setStyle(e.options.previousOptions),delete e.options.previousOptions)),e instanceof GAMap.Marker?(e.dragging.disable(),e.off("dragend",this._onMarkerDragEnd,this).off("touchmove",this._onTouchMove,this).off("MSPointerMove",this._onTouchMove,this).off("touchend",this._onMarkerDragEnd,this).off("MSPointerUp",this._onMarkerDragEnd,this)):e.editing.disable()},_onMouseMove:function(t){this._tooltip.updatePosition(t.latlng)},_onMarkerDragEnd:function(t){var e=t.target;e.edited=!0,this._map.fire(GAMap.Draw.Event.EDITMOVE,{layer:e})},_onTouchMove:function(t){var e=t.originalEvent.changedTouches[0],i=this._map.mouseEventToLayerPoint(e),a=this._map.layerPointToLatLng(i);t.target.setLatLng(a)},_hasAvailableLayers:function(){return 0!==this._featureGroup.getLayers().length}}),GAMap.EditToolbar.Delete=GAMap.Handler.extend({statics:{TYPE:"remove"},initialize:function(t,e){if(GAMap.Handler.prototype.initialize.call(this,t),GAMap.Util.setOptions(this,e),this._deletableLayers=this.options.featureGroup,!(this._deletableLayers instanceof GAMap.FeatureGroup))throw new Error("options.featureGroup must be a GAMap.FeatureGroup");this.type=GAMap.EditToolbar.Delete.TYPE;var i=GAMap.version.split(".");1===parseInt(i[0],10)&&parseInt(i[1],10)>=2?GAMap.EditToolbar.Delete.include(GAMap.Evented.prototype):GAMap.EditToolbar.Delete.include(GAMap.Mixin.Events)},enable:function(){!this._enabled&&this._hasAvailableLayers()&&(this.fire("enabled",{handler:this.type}),this._map.fire(GAMap.Draw.Event.DELETESTART,{handler:this.type}),GAMap.Handler.prototype.enable.call(this),this._deletableLayers.on("layeradd",this._enableLayerDelete,this).on("layerremove",this._disableLayerDelete,this))},disable:function(){this._enabled&&(this._deletableLayers.off("layeradd",this._enableLayerDelete,this).off("layerremove",this._disableLayerDelete,this),GAMap.Handler.prototype.disable.call(this),this._map.fire(GAMap.Draw.Event.DELETESTOP,{handler:this.type}),this.fire("disabled",{handler:this.type}))},addHooks:function(){var t=this._map;t&&(t.getContainer().focus(),this._deletableLayers.eachLayer(this._enableLayerDelete,this),this._deletedLayers=new GAMap.LayerGroup,this._tooltip=new GAMap.Draw.Tooltip(this._map),this._tooltip.updateContent({text:GAMap.drawLocal.edit.handlers.remove.tooltip.text}),this._map.on("mousemove",this._onMouseMove,this))},removeHooks:function(){this._map&&(this._deletableLayers.eachLayer(this._disableLayerDelete,this),this._deletedLayers=null,this._tooltip.dispose(),this._tooltip=null,this._map.off("mousemove",this._onMouseMove,this))},revertLayers:function(){this._deletedLayers.eachLayer(function(t){this._deletableLayers.addLayer(t),t.fire("revert-deleted",{layer:t})},this)},save:function(){this._map.fire(GAMap.Draw.Event.DELETED,{layers:this._deletedLayers})},removeAllLayers:function(){this._deletableLayers.eachLayer(function(t){this._removeLayer({layer:t})},this),this.save()},_enableLayerDelete:function(t){(t.layer||t.target||t).on("click",this._removeLayer,this)},_disableLayerDelete:function(t){var e=t.layer||t.target||t;e.off("click",this._removeLayer,this),this._deletedLayers.removeLayer(e)},_removeLayer:function(t){var e=t.layer||t.target||t;this._deletableLayers.removeLayer(e),this._deletedLayers.addLayer(e),e.fire("deleted")},_onMouseMove:function(t){this._tooltip.updatePosition(t.latlng)},_hasAvailableLayers:function(){return 0!==this._deletableLayers.getLayers().length}})}(window,document);