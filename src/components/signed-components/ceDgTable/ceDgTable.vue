<template>
    <div class="dg-table">
        <!-- 表格区域 -->
        <el-table ref="grid" :class="[ renderData.length === 0 && !$attrs.border ? 'is-hidden-border-bottom': '' ]"
                  v-bind="attrs" v-on="$listeners" v-loading="loadding" :data="renderData" :row-key="rowKey"
                  :prop="prop"
                  :border="border" @selection-change="handleSelectItem" @sort-change="handleSortChange">
            <slot></slot>
            <!-- 拓传插槽 -->
            <template slot="empty">
                <slot name="empty"></slot>
            </template>
            <slot name="append"></slot>
        </el-table>

        <!-- 分页区域 -->
        <template v-if="pagination && renderData.length > 0">
            <el-pagination class="dg-table__pagination" v-bind="paginationOptions" @size-change="handleSizeChange"
                           @current-change="handleCurrentChange" @prev-click="handlePrevClick"
                           @next-click="handleNextClick">
            </el-pagination>
        </template>
        <template v-else>
            <slot name="pagination"></slot>
        </template>
    </div>
</template>

<script>
import dgTable from 'ui-component-v4/lib/table'

export default {
    name: "ceDgTable",
    mixins: [dgTable],
    methods: {
        handleSizeChange(val){
            if(this.pagingType === 'client') this.paginationOptions.currentPage = 1;
            this.paginationOptions.pageSize = val;
            this.$emit('change-size', val);
        }
    }
}
</script>

<style scoped>

</style>
