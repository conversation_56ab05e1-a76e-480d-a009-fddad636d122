.d-s-r-top {
  display: flex;
  justify-content: space-between;
  .d-s-r-t-serach {
    width: 272px;
  }
  .dg-select {
    width: auto;
  }

}
.d-s-r-table {
  padding: 15px 0;
  height: calc(100% - 60px);
  display: flex;
  overflow: hidden;
  align-items: center;
  .r-c-action::after {
    content: "";
    display: inline-block;
    width: 1px;
    height: 12px;
    background-color: rgba(0, 0, 0, 0.15);
    margin: 0 4px 0 10px;
  }
  .r-c-action:last-child::after {
    width:0
  }
}

.list {
  height: 100%;
}
.roate {
  transform: rotate(40deg);
}
.ce-model_p_icon {
  color:#0088FF;
}
.ce-model_s_icon {
  color:#52C41C;
}
.detailInfo{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  color:#1890ff
}
