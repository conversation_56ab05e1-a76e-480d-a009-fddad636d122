<template>
    <common-dialog :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="clearD"
                   top="8vh"
                   height="500"
                   custom-class="importData"
                   
    >
        <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="'dataConnectionMetadata'"></help-document></div>
        <div class="height100" v-loading="settings.loading">
            <el-form ref="importForm" class="addForm" :model="form"
                     size="mini"
                     label-position="right"
                     :rules="rules"
            >
                <el-form-item
                        label="选择数据源"
                        label-width="120px"
                        prop="dataSource"
                >
                    <dg-select v-model="form.type"
                               filterable
                               placeholder="请选择数据源类型"
                               :data="typeList"
                               class="selectData"
                               @change="handleChange"
                    ></dg-select>
                    <dg-select v-model="form.dataName"
                               filterable
                               placeholder="选择数据源"
                               class="selectData"
                               @change="changeInfo"
                               :data="sourceList"
                    ></dg-select>
                </el-form-item>
                <el-form-item
                        label="用户名"
                        label-width="120px"
                        prop="userName"
                >
                    <el-input placeholder="请输入用户名" v-model="form.userName" ></el-input>
                </el-form-item>
                <el-form-item
                        label="密码"
                        label-width="120px"
                        prop="password"
                >
                    <el-input placeholder="请输入密码" v-model="form.password" show-password></el-input>
                </el-form-item>
                <el-form-item
                        label="保存到目录"
                        label-width="120px"
                        prop="catalog"
                >
                    <dg-tree-drop
                            class="expect-select"
                            :props="defaultProps"
                            v-model="catalogInfo.data.id"
                            :data="nodeList"
                            :tree-props="treeBind"
                            ref="treeDrop"
                            filterable
                            :filterNodeMethod="filterNode"
                            :default-expanded-keys="defaultExpandedKeys"
                            @current-change="changeCatalog"
                    >
                    </dg-tree-drop>
                </el-form-item>
            </el-form>
            <data-table ref="impostDataTable" :treeNode="selectSourceInfo" type="import" @refresh="refresh" v-if="visible" class="addtable"></data-table>
        </div>

        <div slot="footer">
            <el-button   size="mini" @click="clearD">取消</el-button>
            <el-button  type="primary" size="mini" @click="save">保存</el-button>
        </div>
    </common-dialog>
</template>

<script>
    import {treeMethods} from "@/api/treeNameCheck/treeName";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
    import dataTable from"./dataTable";
    import {mapGetters} from "vuex"
    export default {
        name: "importData",
        mixins : [  commonMixins , servicesMixins],
        props:['selectTreeNode','treeNode','tabActive'],
        computed: {
            ...mapGetters(["userInfo"]),
        },
        components:{
            dataTable
        },
        data(){
            return {
                width:'800px',
                title:'从元数据导入',
                visible:false,
                settings:{loading:false},
                treeBind : {
                    "default-expand-all" : true ,
                },
                defaultProps: {
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    title: 'name'
                },
                form:{
                    userName : "",
                    password : "",
                    dataName:'',
                    type:'',
                    tree:''
                },
                rules:{
                    dataSource: [
                        {required: true, message: '请选择数据源类型', trigger: 'blur'},
                    ],
                    userName: [
                        {required: true, message: '请输入用户名', trigger: 'blur'},
                    ],
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'},
                    ],
                    catalog:[
                        {required: true, message: '请选择保存目录', trigger: 'blur'},
                    ]
                },
                selectSourceInfo:{
                    instanceType:'',
                    catalogId:'',
                    id:'',
                    schemaId:'',
                    instanceId:''
                },
                typeList:[],//数据类型列表
                sourceList:[],//数据源列表
                catalogInfo:{data:{id:''}},//目录
                nodeList:[],
                defaultExpandedKeys:[],
            }
        },
        methods:{
            filterNode : treeMethods.methods.filterNode ,
            show(){
                const vm=this;
                vm.nodeList = [];
                vm.defaultExpandedKeys=[];
                vm.visible=true;
                vm.getSourceType();
                vm.selectTreeNode && vm.selectTreeNode.data.id ? vm.catalogInfo = vm.selectTreeNode:vm.catalogInfo = {data:{id:null}};
                this.getTree();
            },
            /**
             * 设置 树节点数据
             * @param data
             * @param level
             * @return {*}
             */
            setTreeNode(data , level) {
                const vm = this, {dirLevel} = vm;
                let child_l = level + 1;
                return data.map(item => {
                    let disabled = dirLevel !== undefined ? level >= dirLevel : false;
                    let children = item.children && item.children.length && !disabled ? vm.setTreeNode(item.children, child_l) : [];
                    let child = [];
                    children.forEach(n => {
                        if (!n.schemaId)
                            child.push(n)
                    })
                    return {
                        id: item.id,
                        label: item.name,
                        name: item.name,
                        //icon ,
                        children: child,
                        isParent: true,
                        pId: item.pId === "-1" ? "0" : item.pId,
                        dirType: item.dirType,
                        operateTime: item.operateTime,
                        schemaId: item.schemaId,
                        level,
                        isActive: false
                    }
                });
            },
            getSelectNode(){
                const vm = this;
                if(vm.catalogInfo && vm.catalogInfo.data.id){
                    vm.defaultExpandedKeys=[vm.catalogInfo.data.id];
                    vm.$nextTick(()=>{
                        vm.$refs.treeDrop.setCurrentKey(vm.catalogInfo.data.id);
                    })
                }
            },
            getTree(){
                const vm = this , {dataSpaceServices , dataSpaceMock , settings ,dirId } = this;
                let services = vm.getServices(dataSpaceServices , dataSpaceMock);
                settings.loading = true;
                vm.nodeList=[];
                if(this.tabActive === 'myData'){
                    services.queryDirTree('' , '', '' , settings).then( res => {
                        if(res.data.status === 0){
                            if(!res.data.data || res.data.data.length === 0) return ;
                            let  result = res.data.data;
                            vm.nodeList = vm.setTreeNode(result , 1);
                            vm.getSelectNode();
                        }
                    })
                }
                else{
                    vm.sourceDatasetTree=[];
                    services.getDataSourceTree(settings,vm.isLogic).then(res =>{
                        if (res.data.status === 0){
                            if(!res.data.data || res.data.data.length === 0) return ;
                            vm.nodeList = vm.setTreeNode(res.data.data,1);
                            vm.getSelectNode();
                        }
                    });
                }
            },
            //目录变更
            changeCatalog(data,nodes){
                this.catalogInfo=nodes;
            },

            // validate() {
            //     let flag = true;
            //     this.$nextTick(()=>{
            //         this.$refs.importForm.validate((valid) => {
            //             if (valid) {
            //             } else {
            //                 flag = false;
            //                 return false;
            //             }
            //         })
            //         return flag;
            //     })
            // },
            clearD(){
                this.visible=false;
            },
            //获取数据类型
            getSourceType(){
                const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
                let services = vm.getServices(dataSpaceServices, dataSpaceMock);
                settings.loading = true;
                services.getHouseKeepTree().then(res => {
                    if (res.data.status === 0) {
                        vm.typeList = [];
                        Object.keys(res.data.data).forEach(n=>{
                            if(n.toUpperCase() != "HBASE" && n.toUpperCase() != "HIVE" && n!=''){
                                vm.typeList.push({label:n,value:res.data.data[n]})
                            }
                        })
                        vm.typeList.length ? vm.getDataSourceList(vm.typeList[0].value) : '';
                        vm.form.type = vm.typeList.length?vm.typeList[0].value:'';
                    }
                })
            },
            //获取数据源列表
            getDataSourceList(val){
                const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
                let services = vm.getServices(dataSpaceServices, dataSpaceMock);
                settings.loading = true;
                vm.form.dataName = '';
                services.findHouseKeepDataObjByType({dbtype:val},settings).then(res => {
                    if (res.data.status === 0) {
                        let callbackData = res.data.data;
                        vm.sourceList = callbackData && callbackData.length ? callbackData.map(n=>{n.value = n.id; n.label = n.name;return n}):[];
                        if(vm.sourceList.length){
                            vm.$nextTick(()=>{
                                vm.form.dataName = vm.sourceList[0].id;
                                vm.selectSourceInfo = vm.sourceList[0];
                                vm.getTableList()
                            })
                        }
                        else{
                            vm.$refs.impostDataTable.tableData = [];
                        }
                    }
                })
            },
            //数据类型变更
            handleChange(){
                this.getDataSourceList(arguments[1].value);
            },
            //获取数据源下的数据表
            getTableList(){
                this.$nextTick(()=>{
                    this.$refs.impostDataTable.initData();
                })
            },
            //数据源变更
            changeInfo(val){
                this.selectSourceInfo=arguments[1];
                this.selectSourceInfo.id?this.getTableList():'';
            },
            treeParentId(tree){
                if(tree.level==0){
                    return null;
                }
                else if(tree.level==1){
                    return tree.data.id;
                }
                else{
                    return this.treeParentId(tree.parent);
                }
            },
            //保存
            save(){
                if(this.catalogInfo.level==1)
                    return this.$message.warning('当前目录是第一层级目录，第一层级目录不支持新增数据源！');
                if(!this.form.type||!this.form.dataName) return this.$message.warning('请选择数据源！');
                if(!(this.catalogInfo&&this.catalogInfo.id)) return this.$message.warning('请选择保存目录！');
                let busiDirId=this.treeParentId(this.catalogInfo.parent);
                let classifyId = this.catalogInfo.data.id;

                let sourceData = {
                    classifyId: this.catalogInfo.data.id,
                    dbType: '',
                    domainDbType:this.form.type,
                    name: this.selectSourceInfo.name,
                    otherElementId: this.selectSourceInfo.id,
                    softwareId: this.selectSourceInfo.catalogId,
                    dataSourceName : this.selectSourceInfo.name,
                };
                const vm = this, {dataSpaceServices, dataSpaceMock, settings} = this;
                let services = vm.getServices(dataSpaceServices, dataSpaceMock);
                services.checkImportAuth({catalogId : this.sourceList.find(item => item.value === this.form.dataName).catalogId, username : this.form.userName, password : this.form.password}).then(res => {
                    if (res.data.status === 0) { 
                        vm.signSource(services ,classifyId, busiDirId, sourceData);
                    }
                })
            },
            signSource(services , classifyId, busiDirId, sourceData){
                const vm = this , {settings} = this;
                let isAddExistDataSource = true;
                settings.loading = true;
                services.saveDWDBInstance(classifyId, busiDirId, sourceData , '' , isAddExistDataSource, '大数据管家').then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;
                        result.catalogId = sourceData.softwareId;
                        let dataSetAuthVo = {
                            dataObjectVos: [{name: result.name, code: result.id}],
                            roleVos: [],
                            userVos: [{id: vm.userInfo.id}]
                        };
                        services.dataSetAuthRegister(dataSetAuthVo).then(res => {
                            if (res.data.status === 0) {
                                services.addDataSetAuth(dataSetAuthVo, settings).then(res => {
                                    if (res.data.status === 0) {
                                        vm.isSave=true;
                                        let par = this.$refs.impostDataTable.dataSetVo.dataObjectTableIDList ? this.$refs.impostDataTable.dataSetVo.dataObjectTableIDList : [];
                                        if(par.length){
                                            this.$refs.impostDataTable.saveDataSetObject(par,services,settings ,result.id);
                                        }
                                        vm.clearD();
                                        vm.refresh();
                                    }
                                })
                            }
                        })
                    }
                    else{
                        settings.loading = false;
                    }
                })
            },
            refresh(){
                this.$emit('refresh');
            }
        },
    }
</script>

<style  lang="less" scoped>
    /deep/.importData{
        .el-dialog__body{
            height:calc(70vh - 50px);
        }
        .addtable{
            height:calc(100% - 185px);
        }
        .selectData{
            width:50%;
        }
    }
</style>