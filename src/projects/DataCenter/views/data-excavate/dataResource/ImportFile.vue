<template>
    <div class="importFile">
        <el-dialog
                :title="dialogTitle"
                :visible.sync="dialogVisible"
                :width="width"
                :modal-append-to-body="false"
                :close-on-click-modal="false"
                :append-to-body="true"
                @closed="formClose"
        >
            <ImportFileForm ref="loadF" v-if="loadF" />

            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeDialog">关闭</el-button>
                <el-button size="small" type="primary" @click="save">上传</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import ImportFileForm from './ImportFileForm'
    export default {
        name: "ImportFile",
        components : {ImportFileForm},
        data(){
            return {
                dialogVisible :false ,
                dialogTitle :'文件导入',
                width : '700px' ,
                loadF : false
            }
        },
        methods : {
            openDialog(){
                this.dialogVisible = true;
                this.loadF = true;
            },
            closeDialog(){
                this.dialogVisible = false;
            },
            save(){
                //this.closeDialog();
                this.upLoad();
            },
            upLoad(){
                this.$refs.loadF.submitUpload();
            },
            formClose(){
                this.loadF = false;
            }
        }
    }
</script>

<style scoped>

</style>