<template>
    <div class="ce-collection">
        <el-tabs v-model="tabName">
            <el-tab-pane label="关联设置" name="first"></el-tab-pane>
            <el-tab-pane label="输出字段" name="second"></el-tab-pane>
        </el-tabs>
        <!-- 关联设置 -->
        <div class="ce-plugin_base" v-show="tabName === 'first'">
            <div>
                <span class="leftTitle">左表</span>
                <span class="rightTitle">右表</span>
            </div>
            <br />

            <el-form :inline="true" :model="dataBaseInfo[indx]" class="dataBaseForm"
                v-for="(item, indx) in dataBaseInfo" :key="indx">
                <div>
                    <!-- 选择表区域 -->
                    <el-form-item :label="`关联${indx + 1}:`" class="item" :prop="dataBaseInfo[indx].table1"
                        v-if="indx === 0">
                        <dg-select v-model="dataBaseInfo[indx].table1" placeholder="请选择" :data="FirstTable"
                            :disabled="indx === 0"></dg-select>
                    </el-form-item>

                    <el-form-item :label="`关联${indx + 1}:`" class="item" :prop="dataBaseInfo[indx].table11" v-else>
                        <ce-select-drop ref="pos_tree" :placeholder="dataBasePlaceholder" :props="defaultProps"
                            filterable clearable check-leaf check-strictly visible-type="leaf"
                            :filterNodeMethod="filterNode" v-model="dataBaseInfo[indx].table11" :data="optionsTable"
                            @current-change="getOptionData(...arguments, indx, 'left')"></ce-select-drop>
                    </el-form-item>

                    <el-form-item class="masked" :prop="dataBaseInfo[indx].op">
                        <dg-select v-model="dataBaseInfo[indx].op" :data="item.optionsJoin">
                        </dg-select>
                    </el-form-item>

                    <el-form-item :prop="dataBaseInfo[indx].table2">
                        <ce-select-drop ref="pos_tree" :placeholder="dataBasePlaceholder" :props="defaultProps"
                            filterable clearable check-leaf check-strictly visible-type="leaf"
                            :filterNodeMethod="filterNode" v-model="dataBaseInfo[indx].table2" :data="optionsTable"
                            @current-change="getOptionData(...arguments, indx, 'right')" style="display: inline-block;">
                        </ce-select-drop>
                        &nbsp;&nbsp;
                        <el-link :underline="false" type="primary" title="添加关联" @click="handleAddAllChild()">添加关联
                        </el-link>

                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <el-popconfirm size="mini" title="确认删除?" @confirm="DeleteAddAllChild(indx)">
                            <el-link slot="reference" :underline="false" type="primary" title="删除关联">删除关联
                            </el-link>
                        </el-popconfirm>
                    </el-form-item>

                    <el-form-item class="ce-plugin_contend" :prop="dataBaseInfo[indx].condition">
                        <el-radio v-model="dataBaseInfo[indx].condition" label="AND"
                            @change="selectCondition($event, indx, 'AND')">满足全部条件
                        </el-radio>
                        <el-radio v-model="dataBaseInfo[indx].condition" label="OR"
                            @change="selectCondition($event, indx, 'OR')">满足任意条件
                        </el-radio>
                    </el-form-item>
                    <br />
                    <!-- 字段区域 -->
                    <div v-for="(it, index) in dataBaseInfo[indx].list" :key="index">
                        <el-form-item class="textFilter" :prop="'list.' + index + '.field1'">
                            <dg-select v-model="it.field1" placeholder="请选择" :data="item.optionsFiled1"
                                @change="selectFiledChange($event, indx, 'left', index)">
                            </dg-select>
                        </el-form-item>

                        <el-form-item :prop="'list.' + index + '.symbol'">
                            <span class="symbol">{{ symbol }}</span>
                        </el-form-item>

                        <el-form-item class="textFilter2" :prop="'list.' + index + '.field2'">
                            <dg-select v-model="it.field2" placeholder="请选择" :data="item.optionsFiled2"
                                style="width:197px" @change="selectFiledChange($event, indx, 'right', index)">
                            </dg-select>
                            &nbsp;&nbsp;
                            <!-- 添加字段 -->
                            <el-link :underline="false" type="primary" icon="el-icon-plus" title="添加条件"
                                @click="handleAddChild(indx)"></el-link>
                            <!-- 删除字段 -->
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <el-popconfirm size="mini" title="确认删除?" @confirm="DeleteAddChild(indx, index)">
                                <el-link slot="reference" :underline="false" type="primary" title="删除"
                                    icon="el-icon-delete">
                                </el-link>
                            </el-popconfirm>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </div>

        <!-- 输出字段 -->
        <div class="ce-plugin_attr" v-show="tabName === 'second'">
            <div class="ce-plugin_head">
                <el-input class="ce-plugin_input" placeholder="请输入字段名/字段中文名搜索" suffix-icon="el-icon-search"
                    v-model="filterData">
                </el-input>
                <el-button type="primary" @click="show()">选择输出字段</el-button>
            </div>

            <common-table height="calc(100% - 10px - 2rem)" :columns="resultColumns" :pagination="false"
                :data="filterDataTable">
                <template slot="opers" slot-scope="{ row }">
                    <el-popconfirm title="确定删除吗？" @confirm="handleClickDelete(row)">
                        <i style="cursor:pointer;color: #2f9bff" slot="reference"
                            class="icon icon__btn icon__btn_delete pl5 pr5 f18 colorC" title="删除">&#xe847;</i>
                    </el-popconfirm>
                </template>
                <el-input slot="fieldsName" slot-scope="{ row }" v-model.trim="row.fieldsName">
                </el-input>
            </common-table>
        </div>
        <!-- 选择输出字段弹窗 -->
        <common-dialog custom-class="params-dialog" :title="title" :width="width" :visible.sync="visible"
            @closed="clear_data" v-loading="loading">
            <selectOutputDialog ref="dialog" v-if="showParams" :filedOptions="outputTableFileds"
                :tableOptions="outputTable" @updateOutPut="updateOutPut" :result="result" @cancel="cancel">
            </selectOutputDialog>
            <div slot="footer">
                <el-button @click="canceled">取 消</el-button>
                <el-button type="primary" @click="submit">确定</el-button>
            </div>
        </common-dialog>
        <!-- 预览 -->
        <preView ref="preview"></preView>
    </div>
</template>

<script>
    import {
        pluginMixins
    } from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {
        servicesMixins
    } from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";

    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView";
    import selectOutputDialog from "./OutputDialog";
    import {
        commonMixins
    } from "@/api/commonMethods/common-mixins";
    import {
        common
    } from "@/api/commonMethods/common";
    import {
        treeMethods
    } from "@/api/treeNameCheck/treeName";
    export default {
        name: "ManyJoinPlugin",
        mixins: [pluginMixins, servicesMixins, common, commonMixins, treeMethods],
        components: {
            preView,
            selectOutputDialog,
        },
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        computed: {
            filterDataTable() {
                const vm = this;
                let f = vm.result.filter(data => {
                    if (
                        data.fieldsCode === vm.filterData ||
                        data.fieldsCode.toLowerCase().indexOf(vm.filterData.toLowerCase()) !== -1 ||
                        data.fieldsName === vm.filterData ||
                        data.fieldsName.toLowerCase().indexOf(vm.filterData.toLowerCase()) !== -1
                    ) {
                        return true;
                    }
                });
                return f;
            },
        },
        created() {
            this.initData(this.sourceData);
        },
        data() {
            return {
                width: "1000px",
                filterData: "",
                tabName: "first",
                symbol: "=",
                getValue: [],
                defaultProps: {
                    value: "id",
                    label: "label",
                    children: "children",
                    disabled: "disabled",
                },
                dataBasePlaceholder: "请选择",
                optionsTable: [], //表数据

                firstFiled: [],
                outputTable: [], //给输出字段一个空数组
                outputTableFileds: [],

                resultColumns: [{
                        prop: "fieldsCode",
                        label: "字段名",
                    },
                    {
                        prop: "fieldsName",
                        label: "字段中文名",
                    },
                    {
                        prop: "type",
                        label: "数据类型",
                    },
                    {
                        prop: "tableName",
                        label: "来源于",
                    },
                    {
                        prop: "opers",
                        label: "操作",
                        align: "center",
                        width: 70,
                    },
                ],
                title: "添加输出字段",
                tableData: [], //表数据
                result: [], //输出字段数据存储的数组
                dataBaseInfo: [],
                pluginType: "ManyJoin", //类型
                FirstTable: [],
                jsondata: [], //储存JSON数据
                tableOptions: [], //已选表的数据
                filedOptions: [], //所有字段数据
                pluginMeta: {}, //存储数据
                CicadaManyJoinOutputColumns: [], //输出字段
                cicadaManyJoinTwoTableRelations: [],
                tableIds: [], //已选择表的id
                tableZC: {},
                oneTableDatas: []
            };
        },
        methods: {
            //添加单行字段
            handleAddChild(indx) {
                this.dataBaseInfo[indx].list.push({
                    field1: "",
                    symbol: "",
                    field2: "",
                });

                this.tableData[indx].Filed.push({
                    left: "",
                    right: "",
                });
            },
            //删除单行字段
            DeleteAddChild(indx, index) {
                let vm = this;
                if (vm.dataBaseInfo[indx].list.length > 1) {
                    vm.dataBaseInfo[indx].list.splice(index, 1);
                    vm.tableData[indx].Filed.splice(index, 1);
                } else {
                    vm.$message.warning("至少需要一组字段");
                }
            },
            //添加整个表单项
            handleAddAllChild() {
                const vm = this;
                vm.dataBaseInfo.push({
                    //数组绑定的
                    table11: "",
                    op: "求交集",
                    table2: "",
                    condition: "AND",
                    list: [{
                        field1: "",
                        field2: "",
                    }, ],
                    // optionsTable: [],
                    optionsJoin: [{
                        lable: "求交集",
                        value: "求交集",
                    }, ], //这个是条件
                    optionsFiled1: [],
                    optionsFiled2: [],
                });

                vm.tableData.push({
                    left: "",
                    right: "",
                    condition: "",
                    Filed: [{
                        left: "",
                        right: "",
                    }, ],
                });

                vm.tableOptions.push({
                    leftTableName: "",
                    rightTableName: "",
                });

                vm.tableIds.push({
                    left: "",
                    right: "",
                })

                //每次push都把condition设置成AND，并触发change事件
                let length = vm.dataBaseInfo.length - 1;
                vm.selectCondition(
                    vm.dataBaseInfo[length].condition,
                    length,
                    vm.dataBaseInfo[length].condition
                );


            },
            //删除整个表单项
            async DeleteAddAllChild(index) {
                let vm = this;
                if (index === 0) {
                    vm.$message.warning("不可以删除初始关联1");
                } else if (vm.dataBaseInfo.length > 1) {
                    vm.dataBaseInfo.splice(index, 1);
                    vm.tableData.splice(index, 1);
                    vm.tableIds.splice(index, 1)
                } else {
                    vm.$message.warning("至少需要一个关联");
                }
                //删除时在重新获取一次表单==》目的是为了防止已经删除的数据还在数组中
                await vm.getTable()
                // 删除时对已经删除表的输出字段进行过滤
                vm.getLastResult();

            },

            //输出字段表的删除操作
            handleClickDelete(row) {
                this.result.forEach((item, index) => {
                    if (item.id === row.id && item.tableId === row.tableId) {
                        this.result.splice(index, 1);
                    }
                });
            },
            //取消
            canceled() {
                this.$refs.dialog.cancel();
            },
            //提交
            submit() {
                this.$refs.dialog.submit();
            },
            // 预览
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            //此插件没有运行
            async runScript() {},

            // 保存
            async save(fn,showTip) {
                const vm = this,
                    {
                        pluginServices,
                        pluginMock,
                        loadParams
                    } = this;
                let services = vm.getServices(pluginServices, pluginMock);
                //保存之前做判断，没有验证通过不能提交
                if (vm.result.length === 0) {
                    vm.$message.warning("请选择输出字段");
                    return;
                }
                for (let i = 0; i < vm.dataBaseInfo.length; i++) {
                    if (i === 0) {
                        if (vm.dataBaseInfo[i].table2 === '') {
                            vm.$message.warning("请完善表配置");
                            return;
                        }
                    } else if (vm.dataBaseInfo[i].table11 === '' || vm.dataBaseInfo[i].table2 === '') {
                        vm.$message.warning("请完善表配置");
                        return;
                    }
                    for (let j = 0; j < vm.dataBaseInfo[i].list.length; j++) {
                        if (vm.dataBaseInfo[i].list[j].field1 === '' || vm.dataBaseInfo[i].list[j].field2 === '') {
                            vm.$message.warning("请完善字段配置");
                            return;
                        }
                    }
                }
                //对输出字段进行校验
                for (let i = 0; i < vm.result.length; i++) {
                    if (vm.result[i].fieldsName === '' || vm.result[i].fieldsName === null || vm.result[i]
                        .fieldsName === undefined) {
                        vm.$message.warning("输出字段的字段中文名不能为空");
                        return;
                    }
                    for (let j = i + 1; j < vm.result.length; j++) {
                        if (vm.result[i].fieldsName === vm.result[j].fieldsName) {
                            vm.$message.warning("请不要存在相同的字段中文名");
                            return;
                        }
                        if (vm.result[i].replace === vm.result[j].replace) {
                            vm.result[j].replace = (vm.result[j].replace + j)
                        }
                    }
                }

                //重置外层数组
                vm.cicadaManyJoinTwoTableRelations = []
                vm.tableData.forEach((res, idx) => { //大循环

                    //解析数据

                    let LeftTable = JSON.parse(res.left)


                    let RightTable = JSON.parse(res.right)


                    //push外层的表数据
                    if (idx === 0 && LeftTable.leftStepId === null || LeftTable.leftStepId === '' ||
                        LeftTable
                        .leftStepId === undefined) {
                        vm.oneTableDatas.forEach(it => {
                            if (it.index === 0 || it.index === '0') {
                                vm.cicadaManyJoinTwoTableRelations.push({
                                    index: idx, //序号
                                    leftStepId: LeftTable.leftStepId, //左表id
                                    leftStepCode: LeftTable
                                        .leftStepCode, //左表code(进行转换---> code+id前8位)
                                    rightStepId: RightTable.rightStepId,
                                    rightStepCode: RightTable.rightStepCode,
                                    leftStepName: it.leftStepName, //左表名称
                                    rightStepName: RightTable.rightStepName,
                                    leftJoinKey: it.leftJoinKey,
                                    rightJoinKey: RightTable.rightJoinKey,
                                    op: '求交集',
                                    condition: res.condition, //ADN 或 OR (条件)
                                    cicadaManyJoinMetaJoinColumns: ""

                                })
                            }
                        })

                    } else {

                        vm.cicadaManyJoinTwoTableRelations.push({
                            index: idx, //序号
                            leftStepId: LeftTable.leftStepId, //左表id
                            leftStepCode: LeftTable.leftStepCode, //左表code(进行转换---> code+id前8位)
                            rightStepId: RightTable.rightStepId,
                            rightStepCode: RightTable.rightStepCode,
                            leftStepName: LeftTable.leftStepName, //左表名称
                            rightStepName: RightTable.rightStepName,
                            leftJoinKey: LeftTable.leftJoinKey,
                            rightJoinKey: RightTable.rightJoinKey,
                            op: '求交集',
                            condition: res.condition, //ADN 或 OR (条件)
                            cicadaManyJoinMetaJoinColumns: ""

                        })
                    }
                    //push里层的字段数据
                    vm.jsondata = [];

                    res.Filed.forEach((item, index) => { // 小循环
                        //解析数据
                        let leftFiled = JSON.parse(item.left)
                        let rightFiled = JSON.parse(item.right)
                        //封装字段数据
                        let Filed = {
                            index: index,
                            leftId: leftFiled.leftId,
                            leftCol: leftFiled.leftCol,
                            rightCol: rightFiled.rightCol,
                            rightId: rightFiled.rightId
                        }
                        //push近暂存表中
                        vm.jsondata.push(Filed)
                    })

                    //转换成字符串格式
                    let jsondata = JSON.stringify(vm.jsondata)
                    // push  到 cicadaManyJoinMetaJoinColumns
                    vm.cicadaManyJoinTwoTableRelations[idx].cicadaManyJoinMetaJoinColumns = jsondata

                });
                //输出字段
                vm.CicadaManyJoinOutputColumns = []
                //对  result 中的输出字段进行封装 
                vm.result.forEach((item, index) => {
                    let data = {
                        index: index,
                        newFieldName: item.fieldsCode, //字段code
                        objId: item.tableId, // 对象id--->即表id
                        objCode: item.tableCode, // 对象code--->即表code (进行转换后的 code + id前8位)
                        oldObjCode: item.tableName, //表名称
                        valType: item.type, //类型
                        length: item.length, // 长度
                        precision: item.precsn, //精度
                        replaceFieldName: item.replace, //替换名称
                        columnZhName: item.fieldsName, // 字段中文名
                        columnId: item.id, //字段id
                    }
                    vm.CicadaManyJoinOutputColumns.push(data)


                });

                vm.pluginMeta = {
                    cicadaManyJoinTwoTableRelations: vm.cicadaManyJoinTwoTableRelations,
                    cicadaManyJoinOutputColumns: vm.CicadaManyJoinOutputColumns,
                    pluginType: vm.pluginType
                }
                //保存接口
                services.sqlSavePlugin(vm.sourceData.id, vm.pluginMeta, loadParams).then(result => {
                    if (result.data.status === 0) {
                        showTip && vm.$message.success("保存成功！");
                        this.$emit("setPreviewAble");
                        fn && fn();
                    }
                })
            },

            cancel() {
                this.clear_data();
                this.close();
            },

            //对输出字段进行过滤
            getLastResult() {
                const vm = this;
                vm.result = vm.result.filter(data => {
                    return vm.getValue.includes(data.tableId)
                })
            },

            //初始化
            async initData() {
                //请求
                const vm = this,
                    {
                        pluginServices,
                        pluginMock,
                        loadParams
                    } = this;
                let services = vm.getServices(pluginServices, pluginMock);
                loadParams.loading = true;

                //接口获取到一个目录树 过滤掉空目录，放入 optionsTable
                services.getDataSetTree().then(res => {
                    vm.optionsTable = vm.setSourceNode(res.data.data);
                });
                //在初始化表

                if (vm.dataBaseInfo.length === 0 && vm.tableData.length === 0) {
                    vm.dataBaseInfo.push({
                        //数组绑定的
                        table1: "",
                        table11: "",
                        op: "求交集",
                        table2: "",
                        condition: "",
                        list: [{
                            field1: "",
                            field2: "",
                        }, ],
                        optionsJoin: [{
                            lable: "求交集",
                            value: "求交集",
                        }, ], //这个是条件
                        optionsFiled1: [],
                        optionsFiled2: [],
                    });

                    vm.tableData.push({
                        left: "",
                        right: "",
                        condition: "",
                        Filed: [{
                            left: "",
                            right: "",
                        }, ],
                    });

                    vm.tableOptions.push({
                        leftTableName: "",
                        rightTableName: "",
                    });

                    vm.tableIds.push({
                        left: "",
                        right: "",
                    });
                }
                //先请求接口获取到流进来的表 赋给 vm.dataBaseInfo[0].table1
                vm.FirstTable = [];
                await services.queryJoinTable(vm.sourceData.id, loadParams).then(res => {
                    //  表的表字段
                    res.data.data.inputColumnList.forEach(result => {
                        let filed = {
                            columnZhName: result.columnZhName === null || result
                                .columnZhName ===
                                undefined || result.columnZhName === '' ? result.columnName :
                                result
                                .columnZhName,
                            columnName: result.columnName,
                            columnType: result.columnType,
                            dataColumnId: result.dataColumnId,
                            length: result.length,
                            metaColumnId: '',
                            precsn: result.precsn,
                            stepCode: result.stepCode,
                            stepId: result.stepId,
                            stepName: result.stepName,
                            id: result.stepId,
                            key: 'left1',
                            replace: result.columnName
                        };

                        vm.firstFiled.push(filed);
                    });

                    //表---》表数据封装，直接在getLeftOption 方法中 直接进行赋值，不需要走借口
                    res.data.data.inputStepList.forEach(result => {
                        let oneTables = {
                            label: result.stepname,
                            value: result.stepid,
                            code: result.stepcode,
                            id: result.stepid,
                        };
                        vm.dataBaseInfo[0].table1 = oneTables.value;
                        vm.FirstTable.push(oneTables);
                        vm.getLeftOption(oneTables, 0, "left");
                    });
                });

                //默认为满足全部条件  condition
                if (
                    vm.dataBaseInfo[0].condition === "" ||
                    vm.dataBaseInfo[0].condition === undefined ||
                    vm.dataBaseInfo[0].condition === null
                ) {
                    vm.dataBaseInfo[0].condition = "AND";
                    vm.selectCondition(vm.dataBaseInfo[0].condition, 0, vm.dataBaseInfo[0].condition);
                }

                //回显
                services.sqlQueryData(vm.sourceData.id, vm.pluginType, loadParams).then(res => {
                    let flag = false
                    //需判断回显的数据是否为空
                    if (res.data.data.pluginMeta.cicadaManyJoinTwoTableRelations.length !== 0) {
                        flag = true
                    }
                    if (flag) {
                        //如果回显有数据 预览不置灰
                        this.$emit("setPreviewAble");
                        vm.queryData(res)
                    }
                });
            },
            //冒泡排序（从小到大）对关联数据进行排序（排序完成之后的数据顺序跟保存提交数据的顺序是一样的）
            sortArray(data) {
                for (let i = 0, len = data.length; i < len; i++) {
                    for (let j = i + 1; j < len; j++) {
                        if (data[i].index > data[j].index) {
                            //将最大的放在最后
                            let t = data[i];
                            data[i] = data[j];
                            data[j] = t;
                        }
                    }
                }
            },
            // //大写英文转小写英文
            // getToLowerCase(value) {
            //     let LowerCaseCode = value.toLowerCase()
            //     return LowerCaseCode
            // },
            //请求数据(回显)
            async queryData(res) {
                const vm = this;

                //回填关联设置数据
                let data = res.data.data.pluginMeta.cicadaManyJoinTwoTableRelations;

                //根据index 进行排序
                vm.sortArray(data);

                //排序后的
                vm.oneTableDatas = data
                //遍历之后进行封装
                data.forEach((judg, index) => {

                    //左表
                    let table1 = {
                        leftStepCode: judg.leftStepCode,
                        leftStepId: judg.leftStepId,
                        leftJoinKey: judg.leftJoinKey,
                        leftStepName: judg.leftStepName,
                    };
                    //右表
                    let table2 = {
                        rightStepCode: judg.rightStepCode,
                        rightStepId: judg.rightStepId,
                        rightJoinKey: judg.rightJoinKey,
                        rightStepName: judg.rightStepName,
                    };
                    //有几个数据就添加几个关联
                    if (index > 0) {
                        vm.handleAddAllChild();
                    }
                    let leftTable = JSON.stringify(table1)
                    let rightTable = JSON.stringify(table2)

                    let condition = judg.condition;

                    //赋值(判断是否是第一个关联)
                    if (index === 0) {
                        vm.dataBaseInfo[index].table1 = table1.leftStepId;
                    } else {
                        vm.dataBaseInfo[index].table11 = table1.leftStepId;

                    }
                    vm.dataBaseInfo[index].table2 = table2.rightStepId
                    vm.tableData[index].left = leftTable
                    vm.tableData[index].right = rightTable

                    vm.dataBaseInfo[index].condition = condition;
                    vm.tableData[index].condition = condition


                    //里面的子条件 转化成JSON格式
                    let parse = JSON.parse(judg.cicadaManyJoinMetaJoinColumns);
                    parse.forEach((judgFileds, idx) => {
                        //左字段
                        let field1 = {
                            leftId: judgFileds.leftId,
                            leftCol: judgFileds.leftCol,
                        };
                        //右字段
                        let field2 = {
                            rightId: judgFileds.rightId,
                            rightCol: judgFileds.rightCol,
                        };
                        //有几个数据就添加几个字段
                        if (idx > 0) {
                            vm.handleAddChild(index);
                        }
                        //赋值
                        let judgFiled1 = JSON.stringify(field1)
                        let judgFiled2 = JSON.stringify(field2)

                        vm.tableData[index].Filed[idx].left = judgFiled1
                        vm.tableData[index].Filed[idx].right = judgFiled2


                        vm.dataBaseInfo[index].list[idx].field1 = field1.leftCol;
                        vm.dataBaseInfo[index].list[idx].field2 = field2.rightCol;
                    });
                });
                //回填输出字段数据
                if (res.data.data.outputColumns !== "" || res.data.data.outputColumns !== []) {
                    vm.result = [];
                    res.data.data.outputColumns.forEach((item, idx) => {
                        let outData = {
                            index: idx,
                            fieldsCode: item.newFieldName,
                            tableId: item.objId,
                            tableCode: item.objCode,
                            tableName: item.oldObjCode,
                            type: item.valType,
                            length: item.length,
                            precsn: item.precision,
                            fieldsName: item.columnZhName,
                            replace: item.replaceFieldName,
                            id: item.oldObjCode + item.newFieldName,
                        };
                        vm.result.push(outData);
                    });
                }


                vm.oneTableDatas.forEach(element => {
                    if (element.index === '0' || element.index === 0) {
                        vm.FirstTable = []
                        vm.firstFiled = []
                        res.data.data.inputColumn.forEach(result => {

                            let filed = {
                                columnZhName: result.columnZhName === null || result
                                    .columnZhName ===
                                    undefined || result.columnZhName === '' ? result
                                    .newFieldName : result.columnZhName,
                                columnName: result.newFieldName,
                                columnType: result.columnType,
                                dataColumnId: element.leftStepName + result.newFieldName,
                                length: result.length,
                                metaColumnId: '',
                                precsn: result.precsn,
                                stepCode: element.leftStepCode,
                                stepId: element.leftStepId,
                                stepName: element.leftStepName,
                                id: element.leftStepId,
                                key: 'left1',
                                replace: result.newFieldName
                            };

                            vm.firstFiled.push(filed);
                        });


                        let tableleftName = {
                            leftStepName: element.leftStepName,
                            leftStepId: element.leftStepId,
                            leftStepCode: element.leftStepCode,
                            left: "left1",
                            key: element.leftJoinKey,
                            leftJoinKey: element.leftJoinKey
                        };
                        let rs = JSON.stringify(tableleftName)

                        vm.tableData[0].left = rs

                        let oneTables = {
                            label: element.leftStepName,
                            value: element.leftStepId,
                            code: element.leftStepCode,
                            id: element.leftStepId,
                        };

                        vm.FirstTable.push(oneTables);
                    }


                });

                //请求回显执行的方法
                await vm.getqueryTable();
            },
            //过滤空目录
            setSourceNode(nodes) {
                const vm = this;
                if (nodes !== null) {
                    return nodes.filter(item => {
                        if (item.children && item.children.length !== 0) {
                            item.children = vm.setSourceNode(item.children);
                        }
                        return item.dbType || (item.children && item.children.length !== 0);
                    });
                }
            },

            //把选择的表给push给tableOptions   --->  将关联设置上已经选择的值进行封装 保存，一遍后续使用
            async getTable() {
                const vm = this,
                    {
                        pluginServices,
                        pluginMock,
                        loadParams
                    } = this;
                vm.filedOptions = [];
                vm.outputTable = []
                let leftData;
                let rightData;
                //得到所有一选择的表id(表id重复的只会push 一次)
                vm.getvalue();
                //对暂存数据 tableData 进行遍历
                vm.tableData.forEach((item, idx) => {

                    if (item.left !== "") {
                        leftData = JSON.parse(item.left);
                    }
                    if (item.right !== "") {
                        rightData = JSON.parse(item.right);
                    }

                    if (item.left !== "") {
                        let flag1;
                        let tableleftName = {
                            label: leftData.leftStepName,
                            value: leftData.leftStepId,
                            code: leftData.leftStepCode,
                            id: leftData.leftStepId,
                            left: "left",
                            key: leftData.leftJoinKey
                        };

                        //将封装好的数据 push 进 outputTable 数组中，在选择输出字段的弹窗显示 ==》其他地方不使用
                        if (vm.outputTable.length === 0) {
                            vm.outputTable.push(tableleftName)
                        } else {
                            vm.outputTable.forEach(out => {
                                vm.getValue.forEach(id => {
                                    if (id === out.id) {
                                        return flag1 = true
                                    } else if (leftData.leftStepId !== out.id && leftData
                                        .leftStepId === id) {
                                        flag1 = false;
                                    }
                                })

                            })
                            if (flag1) {} else {
                                vm.outputTable.push(tableleftName)
                            }

                        }

                        //封装成字符串格式
                        let res = JSON.stringify(tableleftName);
                        //赋值
                        vm.tableOptions[idx].leftTableName = res;

                        //表ID赋值到 tableIds 中，有几个关联，tableIds的长度就为几。为下面for循环遍历 走表的接口
                        vm.tableIds[idx].left = leftData.leftStepId;
                    }
                    if (item.right !== "") {
                        let flag2;
                        let tableRightName = {
                            label: rightData.rightStepName,
                            value: rightData.rightStepId,
                            code: rightData.rightStepCode,
                            id: rightData.rightStepId,
                            right: "right",
                            key: rightData.rightJoinKey
                        };

                        //将封装好的数据 push 进 outputTable 数组中，在选择输出字段的弹窗显示 ==》其他地方不使用
                        vm.outputTable.forEach(out => {
                            vm.getValue.forEach(id => {
                                if (id === out.id) {
                                    return flag2 = true
                                } else if (rightData.rightStepId !== out.id && rightData
                                    .rightStepId === id) {
                                    flag2 = false;
                                }
                            })
                        })

                        if (flag2) {

                        } else {
                            vm.outputTable.push(tableRightName)
                        }
                        //push 表数据，输出字段就不用转换
                        vm.tableIds[idx].right = rightData.rightStepId;
                        //封装成字符串格式
                        let res = JSON.stringify(tableRightName);
                        vm.tableOptions[idx].rightTableName = res;

                    }
                });


                //方法一：在外面做push表字段
                //方法二：在输出字段弹窗做 push 所有表字段
                //调用方法，存取tableOptions数组中存在表的字段数据
                let services = vm.getServices(pluginServices, pluginMock);
                loadParams.loading = true;
                // 在根据tableOptions表中的存在的表,去走接口，获取tableOptions表中的存在的表的字段，push 到filedOptions

                for (let i = 0; i < vm.tableIds.length; i++) {

                    if (vm.tableData[i].left !== "") {
                        leftData = JSON.parse(vm.tableOptions[i].leftTableName);

                    }
                    if (vm.tableData[i].right !== "") {
                        rightData = JSON.parse(vm.tableOptions[i].rightTableName);
                    }

                    //接口
                    if (i !== 0) {
                        if (vm.tableData[i].left !== "") {
                            //在源字段数据中心找有咩相应 表 id 的字段数据，有就从这里面封装保存数据进 filedOptions 没有就走接口，可以减少等待时间
                            if (vm.tableZC[leftData.id]) {

                                vm.tableZC[leftData.id].forEach((item, idx) => {

                                    let res = {
                                        index: i,
                                        LR: leftData.left,
                                        columnZhName: item.columnZhName === null || item
                                            .columnZhName ===
                                            undefined || item.columnZhName === '' ? item
                                            .columnName : item
                                            .columnZhName,
                                        columnName: item.columnName,
                                        length: item.dataLength,
                                        dataColumnId: leftData.label + item
                                            .columnName, //字段id
                                        metaColumnId: "",
                                        precsn: item.precsn,
                                        columnType: item.columnType,
                                        key: leftData.key,
                                        id: leftData.id,
                                        stepName: leftData.label,
                                        stepId: leftData.id, //表id
                                        stepCode: leftData.code, //表code
                                        replace: item.columnName,
                                    };
                                    //返回的数据push 到filedOptions
                                    vm.filedOptions.push(res);
                                })
                            } else {
                                await services.getDataSetColumns(leftData.id, loadParams).then(res => {
                                    vm.tableZC[leftData.id] = res.data.data
                                    res.data.data.forEach((item, idx) => {
                                        let res = {
                                            index: i,
                                            LR: leftData.left,
                                            columnZhName: item.columnZhName === null || item
                                                .columnZhName ===
                                                undefined || item.columnZhName === '' ? item
                                                .columnName : item
                                                .columnZhName,
                                            columnName: item.columnName,
                                            length: item.dataLength,
                                            dataColumnId: leftData.label + item
                                                .columnName, //字段id
                                            metaColumnId: "",
                                            precsn: item.precsn,
                                            columnType: item.columnType,
                                            key: leftData.key,
                                            id: leftData.id,
                                            stepName: leftData.label,
                                            stepId: leftData.id, //表id
                                            stepCode: leftData.code, //表code
                                            replace: item.columnName,
                                        };
                                        //返回的数据push 到filedOptions
                                        vm.filedOptions.push(res);
                                    });
                                });
                            }

                        }

                        if (vm.tableData[i].right !== "") {
                            if (vm.tableZC[rightData.id]) {
                                vm.tableZC[rightData.id].forEach((item, idx) => {
                                    let res = {
                                        index: i,
                                        LR: rightData.right,
                                        columnZhName: item.columnZhName === null || item
                                            .columnZhName ===
                                            undefined || item.columnZhName === '' ? item
                                            .columnName : item
                                            .columnZhName,
                                        columnName: item.columnName,
                                        length: item.dataLength,
                                        dataColumnId: rightData.label + item.columnName, //字段id
                                        metaColumnId: "",
                                        precsn: item.precsn,
                                        columnType: item.columnType,
                                        key: rightData.key, //
                                        stepName: rightData.label,
                                        id: rightData.id,
                                        stepId: rightData.id, //表id
                                        stepCode: rightData.code, //表code
                                        replace: item.columnName,
                                    };
                                    //返回的数据push 到filedOptions
                                    vm.filedOptions.push(res);
                                })
                            } else {
                                await services.getDataSetColumns(rightData.id, loadParams).then(res => {
                                    vm.tableZC[rightData.id] = res.data.data
                                    res.data.data.forEach((item, idx) => {
                                        let res = {
                                            index: i,
                                            LR: rightData.right,
                                            columnZhName: item.columnZhName === null || item
                                                .columnZhName ===
                                                undefined || item.columnZhName === '' ? item
                                                .columnName : item
                                                .columnZhName,
                                            columnName: item.columnName,
                                            length: item.dataLength,
                                            dataColumnId: rightData.label + item
                                                .columnName, //字段id
                                            metaColumnId: "",
                                            precsn: item.precsn,
                                            columnType: item.columnType,
                                            key: rightData.key, //
                                            stepName: rightData.label,
                                            id: rightData.id,
                                            stepId: rightData.id, //表id
                                            stepCode: rightData.code, //表code
                                            replace: item.columnName,
                                        };
                                        //返回的数据push 到filedOptions
                                        vm.filedOptions.push(res);
                                    });
                                });
                            }

                        }

                    } else {
                        vm.filedOptions = []
                        vm.outputTableFileds = []
                        //每次都是先把第一张表的字段push进filedOptions 和 outputTableFileds
                        vm.firstFiled.forEach(filed => {
                            vm.filedOptions.push(filed);
                            vm.outputTableFileds.push(filed)
                        });

                        if (vm.tableOptions[i].rightTableName !== "") {
                            if (vm.tableZC[rightData.id]) {

                                vm.tableZC[rightData.id].forEach((item, idx) => {
                                    let res = {
                                        index: i,
                                        LR: 'right',
                                        columnZhName: item.columnZhName === null || item
                                            .columnZhName ===
                                            undefined || item.columnZhName === '' ? item
                                            .columnName : item
                                            .columnZhName,
                                        columnName: item.columnName,
                                        length: item.dataLength,
                                        dataColumnId: rightData.label + item
                                            .columnName, //字段id
                                        metaColumnId: "",
                                        precsn: item.precsn,
                                        columnType: item.columnType,
                                        key: rightData.key, //字段id
                                        stepName: rightData.label,
                                        id: rightData.id,
                                        stepId: rightData.id, //表id
                                        stepCode: rightData.code, //表code
                                        replace: item.columnName,
                                    };
                                    //返回的数据push 到filedOptions
                                    vm.filedOptions.push(res);
                                })
                            } else {
                                await services.getDataSetColumns(rightData.id, loadParams).then(res => {
                                    vm.tableZC[rightData.id] = res.data.data

                                    res.data.data.forEach((item, idx) => {
                                        let res = {
                                            index: i,
                                            LR: 'right',
                                            columnZhName: item.columnZhName === null || item
                                                .columnZhName ===
                                                undefined || item.columnZhName === '' ? item
                                                .columnName : item
                                                .columnZhName,
                                            columnName: item.columnName,
                                            length: item.dataLength,
                                            dataColumnId: rightData.label + item
                                                .columnName, //字段id
                                            metaColumnId: "",
                                            precsn: item.precsn,
                                            columnType: item.columnType,
                                            key: rightData.key, //字段id
                                            stepName: rightData.label,
                                            id: rightData.id,
                                            stepId: rightData.id, //表id
                                            stepCode: rightData.code, //表code
                                            replace: item.columnName,
                                        };
                                        //返回的数据push 到filedOptions
                                        vm.filedOptions.push(res);
                                    });
                                });
                            }

                        }
                    }

                }
                //不走接口且循环完之后 loading 设置为false
                loadParams.loading = false;
                // 获取选择输出字段弹窗中的表 的 全部字段
                await vm.getoutputTableFileds()
            },

            //清空自己本身的字段
            reSetOneSelfFiled(val, idx, LeftOrRight) {
                const vm = this;
                if (LeftOrRight === "left") {
                    vm.dataBaseInfo[idx].list.forEach((filed, idx1) => {
                        if (idx1 !== "" || idx1 !== undefined || idx1 !== null) {
                            vm.dataBaseInfo[idx].list[idx1].field1 = "";
                        }
                    });
                } else if (LeftOrRight === "right") {
                    vm.dataBaseInfo[idx].list.forEach((filed, idx1) => {
                        if (idx1 !== "" || idx1 !== undefined || idx1 !== null) {
                            vm.dataBaseInfo[idx].list[idx1].field2 = "";
                        }
                    });
                }
            },
            //获取表值，存入 getValue  数组之中 （获取已经选择的表id）
            getvalue() {
                const vm = this;
                vm.getValue = []
                for (let i = 0; i < vm.dataBaseInfo.length; i++) {
                    if (i === 0) {
                        if (vm.dataBaseInfo[i].table1 !== '' && !vm.getValue.includes(vm.dataBaseInfo[i]
                                .table1) && vm.dataBaseInfo[i].table1 !== null && vm.dataBaseInfo[i].table1 !==
                            undefined) {
                            vm.getValue.push(vm.dataBaseInfo[i].table1)
                            if (vm.dataBaseInfo[i].table2 !== '' && !vm.getValue.includes(vm.dataBaseInfo[i]
                                    .table2) && vm.dataBaseInfo[i].table2 !== null && vm.dataBaseInfo[i].table2 !==
                                undefined) {
                                vm.getValue.push(vm.dataBaseInfo[i].table2)
                            }
                        }
                    } else {
                        if (vm.dataBaseInfo[i].table11 !== '' && !vm.getValue.includes(vm.dataBaseInfo[i]
                                .table11) && vm.dataBaseInfo[i].table11 !== null && vm.dataBaseInfo[i].table11 !==
                            undefined) {
                            vm.getValue.push(vm.dataBaseInfo[i].table11)
                        }
                        if (vm.dataBaseInfo[i].table2 !== '' && !vm.getValue.includes(vm.dataBaseInfo[i]
                                .table2) && vm.dataBaseInfo[i].table2 !== null && vm.dataBaseInfo[i].table2 !==
                            undefined) {
                            vm.getValue.push(vm.dataBaseInfo[i].table2)
                        }
                    }

                }
            },
            //关联1的左侧表固定的触发方法（其他地方不要使用---> 目的是为了进来时不走接口就有字段）
            async getLeftOption(val, idx, LeftOrRight) {
                const vm = this;
                let res = {
                    leftStepId: val.id,
                    leftStepCode: val.code,
                    leftStepName: val.label,
                    leftJoinKey: val.code,
                };

                let tableleftName = {
                    label: res.leftStepName,
                    value: res.leftStepId,
                    code: res.leftStepCode,
                    id: res.leftStepId,
                    left: "left",
                    key: res.leftJoinKey,
                };

                //封装成字符串
                let data = JSON.stringify(res);
                vm.tableData[idx][LeftOrRight] = data;

                //对字段的数组置空，防止重复
                vm.filedOptions = []
                vm.outputTableFileds = []
                //每次都是先把第一张表的字段push进filedOptions
                vm.firstFiled.forEach(filed => {
                    vm.filedOptions.push(filed);
                    vm.outputTableFileds.push(filed)
                });
                vm.outputTable.push(tableleftName)


                //每次进来都置空
                //将对应表的字段push到对应的字段下拉框中
                vm.dataBaseInfo[idx].optionsFiled1 = [];
                vm.filedOptions.forEach(item => {
                    if (item.id === val.id) {
                        let res = {
                            id: item.id,
                            value: item.columnName,
                            label: item.columnZhName,
                        }
                        vm.dataBaseInfo[idx].optionsFiled1.push(res);
                    }
                });
            },
            //下拉数据映射（表改变时触发的change方法）
            async getOptionData(val, nodes, idx, LeftOrRight) {

                const vm = this;

                if (LeftOrRight === "left") {
                    vm.dataBaseInfo[idx].table11 = val.id;
                } else {
                    vm.dataBaseInfo[idx].table2 = val.id;
                }


                //判断是左还是右，进入相应的方法
                if (LeftOrRight === "left") {

                    let res = {
                        leftStepId: val.id,
                        leftStepCode: val.code,
                        leftStepName: val.label,
                        leftJoinKey: val.code,
                    };
                    let data = JSON.stringify(res);
                    vm.tableData[idx][LeftOrRight] = data;
                }

                if (LeftOrRight === "right") {

                    //对数据进行封装
                    let res = {
                        rightStepId: val.id,
                        rightStepCode: val.code,
                        rightStepName: val.label,
                        rightJoinKey: val.code,
                    };

                    let data = JSON.stringify(res);
                    vm.tableData[idx][LeftOrRight] = data;


                }

                await vm.getTable();

                vm.reSetOneSelfFiled(val.id, idx, LeftOrRight)
                //每次进来都置空
                //将对应表的字段push到对应的字段下拉框中
                let fieldName = LeftOrRight === "left" ? "optionsFiled1" : "optionsFiled2";
                vm.dataBaseInfo[idx][fieldName] = [];

                vm.filedOptions.forEach(item => {

                    if (item.stepId === val.id && LeftOrRight === item.LR && idx === item.index) {

                        let res = {
                            id: item.dataColumnId,
                            value: item.columnName,
                            label: item.columnZhName,
                        };
                        vm.dataBaseInfo[idx][fieldName].push(res);
                    }
                });

            },

            //下拉字段数据映射（回显调用，其他不调用）
            async getqueryTable() {

                const vm = this,
                    {
                        pluginServices,
                        pluginMock,
                        loadParams
                    } = this;
                vm.filedOptions = [];
                vm.outputTable = [];
                vm.outputTableFileds = []
                let leftData;
                let rightData;

                vm.getvalue()
                vm.tableData.forEach((item, idx) => {

                    if (item.left !== "") {
                        leftData = JSON.parse(item.left);
                    }
                    if (item.right !== "") {
                        rightData = JSON.parse(item.right);
                    }
                    if (item.left !== "") {
                        let flag = false;

                        let tableleftName = {
                            label: leftData.leftStepName,
                            value: leftData.leftStepId,
                            code: leftData.leftStepCode,
                            id: leftData.leftStepId,
                            left: "left",
                            key: leftData.leftJoinKey
                        };

                        if (vm.outputTable.length === 0) {
                            vm.outputTable.push(tableleftName)
                        } else {
                            vm.outputTable.forEach(out => {
                                vm.getValue.forEach(id => {
                                    if (id === out.id) {
                                        flag = true
                                    } else if (leftData.leftStepId !== out.id &&
                                        leftData
                                        .leftStepId === id) {
                                        flag = false;
                                    }
                                })
                            })

                            if (flag === false) {
                                vm.outputTable.push(tableleftName)
                            }

                        }


                        //封装成字符串格式
                        let res = JSON.stringify(tableleftName);
                        vm.tableOptions[idx].leftTableName = res;

                        vm.tableIds[idx].left = leftData.leftStepId;
                    }
                    if (item.right !== "") {
                        let flag = false;
                        let tableRightName = {
                            label: rightData.rightStepName,
                            value: rightData.rightStepId,
                            code: rightData.rightStepCode,
                            id: rightData.rightStepId,
                            right: "right",
                            key: rightData.rightJoinKey
                        };
                        vm.outputTable.forEach(out => {
                            vm.getValue.forEach(id => {
                                if (id === out.id) {
                                    flag = true
                                } else if (rightData.rightStepId !== out.id && rightData
                                    .rightStepId === id) {
                                    flag = false;
                                }
                            })
                        })

                        if (flag === false) {
                            vm.outputTable.push(tableRightName)
                        }

                        vm.tableIds[idx].right = rightData.rightStepId;
                        //封装成字符串格式
                        let res = JSON.stringify(tableRightName);
                        vm.tableOptions[idx].rightTableName = res;
                    }

                });


                //方法一：在外面做push表字段
                //方法二：在输出字段弹窗做 push 所有表字段
                //调用方法，存取tableOptions数组中存在表的字段数据
                let services = vm.getServices(pluginServices, pluginMock);
                loadParams.loading = true;
                // 在根据tableOptions表中的存在的表,去走接口，获取tableOptions表中的存在的表的字段，push 到filedOptions
                // 根据表id 将对应表的字段数据 push 到optionFiled 中
                for (let i = 0; i < vm.tableIds.length; i++) {

                    if (vm.tableData[i].left !== "") {
                        leftData = JSON.parse(vm.tableOptions[i].leftTableName);

                    }
                    if (vm.tableData[i].right !== "") {
                        rightData = JSON.parse(vm.tableOptions[i].rightTableName);
                    }

                    //接口
                    if (i !== 0) {
                        if (vm.tableData[i].left !== "") {
                            if (vm.tableZC[leftData.id]) {
                                vm.tableZC[leftData.id].forEach((item, idx) => {

                                    let res = {
                                        index: i,
                                        LR: 'left',
                                        columnZhName: item.columnZhName,
                                        columnName: item.columnName,
                                        length: item.dataLength,
                                        dataColumnId: leftData.label + item
                                            .columnName, //字段id
                                        metaColumnId: "",
                                        precsn: item.precsn,
                                        columnType: item.columnType,
                                        key: leftData.key,
                                        id: leftData.id,
                                        stepName: leftData.label,
                                        stepId: leftData.id, //表id
                                        stepCode: leftData.code, //表code
                                        replace: item.columnName,
                                    };
                                    //返回的数据push 到filedOptions
                                    vm.filedOptions.push(res);
                                })
                            } else {
                                await services.getDataSetColumns(leftData.id, loadParams).then(res => {
                                    vm.tableZC[leftData.id] = res.data.data
                                    res.data.data.forEach((item, idx) => {
                                        let result = {
                                            index: i,
                                            LR: 'left',
                                            columnZhName: item.columnZhName,
                                            columnName: item.columnName,
                                            length: item.dataLength,
                                            dataColumnId: leftData.label + item
                                                .columnName, //字段id
                                            metaColumnId: "",
                                            precsn: item.precsn,
                                            columnType: item.columnType,
                                            key: leftData.key,
                                            id: leftData.id,
                                            stepName: leftData.label,
                                            stepId: leftData.id, //表id
                                            stepCode: leftData.code, //表code
                                            replace: item.columnName,
                                        };
                                        //返回的数据push 到filedOptions
                                        vm.filedOptions.push(result);
                                    });
                                });
                            }
                            vm.dataBaseInfo[i].optionsFiled1 = []
                            vm.filedOptions.forEach(item => {
                                if (item.stepId === leftData.id && 'left' === item.LR && i === item
                                    .index &&
                                    leftData.key === item.key) {
                                    let res = {
                                        id: item.dataColumnId,
                                        value: item.columnName,
                                        label: item.columnZhName,
                                    };
                                    vm.dataBaseInfo[i].optionsFiled1.push(res);
                                }
                            });


                        }
                        if (vm.tableData[i].right !== "") {
                            if (vm.tableZC[rightData.id]) {
                                vm.tableZC[rightData.id].forEach((item, idx) => {
                                    let res = {
                                        index: i,
                                        LR: rightData.right,
                                        columnZhName: item.columnZhName,
                                        columnName: item.columnName,
                                        length: item.dataLength,
                                        dataColumnId: rightData.label + item.columnName, //字段id
                                        metaColumnId: "",
                                        precsn: item.precsn,
                                        columnType: item.columnType,
                                        key: rightData.key, //
                                        stepName: rightData.label,
                                        id: rightData.id,
                                        stepId: rightData.id, //表id
                                        stepCode: rightData.code, //表code
                                        replace: item.columnName,
                                    };
                                    //返回的数据push 到filedOptions
                                    vm.filedOptions.push(res);
                                })
                            } else {
                                await services.getDataSetColumns(rightData.id, loadParams).then(res => {
                                    vm.tableZC[rightData.id] = res.data.data
                                    res.data.data.forEach((item, idx) => {
                                        let result = {
                                            index: i,
                                            LR: 'right',
                                            columnZhName: item.columnZhName,
                                            columnName: item.columnName,
                                            length: item.dataLength,
                                            dataColumnId: rightData.label + item
                                                .columnName, //字段id
                                            metaColumnId: "",
                                            precsn: item.precsn,
                                            columnType: item.columnType,
                                            key: rightData.key, //
                                            stepName: rightData.label,
                                            id: rightData.id,
                                            stepId: rightData.id, //表id
                                            stepCode: rightData.code, //表code
                                            replace: item.columnName,
                                        };
                                        //返回的数据push 到filedOptions
                                        vm.filedOptions.push(result);

                                    });
                                });
                            }

                            vm.dataBaseInfo[i].optionsFiled2 = []
                            vm.filedOptions.forEach(item => {
                                if (item.stepId === rightData.id && 'right' === item.LR && i === item
                                    .index && rightData.key === item.key) {
                                    let res = {
                                        id: item.dataColumnId,
                                        value: item.columnName,
                                        label: item.columnZhName,
                                    };
                                    vm.dataBaseInfo[i].optionsFiled2.push(res);

                                }
                            });


                        }

                    } else {
                        //每次都是先把第一张表的字段push进filedOptions
                        vm.firstFiled.forEach(filed => {
                            vm.filedOptions.push(filed);
                            vm.outputTableFileds.push(filed)
                        });

                        if (vm.tableData[i].right !== "") {
                            await services.getDataSetColumns(rightData.id, loadParams).then(res => {
                                vm.tableZC[rightData.id] = res.data.data
                                res.data.data.forEach((item, idx) => {
                                    let result = {
                                        index: i,
                                        LR: 'right',
                                        columnZhName: item.columnZhName,
                                        columnName: item.columnName,
                                        length: item.dataLength,
                                        dataColumnId: rightData.label + item
                                            .columnName, //字段id
                                        metaColumnId: "",
                                        precsn: item.precsn,
                                        columnType: item.columnType,
                                        key: rightData.key, //字段id
                                        stepName: rightData.label,
                                        id: rightData.id,
                                        stepId: rightData.id, //表id
                                        stepCode: rightData.code, //表code
                                        replace: item.columnName,
                                    };
                                    //返回的数据push 到filedOptions

                                    vm.filedOptions.push(result);
                                });
                            });
                            vm.dataBaseInfo[i].optionsFiled2 = []
                            vm.filedOptions.forEach(item => {
                                if (item.stepId === rightData.id && 'right' === item.LR && i === item
                                    .index && rightData.key === item.key) {
                                    let res = {
                                        id: item.dataColumnId,
                                        value: item.columnName,
                                        label: item.columnZhName,
                                    };

                                    vm.dataBaseInfo[i].optionsFiled2.push(res);
                                }
                            });
                        }

                    }
                }
                //获取选择输出字段弹窗中表的字段数据
                await vm.getoutputTableFileds()
            },

            getoutputTableFileds() {
                const vm = this,
                    {
                        pluginServices,
                        pluginMock,
                        loadParams
                    } = this;
                //置空
                vm.outputTableFileds = []
                vm.outputTable = vm.outputTable.filter(data => {
                    return data.id !== null
                })
                let services = vm.getServices(pluginServices, pluginMock);

                vm.outputTable.forEach((value, index) => {
                    if (vm.tableZC[value.id]) {
                        vm.tableZC[value.id].forEach((item, idx) => {
                            let res = {
                                index: index,
                                columnZhName: item.columnZhName === null || item
                                    .columnZhName ===
                                    undefined || item.columnZhName === '' ? item
                                    .columnName : item
                                    .columnZhName,
                                columnName: item.columnName,
                                length: item.dataLength,
                                dataColumnId: value.label + item.columnName, //字段id
                                metaColumnId: "",
                                precsn: item.precsn,
                                columnType: item.columnType,
                                key: value.key, //
                                stepName: value.label,
                                id: value.id,
                                stepId: value.id, //表id
                                stepCode: value.code, //表code
                                replace: item.columnName,
                            };
                            //返回的数据push 到filedOptions
                            vm.outputTableFileds.push(res);
                        })
                    } else {
                        services.getDataSetColumns(value.id, loadParams).then(res => {
                            vm.tableZC[value.id] = res.data.data
                            res.data.data.forEach((item, idx) => {

                                let res = {
                                    index: index,
                                    columnZhName: item.columnZhName === null || item
                                        .columnZhName ===
                                        undefined || item.columnZhName === '' ? item
                                        .columnName : item
                                        .columnZhName,
                                    columnName: item.columnName,
                                    length: item.dataLength,
                                    dataColumnId: value.label + item
                                        .columnName, //字段id
                                    metaColumnId: "",
                                    precsn: item.precsn,
                                    columnType: item.columnType,
                                    key: value.key, //字段id
                                    stepName: value.label,
                                    id: value.id,
                                    stepId: value.id, //表id
                                    stepCode: value.code, //表code
                                    replace: item.columnName,
                                };
                                //返回的数据push 到 outputTableFileds
                                vm.outputTableFileds.push(res);
                            });
                        });
                    }


                })

                //不走接口循环完之后 loading 设置为false
                loadParams.loading = false;
            },

            //选中字段触发的changge方法
            selectFiledChange(val, idx, LeftOrRight, index) {

                const vm = this;

                if (LeftOrRight === "left") {
                    vm.dataBaseInfo[idx].optionsFiled1.forEach(item => {
                        if (item.value === val) {
                            let res = {
                                leftId: item.id,
                                leftCol: item.value,
                            };
                            let data = JSON.stringify(res);
                            vm.tableData[idx].Filed[index].left = data;
                        }
                    });
                }

                if (LeftOrRight === "right") {
                    vm.dataBaseInfo[idx].optionsFiled2.forEach(item => {
                        if (item.value === val) {

                            let res = {
                                rightId: item.id,
                                rightCol: item.value,
                            };
                            let data = JSON.stringify(res);
                            vm.tableData[idx].Filed[index].right = data;
                        }
                    });
                }

            },

            //绑定 condition 条件
            selectCondition(val, idx, LeftOrRight) {
                const vm = this;
                if (LeftOrRight === "AND" || LeftOrRight === "OR") {
                    vm.tableData[idx].condition = val;
                }
            },

            //对输出字段进行封装，push给 CicadaManyJoinOutputColumns
            updateOutPut(data) {
                this.result = data;
                this.clear_data();
                this.close();
                this.triggerEvent("resize", 300);
            },
        },
    };
</script>
<style lang="less" scoped src="./multiTable.less"></style>
<style scoped lang="less">
    .mainButton {
        width: 100%;
    }

    .ce-operation_box {
        display: flex;
        justify-content: center;
    }

    .radio {
        display: flex;
        align-items: center;
        /*padding: 0 14px;*/

        justify-content: center;
        flex: 1;
        margin-right: 4px;
        width: 150px;
        height: 44px;
        border-radius: 2px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        box-sizing: border-box;
        cursor: pointer;
    }

    .radio:hover {
        border: 1px solid #0088ff;
        box-shadow: 0 2px 2px 0 rgba(0, 136, 255, 0.15);
    }

    .radio_i {
        font-size: 26px;
        color: #0088ff;
    }

    .radio_span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-left: 14px;
    }

    .is-active {
        border: 1px solid #0088ff;
        box-shadow: 0 2px 2px 0 rgba(0, 136, 255, 0.15);
    }

    .leftTitle {
        margin-left: 200px;
        float: left;
        width: 200px;
        color: #0088ff;
    }

    .rightTitle {
        margin-left: 120px;
        float: left;
        width: 200px;
        color: yellowgreen;
    }

    .dataBaseForm {
        clear: both;
        margin-top: 10px;
        align-items: center;
    }

    .masked {
        width: 100px;
    }

    .textFilter {
        margin-left: 200px;
    }

    .textFilter2 {
        margin-left: 45px;
    }

    .symbol {
        margin-left: 35px;
        font-size: 25px;
    }

    .item {
        margin-left: 150px;
    }
</style>
