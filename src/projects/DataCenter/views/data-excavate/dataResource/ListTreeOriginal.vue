<template>
    <div class="listTreeOriginal">
        <div class="filter-b">
            <el-input placeholder="请输入资源名称搜索" v-model="filterText" size="small" class="ce-search">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
        </div>
        <div class="tree-cont selectn">
            <el-tree
                    ref="tree"
                    :data="treeData"
                    node-key="id"
                    default-expand-all
                    :expand-on-click-node="false"
                    :props="treeProps"
                    :filter-node-method="filterNode"
            >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span class="node-label" v-if="node.level > 1" :title="node.label" draggable="true"
                              @dragend="dragend"
                              @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' ,type : 'relation' ,data : data})"
                        >{{ node.label }}</span>
                        <span v-else >{{ node.label }}</span>
                    </span>
            </el-tree>
        </div>
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    export default {
        name: "ListTreeOriginal" ,
        data() {
            return {
                treeData: [],
                filterText: '' ,
                treeProps : {
                    label : 'name'
                }
            }
        },
        watch : {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        methods :{
            dragend(){
                globalBus.$emit('dragend');
            },
            initTree() { //
                this.$axios.get("/dataModeling/localDataObj?condition=&dataTypeId=")
                    .then(res =>{
                        console.log("获取原始数据集返回结果：");
                        console.log(res);
                        this.treeData = res.data.data;
                    }).catch(err =>{
                    console.error(err);
                });
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
            addNode(plug){
                globalBus.$emit('plugNode', plug);
            },
        },
        created() {
            //this.initTree();
        }
    }
</script>

<style scoped>
    .listTreeOriginal {
        height: 100%;
    }

    .tree-cont {
        height: calc(100% - 42px);
        overflow: auto;
    }
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }
    .node-label {
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .filter-b {
        margin-bottom: 6px;
    }
</style>