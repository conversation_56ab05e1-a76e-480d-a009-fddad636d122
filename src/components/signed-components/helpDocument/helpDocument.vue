<template>
    <span class="helpDocument">
        <span v-if="isShow && code" class="poi ml5 mr10">
            <el-tooltip class="item" effect="dark" :content="title" placement="bottom" >
               <i class="dg-iconp icon-help"  @click.stop="jumpDocument"></i>
            </el-tooltip>
            <slot name="name"></slot>
        </span>
    </span>
</template>

<script>
    import store from "@store/index"
    export default {
        name: "helpDocument",
        props: {
            code:{
                type: String,
                default: ''
            },
        },
        watch: {
            code(val) {
                let info = store.getters.functionCodeInfo;
                this.isShow = info && info.showFuncCodes && info.showFuncCodes.includes(val);
            }
        },
        data() {
            return {
                title: '查看帮助文档',
                isShow: true,
            }
        },
        mounted(){
            let info = store.getters.functionCodeInfo;
            this.isShow = info && info.showFuncCodes && info.showFuncCodes.includes(this.code);
        },
        methods:{
            jumpDocument(){
                let info = store.getters.functionCodeInfo;
                let url = `${info.helpDocIp}/zyplayer-doc-manage/zyplayer-doc-wiki/page/forwardToPage?forwardIp=${info.helpDocFrontIp}&funcCode=${this.code}&dcVersion=${info.dcVersion}`
                window.open(url, '_blank');
            }
        }
    }
</script>

<style scoped lang="less">
    .helpDocument{
        display: inline-flex;
    }
    .helpDocument:hover{
       color:#0088ff;
    }

</style>