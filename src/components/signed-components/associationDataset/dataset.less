@activeColor: rgb(0, 136, 255);
@defColor: #c1c1c1;
@warningColor: #fa1414;
.as-dataset {
    &-outer {
        width: 100%;
        height: 100%;
        position: relative;
    }

    &-drag {
        width: 100%;
        height: 100%;
        position: relative;
    }

    &-empty.empty {
        color: rgba(0, 0, 0, .45);
        font-size: 16px;
        position: absolute;
        top: 0;
        left: 0;
        cursor: default;
    }

    &-link {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        width: 90px;
        height: 36px;
        z-index: 2;

        &-icon {
            font-size: 24px;
            line-height: 1;
            color: rgba(@activeColor, 0.45);
            cursor: pointer;
            position: relative;
            z-index: 3;
            background: #fff;
        }

        &-line {
            position: absolute;
            z-index: 1;

            &.horizontal {
                height: 1px;
                right: 0;
                background-image: linear-gradient(to right, @defColor 0%, @defColor 100%);
                background-repeat: repeat-x;
                background-size: 6px 1px;
            }

            &.vertical {
                width: 1px;
                left: 20px;
                bottom: 18px;
                background-image: linear-gradient(to top, @defColor 0%, @defColor 100%);
                background-repeat: repeat-y;
                background-size: 1px 6px;
            }
        }

        &:hover {
            z-index: 1000;

            .as-dataset-link-icon {
                color: rgba(@activeColor, 0.85);
            }

            .as-dataset-link-line {
                background: rgba(@activeColor, 0.85);
            }

        }

        &-warning {
            .as-dataset-link-line {
                background-image: linear-gradient(to right, @warningColor 0%, @warningColor 100%);
            }
            .as-dataset-link-icon {
                color: @warningColor;
            }
        }
    }


    &-item {
        display: flex;
        align-items: center;
        width: 204px;
        height: 36px;
        border: 1px solid rgba(@activeColor, 0.35);
        border-radius: 2px;
        cursor: pointer;
        position: absolute;
        box-sizing: border-box;
        top: 0;
        left: 0;

        &-icon {
            display: inline-block;
            width: 34px;
            height: 100%;
            line-height: 34px;
            text-align: center;
            background-color: rgba(#0088ff, 0.12);

            > i {
                color: #1890ff;
                font-size: 18px;
            }
        }

        &-input {
            padding: 0 14px;
            box-sizing: border-box;
            font-size: 14px;
            flex: 1;
            overflow: hidden;
        }

        &-name {
            width: 100%;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        &-close {
            display: none;
            position: absolute;
            top: -12px;
            right: -8px;
            cursor: pointer;
            line-height: 1;

            > i {
                background-color: #fff;
                font-size: 16px;
                color: #1890ff;
                border-radius: 50%;
            }
        }

        &:hover {
            border-color: #0088ff;

            > .as-dataset-item-close {
                display: block;
            }
        }

    }

    &-ghost {
        &-item {
            width: 204px;
            height: 36px;
            //border: 1px dashed @defColor;
            border-radius: 2px;
            position: absolute;
            box-sizing: border-box;
            top: 0;
            left: 0;
            z-index: 0;
            padding: 0 10px;
            display: flex;
            align-items: center;
            color: @defColor;
            background: linear-gradient(90deg, @defColor 50%, transparent 0) repeat-x,
            linear-gradient(90deg, @defColor 50%, transparent 0) repeat-x,
            linear-gradient(0deg, @defColor 50%, transparent 0) repeat-y,
            linear-gradient(0deg, @defColor 50%, transparent 0) repeat-y;
            background-size: 6px 1px, 6px 1px, 1px 6px, 1px 6px;
            background-position: 0 0, 0 100%, 0 0, 100% 0;
        }

        &-link {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            width: 90px;
            height: 36px;
            z-index: 0;

            > .horizontal {
                background-image: linear-gradient(to right, @defColor 0%, @defColor 50%, transparent 50%);
            }

            > .vertical {
                background-image: linear-gradient(to top, @defColor 0%, @defColor 50%, transparent 50%);
            }
        }

        &-active {
            > .as-dataset-ghost-item {
                background: linear-gradient(90deg, @activeColor 50%, transparent 0) repeat-x,
                linear-gradient(90deg, @activeColor 50%, transparent 0) repeat-x,
                linear-gradient(0deg, @activeColor 50%, transparent 0) repeat-y,
                linear-gradient(0deg, @activeColor 50%, transparent 0) repeat-y;
                background-size: 6px 1px, 6px 1px, 1px 6px, 1px 6px;
                background-position: 0 0, 0 100%, 0 0, 100% 0;
                animation: ghostItemRun .5s infinite linear;
            }

            .horizontal {
                background-image: linear-gradient(to right, @activeColor 0%, @activeColor 50%, transparent 50%);
                animation: ghostRun .5s infinite linear;
            }

            .vertical {
                background-image: linear-gradient(to top, @activeColor 0%, @activeColor 50%, transparent 50%);
                animation: ghostRun1 .5s infinite linear;
            }
        }
    }

    @keyframes ghostRun {
        from {
            background-image: linear-gradient(to right, @activeColor 0%, @activeColor 50%, transparent 50%);
        }

        to {
            background-image: linear-gradient(to right, transparent 0%, transparent 50%, @activeColor 50%);
        }
    }
    @keyframes ghostRun1 {
        from {
            background-image: linear-gradient(to top, @activeColor 0%, @activeColor 50%, transparent 50%);
        }

        to {
            background-image: linear-gradient(to top, transparent 0%, transparent 50%, @activeColor 50%);
        }
    }

    @keyframes ghostItemRun {
        100% {
            background-position: 6px 0, -6px 100%, 0 -6px, 100% 6px;
        }
    }

}
