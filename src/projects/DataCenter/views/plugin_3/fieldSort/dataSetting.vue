<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/7/29
-->
<template>
    <el-form @submit.native.prevent inline label-suffix=":">
        <el-form-item :label="label">
            <el-input v-model.number="value"
                      v-input-limit:number
                      ref="sortInx"
                      @input="onInputFn"></el-input>
        </el-form-item>
        <dialog-btn-group v-footer :data="btnGroup" />
    </el-form>
</template>

<script>
import dialogBtnGroup from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn.vue"
export default {
    name: "dataSetting",
    components:{
        dialogBtnGroup
    },
    props: {
        maxVal:Number,
    },
    data() {
        return {
            label: "排序号",
            value: "",
            btnGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.submitFn,
                    show: () => true
                },
            ]
        }
    },
    methods: {
        cancelFn(){
            this.$emit("close")
        },
        submitFn(){
            if(this.value === 0) {
                this.$refs.sortInx.focus();
                return this.$message.warning("排序号须大于 0");
            }
            if(!this.value) {
                this.$refs.sortInx.focus();
                return this.$message.warning("请输入排序号");
            }
            this.$emit("submit", this.value);
            this.cancelFn();

        },
        onInputFn(){
            const {maxVal} = this;
            // 限制 max
            if(this.value && this.value > maxVal ) {
                this.value = maxVal;
                this.$message.warning(`字段总数为 ${maxVal},序号不能超过总数`);
            }
        }
    }
}
</script>

<style scoped lang="less">

</style>
