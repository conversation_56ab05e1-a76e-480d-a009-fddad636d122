/*
* 头部公用方法
* */
import {common} from "@/api/commonMethods/common";
import {goToLogout} from "@/api/dids-logout";

export const headerMixins = {
    mixins: [common],
    data() {
        let loginServices = require("../../services/login/login-services"),
            loginMock = require("../../mock/login/login-mock"),
            manageServices = require("../../services/model-market/manage-services"),
            manageMock = require("../../mock/model-market/manage-mock");
        return {
            loginServices,
            loginMock,
            manageServices,
            manageMock,
            marketName: this.$marketName,
            systemName: this.$systemName,
            hideMarket: this.$hideMarket,
        }
    },
    watch: {
        $route: {
            handler: function (val, oldVal) {
                this.setCurrentTab();
                this.triggerEvent("resize", 300);
            },
            // 深度观察监听
            deep: true
        }
    },
    computed: {
        countColor() {
            let {$layoutBg} = this;
            if (!$layoutBg) return;
            let m = $layoutBg.match(/[\da-z]{2}/g);
            if (!m) return $layoutBg;
            for (let i = 0; i < m.length; i++) {
                m[i] = parseInt(m[i], 16);
                if (isNaN(m[i])) return $layoutBg;
            }
            if (m.length !== 3) return $layoutBg;
            let colors = "";
            for (let i = 0; i < 10; i++) {
                let dis = 12;
                let r = Math.floor(m[0] - dis > 0 ? m[0] - dis : 0).toString(16),
                    g = Math.floor(m[1] - dis > 0 ? m[1] - dis : 0).toString(16),
                    b = Math.floor(m[2] - dis > 0 ? m[2] - dis : 0).toString(16);
                r = r.length === 2 ? r : '0' + r;
                g = g.length === 2 ? g : '0' + g;
                b = b.length === 2 ? b : '0' + b;
                colors = r + g + b;
                break;
            }
            return '#' + colors;
        }
    },
    methods: {
        goMarket() {
            this.initMainPageModel();
            let routeData = this.$router.resolve({name: "MarketHome"});
            window.open(routeData.href, '_blank');
        },
        goHome() {
            let routeData = this.$router.resolve({name: "Home"});
            window.open(routeData.href, '_blank');
        },
        //增加大数据模型市场日志
        initMainPageModel() {
            const vm = this, {manageServices, manageMock} = this;
            let services = vm.getServices(manageServices, manageMock);
            services.initMainPageModel();
        },

        /**
         * 添加二级路由
         */
        handleToSubRoute(route) {
            this.$set(this.active, 0, route.path);
            let children = route.hiddenChildren ? [] : route.children;
            if (!route.children || route.children.length === 0) {
                this.$router.push({path: route.path});
                this.subRoutes = [];
            } else {
                this.subRoutes = children;
                this.$set(this.active, 1, route.children[0].path);
                this.$router.push({path: route.children[0].path});
            }
        },
        /**
         * 退出登录 提示
         * */
        loginOut() {
            this.confirm("提示", "确定退出系统？", () => {
                this.logoutFn();
            });

        },
        /**
         * 退出登录 接口
         * */
        async logoutFn() {
            const vm = this;
            let {loginServices, loginMock, $loginWay} = this;
            let services = vm.getServices(loginServices, loginMock);
            let res;
            if ($loginWay === 'dids') {
                res = await services.didsLogout();
            } else {
                res = await services.logout();
            }
            if (res.data.status === 0) {
                localStorage.removeItem("userInfo");
                localStorage.removeItem("userRight");
                localStorage.removeItem("userRole");
                vm.$store.dispatch("logout", null);
                sessionStorage.removeItem('sessionId');
                localStorage.removeItem("sessionId");

                if ($loginWay === 'dids') {
                    goToLogout(res.data.data.data);
                } else window.location.reload();
            }
        }
    },
    created() {
        this.init();
    }
}
