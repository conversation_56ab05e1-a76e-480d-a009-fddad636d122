<template>
    <ul class="childPlugIn" ref="child" :class="{'overLeft' : overLeft ,'overRight' : overRight }" >
        <li v-for="(child , index) in childData"
            :key="index"
            :draggable="true"
            @dragstart.stop="addNode(child)"
            @dragend="dragend"
            @mouseenter="getChildPos($event)"
        >
            <svg class="eda_icon" aria-hidden="true">
                <use :xlink:href="child.icon"></use>
            </svg>
            <p class="plug-name" :title="child.label">{{child.label}}</p>
        </li>
    </ul>
</template>

<script>
    import {globalBus} from '@/api/globalBus';
    import $ from 'jquery'

    export default {
        name: "ChildPlugIn",
        data() {
            return {
                childOver: false ,
            }
        },
        props: {
            childData: Array,
            overLeft: Boolean,
            overRight: Boolean
        },
        computed: {
            addRightC() {
                return this.isOver;
            },

        },
        watch : {
            visible(val){

            }
        },
        methods: {
            dragend(){
                globalBus.$emit('dragend');
            },
            addNode(plug) {
                globalBus.$emit('plugNode', plug);
            },
            getChildPos(e) {
                let target = e.currentTarget,
                    screenW = $(window).width(),
                    left = e.pageX - e.layerX + $(target).width(),
                    childW = $(target).children('.childPlugIn').outerWidth();
                screenW - left < childW ? this.childOver = true : this.childOver = false;
            },
            countChildWidth() {
                let childLis = $(this.$refs.child).children(),
                    width = 0, maxW = 0 ,
                    len = childLis.length <= 4 ? childLis.length : 4; //一排限6个

                for (let i = 0; i < childLis.length; i++) {
                    width += Math.ceil(childLis.eq(i).outerWidth());
                    if(i > 0 && (i+1) % len === 0){
                        if(width > maxW){
                            maxW = width;
                        }
                        width = 0;
                    }else if(childLis.length <= 4) {
                        maxW = width;
                    }
                }
                $(this.$refs.child).width(maxW);
            }
        },
        mounted() {
            this.countChildWidth();
        },
        created() {

        }
    }
</script>

<style scoped lang="less">


    .childPlugIn {
        position: absolute;
        padding: 1px;
        border: 1px solid #ddd;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
        left: 50%;
        top: 100%;
        background: #fff;
        overflow: hidden;
        transform: translate(-50% , 0);
        z-index: 10;
    }

    .plug-name {
        padding: 0 10px;
    }

    .childPlugIn {
        li {
            float: left;
            padding: 10px;
            white-space: nowrap;
            line-height: 24px;
            text-align: center;
            color: @main-font;
            position: relative;
        }

        li:hover {
            background-color: rgba(40, 155, 241, .1);
        }

        li:hover > .childPlugIn {
            display: block;
        }

        .eda_icon {
            vertical-align: middle;
            width: 2.4rem;
            height: 2.4rem;
        }
    }

    .overPos {
        left: auto;
        right: 0;
    }

    .childTop {
        top: -2px;
        left: 100%;
    }

    .childOver {
        left: auto;
        right: 100%;
    }
    .overLeft {
        left: 0;
        right: auto;
        transform: translate(0 ,0);
    }
    .overRight {
        left: auto;
        right: 0;
        transform: translate(0 ,0);
    }
</style>
