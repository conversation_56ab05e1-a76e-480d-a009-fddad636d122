<template>
    <common-dialog custom-class="publishDia" :width="width"
                   :title="title"
                   :visible.sync="visible"
                   @closed="close"
    >
        <div>
            <el-form ref="form" :model="form" :disabled="loading" label-width="120px" :rules="rules" v-if="visible">
                <el-form-item label="评分：" required prop="score">
                    <el-rate v-model="form.score" allow-half>
                    </el-rate>
                </el-form-item>
                <el-form-item label="评论：" required prop="content">
                    <el-input type="textarea" :placeholder="placeholder" v-model="form.content"
                              :autosize="{ minRows: 5, maxRows: 8}" maxlength="500" show-word-limit></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @click="close" size="mini">{{ btnCancelTxt }}</el-button>
            <el-button @click="submit" :disabled="loading" type="primary" size="mini">{{ submitTxt }}</el-button>
        </div>
    </common-dialog>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "../service-mixins/service-mixins";

export default {
    name: "evaluation",
    mixins: [commonMixins, servicesMixins],
    data() {
        let validateScore = (rule, value, callback) => {
            if (value === 0) {
                callback(new Error('请选择评分！'));
            } else {
                callback();
            }
        };
        return {
            btnCancelTxt: '取消',
            submitTxt: '提交',
            width: '800px',
            title: '评价',
            visible: false,
            placeholder: '写点评价吧，您的评价对其他用户很有帮助！',
            form: {
                id: '',
                score: 0,
                content: ''
            },
            rules: {
                score: [{required: true, validator: validateScore, trigger: 'blur'}],
                content: [{required: true, message: '请输入评论', trigger: ["blur", "change"]}],
            },
            loading: false
        }
    },
    methods: {
        show(row) {
            this.form = row;
            this.visible = true;
            this.loading = false;
        },
        validate() {
            let flag = true;
            this.$refs['form'].validate((valid) => {
                if (valid) {
                } else {
                    flag = false;
                    return false;
                }
            })
            return flag;
        },
        submit() {
            const vm = this;
            if (vm.validate()) {
                vm.loading = true;
                vm.$emit('save', vm.form);
            }
        },
        close() {
            this.form = {
                score: 0,
                review: ''
            }
            this.visible = false;
        },
    },
}
</script>

<style scoped>

</style>
