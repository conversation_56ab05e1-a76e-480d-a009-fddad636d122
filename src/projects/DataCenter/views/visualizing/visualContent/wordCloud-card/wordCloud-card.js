import {chartMxins} from "../chart-mixins/chart-mixins";
import {globalBus} from "@/api/globalBus";
import {common} from "@/api/commonMethods/common";
import numerify from "numerify";

export default{
    name: "wordCloud" ,
    mixins : [chartMxins , common],
    data() {
        return {
            chartSettings: {
            },
            chartData: {
                columns: [],
                rows: []
            },
            extend: {
                title: {
                    show: true ,
                },
                legend: {
                    left: 'center'
                },
                tooltip : {},
                series: {
                    drawOutOfBound: true,
                    name : "" ,
                    type : "wordCloud" ,
                    rotationRange: [1, 10],
                    textStyle: {
                        normal: {
                    }
                    },
                    label: {
                        show: true,
                    },
                },
            },
        }
    },
    methods: {
        getNodeData() {
            globalBus.$on('setWordCloudWidgetPanel', this.setHistogramPanel);
        },
        setChartExtend(settings) {
            this.setTitle(settings.title);
            this.setRotationRange(settings.legend);
            this.setColor(settings.legend.color, settings.legend.color_theme, settings.legend.color_type);
            this.setTooltip();
        },
        setTooltip(){
            const vm = this;
            let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
            vm.extend.tooltip.formatter = function (params) {
                let result , meaCont = "" , funcLabel = "" , paramsVal = "" , xString = "";
                let measures = nodeData.xData && nodeData.xData[0];
                if(measures){
                    funcLabel = measures.func.name;
                    if(params.value) paramsVal = measures.data.numberFormat && measures.data.numberFormat !== "none" ? numerify(params.value , vm.format[measures.data.numberFormat]) : params.value;//数据格式
                    xString = measures.data.label + ":" + paramsVal + "(" + funcLabel + ")";
                    meaCont += "<div>" + " <span class='dib vm mr5' style='width:14px;height: 14px;border-radius:4px;background:" + params.color + ";'></span>" + xString + " </div>";
                }
                if(meaCont){
                    result = `<div>
                               <div class="f16 mb5">${params.name}</div>
                               <span>${meaCont}</span>
                          </div>`;
                }else {
                    result = `<div class="f16">${params.name}</div>`;
                }


                return result;
            }
        },
        setRotationRange(legend){
            let rotation = [];
            rotation[0] = legend.startRotate;
            rotation[1] = legend.endRotate;
            this.extend.series.rotationRange = rotation
        },
        setColor(color, theme, type) {
            let txt_colors ;
            if (type === "theme") {
                txt_colors = this.themes[theme];
            } else if (type === "custom") {
                if (color && color.length > 0) {
                    txt_colors = color;
                } else {
                    txt_colors = [...this.colors];
                }
            }
            this.extend.series.textStyle.normal.color = function(params) {
                let inx = params.dataIndex % txt_colors.length;
                return txt_colors[inx];
            }
        },
        preview() {
            const vm = this;
            vm.data_empty = true;
            if (!vm.node.widget) {
                return;
            }
            let {beforeData, widgetDataset} = JSON.parse(vm.node.widget),
                nodeData = beforeData ? JSON.parse(beforeData) : {};
            if (vm.node.code !== "IndicatorCardWidget" && nodeData.yAxis && nodeData.yAxis.length === 0) {
                return;
            }
            if (!widgetDataset) {
                return;
            }
            if (!widgetDataset.tableCode) {
                return;
            }
            if (!widgetDataset.widgetDatasetDims && widgetDataset.widgetDatasetDims.length === 0) {
                return;
            }
            if (!widgetDataset.widgetDatasetMeasures && widgetDataset.widgetDatasetMeasures.length === 0) {
                return;
            }

            vm.setHistogramPanel();
        },
    }
}