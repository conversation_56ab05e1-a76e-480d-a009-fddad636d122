/**
 * 数仓规划 树接口
 * @Author: wangjt
 * @Date: 2020-04-28
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'

const root = "/manage/dataWarehouse/";
/*
*  查询数仓 树
 * @param id , pId , keyword
 * @returns {Promise<*>}
* */
export async function queryDirTree(id = "" , pId = "" , keyword = "" ) {
    return await reqUtil.get({
        url: root + 'queryDirTree',
        errorMsg: false,
        successMsg: false
    }, {id , pId , keyword} );
}

/*
*  移动树节点
 * @param id , ownerId
 * @returns {Promise<*>}
* */

export async function moveDataOrigin(id , ownerId  ) {
    return await reqUtil.get({
        url: root + 'moveDataOrigin',
        errorMsg: false,
        successMsg: false
    }, {id , ownerId} );
}
/*
*  新建子仓库 或 同级仓库
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addTreeNode( {pId  , dirName , dirCode } ) {
    return await reqUtil.post({
        url: root + 'addTreeNode',
        errorMsg: false,
        successMsg: false
    }, {pId , dirName , dirCode } );
}
/*
*  新建子层次 或 同级层次
 * @param pId  , dirName , dirCode
 * @returns {Promise<*>}
* */

export async function saveDWLevel( {pId  , levelName , levelCode } ) {
    return await reqUtil.post({
        url: root + 'saveDWLevel',
        errorMsg: false,
        successMsg: false
    }, {pId , levelName , levelCode } );
}
/*
*  新建仓库
 * @param  dirName , dirCode
 * @returns {Promise<*>}
* */

export async function addBusiDir( { dirName , dirType } ) {
    return await reqUtil.post({
        url: root + 'addBusiDir',
        errorMsg: false,
        successMsg: false
    }, { dirName , dirType } );
}
/*
*  新建业务库库
 * @param  busiId , dbType , catalogId , catalogName , selectId ,selectName , customName
 * @returns {Promise<*>}
* */

export async function saveBusi( busiId , dbType , catalogId , catalogName ,selectId , selectName , levelNum , customName ) {
    return await reqUtil.get({
        url: root + 'saveBusi',
        errorMsg: false,
        successMsg: false
    }, { busiId , dbType , catalogId , catalogName , selectId ,selectName ,levelNum , customName} );
}

/*
*  重命名
 * @param  id , dirId , type , classifyName , classifyId
 * @returns {Promise<*>}
* */

export async function renameDWInstance( { id , dirId , type , classifyName , classifyId } ) {
    return await reqUtil.post({
        url: root + 'renameDWInstance',
        errorMsg: false,
        successMsg: false
    }, { id , dirId , type , classifyName , classifyId } );
}
/*
*  数据集重命名
 * @param  dataId , newName , dirId
 * @returns {Promise<*>}
* */

export async function renameDataSet( { dataId , newName , dirId } ) {
    return await reqUtil.post({
        url: root + 'renameDataSet',
        errorMsg: false,
        successMsg: false
    }, { dataId ,newName , dirId } );
}
/*
*  业务库重命名
 * @param newName , id , catalogType
 * @returns {Promise<*>}
* */

export async function updateCatalogName( { newName , id , catalogType } ) {
    return await reqUtil.post({
        url: root + 'updateCatalogName',
        errorMsg: false,
        successMsg: false
    }, { newName , id , catalogType } );
}
/*
*  删除
 * @param deleteType
 * @returns {Promise<*>}
* */

export async function dataWarehouse( deleteType ) {
    return await reqUtil.get({
        url: root + deleteType,
        errorMsg: false,
        successMsg: false
    } );
}
/*
*  添加业务库
 * @param deleteType
 * @returns {Promise<*>}
* */

export async function getResTree( softwares ) {
    return await reqUtil.get({
        url: root + 'getResTree',
        errorMsg: false,
        successMsg: false
    } , {softwares});
}
