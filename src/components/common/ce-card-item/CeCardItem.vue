<!--
    分享 、场景案例 、 这类的卡片。 通用 不加其他逻辑，显示型插件
-->

<template>
    <div class="CeCardItem">
        <div class="CeCardItem_icon">
            <slot name="icon">
                <svg :title="iconTitle" class="CeCardItem_ic" aria-hidden="true">
                    <use class="CeCardItem_color" :xlink:href="icon"></use>
                </svg>
            </slot>
        </div>
        <ul class="CeCardItem_list">
            <li class="CeCardItem_item CeCardItem_name" >
                <slot name="name">
                    <div :title="name">{{name}}</div>
                </slot>
            </li>
            <li class="CeCardItem_item">
                <slot name="target">
                    <i class="dg-iconp">&#xe726;</i>
                    <span>{{target}}</span>
                </slot>
            </li>
            <li class="CeCardItem_item" >
                <slot name="time">
                    <div :title="time">
                        <i class="dg-iconp">&#xea9e;</i>
                        <span :title="time">{{time}}</span>
                    </div>
                </slot>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    name: "CeCardItem" ,
    props : {
        data : {
            type : Object ,
            default : ()=>{
                return {
                    icon : "" ,
                    iconTitle : "" ,
                    name : "" ,
                    target : "" ,
                    time : ""
                }
            }
        },
        cardProp : {
            type : Object ,
            default : ()=> {
                return {
                    "icon" : "icon" ,
                    "iconTitle" : "iconTitle" ,
                    "name" : "name" ,
                    "target" : "target" ,
                    "time" : "time" ,
                }
            }
        }
    },
    data(){
        return {
            icon : "" ,
            iconTitle : "" ,
            name : "" ,
            target : "" ,
            time : ""
        }
    },
    methods : {
        getData(){
            const vm = this, {cardProp , data} = vm;
            for(let k in cardProp){
                vm[k] = data[cardProp[k]];
            }
        }
    },
    created() {
        this.getData();
    }
}
</script>

<style scoped lang="less">
.CeCardItem {
    border: 1px solid rgba(0,0,0,0.09);
    height: 100px;
    border-radius: 2px;
    cursor: pointer;
    &:hover, &.active{
        border: 1px solid #0088FF;
        box-shadow: 0 5px 10px 0 rgba(24,144,255,0.30);
    }
    &_icon {
        width: 90px;
        height: 100%;
        float: left;
        background: url("../../../projects/DataCenter/assets/images/home/<USER>") no-repeat center center;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    &_ic {
        width: 30px;
        height: 30px;
    }
    &_color {
        fill: #0088FF;
    }
    &_list {
        margin-left: 90px;
        padding: 8px 10px;
    }
    &_item {
        line-height: 28px;
        color: rgba(0,0,0,0.65);
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        > i {
            padding-right: 4px;
        }
    }
    &_caseDescripeItem {
        line-height: 28px;
        color: rgba(0,0,0,0.65);
        font-size: 14px;
        > i {
            padding-right: 4px;
        }
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    &_name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #000;
    }
}
</style>
