<!--
    @Describe: 人员评价
    @Author: wangjt
    @Date: 2024/6/20
-->
<template>
    <div class="evaluation-outer" v-loading="settings.loading">
        <dg-scrollbar>
            <div class="evaluation-box">
                <div class="evaluation-title b">{{ text.base }}</div>
                <el-divider/>
                <el-form label-suffix=":">
                    <el-row>
                        <el-col :span="8" class="pl30" v-for="(item , k) in baseInfo" :key="k">
                            <el-form-item :label="item.label">
                                <span class="g3">{{ item.value }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <div class="evaluation-title b">{{ text.dataAnalysis }}</div>
                <el-divider/>
                <div class="evaluation-title pl20 mb15">{{ text.modelAnalysis }}</div>
                <div class="evaluation-list_box">
                    <div class="evaluation-list_item" v-for="(li ,i , j) in modelList" :key="i">
                        <analysis-card :type="li.type" :title="li.title" :value="li.value"
                                       :icon-color="computeColor(j , 5)"
                                       :icon="li.icon"/>
                    </div>
                </div>
                <div class="evaluation-title pl20 mb15">{{ text.useAnalysis }}</div>
                <div class="evaluation-list_box">
                    <div class="evaluation-list_item" v-for="(li ,i , j) in useList" :key="i">
                        <analysis-card :type="li.type" :title="li.title" :value="li.value"
                                       :icon-color="computeColor(j , 5)"
                                       :icon="li.icon"/>
                    </div>
                </div>
                <div class="evaluation-title b">{{ text.modelEvaluation }}</div>
                <el-divider/>
                <el-tabs class="model-publish_tab" v-model="activeName" @tab-click="tabClick" v-if="publishUser.userId">
                    <el-tab-pane v-for="(tab, inx) in tabPanes" :key="inx"
                                 :label="setLabel(tab.label, tab.total)" :name="tab.value">
                        <evaluation-page :total.sync="tab.total"
                                         :loadEvaluation="() => loadEvaluation(tab.operateType)"
                                         :submitEvaluation="(val) => submitEvaluation(val , tab.operateType)"
                                         :loadPageEvaluation="(inx , size) => loadPageEvaluation(inx , size , tab.operateType)"
                                         :canEvaluate="()=> userInfo.id !== publishUser.userId"
                        />
                    </el-tab-pane>
                </el-tabs>
            </div>
        </dg-scrollbar>
    </div>

</template>

<script>
import AnalysisCard from "@views/model-market/modelMarket/components/analysisCard.vue";
import EvaluationPage from "@views/model-market/modelMarket/components/evaluationPage.vue";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {mapGetters} from "vuex";

export default {
    name: "personalEvaluation",
    mixins: [commonMixins],
    components: {
        AnalysisCard,
        EvaluationPage,
    },
    computed: {
        computeColor() {
            return (i) => this.itemColors[i % (this.itemColors.length || 1)]
        },
        ...mapGetters(["userInfo"]),
        modelId() {
            return this.$route.query.id;
        }
    },
    data() {
        return {
            text: {
                base: '基础信息',
                dataAnalysis: '数据分析',
                modelAnalysis: '建模分析',
                useAnalysis: '使用分析',
                modelEvaluation: "模型评价"

            },
            itemColors: [
                '#00c488', '#FA8E5A', '#FAAD14', '#7386CB', "#07a2a4",
                '#42acff', '#3666ee', '#00dbe3', '#f97da1', '#ff9e85',
            ],
            baseInfo: {
                appraisedUserName: {
                    label: '姓名',
                    value: "",
                },
                appraisedUserTelphone: {
                    label: "联系电话",
                    value: "",
                },
                appraisedUserEmail: {
                    label: "电子邮箱",
                    value: "",
                }
            },
            // 模型分析
            modelList: {
                modelCount: {
                    title: '建模数量（个）',
                    value: "",
                    icon: "iconfont iconshuju_huaban",
                },
                modelDowloadCount: {
                    title: '所建模型下载次数（次）',
                    value: "",
                    icon: "dg-iconp icon-download",
                },
                modelUseCount: {
                    title: "所建模型使用次数（次）",
                    value: "",
                    icon: "iconfont iconcishutongji_huaban",
                },
                modelUpvoteCount: {
                    title: "所建模型点赞量（个）",
                    value: "",
                    icon: "icon icon-good",
                },
                modelEvaluationScore: {
                    title: "所建模型评论好坏",
                    value: 0,
                    icon: "el-icon-chat-line-square",
                    type: "rate",
                },
                modelCreateSpeed: {
                    title: "建模速度（近7天建模数量（个））",
                    value: "",
                    icon: "dg-iconp icon-time-b",
                },
            },
            // 使用分析
            useList: {
                loginCount: {
                    title: "登录次数（次）",
                    value: "",
                    icon: "icon icon-log",
                },
                userModelSpeed: {
                    title: "使用模型数量（个）",
                    value: "",
                    icon: "dg-iconp icon-model-a",
                },
                modelCreateFrequency: {
                    title: "使用模型频次(近7天使用次数（次）)",
                    value: "",
                    icon: "iconfont iconpinshuaishaixuan_huaban",
                },
                needSubmitCount: {
                    title: "需求提交量（次）",
                    value: "",
                    icon: "icon icon-ok",
                },
                needRejectCount: {
                    title: "需求拒绝量（次）",
                    value: "",
                    icon: "icon icon-forbid",
                },
                needPassCount: {
                    title: "需求通过量（次）",
                    value: "",
                    icon: "icon icon-ok2",
                },
                commentCount: {
                    title: "评论量",
                    value: "",
                    icon: "el-icon-chat-line-square",
                },
                declaredBattleCount: {
                    title: "申报战果量",
                    value: "",
                    icon: "iconfont iconshujutongji_huaban",
                },
            },
            activeName: "modelEvaluation",
            tabPanes: [
                {label: "建模评价", value: "modelEvaluation", total: 0, operateType: "CreateModelEvaluation"},
                {label: "使用评价", value: "useEvaluation", total: 0, operateType: "UseModelEvaluation"},
            ],
            publishUser: {}, // 发布用户

        }
    },
    methods: {
        tabClick() {
        },
        setLabel(label, total) {
            return label + '(' + total + ')'
        },
        /**
         * 回显 评价
         * @param operateType
         * @returns {*}
         */
        loadEvaluation(operateType) {
            const vm = this, {$services, userInfo, publishUser} = vm;
            const params = {
                userId: userInfo.id,
                appraisedUserId: publishUser.userId,
                operateType,
            };
            return $services('market').userEvaluationDetail(params).then(res => {
                if (res.data.status === 0) {
                    return res.data.data.operateContent || "";
                }
            })
        },
        /**
         * 加载评价分页
         * @param inx
         * @param size
         * @param operateType
         * @returns {*}
         */
        loadPageEvaluation(inx, size, operateType) {
            const vm = this, {$services, modelId, publishUser} = vm;
            const params = {
                operateType,
                pageNum: inx,
                pageSize: size,
                id: modelId,
                appraisedUserId: publishUser.userId,
            };
            return $services("market").queryModelEvaluationList(params).then(res => {
                if (res.data.status === 0) {
                    return {
                        list: res.data.data.dataList,
                        total: res.data.data.totalCount
                    }
                }
            })
        },
        /**
         * 提交评价
         * @param val
         * @param evaluationType
         * @returns {*}
         */
        submitEvaluation(val, evaluationType) {
            const vm = this, {$services, userInfo, publishUser, settings} = vm;
            const params = {
                userId: userInfo.id,
                appraisedUserId: publishUser.userId,
                evaluationType,
                operateContent: val,
            };
            settings.loading = true;
            return $services("market").evaluationUser(params).then(async (res) => {
                if (res.data.status === 0) {
                    vm.$message.success("评价成功");
                    await vm.userEvaluationBasicDetail();
                }
                settings.loading = false
            })
        },
        /**
         * 根据模型 id 获取模型详情
         * @param id
         */
        getModelInfo(id) {
            const vm = this, {$services, settings} = this;
            settings.loading = true;
            let data = {
                id: id,
                addView: false, // 不增加浏览次数
            }
            $services("market").viewMarkModelDetail(data).then(async (res) => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.publishUser = result.publishUser;
                    await vm.userEvaluationBasicDetail();
                }
                settings.loading = false;
            })
        },
        init() {
            if (this.$route.query.id) {
                this.getModelInfo(this.$route.query.id, true)
            }
        },
        /**
         * 回显人员评价页面
         */
        userEvaluationBasicDetail() {
            const vm = this,
                {userInfo, publishUser, $services, settings} = vm,
                params = {
                    userId: userInfo.id,
                    appraisedUserId: publishUser.userId
                }
            return $services("market").userEvaluationBasicDetail(params).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.setListValue(vm.baseInfo, result, "-");
                    vm.setListValue(vm.modelList, result);
                    vm.setListValue(vm.useList, result);
                }else {
                    settings.loading = false;
                }
            })
        },
        // 设置 值
        setListValue(list, data, defaultValue) {
            for (let k in list) {
                list[k].value = data[k] || defaultValue;
            }
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.evaluation {
    &-outer {
        background: #fff;
        height: calc(100% - 12px);
        overflow: hidden;
    }

    &-box {
        padding: 20px;
    }

    &-title {
        font-family: MicrosoftYaHei;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1;
        margin: 12px 0;
    }

    &-list {
        @listGap: 14px;

        &_box {
            display: flex;
            gap: @listGap;
            flex-wrap: wrap;
            padding: 0 @listGap 20px;
        }
    }
}

.itemRowLast(@index , @gap , @cols) {
    .evaluation-list_item {
        width: calc(100% / @cols - @gap);
    }
    .evaluation-list_item:nth-child(@{index}) {
        width: 100% / @cols;
    }
}

.itemRowLast(4n, 14px, 4);
</style>
