<!--
    通用新建方案tab
    使用 ：
        配置的属性说明 props 注释
        hasSaveOther 显示另存为  默认false 也可以传function 自定义显示的逻辑
        hasOpenCase 显示打开文件 默认false
        modelTxt 显示新建名称 ，如 设置为 "模型"  新建的提示和内容 => 新建模型 打开 => 打开模型
        loadOnlyFocus 仅当前tab内容渲染 默认 false ,设为true 即当前tab下的内容页渲染，切换则会重新渲染
        setNewRowProps 保存新建方案需要修改rowData的所有属性 tab 数据都会有 rowData 对象，存方案的数据 ，新建保存方案则需要添加上
                        如 设置为 ["name" , "id" , "code" , "parentid"] 在保存接口执行完后， 需要发送事件
                        globalBus.$emit("caseSaveSuccess", row.tabId ,  chName , {name : chName ,code : engName, id : res.data.data.dataObjId , parentid:savePath });
                        即会修改新方案里的rowData对应属性数据
    事件 ：tabChange tab切换改变时触发，参数： rowData=> 当前tab的数据的rowData
          save 保存事件
          saveOther 另存为事件

     slot :插槽
           tabCont 设置tab下的内容页 ， 如果需要获取当前tab下内容页的参数  可从ref 获取
           <sql-panel slot-scope="{item}" :data="item" slot="tabCont" :ref="'sql_'+item.id" ></sql-panel>
-->

<template>
    <div class="rapid-analysis CaseTabPanel">
        <header class="rapid-analysis__header">
            <div class="rapid-analysis__tabs analysis-tab">
                <div class="rapid-analysis__add" :style="{'left' : addLeft+'px'}">
                    <el-popover v-if="hasOpenCase" popper-class="ce-popover" placement="top-start" width="106"
                                trigger="click" :visible-arrow="false" :offset="-50">
                        <div class="rapid-analysis__add-box">
                            <div @click="addTab">{{ addModelTxt + modelTxt }}</div>
                            <div @click="openFile">{{ openModelTxt + modelTxt }}</div>
                        </div>
                        <i slot="reference" class="dg-iconp icon-l-plus"></i>
                    </el-popover>
                    <i v-else :title="addModelTxt+modelTxt" class="dg-iconp icon-l-plus" @click="addTab"></i>
                </div>
                <model-tab
                        class="ce-common_tab"
                        v-if="editableTabs.length"
                        v-model="editableTabsValue"
                        :editable-tabs="editableTabs"
                        @tab-click="tabClick"
                        @tab-remove="checkRemove"
                >
                    <slot name="tabCont" slot="tabCont" slot-scope="{item}" :item="item"
                          v-if="loadOnlyFocus ? item.id === editableTabsValue : true"></slot>
                </model-tab>
            </div>
            <span class="rapid-analysis__menus" ref="icon" @click="showMore = !showMore">
                <i class="dg-iconp icon-menu"></i>
            </span>
            <div class="rapid-analysis__more" v-show="showMore">
                <div class="rapid-analysis__item" v-for="(menu, idx) in editableTabs" :title="menu.label" :key="idx"
                     :class="{'active' : menu.id === editableTabsValue }" @click="checkTab(menu)">{{ menu.label }}
                </div>
            </div>
            <span class="rapid-analysis__actions">
                <el-button type="primary" size="mini" class="rapid-analysis__actions-item" @click="handleSave()">
                    <i class="dg-iconp icon-save"></i>{{ saveTip }}
                </el-button>
                <el-divider direction="vertical" v-if="setSaveOther()"></el-divider>
                <el-button size="mini" class="rapid-analysis__actions-item" @click="handleSave('saveOther')" v-if="setSaveOther()">
                    <i class="dg-iconp icon-save"></i>{{ saveOTip }}
                </el-button>
                <el-divider direction="vertical"></el-divider>
                <span class="rapid-analysis__actions-item"></span>
<!--                <i :class="['dg-iconp', !isFull ? 'icon-maximize' : 'icon-minimum']" @click="handleFull"
                   :title="isFull?'退出全屏':'全屏'"></i>-->
                <i class="dg-iconp icon-l-close" @click="handleClose" title="关闭"></i>
            </span>
        </header>
        <!--放弹窗 -->
        <slot></slot>
    </div>
</template>

<script>
import {mapActions, mapState} from "vuex";
import {common} from "@/api/commonMethods/common";
import ModelTab from "@/components/common/case-tab-panel/ModelTab";
import * as $ from "jquery";
import {globalBus} from "@/api/globalBus";

export default {
    name: "CaseTabPanel",
    mixins: [common],
    components: {ModelTab},
    data() {
        return {
            addLeft: 0,
            showMore: false,
            editableTabsValue: '',
            editableTabs: [],
            saveTip: "保存",
            saveOTip: "另存为",
            isFull: false,
            addModelTxt : '新建' ,
            openModelTxt : "打开",
            tabIndex: 1,
        }
    },
    computed: {
        ...mapState({
            planId: (state) => state.plans.planId,
            planRowData : (state) => state.plans.plans.find( pl => pl.id === state.plans.planId).rowData
        }),
    },
    props: {
        hasSaveOther: { //显示另存为
            type: [Boolean , Function],
            default() {
                return false;
            }
        },
        hasOpenCase: { //显示打开文件夹
            type: Boolean,
            default: false
        },
        modelTxt: { //新建tab名称
            type: String,
            default: "模型"
        },
        loadOnlyFocus: { //仅当前tab内容渲染
            type: Boolean,
            default: false
        },
        setNewRowProps : { //保存新建方案需要修改rowData的所有属性
            type : Array ,
            default : ()=>[]
        }
    },
    watch: {
        "editableTabs.length": {
            handler(len) {
                this.addPlan(this.editableTabs);
                this.setPlanId(this.editableTabsValue);
                this.triggerEvent('resize', 300);
            },
            deep: true,
        },
        editableTabsValue(val) {
            this.setPlanId(val);
            this.$emit("tabChange" , this.planRowData )
        },
    },
    methods: {
        ...mapActions(["addPlan", "setPlanId"]),
        setSaveOther(){
            const vm = this , {hasSaveOther} = vm;
            let otherType = Object.prototype.toString.call(hasSaveOther);
            if(otherType === "[object Function]"){
                return hasSaveOther();
            }else if(otherType === "[object Boolean]") {
                return hasSaveOther;
            }else {
                return Boolean(hasSaveOther);
            }
        },
        /**
         * 打开文件
         */
        openFile() {
            this.$emit("openFile");
        },
        /**
         * 设置添加按钮位置
         */
        setAddBtnPos() {
            const vm = this, width1 = 50, width2 = 90;
            vm.$nextTick(() => {
                let tabScrollW = $(".analysis-tab").find(".el-tabs__nav-scroll").width(),
                        tabW = $(".analysis-tab").find(".el-tabs__nav").width();
                vm.addLeft = tabW <= tabScrollW ? tabW + width1 : tabScrollW + width2;
            })
        },
        /**
         * 监听tab变化改变+位置
         */
        setTabWatch() {
            window.addEventListener(
                    "resize", this.setAddBtnPos, false
            );
        },
        /**
         * 移除监听
         */
        removeTabHWatch() {
            window.removeEventListener(
                    "resize", this.setAddBtnPos, false
            );
        },
        /**
         * 显示tab
         * @param rowData
         * @param new_trans_id
         * @param newTransName
         */
        showFlow(rowData, new_trans_id, newTransName) {
            const vm = this;
            let tabLabel;
            tabLabel = rowData.name ? rowData.name : newTransName;
            let rowD = rowData.name ? rowData : {
                name: newTransName,
                isNew: true,
                tabId : new_trans_id ,
                ...rowData
            };
            let newTab = {
                id: new_trans_id,
                label: tabLabel,
                rowData: rowD,
            };
            let hasTab = false;
            hasTab = vm.hasTabs(newTab, hasTab);//
            if (hasTab || vm.editableTabs.length === 0) {
                vm.editableTabs.push(newTab);
                vm.editableTabsValue = new_trans_id;
            }
        },
        /**
         * 显示tab是否已打开
         * @param newTab
         * @param hasTab
         * @return {*}
         */
        hasTabs(newTab, hasTab) {
            let tabs = this.editableTabs;
            for (let i = 0; i < tabs.length; i++) {
                if (tabs[i].id === newTab.id) {
                    this.editableTabsValue = newTab.id;
                    hasTab = false;
                    return;
                } else {
                    hasTab = true;
                }
            }
            return hasTab;
        },
        /**
         * 点击tab
         */
        tabClick(tab) {

        },
        checkRemove(targetName){
            const vm = this;
            let {editableTabs , modelTxt} = vm;
            let row = editableTabs.find(ta => ta.id === targetName).rowData;
            if(row.isNew){
                vm.confirm('提示' ,`该${modelTxt}未保存，是否放弃修改?` , ()=>{
                    vm.removeTab(targetName);
                })
            }else {
                vm.removeTab(targetName);
            }
        },
        /**
         * 删除tab
         */
        removeTab(targetName) {
            const vm = this;
            let {editableTabs: tabs, editableTabsValue: activeName} = vm;
            if (activeName === targetName) {
                tabs.forEach((tab, index) => {
                    if (tab.id === targetName) {
                        let nextTab = tabs[index + 1] || tabs[index - 1];
                        if (nextTab) activeName = nextTab.id;
                    }
                });
            }
            vm.editableTabsValue = activeName;
            vm.editableTabs = tabs.filter(tab => tab.id !== targetName);
            vm.currentRowData = tabs.find(item => item.id === targetName);
            if (vm.editableTabs.length === 0) {
                vm.$emit('closeTab');
                vm.$emit("close");
            }
        },
        /**
         * 保存
         */
        handleSave(name = "save") {
            this.$emit(name , this.planRowData);
        },
        /**
         * 选择切换tab
         * @param menu
         */
        checkTab(menu) {
            this.editableTabsValue = menu.id;
            this.showMore = false;
            this.$emit("checkTab", menu);
        },
        /**
         * 添加
         * @param row
         */
        addTab(row={}){
            let newTransName = `新建${this.modelTxt}_${this.tabIndex}`;
            this.tabIndex ++;
            if (row.id === undefined) {
                this.addNewTrans(row, newTransName);
            } else {
                this.showFlow(row, row.id, newTransName);
            }
        },
        /**
         * 获取新建id
         * @param row
         * @param name
         */
        addNewTrans(row , name){
            let newId = new Date().getTime().toString();
            this.showFlow(row , newId , name);
        },
        /**
         * 点击菜单
         */
        handleTab(item) {
            this.activeMenu = item.value;
        },
        /**
         * 关闭弹窗
         */
        handleClose() {
            const vm = this;
            let tabs = this.editableTabs.filter(n=>n.rowData.id).map(p=>p.rowData.id);
            vm.confirm("关闭", "确认关闭该页面", () => {
                vm.$emit("close", tabs);
            })
        },
        /**
         * 全屏
         */
        handleFull() {
            this.isFull = !this.isFull;
            if (
                    (document.fullScreenElement !== undefined && document.fullScreenElement === null) ||
                    (document.msFullscreenElement !== undefined && document.msFullscreenElement === null) ||
                    (document.mozFullScreen !== undefined && !document.mozFullScreen) ||
                    (document.webkitIsFullScreen !== undefined && !document.webkitIsFullScreen)
            ) {
                if (document.documentElement.requestFullScreen) {
                    document.documentElement.requestFullScreen();
                } else if (document.documentElement.mozRequestFullScreen) {
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.webkitRequestFullScreen) {
                    document.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            } else {
                if (document.cancelFullScreen) {
                    document.cancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        },
        setTabChange(id , name , row ){
            const {setNewRowProps} = this;
            this.editableTabs.forEach(tab =>{
                if(id === tab.id){
                    if(tab.rowData.isNew){
                        tab.rowData.isNew = false;
                        for(let pro of setNewRowProps){
                            if(row[pro])tab.rowData[pro] = row[pro];
                        }
                    }else {
                        for(let pro of setNewRowProps){
                            if(row[pro])tab.rowData[pro] = row[pro];
                        }
                    }
                    tab.label = name;
                }
            });
            this.triggerEvent("resize" , 300);
            this.addPlan(this.editableTabs);
            this.$emit("tabChange" , this.planRowData )
        },
        onListenFun(){
            globalBus.$on("caseSaveSuccess", this.setTabChange);
        },
        offListenFun(){
            globalBus.$off("caseSaveSuccess", this.setTabChange);
        },
    },
    created() {
        this.onListenFun();
        this.setTabWatch();
    },
    destroyed() {
        this.offListenFun();
        this.removeTabHWatch();
    }
}
</script>

<style scoped lang="less">
.menu-active {
    background: rgba(0, 136, 255, 0.08);
    border-radius: 2px;
    font-size: 14px;
    color: #0088ff;
}

.rapid-analysis {
    width: 100%;
    height: 100%;
    overflow: hidden;

    &__header {
        width: 100%;
        display: block;
        align-items: center;
        background-color: #ffffff;
        //padding: 0 16px;
        height: 40px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        padding-top: 5px;
        position: relative;
    }

    &__actions {
        position: absolute;
        right: 20px;
        top: 14px;

        &-item {
            cursor: pointer;
            font-size: 14px;
            //color: rgba(0, 0, 0, 0.85);
        }

        i {
            //font-size: 16px;
            //color: #000000;
            margin: 0 4px;
            cursor: pointer;
        }
    }

    &__menus {
        position: absolute;
        padding-right: 20px;
        cursor: pointer;
        user-select: none;
        left: 10px;
        top: 14px;
        z-index: 10;
        background: #fff;

        &::before {
            content: "";
            position: absolute;
            top: 50%;
            margin-top: -9px;
            right: -1px;
            height: 18px;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.09);
        }
    }

    &__more {
        position: fixed;
        top: 46px;
        left: 0;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
        border-radius: 2px;
        max-width: 260px;
        max-height: 300px;
        z-index: 8;
        overflow: auto;
        overflow-x: hidden;

        div.rapid-analysis__item {
            line-height: 36px;
            cursor: pointer;
            padding: 0 20px;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &:hover, &.active {
                color: #0088ff;
            }
        }
    }

    &__tabs {
        align-items: center;
        height: 100%;
        display: block;
    }

    &__add {
        padding: 0 14px;
        cursor: pointer;
        line-height: 40px;
        z-index: 1;
        position: absolute;
        transition: 200ms;
        top: 4px;
        /*.ce-rapid_tab{*/
        /*/deep/.el-tabs__content{*/
        /*height: calc(100vh - 46px);*/
        /*}*/
        /*}*/

        &-box {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);

            div {
                line-height: 36px;
                cursor: pointer;

                &:hover {
                    color: #0088ff;
                }
            }
        }

        i {
            font-size: 16px;
        }
    }

    &__menu {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 20px;
        cursor: pointer;
        height: 100%;
        box-sizing: border-box;
        margin-top: 1px;

        &::before {
            content: "";
            position: absolute;
            top: 50%;
            margin-top: -9px;
            right: -1px;
            height: 18px;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.09);
        }

        i {
            margin-left: 4px;
        }

        &:hover {
            color: #0088ff;
        }

        &--active {
            color: #0088ff;
            background: #f5f5f5;
            border: 1px solid rgb(224, 224, 224);
            border-bottom: none;
            border-radius: 4px 4px 0 0;
        }
    }

    &__main {
        display: flex;
        margin-top: 8px;
        flex: 1;
        overflow: hidden;
    }

    &__aside {
        width: 208px;
        margin-right: 11px;
        overflow: hidden;

        /deep/ .el-card__body {
            height: 100%;
            overflow: auto;
        }

        &-content {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: 7px 14px;
            box-sizing: border-box;
        }
    }

    &__steps {
        overflow: auto;
    }

    &__wrap {
        overflow: hidden;
        flex: 1;
        margin-right: 14px;
    }

    &__open {
        position: fixed;
        top: 54px;
        right: 0;
        width: 45px;
        height: 120px;
        font-weight: bold;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        background: #ffffff;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.09);
        border-radius: 2px 0 0 2px;
        padding: 13px;
        box-sizing: border-box;
        user-select: none;
        cursor: pointer;

        i {
            color: rgba(0, 0, 0, 0.45);
            font-weight: 400;
        }
    }
}

.sub-title {
    padding: 12px 14px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
}

</style>
