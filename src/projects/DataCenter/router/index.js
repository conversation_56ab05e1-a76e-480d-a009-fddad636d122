import Vue from 'vue'
import Router from 'vue-router'
import {basicRouter} from "./basic";
import {standardRouter} from "./standard_3";
import {marketRouter} from "@/projects/DataCenter/router/model-market";
import {default as $right} from "@/assets/data/right-data/right-data";

const routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
    return routerPush.call(this, location).catch(error => error)
};
Vue.use(Router);
let routes;

if (process.env.VUE_APP_SECRET === 'basic') {
    routes = basicRouter;
} else if (process.env.VUE_APP_SECRET === 'standard') {
    routes = standardRouter;
}

const index = new Router({
    mode: 'history',
    base: process.env.BASE_URL,
    linkActiveClass: 'active',
    routes: [...routes, ...marketRouter]
});

index.beforeEach((to, from, next) => {
    if (to.fullPath.indexOf('/datacenter/modelView') > -1 || to.fullPath === '/login'
        || to.fullPath.startsWith('/redirectBack')
        || (to.fullPath.indexOf('/datacenter/visualview') > -1 && to.fullPath.indexOf('userCode') > -1 && to.fullPath.indexOf('passWord') > -1)
        || (to.fullPath.indexOf('/datacenter/jumpView') > -1 && to.fullPath.indexOf('userName') > -1 && to.fullPath.indexOf('password') > -1)
        || (to.fullPath.indexOf('/datacenter/visualview') > -1 && to.fullPath.indexOf('userId') > -1)) {//|| to.fullPath.startsWith('/redirectBack')
        next();
    } else {
        let userInfo = localStorage.getItem("userInfo"), rights = JSON.parse(localStorage.getItem("userRight"));
        if (!userInfo) {
            if (to.fullPath.indexOf('/datacenter/visualview') > -1 || to.fullPath.indexOf('/datacenter/homeView') > -1) {
                next('/error');
            } else if (to.fullPath === "/error") {
                next();
            } else {
                // sessionStorage.setItem('next', to.fullPath);
                next('/login');
            }
        } else {
            if (to.matched.length === 0) {  //如果未匹配到路由
                from.path ? next({path: from.path}) : next('/');   //如果上级也未匹配到路由则跳转主页面，如果上级能匹配到则转上级路由
            } else if (to.matched[0].path === '*') {
                next();
            } else {
                routerRight(rights, to, next);
                next();
            }
        }
    }
});


export default index;

export function getMenu() {
    const rights = JSON.parse(localStorage.getItem("userRight")), {menus} = getParentMenu(rights);
    return getRouterByRight(menus, routes);
}

export function getMarketMenus() {
    return marketRouter;
}

async function routerRight(rights, to, next) {
    if (rights) {
        let {allRight} = getParentMenu(rights), path = "/";
        if (to.matched[1] && allRight.indexOf(to.matched[1].path) > -1) {
            next();
        } else if (to.matched[0] && allRight.indexOf(to.matched[0].path) > -1) {
            next();
        } else {
            path = await gotoPath(allRight);
            next({path: path});
        }
    } else {
        localStorage.removeItem("userRole");
        next("/login")
    }
}

/**
 * 获取
 * @param menus
 * @param router
 */
function getRouterByRight(menus, router) {
    return router.filter(rou => {
        if (rou.children && rou.children.length) rou.children = getRouterByRight(menus, rou.children);
        return menus.includes(rou.name);
    })
}

/**
 * 获取默认 菜单
 * @param params
 * @return {[]}
 */
function getPathMenu(params) {
    return [
        "/home",
        "/modelSpace/modeling",
        "/modelSpace/rapidAnalysis",
        "/visualSpace/visualizing",
        "/visualSpace/themePortal",
        "/dataSpace/dataContact",
        "/dataSpace/dataReady",
        "/management/rolesmanage",
        "/management/usergroupmanage",
        "/management/usermanage",
        "/management/operator",
        "/serviceSpace/serviceManage",
        "/caseScenario",
        "/modelSpace/AIModeling",
        "/datacenter/service",
        // "/businessSpace/overview",
        // "/businessSpace/definition",
        "/management/serviceMediation",
        "/management/busService",
        "/modelSpace/ImportAndExport",
        "/serviceSpace/testOnline",
        "/serviceSpace/useCaseManage",
        "/serviceSpace/serviceImport",
    ];
}

function gotoPath(allR, params) {
    let path = "";
    let menus = getPathMenu(params);
    for (let i = 0; i < menus.length; i++) {
        if (allR.indexOf(menus[i]) > -1) {
            path = menus[i];
            break;
        }
    }
    return path;
}

/**
 *  权限
 * @param allRight
 * @param path
 */
function pushRights(allRight, path) {
    allRight = allRight.filter(all => {
        return all !== path;
    });
    allRight.push(path);
    return allRight
}

/**
 * 路由菜单
 * @param routes
 * @param route
 */
function pushRoutes(routes, route) {
    routes = routes.filter(all => {
        return all !== route;
    });
    routes.push(route);
    return routes
}

/**
 * @description 根据用户的权限code组， 遍历 ，
 * allRight 放权限对应的路由 ，menu 放父级路由name 、子级路由name 、接口 code
 * @param userRight
 * @return {{allRight: [], menus: []}}
 */
export function getParentMenu(userRight) {
    let allRight = [], menus = [];
    const routerMenu = Vue.prototype.$routerMenu;
    let $right = require("@/assets/data/right-data/right-data").default;
    userRight.forEach(r => {
        // menus.push(r.funcCode);
        //数据空间
        if(routerMenu.includes("dataSpace")){
            if (r.funcCode.indexOf($right["dataSetOperation"]) > -1 ) {
                allRight = pushRights(allRight, "/dataSpace/dataReady");
                menus = pushRoutes(menus, "dataSetOperation");
                menus = pushRoutes(menus, "dataSpace");
                menus = pushRoutes(menus, "dataReady");
            }

            if (r.funcCode.indexOf($right["dataConnection"]) > -1) {
                allRight = pushRights(allRight, "/dataSpace/dataContact");
                menus = pushRoutes(menus, "dataConnection");
                menus = pushRoutes(menus, "dataSpace");
                menus = pushRoutes(menus, "dataContact");
            }
        }
        //建模空间
        if(routerMenu.includes("modeling")){
            //数据建模
            if (r.funcCode.indexOf($right["processModeling"]) > -1 && r.funcCode.indexOf($right["fastAnalysis"]) === -1) {
                allRight = pushRights(allRight, "/modelSpace/modeling");
                menus = pushRoutes(menus, "processModeling");
                menus = pushRoutes(menus, "Modeling");
            }
            //快速分析
            if (r.funcCode.indexOf($right["fastAnalysis"]) > -1) {
                allRight = pushRights(allRight, "/modelSpace/rapidAnalysis");
                menus = pushRoutes(menus, "rapidAnalysis");
                menus = pushRoutes(menus, "Modeling");
                menus = pushRoutes(menus, "fastAnalysis");
            }
            //AI建模
            if (r.funcCode.indexOf($right["AIModeling"]) > -1) {
                if (!Vue.prototype.$hideAI) {
                    allRight.push("/modelSpace/AIModeling");
                    menus = pushRoutes(menus, "Modeling");
                    menus = pushRoutes(menus, "processAIModeling");
                }
            }
            //导入导出
            if (r.funcCode.indexOf($right["modelSpace"]) > -1) {
                menus = pushRoutes(menus, "ImportAndExport");
                menus = pushRoutes(menus, "Modeling");
                allRight.push("/modelSpace/ImportAndExport");
            }
        }
        //可视空间
        if(routerMenu.includes("visualSpace")){
            if (r.funcCode.indexOf($right["dashboard"]) > -1 || r.funcCode.indexOf("visual") > -1) {
                allRight = pushRights(allRight, "/visualSpace/visualizing");
                menus = pushRoutes(menus, "visual");
                menus = pushRoutes(menus, "dashboard");
                menus = pushRoutes(menus, "visualSpace");
            }
            if (r.funcCode.indexOf($right["themePortal"]) > -1) {
                allRight = pushRights(allRight, "/visualSpace/themePortal");
                menus = pushRoutes(menus, "themePortal");
                menus = pushRoutes(menus, "visualSpace");
            }

        }
        //服务空间
        if(routerMenu.includes("serviceSpace")){
            //导入导出
            if (r.funcCode.indexOf($right["serviceSpace"]) > -1) {
                menus = pushRoutes(menus, "serviceImport");
                menus = pushRoutes(menus, "serviceSpace");
                allRight.push("/serviceSpace/serviceImport");
            }
            //API管理
            if (r.funcCode.indexOf($right["serviceManagement"]) > -1) {
                menus = pushRoutes(menus, "processServiceManage");
                menus = pushRoutes(menus, "serviceSpace");
                allRight.push("/serviceSpace/serviceManage");
            }
            /**
             * 在线测试
             */
            if (r.funcCode.indexOf($right["testOnline"]) > -1) {
                menus = pushRoutes(menus, "serviceSpace");
                allRight.push("/serviceSpace/testOnline");
                menus = pushRoutes(menus , 'testOnline');
            }
            //用例管理
            if (r.funcCode.indexOf($right["useCaseManagement"]) > -1) {
                menus = pushRoutes(menus, "serviceSpace");
                menus = pushRoutes(menus, "useCaseManage");
                allRight.push("/serviceSpace/useCaseManage");
            }

        }
        if(routerMenu.includes("management")){
            //角色管理
            if (r.funcCode.indexOf($right["roleManagement"]) > -1) {
                allRight = pushRights(allRight, "/management/rolesmanage");
                menus = pushRoutes(menus, "roleManagement");
                menus = pushRoutes(menus, "management");
            }
            //用户组
            if (r.funcCode.indexOf($right["groupManagement"]) > -1) {
                allRight = pushRights(allRight, "/management/usergroupmanage");
                menus = pushRoutes(menus, "groupManagement");
                menus = pushRoutes(menus, "management");
            }
            //用户
            if (r.funcCode.indexOf($right["userManagement"]) > -1) {
                allRight = pushRights(allRight, "/management/usermanage");
                menus = pushRoutes(menus, "userManagement");
                menus = pushRoutes(menus, "management");
            }
            //算子管理
            if (r.funcCode.indexOf($right["udfOperator"]) > -1) {
                allRight = pushRights(allRight, "/management/operator");
                menus = pushRoutes(menus, "udfOperator");
                menus = pushRoutes(menus, "management");
            }
            //日志查询
            if (r.funcCode.indexOf($right["logQuery"]) > -1) {
                allRight = pushRights(allRight, "/management/logquery");
                menus = pushRoutes(menus, "logQuery");
                menus = pushRoutes(menus, "BusManagement");
                menus = pushRoutes(menus, "management");
            }
            //接入服务
            if (r.funcCode.indexOf($right["accessBusManagement"]) > -1) {
                allRight = pushRights(allRight, "/datacenter/service");
                menus = pushRoutes(menus, "accessBusManagement");
                menus = pushRoutes(menus, "BusManagement");
                menus = pushRoutes(menus, "management");
            }
        }

        if(routerMenu.includes("caseScenario")){
            if (r.funcCode.indexOf($right["scenarioCase"]) > -1) {
                menus = pushRoutes(menus, "caseScenario");
                allRight.push("/case");
                allRight.push("/caseScenario");
            }
        }


        /* if (r.funcCode.indexOf($right["dataMining"]) > -1) {
            allRight = pushRights(allRight , "/datacenter/excavate");
            menus = pushRoutes(menus , "dataMining");
            menus = pushRoutes(menus , "Mining");
        }*/


    });
    if(routerMenu.includes("home")){
        menus = pushRoutes(menus, "Home");//主页
        allRight.push("/home");
    }


    allRight.push("/share");
    menus = pushRoutes(menus, "homeView");//门户预览
    menus = pushRoutes(menus, "visualView");//可视化预览
    menus = pushRoutes(menus, "error");//404
    menus = pushRoutes(menus, "login");//登录
    allRight.push("/datacenter/homeView"); //门户预览
    allRight.push("/datacenter/visualview");//预览都有路由权限

    allRight.push("/datacenter/jumpView");//其他系统跳转进入
    menus = pushRoutes(menus, "jumpView");//

    /*
    * 模型超市
    * */
    menus = pushRoutes(menus, "Market");
    menus = pushRoutes(menus, "MarketHome");
    allRight.push("/market")
    allRight.push("/market/home")

    menus = pushRoutes(menus, "MarketManagement");
    allRight.push("/market/management");

    menus = pushRoutes(menus, "MarketAttention");
    allRight.push("/market/attention")

    menus = pushRoutes(menus, "MarketModelMarket");
    allRight.push("/market/modelMarket")

    menus = pushRoutes(menus, "MarketModelMarketIndex");
    allRight.push("/market/modelMarketIndex")

    /*
    * 业务空间
    * */
    menus = pushRoutes(menus, "businessSpace");
    menus = pushRoutes(menus, "businessSpaceOverview");
    menus = pushRoutes(menus, "businessSpaceDefinition");
    allRight.push("/businessSpace/overview");
    allRight.push("/businessSpace/definition");

    allRight.push("/datacenter/modelView");

    menus = pushRoutes(menus, "serviceMediation");
    allRight.push("/management/serviceMediation");
    menus = pushRoutes(menus, "busService");
    allRight.push("/management/busService");

    allRight.push("/datacenter/dataContact");
    allRight.push("/datacenter/dataReady");
    allRight.push("/datacenter/rapidAnalysis");
    allRight.push("/datacenter/modeling");

    // 流程管理
    allRight.push("/management/process");
    menus = pushRoutes(menus , "process");
    return {allRight, menus};
}
