<!--
    搭配 ceDragList  来绑定放置接收事件
    props :
        hoverClass 拖拽经过的样式类名
        height 高度设置
    放置 触发事件 drop
-->
<template>
    <div class="CeDropCont" :style="{height}" ref="dropper">
        <slot></slot>
    </div>
</template>

<script>
import * as $ from "jquery"
import 'jquery-ui-dist/jquery-ui'
import 'jquery-ui-dist/jquery-ui.min.css'
export default {
    name: "CeDropCont",
    props : {
        hoverClass : {
            type : String ,
            default : "drop_hover"
        },
        height : {
            type : String ,
            default: "100%"
        }
    },
    data(){
        return {
            canDrop : false
        }
    },
    methods : {
        bindDrop() {
            const vm = this , {hoverClass} = vm;
            $(vm.$refs.dropper).droppable({
                hoverClass ,
                drop: vm.drop ,
                over : function (){

                }
            })
        },
        dragStart(){
            this.canDrop = true;
        },
        dragEnd() {
            this.canDrop = false;
        },
        drop(e){
            this.$emit("drop" ,e);
        }
    },
    mounted() {
        this.bindDrop();
    }

}
</script>

<style scoped>

</style>
