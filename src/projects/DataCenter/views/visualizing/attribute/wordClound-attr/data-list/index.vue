<template>
    <div class="chartAttr newAttr">
        <div class="ce-attr_form ce-attr_form-axis">
            <chart-base
                :axisList="axisList"
                :showDrill="showDrill"
                :filterSelects="filterSelects"
                :chartData="chartData"
                @allowDrop="allowDrop"
                @dropEnd="dropEnd"
                @changeVal="changeVal"
                @duplicateFun="duplicateFun"
                @nodeFilter="nodeFilter"
                @deleteNode="deleteNode"
                @changeFastCount="changeFastCount"
                @deleteFilter="deleteFilter"
                @inputData="inputData"
                @addFilter="addFilter"
                @typeCheck="typeCheck"
                @dropFilter="dropFilter"
                @filterData="filterData"
                @updateValue="updateValue"
            ></chart-base>
        </div>
        <div class="ce-data_tree ce-data_tree-axis">
            <DataNodeTree ref="nodeTree"  :dir_dataSetId="dir_dataSetId" @resetList="resetList" @inputData="inputData"/>
        </div>
        <!--过滤器配置-->
        <FilterDialog ref="filter_dialog" @filterData="setFilter"/>
    </div>
</template>
<script src="./data-list.js"></script>
<style scoped lang="less" src="../../css/attr.less"></style>
<style scoped lang="less" src="./data-list.less"></style>
