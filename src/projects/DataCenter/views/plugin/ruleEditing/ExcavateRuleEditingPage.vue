<template>
    <div class="ruleEditingPage selectn">
        <div class="tree">
            <ExcavateRuleTree :row="row" :sourceData="sourceData" @setAllUdf="setAllUdf"/>
        </div>
        <div class="flow" v-loading="settings.loading">
            <ExcavateRuleFlowPanel ref="flow" :loadParams="settings" :sourceData="sourceData" :row="row" :paramsNode="paramsNode" @setParamPanel="showOperation" @setColumn="setColumn"/>
        </div>
    </div>
</template>

<script>
    import OperatorLogicSetting from '../streamingDispose/OperatorLogicSetting'
    import ExcavateRuleTree from './ExcavateRuleTree'
    import ExcavateRuleFlowPanel from './ExcavateRuleFlowPanel'

    export default {
        name: "ExcavateRuleEditingPage",
        props : {
            sourceData : Object,
            row:Object
        },
        components: {
            OperatorLogicSetting,
            ExcavateRuleTree,
            ExcavateRuleFlowPanel
        },
        data() {
            return {
                editParams: false ,
                nodeData : {},
                paramsNode:{},
                column:[],
                settings:{
                    loading: false
                }
            }
        },
        methods : {
            setAllUdf(udfs){
                this.$refs.flow.reqLineOptions(udfs);
            },
            closeOperator(data,code){
                this.$emit("closeOperator",data,code);
            },
            setColumn(val){
                this.column = val;
            },
            showOperation(show , node,data){
                this.editParams = show;
                if(node){
                    this.nodeData = node;
                    this.$nextTick(()=>{
                        this.$refs.operation.init(node,this.column,data);
                    })
                }
            },

            acceptNodeParams(paramsNode){
                this.paramsNode=paramsNode;
            }

        }
    }
</script>

<style scoped>
    .ruleEditingPage {
        height: calc(100vh - 65px);
        overflow: hidden;
        position: relative;
        background-color: #f5f5f5;
        margin-top: 15px;
    }

    .tree {
        float: left;
        width: 278px;
        height: 100%;
        padding:0 4px;
        box-sizing: border-box;
    }

    .flow {
        margin: 0 0 0 200px;
        height: 100%;
        position: relative;
        background-size: 10px 10px;
        background-color: #fff;
        overflow: auto;
    }

    .param {
        float: right;
        height: 100%;
        width: 360px;
        border-left: 1px solid #ccc;
    }
</style>
