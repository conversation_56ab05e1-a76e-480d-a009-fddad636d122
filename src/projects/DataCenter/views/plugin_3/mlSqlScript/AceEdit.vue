<script>
    import editor from 'vue2-ace-editor';
    export default {
        name: "AceEdit" ,
        extends : editor ,
        props : {
            autoComplete: Boolean,  //源码中添加该配置
        },
        mounted() {
            var _this = this;
            var vm = this;
            var lang = this.lang||'text';
            var theme = this.theme||'chrome';
            var autoComplete = this.autoComplete || true
            require('brace/ext/emmet');
            var ace = require('brace');
            var editor = vm.editor = ace.edit(this.$el);
            editor.$blockScrolling = Infinity;

            this.$emit('init',editor);

            //editor.setOption("enableEmmet", true);
            editor.getSession().setMode(typeof lang === 'string' ? ( 'ace/mode/' + lang ) : lang);
            editor.setTheme('ace/theme/'+theme);
            if(this.value)
                editor.setValue(this.value,1);
            this.contentBackup = this.value;

            editor.on('change',function () {
                var content = editor.getValue();
                vm.$emit('input',content);
                vm.contentBackup = content;
            });
            if(vm.options)
                editor.setOptions(vm.options);
            if (autoComplete) {
                var staticWordCompleter = {
                    getCompletions: function (editor, session, pos, prefix, callback) {
                        _this.$emit('setCompletions', editor, session, pos, prefix, callback)
                    }
                }
                editor.completers = [staticWordCompleter]

                editor.setOptions({
                    enableBasicAutocompletion: true,
                    enableSnippets: true,
                    enableLiveAutocompletion: true,//只能补全
                })
            }
        }
    }
</script>
