<!--
    @Describe: 审批的表单
    @Author: wangjt
    @Date: 2024/4/19
-->
<template>
    <div class="dialog-page__outer">
        <div :class="{'ovh height100' : settings.loading}" v-loading="settings.loading">
            <el-form ref="form" :model="form" :rules="rules" :disabled="!!type" label-position="right"
                     label-width="100px">
                <el-form-item v-for="(item ,k) in formData" :key="k" :label="`${item.label} :`" :prop="item.prop">
                    <el-input v-model="form[k]" :disabled="item.disabled" v-if="item.formType === 'input'"
                              :maxlength="item.maxlength"
                              show-word-limit :placeholder="item.placeholder"></el-input>
                    <dg-select v-model="form[k]" v-if="item.formType === 'select'" :disabled="item.disabled"
                               :data="item.data()" :placeholder="item.placeholder"></dg-select>
                    <span v-if="item.formType === 'span'">{{ form[k] }}</span>
                    <el-input v-model="form[k]" type="textarea" :maxlength="item.maxlength"
                              show-word-limit v-if="item.formType === 'textarea'"
                              :disabled="item.disabled"
                              resize="vertical"
                              :rows="item.rows" :placeholder="item.placeholder"></el-input>
                </el-form-item>
            </el-form>
            <dialogFooterBtn v-footer :data="btnGroup"/>
        </div>
    </div>
</template>

<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins"
import marketUse from "@store/modules/market/market-constant"
import DialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn";
import {common} from "dc-front-plugin/src/api/commonMethods/common"

export default {
    name: "approvalPage",
    mixins: [commonMixins, marketUse, common],
    components: {DialogFooterBtn},
    props: {
        row: Object,
        type: {
            type: String,
            default: ''
        },
    },
    computed: {
        rules() {
            return this.type ? {} : {
                auditOption: [
                    {required: true, message: '请输入审批意见', trigger: ['blur', 'change']}
                ]
            }
        },
    },
    data() {
        return {
            btnGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => !this.type
                },
                {
                    name: '关闭',
                    clickFn: this.cancelFn,
                    show: () => this.type
                },
                {
                    name: '同意',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: () => this.approveForApply("2"),
                    disabledFn: () => this.settings.loading,
                    show: () => !this.type
                },
                {
                    name: '不同意',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: () => this.approveForApply("1"),
                    disabledFn: () => this.settings.loading,
                    show: () => !this.type
                },
            ],
            form: {
                auditOption: "",
            },

            formData: {
                resourceType: {
                    label: '资源类型',
                    prop: "resourceType",
                    formType: 'select',
                    disabled: true,
                    data: () => this.m_market_get_model_types()
                },
                resourceName: {
                    label: '资源名称',
                    prop: 'resourceName',
                    formType: 'input',
                    disabled: true,
                },
                applyTime: {
                    label: '申请时间',
                    prop: 'applyTime',
                    formType: 'input',
                    disabled: true,
                },
                applyReason: {
                    label: '申请原因',
                    prop: 'applyReason',
                    formType: 'textarea',
                    disabled: true,
                    rows:4,
                },
                auditUser: {
                    label: '审批人',
                    prop: 'auditUser',
                    formType: 'input',
                    disabled: true
                },
                modelEntry:{
                    label:'模型入口',
                    prop:'modelEntry',
                    formType:'input',
                    disabled: true
                },
                auditOption: {
                    label: '审批意见',
                    prop: 'auditOption',
                    formType: 'textarea',
                    maxlength: 500,
                    placeholder: "请输入审批意见",
                    rows:4,
                }
            }
        }
    },
    methods: {
        // 同意 、不同意
        approveForApply(auditState = "1") {
            const vm = this, {settings, $services, confirm} = vm;
            let params = {
                auditState,
                id: this.row.id,
                auditOption: this.form.auditOption,
            };
            this.$refs.form.validate(valid => {
                if (valid) {
                    confirm('提示', '确认提交审批?', () => {
                        settings.loading = true;
                        $services('market').audit(params, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("已审批")
                                vm.$emit("submit")
                            }
                        })
                    })
                }
            })
        },
        getDetail() {
            this.$services('market').viewAuditDetail({id: this.row.id}).then(res => {
                if (res.data.status === 0) {
                    this.form.auditOption = res.data.data.auditOption;
                }
            })
        },
        init() {
            const {row, type} = this;
            this.form.resourceType = row.resource_type;
            this.form.resourceName = row.resource_name;
            this.form.applyTime = row.apply_time;
            this.form.applyReason = row.apply_reson;
            this.form.auditUser = row.audit_user_name;
            this.form.modelEntry = row.model_entry;
            if (type) this.getDetail();
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">

</style>
