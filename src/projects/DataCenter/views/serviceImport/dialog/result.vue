<template>
    <div class="all">
        <el-tabs v-model="actTab" @tab-click="tabChange">
            <el-tab-pane :label="allTxt" name="all"></el-tab-pane>
            <el-tab-pane :label="successTxt" name="success"></el-tab-pane>
            <el-tab-pane :label="failedTxt" name="failed"></el-tab-pane>
            <!-- <el-tab-pane label="历史日志" name="log"></el-tab-pane> -->
        </el-tabs>
        <el-input class="filter"
                      v-model="filterTxt"
                      v-input-limit:trim
                      :placeholder="placeholder" suffix-icon="el-icon-search"></el-input>
        <div class="">
            <common-table
                        ref="list"
                        height="120px;"
                        :pagination="false"
                        :data="showTableList"
                        :columns="tHeadData"
                >
            </common-table>
        </div>
    </div>
</template>

<script>
export default {
    name: "index",
    components: {

    },
    computed: {
        showTableList() {
            return this.tableData.filter(li => !this.filterTxt || li.name.toLowerCase().includes(this.filterTxt.toLowerCase()));
        },
    },
    watch : {
        filterTxt(){
            this.tableSearch();
        },
    },
    props: {
        res : Object,
    },
    data(){
        return {
            filterTxt : "",
            placeholder : "请输入关键字搜索",
            allTxt : "",
            successTxt : "",
            failedTxt :"",
            tableData : [],
            tHeadData : [
                {
                    prop: "name",
                    label: "名称"
                },{
                    prop: "type",
                    label: "类型"
                },{
                    prop: "importState",
                    label: "结果"
                },{
                    prop: "memo",
                    label: "备注"
                },
            ],
            row : [],
            actTab : "all",
            tabs : [
            ]
        }
    },
    methods : {
        tableSearch() {
            // return this.tableData.filter(li => !this.filterTxt || li.name.toLowerCase().includes(this.filterTxt.toLowerCase()));
        },
        tabChange(value) {
            if(value.name === 'all') this.tableData = [...this.res.importCompleteFailResultVoList, ...this.res.importCompleteSuccessResultVoList];
                else if (value.name === 'success') this.tableData = [ ...this.res.importCompleteSuccessResultVoList];
                    else if(value.name === 'failed') this.tableData = [...this.res.importCompleteFailResultVoList];
        }
    },
    created() {
        let allLenth = this.res.importCompleteFailResultVoList.length + this.res.importCompleteSuccessResultVoList.length;
        this.allTxt = '全部(' + allLenth + ')';
        this.successTxt = '成功(' + this.res.importCompleteSuccessResultVoList.length + ')';
        this.failedTxt = '失败(' + this.res.importCompleteFailResultVoList.length + ')';
        this.tableData = [...this.res.importCompleteFailResultVoList, ...this.res.importCompleteSuccessResultVoList];
    }
}
</script>

<style scoped lang="less">
.filter {
    width : 200px;
    float: right;
    padding-bottom: 5px;
}
.all {
    background-color : white;height: 100%;padding-left:15px;
}
.manage {
    height: 100%;
    &-cont {
        height: calc(100% - 33px);
    }
    &-page {
        height: calc(100% - 14px);
        padding-top: 14px;
    }
}

</style>
