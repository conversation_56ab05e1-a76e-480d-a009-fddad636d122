<template>
    <div class="modelMarket">
        <div class="modelMarket_header">
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(targetPlace) }}</span>
                <dg-select v-model="targetType" :data="targetOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="targetPlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(appPlace) }}</span>
                <dg-select v-model="appType" :data="appOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="appPlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(policePlace) }}</span>
                <dg-select v-model="policeType"
                           :data="policeOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="policePlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(casePlace) }}</span>
                <dg-select v-model="caseType"
                           :data="caseOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="casePlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(zonePlace) }}</span>
                <dg-select v-model="zoneType"
                           :data="zoneOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="zonePlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(defensivePlace) }}</span>
                <dg-select v-model="defensive"
                           :data="defensiveOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="defensivePlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(scorePlace) }}</span>
                <dg-select v-model="scoreVal"
                           :data="scoreOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="scorePlace"></dg-select>
            </div>
            <div class="modelMarket_search">
                <span class="modelMarket_label">{{ getLabel(activityPlace) }}</span>
                <dg-select v-model="activityVal"
                           :data="activityOpt"
                           filterable clearable
                           @change="searchList"
                           :placeholder="activityPlace"></dg-select>
            </div>


            <div class="modelMarket_date">
                <span class="modelMarket_label">{{ getLabel(issuePlace) }}</span>
                <time-range-picker @timeChange="timeChange" />
            </div>
            <el-input clearable class="modelMarket_input"
                      v-model.trim="searchTxt"
                      v-input-limit:trim
                      :placeholder="searchPlace"
                      @keyup.enter.native="searchList"
            >
                <dg-select class="modelMarket_sel" slot="prepend"
                           @change="searchList"
                           v-model="searchType" :data="searchOpt"></dg-select>
                <i
                        class="el-icon-search el-input__icon poi"
                        slot="suffix"
                        @click="searchList">
                </i>
            </el-input>
        </div>
        <div class="modelMarket_cont" v-loading="settings.loading">
            <div class="modelMarket_top">
                <div class="modelMarket_rank selectn">
                    <sort-list :data="sortList" @change="sortChange"/>
                </div>
                <div class="modelMarket_count" v-html="countResult"></div>
            </div>
            <div class="modelMarket_card">
                <dg-scrollbar>
                    <ul class="modelMarket_box">
                        <list-card v-for="(list , i) in listData" :key="i"
                                   :data="list"/>
                        <no-data class="pct100" v-if="!listData.length"/>
                    </ul>
                </dg-scrollbar>
                <div class="modelMarket_page">
                    <el-pagination
                            @size-change="changeSize"
                            @current-change="changePage"
                            v-bind="paginationProps"
                            :total="total">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import SortList from "@/components/common/SortList";
import ListCard from "@/projects/DataCenter/views/model-market/home/<USER>/list-card";
import NoData from "@/components/no-data";
import {servicesMixins} from "@/projects/DataCenter/views/model-market/service-mixins/service-mixins";
import {CardMixins} from "@/projects/DataCenter/views/model-market/home/<USER>/CardMixins";
import TimeRangePicker from "./components/timeRangePicker.vue"
export default {
    name: "index",
    components: {
        SortList,
        ListCard,
        NoData,
        TimeRangePicker,
    },
    mixins: [commonMixins, servicesMixins, CardMixins],
    data() {
        return {
            targetType: "",
            targetOpt: [],
            targetPlace: "对象类型",
            appType: "",
            appOpt: [],
            appPlace: "应用类型",
            policeType: "",
            policeOpt: [],
            policePlace: "警种类型",
            caseType: "", // 案件类型
            caseOpt: [],
            casePlace: "案件类型",
            scoreVal: "",
            scoreOpt: [
                {label: '低', value: '低'},
                {label: '中', value: '中'},
                {label: '高', value: '高'},
            ],
            scorePlace: "评价分",
            activityVal: "",
            activityOpt: [
                {label: '低', value: '低'},
                {label: '中', value: '中'},
                {label: '高', value: '高'},
            ],
            activityPlace: "活跃度",
            issueTime: [],
            issuePlace: "发布时间",
            defensive: "",
            defensiveOpt: [],
            defensivePlace: "打防管控",
            zoneType: "",
            zoneOpt: [],
            zonePlace: "按区分类",
            searchType: "name",
            searchOpt: [
                {
                    label: "模型名称",
                    value: "name"
                }, {
                    label: "模型标签",
                    value: "label"
                }
            ],
            searchTxt: "",
            sortList: [
                {
                    label: "关注量",
                    code: "focus"
                }, {
                    label: "浏览量",
                    code: "browse"
                }, {
                    label: "评分",
                    code: "score"
                }, {
                    label: "发布时间",
                    code: "publishTime"
                },
            ],
            listData: [],
            //分页
            paginationProps: {
                currentPage: 1,
                pageSizes: [8, 16, 24],
                pageSize: 8,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            sortCode: "",
            sortOrder: false,
        }
    },
    computed: {
        searchPlace() {
            const vm = this;
            let typeN = vm.searchOpt.find(sea => sea.value === vm.searchType).label;
            return `请输入${typeN}搜索`;
        },
        countResult() {
            return `共找到<span class="modelMarket_total"> ${this.total} </span>个模型`;
        },
    },
    methods: {
        timeChange(start , end , noQuery){
            this.issueTime = [];
            this.issueTime.push.apply(this.issueTime , [start,end]);
            if(noQuery) return;
            console.log(
                this.issueTime
            )
            this.searchList()
        },
        getLabel(label) {
            return `${label} :`
        },
        //获取模型信息
        getTypeList() {
            const vm = this, {manageServices, manageMock} = this;
            let services = vm.getServices(manageServices, manageMock);
            vm.targetOpt = [];
            vm.appOpt = [];
            vm.policeOpt = [];
            vm.caseOpt = [];
            vm.defensiveOpt = [];
            vm.zoneOpt = [];

            services.initPublishModel().then(res => {
                if (res.data.status === 0) {
                    const result = res.data.data;
                    vm.targetOpt = result.objTypeCodeList.map(n => {
                        return {label: n.name, value: n.code}
                    });
                    vm.appOpt = result.appTypeCodeList.map(n => {
                        return {label: n.name, value: n.code}
                    });
                    vm.policeOpt = result.polTypeCodeList.map(n => {
                        return {label: n.name, value: n.code}
                    });
                    vm.caseOpt = result.caseTypeCodeList.map(n => {
                        return {label: n.name, value: n.code}
                    })
                    vm.zoneOpt = result.areaTypeCodeList.map(n => {
                        return {label: n.name, value: n.code}
                    })
                    vm.defensiveOpt = result.controlTypeCodeList.map(n => {
                        return {label: n.name, value: n.code}
                    })
                }
            })
        },
        searchList() {
            this.changePage();
        },
        /**
         * 排序改变 created 初始化 即触发
         * @param code
         * @param order
         */
        sortChange(code, order) {
            this.sortCode = code;
            this.sortOrder = order;
            this.changePage();
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);

        },
        /**
         * 模型市场卡片
         * @param inx
         */
        changePage(inx = 1) {
            const vm = this, {
                manageServices,
                manageMock,
                searchTxt,
                searchType,
                targetType,
                appType,
                policeType,
                sortCode,
                sortOrder,
                settings,
                caseType,
                scoreVal,
                activityVal,
                issueTime,
                defensive,
                zoneType,
            } = this;
            let services = vm.getServices(manageServices, manageMock);
            vm.paginationProps.currentPage = inx;
            let params = {
                "objType": targetType, //传code
                "appType": appType,
                "policeType": policeType,
                "searchType": searchType,
                "searchCond": searchTxt,
                "sortType": sortCode,//focus:关注 browse：浏览 score：评分  publishTime：发布时间
                "sort": sortOrder ? "asc" : "desc",  //asc:升序 desc:降序
                "pageNum": inx,
                "pageSize": vm.paginationProps.pageSize,
                "caseType": caseType,
                "areaType": zoneType,
                "controlType": defensive,
                "score": scoreVal,
                "browseCount": activityVal,
                "publishTimeStart": issueTime && issueTime[0] || "",
                "publishTimeEnd": issueTime && issueTime[1] || "",
            };
            vm.listData = [];
            settings.loading = true;
            services.queryModelMarketPage(params, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.listData = vm.transCardList(result.dataList);
                    vm.total = result.totalCount;
                }
            })
        }
    },
    created() {
        this.getTypeList();
    }
}
</script>

<style scoped lang="less">
.modelMarket {
    height: 100%;
    overflow: auto;

    &_header {
        padding: 28px 20px;
        background: #fff;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 14px;
        justify-content: flex-start;
        gap: 10px;
    }
    &_input {
        //flex:1;
        width: 25%;
    }
    &_search {
        //flex: 1;
        width:calc(25% - 10px);
        min-width: 14.2rem;
        display: inline-flex;
    }

    &_date {
        display: inline-flex;
    }

    &_label {
        line-height: 2rem;
        min-width: 100px;
        height: 2rem;
        color: #888;
        padding: 0 6px;
        white-space: nowrap;
        text-align: right;
    }


    &_sel {
        width: 108px;
    }

    &_count {
        font-family: MicrosoftYaHei;
        font-size: 14px;
        line-height: 34px;
        color: rgba(0, 0, 0, 0.65);
    }

    &_top {
        display: flex;
        justify-content: space-between;
        padding: 12px 0;
        margin: 0 28px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    &_cont {
        padding-bottom: 14px;
        background: #fff;
        height: calc(100% - 104px - 6rem);
        box-sizing: border-box;
    }

    &_card {
        padding: 0 21px;
        height: calc(100% - 100px);
    }

    &_box {
        padding: 10px 9px 9px 0;
        background: #fff;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: flex-start;
        align-content: flex-start;

        .list-card {
            width: calc(25% - 16px);
            margin: 7px;
        }
    }

    &_page {
        text-align: right;
        padding-top: 8px;
    }
}
</style>
<style>
.modelMarket_total {
    font-family: MicrosoftYaHei-Bold;
    font-size: 14px;
    color: #1890FF;
}
</style>
