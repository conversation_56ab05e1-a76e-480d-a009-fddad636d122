<template>
    <div class="model-share">
        <p>关于此模型</p>
        <div class="model-share-explain">
            <div class="model-share_title">模型说明</div>
            <div class="model-share-explain_content">
                <el-table
                        :data="tableData"
                        style="width: 100%"
                        :show-header="false"
                        border
                >
                    <el-table-column
                            prop="label"
                            width="200">
                    </el-table-column>
                    <el-table-column
                            prop="value">
                    </el-table-column>
                </el-table>
            </div>

        </div>
        <div class="model-share-pic">
            <div class="model-share_title">模型配图</div>
            <div v-if="picList && picList.length">
                <el-carousel indicator-position="outside">
                    <el-carousel-item v-for="(item , index) in picList" :key="index">
                        <el-image  :src="item.picture" :preview-src-list="srcList" class="previewImg"></el-image>
                    </el-carousel-item>
                </el-carousel>
            </div>
            <no-data v-else :title="emptyTxt" top="5%">
            </no-data>
        </div>
        <div class="model-share-set">
            <div class="model-share_title bold">来源数据集</div>
            <div class="model-share-set_content">
                <div v-for="(item , index) in dataSetList" :key="index">
                    <span class="model-share-set_content_icon">
                        <i class="dg-iconp icon-database"></i>
                    </span>
                    <span class="model-share-set_content_name overLineClamp1" :title="item.name">{{item.name}}</span>
                </div>

            </div>
        </div>
        <div class="model-share-user">
            <div class="model-share_title bold">模型开发者</div>
            <div class="model-share-user_content">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <div class="">创建人：{{userInfo.userName}}</div>
                        <div class="">电子邮箱：{{userInfo.email}}</div>
                        <!--<div class="">所属单位：{{userInfo.deptName}}</div>-->
                    </el-col>
                    <el-col :span="12">
                        <div class="">联系电话：{{userInfo.phone}}</div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>

<script>
    import  imgInfo from '@/assets/images/common/pic1.png';
    import NoData from "@/components/no-data";
    import marketUse from '@store/modules/market/market-constant'
    export default {
        name: "modelShare",
        mixins:[marketUse],
        props:{
            modelDataInfo:Object
        },
        components: {
            NoData
        },
        watch: {
            modelDataInfo: {
                handler(newVal) {
                    const vm = this;
                    vm.initInfo();
                },
                deep: true
            }
        },
        computed: {
            modelTypes(){
                return this.m_market_get_model_types();
            }
        },
        data(){
            return {
                imgInfo,
                tableData:[
                    { label:'模型分类',value:''},
                    { label:'模型版本',value:''},
                    { label:'上架时间',value:''},
                    { label:'来源类型',value:''},
                ],
                picList:[],
                srcList:[],
                dataSetList:[],
                userInfo:{
                    userName:'',
                    phone:'',
                    email:'',
                    deptName:''
                },
                showViewer:false,
                emptyTxt:'暂无模型配图'
            }
        },
        methods:{
            initInfo(){
                this.tableData[0].value = this.modelDataInfo.typeCodeName;
                this.tableData[1].value = this.modelDataInfo.version;
                this.tableData[2].value = this.modelDataInfo.putTime;
                let model = this.modelTypes.find(it => it.value === this.modelDataInfo.type);
                this.tableData[3].value = model && model.label || '';
                this.userInfo = this.modelDataInfo.publishUser || [];
                this.dataSetList = this.modelDataInfo.dataSets || [];
                this.picList = this.modelDataInfo.pics;
                this.srcList = this.picList ? this.picList.map(n=>n.picture) :[];
            },
        },
        mounted(){
            this.initInfo();
        }
    }
</script>

<style scoped lang="less">
    .model-share{
        .overLineClamp1 {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
        }
        .bold{
            font-weight: bold;
        }
        p{
            ont-family: MicrosoftYaHei-Bold;
            font-size: 18px;
            color: rgba(0,0,0,0.85);
            font-weight: bold;
        }
        /deep/.el-carousel__container{
            height:450px;
        }
        &_title{
            font-family: MicrosoftYaHei;
            font-size: 16px;
            margin:20px 0;
            color: rgba(0,0,0,0.85);
            line-height: 16px;
        }
        &-explain{
            &_content{
                table{
                    border: 1px solid rgba(0,0,0,0.12);
                }
            }
        }
        &-set{
            &_content{
                display: flex;
                flex-wrap: wrap;
                div{
                    background: #FFFFFF;
                    line-height: 34px;
                    border: 1px solid rgba(0,136,255,0.35);
                    border-radius: 2px;
                    margin:0 10px 10px 0;
                    width:220px;
                    flex-wrap: nowrap;
                    display: flex;
                }
                &_icon{
                    display: inline-block;
                    height:36px;
                    width:34px;
                    text-align: center;
                    background: rgba(0,136,255,0.12);
                    border-radius: 1px 0 0 1px;
                    i{
                        color:rgba(0,136,255,1);
                        font-size: 20px;
                    }
                }
                &_name{
                    font-family: MicrosoftYaHei;
                    font-size: 14px;
                    color: rgba(0,0,0,0.65);
                    padding:0 10px;
                    width:calc(100% - 34px);
                }
            }
        }
        &-user{
            &_content{
                font-size: 14px;
                color: rgba(0,0,0,0.65);
                line-height: 35px;
            }
        }
    }

</style>
