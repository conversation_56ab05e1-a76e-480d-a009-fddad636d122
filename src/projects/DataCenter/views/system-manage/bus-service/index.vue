<<template>
    <div class="busService">
        <div>
            <dg-button type="primary" @click="optionInfo('add',{})">新增服务</dg-button>
            <dg-button type="primary">批量接入</dg-button>
        </div>
        <div>
            <el-tabs class="ce-share_tab" v-model="activeName" @tab-click="tabClick">
                <el-tab-pane v-for="(tab , inx) in tabs" :key="inx" :label="tab.label" :name="tab.name"></el-tab-pane>
            </el-tabs>
        </div>
        <div class="busService-com">
            <thirdPartyServices ref="thirdParty" v-show="activeName === 'thirdParty'" @optionInfo="optionInfo"></thirdPartyServices>
            <mediationServices ref="mediation" v-show="activeName === 'mediation'" @optionInfo="optionInfo"></mediationServices>
        </div>
    </div>
</template>

<script>
    import thirdPartyServices from './dialog/thirdPartyServices'
    import mediationServices from './dialog/mediationServices'

    export default {
        name: "bus-service",
        components:{
            thirdPartyServices,
            mediationServices
        },
        data() {
            return {
                activeName:'mediation',
                tabs:[
                    { name: 'mediation', label: '中介服务'},
                    { name: 'thirdParty', label: '第三方服务'},
                ]
            }
        },
        methods: {
            tabClick(){

            },
            //服务接口
            portInfo(type, row){
                const vm = this;
                let layer = vm.$dgLayer({
                    title : "服务接口",
                    content : require("./dialog/servicesOption/servicesPort"),
                    maxmin:false,
                    move:false,
                    props:{
                        type: type,
                        info: row || {},
                    },
                    area : ["1000px" , "600px"],
                    on : {
                        submit(){
                        }
                    }
                })
            },
            //编辑、新增、查看
            optionInfo(type, row){
                const vm = this;
                if(type === 'port'){
                    this.portInfo(type, row);
                    return
                }
                let titleList = {
                    add: '新增服务',
                    edit: '编辑服务',
                    look: '查看服务',
                }
                let layer = vm.$dgLayer({
                    title : titleList[type],
                    content : require("./dialog/servicesOption/operation"),
                    maxmin:false,
                    move:false,
                    noneBtnField: type === 'look',
                    props:{
                        type: type,
                        info: row || {},
                    },
                    area : ["1000px" , "600px"],
                    on : {
                        submit(){
                        }
                    }
                })
            },
        },
        mounted(){
            this.$refs.thirdParty.tableChange();
            this.$refs.mediation.tableChange();
        }
    }
</script>

<style scoped lang="less">
.busService{
    height: calc(100% - 14px);
    background: #fff;
    border-radius: 4px;
    padding: 28px 28px 14px 28px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;

    &-com{
        height:calc(100% - 86px);
    }
}
</style>