<template>
    <div class="dataTree" v-loading="settings.loading">
        <el-input placeholder="请输入名称" v-model="filterText" v-input-limit:trim size="medium" class="ce-search">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="ce-tree selectn">
            <dg-tree
                    class="filter-tree"
                    :data="data"
                    :props="defaultProps"
                    node-key="id"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    :highlight-current="true"
                    @node-click="nodeClick"
                    :default-expanded-keys="expandData"
                    ref="tree">

                <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                    <span class="node-label"
                          :title="data.name"
                          :class="{
                            'el-icon-folder' : !node.expanded && data.elementOrDir === '1',
                            'el-icon-folder-opened' : node.expanded && data.elementOrDir === '1',
                             'el-icon-document' : data.elementOrDir === '0'
                          }"
                    >{{ data.name }}</span>
                </span>

            </dg-tree>
        </div>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {treeMethods} from "@/api/treeNameCheck/treeName"

export default {
    name: "DataTree",
    mixins: [commonMixins],
    props: {
        hasSave: Boolean,
        notFirst: Boolean,
        row: Object,
        dirId: String,
        editDir: Boolean,
    },
    data() {
        return {
            filterText: '',
            data: [],
            defaultProps: {
                children: 'children',
                label: 'label',
                value: "id"
            },
            currentNode: '',
            expandData: []
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    methods: {
        ...treeMethods.methods,
        nodeClick(v, i) {
            this.$emit('nodeClick', v.label, v.id, i);
        },
        setTreeNode(data, level) {
            const vm = this, {editDir} = vm;
            let child_l = level + 1;
            return data.map(item => {
                let disabled = editDir && level > 1;
                let children = item.children && item.children.length && !disabled ? vm.setTreeNode(item.children, child_l) : [];
                return {
                    id: item.id,
                    label: item.name,
                    name: item.name,
                    children,
                    isParent: true,
                    pId: item.parentId,
                    type: item.type,
                    level,
                    elementOrDir:'1',
                    operateTime: item.oprateTime
                }

            });
        },
        initTree() {
            const vm = this, { settings, dirId} = this;
            let parentId = '', curId;
            if (this.notFirst === true) parentId = '-1';
            if (vm.row) curId = vm.row.dirParentId;
            if (dirId) curId = dirId;
            settings.loading = true;
            vm.data = [];
            vm.expandData = [];
            let services = vm.$services("serviceManage");
            services.queryServiceClassifyTree(settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if (!result || result.length === 0) return;
                    curId = curId || result[0].id;
                    vm.data = vm.setTreeNode(result, 1);
                    vm.data = vm.treeSortByWay(vm.data, 'operateTime', 'sortInTime');
                    vm.$nextTick(() => {
                        if (vm.$refs.tree) {
                            vm.$refs.tree.setCurrentKey(curId);
                            vm.expandData.push(curId);
                            let currentNode = vm.$refs.tree.getNode(curId);
                            vm.nodeClick(currentNode.data, currentNode);
                        }
                    });
                }
            })
        },
        setCurNode(){

        },
        filterNode: treeMethods.methods.filterNode,
        filterNodeType() {

        }
    },
    created() {
        this.initTree();
    }
}
</script>

<style scoped lang="less">


.dataTree {
    float: left;
    margin: 10px 0 0 10px;
    width: 230px;
    padding: 10px;
    background: #fff;
    border: 1px solid #ddd;
    height: calc(100% - 88px);
}

.ce-tree {
    margin-top: 12px;
    height: calc(100% - 50px);
    overflow: auto;
}

.ce-tree__menu {
    position: fixed;
    top: 0;
    min-width: 80px;
    text-align: left;
    border: 1px solid #ccc;
    background: #fff;
    padding: 0;
    z-index: 100;
    box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
    border-radius: 2px;
}

.ce-tree__menu li {
    cursor: pointer;
    list-style: none outside none;
    font-size: 12px;
    white-space: nowrap;
    border-bottom: 1px solid #eee;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
    color: #666;
}

.ce-tree__menu li:hover {
    color: @font-color;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
}

.node-label {
    max-width: 166px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.node-label::before {
    padding-right: 5px;
}
</style>
