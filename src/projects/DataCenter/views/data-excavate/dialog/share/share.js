

export default {
    name: "share",
    data(){
        return {
            placeholder : "输入用户名搜索",
            title : ["组织架构" , "全选"],
            value : ['1-1', '1-2-1'] ,
            data: [
                {
                    id: '1',
                    pid: 0,
                    label: '一级 1',
                    children: [
                        {
                            id: '1-1',
                            pid: '1',
                            label: '二级 1-1',
                            children: []
                        },
                        {
                            id: '1-2',
                            pid: '1',
                            label: '二级 1-2',
                            children: [
                                {
                                    id: '1-2-1',
                                    pid: '1-2',
                                    children: [],
                                    label: '二级 1-2-1'
                                },
                                {
                                    id: '1-2-2',
                                    pid: '1-2',
                                    children: [],
                                    label: '二级 1-2-2'
                                }
                            ]
                        }
                    ]
                }
            ],
        }
    },
    methods: {
        handleSearch(value, data) {
            console.log(value, data.label);
            if (!value) return true;
            return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
        }
    }
}