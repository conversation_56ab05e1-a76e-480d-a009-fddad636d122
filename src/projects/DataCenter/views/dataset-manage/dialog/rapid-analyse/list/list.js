import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import syncLogic from "../tree-operate/sync-logic-data"
import mergeDialog from "../tree-operate/merge-dataset"
import filterCondition from "../tree-operate/filter-condition"

export default {
    name: "list" ,
    components :{
        syncLogic,
        mergeDialog,
        filterCondition
    },
    props : {
        datasetId : String
    },
    mixins: [commonMixins, servicesMixins, common],
    data(){
        return {
            listTip :"", // 显示计算结果的前1000行数据
            listBtns : [
                {
                    icon : "el-icon-view" ,
                    label : "预览" ,
                    clickFn : this.preview ,
                    type : "" ,
                    show : ()=> true
                },{
                    icon : "el-icon-help" ,
                    label : "合并数据集" ,
                    clickFn : this.mergeData ,
                    type : "" ,
                    show : ()=> true
                },{
                    icon : "el-icon-finished" ,
                    label : "设置过滤条件" ,
                    clickFn : this.setFilter ,
                    type : "" ,
                    show : ()=> true
                },{
                    icon : "el-icon-document-copy" ,
                    label : "同步数据集结构" ,
                    clickFn : this.syncDataset ,
                    type : "primary" ,
                    show : ()=> true
                },
            ],
            tableData: [],
            tHeadData: [],
            total: 0,
            storeColumns : [] ,
            fields :[] ,
            addDatasetId : ""
        }
    },
    methods :{
        filterConditionStep(data){
            this.$refs.filterCon.showHistory(this.datasetId , {step :true ,...data});
        },
        joinTableStep(data){
            this.$refs.merge.showHistory(this.datasetId ,{step :true ,...data} , 'join');
        },
        unionTableStep(data){
            this.$refs.merge.showHistory(this.datasetId ,{step :true ,...data} , 'union');
        },
        syncColumnStep(data , name){
            this.$refs.syncLogic.showInfo(data , name);
        },
        mergeSuccess( data){//合并
            this.$emit("mergeSuccess" ,  data);
        },
        appendColumns(data){
            this.$emit("appendColumns" , data);
        },
        async preview(data , addDatasetId="") {
            const vm = this;
            if (data === undefined || !Array.isArray(data)) {
                await vm.getAllColumns();
            } else {
                vm.storeColumns = data;
            }
            if(addDatasetId){
                vm.addDatasetId = addDatasetId;
            }

            vm.fields = [];
            vm.storeColumns.forEach(col => {
                let field = col.name || col.code;
                vm.fields.push(field);
            });
            vm.changePage(1);
        },
        getAllColumns(){
            this.$emit("getAllColumns");
        },
        storeAllColumns(datas){
            this.storeColumns = datas;
        },
        getTableData(node){

        },
        mergeData (){
            this.$refs.merge.show(this.datasetId);
        },
        setFilter(){
            this.$refs.filterCon.show(this.datasetId);
        },
        syncDataset(){
            this.$refs.syncLogic.show(this.datasetId);
        },
        changePage(index){
            const vm = this , {datasetServices , datasetMock , datasetId , settings  ,paginationProps } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            if(!vm.storeColumns.length){
                vm.$message.info("暂无字段数据");
                return;
            }
            let previewVo = { addDataSetId : vm.addDatasetId, dataSetId : datasetId , page : index , pageSize : paginationProps.pageSize};
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            vm.tableData = [];
            vm.tHeadData = [];
            services.previewLogicDataSet(previewVo , settings).then(res => {
                if(res.data.status === 0){
                    vm.tHeadData = [];vm.tableData = [];
                    let result = res.data.data , head;
                    if(result && result.dataList && result.dataList.length){
                        head = result.dataList[0];
                        for(let key in head){
                            vm.tHeadData.push({
                                label : key ,
                                prop : key,
                                minWidth : key.length < 6 ? 120 : key.length * 16 + 32
                            })
                        }
                        vm.tableData = result.dataList;
                        vm.total = result.totalCount;
                    }else {
                        vm.total = 0;
                    }


                }
            })
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.changePage(1);
        }
    }
}