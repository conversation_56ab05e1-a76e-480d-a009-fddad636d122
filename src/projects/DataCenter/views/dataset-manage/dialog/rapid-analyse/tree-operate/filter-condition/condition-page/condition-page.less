
.f-c-content {
  max-height: calc(75vh - 160px);
  overflow: auto;
  .f-c-c-item {
    margin-top: 17px;
    border-radius: 2px;
    border: dashed 1px rgba(#000000, 0.15);
    padding: 12px 16px;

    .f-c-c-i-row {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 8px;
      align-items: center;

      .f-c-c-i-select-one,
      .f-c-c-i-select-two {
        width: 140px;
        margin-right: 7px;
      }

      .f-c-c-i-other {
        display: flex;
        min-width:200px;
        .number-select-left {
          margin-right: 10px;
        }

        .number-select /deep/ {
          .el-input__inner {
            padding-left: 8px;
            padding-right: 20px;
          }

          .el-input__suffix {
            right: 0;
          }
        }
      }

      .f-c-c-i-action {
        padding-left: 10px;
      }
    }
  }

  .filter-relation {
    padding: 17px 0 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}