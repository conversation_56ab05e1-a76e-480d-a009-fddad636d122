/**
 * 流式处理 接口
 * @Author: wangjt
 * @Date: 2020-05-27
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/plugin/window/" ,
    delete_root = "/serviceOrganization/";

/*
*  初始化
 * @param tranStepId
 * @returns {Promise<*>}
* */
export async function getPlugin( transtepId , settings) {
    return await reqUtil.get({
        url: root + 'getPlugin' ,
        settings : settings
    }, {transtepId} );
}

/*
*  保存
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function savePlugin( params ,settings ) {
    return await reqUtil.post({
        url: root + 'savePlugin' ,
        settings : settings
    }, params );
}

