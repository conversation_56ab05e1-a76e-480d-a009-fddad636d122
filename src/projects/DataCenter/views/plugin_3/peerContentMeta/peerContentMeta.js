

import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin/service-mixins/service-mixins";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {common} from "@/api/commonMethods/common"
import {commonMixins} from "@/api/commonMethods/common-mixins"
export default {
    name: "peerContentMeta",
    mixins : [pluginMixins , servicesMixins, common, commonMixins],
    components: {preView},
    props:{
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    data() {
        return {
            tabActiveName: "first",
            inputColumn: [],
            trajectoryInputColumn : [],
            tableOptions :[],
            width: "145px",
            effectiveData: {
                source_table : "",
                trajectory_table : "",
                zjhm : "",
                startTime : "",
                endTime : "",
                isEndTimeLabel : "",
                invterval : "",
                is_count : '0',
            },
            colomnInfo : [],
            // leftCode : "",
            // leftId : "",
            // leftName : "",
            // rightCode : "",
            // rightId : "",
            // rightName : "",
            sourceChooseInfo : [],
            trajectoryChooseInfo : [],
        }
    },
    methods: {
        /**
         * 来源表变更
         * @param {*} val 
         */
        sourceTableChange(val, columnList){
            const vm = this;
            // vm.inputColumn = [];
            // vm.trajectoryInputColumn = [];
            if (columnList === 'inputColumn') {
                vm.sourceChooseInfo = this.tableOptions.find((item) =>{return item.value === val});
            } else {
                vm.trajectoryChooseInfo = this.tableOptions.find((item) =>{return item.value === val});
            }
            vm.effectiveData.zjhm = "",
            vm.effectiveData.startTime = "",
            vm.effectiveData.endTime = "",
            vm.effectiveData.isEndTimeLabel = "",
            vm.effectiveData.invterval = "";
            if (vm.colomnInfo.leftTableColumns[0].stepCode === val) {
                vm[columnList] = vm.colomnInfo.leftTableColumns.map((item) =>{
                    item.label = item.columnZhName;
                    item.value = item.columnName;
                    return item;
                })
            }else if (vm.colomnInfo.rightTableColumns[0].stepCode === val) {
                vm[columnList] = vm.colomnInfo.rightTableColumns.map((item) =>{
                    item.label = item.columnZhName;
                    item.value = item.columnName;
                    return item;
                })
            }
        },
        checkColumn(){

        },
        peerQueryData(){
            const vm = this, {settings} = this;

            let services = this.$services("plugin3");
            settings.loading = true;
            vm.tableOptions = [];
            vm.colomnInfo = [];
            services.peerQueryData(this.sourceData.id, settings).then(res =>{
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.tableOptions.push({
                        value : result.leftname.code || result.leftname.name,
                        label : result.leftname.name,
                        id : result.leftname.transplugin_id,
                    })
                    vm.tableOptions.push({
                        value : result.rightname.code || result.rightname.name,
                        label : result.rightname.name,
                        id: result.rightname.transplugin_id,
                    });
                    // vm.leftCode  = result.leftname.code,
                    // vm.leftId = result.leftname.transplugin_id,
                    // vm.leftName = result.leftname.name
                    // vm.rightCode = result.rightname.code,
                    // vm.rightId = result.rightname.transplugin_id,
                    // vm.rightName = result.rightname.name,
                    vm.colomnInfo = result.inputColumnMap;

                    this.sourceTableChange(result.pluginMeta.source_table, 'inputColumn');
                    this.sourceTableChange(result.pluginMeta.trajectory_table, 'trajectoryInputColumn');
                    let {  source_table, trajectory_table , zjhm ,
                        startTime ,endTime , isEndTimeLabel,
                        invterval ,is_count } = result.pluginMeta;
                    this.effectiveData = {  source_table, trajectory_table , zjhm ,
                                            startTime ,endTime , isEndTimeLabel,
                                            invterval ,is_count };
                }
            })
        },
        save(fn,showTip){
            const vm = this, {settings} = this;
            if (
                vm.effectiveData.source_table === ""||
                vm.effectiveData.trajectory_table === ""||
                vm.effectiveData.zjhm === ""||
                vm.effectiveData.startTime === ""||
                vm.effectiveData.invterval === ""
                ) 
            {
                vm.$message.warning("请填写完整信息");
                return;
            }
            if (vm.effectiveData.isEndTimeLabel === '0' && vm.effectiveData.endTime === '') {
                vm.$message.warning("请选择结束时间字段");
                return;
            }
            let service = this.$services("plugin3");
            let params = {
                rightStepId : vm.sourceChooseInfo.id,
                rightStepCode : vm.sourceChooseInfo.value,
                rightStepName : vm.sourceChooseInfo.label,
                leftStepId : vm.trajectoryChooseInfo.id,
                leftStepCode : vm.trajectoryChooseInfo.value,
                leftStepName : vm.trajectoryChooseInfo.label,
                ...vm.effectiveData
            };
            if (vm.effectiveData.isEndTimeLabel === '1') {
                params.endTime = '';
            }
            settings.loading = true;
            service.PeerSavePlugin(this.sourceData.id, params, settings).then(res => {
                if (res.data.status === 0) {
                    // let result = res.data.data;
                    showTip && vm.$message.success("保存成功");
                    vm.$emit("setPreviewAble");
                    fn && fn();
                }
            })
        },
        preview() {
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.preview.show(this.rowData, this.sourceData);
            });
        },
    },
    mounted() {
        this.peerQueryData();
    },
}
