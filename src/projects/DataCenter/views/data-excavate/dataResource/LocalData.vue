<template>
    <div class="localData">
        <div class="filter-b">
            <el-input placeholder="请输入资源名称搜索" v-model.trim="filterText" @input="queryTable" size="small" class="ce-search">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
        </div>
        <ul class="list-tab">
            <li class="list-item"
                v-for="(item , index) in listTabs"
                :key="index"
                :class="{'active' : activeTab === index}"
                @click="sourceTabClick(index)"
            >{{item.title}}
            </li>
        </ul>
        <ul class="list-cont selectn">
            <li class="list-cont-item" v-for="(item,index) in listTabs" :key="index" v-show="activeTab === index" >
                <SourcesPlug ref="plug" :sourceData="item.plugs" v-if="activeTab === index"/>
            </li>
        </ul>
    </div>
</template>

<script>
    import SourcesPlug from './SourcesPlug'

    export default {
        name: "LocalData",
        components: {
            SourcesPlug
        },
        props: {
            general: Boolean
        },
        data() {
            return {
                loading:true,
                filterText: '',
                listTabs: [
                    {
                        title: '人员',
                        plugs: []
                    }, {
                        title: '车辆',
                        plugs: []
                    }, {
                        title: '案件',
                        plugs: []
                    }, {
                        title: '金融',
                        plugs: []
                    }
                ],
                activeTab: 0,
                carPlug : [],
                personPlug : [],
                casePlug : [],
                financePlug : []
            }
        },
        methods: {
            sourceTabClick(index) {
                this.activeTab = index;
            },
            async getData(){
                const  _this = this;
                await this.$axios.get("/dataModeling/localDataObj?condition=" + this.filterText)
                    .then(res => {
                        // console.log("自定义数据集：",res);
                        if (res.data.status === 0) {
                            _this.setListData(res.data.data["personer"], _this.listTabs[0] , 0);
                            _this.setListData(res.data.data["carer"], _this.listTabs[1] , 1);
                            _this.setListData(res.data.data["case"], _this.listTabs[2] , 2);
                            _this.setListData(res.data.data["finance"], _this.listTabs[3] , 3);
                        }
                    }).catch(err => {
                        console.error(err);
                    });
            },
            async initTabs() {
                const  _this = this;
                await this.getData();
                _this.$refs.plug[0].setDefault(_this.listTabs[0].plugs);
            },
            setListData(data, list,index) {
                if(data === undefined){
                    return;
                }
                // console.log("设置数据：",data,list);
                list.plugs = [];
                data.forEach(item => {
                    let dbKeyWord = '';
                    let general = false;
                    switch (item['dbtype']) {
                        case 'RdbDataObj':
                            dbKeyWord = 'standardSqlInput';
                            break;
                        case 'HbaseHtable':
                            dbKeyWord = 'kVInput';
                            general = true;
                            break;
                        case 'ElasticsearchCollection':
                            dbKeyWord = 'fullTextInput';
                            break;
                    }
                    let huabanIcon = "";

                    // 案件 #sheanrenyuan_huaban
                    // 人员 #renyuan_huaban
                    // 车辆 #cheliang_huaban
                    //金融  #iconjinrong_huaban
                    switch (index) {
                        case 0 :
                            huabanIcon = '#iconrenyuan_huaban';
                            break;
                        case 1:
                            huabanIcon = '#iconcheliang_huaban';
                            break;
                        case 2:
                            huabanIcon = '#iconsheanrenyuan_huaban';
                            break;
                        default:
                            huabanIcon = '#iconjinrong_huaban';
                            break;

                    }

                    let data = {
                        label: item.name ? item.name : "",
                        type: "DS",
                        keyWord: dbKeyWord,
                        classifierId: item.id,
                        general:general,
                        dbType:item['dbtype'],
                        icon:huabanIcon,
                        customDataSet:true
                    };
                    list.plugs.push(data);
                });
                const _this = this;
                switch ( index ) {
                    case 0://人员
                        _this.personPlug = list.plugs;
                        break;
                    case 1://车辆
                        _this.carPlug = list.plugs;
                        break;
                    case 2://案件
                        _this.casePlug = list.plugs;
                        break;
                    default://金融
                        _this.financePlug = list.plugs;
                }
                // console.log(this.listTabs);
            },
            queryTable() {
                const _this = this;
                this.listTabs.forEach((tab , i)=>{
                    let plugs = [] ;
                    switch ( i ) {
                        case 0://人员
                            plugs = _this.personPlug;
                            break;
                        case 1://车辆
                            plugs = _this.carPlug;
                            break;
                        case 2://案件
                            plugs = _this.casePlug;
                            break;
                        default:
                            plugs = _this.financePlug;
                    }
                    if(_this.filterText){
                        tab.plugs = plugs.filter(plug =>{
                            return plug.label.indexOf(_this.filterText) > -1;
                        });
                    }else {
                        tab.plugs = plugs;
                    }

                    _this.$refs.plug[0].setDefault(_this.listTabs[_this.activeTab].plugs);
                })
                // this.initTabs();
            },
            dataFilter(plug){

            }
        },
        created() {
            this.initTabs();
        }
    }
</script>

<style scoped>
    .localData {
        height: 100%;
        position: relative;
    }

    .list-tab {
        padding: 6px 0;
        overflow: hidden;
    }

    .list-item {
        font-size: 14px;
        float: left;
        margin: 0 6px;
        color: #666;
        cursor: pointer;
    }

    .list-item:hover, .list-item.active {
        color: #289bf1;
        border-bottom: 1px solid #289bf1;
    }

    .list-cont {
        height: calc(100% - 60px);
    }

    .list-cont-item {
        overflow: hidden;
        height: 100%;
    }
</style>