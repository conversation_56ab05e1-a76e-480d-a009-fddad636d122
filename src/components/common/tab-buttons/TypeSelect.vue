<template>
    <el-radio-group v-model="radioValue" @change="changRadio" size="mini">
        <el-radio-button
            v-for="(item,i) in radioList" :key="i" :label="item.value"
        >{{item.label}}</el-radio-button>
    </el-radio-group>
</template>

<script>
    export default {
        name: "TypeSelect",
        props:{
            radioList:Array,
        },
        data(){
            return {
                radioValue:this.radioList[0].value,
            }
        },
        methods:{
            changRadio(value){
                this.$emit("changeRadio",value);
            },
            setValue(val){
                this.radioValue = val;
            }
        }
    }
</script>

<style scoped>

</style>