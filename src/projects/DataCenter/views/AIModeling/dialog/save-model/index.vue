<template>
    <div>
        <el-form :model="form" label-width="100px" label-position="right">
            <el-form-item :label="formList.name.label" prop="transName"
                        :rules="[{ required: true, message: '请输入模型名称', trigger: ['blur','change'] }]">
                <div class="content__name">
                    <el-input v-model.trim="form.transName" maxlength="50" v-input-limit:trim
                            @input="inputFilterSpecial($event , 'form' ,'transName')" :disabled="isLook"
                            :placeholder="formList.name.placeholder"></el-input>
                    <p>{{ formList.name.tip }}</p>
                </div>
            </el-form-item>
            <el-form-item :label="formList.pos.label" required>
                <div class="content__wrap">
                    <!--目录树-->
                    <DataTreeVue class="content__tree" ref="tree" v-if="openDialog" @nodeClick="treeDir"
                                :dirId="classifyId" hasSave :isLook="isLook"/>
                </div>
            </el-form-item>
            <el-form-item :label="formList.description.label">
                <el-input v-model="form.description" type="textarea"
                        :placeholder="formList.description.placeholder"
                        maxlength="255"
                        rows="3"
                        resize="none"
                          :disabled="isLook"
                        show-word-limit></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import {globalBus} from "@/api/globalBus";
import DataTreeVue from "@/projects/DataCenter/views/AIModeling/dialog/ModelTree";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "@/projects/DataCenter/views/AIModeling/service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";

export default {
    name: "saveModel",
    mixins: [commonMixins, servicesMixins, listMixins],
    components: {DataTreeVue},
    props :{
        saveInfo : Object,
        isLook : {
            type : Boolean,
            default : false,
        }
    },
    data() {
        return {
            visible: false,
            width: '800px',
            active: true,
            classifyId: "",
            form: {
                instance: "",
                mode: "",
                transName: "",
                processMode: "",
                description: ""
            },
            formList: {
                name: {
                    label: '模型名称 :',
                    placeholder: '请输入模型名称',
                    tip: "名称最大不超过50字符，只允许除/、\\、<、>等特殊字符以外。"
                },
                pos: {
                    label: "保存到目录 :"
                },
                description: {
                    label: "模型描述 :",
                    placeholder: "请输入对模型进行描述的一段说明"
                }
            },
            optSolveModeData: [
                {
                    value: "",
                    label: ""
                }
            ],
            rowData: {},
            openDialog: false,
            scriptId :"",
        }
    },
    methods: {
        treeDir(label, value) {
            this.classifyId = value;
        },
        /**
         *  @param rowData
         * */
        show(rowData) {
            this.rowData = rowData || {};
            if(rowData.modelName) this.form.transName = rowData.modelName;
            this.form.description = rowData ? rowData.description : "";
            this.scriptId = rowData ? rowData.transId : "";
            this.visible = true;
            this.openDialog = true;
            if (rowData.dirId && rowData.dirId !== "-1") {
                this.classifyId = rowData.dirId;
            } else if (rowData.dirParentId) {
                this.classifyId = rowData.dirParentId;
            }
        },
        save() {
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            if (vm.classifyId === "") {
                vm.$message.warning("请选择方案所在目录！")
            } else if (vm.form.transName === "") {
                vm.$message.warning("请输入模型名称！")
            } else if (vm.form.instance === "") {
                vm.$message.warning("请选择方案运行实例！")
            } else {
                // String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId
                settings.loading = true;
            }
        },//transSchedule
        stop() {
            this.visible = false;
        },
    },
    created(){
        
    }
}
</script>

<style scoped lang="less">
.content {
    &__name {
        p {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 21px;
            margin-top: 4px;
        }
    }

    &__tree.dataTree {
        float: none;
        margin: 0;
        border: none;
        height: 100%;
        width: 100%;
        padding: 0;
    }

    &__wrap {
        padding: 14px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        box-sizing: border-box;
        height: 250px;
    }
}
</style>
