import {chartMxins} from "../chart-mixins/chart-mixins";
import {globalBus} from "@/api/globalBus";

export default {
    name: "formCard",
    mixins: [chartMxins],
    data() {
        return {
            formData:{},
            columnWidth:"",
            titleStyle: {},
            height:"",
            showOneColumn:true,
            isEll:false,
            dataLength:0,
            showKey:true,
        }
    },
    computed: {
        tabH() {
            return this.tabHeight ? this.tabHeight : 40;
        }
    },
    methods: {
        preview() {
            const vm = this;
            if (!vm.node.widget) {
                return;
            }
            let {beforeData, widgetDataset} = JSON.parse(vm.node.widget),
                nodeData = beforeData ? JSON.parse(beforeData) : {};
            if ( nodeData.yAxis && nodeData.yAxis.length === 0) {
                return;
            }
            if (!widgetDataset) {
                return;
            }
            if (!widgetDataset.tableCode) {
                return;
            }
            if (!widgetDataset.widgetDatasetMeasures && widgetDataset.widgetDatasetMeasures.length === 0) {
                return;
            }
            vm.setHistogramPanel();
        },
        getNodeData() {
            globalBus.$on('setFormPanel', this.setHistogramPanel);
        },
    
        setChartExtend(settings) {
            this.setTitleTxt(settings.title);
            this.setStyle(settings.legend);
            this.setData(settings.legend);
        },
        setStyle(legend) {
            const vm = this;
            vm.height = legend.line_height === "auto" ? false : legend.customH;
            vm.columnWidth = legend.column_width;
            vm.showKey = legend.showKey !== undefined ? legend.showKey : true;
        },
        setData(legend){
            let widget = JSON.parse(this.node.widget), nodeData = JSON.parse(widget.beforeData);
            let isLinkage = !widget.linkageFilter;
            this.formData = [];
            // this.dataLength = 0;
            // for (var key in this.chartData.data[0]) this.dataLength++;
            if (isLinkage) {
                let newFormObject = {};
                for (var key in this.chartData.data[0]) {  
                    newFormObject[key] = "";
                }
                this.formData.push(newFormObject);
            }else {
                this.formData = this.chartData.data;
            }
            this.showOneColumn = legend.show_style === "oneColumn";
            this.isEll = legend.line_height !== "auto";
        },
        getMessage(msg) {
            const vm = this;
            let result = JSON.parse(msg.data), data = result.data;
            if (vm.socket) vm.socket.close();
            if (result.status === 0 && data.data.length !== 0) {
                vm.chartData = data;
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                vm.setChartExtend(nodeData.style);
                vm.data_empty = false; //没有数据为true
                vm.loading = false;
            } else {
                vm.data_empty = true; //没有数据为true
                vm.loading = false;
                vm.chartData = {};
            }
        },
    }
}
