<template>
    <div class="l-sidebar__container">
        <div class="l-sidebar__header">
            <i class="el-icon-s-fold" :class="[isCollapse ? 'is-active' : '']" @click="toggleSideBar"></i>
        </div>
        <div class="l-sidebar__content">
            <el-scrollbar wrap-class="l-sidebar__scrollbar">
                <div class="u-menu" >
                    <el-menu
                        class="u-menu__container"
                        :show-timeout="200"
                        :default-active="$route.meta.belong || $route.path"
                        :collapse="isCollapse"
                        :collapse-transition="false"
                        mode="vertical"
                    >
                        <sidebar-item
                            class="u-menu__list"
                            v-for="route in routers"
                            :key="route.path"
                            :item="route"
                            :base-path="route.path"
                        />
                    </el-menu>
                </div>
            </el-scrollbar>
        </div>
        <div class="l-sidebar__container-toggle" @click=handleToggleMenu>
            <i :class="{ 'el-icon-s-unfold': fold === 0, 'el-icon-s-fold': fold === 1 }"></i>
        </div>
    </div>
</template>

<script>
import SidebarItem from "./sidebar-item";
import { mapGetters } from "vuex";
import * as $ from "jquery"
export default {
    props : {
        routers : {
            type : Array
        }
    },
    components: { SidebarItem },
    data(){
      return{
          fold: 1,
      }
    },
    computed: {
        ...mapGetters(["sidebar"]),
        isCollapse() {
            return !this.sidebar.opened;
        }
    },
    methods: {
        toggleSideBar() {
            this.$store.dispatch("toggleSideBar");
        },
        /**
         *
         * 切换菜单
         *
         * */
        handleToggleMenu(){
            $(".l-sidebar__header i").click();
            if(this.fold  === 0){
                this.fold = 1
            }else{
                this.fold = 0
            }
        }
    },
    created() {
        this.fold = this.sidebar.opened ? 1 : 0;
    }
};
</script>
<style scoped>
    .l-sidebar__container {
        z-index: auto;
    }
</style>
<style>
    .l-sidebar__scrollbar.el-scrollbar__wrap {
        width: calc(100% + 8px);
        height: calc(100% + 8px);
    }
</style>
