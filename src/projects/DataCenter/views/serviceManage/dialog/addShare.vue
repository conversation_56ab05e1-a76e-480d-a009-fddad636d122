<template>
    <div class="AddShare" v-loading="settings.loading">
        <el-form :model="form" label-position="right" label-width="120px">
            <el-form-item :label="targetUser.label+':'" required>
                <div class="AddShare_dbselect">
                    <dg-select class="AddShare_sel" v-model="targetType" :data="targetOpt" @change="targetChange"></dg-select>
                    <ce-select-drop
                            :title="form.targetUser"
                            class="AddShare_drop"
                            ref="tree"
                            :props="defaultProps"
                            v-model="form.targetUser"
                            :data="userData"
                            :tree-props="treeBind"
                            :filterNodeMethod="filterNode"
                            :placeholder="'选择分享'+targetLabel"
                            clearable
                            filterable
                            multiple
                            visible-type="leaf"
                    >
                    </ce-select-drop>
                </div>

            </el-form-item>
            <el-form-item :label="sourceType.label+':'" required v-if="!isVisualizing">
                <dg-select v-model="form.sourceType" :data="$hideAI ? sourceOpt : [...sourceOpt, {label : 'AI算法', value : 'aiService'}]" @change="sourceTypeChange"></dg-select>
                <!--<dg-radio-group type="button" v-model="form.sourceType" :call-off="true" :data="$hideAI ? sourceOpt : [...sourceOpt, {label : 'AI算法', value : 'aiService'}]" @change="sourceTypeChange"></dg-radio-group>-->
            </el-form-item>
            <el-form-item :label="chooseSource.label+':'" required>
                <el-select multiple filterable v-model="dataServiceSourceValue">
                    <el-option
                        v-for="item in dataServiceOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import {roleData} from "@/projects/DataCenter/views/dataSources/dialog/mixins/roleData";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {treeMethods} from "@/api/treeNameCheck/treeName";
import {servicesMixins} from "@/projects/DataCenter/views/home/<USER>/service-mixins";

export default {
    name: "AddShare",
    mixins: [roleData ,commonMixins, servicesMixins],
    props : {
        isVisualizing : {
            type : Boolean,
            default : false,
        },
    },
    computed : {
        targetLabel(){
            return this.targetOpt.find(ta => ta.value === this.targetType).label;
        }
    },
    data(){
        return {
            form : {
                targetUser : "",
                sourceType:"dataService" ,
                chooseSource:""
            },
            targetUser : {
                label : "被分享的对象"
            },
            sourceType : {label : "选择资源类型"},
            sourceOpt : [
                {label : "数据查询" , value : "dataService"},
                {label : "比对订阅", value : "compareService"},
                {label : "信息核查", value : "informationVerification"},
                {label : "数据碰撞", value : "dataCollision"},
                {label : "模型分析", value : "modelService"}
            ],
            chooseSource : {label : "选择分享资源"},
            targetType : "user" ,
            targetOpt :[
                {label : "用户" ,value : "user"},
                {label : "角色" ,value : "role"},
            ],
            sourceData : [],
            sourceTreeProps : {
                value: 'id',
                label: 'label',
                children: 'children'
            },
            dataServiceOptions : [],
            dataServiceSourceValue : null,
        }
    },
    methods : {
        filterNode: treeMethods.methods.filterNode,
        filterSourceNode(...arg){
            return treeMethods.methods.filterNode(...arg, this.sourceTreeProps)
        },
        targetChange(val){
            this.form.targetUser = "";
            this.getAuthorizer(val);
        },
        sourceTypeChange(value){
            this.dataServiceSourceValue = null;
            this.dataServiceOptions = [];
            if (value === 'dataSet') {
                this.getSourceDataSetTree();
            }else if(value === 'dataService'){
                this.getSourceDataServiceTree(value);
            }else if(['modelService','aiService','dataCollision','compareService','informationVerification'].includes(value)){
                this.getModelServiceTree(value);
            }
        },
        /**
         * 数据集 - 分享资源
         */
        getSourceDataSetTree(){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            services.getSourceDataSetTree(settings).then(res=>{
                let result = vm.setSourceNode(res.data.data);
                this.getSourceData(result);
            })
        },
        /**
         * 流程建模服务、AI建模服务分享资源
         */
        getModelServiceTree(serviceType){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            vm.dataServiceOptions = [];
            services.getMyModelService(serviceType, settings).then(res=>{
                res.data.data.forEach(element => {
                    vm.dataServiceOptions.push({
                        value: element.id,
                        label : element.name
                    })
                });
            })
        },
        /**
         * 流程建模api服务获取分享资源
         */
        getSourceDataModeling(id){
            const vm = this, {settings} = this;
            vm.form.sourceType = "modelService";
            settings.loading = true;
            let services = vm.$services('modeling');
            vm.dataServiceOptions = [];
            services.getResourceByTran(id, settings).then(res=>{
                if(res.data.status === 0){
                    res.data.data.modelService.forEach(element => {
                        vm.dataServiceOptions.push({
                            value: element.id,
                            label : element.name
                        })
                    });
                }

            })
        },
        setSourceNode(nodes) { //
            const vm = this;
            if (nodes !== null) {
                return nodes.filter(item => {
                    if(item.children && item.children.length !== 0){
                        item.children = vm.setSourceNode(item.children);
                    }
                    return  item.belongType || item.children && item.children.length !== 0 && item.code !== '来自分享';
                })
            }
        },
        /**
         * 根据返回的值获取树的数据
         */
        getSourceData(res){
            const vm = this;
            vm.sourceData = res.map(item => {
                for (let index = 0; index < item.children.length; index++) {
                    const element = item.children[index];
                    if(element.children.length === 0){
                        item.children.splice(index, 1);
                    }
                }
                return item;
            });
        },
        /**
         * 数据服务 - 分享资源
         */
        getSourceDataServiceTree(value){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let params = {
                "resourceName":"",
                "resourceType":value,
                "dataSetTreeId":""
            };
            let services = vm.getServices(homeServices ,homeMock);
            services.getSourceDataServiceTree(params, settings).then(res=>{
                res.data.data && res.data.data.forEach(element => {
                    vm.dataServiceOptions.push({
                        value: element.resourceId,
                        label : element.resourceName
                    })
                });
            })
        },
        /**
         * 获取仪表盘分享资源
         */
        getVisualizingSourceData(){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            services.getDashboardAllByUserId( settings).then(res=>{
                res.data.data.forEach(element => {
                    vm.dataServiceOptions.push({
                        value: element.id,
                        label : element.name
                    })
                });
            })
        },
        /**
         * 添加分享
         */
        save(index){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            let chooseSourceInfo = [];
            if (this.form.sourceType === 'dataSet') {
                let object = this.$refs.dataSetTree.getCheckedNodes(true);
                object.forEach(element => {
                    chooseSourceInfo.push(element.id);
                });
            } 
            let params = {
                "userType": this.targetType === 'user' ? "0" : "2",
                "sharedUserIds":this.form.targetUser.split(","),
                "resourceType": this.form.sourceType,
                "resourceIds": this.form.sourceType === 'dataSet' ? chooseSourceInfo : this.dataServiceSourceValue,
            };
            let isNull = this.form.sourceType === 'dataSet' ? this.form.targetUser.length === 0 || this.form.chooseSource.length === 0 
                        : this.form.targetUser.length === 0 || this.dataServiceSourceValue.length === 0;
            if (isNull) {
                this.$message.warning("请填写完整信息");
                return;
            };
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock), flag = false;
            services.addShare(params, settings).then(res=>{
                if (res.data.status === 0) {
                    this.$message.success("分享成功");
                    this.$emit('refresh');
                    layer.close(layer.index);
                }
            })
        },
        setVisualizingSaveVo(){
            let chooseSourceInfo = [];
            this.dataServiceSourceValue.forEach(element => {
                chooseSourceInfo.push({
                    code : element,
                    name : element
                });
            });
            let roleVos = [], userVos = [];
            this.form.targetUser.split(",").forEach(element => {
                roleVos.push({
                    roleId : element,
                })
            });
            this.form.targetUser.split(",").forEach(element => {
                userVos.push({
                    id : element,
                })
            });
            let params = {
                dashboardVos : chooseSourceInfo,
                roleVos : this.targetType === 'user' ? [] : roleVos,
                userVos : this.targetType === 'user' ? userVos : [],
            };
            return params;
        },
        visualizingSave(index){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;

            let isNull = this.form.targetUser.length === 0 || this.dataServiceSourceValue.length === 0;
            if (isNull) {
                this.$message.warning("请填写完整信息");
                return;
            };
            let params = this.setVisualizingSaveVo();
            let services = vm.getServices(homeServices ,homeMock), flag = false;
            settings.loading = true;
            services.dashboardAuthRegister(params, settings).then(res=>{
                if (res.data.status === 0) {
                    let params = vm.setVisualizingSaveVo();
                    services.addDashboardAuth(params, settings).then(res=>{
                        if (res.data.status === 0) {
                            this.$message.success("分享成功");
                            this.$emit('refresh');
                            layer.close(layer.index);
                        }
                    })
                    
                }
            })
        }
    },
    created() {
        this.getAuthorizer('user');
        if (!this.isVisualizing) {
            this.getSourceDataServiceTree('dataService');
        } else {
            this.getVisualizingSourceData();
        }
    }
}
</script>

<style scoped lang="less">
    .AddShare{
        padding: 14px 0;
        &_sel {
            width: 120px;
            /deep/.el-input--suffix {
                &.is-focus {
                    z-index: 1;
                }
                .el-input__inner {
                    border-radius: 2px 0 0 2px;
                }
            }

        }
        &_dbselect {
            display: flex;
        }
        &_drop {
            flex: 1;
            margin-left: -1px;
        }
    }
</style>
