<!--
Excel 导出
-->
<script>
export default {
    name: "download-excel",
    props: {
        downloadFile: {
            type: Function,
            default:()=> ()=>{}
        },
        limitDownload: {
            type: Number,
            default: 20
        }
    },
    data(){
        return {
            EXCEL: {
                excelname: '',
                downloadSize: 20,
            },
            rules: {
                excelname: [{required: true, message: '请输入导出的文件名', trigger: ['blur','change']}]
            },
            loading:false
        }
    },
    methods: {
        limitInput(value) {
            const vm = this;
            let val = Number(value);
            if (val > vm.limitDownload) {
                vm.EXCEL.downloadSize = '';
                vm.$message.warning(`条数最大不超过${vm.limitDownload}条`);
            } else if (val === 0) {
                vm.EXCEL.downloadSize = '';
                vm.$message.warning(`下载条数不能为0`);
            } else {
                vm.EXCEL.downloadSize = val;
            }
        },
        async download(){
            await this.$nextTick();
            if(this.loading) return;
            this.loading = true;
            this.downloadFile(this.EXCEL).then((res)=> {
                if(res === true) this.$emit('close');
                this.loading = false;
            })
        }
    },
    created(){
        this.EXCEL.downloadSize = this.limitDownload;
    }
}
</script>

<template>
    <el-form :model="EXCEL" :rules="rules" label-width="120px" ref="excelDownload">
        <el-form-item label="导出文件名:" prop="excelname">
            <el-input v-model.trim="EXCEL.excelname" placeholder="请输入文件名(限 30个字符)"
                      :maxlength="30" style="width: 200px;"></el-input>
        </el-form-item>
        <el-form-item label="输出文件格式:">
            <el-input placeholder="EXCEl文件" :disabled="true" style="width: 200px;">
            </el-input>
        </el-form-item>
        <el-form-item label="条数:">
            <el-input min="1" :max="limitDownload" style="width: 200px;" step="1"
                      v-model.trim="EXCEL.downloadSize" type="number" v-input-limit:number
                      @input="limitInput($event)"></el-input>
        </el-form-item>
        <div v-footer class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="download" :disabled="loading">确 定</el-button>
        </div>
    </el-form>

</template>

<style scoped lang="less">

</style>
