<template>
    <el-form ref="form" :hide-required-asterisk="canEdit" class="editPage" label-width="120px" :model="form" :rules="rules">
        <el-form-item v-for="(col , key) in list_form" :key="key" :prop="key" :label="key === 'name' ? columnName + '显示名' : col.label" >
            <el-input v-model.trim="form[key]" :maxlength="col.maxlength" v-input-filter="form[key]" :placeholder="col.placeholder" :disabled="col.disabled ? true : canEdit"></el-input>
            <p v-if="col.tip">{{col.tip}}</p>
        </el-form-item>
    </el-form>
</template>

<script src="./edit-page.js"></script>
<style scoped lang="less" src="./edit-page.less"></style>