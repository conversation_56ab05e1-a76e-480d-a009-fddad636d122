<template>
    <div class="list" v-loading="settings.loading">
        <div class="d-s-r-top">
            <dg-button type="primary" v-if="ifHasRight(createR) && !isOtherUser"
                       @click="APICreate('add')">{{ createAPI }}
            </dg-button>
            <dg-button v-if="ifHasRight(batchShareR)" @click="batchShare">{{
                    buttonTxt
                }}
            </dg-button>
            <dg-button @click="linkToShare">{{ myShareTxt }}</dg-button>
            <div class="list-top-gap"></div>
            <dg-select v-model="activeN" :disabled="loading" :data="tabData" @change="tabChange"
                       class="mr10"></dg-select>
            <dg-select v-model="statusQuery" :disabled="loading" :data="statusData" class="mr10" @change="changePage(1)"></dg-select>
            <el-select v-model="apiName" placeholder="" class="w100" @change="changePage(1)">
                <el-option
                    v-for="item in apiOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
            <div class="ce-table__search">
                <el-input
                    :disabled="loading"
                    size="mini"
                    class="ml10"
                    placeholder="请输入名称搜索"
                    v-model.trim="inputValueTable"
                    v-input-limit:trim
                    @input="inputFilterSpecial($event , 'inputValueTable')"
                    @keyup.enter.native="searchTableEvent"
                >
                    <i
                        class="el-icon-search el-input__icon poi"
                        slot="suffix"
                        @click="searchTableEvent">
                    </i>
                </el-input>
            </div>
        </div>
        <div class="d-s-r-table">
            <common-table
                :data="tableData"
                :columns="tHeadData"
                :paging-type="isMock ? 'client' : 'server'"
                :pagination-props="paginationProps"
                class="width100"
                :max-height="tableBodyH"
                :border="false"
                noneImg
                :pagination-total="total"
                @change-current="isMock ? ()=>{} : changePage($event) "
                @change-size="changeSize"
                @selection-change="handleSelectionChange"
            >
                <template slot="operate" slot-scope="{row , $index}" v-if="!isOtherUser">
                    <span class="r-c-action"
                          v-for="(item,index) in operateIconShow(row).slice(0 , 3)"
                          :key="index"
                    >
                        <el-button type="text"
                                   :class="item.class"
                                   @click="item.clickFn( row , $index)"
                                   :title="item.name"
                        >{{ item.name }}</el-button> <!--v-html="item.icon"-->
                    </span>
                    <dir-edit-action v-if="operateIconShow(row).length > 3"
                                     placement="bottom"
                                     @command="menuCommand($event , row , $index)" :data="operateIconShow(row).slice(3)"
                                     :node="row"
                                     :rights="rights">
                        <span slot="reference" class="el-dropdown-link">
                            <span>{{ moreTxt }}</span>
                            <i class="el-icon-caret-bottom el-icon right"></i>
                        </span>
                    </dir-edit-action>
                </template>
                <template slot="interfaceChineseName" slot-scope="{row , $index}">
                    <div class="detailInfo" v-if="ifHasRight(lookOverR)" @click="detailInfo(row, $index)">
                        {{ row.interfaceChineseName }}
                    </div>
                    <span v-else>{{row.interfaceChineseName}}</span>
                </template>
            </common-table>
        </div>
        <detailDialog ref="detailDialog"></detailDialog>
        <createAPI ref="createAPI" @changePage="changePage" @showSuccess="showSuccess" :type="addOrEdit" @clear="clear"
                   :isServiceManageUpdate="isServiceManageUpdate" :modelInfo="modelInfo"
                   :isServiceManage="isServiceManage" :entrance="'service'"></createAPI>
        <testDialog ref="testDialog"></testDialog>
        <AuthDialog ref="auth" @success="reNewTable"/>
        <!--创建API成功弹窗-->
        <successDialog
            ref="successDialog"
            @share="share"
            @test="test"
            @detailInfo="detailInfo">
        </successDialog>
        <subscribeDialog ref="subscribeDialog"></subscribeDialog>
        <look-version ref="lookVersion"></look-version>
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>
