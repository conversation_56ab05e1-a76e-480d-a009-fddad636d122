<!--业务空间-->
<template>
    <ce-tab-page
        v-model="activeName"
        :tabPanes="tabPanes"
        @tabClick="tabClick"
    >
        <component ref="page" slot="page" slot-scope="{classN}" :class="classN" :is="activeName"></component>
    </ce-tab-page>
</template>

<script>
import CeTabPage from "@/components/common/ce-tab-page/CeTabPage";
import overview from "@/projects/DataCenter/views/business-space/overview/overview.vue";
import moduleManage from "@/projects/DataCenter/views/business-space/moduleManage/moduleManage.vue";
export default {
    name: "index",
    components : {
        CeTabPage ,
        overview ,
        moduleManage
    },
    data(){
        return {
            activeName: "overview",
            tabPanes: [
                {label: "概览", value: "overview"},
                {label: "模板管理", value: "moduleManage"},
            ],
        }
    },
    methods : {
        tabClick(){

        }
    }
}
</script>

<style scoped lang="less"></style>
