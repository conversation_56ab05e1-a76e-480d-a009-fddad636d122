<template>
    <div class="AddShare" v-loading="settings.loading">
        <el-form :model="form" label-position="right" label-width="120px">
            <el-form-item :label="targetUser.label+':'" required>
                <div class="AddShare_dbselect">
                    <dg-select class="AddShare_sel" v-model="targetType" :data="targetOpt" @change="targetChange"></dg-select>
                    <ce-select-drop
                            :title="form.targetUser"
                            class="AddShare_drop"
                            ref="tree"
                            :props="defaultProps"
                            v-model="form.targetUser"
                            :data="userData"
                            :tree-props="treeBind"
                            :filterNodeMethod="filterNode"
                            :placeholder="'选择分享'+targetLabel"
                            clearable
                            filterable
                            multiple
                            visible-type="leaf"
                    >
                    </ce-select-drop>
                </div>

            </el-form-item>
            <el-form-item :label="sourceType.label+':'" required v-if="!tranId">
                <dg-select  v-model="form.sourceType" 
                            group-name="tag"
                            group
                            :data="$hideAI ? sourceOpt : [...sourceOpt, {label : 'AI算法', value : 'aiService', tag: '服务'}]" 
                            @change="sourceTypeChange"></dg-select>
                <!--<dg-radio-group type="button" v-model="form.sourceType" :data="$hideAI ? sourceOpt : [...sourceOpt,{label : 'AI算法', value : 'aiService'}] " :call-off="true" @change="sourceTypeChange"></dg-radio-group>-->
            </el-form-item>
            <el-form-item :label="sourceType.label+':'" required v-else>
                <dg-select  v-model="form.sourceType"
                            group-name="tag"
                            group
                            :data="sourceOptModel"
                            @change="sourceTypeChangeModel"></dg-select>
            </el-form-item>
            <el-form-item :label="chooseSource.label+':'" required>
                <ce-select-drop
                        v-show="form.sourceType === 'dataSet'"
                        :title="form.chooseSource"
                        class=""
                        :loading="settings.loading"
                        ref="dataSetTree"
                        :props="sourceTreeProps"
                        v-model="form.chooseSource"
                        :data="sourceData"
                        :tree-props="treeBind"
                        :filterNodeMethod="filterSourceNode"
                        placeholder="选择分享资源"
                        clearable
                        filterable
                        multiple
                        visible-type="leaf"
                >
                </ce-select-drop>
                <el-select multiple v-model="dataServiceSourceValue" v-show="['dataService','visualizing','modelService','aiService','dataCollision','compareService','informationVerification'].includes(form.sourceType)" filterable >
                    <el-option
                        v-for="item in dataServiceOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import {roleData} from "@/projects/DataCenter/views/dataSources/dialog/mixins/roleData";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {treeMethods} from "@/api/treeNameCheck/treeName";
import {servicesMixins} from "../../service-mixins/service-mixins";

export default {
    name: "AddShare",
    mixins: [roleData ,commonMixins, servicesMixins],
    props: {
        tranId: String,
        activeN : String
    },
    computed : {
        targetLabel(){
            return this.targetOpt.find(ta => ta.value === this.targetType).label;
        }
    },
    data(){
        return {
            form : {
                targetUser : "",
                sourceType:"dataSet" ,
                chooseSource:""
            },
            targetUser : {
                label : "被分享的对象"
            },
            sourceType : {label : "选择资源类型"},
            sourceOpt : [
                {label : "数据集" , value : "dataSet", tag: '数据集'},
                {label : "数据查询" , value : "dataService", tag: '服务'},
                {label : "仪表盘", value : "visualizing", tag: '仪表盘'},
                {label : "模型分析", value : "modelService", tag: '服务'},
                {label : "比对订阅", value : "compareService", tag: '服务'},
                {label : "信息核查", value : "informationVerification", tag: '服务'},
                {label : "数据碰撞", value : "dataCollision", tag: '服务'},
            ],
            sourceOptModel:[
                {label : "数据集" , value : "dataSet", tag: '数据集'},
                // {label : "数据查询" , value : "dataService", tag: '服务'},
                {label : "模型分析", value : "modelService", tag: '服务'},
                {label : "比对订阅", value : "compareService", tag: '服务'},
                {label : "信息核查", value : "informationVerification", tag: '服务'},
                {label : "数据碰撞", value : "dataCollision", tag: '服务'},
            ],
            chooseSource : {label : "选择分享资源"},
            targetType : "role" ,
            targetOpt :[
                {label : "用户" ,value : "user"},
                {label : "角色" ,value : "role"},
            ],
            sourceData : [],
            sourceTreeProps : {
                value: 'id',
                label: 'label',
                children: 'children'
            },
            dataServiceOptions : [],
            dataServiceSourceValue : null,
            allDataSourcesList:{}//建模api详情分析资源信息
        }
    },
    methods : {
        filterNode: treeMethods.methods.filterNode,
        filterSourceNode(...arg){
            return treeMethods.methods.filterNode(...arg, this.sourceTreeProps)
        },
        targetChange(val){
            this.form.targetUser = "";
            this.getAuthorizer(val);
        },
        sourceTypeChangeModel(value){
            this.dataServiceSourceValue = null;
            this.form.chooseSource = null;
            this.form.sourceType = value;
            if(value === 'modelService'){
                this.dataServiceOptions = this.allDataSourcesList.modelService;
            }
            else if(value === 'dataSet'){
                this.$refs.dataSetTree.setCheckedKeys([]);
                this.sourceData = this.allDataSourcesList.dataSet;
            }
            else if(value === 'dataService'){
                this.dataServiceOptions = this.allDataSourcesList.dataService;
            }
            else if(value === 'dataCollision'){
                this.dataServiceOptions = this.allDataSourcesList.dataCollision;
            }
            else if(value === 'compareService'){
                this.dataServiceOptions = this.allDataSourcesList.compareService;
            }
            else if(value === 'informationVerification'){
                this.dataServiceOptions = this.allDataSourcesList.informationVerification;
            }
        },
        sourceTypeChange(value){
            this.dataServiceSourceValue = null;
            this.form.chooseSource = null;
            if (value === 'dataSet') {
                this.$refs.dataSetTree.setCheckedKeys([]);
                this.getSourceDataSetTree();
            } else if (value === 'visualizing' || value === 'dashboard'){
                this.getVisualizingSourceData();
            } else if(value === 'dataService'){
                this.getSourceDataServiceTree();
            }else if(['modelService','aiService','dataCollision','compareService','informationVerification'].includes(value)){
                this.getModelServiceTree(value);
            }
        },
        /**
         * 流程建模服务、AI建模服务分享资源
         */
        getModelServiceTree(serviceType){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            vm.dataServiceOptions = [];
            services.getMyModelService(serviceType, settings).then(res=>{
                res.data.data.forEach(element => {
                    vm.dataServiceOptions.push({
                        value: element.id,
                        label : element.name
                    })
                });
            })
        },
       /**
        * 流程建模api服务获取分享资源
        */
        getSourceDataModeling(id,type){
           const vm = this, {settings} = this;
           vm.form.sourceType = "dataSet";
           settings.loading = true;
           let services = vm.$services('modeling');
           vm.dataServiceOptions = [];
           services.getResourceByTran(id, settings).then(res=>{
               if(res.data.status === 0){
                   res.data.data.dataSet.forEach(element => {
                       element.value = element.id;
                       element.label = element.name;
                   });
                   res.data.data.modelService.forEach(element => {
                       element.value = element.id;
                       element.label = element.name;
                   });
                   res.data.data.dataService.forEach(element => {
                       element.value = element.id;
                       element.label = element.name;
                   });

                   res.data.data.compareService.forEach(element => {
                       element.value = element.id;
                       element.label = element.name;
                   });
                   res.data.data.dataCollision.forEach(element => {
                       element.value = element.id;
                       element.label = element.name;
                   });
                   res.data.data.informationVerification.forEach(element => {
                       element.value = element.id;
                       element.label = element.name;
                   });
                   vm.allDataSourcesList = res.data.data;
                   vm.sourceTypeChangeModel(vm.form.sourceType)
               }
           })
        },
        /**
         * 仪表盘分享资源
         */
        getVisualizingSourceData(){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            vm.dataServiceOptions = [];
            services.getDashboardAllByUserId( settings).then(res=>{
                res.data.data.forEach(element => {
                    vm.dataServiceOptions.push({
                        value: element.id,
                        label : element.name
                    })
                });
            })
        },
        /**
         * 数据集 - 分享资源
         */
        getSourceDataSetTree(){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            services.getSourceDataSetTree(settings).then(res=>{
                let result = vm.setSourceNode(res.data.data);
                this.getSourceData(result);
            })
        },
        setSourceNode(nodes) { //
            const vm = this;
            if (nodes !== null) {
                return nodes.filter(item => {
                    if(item.children && item.children.length !== 0){
                        item.children = vm.setSourceNode(item.children);
                    }
                   /* if (item.name === '场景案例' && item.pId === null){
                        return  item.belongType || item.children && item.code !== '来自分享';
                    } else {*/
                    return  item.belongType || item.children && item.children.length !== 0 && item.code !== '来自分享';
                    //}

                    //return  item.belongType || item.children && item.children.length !== 0 && item.code !== '来自分享';
                })
            }
        },
        /**
         * 根据返回的值获取树的数据
         */
        getSourceData(res){
            const vm = this;
            vm.sourceData = res.map(item => {
                for (let index = 0; index < item.children.length; index++) {
                    const element = item.children[index];
                    if(element.children.length === 0){
                        item.children.splice(index, 1);
                    }
                }
                return item;
            });
        },
        /**
         * 数据服务 - 分享资源
         */
        getSourceDataServiceTree(){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let params = {
                "resourceName":"",
                "resourceType":"dataService",
                "dataSetTreeId":""
            };
            vm.dataServiceOptions = [];
            let services = vm.getServices(homeServices ,homeMock);
            services.getSourceDataServiceTree(params, settings).then(res=>{
                res.data.data.forEach(element => {
                    vm.dataServiceOptions.push({
                        value: element.resourceId,
                        label : element.resourceName
                    })
                });
            })
        },
        setDataSetParams(){
            let chooseSourceInfo = [], userInfoList = [];
            let object = this.$refs.dataSetTree.getCheckedNodes(true), userInfo = this.$refs.tree.getCheckedNodes(true);
            object.forEach(element => {
                chooseSourceInfo.push(element.id);
            });
            userInfo.forEach(element => {
                userInfoList.push(element.id);
            });
            let params = {
                "userType": this.targetType === 'user' ? "0" : "2",
                "sharedUserIds":userInfoList,
                "resourceType": 'dataSet',
                "resourceIds": chooseSourceInfo 
            };
            return params;
        },
        setVisualizingSaveVo(){
            let chooseSourceInfo = [];
            this.dataServiceSourceValue.forEach(element => {
                chooseSourceInfo.push({
                    code : element,
                    name : element
                });
            });
            let roleVos = [], userVos = [];
            this.form.targetUser.split(",").forEach(element => {
                roleVos.push({
                    roleId : element,
                })
            });
            this.form.targetUser.split(",").forEach(element => {
                userVos.push({
                    id : element,
                })
            });
            let params = {
                dashboardVos : chooseSourceInfo,
                roleVos : this.targetType === 'user' ? [] : roleVos,
                userVos : this.targetType === 'user' ? userVos : [],
            };
            return params;
        },
        visualizingSave(index){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;

            let isNull = this.form.targetUser.length === 0 || this.dataServiceSourceValue.length === 0;
            if (isNull) {
                this.$message.warning("请填写完整信息");
                return;
            }
            let params = this.setVisualizingSaveVo();
            let services = vm.getServices(homeServices ,homeMock);
            settings.loading = true;
            services.dashboardAuthRegister(params, settings).then(res=>{
                if (res.data.status === 0) {
                    let params = vm.setVisualizingSaveVo();
                    services.addDashboardAuth(params, settings).then(res=>{
                        if (res.data.status === 0) {
                            this.$message.success("分享成功");
                            this.$emit('refresh');
                        }
                    })
                    
                }
            })
        },
        /**
         * 添加分享
         */
        async save(index){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            if (this.form.sourceType === 'visualizing') {
                this.visualizingSave(index);
                return;
            }
            let params = this.setDataSetParams(), userInfo = this.$refs.tree.getCheckedNodes(true), userInfoList = [];
            userInfo.forEach(element => {
                userInfoList.push(element.id);
            });
            let serviceParams = {
                "userType": this.targetType === 'user' ? "0" : "2",
                "sharedUserIds":userInfoList,
                "resourceType":this.form.sourceType,
                "resourceIds": this.dataServiceSourceValue,
            }
            let isNull = this.form.sourceType === 'dataSet' ? this.form.targetUser.length === 0 || this.form.chooseSource.length === 0 
                        : this.form.targetUser.length === 0 || this.dataServiceSourceValue.length === 0;
            if (isNull) {
                this.$message.warning("请填写完整信息");
                return;
            }
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            let dataSetFlag = true, dataServiceFlag = true;
            if (this.$refs.dataSetTree.getCheckedNodes(true).length > 0) {
                await services.addShare(params, settings).then(res=>{
                    if (res.data.status !== 0) {
                        dataSetFlag = false;
                    }
                });
            }
            if (!dataSetFlag) return;
            if (this.dataServiceSourceValue && this.dataServiceSourceValue.length > 0) {
                await services.addShare(serviceParams, settings).then(res=>{
                    if (res.data.status !== 0) {
                        dataServiceFlag = false;
                    }
                })
            }
            if (dataSetFlag && dataServiceFlag) {
                this.$message.success("分享成功");
                this.$emit('refresh');
            }
        }
    },
    created() {
        this.getAuthorizer('role');
        this.form.sourceType = this.activeN === 'dashboard' ? 'visualizing' : this.activeN || "dataSet" ;
        if(this.tranId){
            this.getSourceDataModeling(this.tranId)
        }
        else{
            this.getSourceDataSetTree();
            this.sourceTypeChange(this.activeN);
        }
    }
}
</script>

<style scoped lang="less">
    .AddShare{
        padding: 14px 0;
        &_sel {
            width: 120px;
            /deep/.el-input--suffix {
                &.is-focus {
                    z-index: 1;
                }
                .el-input__inner {
                    border-radius: 2px 0 0 2px;
                }
            }

        }
        &_dbselect {
            display: flex;
        }
        &_drop {
            flex: 1;
            margin-left: -1px;
        }
    }
</style>
