<template>
    <div class="ObjectToJson height100">
            <!--参数配置弹窗内容页-->
            <ObjectToJsonDialog v-if="showDialog" :sourceData="sourceData"
                                :jsonOptions="jsonOptions"
                                :options="options"
                                :ordinaryType="ordinaryType"
                                :complexType="complexType"
                                :value1="value1"
                                :groupFieldName1="groupFieldName1"
                                :tableData1="tableData1"
                                :ruleTableData1="ruleTableData1"
                                :radio1="radio1"
                                :inputType1="inputType1"
                                @updateOutput="updateOutput(arguments)"
                                ref="objectToJsonDialog"></ObjectToJsonDialog>
            <!-- 弹窗按钮-->
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import ObjectToJsonDialog from "./ObjectToJsonDialog"
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"

    export default {
        name: "ObjectToJson",
        mixins : [pluginMixins , servicesMixins],
        components: {ObjectToJsonDialog, preView},
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return{
                showDialog:false,
                title:'配置转换字段',
                jsonOptions: [{
                    label:"转成json",
                    value:"json"
                },{
                    label:"转成json数组",
                    value:"array"
                }],
                options:[],
                ordinaryType:[],
                complexType:[],
                value1:"json",
                groupFieldName1:"",
                tableData1:[{
                    columnName:"",
                    columnCode:"",
                    columnType:"",
                    length:"",
                }],
                ruleTableData1: [],
                radio1:"false",
                inputType1: [],
                isOutColumnChoose : true,
            }
        },
        methods: {
            save(...args){
                //this.loadParams.loading = true;
                this.saveFn(...args);
            },
            preview(){
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            updateOutput(e){
                this.value1 = e[0];
                this.radio1 = e[1];
                this.groupFieldName1 = e[2];
                this.ruleTableData1 = [];
                e[3].forEach(item => {
                    this.ruleTableData1.push({
                        conversionKey : item.conversionKey,
                        conversionValue : item.conversionValue,
                        isConstant: item.isConstant,
                        isContent : item.isContent,
                        message : item.message
                    });
                });
                this.tableData1 = [];
                e[4].forEach(item => {
                   this.tableData1.push({
                       columnCode:item.columnCode,
                       columnName : item.columnName,
                       columnType : item.columnType,
                       length : item.length
                   });
                });
            },
            saveEditDialog(){
                if (!this.$refs.objectToJsonDialog.saveClose()){
                    return;
                }
                this.close();
            },
            closeEditDialog(){
                this.clear_data();
                this.close();
            },
            getSelectOptions(){
                const vm = this, {pluginServices, pluginMock, settings,loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                loadParams.loading = true;
                this.jsonOptions = [];
                services.getObjectJsonSelectData(loadParams).then(res => {
                    let result = res.data.data;
                    result.forEach(element => {

                        let object = {
                            label : element.name,
                            value : element.code
                        };
                        vm.jsonOptions.push(object);
                    });
                })
            },
            getColumns() {
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                services.queryObjectJsonData(this.sourceData.id, settings).then(res => {
                    let result = res.data.data;
                    result.inputColumn.forEach(element => {
                        let selectObject = {
                            label: element.columnName || element.columnCode,
                            value: element.columnCode,
                        };
                        this.options.push(selectObject);
                    });
                    result.types.forEach(element => {
                        if (element.label !== 'Object') this.ordinaryType.push(element);
                        else this.complexType.push(element);
                    });
                    if (this.options.length > 0){
                        if ( result.pluginMeta.cicadaConversionColumns.length !==0 &&  result.pluginMeta.cicadaOutColumns.length !==0 && result.pluginMeta.conversionType !== null) {
                            let sameField = 0;
                            let optionsTmp = [];
                            for (let i = 0; i < this.options.length; i++) {
                                for (let j = 0; j < result.pluginMeta.cicadaConversionColumns.length; j++) {
                                    if (this.options[i].value === result.pluginMeta.cicadaConversionColumns[j].conversionValue){
                                        sameField++;
                                        optionsTmp.push(result.pluginMeta.cicadaConversionColumns[j]);
                                    }
                                }
                            }
                            if (sameField > 0){
                                this.value1 = result.pluginMeta.conversionType;
                                if (result.pluginMeta.groupColumn !== '') this.groupFieldName1 = result.pluginMeta.groupColumn.split(",");
                                this.tableData1 = result.pluginMeta.cicadaOutColumns;
                                optionsTmp.forEach(element => {
                                    element.isConstant = element.isContent === '0' ? false : true;
                                    element.message = element.isContent === '0' ? 'key是变量' : 'key是常量';
                                });
                                this.ruleTableData1 = optionsTmp;
                                this.radio1 = result.pluginMeta.jsonObj?"true":"false";
                            }
                        }
                        this.columnTypeCheck();
                    }
                    this.showDialog = true;
                });
            },
            columnTypeCheck(){
                if (this.value1 === 'object') this.inputType1 = this.complexType;
                else this.inputType1 = this.ordinaryType;
                if(!this.tableData1[0].columnType)
                    this.tableData1[0].columnType = this.inputType1[0].value;
            },
            async saveFn(saveName,showTip) { //保存后才能预览
                if (!this.$refs.objectToJsonDialog.saveClose()){
                    return;
                }
                const vm = this, {pluginServices, pluginMock, settings,loadParams} = this;
                loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                let serviceOrgOutputs = [];
                let groupColumnName ="";
                if (vm.value1 === 'array' || vm.value1 === 'object') {
                    if (vm.groupFieldName1.length === 0) {
                        vm.$message.warning("请选择分组字段!");
                        return;
                    }else {
                        this.groupFieldName1.forEach(element => {
                            groupColumnName = groupColumnName + element + ",";
                        });
                        if(groupColumnName.length != 0){
                            groupColumnName = groupColumnName.substring(0,groupColumnName.length-1)
                        }
                    }
                }
                if(vm.ruleTableData1.length === 0){
                    vm.$message.warning("行数据转json插件未配置相关字段!");
                    return;
                }
                for (let k = 0; k < vm.ruleTableData1.length; k++) {
                    for (let key in vm.ruleTableData1[k]) {
                        if (vm.ruleTableData1[k][key] === '') {
                            if (key === 'conversionKey') {
                                vm.$message.warning("key不能为空!");
                                return;
                            } else if (key === 'conversionValue') {
                                vm.$message.warning("value不能为空!");
                                return;
                            }
                        }
                    }
                }

                this.isOutColumnChoose  = true;
                for (let key in this.tableData1[0]) {
                    if (key !== 'columnId') {
                        if (this.tableData1[0][key] === '' || this.tableData1[0][key] === 'undefined' || this.tableData1[0][key] === null) {
                            this.isOutColumnChoose = false;
                        }
                    }

                }
                if (!this.isOutColumnChoose) {
                    vm.$message.warning("输出字段请填写完整!");
                    return;
                }
                vm.ruleTableData1.forEach((item, i) => {
                    let serviceOrgOutput = {
                        conversionKey: item.conversionKey,
                        conversionValue: item.conversionValue,
                        isContent: item.isConstant ? 1 : 0 //1 是常量
                    };
                    // if (item.isConstant ) {
                    //     for (let index = 0; index < this.options.length; index++) {
                    //         const element = this.options[index];
                    //         if (element.value === item.conversionKey) {
                    //             serviceOrgOutput = {
                    //                 conversionKey: element.label,
                    //                 conversionValue: item.conversionValue,
                    //                 isContent: item.isConstant ? 1 : 0 //1 是常量
                    //             };
                    //             break;
                    //         }
                    //     }
                    // }
                    serviceOrgOutputs.push(serviceOrgOutput);
                });

                let outColumnsArray = [];
                let objectValue = '';
                this.inputType1.forEach(item => {
                    if (item.label === 'Object'){
                        objectValue = item.value;
                    }
                });
                if (this.tableData1[0].columnType === objectValue){
                    this.complexType.forEach(type => {
                        this.tableData1[0].columnType = type.value;
                    });

                }
                outColumnsArray.push(this.tableData1[0]);
                let objectToJsonMeta = {
                    conversionType: this.value1,
                    groupColumn: groupColumnName,
                    cicadaConversionColumns : serviceOrgOutputs,
                    cicadaOutColumns:outColumnsArray,
                    jsonObj:JSON.parse(vm.radio1)
                };
                settings.loading = true;
                services.objectToJsonSavePlugin(vm.sourceData.id, objectToJsonMeta,loadParams).then(res => {
                    if (res.data.status === 0) {
                        showTip && this.$message.success("保存成功!");
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                        // this.initSettingSenior();
                    }
                })
                //this.loadParams.loading = false;
            },
        },
        created() {
            this.getSelectOptions();
            this.getColumns();
        }
    }
</script>

<style scoped>
    .ce-normal_item.is-require::before , .ce-common_item.is-require::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }
</style>
