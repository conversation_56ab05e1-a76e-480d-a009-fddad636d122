<!--
    @Describe:
    @Author: wangjt
    @Date: 2024/4/18
-->
<template>
    <div class="process-outer">
        <el-tabs class="ce-share_tab" v-model="activeName">
            <el-tab-pane v-for="(tab , inx) in tabs" :key="inx" :label="tab.label" :name="tab.name"></el-tab-pane>
        </el-tabs>
        <div class="process-content">
            <component :is="activeName"></component>
        </div>
    </div>
</template>

<script>
import ApplyMessage from "@views/system-manage/process-manage/list/applyMessage.vue";
import ApprovalMessage from "@views/system-manage/process-manage/list/approvalMessage.vue";
export default {
    name: "process-manage",
    components: {
        ApplyMessage,
        ApprovalMessage,
    },
    data() {
        return {
            activeName: 'ApplyMessage',
            tabs: [
                {name: "ApplyMessage", label: '申请信息'},
                {name: 'ApprovalMessage', label: '审批信息'},
            ]
        }
    },
    methods:{
        showSaveModel(){
            this.$refs.saveModel.show();
        }
    }
}
</script>

<style scoped lang="less">
.process {
    &-outer {
        height: calc(100% - 14px);
        background: #fff;
        border-radius: 4px;
        padding: 28px 28px 14px 28px;
        box-sizing: border-box;
    }

    &-content {
        height: calc(100% - 60px);
    }
}

/deep/ .list_form {
    .el-form-item {
        margin-bottom: 0;
    }
}
</style>
