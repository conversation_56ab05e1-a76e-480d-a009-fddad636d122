import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../../../service-mixins/service-mixins";
import {mapGetters} from "vuex";

export default {
    name: "leftRightJoin" ,
    props :{
        datasetId :String
    },
    computed : {...mapGetters(["userInfo"])},
    mixins : [commonMixins ,servicesMixins],
    data() {
        return {
            tableName : "" ,
            join_label : "关联方式",
            join_table : "要关联的表",
            join_way : "LEFT" ,
            join_opts : [
                {label : "左关联" , value :"LEFT"},
                {label : "右关联" , value :"RIGHT"},
                // {label : "外连接" , value :"OUTER"},
                {label : "右排除" , value :"RIGHT JOIN"},
                {label : "左排除" , value :"LEFT JOIN"},
                {label : "内连接" , value :"JOIN"},
                {label : "全连接" , value :"FULL JOIN"},
            ],
            join_gist :"关联依据" ,
            tableData :[],
            tableList : [],
            datasets : [],
            defaultProps: {
                children: "children",
                label: "name" ,
                value : "id"
            },
            treeBind : {
                "default-expand-all" : true,
                // "check-on-click-node" : true ,
                // "expand-on-click-node" : false
            },
            tableListOpts : {},
            listLabel : {} ,
            minWs : {},
            maxheight : '420px' ,
            hasData : false,
            sourceCode : "" ,
            targetDataSetId : "",
            disabled : false ,
            metaDatasetId :""
        }
    },
    methods : {
        deleteRow(index){
            this.tableData.splice(index ,1);
        },
        filterNode : treeMethods.methods.filterNode ,
        addMergeTable(){
            const vm = this;
            let row = {
                // result : "" ,
                source : "" ,
                sourceCode: ""
            };
            for(let i in vm.tableListOpts){
                row[i] = "";
                row[i + 'Code'] = "";
            }
            this.tableData.push(row);
        },
        radioChange(val){
            this.join_way = val;
        },
       async queryDataSetTree(){
            const vm = this , {datasetServices , datasetMock , settings } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;

            await services.queryDataSetTree(true ,settings,this.datasetId).then(res => {
                if(res.data.status === 0){
                    let result =  res.data.data;
                    vm.datasets = vm.removeCurId(result);
                }
            });
        },
        initColumnsTree(){
            const vm = this , {datasetServices , datasetMock ,datasetId} = this;
            let services = vm.getServices(datasetServices , datasetMock);
            vm.tableList = [];
            services.getLogicColumn("" , datasetId).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    result.forEach(item => {
                        if(item.belongParentId === datasetId){
                            vm.tableList.push({
                                label : item.name || item.code ,
                                value : item.id,
                                code : item.code
                            })
                        }
                    })
                }
            });
        },
        initBackData(data){
            const vm = this;
            vm.disabled = !data.canEdit;
            vm.tableName = data.targetDataSetId;
            vm.join_way = data.joinType;
            vm.datasetChange(vm.tableName);
            let sourceFields = JSON.parse(data.sourceField)   , targetFileds = JSON.parse(data.targetFiled) ;
            sourceFields.forEach((item , i) => {
                let row = {
                    source : item.fieldId ,
                    sourceCode : item.fieldCode
                };
                row[data.targetDataSetCode] = targetFileds[i].fieldId;
                row[data.targetDataSetCode + 'Code'] = targetFileds[i].fieldCode;
                vm.tableData.push(row);
            });
        },
        async initEditData(data){
            const vm = this , {datasetServices , datasetMock , datasetId } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            await vm.queryDataSetTree();
            if(!data){
                services.getJoinAndFilter(datasetId , 'join').then(res => {
                    if(res.data.status === 0) {
                        let result = res.data.data;
                        if(result){
                            vm.metaDatasetId = result.targetDataSetId;
                            vm.initBackData({canEdit : true , ...result});
                        }
                    }
                })
            }else {
                vm.initBackData({canEdit: data.canEdit , ...data.result});
            }
        },
        removeCurId(data){
            const vm = this;
            return data.filter(item => {
                if(item.children && item.children.length){
                    item.children = vm.removeCurId(item.children);
                }
                item.name === null ? item.name = item.code :true;
                if(item.id === vm.datasetId){
                    vm.sourceCode = item.code;
                }
                return item.id !== vm.datasetId;
            })
        },
        datasetChange(val){
            if(!val.length) return;
            const vm = this , {datasetServices , datasetMock } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            vm.tableData = [];
            vm.tableListOpts = {}; vm.minWs  = {} ; vm.listLabel = {} ;vm.hasData = false;
            // val.split(",").forEach(it => {
            vm.$nextTick(()=>{
                let cNode = vm.$refs.pos_tree.$refs.tree.getNode(val);
                vm.$set(vm.listLabel , cNode.data.globalCode , cNode.data.name );
                let minW = cNode.data.name.length < 6 ? 120 : cNode.data.name.length * 14 + 20;
                vm.tableListOpts[cNode.data.globalCode] = [];
                vm.$set( vm.tableListOpts , cNode.data.globalCode , []);
                vm.$set(vm.minWs , cNode.data.globalCode , minW);
                services.getLogicColumn("" , val).then(res => {
                    if(res.data.status === 0){
                        let result = res.data.data;
                        result.forEach(item => {
                            vm.tableListOpts[cNode.data.globalCode].push({
                                label : item.name || item.code ,
                                value : item.id,
                                code : item.code
                            })
                        })
                    }
                });
                vm.hasData = true;
            })

            // })
        },
        checkJoinFields(row){
            const vm = this; let res = false;
            for(let i in vm.tableListOpts){
                if(row[i] === ""){
                    res = true;
                    break;
                }
            }
            return res;
        },
        checkFields(){
            const vm = this , {tableData} = this;
            let res = false;
            for(let i = 0;i < tableData.length ;i ++){
                let row = tableData[i];
                /*if(row.source === ""){
                    res = true;
                    vm.$message.warning("当前表字段名不能为空");
                    break;
                }else */if(vm.checkJoinFields(row)){
                    res = true;
                    vm.$message.warning("关联表字段名不能为空");
                    break;
                }
            }
            return res;
        },
        validate(){
            const vm = this;
            let res = false;
            if(vm.tableName === ""){
                res = true;
                vm.$message.warning("请选择合并数据集");
            }else if(vm.tableData.length === 0){
                res = true;
                vm.$message.warning("请添加合并字段");
            }else if(vm.checkFields()){
                res = true;
            }
            return res;

        },
        setFieldCode(params , row , filed){
            const [val , data]  = params ;
            row[filed+'Code'] = data.code;
        },
        setFieldsVo(fieldCode){
            const vm = this;
            let fields =  [];
            vm.tableData.forEach(item => {
                fields.push({
                    fieldId: item[fieldCode],
                    fieldCode:item[fieldCode + 'Code']
                })
            });
            return JSON.stringify(fields) ;
        },
        setJoinVo(){
            const vm = this , {join_way ,sourceCode ,datasetId ,listLabel , tableName , userInfo , metaDatasetId} = this;
            return {
                dataSetId: datasetId,
                joinType: join_way,
                sourceDataSetCode: sourceCode,
                sourceDataSetId: datasetId,
                sourceField: vm.setFieldsVo('source'),
                targetDataSetCode: Object.keys(listLabel)[0],
                targetDataSetId: tableName,
                targetFiled: vm.setFieldsVo(Object.keys(listLabel)[0]),
                userId: userInfo.id ,
                metaTargetDataSetId : metaDatasetId
            };
        },
        submit(){
            const vm = this;
            if( vm.validate()) return;
            let params  = vm.setJoinVo();
            vm.$emit("join" , params ,vm.tableName );
        }
    },
    created(){
        this.initColumnsTree();
    }
}