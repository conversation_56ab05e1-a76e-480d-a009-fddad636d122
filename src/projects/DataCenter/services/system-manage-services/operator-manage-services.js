/**
 * 算子管理 接口
 * @Author: baijx
 * @Date: 2020-10-29
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/udfOperatorManage/" ;

/*
*  获取列表
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function getOperatorListPage(params, settings ) {
    return await reqUtil.post({
        url: root + 'getOperatorListPage',
        settings : settings
    },  params );
}
export async function getOperatorTree(version , settings ) {
    return await reqUtil.get({
        url: root + 'getOperatorTree',
        settings : settings
    },{version});
}

export async function createUdfOperator(params,settings) {
    return await reqUtil.post({
        url: root + 'createUdfOperator',
        settings : settings
    },  params );
}

/**
 * 算子测试
 */
export async function testUdfFunction(params,settings) {
    return await reqUtil.post({
        url: root + 'testUdfFunction',
        settings : settings
    },  params );
}

export async function deleteUdfOperator(operatorId,settings) {
    return await reqUtil.get({
        url: root + 'deleteUdfOperator',
        settings : settings
    },  {operatorId} );
}

export async function expressionParser(expression,settings) {
    return await reqUtil.get({
        url: root + 'expressionParser',
        settings : settings
    },  {expression} );
}

export async function getParameterTypes(settings) {
    return await reqUtil.get({
        url: root + 'getParameterTypes',
        settings : settings
    },  );
}