<template>
    <div>
        <el-dialog :close-on-click-modal="false" custom-class="ce-prompt" title="过程重命名" :visible.sync="dialogFormVisible" width="30%">
            <el-form ref="form" size="mini" :model="form" label-position="top" @submit.native.prevent>
                <el-form-item label="过程名称:" prop="name" :rules="rule" :label-width="formLabelWidth">
                    <el-input v-model.trim="form.name" :placeholder="placeholder" maxlength="100" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="btnSureClick" size="mini" type="primary">确定</el-button>
                <el-button @click="dialogFormVisible = false " size="mini">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        name: "RenameDailog",
        data() {
            return {
                placeholder : "请输入过程名称(限100个字符)",
                tempName:"",
                dialogFormVisible: false,
                formLabelWidth: "100px",
                index: 0,
                form: {
                    transId: "",
                    name: ""
                },
                rule :[
                    {required :true , message : "过程名称不能为空" , trigger :'blur'} ,
                    {max :100 , message : "过程名称限100个字符" , trigger : 'blur'}
                ]
            };
        },
        methods: {
            show() {
                this.dialogFormVisible = !this.dialogFormVisible;
            },
            btnSureClick() {
                const _this = this;
                if(this.tempName === _this.form.name){
                    _this.dialogFormVisible = false;
                    return;
                }
                let data = {
                    name: _this.form.name,
                    transId: _this.form.transId,
                };
                this.$refs.form.validate(value =>{
                    if(value){
                        _this.$axios.post("/transOperator/updateTransName", data).then(res => {
                            // console.log(res);
                            if (res.data.status === 1) {
                                _this.$message.error(res.data.msg);
                            } else {
                                if(res.data.data === "success"){
                                    _this.$message.success("方案名称更改成功！");
                                    _this.dialogFormVisible = false;
                                    _this.$emit("rename", _this.form.name, _this.index, _this.form.transId);
                                }else{
                                    _this.$message.warning(res.data.data);
                                }

                            }
                        }).catch(err => {
                            console.log(err);
                            _this.$message.error("服务器异常，请联系管理员！！！")
                        })
                    }
                })

            },
            setName(val, index) {
                this.tempName = val.modelName;
                this.form.name = val.modelName;
                this.form.transId = val.transId;
                this.index = index;
            }
        }
    };
</script>
<style scoped>
</style>