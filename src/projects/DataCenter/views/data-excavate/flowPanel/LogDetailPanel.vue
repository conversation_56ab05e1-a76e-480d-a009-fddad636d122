<template>
    <div class="logDetailPanel">
        <el-button-group>
            <el-button v-for="(btn , inx) in buttons"
                       :key="inx"
                       type="primary"
                       size="mini"
                       :class="{'button__active' : btn.active}"
                       :icon="btn.icon"
                       @click="btn.clickFn(inx)"
                       plain>{{btn.label}}
            </el-button>
        </el-button-group>
        <el-popover
                class="dropdown"
                trigger="click"
                v-model="visible1"
        >
            <el-button size="mini"
                       type="primary"
                       slot="reference"
                       @click="showMenu"
                       plain>{{logType}}
                <i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <ul class="drop-menu">
                <li class="drop-item"
                    v-for="(item , i) in dropMenu"
                    :key="i"
                    :class="{'active' : item.active}"
                    @click="filterLog(item.value , i)"
                >{{item.label}}
                </li>
            </ul>

        </el-popover>
        <div class="dropdown">
            <el-button size="mini"
                       type="primary"
                       slot="reference"
                       @click="popShow"
                       plain>{{filterName}}
                <i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <div class="ce-popover" v-if="visible2">
                <div class="ce-popper__arrow"></div>
                <el-form :model="filterForm" size="mini" ref="ruleForm" label-width="80px">
                    <el-form-item
                            v-for="(formI , i) in formList"
                            :key="i"
                            :label="formI.label"
                            :prop="formI.prop">
                        <el-input :placeholder="formI.placeholder"
                                  v-if="formI.type ==='input'"
                                  v-model="formI.value"></el-input>
                        <el-date-picker
                                v-if="formI.type ==='daterange'"
                                v-model="formI.value"
                                type="daterange"
                                range-separator="-"
                                :start-placeholder="formI.startL"
                                :end-placeholder="formI.endL"
                        >
                        </el-date-picker>
                        <el-button @click="submit" type="primary" v-if="formI.type === 'button'" plain>{{formI.name}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="search_box">
            <el-input :placeholder="filterPlaceholder" @focus="popHide" v-model="filterText" size="small"
                      class="ce-search">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
        </div>
        <div class="cl"></div>
        <div class="ce-log__content" ref="logContent">
            <ul class="ce-log__wrap">
                <li class="ce-log__item"
                    v-for="(log , inx) in logList"
                    :key="inx"
                    :class="['ce-log__'+log.type ,{'ce-log__nowrap' : isNowrap}] "
                >{{log.content}}
                </li>
                <li v-if="!logList.length" class="ce-log__item ce-log__nodata">{{noLog}}</li>
            </ul>
        </div>
    </div>
</template>

<script>
    export default {
        name: "LogDetailPanel",
        props: {
            taskData: Object,
        },
        data() {
            return {
                noLog: '暂无数据',
                dropName: '级别',
                dropMenu: [
                    {
                        label: '全部(ALL)',
                        value: 'all',
                        active: true
                    }, {
                        label: '错误(ERROR)',
                        value: 'error',
                        active: false
                    }, {
                        label: '警告(WARN)',
                        value: 'warn',
                        active: false
                    }, {
                        label: '调试(DEBUG)',
                        value: 'debug',
                        active: false
                    }, {
                        label: '信息(INFO)',
                        value: 'info',
                        active: false
                    }, {
                        label: '追踪(TRACE)',
                        value: 'trace',
                        active: false
                    }
                ],
                visible1: false,
                visible2: false,
                logTypeFilter: [],
                logType: '级别',
                filterName: '过滤',
                checkboxGroup: [],
                buttons: [
                    {
                        label: 'Top',
                        icon: 'el-icon-caret-top',
                        clickFn: this.gotoTop
                    }, {
                        label: '重置',
                        icon: 'el-icon-refresh',
                        clickFn: this.resetData
                    },
                    {
                        label: '实时数据',
                        icon: '',
                        active: false,
                        clickFn: this.realTimeData
                    },
                    {
                        label: '样式',
                        icon: '',
                        active: false,
                        clickFn: this.logNowrap
                    }
                ],
                formList: [
                    {
                        label: '时间范围',
                        value: '',
                        prop: 'time',
                        type: 'daterange',
                        startL: '开始时间',
                        endL: '结束时间'
                    },
                    {
                        label: '线程名',
                        value: '',
                        prop: 'threadN',
                        type: 'input',
                        placeholder: '请输入线程名'
                    }, {
                        label: '类名',
                        value: '',
                        prop: 'classN',
                        type: 'input',
                        placeholder: '请输入类名'
                    }, {
                        label: '',
                        type: 'button',
                        name: '确定'
                    }
                ],
                filterText: '',
                filterPlaceholder: '请输入日志关键字',
                logList: [], //日志内容
                isNowrap: false,
                list: [],
                startTimeStamp: "",
                endTimeStamp: "",
                timeFlag: 0,
            }
        },
        watch: {
            filterText(val) { //输入过滤日志
                let value = val.trim().toLowerCase();
                value ?
                    this.logList = this.logList.filter(log => {
                        return log.content.toLowerCase().indexOf(val) !== -1;
                    }) :
                    this.initLogList();
                //后面可换为请求的方法
            },
            logTypeFilter(val) {
                let isAll = val[0] === 'all';
                isAll ? this.initLogList() :
                    this.logList = this.list.filter(log => {
                        return val.indexOf(log.type) !== -1;
                    }); //后面可换为请求的方法
            }
        },
        computed: {
            filterForm() { //过滤表单 结果
                return {
                    time: this.formList[0].value,
                    threadN: this.formList[1].value,
                    classN: this.formList[2].value
                }
            },

        },
        methods: {
            initLogList() {
                this.logList = this.list;
            },
            filterLog(value, i) {
                this.menuClick(i);
                if (this.dropMenu[i].active) { //选中即把过滤条件添加，否移除
                    if(i === 0){
                        this.logTypeFilter = [];
                    }else{
                        this.logTypeFilter.push(value);
                    }
                } else {
                    this.logTypeFilter = this.logTypeFilter.filter(logType => {
                        return logType !== value;
                    })
                }
            },
            menuClick(i) {
                let isActive = this.dropMenu[i].active;
                if (i > 0) {
                    this.dropMenu[i].active = !isActive;
                    this.dropMenu[0].active = false;
                } else {
                    this.dropMenu.forEach((menu, inx) => {
                        if (inx > 0) {
                            menu.active = false;
                        } else {
                            menu.active = !isActive;
                        }
                    })
                }
            },
            gotoTop(i) {
                this.popHide();
                this.$refs.logContent.scrollTop = 0;
            },
            resetData(i) {
                this.popHide();
                this.logList = [];
            },
            realTimeData(i) {
                this.popHide();
                this.buttons[i].active = !this.buttons[i].active;
                let timer = null;
                if (this.buttons[i].active) { //定时请求数据
                    // this.getRealTime();
                    this.timeFlag = setInterval(this.getRealTime, 1000);
                } else {
                    this.clearTime();
                }
            },
            logNowrap(i) {
                this.popHide();
                this.buttons[i].active = !this.buttons[i].active;
                this.isNowrap = this.buttons[i].active;
            },
            popShow() {
                this.visible2 = !this.visible2;
            },
            popHide() {
                this.visible2 = false;
            },
            submit() {
                this.popHide();
            },
            showMenu() {
                this.popHide();
            },
            getRealTime() {
                let data = this.filterForm;
                if (data.time instanceof Array
                    && data.time[0] instanceof Date
                    && data.time[1] instanceof Date) {
                    this.startTimeStamp = this.FomartDate(data.time[0]);
                    this.endTimeStamp = this.FomartDate(data.time[1]);
                }
                // console.log("level:",this.logTypeFilter);
                this.$axios.get("/taskLog/realTimeData?id=&startTimeStamp=" + this.startTimeStamp + "&endTimeStamp=" + this.endTimeStamp + "&level=" + this.logTypeFilter + "&threadName=" + this.filterForm.threadN + "&className=" + this.filterForm.classN + "&tableName=t_etl_trans_exec_detail&categoryName=etlTask." + this.taskData.taskId).then(res => {
                    // console.log(res);
                    if(res.data.status === 1){
                        this.$message.error(res.data.msg);
                    }else {
                        this.logList = [];
                        res.data.data.forEach(item => {
                            let logInfo = {
                                type: item.level.toLowerCase(),
                                content: item.msg,
                            }
                            this.logList.push(logInfo);
                        })
                    }
                })
            },
            FomartDate(date){
                let y=date. getFullYear()
                let m=date. getMonth()+1
                let d=date. getDate()
                let H=date. getHours()
                let M=date. getMinutes()
                let S=date. getSeconds()
                function Covering(num){
                    return num < 10? "0"+num:num;
                }
                return y+"-"+Covering(m)+"-"+Covering(d)+" "+Covering(H)+":"+Covering(M)+":"+Covering(S)
            },
            clearTime(){
                clearInterval(this.timeFlag);
            },
        },
        created() {
            this.initLogList();
        }
    }
</script>

<style scoped>
    .logDetailPanel {
        vertical-align: middle;
    }

    .dropdown {
        display: inline-block;
        vertical-align: middle;
        margin-left: 10px;
        position: relative;
    }

    .drop-item {
        line-height: 28px;
        height: 28px;
        cursor: pointer;
        padding: 0 10px;
        font-size: 12px;
        border-radius: 3px;
        border-bottom: 1px solid #fff;
    }

    .drop-item:hover, .drop-item.active {
        background-color: #409EFF;
        color: #fff;
    }

    .ce-popover {
        position: absolute;
        background: #FFF;
        min-width: 150px;
        border: 1px solid #EBEEF5;
        padding: 12px;
        z-index: 2000;
        color: #606266;
        line-height: 1.4;
        text-align: justify;
        font-size: 14px;
        -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        word-break: break-all;
        margin-top: 10px;
        border-radius: 4px;
        left: 50%;
        transform: translate(-50%, 0);
    }

    .ce-popper__arrow {
        border: 6px solid transparent;
        position: absolute;
        width: 0;
        height: 0;
        top: -6px;
        left: 50%;
        border-top-width: 0;
        border-bottom-color: #EBEEF5;
        z-index: 10;

    }

    .ce-popper__arrow::after {
        content: "";
        border-width: 6px;
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        top: 1px;
        margin-left: -6px;
        border-top-width: 0;
        border-bottom-color: #FFF;
    }

    .search_box {
        width: 200px;
        float: right;
    }

    .button__active {
        background: #409EFF;
        border-color: #409EFF;
        color: #FFF;
    }

    .ce-log__content {
        margin-top: 10px;
        height: 400px;
        overflow: auto;
        background: #333;
    }

    .ce-log__wrap {
        padding: 12px;
    }

    .ce-log__item {
        padding: 4px 2px;
        white-space: pre-wrap;
    }

    .ce-log__item:hover {
        background: #606669;
    }

    .ce-log__nowrap {
        white-space: nowrap;
    }

    .ce-log__error {
        color: #f56c6c;
    }

    .ce-log__warn {
        color: #e6a23c;
    }

    .ce-log__info {
        color: #fff;
    }

    .ce-log__debug {
        color: #409eff;
    }

    .ce-log__trace {
        color: #e1f3d8;
    }

    .ce-log__nodata {
        color: #fff;
        text-align: center;
    }
</style>
