<template>
    <div class="chartCard" >

        <div :style="{'height' : showDrill ? drillHeight : height}">
            <ve-chart
                :data="chartData"
                :settings="chartSettings"
                :width="width"
                :height="height"
                :judge-width="true"
                ref="chart"
                :extend="extend"
                v-loading="loading"
                :data-empty="data_empty"
                :events="chartEvents"
            ></ve-chart>
        </div>
        <div v-if="showDrill" class="chartTip"
             @click="backToDims"
             :title="dimsName"><i class="el-icon-arrow-left"></i>{{ dimsName }}
        </div>
    </div>
</template>

<script src="./combination-card.js"></script>
<style scoped lang="less" src="../css/chart.less"></style>
<style scoped lang="less" src="./combination-card.less"></style>
