<template>
    <div class="DataFormat">
        <el-dialog
                :title="dialogTitle"
                :visible.sync="dialogVisible"
                :width="width"
                :append-to-body="true"
                :modal-append-to-body="true"
                :close-on-click-modal="false"
                @closed="clear"
        >
            <el-form  label-position="right" size="mini">
                <el-form-item v-for="(item  , key ) in format"
                              :key="key"
                              :label="item.label ? item.label + ' :' : ''"
                              :label-width="item.type === 'checkbox' ? '0' : '100px'"
                              v-show="key === 'type' ? true : key === 'decimal' || key === 'separator' ? formatVal.type === 'number' : formatVal.type === 'time'"
                >
                    <el-radio-group v-if="item.type === 'radio'" v-model="formatVal[key]">
                        <el-radio v-for="radio in item.options" :key="radio.value" :label="radio.value">{{radio.label}}</el-radio>
                    </el-radio-group>
                    <el-select v-if="item.type === 'select'" v-model="formatVal[key]">
                        <el-option
                                v-for="opt in item.options"
                                :key="opt.value"
                                :label="opt.label"
                                :value="opt.value"
                        ></el-option>
                    </el-select>
                    <!--<div v-if="item.type === 'checkbox' && formatVal.type === 'number'"  class="pl10">
                        <el-checkbox v-model="formatVal[key]">使用千位分隔符(,)</el-checkbox>
                    </div>-->
                </el-form-item>
            </el-form>

            <div slot="footer" >
                <el-button @click="save" size="mini" type="primary">确定</el-button>
                <el-button @click="close" size="mini">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: "DataFormat" ,
        data(){
            return {
                dialogTitle : '数据格式' ,
                width : '400px',
                dialogVisible : false ,
                dataType : 'number',
                format : {
                    type : {
                        label : '数据类型' ,
                        type : 'radio',
                        options :  [
                            {
                                value : 'number',
                                label : '数值'
                            }, {
                                value : 'time',
                                label : '时间'
                            }
                        ]
                    },
                    decimal : {
                        label : '保留小数位' ,
                        type : 'select' ,
                        options : [
                            {value : 0 , label : '不保留'},
                            {value : 1 , label : '1'},
                            {value : 2 , label : '2'},
                            {value : 3 , label : '3'},
                            {value : 4 , label : '4'},
                            {value : 5 , label : '5'},
                            {value : 6 , label : '6'},
                            {value : 7 , label : '7'},
                            {value : 8 , label : '8'},
                        ]
                    },
                    separator : {
                        label : '' ,
                        type : 'checkbox'
                    } ,
                    timeFormat : {
                        label :'时间格式' ,
                        type : 'select' ,
                        options : [
                            {value :'yy-MM-dd' , label :'yy-MM-dd'},
                            {value :'yyyy-MM-dd HH:mm:ss' , label :'yyyy-MM-dd HH:mm:ss'},
                            {value :'yyyy-MM-dd HH:mm:ss.SSS' , label :'yyyy-MM-dd HH:mm:ss.SSS'},
                            {value :'yyyy年MM月dd日' , label :'yyyy年MM月dd日'},
                            {value :'yyyyMMdd' , label :'yyyyMMdd'},
                            {value :'yyyyMMddHHmmss' , label :'yyyyMMddHHmmss'},
                        ]
                    }
                },
                formatVal :{
                    type : 'number' ,
                    decimal : '' ,
                    separator : false ,
                    timeFormat : ''
                },
                dataItem : {} ,
                inx : ''
            }
        },
        methods : {
            show(data , item , inx){
                this.dialogVisible = true;
                this.dataItem = item ;
                this.inx = inx;
                if(item.options[inx].format.value){
                    this.formatVal = this.copyArrayObj(item.options[inx].format.value);
                }
            },
            clear(){
                this.formatVal = {
                    type : 'number' ,
                    decimal : '' ,
                    separator : false ,
                    timeFormat : ''
                };
            },
            save(){
                if(this.formatVal.type === 'number'){
                    this.formatVal.timeFormat = '';
                }else {
                    this.formatVal.decimal = '';
                    this.formatVal.separator = false;
                }
                this.$emit('formatData' , this.formatVal , this.dataItem , this.inx);
                this.close();
            },
            close(){
                this.dialogVisible = false;
            }
        }
    }
</script>

<style scoped>

</style>