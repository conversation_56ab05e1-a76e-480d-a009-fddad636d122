<template>
    <div class="BusinessFeature">
        <ce-list-table
                :border="false"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                :operate-icons="operateIcons"
                @changePage="changePage"
                :filter-placeholder="filterPlaceholder"
        >
            <template slot="button">
                <el-button type="primary" @click="addFn" >{{add}}</el-button>
                <el-button @click="addAllFn" >{{addAll}}</el-button>
                <el-button @click="outputData" >{{output}}</el-button>
            </template>
            <el-form slot="search" inline class="ce-form__m0 ml20">
                <el-form-item label="修改时间范围:">
                    <el-date-picker
                            v-model="date"
                            unlink-panels
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
        </ce-list-table>
    </div>
</template>

<script>
export default {
    name: "BusinessFeature",
    data(){
        return {
            filterPlaceholder:"请输入特征名称/业务元搜索",
            date:"",
            add : '新增' ,
            addAll : '批量新增' ,
            output : '导出' ,
            filterText : "" ,
            tHeadData:[
                {
                    prop: "name" ,
                    label : "特征名称",
                    minWidth:"140px"
                },{
                    prop: "code" ,
                    label : "英文名称",
                    minWidth:"140px"
                },{
                    prop: "businessEle" ,
                    label : "关联业务元",
                    minWidth:"100px"
                },{
                    prop: "editTime" ,
                    label : "修改时间",
                    minWidth:"160px"
                },{
                    prop: "editor" ,
                    label : "修改用户",
                    minWidth:"100px"
                },{
                    prop:'operate' ,
                    label : "操作",
                    align:"center"
                }
            ],
            tableData:[],
            operateIcons:[
                {
                    name: "编辑",
                    clickFn: this.edit,
                    show: () => true,
                    condition: (right) => true
                },{
                    name: "删除",
                    clickFn: this.deleteB,
                    show: () => true,
                    condition: (right) => true
                },
            ],
            eleOption : [] ,//业务元下拉选项数据
        }
    },
    methods : {
        /**
         * 编辑
         * @param row
         * @param inx
         */
        edit(row , inx){},
        /**
         * 删除
         * @param row
         * @param inx
         */
        deleteB(row ,inx){},
        /**
         * 分页跳转
         * @param inx
         */
        changePage(inx){

        },
        /**
         * 导出
         */
        outputData(){},
        /**
         * 新增
         */
        addFn(){
            const vm = this , {eleOption} = vm;
            let layer = vm.$dgLayer({
                title : '新增业务特征',
                content : require('./addFeature.vue'),
                maxmin:false,
                move:false,
                props:{
                    eleOption
                },
                area : ["660px" , "300px"],
                on : {
                    submit(list){

                    }
                }
            })
        },
        /**
         * 批量新增
         */
        addAllFn(){
            const vm = this, {eleOption} = vm;
            let layer = vm.$dgLayer({
                title : '批量新增业务特征',
                content : require('./batchAddFeature.vue'),
                maxmin:false,
                move:false,
                area : ["1200px" , "620px"],
                props:{
                    eleOption
                },
                on : {
                    submit(list){

                    }
                }
            })
        },
    }
}
</script>

<style scoped>

</style>
