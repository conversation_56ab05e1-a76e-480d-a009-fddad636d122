<template>
    <div class="ce-list" v-loading="settings.loading">
        <div class="ce-list_top">
            <!--<dg-select v-model="type" :data="typeList" :placeholder="typePlace"></dg-select>-->
            <div class="ce-table__search ml10 mr10">
                <el-input
                        size="mini"
                        placeholder="请输入用户名搜索"
                        v-model.trim="inputValueTable"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'inputValueTable')"
                        @keyup.enter.native="searchTableEvent"
                >
                    <i
                            class="el-icon-search el-input__icon poi"
                            slot="suffix"
                            @click="searchTableEvent">
                    </i>
                </el-input>
            </div>
            <el-popover popper-class="ce-popover"
                        placement="bottom-end"
                        trigger="click"
                        v-model="showSenior"
                        width="564"
            >
                <senior-query ref="senior" @seniorQuery="seniorQuery"/>
                <el-button type="primary" @click="seniorInit" slot="reference">{{ showSenior ? hideM : queryM }} <em
                        :class="showSenior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></em></el-button>
            </el-popover>

        </div>
        <div class="ce-list_table">
            <common-table
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    :max-height="tableBodyH"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                <template slot="operate" slot-scope="{row}">
                    <span v-for="(opt , inx) in operator" :key="inx"
                          v-if="opt.condition(row , rights)">
                        <dg-button type="text" v-html="opt.label" :title="opt.label"
                                   @click="opt.clickFn(row)"></dg-button>
                    </span>
                </template>
            </common-table>
        </div>
    </div>
</template>

<script>
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {common} from "@/api/commonMethods/common"
import $right from "@/assets/data/right-data/right-data";
import {mapGetters} from "vuex";
import seniorQuery from "@/projects/DataCenter/views/system-manage/log-query/dialog/senior-query"
import {servicesMixins} from "@/projects/DataCenter/views/system-manage/log-query/services-mixins/services-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {createComponent} from "@/api/commonMethods/createComponent";
import helpDocument from "@components/signed-components/helpDocument/helpDocument";
export default {
    name: "list",
    mixins: [listMixins, coustTableH, common, commonMixins, servicesMixins],
    components: {seniorQuery},
    computed: {
        ...mapGetters(["userInfo", "userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
    },
    data() {
        return {
            inputValueTable: "",
            type: "",
            typeList: [
                {label: "登录", value: "login"},
                {label: "查询", value: "query"},
                {label: "新增", value: "add"},
                {label: "修改", value: "edit"},
                {label: "删除", value: "delete"},
            ],
            typePlace: "请选择操作类型",
            queryM: "高级检索",
            hideM: "收起检索",
            tableData: [],
            tHeadData: [
                {
                    prop: "id",
                    label: "记录标识",
                    align: "left",
                    minWidth: "140"
                }, {
                    prop: "userName",
                    label: "用户名",
                    align: "left",
                    minWidth: "140"
                }, /*{
                    prop: "unit",
                    label: "单位名称",
                    align: "left",
                    minWidth: "100"
                },*/ {
                    prop: "ipAddress",
                    label: "终端标识",
                    align: "left",
                    minWidth: "100"
                }, {
                    prop: "operateTime",
                    label: "操作时间",
                    align: "left",
                    minWidth: "160"
                }, {
                    prop: "operateType",
                    label: "操作类型",
                    align: "center",
                    minWidth: "100"
                }, {
                    prop: "modelName",
                    label: "功能模块",
                    align: "center",
                    minWidth: "100"
                }, {
                    prop: "stateName",
                    label: "操作结果",
                    align: "left",
                    minWidth: "100"
                }, {
                    prop: "operate",
                    label: "操作",
                    align: "center",
                    minWidth: "100"
                },
            ],
            operator: [
                {
                    label: '查看详情',
                    clickFn: this.detail,
                    condition: (row, right) => right.includes($right["logQueryViewDetails"]),
                }
            ],
            showSenior: false,
            queryForm: {
                userName: "",
                ipAddress: "",
                state: "",
                operateType: "",
                beginTime: "",
                endTime: "",
                functionalModule: "",
            },
            logModels: [],//模型
            operateTypeOpt: [],//操作类型
        }
    },
    methods: {
        /**
         * 高级筛选初始化
         */
        seniorInit() {
            const vm = this, {showSenior, queryForm, logModels, operateTypeOpt} = vm;
            if (showSenior) return;
            this.$refs.senior.init(queryForm, logModels, operateTypeOpt);
        },
        seniorQuery({userName,ipAddress ,operateType ,state ,endTime , beginTime , functionalModule}) {
            this.queryForm = {
                userName,ipAddress ,operateType ,state ,endTime , beginTime , functionalModule
            };
            this.inputValueTable = userName;
            this.showSenior = false;
            this.changePage();
        },
        /**
         * 详情
         * @param row
         */
        detail(row) {
            const vm = this;
            let rowData = {
                ...row ,
                id: row.id || "-",
                userName: row.userName || "-",
                ipAddress: row.ipAddress || "-",
                operateTime: row.operateTime || "-",
                operateType: row.operateType || "-",
                stateName: row.stateName || "-",
                message: row.message || "-",
                modelName: row.modelName || "-",
                operateCondition : row.operateCondition || "-",
                certificateNumber : row.certificateNumber || "-",

            };
            vm.$dgLayer({
                title: "日志详情",
                content: require("../dialog/detail"),
                maxmin: false,
                move: false,
                props: {
                    row: rowData
                },
                area: ["800px", "450px"],
                success: function (layero){
                    createComponent(helpDocument , {code : "logQueryViewDetails"} , layero.find(".layui-layer-title")[0])
                }
            })
        },
        /**
         * 搜索
         * @param val
         */
        searchTableEvent() {
            this.queryForm = {
                userName: "",
                ipAddress: "",
                state: "",
                operateType: "",
                beginTime: "",
                endTime: "",
                functionalModule: "",
            };
            this.queryForm.userName = this.inputValueTable;
            this.changePage();
        },
        async changePage(inx = 1) {
            const vm = this, {logServices, queryForm, paginationProps, logModels ,settings} = vm;
            vm.paginationProps.currentPage = inx;
            let pageIndex = inx, {pageSize} = paginationProps;
            settings.loading = true;
            vm.tableData = [];
            let {data} = await logServices.getLogInfo({...queryForm, pageIndex, pageSize} ,settings);
            if (data.code !== 0) return;
            let result = data.data;
            vm.tableData = result.dataList.map(list => {
                list.stateName = list.state === 'success' ? '成功' : '失败';
                let model = logModels.find(log => log.value === list.functionalModule);
                list.modelName = list.functionalModule && model && model.label ? model.label : "-";
                return list;
            });
            vm.total = result.totalCount;
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage();
        },
        /**
         * 获取模块
         * @return {Promise<void>}
         */
        async init() {
            const vm = this, {logServices} = vm;
            await vm.getLogModels(logServices, vm);
            await vm.getTypes(logServices, vm);
            vm.changePage();
        },
        async getLogModels(logServices, vm) {
            let {data} = await logServices.getLogModels();
            vm.logModels = [];
            if (data.code !== 0) return;
            for (let k in data.data) {
                vm.logModels.push({
                    label: k,
                    value: data.data[k]
                })
            }
        },
        async getTypes(logServices, vm) {
            let {data} = await logServices.getTypes();
            vm.operateTypeOpt = [];
            if (data.code !== 0) return;
            for (let k in data.data) {
                vm.operateTypeOpt.push({
                    label: k,
                    value: data.data[k]
                })
            }
        }
    },
    created() {
        this.init();

    }
}
</script>

<style scoped lang="less">
.ce-list {
    height: 100%;

    &_top {
        display: flex;
        justify-content: flex-end;
    }

    &_table {
        padding: 15px 0;
        height: calc(100% - 60px);
        display: flex;
        overflow: hidden;
        align-items: center;
    }

}
</style>
