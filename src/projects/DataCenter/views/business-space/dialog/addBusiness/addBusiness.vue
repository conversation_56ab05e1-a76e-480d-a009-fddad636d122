<!--新建业务板块-->
<template>
    <div class="addBusiness">
        <ce-step :data="steps" :value.sync="activeStep" :space="350"></ce-step>
        <base-info ref="baseInfo" v-show="activeStep === 0"></base-info>
        <physics-setting v-show="activeStep === 1"></physics-setting>

        <el-button v-footer @click="close">{{ btnCancelTxt }}</el-button>
        <el-button v-footer @click="nextStep" v-show="activeStep === 0" type="primary">{{ btnNextTxt }}</el-button>
        <el-button v-footer @click="prevStep" v-show="activeStep === 1" type="primary">{{ btnPrevTxt }}</el-button>
        <el-button v-footer @click="testLink" v-show="activeStep === 1" type="primary">{{ btnTestTxt }}</el-button>
        <el-button v-footer @click="save" v-show="activeStep === 1" type="primary">{{ saveBtnTxt }}</el-button>
    </div>
</template>

<script>
import CeStep from "@/components/common/ce-step/CeStep";
import baseInfo from "@/projects/DataCenter/views/business-space/dialog/addBusiness/baseInfo";
import physicsSetting from "@/projects/DataCenter/views/business-space/dialog/addBusiness/physicsSetting";
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "addBusiness",
    mixins: [commonMixins],
    components: {
        CeStep,
        baseInfo,
        physicsSetting
    },
    data() {
        return {
            activeStep: 0,
            steps: [
                {
                    label: "基础信息",
                    clickFn: this.baseClick
                }, {
                    label: "物理配置",
                    clickFn: this.physicsClick
                }
            ],
        }
    },
    methods: {
        /**
         * 表单校验
         * @return {boolean}
         */
        baseValidate() {
            const vm = this;
            let result = false;
            vm.$refs.baseInfo.validate(valid => {
                if (valid) {
                    this.activeStep = 1;
                    result = valid;
                }
            })
            return result;
        },
        /**
         * 物理 点击
         * @return {boolean}
         */
        physicsValidate() {
            return this.nextStep();
        },
        /**
         * 基础 点击
         * @param step
         * @param i
         * @return {boolean}
         */
        baseClick(step, i) {
            return true;
        },
        physicsClick(step, i) {
            return this.baseValidate();
        },
        close() {
            this.$emit("close")
        },
        /**
         * 下一步
         * @return {boolean}
         */
        nextStep() {
            return this.baseValidate();
        },
        /**
         * 上一步
         */
        prevStep() {
            this.activeStep = 0;
        },
        testLink(){

        },
        save(){

        }
    }
}
</script>

<style scoped>

</style>
