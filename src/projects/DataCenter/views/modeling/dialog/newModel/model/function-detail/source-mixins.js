import {globalBus} from "@/api/globalBus";

export const sourceMixins = {
    methods : {
        dragend(){
            globalBus.$emit('dragend');
        },
        addNode(plug) {
            globalBus.$emit('plugNode', plug);
        },
        getSourceTitle(data){
            const lastUpdateTime = data.lastUpdateTime ? `\n更新时间: ${data.lastUpdateTime};` : '';
            return `来源数据表: ${data.belongTableName};\n数据集名称: ${data.label}; \n创建人: ${data.operateUser} ;\n创建时间: ${data.operateTime};${lastUpdateTime}`;
        }
    }
}
