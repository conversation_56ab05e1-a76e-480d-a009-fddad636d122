<template>
    <div class="RuleEditingPanel ce-plug_cont" v-loading="settings.loading">
        <el-row :gutter="10">
            <el-col :span="5">
                <el-select v-model="value" placeholder="请选择" @change="jsonTypeChange">
                    <el-option
                        v-for="item in jsonOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="10" v-show="value === 'array'|| value === 'object'">
                <div class="ce-normal_item ce-form_item is-require">
                    <span>分组字段: </span>
                    <el-select
                        class="mr10 ce-form-select"
                        size="mini"
                        v-model="groupFieldName"
                        placeholder="请选择"
                        filterable
                        multiple
                        clearable
                        collapse-tags
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.label"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </el-col>
            <el-col :span="10" v-show="value === 'json'" class="ce-form_radio">
                <el-radio v-model="radio" label="true">转成json对象</el-radio>
                <el-radio v-model="radio" label="false">转成json字符串</el-radio>
            </el-col>
        </el-row>


        <div class="ce-plug_attr1">
            <CommonTable
                :data="ruleTableData"
                :columns="ruleHeader"
                height="100%"
                :pagination="false"
                :pagination-props="paginationProps"
                paging-type="client"
                :pagination-total="ruleTableData.size"
                @change-size="changeSize"
                @change-current="changePage($event)"
            >
                <template slot="header-ruleOperate">
                    <em class="icon ce_link f14 ml5" title="添加" @click="addRuleEdit">&#xe6e1;</em>
                </template>
                <template slot="columnCode" slot-scope="scope">
                    <div :title="scope.row.conversionKey">
                        <el-select
                            size="mini"
                            class="pct100"
                            v-model.trim="scope.row.conversionKey"
                            filterable
                            clearable
                            @change="selectKey(scope.row.conversionKey,scope.$index)"
                            :allow-create="scope.row.isConstant">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </template>
                <template slot="columnName" slot-scope="scope">
                    <div :title="scope.row.conversionValue">
                        <el-select
                            size="mini"
                            class="pct100"
                            v-model.trim="scope.row.conversionValue"
                            filterable
                            clearable>
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </template>
                <template slot="ruleOperate" slot-scope="scope">
                    <el-switch v-if="radio !== 'true' && value!=='object'"
                               v-model="scope.row.isConstant"

                               :title="scope.row.message"

                               class="switch_width mr10"
                               @change="changeTitle(scope.row)">
                    </el-switch>

                    <el-popconfirm
                        title="确定删除吗？"
                        @confirm="deleteFn(scope.row, scope.$index)"
                    >
                        <el-button type="text"
                                   slot="reference"
                        > 删除
                        </el-button>
                    </el-popconfirm>

                    <!-- <em class="icon ce_link p5"
                         v-for="( ico , key ) in ruleOperateIcon"
                         :key="key"
                         :title="ico.label"
                         v-html="ico.icon"
                         @click="ico.clickFn(scope.row , scope.$index)"
                     ></em>-->
                </template>
            </CommonTable>
        </div>

        <div class="ce-normal_item lh30  is-require">
            <span class="ce-form_title">输出字段: </span>
            <el-table
                :data="tableData"
                border
                style="width: 100%">
                <el-table-column
                    prop="columnCode"
                    label="字段名">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.columnCode"
                                  @keyup.native="scope.row.columnCode=scope.row.columnCode.replace(/[^a-zA-Z]/g,'')">
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="columnName"
                    label="字段中文名">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.columnName">

                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="columnType"
                    label="字段类型">
                    <template slot-scope="scope">
                        <dg-select size="mini" class="inputField"
                                   v-model="scope.row.columnType" :data="inputType">
                        </dg-select>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="length"
                    label="字段长度">
                    <template slot-scope="scope">
                        <el-input :disabled="inputDisable(scope.row.columnType)" v-model="scope.row.length"
                                  @keyup.native="typeCheck(scope.row.length , scope.row , 'length')">
                        </el-input>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";

export default {
    name: "ObjectToJsonDialog",
    mixins: [pluginMixins, servicesMixins],
    props: ['sourceData', 'rowData', 'loadParams', 'jsonOptions', 'options', 'ordinaryType', 'complexType', 'value1', 'groupFieldName1', 'tableData1'
        , 'ruleTableData1', 'radio1', 'inputType1'
    ],


    data() {
        return {
            tableData: [],
            ruleTableData: [],
            paginationProps: {
                currentPage: 1,
                pageSizes: [5, 10, 20, 30],
                pageSize: 5,
            },
            groupFieldName: this.groupFieldName1,
            //
            inputType: this.inputType1,
            radio: this.radio1,
            reg: /^[^A-Za-z_]|[^A-Za-z_0-9]+/,
            /*jsonOptions: [{
                label:"转成json",
                value:"json"
            },{
                label:"转成json数组",
                value:"array"
            }],
            inputType: [],
            tableData:[{
                columnName:"",
                columnCode:"",
                columnType:"",
                length:"",
            }],*/
            columns: [],
            value: this.value1,
            /*
            groupFieldName:"",
            options: [],*/
            inputMax: 14,
            canSave: false,
            isSeniorShow: {},
            results: [],
            addTxt: '点击添加字段处理',
            /*ruleTableData: [],*/
            ruleHeader: [
                {
                    label: 'key',
                    prop: 'columnCode',
                    align: 'center',
                    minWidth: '140px',
                    'show-overflow-tooltip': false
                }, {
                    label: 'value',
                    prop: 'columnName',
                    align: 'center',
                    minWidth: '140px',
                    'show-overflow-tooltip': false
                },
                {
                    label: '',
                    prop: 'ruleOperate',
                    align: 'center',
                    width: '140px',
                    fixed: "right",
                    'show-overflow-tooltip': false
                },
            ],
            ruleTypeOptions: [
                {}
            ],
            ruleOperateIcon: {
                delete: {
                    label: '删除',
                    icon: '&#xe65f;',
                    clickFn: this.deleteFn
                }
            },
            graphIds: [],
            isOutColumnChoose: true,
            /* complexType :[],
             ordinaryType : []*/
        }
    },
    methods: {
        checkColumn(val, type) {
            const vm = this;
            let reg = new RegExp(/^[\u4E00-\u9FA5]+$/);
            let strs = new Array(); //定义一数组
            strs = val.split(""); //字符分割
            let tem = "";
            strs.forEach((item, i) => {
                let n = Number(item);
                if (reg.test(item) || item === " " || (i === 0 && !isNaN(n))) {
                    tem += "";
                } else {
                    tem += item;
                }
            });
            if (type === "year") {
                vm.effectiveData.year = tem;
            } else if (type === "showTime") {
                vm.effectiveData.showTime = tem;
            } else {
                vm.effectiveData.num = tem;
            }
        },
        inputDisable(type) {
            let flag = false;
            for (let i = 0; i < this.inputType.length; i++) {
                let item = this.inputType[i];
                if (item.label === 'Object') {
                    if (item.value === type) {
                        this.tableData[0].length = 1;
                        flag = true;
                        return true;
                    }
                }
            }
            return false;
        },
        selectKey(conversionKey, index) {
            this.options.forEach(item => {
                if (conversionKey === item.value) {
                    this.ruleTableData[index].conversionValue = item.value;
                    return;
                }
            });
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.paginationProps.currentPage = 1;
        },
        jsonTypeChange() {
            this.groupFieldName = "";
            this.radio = "false";
            this.tableData[0].columnType = '';
            this.columnTypeCheck();
        },
        columnTypeCheck() {
            if (this.value === 'object') this.inputType = this.complexType;
            else this.inputType = this.ordinaryType;
            if (!this.tableData[0].columnType) this.tableData[0].columnType = this.inputType[0].value;

        },
        changeTitle(value) {
            value.message = value.isConstant ? 'key是常量' : 'key是变量';
            if (!value.isConstant) value.conversionKey = value.conversionValue;
        },
        typeCheck(val, row, target) {
            if (isNaN(val)) {
                isNaN(parseFloat(val)) ?
                    row[target] = '' :
                    row[target] = parseFloat(val);
                this.$message.warning('请输入整数');
            }
        },
        changePage(index) {
            this.paginationProps.currentPage = index;
        },
        deleteFn(row, index) {
            const vm = this, {pluginServices, pluginMock} = this;
            if (row.serviceOrgId !== null && row.serviceOrgId !== '' && row.serviceOrgId !== undefined) {
                // services.deleteGraph(row.serviceOrgId);
                vm.graphIds.push(row.serviceOrgId);
            }
            let num = (this.paginationProps.currentPage - 1) * this.paginationProps.pageSize + index;
            vm.ruleTableData.splice(num, 1);
            this.$message({
                type: 'success',
                message: '删除成功!'
            });

        },
        saveClose() {
            const vm = this;
            if (vm.value === 'array' || vm.value === 'object') {
                if (vm.groupFieldName.length === 0) {
                    vm.$message.warning("请选择分组字段!");
                    return false;
                }
            }
            if (vm.ruleTableData.length === 0) {
                vm.$message.warning("行数据转json插件未配置相关字段!");
                return false;
            }
            for (let k = 0; k < vm.ruleTableData.length; k++) {
                for (let key in vm.ruleTableData[k]) {
                    if (vm.ruleTableData[k][key] === '' || vm.ruleTableData[k][key] === undefined) {
                        if (key === 'conversionKey') {
                            vm.$message.warning("key不能为空!");
                            return false;
                        } else if (key === 'conversionValue') {
                            vm.$message.warning("value不能为空!");
                            return false;
                        }
                    }
                }
            }
            this.isOutColumnChoose = true;
            for (let key in this.tableData[0]) {
                if (key !== 'columnId') {
                    if (this.tableData[0].length === '0' && this.tableData[0].type !== 'Object') {
                        vm.$message.warning("输出字段长度必须大于0!");
                        return;
                    }
                    if (this.tableData[0][key] === '' || this.tableData[0][key] === 'undefined' || this.tableData[0][key] === null) {
                        this.isOutColumnChoose = false;
                    }
                }

            }
            if (!this.isOutColumnChoose) {
                vm.$message.warning("输出字段请填写完整!");
                return false;
            }
            let tableItem = [];
            vm.$emit('updateOutput', vm.value, vm.radio, vm.groupFieldName, vm.ruleTableData, vm.tableData);
            return true;
        },
        async saveFn() { //保存后才能预览
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            let serviceOrgOutputs = [];
            let groupColumnName = "";
            if (vm.value === 'array' || vm.value === 'object') {
                if (vm.groupFieldName.length === 0) {
                    vm.$message.warning("请选择分组字段!");
                    return;
                } else {
                    this.groupFieldName.forEach(element => {
                        groupColumnName = groupColumnName + element + ",";
                    });
                    if (groupColumnName.length != 0) {
                        groupColumnName = groupColumnName.substring(0, groupColumnName.length - 1)
                    }
                }
            }
            if (vm.ruleTableData.length === 0) {
                vm.$message.warning("行数据转json插件未配置相关字段!");
                return;
            }
            for (let k = 0; k < vm.ruleTableData.length; k++) {
                for (let key in vm.ruleTableData[k]) {
                    if (vm.ruleTableData[k][key] === '' || vm.ruleTableData[k][key] === undefined) {
                        if (key === 'conversionKey') {
                            vm.$message.warning("key不能为空!");
                            return;
                        } else if (key === 'conversionValue') {
                            vm.$message.warning("value不能为空!");
                            return;
                        }
                    }
                }
            }

            this.isOutColumnChoose = true;
            for (let key in this.tableData[0]) {
                if (key !== 'columnId') {
                    if (this.tableData[0][key] === '' || this.tableData[0][key] === 'undefined' || this.tableData[0][key] === null) {
                        this.isOutColumnChoose = false;
                    }
                }

            }
            if (!this.isOutColumnChoose) {
                vm.$message.warning("输出字段请填写完整!");
                return;
            }
            vm.ruleTableData.forEach((item, i) => {
                let serviceOrgOutput = {
                    conversionKey: item.conversionKey,
                    conversionValue: item.conversionValue,
                    isContent: item.isConstant ? 1 : 0 //1 是常量
                };
                // if (item.isConstant ) {
                //     for (let index = 0; index < this.options.length; index++) {
                //         const element = this.options[index];
                //         if (element.value === item.conversionKey) {
                //             serviceOrgOutput = {
                //                 conversionKey: element.label,
                //                 conversionValue: item.conversionValue,
                //                 isContent: item.isConstant ? 1 : 0 //1 是常量
                //             };
                //             break;
                //         }
                //     }
                // }
                serviceOrgOutputs.push(serviceOrgOutput);
            });

            let outColumnsArray = [];
            outColumnsArray.push(this.tableData[0]);
            let objectToJsonMeta = {
                conversionType: this.value,
                groupColumn: groupColumnName,
                cicadaConversionColumns: serviceOrgOutputs,
                cicadaOutColumns: outColumnsArray,
                jsonObj: JSON.parse(vm.radio)
            };
            settings.loading = true;
            services.objectToJsonSavePlugin(vm.sourceData.id, objectToJsonMeta, settings).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("保存成功!");
                    // this.initSettingSenior();
                }
            })
            //this.loadParams.loading = false;
        },
        initSettingSenior() {
            this.$refs.settingSenior.initValue();
        },
        previewFn() {
            this.$refs.preview.show(this.rowData, this.sourceData.id);
        },
        addRuleEdit() {
            this.ruleTableData.push({
                conversionKey: '',
                conversionValue: '',
                isConstant: true,
                message: "key是常量"
            })
        },
        getColumns() {
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            settings.loading = true;
            services.queryObjectJsonData(this.sourceData.id, settings).then(res => {
                let result = res.data.data;
                result.inputColumn.forEach(element => {
                    let selectObject = {
                        label: element.columnName || element.columnCode,
                        value: element.columnCode,
                    };
                    this.options.push(selectObject);
                });
                result.types.forEach(element => {
                    if (element.label !== 'Object') this.ordinaryType.push(element);
                    else this.complexType.push(element);
                });
                if (result.pluginMeta.cicadaConversionColumns.length !== 0 && result.pluginMeta.cicadaOutColumns.length !== 0 && result.pluginMeta.conversionType !== null) {
                    this.value = result.pluginMeta.conversionType;
                    if (result.pluginMeta.groupColumn !== '') this.groupFieldName = result.pluginMeta.groupColumn.split(",");
                    this.tableData = result.pluginMeta.cicadaOutColumns;
                    result.pluginMeta.cicadaConversionColumns.forEach(element => {
                        element.isConstant = element.isContent === '0' ? false : true;
                        element.message = element.isContent === '0' ? 'key是变量' : 'key是常量';
                    });
                    this.ruleTableData = result.pluginMeta.cicadaConversionColumns;
                }
                this.radio = result.pluginMeta.jsonObj ? "true" : "false";
                this.columnTypeCheck();
            });
        },
        getSelectOptions() {
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = vm.getServices(pluginServices, pluginMock);
            settings.loading = true;
            this.jsonOptions = [];
            services.getObjectJsonSelectData(settings).then(res => {
                let result = res.data.data;
                result.forEach(element => {

                    let object = {
                        label: element.name,
                        value: element.code
                    };
                    this.jsonOptions.push(object);
                });
            })
        },

    },
    created() {
        // this.init(this.sourceData);
        /* this.getSelectOptions();
         this.getColumns();*/
        this.ruleTableData = [];
        this.ruleTableData1.forEach(item => {
            this.ruleTableData.push({
                conversionKey: item.conversionKey,
                conversionValue: item.conversionValue,
                isConstant: item.isConstant,
                isContent: item.isContent,
                message: item.message
            });
        });
        this.tableData = [];
        this.tableData1.forEach(item => {
            this.tableData.push({
                columnCode: item.columnCode,
                columnName: item.columnName,
                columnType: item.columnType,
                length: item.length
            });
        });
        this.columnTypeCheck();
    },
}
</script>

<style scoped>
.switch_width {
    width: 30px;
}

.ce-form_item {
    display:flex;
    align-items: center;
    justify-content: flex-start;
}

.ce-normal_item {
    text-align: left;
}

.ce-plug_cont {
    padding: 0 8px;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ce-normal_item.is-require::before {
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
    //line-height: 30px;
}

.ce-plug_attr1 {
    flex: 1;
    height: calc(100% - 270px);
    margin: 10px 0;
    box-sizing: border-box;
    overflow: auto;
}

.ce-add_b {
    line-height: 200px;
    border: 3px dashed #ddd;
    cursor: pointer;
    text-align: center;
    color: #999;
    font-size: 18px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
}

.ce-form_title {
    color: rgba(0, 0, 0, .65);
}

.ce-form_radio {
    line-height: 2rem;
}
</style>
