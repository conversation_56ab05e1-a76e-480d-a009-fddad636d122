<template>
    <div class="RelationBase">
        <common-dialog
                class="test-dialog"
                title="订阅测试"
                width="80%"
                height="80vh"
                :visible.sync="visible"
                @closed="clear_data"
        >
            <!--参数配置弹窗内容页-->
            <div class="height100">
                <el-row  class="height100" :gutter="20" v-loading="settings.loading">
                    <el-col :span="12" style="border-right:1px solid #555;overflow-y: auto;overflow-x: hidden;height:100%;">
                        <div style="height:330px;">
                            <el-form ref="form" :model="form" label-width="110px" label-position="left" class="height90">
                                <el-form-item label="API名称：">
                                    <span class="wordStyle">
                                        {{form.interfaceChineseName}}
                                    </span>
                                </el-form-item>
                                <!--<el-form-item label="请求路径：">-->
                                    <!--<span class="wordStyle">-->
                                        <!--{{form.requestPath}}-->
                                    <!--</span>-->
                                <!--</el-form-item>-->
                                <el-form-item label="订阅接口路径：">
                                    <span class="wordStyle">
                                        {{$axios.defaults.baseURL}}/publish/subscribe
                                    </span>
                                </el-form-item>
                                <el-form-item label="订阅Url：">
                                    <span class="wordStyle">
                                        {{form.relativePath}}
                                    </span>
                                </el-form-item>
                                <el-form-item label="请求参数">
                                    <common-table
                                            :data="requestTableData"
                                            :columns="requestTHeadData"
                                            :pagination="false"
                                            class="width100"
                                            :border="false"
                                            row-key="paramName"
                                            default-expand-all
                                    >
                                        <template slot="paramName" slot-scope="scope">
                                            <span style="color:red;" v-if="scope.row.isMust ==='t' || scope.row.is_must==='t'">*</span>
                                            {{['数据查询','信息核查'].includes(row.serviceType) ? scope.row.paramCode : scope.row.paramName}}
                                        </template>
                                        <template slot="defaultValue" slot-scope="scope">
                                            <dg-button v-if="scope.row.type === 'List' && scope.row.paramCode != 'params'" @click="setData(scope.row)">设置数据</dg-button>
                                            <div v-else>
                                                <el-date-picker
                                                        type="date"
                                                        v-model="scope.row.defaultValue"
                                                        v-if="scope.row.type === 'Time' && scope.row.paramCode ==='startTime'"
                                                        value-format="yyyy-MM-dd"
                                                        :picker-options="pickerOptionsStart"
                                                        @change="changeStartTime"
                                                >
                                                </el-date-picker>
                                                <el-date-picker
                                                        type="date"
                                                        v-model="scope.row.defaultValue"
                                                        v-else-if="scope.row.type === 'Time' && scope.row.paramCode ==='endTime'"
                                                        value-format="yyyy-MM-dd"
                                                        :picker-options="pickerOptionsEnd"
                                                >
                                                </el-date-picker>
                                                <el-input  v-input-limit:trim v-model="scope.row.defaultValue" @input="changeInput($event)" v-else-if="scope.row.showInput"></el-input>
                                            </div>
                                        </template>
                                    </common-table>
                                </el-form-item>
                            </el-form>
                        </div>
                    </el-col>
                    <el-col :span="12" style="height:100%;" >
                        <div>
                            <el-radio-group v-model.sync="showStyle">
                                <el-radio-button v-for="(item, key) in tabData" :label="item.value" :value="item.value">{{item.label}}</el-radio-button>
                            </el-radio-group>
                            <el-form ref="form" :model="form" label-width="100px" label-position="top" class="ce-form__m0" v-if="showStyle === 'text'">
                                <el-form-item label="响应">
                                    <el-input
                                            type="textarea"
                                            :autosize="{ minRows: 9, maxRows: 10}"
                                            resize="none"
                                            v-model="form.response">
                                    </el-input>
                                </el-form-item>
                            </el-form>
                            <!-- 表格展示 -->

                            <common-table
                                    :data="data"
                                    :columns="tHeadData"
                                    :pagination="pageFlag"
                                    :paging-type="'server'"
                                    :pagination-props="paginationProps"
                                    @change-current="changePage($event) "
                                    height="300px"
                                    class="width100"
                                    :border="false"
                                    v-else
                            >
                            </common-table>
                        </div>
                    </el-col>
                </el-row>
                <setData ref="setData"></setData>
            </div>
            <div slot="footer">
                <el-button size="mini" @click="clear_data">取消</el-button>
                <el-button size="mini" type="primary" @click="subscribe">订阅测试</el-button>
            </div>
        </common-dialog>
    </div>
</template>

<script>
    import {commonMixins} from "@/api/commonMethods/common-mixins";
    import setData from './setData'
    export default {
        name: "subscribeDialog",
        mixins : [commonMixins],
        components: {setData},
        data() {
            return {
                visible: false,
                row : {},
                showStyle : "text",
                tabData : [{
                    value : "text",
                    label : "文本"
                },{
                    value : "table",
                    label : "表格"
                }],
                form:{},
                requestTableData:[],
                requestTHeadData: [{
                    prop: "paramName",
                    label: "前端参数名",
                    minWidth: "",
                },{
                    prop: "defaultValue",
                    label: "值",
                    minWidth: "",
                },],
                tHeadData : [],
                data : [],
                pageFlag: false,
                selectTable: {},
                pickerOptionsStart:{   //禁用当前日期之前的日期
                    disabledDate(time) {
                        //Date.now()是javascript中的内置函数，它返回自1970年1月1日00:00:00 UTC以来经过的毫秒数。
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                },
                pickerOptionsEnd:{   //禁用当前日期之前的日期
                    disabledDate:(time)=>{
                        let startTime =  (this.requestTableData.find(n=>n.paramCode == "startTime") || {}).defaultValue;
                        let endTime = startTime ? new Date(startTime).getTime() : '';
                        if(endTime){
                            return (time.getTime()) <= endTime;
                        }
                        else{
                            return time.getTime() < Date.now() - 8.64e7;
                        }
                    },
                },
            }
        },
        methods:{
            clear_data(){
                this.visible = false;
                this.form.response = '';
                this.data = [];
                this.tHeadData = [];
            },
            show(row){
                this.row = row;
                this.visible = true;
                this.init();
            },
            //获取详情
            getModelServicesInfo(){
                let vm = this;
                let services = vm.$services("servicesServices");
                vm.requestTableData = [];
                services.queryServiceDetails(vm.row.serviceMetaId).then(res => {
                    if (res.data.status === 0) {
                        res.data.data.paramList = res.data.data.paramList || [];
                        vm.requestTableData = res.data.data.paramList.sort((a,b)=>{return b.paramCode.localeCompare(a.paramCode)}).filter(n=>{ return n.paramCode != 'requestId'});
                        vm.requestTableData.forEach(n=>{
                            vm.setParamName(n);
                        })
                        vm.form.relativePath = res.data.data.relativePath;
                        let operatorDataSet = vm.requestTableData.find(n=>n.paramCode === "operatorDataSet");
                        //vm.requestTableData.find(n=>n.paramCode === "requestId").showInput = true;
                        vm.requestTableData.find(n=>n.paramCode === "startTime").showInput = true;
                        vm.requestTableData.find(n=>n.paramCode === "endTime").showInput = true;
                        if(operatorDataSet && operatorDataSet.children){
                            operatorDataSet.children.forEach(n=>{
                                if(n.type === 'List'){
                                    n.childrenCopy = n.children ? n.children : [];
                                    n.children = [];
                                    n.setDataList = [];
                                    let obj ={};
                                    n.childrenCopy.forEach(h=>{
                                        if(h.defaultValue){
                                            obj[h.paramCode] = h.defaultValue;
                                        }
                                    })
                                    n.setDataList.push(obj)
                                }
                            })
                        }
                        let params = vm.requestTableData.find(n=>n.paramCode === "params");
                        if(params && params.children){
                            params.children.forEach(n=>{
                                n.showInput = true;
                            })
                        }
                    }
                })
            },
            //模型分析中设置数据
            setData(row){
                this.$refs.setData.show(row)
            },
            //设置paramName
            setParamName(info){
                info.paramName = info.paramCode || info.paramName || info.name;
                info.paramcode = info.paramCode || info.code;
                info.defaultValue = info.type === 'Time' && info.example ?  new Date(info.example) : info.example ;
                if(info.children && info.children.length){
                    info.children.forEach(n=>{
                        this.setParamName(n);
                    })
                }
            },
            init(){
                this.form = JSON.parse(JSON.stringify(this.row)) ;
                this.form.response = '';
                if (this.row.paramList) {
                    this.row.paramList.forEach(element => {
                        element.defaultValue = element.example;
                        element.showInput = true;
                    });
                }
                //获取详情
                this.getModelServicesInfo();
            },
            setModelResultData(res){
                const vm = this;
                vm.data = [];
                vm.form.response = JSON.stringify(res);
                vm.data = [res.data] || [];
                for (const key in vm.data[0]) {
                    vm.tHeadData.push({
                        prop : key,
                        label : key
                    })
                }
            },
            validationModel(info){
                let flag = false;
                if(info.type === 'List' && info.paramCode != 'params'){
                    let par = info.childrenCopy.find(n => n.isMust == 't' || n.is_must == 't');
                    if(info.setDataList && info.setDataList.length){
                        flag = false;
                    }
                    else{
                        flag = par ? true : false;
                    }
                }
                else{
                    if (info.paramCode != "operatorDataSet" && (info.isMust === 't' || info.is_must === 't') && !info.defaultValue){
                        flag = true;
                    }
                    else if(info.children && info.children.length){
                        for(let i = 0; i< info.children.length; i++){
                            flag = this.validationModel(info.children[i]);
                            if(flag) break
                        }
                    }
                }
                return flag;
            },
            //订阅
            subscribe(){
                const vm = this, {settings} = this;
                let flag = false;
                //判断请求数据是否必填项已填
                for(let i = 0; i< vm.requestTableData.length; i++){
                    flag = vm.validationModel(vm.requestTableData[i]);
                    if(flag) break
                }

                if (flag) {
                    this.$message.warning("请填写请求参数的值信息");
                    return;
                }
                this.tHeadData = [];
                this.data = [];
                let ser = vm.$services("serviceManage");
                let dataSet = vm.requestTableData.find(n=>n.paramCode == "operatorDataSet");
                let operatorDataSet = {};
                if(dataSet && dataSet.children && dataSet.children.length){
                    dataSet.children.forEach(n=>{
                        operatorDataSet[n.paramCode] = n.setDataList ? [...n.setDataList] : [];
                    })
                }
                let par = {
                    url:vm.form.relativePath,
                    //requestId:vm.requestTableData.find(n=>n.paramCode == "requestId").defaultValue,
                    operatorDataSet:operatorDataSet,
                    params:vm.requestTableData.find(n=>n.paramCode == "params") ? vm.requestTableData.find(n=>n.paramCode == "params").children.map(n=>{return { paramCode: n.paramCode, paramValue: n.defaultValue}}) ||[] : [],
                    startTime : vm.requestTableData.find(n=>n.paramCode == "startTime").defaultValue,
                    endTime : vm.requestTableData.find(n=>n.paramCode == "endTime").defaultValue,
                }
                settings.loading = true;
                ser.serviceSubscribe(par, settings).then(res => {
                    if (res.data.status === 0) {
                        let parInfo = res.data;
                        vm.setModelResultData(parInfo);
                        if(parInfo.data.requestId)
                            vm.$emit('getAsyncInfo',parInfo.data.requestId);
                    }
                })
            },

            changeStartTime(val){
                let time = this.requestTableData.find(n=>n.paramCode == "endTime");
                let endTime =  new Date(time.defaultValue).getTime();
                if(new Date(val).getTime() >= endTime){
                    time.defaultValue = '';
                }
            }
        },
    }
</script>

<style scoped lang="less">
/deep/ .el-date-editor{
    width:280px;
}
</style>