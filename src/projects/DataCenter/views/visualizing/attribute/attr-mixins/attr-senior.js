export const attrSenior = {
    data() {
        return {
            refresh: {
                opened: false,
                label: '自动刷新',
                value: 0,
                placeholder: '请输入刷新频率',
                selectValue: 1000,
                options:
                    [{
                        label: '秒',
                        value: 1000
                    }, {
                        label: '分钟',
                        value: 60 * 1000
                    }, {
                        label: '小时',
                        value: 60 * 60 * 1000
                    }, {
                        label: '天',
                        value: 24 * 60 * 60 * 1000
                    }, {
                        label: '月',
                        value: 30 * 24 * 60 * 60 * 1000
                    }]
            },
            tip: ' 0 为固定数据不自动刷新',
            linkage: {
                label: "联动",
                btnTxt: "编辑",
            },
            jumpLink: {
                label: "跳转",
                btnTxt: "添加跳转",
                editTxt: "编辑跳转",
                deleteTxt: "清除跳转",
            },
            relation: null,
            setData: false,
            chartData: {},
            jumpInfo: null,
        }
    },
    props: {
        node: Object,
        dir_dataSetId: String,
        nodeList: Array
    },
    methods: {
        setChartData(chartD) {
            this.chartData = chartD;
        },
        setRelation() {
            const vm = this;
            let nodeData = vm.chartData;
            let widget = JSON.parse(vm.node.widget), node = JSON.parse(JSON.stringify(vm.node));
            widget.beforeData = JSON.stringify(nodeData);
            node.widget = JSON.stringify(widget);
            if (nodeData.classifierStatId || vm.dir_dataSetId) {
                let datasetId = nodeData.classifierStatId || vm.dir_dataSetId;
                vm.$refs.relation.show(vm.nodeList, node, vm.relation, datasetId);
            } else {
                vm.$message.info("请选择数据集");
            }
        },
        typeCheck(val) {
            if (val === '' || isNaN(val)) {
                this.refresh.value = 0;
                this.$message.warning('请输入数字')
            }
        },
        getSeniorData() {
            const {refresh, relation, jumpInfo} = this;
            return {
                refreshVal: refresh.value,
                refreshUnit: refresh.selectValue,
                isRefresh: refresh.opened,
                linkage: relation,
                jumpInfo: jumpInfo,
            };
        },
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.setData = false;
                if (!vm.node.widget) return;
                let {beforeData, linkage} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
                nodeData.isRefresh !== undefined ? vm.refresh.opened = nodeData.isRefresh : true;
                nodeData.refreshVal ? vm.refresh.value = nodeData.refreshVal : true;
                nodeData.refreshUnit ? vm.refresh.selectValue = nodeData.refreshUnit : true;
                linkage ? vm.relation = JSON.parse(linkage) : true;
                nodeData.jumpInfo ? vm.jumpInfo = nodeData.jumpInfo : true;
                vm.setData = true;
            })
        },
        storeRelation(data) {
            this.relation = data;
            this.$emit("renewAllData");
        },
        setJump() {
            const vm = this;
            let nodeData = vm.chartData;
            let widget = JSON.parse(vm.node.widget), node = JSON.parse(JSON.stringify(vm.node));
            widget.beforeData = JSON.stringify(nodeData);
            node.widget = JSON.stringify(widget);
            vm.$refs.jump.show(vm.nodeList, node, JSON.parse(JSON.stringify(vm.jumpInfo)));
        },
        storeJump(data) {
            this.jumpInfo = data;
            this.$emit("renewAllData");
        },
        clearJump(){
            const vm = this;
            vm.confirm("确定清除跳转吗", `清除跳转`, () => {
                this.jumpInfo = null;
                this.$emit("renewAllData");
                vm.$message.success("清除成功!");
            })
        },
    },
    watch: {
        refresh: {
            handler(val) {
                const vm = this;
                if (this.node && this.setData) {
                    vm.$emit('nodeDataRenew', this.node.id);
                }
            },
            deep: true
        },
        relation: {
            handler(val) {
                const vm = this;
                if (this.node && this.setData) {
                    vm.$emit('nodeDataRenew', this.node.id);
                }
            },
            deep: true
        },
        jumpInfo: {
            handler(val) {
                const vm = this;
                if (this.node && this.setData) {
                    vm.$emit('nodeDataRenew', this.node.id);
                }
            },
            deep: true
        }
    }
}