<template>
    <div class="height100">
        <common-table
                :data="tableData"
                :columns="tHeadData"
                tooltip-effect="light"
                cell-class-name="ce-ellipsis"
                size="mini"
        ></common-table>
    </div>
</template>

<script>
    import CommonTable from '@/components/common/CommonTable'

    export default {
        name: "model",
        components: {
            CommonTable
        },
        props: {
            res : Object,
        },
        data() {
            return {
                tableData: [],
                tHeadData: [
                    {
                        label: '模型名称',
                        prop: 'name',
                        align: 'center',
                    }, {
                        label: '创建人',
                        prop: 'operateUserName',
                        align: 'center'
                    },{
                        label : '更新时间',
                        prop : 'operateTime'
                    }
                ]
            }
        },
        methods: {
            init() {
                this.tableData = this.res.useCases;
            }
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>

</style>