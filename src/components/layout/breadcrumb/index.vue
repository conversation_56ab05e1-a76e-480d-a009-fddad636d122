<template>
    <el-breadcrumb class="app-breadcrumb" :separator="separator">
        <transition-group name="breadcrumb">
            <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
                <span v-if="item.redirect === 'noredirect' || index === levelList.length - 1" class="no-redirect">{{
                    generateTitle(item.meta.title)
                }}</span>
                <!--<a v-else-if="item.meta.linked" @click.prevent="handleLink(item)" >{{ generateTitle(item.meta.title) }}</a>-->
                <span v-else >{{ generateTitle(item.meta.title) }}</span>
            </el-breadcrumb-item>
        </transition-group>
    </el-breadcrumb>
</template>

<script>
import pathToRegexp from "path-to-regexp";

export default {
    data() {
        return {
            levelList: null
        };
    },
    watch: {
        $route() {
            this.getBreadcrumb();
        }
    },
    props : {
        // 面包屑分割符
        separator: {
            type: String,
            default: "/"
        }
    },
    created() {
        this.getBreadcrumb();
    },
    methods: {
        generateTitle(title){
            return title;
        },
        getBreadcrumb() {
            let lastAry = null;
            let matched = this.$route.matched;
            let matchAry = [];
            matched.reduce((last,prev) =>{
                if(last.path === prev.path){
                    if(!last.name || !prev.name){
                        last.name?matchAry.push(last):matchAry.push(prev)
                    }
                }
                last = prev
            });
            const first = matched[0];
            lastAry = matchAry.length > 0? matchAry:matched;

            this.levelList = lastAry.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false);
        },
        pathCompile(path) {
            // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561
            const { params } = this.$route;
            var toPath = pathToRegexp.compile(path);
            return toPath(params);
        },
        handleLink(item) {
            const { redirect, path } = item;
            if (redirect) {
                this.$router.push(redirect);
                return;
            }
            this.$router.push(this.pathCompile(path));
        }
    }
};
</script>
<style scoped>
    .app-breadcrumb.el-breadcrumb {
        height: 48px;
        line-height: 48px;
    }
</style>
