# vue-com-project

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for standard

标准版

```
npm run build:standard
```

### Run your production

```
npm run build:prod
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

```
数据中心 页面 有 用到下拉树<ce-select-drop></ce-select-drop> 统一都改成  <dg-tree-drop></dg-tree-drop> ，绑定值不变。

serverConfig.json  配置文件

  "production" 生产环境后端服务地址,
  "test": 测试环境后端服务地址,
  "dev": 开发环境后端服务地址,
  "isMock" : 是否使用mock数据 ,
  "uploadFileLimit" : 文件上传限制 ,
  "roleCode" : 系统管理员角色code,
  "userGroupCode" : 系统管理员用户组code,
  "userCode" : 系统管理员用户code,
  "pgis_url" : pgis 地图服务地址 , 天津  "http://17.22.40.34/QuadServer/maprequest?services=tj_gd&level={z}&col={x}&row={y}&rowModel=d"  青海 "http://10.192.62.8:8080/EzServer717/Maps/shiliang/EzMap?Service=getImage&Type=RGB&ZoomOffset=0&Col={x}&Row={y}&Zoom={z}&V=0.3&key=text"
  "pgis_type" : "openStreetMapConfig", 天津 "QuadServerMapConfig"  青海 "fangzhengMapConfig"
  "map_area_code" : 地图 显示范围的地区行政编码,  天津  ["120000"]  青海 ["630000"]
  "map_center": pgis 地图中心经纬度, 天津 [39.12, 117.2] 青海 [36.620901 , 101.780199]
  "map_corner": pgis 地图范围经纬度, 天津 [[38.554824, 116.702073], [40.251765, 118.059209]] 青海 [ [ 31.600668 , 89.401764 ] , [39.212599 , 103.068897] ]
  "systemName" : 系统名称,
  "marketName" : "大数据模型市场", 模型市场名称
  "layoutBg" : "#365289" , 系统外框 背景色
  "layoutColor" : "#fff" 系统外框 字体颜色
  "hideMarket": false  隐藏模型市场
  "hideAI" : false 隐藏AI建模相关信息
  "fileUploadLimit" : false  [Boolean Number] ,false 则没有限制，数值 10 是限制10M ,
  "routerMenu" :["home" , "modeling" , "visualSpace" , "serviceSpace" , "dataSpace" , "management" , "caseScenario"] 配置展示的菜单,对应的内容 主页、建模空间、可视、服务、数据、管理、场景案例
  "pluginPanel" : {
    "ComponentPanel" : "组件面板",
    "DatasetPanel":"数据集",
    "dataConnectionDataWarehouse":"数据仓库",
    "modelServe":"模型服务"
   }, // 配置建模 插件面板菜单名
   "marketModelTypes": ["TRANS","SCRIPT","DASHBOARDS","PORTAL"], // 配置模型市场 模型类型  ： 数据模型、AI模型、仪表盘、模型主题
   "showVersionAndFirm": false, // 建模 显示 开发商和版本
   "datasetLinkUrl": true, // 数据集、插件 预览显示跳转 档案 布控链接
   "linkFieldKeys":{ // 跳转布控需要 姓名字段 按顺序取值，获取有值即停，否则为空
    "person": ["姓名","xm"]
  }
iconfont3 文件更新时候，需要对文件夹里面 iconfont.js  
搜索 fill="#515151" 和 fill="#000000"  替换为空
```

### 可视化方案  链接 分享

例： 前端环境  http://*************:8080
可视化分享链接 {方案id}、{用户id}、{userToken} 用具体指代替 , 密码是密文的
1、  http://*************:8080/datacenter/visualview?rowid={方案id}&userId={用户id}
2、  http://*************:8080/datacenter/visualview?rowid={方案id}&userCode={用户code}&passWord={用户密码}
3、  http://*************:8080/datacenter/visualview?rowid={方案id}&userToken={userToken} // 常德现场使用，需要后端 loginWay 配置为 2

### 建模方案 模型分享查看链接  使用的 dc_super 登录查看
1、http://*************:8080/datacenter/modelView?transId=cc0215b0966843ce9dcf99edd059b012

### 第三方系统嵌入魔方 使用的链接
1、http://*************:8080/datacenter/jumpView?userName={}&password={密码}
