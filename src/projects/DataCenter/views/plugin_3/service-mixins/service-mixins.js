import {commonMixins} from "@/api/commonMethods/common-mixins";
export const servicesMixins = {
    mixins : [commonMixins],
    data(){
        const {isMock} = this;
        let pluginServices = require("@/projects/DataCenter/services/plugin-services_3.0/plugin-services3") ,
            pluginMock = isMock ? require("@/projects/DataCenter/mock/plugin-mock_3.0/plugin-mock3"):{};
        return {
            pluginServices ,
            pluginMock ,
            services : this.getServices(pluginServices , pluginMock)
        }
    }
}
