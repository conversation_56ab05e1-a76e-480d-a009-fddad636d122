<template>
    <div class="RelationBase">
        <common-dialog
            class="params-dialog"
            :title="sortTitle"
            :width="width"
            :visible.sync="visible"
            @closed="clear_data"
            v-loading="loading"
        >
            <!--参数配置弹窗内容页-->
            <params-page v-if="showParams"></params-page>
            <!--
            弹窗按钮
            <div slot="footer">
                <el-button @click="closeEditDialog" size="mini">关闭</el-button>
            </div>
            -->
        <div slot="footer">
            <el-button size="mini" @click="visible = false">取消</el-button>
            <el-button size="mini" type="primary">确定</el-button>
        </div>
        </common-dialog>
    </div>
</template>

<script>
import paramsPage from "./conds/index"
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin/service-mixins/service-mixins";


export default {
    name: "dataSort",
    mixins : [pluginMixins , servicesMixins],
    components: {paramsPage},
    props:{
        sortTitle:String,
        numList:Array,
        plug_data:Object,
        fieldOpt : Array,
        sortOpt : Array,
        streamingData: Array
    },
    data(){
        return {
        }
    },
    methods: {
        save() {

        },
        preview() {

        }
    }
}
</script>

<style scoped>
</style>
