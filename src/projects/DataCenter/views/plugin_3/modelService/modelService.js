

import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin/service-mixins/service-mixins";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"

export default {
    name: "modelService",
    mixins : [pluginMixins , servicesMixins],
    components: {preView},
    props:{
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    data(){
        return {
            inputValueTable : "",
            wayOption: [
                {label: "按字段名", value: "byCode"},
                {label: "按字段中文名", value: "byName"}
            ],
            mappingTxt: "自动映射",
            tableData: [],
            tHeadData: [
                {
                    prop: "featureColumnCode",
                    label: "特征列",
                    minWidth: 160,
                    align: "left"
                },
                {
                    prop: "featureColumnName",
                    label: "特征列中文名",
                    minWidth: 120,
                    resizable: false
                },
                {
                    prop: "featureColumnType",
                    label: "特征列类型",
                    minWidth: 120,
                    align: "center"
                },
                {
                    prop: "inputColumnCode",
                    label: "输入字段名",
                    minWidth: 140,
                    align: "center",
                },
                {
                    prop: "inputColumnName",
                    label: "输入字段中文名",
                    minWidth: 90,
                    align: "center",
                },
            ],
            options : [],
            codeOptions : [],
            mappingWay: "byCode",
            total: 0,
            originalData : [],
            serviceId :"",
            serviceClassify : "",
            serviceType : "",
        }
    },
    computed : {
        mapLabel(){
            return `${this.mappingTxt} (${this.wayOption.find(opt => opt.value === this.mappingWay).label})`;
        },
    },
    watch: {
        inputValueTable(val) {
            this.filterTableMethod(val);
        }
    },
    methods: {
        filterTableMethod(text) {
            this.tableData = text ? this.originalData.filter(list =>
                    list.featureColumnCode.indexOf(text) > -1 ||
                    list.featureColumnName.indexOf(text) > -1 )
                : [...this.originalData];
        },
        /**
         * 输入字段名变更事件
         * @param {} row 
         * @param {*} val 
         */
        codeChange(row, val) {
            row.inputColumnName = this.codeOptions.find(item => item.value === val).name;
        },
        wayChange(val) {
            if (this.mappingWay !== val) this.outMappingColumn(val);
        },
        async init(){
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = this.$services("plugin3"); 
            settings.loading = true;
            this.tableData = [];
            this.originalData = [];
            await services.queryLocalModelServiceData(this.sourceData.id, vm.sourceData.keyWord, settings).then(res => {
                if (res.data.status === 0) {
                    if (res.data.data.pluginMeta.cicadaModelServiceInputs.length > 0) {
                        this.tableData = JSON.parse(JSON.stringify(res.data.data.pluginMeta.cicadaModelServiceInputs));
                        this.originalData = JSON.parse(JSON.stringify(res.data.data.pluginMeta.cicadaModelServiceInputs));
                    } else {
                        this.originalData = res.data.data.serviceInput;
                        res.data.data.serviceInput.forEach(item => {
                            this.tableData.push({
                                ... item,
                                code: item.code,
                                name: item.name,
                                data_type: item.data_type,
                                inputColumnCode: "",
                                inputColumnName: "",
                            });
                        })
                    }
                    
                    res.data.data.inputColumns.forEach(element => {
                        this.codeOptions.push({
                            label : element.columnName,
                            value : element.columnName,
                            name : element.columnZhName
                        })
                        this.options.push({
                            label : element.columnZhName,
                            value : element.columnZhName,
                            code : element.columnName,
                        });
                    });
                    this.serviceId = res.data.data.pluginMeta.serviceId; 
                    vm.serviceClassify = res.data.data.pluginMeta.serviceClassify;
                    vm.serviceType = res.data.data.pluginMeta.serviceType;
                }
            })
            if (!this.tableData[0].inputColumnCode) this.outMappingColumn();
        },
        outMappingColumn(val){
            const vm = this;
            if(typeof val === "string") vm.mappingWay = val;
            this.mappingField();
        },
        mappingField(numList) {
            const vm = this, { mappingWay} = vm;
            if (mappingWay === "byCode") {
                vm.tableData.forEach(item =>{
                    item.inputColumnCode = "";
                    item.inputColumnName = "";
                    vm.codeOptions.forEach(code=>{
                        if (code.value.toLowerCase() === item.featureColumnCode.toLowerCase()) {
                            item.inputColumnCode = code.value;
                            item.inputColumnName = vm.options.find((i) => i.code === code.value).value;
                        }
                    })
                })
            } else {
                vm.tableData.forEach(item =>{
                    item.inputColumnCode = "";
                    item.inputColumnName = "";
                    vm.options.forEach(code=>{
                        if (code.value && code.value.toLowerCase() === item.featureColumnCode.toLowerCase()) {
                            item.inputColumnName= code.value;
                            item.inputColumnCode = vm.codeOptions.find((i) => i.name === code.value).value;
                        }
                    })
                })
            }
            return numList;
        },
        save(fn,showTip) {
            const vm = this, {settings} = this;
            let services = this.$services("plugin3"); 
            settings.loading = true;
            let params = {
                cicadaModelServiceInputs : this.tableData,
                serviceId : this.serviceId,
                serviceClassify : this.serviceClassify,
                serviceType : this.serviceType,
            }
            services.saveModelServicePlugin(this.sourceData.id, params, settings).then(res => {
                if (res.data.status === 0) {
                    showTip && this.$message.success("保存成功");
                    vm.$emit("setPreviewAble");
                    fn && fn();
                }
            })
        },
        view(){
            const vm = this, {pluginServices, pluginMock, settings} = this;
            let services = this.$services("plugin3"); 
            settings.loading = true;
            services.saveModelServicePlugin(this.sourceData.id, params, settings).then(res => {
                if (res.data.status === 0) {
                    this.$message.success("保存成功");
                }
            })
        },
        preview() {
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.preview.show(this.rowData, this.sourceData);
            });
            
        },
    },
    mounted() {
        this.init();
    },
}
