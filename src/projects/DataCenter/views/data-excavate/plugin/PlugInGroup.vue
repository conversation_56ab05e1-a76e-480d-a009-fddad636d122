<template>
    <ul class="plugInGroup" ref="plugGroup" @mouseleave="hideSlideIcon">
        <div class="groupN" v-if="plugInData.plugList.length">{{plugInData.groupName}}</div>
        <li class="module-icon"
            v-for="(plug , index) in storeList"
            :key="index"
            @mouseenter="getChildPos( $event , index )"
            @mouseleave="visibleChange(index)"
        >
            <div :draggable="!(plug.children && plug.children.length)" @dragstart="addNode(plug)" @dragend="dragend">
                <div class="m-icon">
                    <svg class="eda_icon" >
                        <use :xlink:href="plug.icon"></use>
                    </svg>
                </div>
                <p class="m-name" :title="plug.label">{{plug.label}}</p>
            </div>
            <PlugDetail v-if="plug.detail" :detail="plug.detail" :arrowP="plug.arrowPos" :marginL="plug.marginL"/>
            <div class="child-arrow" v-if="plug.children && plug.children.length">
                <i class="el-icon-caret-bottom"></i>
                <ChildPlugIn :childData="plug.children" :overLeft="overLeft" :overRight="overRight" v-if="childI === index" />
            </div>
        </li>
        <div class="model-a" v-if="plugInData.canSlide" @click="showSlideIcon"></div>
        <div class="group-line" v-if="plugInData.plugList.length"></div>
        <SlidePlugIn v-if="plugInData.canSlide" :plugList="slideList" :showSlide="showSlide"/>
    </ul>
</template>

<script>
    import PlugDetail from './PlugDetail'
    import ChildPlugIn from './ChildPlugIn'
    import SlidePlugIn from './SlidePlugIn'
    import $ from 'jquery'
    import { globalBus } from '@/api/globalBus';
    export default {
        name: "PlugInGroup",
        props: {
            plugInData: Object,
            addIcon: Boolean ,
            removeIcon : Boolean
        },
        data() {
            return {
                isOver: false,
                slideList: [],
                storeList: [],
                storeLen: 5 ,
                showSlide : false,
                childI : '',
                overLeft : false ,
                overRight: false
            }
        },
        components: {
            PlugDetail,
            ChildPlugIn,
            SlidePlugIn
        },
        methods: {
            dragend(){
                globalBus.$emit('dragend');
            },
            addNode(plug){
                globalBus.$emit('plugNode', plug);
            },
            getChildPos(e , i ) {
                const vm = this;
                vm.childI = i;
                vm.$nextTick(()=>{
                    let target = e.currentTarget,
                        parent = target.parentNode,
                        screenW = $(window).width(),
                        left = target.offsetLeft + parent.offsetLeft,
                        childW = $(target).find('.childPlugIn').outerWidth();
                    screenW - left < childW / 2 ? vm.overRight = true : vm.overRight = false;
                    left < childW  ? vm.overLeft = true : vm.overLeft = false;
                })

            },
            visibleChange(i){
                this.childI = '';
            },
            initList() {
                let plugLi = this.plugInData.plugList;
                // console.log("插件数据：",this.plugInData.plugList);
                if (this.plugInData.canSlide) {
                    this.storeList = plugLi.filter((plug, i) => {
                        return i < this.storeLen;
                    });
                    this.slideList = plugLi.filter((plug, i) => {
                        return i >= this.storeLen;
                    });
                } else {
                    this.storeList = plugLi;
                }
            },
            addIconFun() {
                if( $(this.$refs.plugGroup).is(':visible')) this.storeLen ++;
                this.initList();
            },
            removeIconFun(){
                if( $(this.$refs.plugGroup).is(':visible')) this.storeLen --;
                this.initList();
            },
            showSlideIcon(){
                this.showSlide = true;
            },
            hideSlideIcon(){
                this.showSlide = false;
            }
        },
        created() {
            this.initList();
        },
    }
</script>

<style scoped lang="less">


    .plugInGroup {
        position: relative;
        float: left;
        padding: 0 0.5rem;
    }

    .module-icon {
        padding: 8px 16px 8px;
        cursor: pointer;
        text-align: center;
        float: left;
        position: relative;
        height: 75px;
        box-sizing:border-box;
    }

    .m-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin: auto;
        text-align: center;
    }

    .m-icon .eda_icon {
        width: 40px;
        height: 36px;
    }

    .m-name {
        font-size:14px;
        line-height: 20px;
        display: block;
        max-width: 80px;
        min-width:50px;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: @main-font;
    }

    .groupN {
        color: @font-color;
        text-align: center;
        font-size: 12px;
        line-height: 22px;
    }

    .module-icon:hover {
        background: rgba(40, 155, 241, .1);
        border-radius: 2px;
    }

    .module-icon:hover .plugDetail {
        display: block;
    }

    .group-line {
        height: calc( 100% - 20px);
        display: block;
        border-right: 1px solid #e0e0e0;
        position: absolute;
        top: 50%;
        transform: translate(0 , -50%);
        right: 0;
    }

    .child-arrow {
        font-size: 12px;
        width: 100%;
        height: 12px;
        line-height: 12px;
        margin-bottom: -8px;
        margin-top: -4px;
        color: @minor-font;
    }

    .module-icon:hover .childPlugIn {
        display: block;
    }

    .model-a {
        width: 16px;
        height: 16px;
        background: url("../../../assets/images/arrow/module-a.png") no-repeat center center;
        position: absolute;
        right: 0;
        bottom: 0;
        cursor: pointer;
    }

    .model-a:hover {
        background-color: #ccc;
    }

</style>
