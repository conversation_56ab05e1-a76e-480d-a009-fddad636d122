import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters, mapState} from "vuex"
import * as $ from 'jquery'

export default {
    name: "tree" ,
    mixins : [treeMethods , commonMixins , servicesMixins],
    computed : {
        ...mapGetters(["userRight","userInfo"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        ...mapState({
            otherUserCatalogId: (state) => state.plans.otherUserCatalogId,
            // 可编辑节点 非分享，非他人目录(不等于他人目录 Id ，没有用户 id 值)
            nodeEditable: (state) =>{
                return (data) => data.id !== state.plans.shareId && data.id !== state.plans.otherUserCatalogId && data.currentUserId === state.user.userInfo?.id;
            }
        }),
        disableClick(){
            return (data) => data.disabled;
        }
    },
    data(){
        return {
            defaultProps : {
                value : "id" ,
                label : "label" ,
                children : "children"
            },
            treeMenu : [] ,
            addBtnTxt : '新增子目录' ,
            menu: [
                /*{
                    name: '新增子目录',
                    fn: this.addDialog,
                    show: (right,node) => node && node.level < 5
                },*/
                {
                    name: '重命名目录',
                    fn: this.editNode ,
                    show : () => true
                }, {
                    name: '删除目录',
                    fn: this.deleteNode ,
                    show : () => true
                }
            ],
            expandData:[],
        }
    },
    props:{
        hasSave:Boolean,
    },
    methods : {
        nodeClick(v,i){
            this.currentId = v.id;
            this.$emit('nodeClick',v.name , v.id , i);
        },
        setCheckedNode(id){
            this.$refs.tree.setCurrentKey(id);
        },
        setCurrentKey(key){
            this.$refs.tree.setCurrentKey(key);
        },
        /**
         * 获取我的id
         * @param data
         * @param code
         * @returns {string}
         */
        async getDefaultNode(data, code) {
            const vm = this;
            let curId = "";
            for (let no of data) {
                if (no.children && no.children.length) {
                    curId = await vm.getDefaultNode(no.children, code);
                }
                if (no.code === code) {
                    curId = no.id;
                    break;
                }
            }
            return curId;
        },
        /**
         * 设置节点图标
         * @param data
         */
        setTreeIcon(data){
            const vm = this;
            return data.map(item => {
                item.icon = item.code === 'POR_DIR_STANDARD' ?
                    'dg-iconp icon-journal-b' : item.code === 'PORTAL_DIR_MY' ? 'dg-iconp icon-notebook' :
                        item.id === '2' ? 'el-icon-share' : '';
                item.isActive = false;
                if(item.children && item.children.length) vm.setTreeIcon(item.children);
                return item;
            })
        },
        async initTree() {
            const vm = this , {portalServices , portalMock , settings } = this;
            let services = vm.getServices(portalServices , portalMock);
            settings.loading = true;
            vm.data = [];
            services.queryPortalTree(settings).then(async res => {
                if(res.data.status === 0){
                    let result = vm.setTreeIcon(res.data.data);
                    if(result && result.length){
                        vm.data = vm.setNodeProp(result[0].children);
                        let curId = await vm.getDefaultNode(result[0].children ,"PORTAL_DIR_MY");
                        // 增、改目录过滤他人空间
                        if(vm.hasSave) {
                            // vm.data = vm.data.filter(l => vm.nodeEditable(l));
                        }
                        vm.$nextTick(()=>{
                            if(vm.$refs.tree && curId){
                                vm.expandData.push(curId);
                                vm.$refs.tree.setCurrentKey(curId);
                                let currentNode = vm.$refs.tree.getCurrentNode();
                                vm.$emit("getCenterTable" , currentNode);
                            }
                        });
                    }

                }
            })
        },
        setNodeProp(list=[]){
            const {setNodeProp} = this;
            return list.map(d => {
                const children = d.children && d.children.length ?
                    setNodeProp(d.children) : [];
                return {
                    ...d,
                    children,
                    currentUserId: d.currentUserId,
                    disabled: d.id === this.otherUserCatalogId,
                }
            })
        },
        getChildList(list , pId) {
            let ls = [];
            for (let i = 0; i < list.length; i++) {
                let d = list[i];
                let c = {
                    id: d.id,
                    pId : pId,
                    name: d.name,
                    label: d.name,
                    isParent: false,
                };
                ls.push(c)
            }
            return ls;
        },
        getCenterTable(nodeData, i) {
            this.nodeClick(nodeData, i);
            this.$emit("getCenterTable", nodeData);
        },
        addDialog(data){
            // let dir = data || this.selectedData;
            // this.$emit("addTreeDir", dir);
            this.showMenu(this.selectedNode, this.selectedData);
            this.prompt('新增子目录', '', this.addTreeNode , '目录名称' , 'child' );
        },
        addChildren(event , node, object) {//新增子目录
            this.showMenu(node, object);
            this.prompt('新增子目录', '', this.addTreeNode , '目录名称' , 'child' );
        },
        addTreeNode(value) {
            const vm = this , { portalServices , portalMock,userInfo } = this;
            let services = vm.getServices(portalServices , portalMock);
            let pId = vm.selectedData.id;
            services.addPortalTreeNode(pId, value ).then(res =>{
                if(res.data.status === 0){
                    let data = res.data.data;
                    vm.successAddTip(value);
                    let newTreeNode = {
                        children: [],
                        id: data,
                        label: value,
                        isParent: false,
                        pId: pId ,
                        currentUserId: userInfo.id,
                    };
                    if(!vm.selectedData.children) vm.selectedData.children=[];
                    vm.selectedData.children.unshift(newTreeNode);

                    vm.expandData.push(newTreeNode.id);
                    vm.$nextTick(()=>{
                        vm.$refs.tree.setCurrentKey(newTreeNode.id);
                        let currentNode = vm.$refs.tree.getCurrentNode();
                        vm.getCenterTable(currentNode );
                    });
                }
            })
        },
        editNode(value) {//编辑
            this.prompt('重命名目录', this.selectedData.label, this.editTree , '目录名称' , 'child' );
        },
        deleteNode() {//删除
            const vm = this , { portalServices , portalMock } = this;
            let services = vm.getServices(portalServices , portalMock);
            let node = this.getTreeNode() , nodeN = node.data.label;
            let currentNode = vm.$refs.tree.getCurrentNode();
            const index = node.children.findIndex(d => d.id === node.data.id);
            vm.confirm(`确认删除\"${nodeN}\"目录吗` , '删除' , ()=>{
                services.deletePortalTreeNode(node.data.id).then(res => {
                    if(res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        node.children.splice(index, 1);
                        if(currentNode.id === node.data.id){
                            vm.$emit('getCenterTable' , vm.selectedNode.parent.data);
                            vm.$refs.tree.setCurrentKey(vm.selectedNode.parent.data.id);
                        }
                    }
                })
            });

        },
        editTree(value) {
            const vm = this , { portalServices , portalMock } = this;
            let services = vm.getServices(portalServices , portalMock);
            let node = this.selectedData,
                nodeId = node.id,
                nodePid = node.pId;
            services.editPortalTreeNode(nodeId,nodePid,value).then(res => {
                if(res.data.status === 0){
                    vm.$message({
                        type: 'success',
                        message: "目录修改成功"
                    });
                    vm.selectedData.label = value;
                }
            });
        },
    }
}
