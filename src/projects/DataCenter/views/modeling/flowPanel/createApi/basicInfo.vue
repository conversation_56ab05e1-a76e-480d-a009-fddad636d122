<template>
    <div class="basicInfo">
        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
            <el-form-item>
                <dg-radio v-model="form.radio" label="1">新增API</dg-radio>
                <dg-radio v-model="form.radio" label="2">更新API</dg-radio>
            </el-form-item>
            <el-form-item label="API名称：" required prop="name">
                <el-input v-model="form.name" v-input-limit:fieldChineseCode></el-input>
                <div>支持中文、英文、数字、下划线，且只能以英文或中文开头，3-64个字符。</div>
            </el-form-item>
            <el-form-item label="API类型：" required prop="type">
                <el-button-group>
                    <dg-button v-for="(item, index) in btnOptions" :key="index" :type="selected === index ? 'primary': ''" @click="handleSelect(index)">
                        {{ item }}
                    </dg-button>
                </el-button-group>
            </el-form-item>
            <el-form-item label="服务资源：" required prop="source">
                <el-input v-model="form.source" disabled></el-input>
            </el-form-item>
            <el-form-item label="版本号：" required >
                <el-input v-model="form.version"></el-input>
            </el-form-item>
            <el-form-item label="API描述：" prop="desc">
                <el-input type="textarea" resize="none" v-model="form.desc" :placeholder="placeholder_desc" :autosize="{ minRows: 4, maxRows: 6}" maxlength="100" show-word-limit></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "basicInfo",
        props:{
            name: String,
        },
        data(){
            let validateVersion = (rule, value, callback) => {
                let req = /^\d+\.\d+((\.\d+)*)$/;
                if(value === ''){
                    callback(new Error('请输入版本号！'));
                }
                if (!req.test(value)) {
                    callback(new Error('仅支持数字，比如：1.0、3.1.2'));
                } else {
                    callback();
                }
            };
            return{
                rules: {
                    version: [
                        { required: true, message:'请输入版本号！', trigger: "change" },
                        { validator: validateVersion,trigger: 'blur' },
                    ],
                    name: [
                        { required: true, message:'请输入API名称！', trigger: ['blur', 'change'] },
                    ],
                    type: [
                        { required: true, message:'请选择API类型！', trigger: 'blur' },
                    ],
                    source: [
                        { required: true, message:'请输入服务资源！', trigger: 'blur' },
                    ],
                },
                btnOptions: ['模型分析', '数据查询'],
                selected:0,
                placeholder_desc: '对API进行简要描述',
                form: {
                    name: '',
                    version: '1.0',
                    desc: '',
                    type: '1',
                    source: '',
                    radio: '1'
                },
            }
        },
        mounted(){
            this.form.source = this.name;
        },
        methods:{
            handleSelect(index){
                this.selected = index;
            },
            validate(){

            },
        },
    }
</script>

<style scoped lang="less">
    .basicInfo{
        /deep/.dg-upload--pictureList:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        /deep/ .dg-upload--picture-card{
            width: 55px;
            height: 55px;
        }
        /deep/ .el-upload{
            .dg-upload--pictureList{
                width: 71px;
                height: 71px;
                line-height:71px;
            }
            .dg-upload--text{
                bottom:40px;
            }
        }
        /*/deep/ .el-upload-list--picture-card .dg-upload-list__item{*/
        /*width: 55px;*/
        /*height: 55px;*/
        /*line-height: 55px;*/
        /*}*/
        /*/deep/ .dg-upload-list--picture-card .dg-upload-list__item-thumbnail{*/
        /*width: 55px;*/
        /*height: 55px;*/
        /*line-height: 55px;*/
        /*}*/
        /*/deep/ .avatar{*/
        /*width: 55px;*/
        /*height: 55px;*/
        /*}*/
        /deep/.dg-upload__carousel{
            width: 55px;
        }
    }
</style>