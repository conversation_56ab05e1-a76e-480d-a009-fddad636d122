import {treeMethods} from "@/api/treeNameCheck/treeName";
import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins"
import {outputMixins} from "@/api/commonMethods/output";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import datasetList from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/sql-analyse/dataset-list/dataset-list"
import {mapGetters} from "vuex";
export default {
    name: "sourceTree",
    components : {datasetList},
    mixins :[commonMixins ,servicesMixins , outputMixins],
    data(){
        return {
            sourceTi : "选择数据源" ,
            datasetTi : "选择数据集",
            defaultProps: {
                children: "children",
                label: "label",
                value: "id"
            },
            dataSource : "" ,
            dataSourceOpt:[],
            showAddBox : false ,
            sortMenu : {
                time : {label : "按时间排序" , icon : "iconshijianpaixutubiao"} ,
                letter : {label : "按字母排序", icon : "iconsort"}
            },
            sortVal : "time",
            timeSort : "按时间排序",
            letterSort : "按字母排序",
            search:"" ,
            data : [] ,//数据集
            dbType: ["GREENPLUM","ORACLE","POSTGRESQL","hwmpp","Mysql","HIVE","TBASE","vertica","gbase"],
            schemaId : "" ,
        }
    },
    computed : {
        ...mapGetters(["userInfo", "userRight"]),
        rights() {
            const vm = this;
            let rights = [];
            if (vm.userRight) rights = vm.userRight.map(r => r.funcCode);
            return rights;
        },
    },
    watch : {
        search(val){
            this.$refs.dataset.filterList(val);
        }
    },
    methods : {
        filterNode: treeMethods.methods.filterNode,
        treeSortByWay: treeMethods.methods.treeSortByWay,
        sortInTime: treeMethods.methods.sortInTime,
        sortInLetter: treeMethods.methods.sortInLetter,
        selectSchema(val , data){
            this.dataSource = data.data.id;
            this.schemaId = data.data.schemaId;
            this.getTargetTable(this.dataSource);
        },
        setTreeSort(val){
            this.showAddBox = false;
            this.sortTreeData(val);
        },
        async sortTreeData(val){
            const vm = this;
            vm.sortVal = val;
            if(val === 'time'){
                vm.data = await vm.treeSortByWay(vm.data , 'opTime' , 'sortInTime' , true);
            }else if(val === 'letter') {
                vm.data = await vm.treeSortByWay(vm.data , 'label' , 'sortInLetter' , true);
            }
        },
        async getDataSourceTree() {
            const vm = this, { settings, listApi, listMock , rights} = this;
            //数据源目录树
            let listServices = vm.getServices(listApi, listMock);
            let res;
            if (rights.includes("dataConnectionDataWarehouse")) {
                res = await listServices.getDataPreparationTree();
            } else {
                res = await listServices.queryDirTree();
            }
            if (res.data.code === 0) {
                let result = res.data.data;
                vm.dataSourceOpt = vm.resTreeDataDispose(result);
            }
        },
        /**
         * 通过schemaId查表
         * @param schemaId
         * @param data
         */
        getTableBySchemaId(schemaId , data){
            const vm = this;
            data.forEach(item => {
                if(item.schemaId && item.schemaId === schemaId){
                    vm.dataSource = item.id;
                    vm.getTargetTable(item.id);
                }
                if(item.children && item.children.length){
                    vm.getTableBySchemaId(schemaId , item.children);
                }
            })
        },
        async setSchemaId(schemaId){
            const vm = this;
            vm.schemaId = schemaId;
            await vm.getDataSourceTree();
            vm.getTableBySchemaId(vm.schemaId , vm.dataSourceOpt);
            vm.$refs.selectTree.$refs.tree.setCurrentKey(vm.dataSource);
        },
        /**
         * //获取目标表数据
         * @param instanceId 库id
         * */
        async getTargetTable(instanceId) {
            const vm = this, {listApi, listMock} = this;
            let dw_service = vm.getServices(listApi, listMock);
            vm.data = [];
            await dw_service.queryDataBaseTableAll(instanceId).then(res => {
                if (res.data.code === 0) {
                    let data = res.data.data;
                    vm.data = data.map(item => {
                        return {
                            ...item ,
                            label:  item.name || item.code,
                            title : item.name ? `${item.code}(${item.name})` : item.code,
                            extend: false,
                            loaded: false,
                            pId:"0",
                            children: [],
                        };
                    })
                    vm.setTreeSort(vm.sortVal);
                }
            })
        },
        async loadChildren(data , settings){
            const { services } = this;
            let params = {
                dataObjId: data.id,
                dbType: data.dbType,
            };
            let result = [];
            await services.getDataObjColumn(params , settings).then(res => {
                if(res.data.status === 0){
                    result = res.data.data.map(item => {
                        return {
                            ...item ,
                            pId : data.id,
                            id : item.columnId ,
                            label : item.name || item.code ,
                            title : item.name ? `${item.code}(${item.name})` : item.code,
                            children : []
                        }
                    })
                }
            })
            return result;
        }
    }
}
