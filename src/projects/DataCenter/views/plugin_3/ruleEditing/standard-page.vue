<script>
export default {
    name: "standard-page",
    props: {
        value: {
            type: Array,
            default: () => [],
            required: true,
        }
    },
    inject: ['prevColumns', 'timeFuncOption'],
    computed: {
        columns() {
            return [...this.prevColumns(), ...this.timeFuncOption()];
        },
        paramsData: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        }
    },
    watch: {
        paramsData: {
            handler(val) {
                this.$emit('input', val);
            },
            deep: true,
        }
    },
    data() {
        return {
            paramsHead: [
                {label: '参数名', prop: 'name',},
                {label: '参数类型', prop: 'dataType',},
                {label: '参数说明', prop: 'memo',},
                {label: '值来源', prop: 'source', minWidth: 120},
                {label: '参数值', prop: 'value', minWidth: 120},
                {label: '调试值', prop: 'debugValue', minWidth: 80},
            ],
            sourceOpt: [
                {label: '自定义', value: 'custom'},
                {label: '字段', value: 'field'},
            ], // 值来源类型
            checkConditionColumn: [{required: true, message: '请设置参数', trigger: ['blur', 'change']}],
        }
    },
    methods: {
        paramsSourceChange(row) {
            row.value = "";
        },
        paramsValueChange(row) {
            row.debugValue = row.value;
        },
        validate(...args) {
            return this.$refs.paramsForm.validate(...args);
        },
        optionDisabled(item, row) {
            const state = {
                "time": (type) => !['time', 'timestamp'].includes(type),
                "timestamp": (type) => !['time', 'timestamp'].includes(type),
                "string": (type) => !['string', 'varchar','text'].includes(type),
                "text": (type) => !['string', 'varchar','text'].includes(type),
                "boolean": (type) => !['boolean'].includes(type),
                "short": (type) => !['short'].includes(type),
                "blob": (type) => !['blob'].includes(type),
                "double": (type) => !['double'].includes(type),
                "date": (type) => !['date'].includes(type),
                "bigdecimal": (type) => !['bigdecimal'].includes(type),
                "integer": (type) => !['integer'].includes(type),
                "float": (type) => !['float'].includes(type),
                "byte": (type) => !['byte'].includes(type),
                "long": (type) => !['long'].includes(type),
                "biginteger": (type) => !['biginteger'].includes(type),
            };
            const dataType = row.dataType.toLowerCase();
            return row.dataType && state[dataType] && state[dataType](item.type.toLowerCase());
        },
        resetFields() {
            return this.$refs.paramsForm.resetFields();
        }
    }
}
</script>

<template>
    <el-form :model="{ list: paramsData }" ref="paramsForm" class="height100 ce-form__m0">
        <common-table
            size="mini"
            :pagination="false"
            height="100%"
            :border="false"
            :data="paramsData"
            :columns="paramsHead">
            <template #header-value="{column}">
                <span class="ce-is-required">{{ column.label }}</span>
            </template>
            <template #name="{row}">
                <span>{{ `${row.name} (${row.code})` }}</span>
            </template>
            <template #source="{row}">
                <dg-select v-model="row.source" :data="sourceOpt"
                           @change="paramsSourceChange(row)"></dg-select>
            </template>
            <template #value="{row,$index}">
                <el-form-item :prop="`list.${$index}.value`" :rules="[...checkConditionColumn]">
                    <el-input v-if="row.source === 'custom'" v-model="row.value"
                              @input="paramsValueChange(row)"
                              placeholder="请输入参数值"></el-input>
                    <el-select v-if="row.source === 'field'"
                               filterable
                               v-model="row.value" placeholder="请选择参数">
                        <el-option
                            v-for="item in columns"
                            :disabled="optionDisabled(item , row)"
                            :key="item.value"
                            :label="item.label"
                            :value="item.columnCode">
                            <span class="l">{{ item.label }}</span>
                            <span class="r g8 pl6">{{ item.type }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
            </template>
            <template #debugValue="{row}">
                <el-input v-model="row.debugValue" placeholder="请输入调试值"></el-input>
            </template>
        </common-table>
    </el-form>
</template>

<style scoped lang="less">


</style>
