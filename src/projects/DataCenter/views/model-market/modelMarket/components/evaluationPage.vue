<!--
    @Describe: 模型评价 （建模评价 使用评价）
    @Author: wangjt
    @Date: 2024/6/21
-->
<template>
    <div class="evaluation-outer">
        <template v-if="canEvaluate()">
            <div class="evaluation-row mb20 pl20 pr20 mt20">
                <div class="evaluation-col pr10" data-fit>
                    <el-avatar icon="el-icon-user-solid"></el-avatar>
                </div>
                <div class="evaluation-col tr" data-full>
                    <el-input type="textarea" v-model="evaluation" placeholder="请输入评价"
                              show-word-limit
                              class="mb14"
                              :autosize="{ minRows: 5, maxRows: 8}"
                              maxlength="500"></el-input>
                    <el-button type="primary" @click="submit">{{ issueTxt }}</el-button>
                </div>
            </div>
            <el-divider/>
        </template>
        <div v-if="listData.length" class="pr20 mt20">
            <div class="evaluation-row pl20" v-for="(item , index) in listData" :key="index">
                <div class="evaluation-col pr10" data-fit>
                    <el-avatar icon="el-icon-user-solid"></el-avatar>
                </div>
                <div class="evaluation-col" data-full>
                    <div class="evaluation-row evaluation-sec_font">
                        <div class="evaluation-col g6" data-full>
                            {{ item.username }}
                        </div>
                        <div class="evaluation-col g9" data-fit>{{ item.operatetime }}</div>
                    </div>
                    <div class="pt8 pb30">
                        <pre class="evaluation-text">{{ item.operatecontent }}</pre>
                    </div>
                </div>
            </div>
            <div class="tr">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[5,10, 20]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageTotal">
                </el-pagination>
            </div>
        </div>

    </div>
</template>

<script>
export default {
    name: "evaluationPage",
    props: {
        total: Number,
        // 加载自己的评论
        loadEvaluation: {
            type: Function,
            default: () => {
            }
        },
        // 加载分页评论
        loadPageEvaluation: {
            type: Function,
            default: () => {
            }
        },
        // 提交评价接口
        submitEvaluation: {
            type: Function,
            default: () => {
            }
        },
        // 是否可以评价
        canEvaluate: {
            type: Function,
            default: () => {
            }
        }
    },
    computed: {
        pageTotal: {
            get() {
                return this.total;
            },
            set(val) {
                this.$emit("update:total", val);
            }
        }
    },
    data() {
        return {
            evaluation: "",
            issueTxt: "发布",
            listData: [],
            currentPage: 1,
            pageSize: 10,
        }
    },
    methods: {
        async submit() {
            if (!this.evaluation.trim()) return this.$message.warning("评价内容不能为空");
            await this.submitEvaluation(this.evaluation);
            await this.handleCurrentChange();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val;
            this.currentPage = 1;
            this.handleCurrentChange();
        },
        // 切换分页
        async handleCurrentChange(val = 1) {
            this.currentPage = val;
            this.listData = [];
            let res = await this.loadPageEvaluation(val, this.pageSize);
            if (res) {
                this.listData = res.list;
                this.pageTotal = res.total;
            }
        },
        async init() {
            this.handleCurrentChange()
            this.evaluation = await this.loadEvaluation();
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.evaluation {
    &-row {
        display: flex;
        gap: 20px;
    }

    &-col {
        // 适应内容
        &[data-fit] {
            width: fit-content;
            flex-shrink: 0;
        }

        // 适应宽度
        &[data-full] {
            flex: 1;
        }
    }

    &-sec_font {
        font-size: 0.9rem;
    }

    &-text {
        white-space: pre-wrap;
        color: @fontMainColor;
        font-size: 0.92rem;
    }
}
</style>
