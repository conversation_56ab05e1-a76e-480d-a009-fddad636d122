<!--
    @Describe: 字段排序
    @Author: wangjt
    @Date: 2024/7/26
-->
<template>
    <div class="ovh height100">
        <div class="tr mb10">
            <el-input
                    class=ce-field_filter
                    placeholder="请输入字段中、英文名搜索"
                    prefix-icon="el-icon-search"
                    v-model.trim="searchValue"
            >
            </el-input>
        </div>
        <common-table
                height="calc(100% - 10px - 2rem)"
                :columns="columns"
                :pagination="false"
                :data="showData"
                row-key="columnName"
        >
            <template #index="{row}">
                <span>{{ getCurIndex(row) + 1 }}</span>
            </template>
            <template slot="operate" slot-scope="{row , $index}">
                <el-button
                        type="text"
                        class="model_status"
                        v-for="(col , inx) in operateIcons"
                        :key="inx"
                        :title="col.tip"
                        v-html="col.tip"
                        :disabled="col.disabled(row)"
                        @click="col.clickFn(row)"
                ></el-button>
            </template>
        </common-table>
        <preView ref="preview"></preView>
    </div>
</template>

<script>
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView";
import {saveFieldSortPlugin} from "@/projects/DataCenter/services/plugin-services_3.0/plugin-services3";

export default {
    name: "fieldSort",
    props: {
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    components: {
        preView,
    },
    computed: {
        showData() {
            const {searchValue} = this;
            if (this.searchValue) {
                return this.data.filter(li => (
                    li.columnName && li.columnName.toLowerCase().includes(searchValue.toLowerCase()) ||
                    li.columnZhName && li.columnZhName.toLowerCase().includes(searchValue.toLowerCase())
                ))
            }
            return this.data;
        }
    },
    data() {
        return {
            searchValue: "",
            columns: [
                {
                    prop: "index",
                    label: "排序号",
                    width: 100,
                },
                {prop: 'columnName', label: '字段别名'},
                {prop: 'columnZhName', label: '字段中文名'},
                {prop: 'type', label: '字段类型'},
                {
                    prop: "operate",
                    label: "操作",
                    width: "220",
                    align: "center"
                }
            ],
            data: [],
            operateIcons: [
                {
                    tip: "上移",
                    clickFn: (row) => this.moveTo(this.getCurIndex(row) - 1, this.getCurIndex(row)),
                    disabled: (row) => !this.getCurIndex(row)
                },
                {
                    tip: "下移",
                    clickFn: (row) => this.moveTo(this.getCurIndex(row) + 1, this.getCurIndex(row)),
                    disabled: (row) => this.data.length - 1 === this.getCurIndex(row)
                },
                {
                    tip: "置顶",
                    clickFn: (row) => this.moveTo(0, this.getCurIndex(row)),
                    disabled: (row) => !this.getCurIndex(row)
                },
                {
                    tip: "调整序号",
                    clickFn: (row) => this.setIndexShow(row),
                    disabled: () => false
                },
            ],
            getType: ['Date', 'String', 'Double', 'Integer', 'Boolean', 'Timestamp', 'BigInteger', 'Long', 'BigDecimal', 'Text', "Blob"]
        }
    },
    methods: {
        /**
         * 获取当前位置
         */
        getCurIndex(row) {
            return this.data.findIndex(li => li.id === row.id);
        },
        moveTo(inx, cur) {
            console.log('改变，序号(index)"' + cur + '"移动到序号(index)"' + inx + '"');
            const currentRow = this.data.splice(cur, 1)[0]; // 将oldIndex位置的数据删除并取出，oldIndex后面位置的数据会依次前移一位
            this.data.splice(inx, 0, currentRow); // 将被删除元素插入到新位置（currRow表示上面的被删除数据）
        },
        setIndexShow(row) {
            const vm = this;
            this.$dgLayer({
                title: "调整序号",
                content: require("./dataSetting.vue"),
                maxmin: false,
                area: ["300px", "auto"],
                props: {
                    maxVal: this.data.length
                },
                on: {
                    submit(inx) {
                        vm.moveTo(inx - 1, vm.getCurIndex(row))
                    }
                }
            })
        },
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        save(saveName,showTip) {
            const vm = this, {$services, loadParams} = vm;
            loadParams.loading = true;
            const cicadaOutPutFsSet = vm.setParams(),tranStepId = vm.sourceData.id;
            $services("plugin3").saveFieldSortPlugin({
                tranStepId,cicadaOutPutFsSet,
                settings: loadParams,
            }).then(res => {
                if(res.data.code === 0){
                    vm.$emit("setPreviewAble");
                    showTip && vm.$message.success("保存成功！");
                    saveName && saveName();
                }
            })
        },
        setParams() {
            const vm = this;
            let res = [];
            this.data.forEach(li => {
                res.push({
                    "columnName": li.columnName,
                    "columnZhName": li.columnZhName,
                    "type": li.type,
                    "columnNewName": li.columnNewName,
                    "length": li.length,
                    "precision": li.precision,
                    "columnId": li.columnId,
                    "columnCode": li.columnCode,
                    "dataType": li.dataType,
                    sort: vm.getCurIndex(li),
                })
            })
            return res;
        },
        init() {
            const vm = this, {$services, loadParams} = vm;
            loadParams.loading = true;
            vm.data = [];
            $services("plugin3").queryFieldSortData({
                tranStepId: vm.sourceData.id,
                settings: loadParams,
            }).then(res => {
                if (res.data.code === 0) {
                    const {outputColumns, inputColumn} = res.data.data;
                    (inputColumn || []).forEach((n, i) => {
                        let item = (outputColumns || []).find((it) => it.columnCode === n.columnCode);
                        if (item) {
                            vm.data.push({
                                id: item.columnId + '_' + i,
                                ...item,
                            })
                        } else {
                            vm.data.push({
                                id: n.columnId + '_' + i,
                                ...n
                            })
                        }
                    })
                }
            })
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.ce-field_filter {
    width: 250px;
}
</style>
