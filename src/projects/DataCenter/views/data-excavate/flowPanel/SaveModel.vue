<template>
    <div class="mainDialog">
        <common-dialog custom-class="save" :width="width"
                   :title="title"
                   :visible.sync="dialogFormVisible"
                   @closed="clearD"
                   v-loading="settings.loading"
        >
            <el-row>
                <el-col class="tree-height" :span="12" >
                    <!--目录树-->
                    <DataTreeVue ref="tree" v-if="openDialog" @nodeClick="treeDir" :dirId="rowData.dirId" :notFirst="true" :hasSave="true"/>
                </el-col>
                <el-col :span="12">
                    <el-form :model="form" size="mini" label-width="80px" >
                        <!-- 过程名称-->
                        <el-form-item :label="formList[2].label" :required="true">
                            <el-input v-model.trim="form.transName" maxlength="30" v-input-filter="form.transName" @input="inputFilterSpecial($event , 'form' , 'transName')" :placeholder="formList[2].placeholder"></el-input>
                        </el-form-item>
       <!--                 &lt;!&ndash; 运行实例 &ndash;&gt;
                        <el-form-item :label="formList[0].label">
                            <el-select v-model="form.instance" :placeholder="formList[0].placeholder">
                                <el-option
                                        v-for="item in optInstanceData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        &lt;!&ndash; 运行模式 &ndash;&gt;
                        <el-form-item :label="formList[1].label">
                            <el-select v-model="form.mode" :placeholder="formList[1].placeholder">
                                <el-option
                                        v-for="item in optProcessModeData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        &lt;!&ndash; 处理模式 &ndash;&gt;
                        <el-form-item :label="formList[3].label">
                            <el-select v-model="form.processMode" :placeholder="formList[3].placeholder">
                                <el-option
                                        v-for="item in optSolveModeData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>-->
                    </el-form>
                </el-col>
            </el-row>

            <div slot="footer">
                <el-button  v-show="active" @click="stop" size="mini">取消</el-button>
                <el-button @click="save" type="primary" size="mini">确定</el-button>
            </div>
        </common-dialog>
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    import DataTreeVue from "../dialog/ModelTree";
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins";
    import {listMixins} from "@/api/commonMethods/list-mixins";
    export default {
        name: "SaveModel",
        mixins: [commonMixins, servicesMixins,listMixins],
        components:{
            DataTreeVue
        },
        data(){
            return {
                dialogFormVisible: false,
                width: '600px',
                active: true,
                title :"",
                classifyId:"",

                form: {
                    instance: "",
                    mode: "",
                    transName:"",
                    processMode: "",
                },
                formList: [
                    {
                        label: '运行实例 :',
                        placeholder: '请选择运行实例'
                    }, {
                        label: '运行模式 :',
                        placeholder: '请选择运行模式'
                    }, {
                        label: '名称 :',
                        placeholder: '请输入模型名称(限30个字符)'
                    }, {
                        label: '处理模式 :',
                        placeholder: '请选择处理模式'
                    },
                ],
                optProcessModeData: [
                    {
                        value: "0",
                        label: "单机"
                    },
                    {
                        value: "1",
                        label: "分布式"
                    },
                    {
                        value: "3",
                        label: "Spark"
                    },
                ],
                optInstanceData: [
                    {
                        value: "运行实例",
                        label: "运行实例"
                    }
                ],
                optSolveModeData: [
                    {
                        value: "",
                        label: ""
                    }
                ],
                rowData:{},
                isModel : false,
                openDialog : false
            }
        },
        methods:{
            treeDir(label,value){
                this.classifyId = value;
            },
            show(rowData,saveInstance , isModel){
                this.form.instance = saveInstance;
                this.rowData = rowData;
                this.form.transName = rowData.modelName;
                this.initSelectValue();
                this.dialogFormVisible = true;
                this.openDialog = true;
                this.isModel = isModel;
                if(isModel){
                    this.title = "保存为模板";
                }else {
                    this.title = "新建模型";
                }
                if(rowData.dirId && rowData.dirId !== "-1"){
                    this.classifyId = rowData.dirId;
                }
            },
            save(){
                const vm = this, {modelingServices, modelingMock, settings} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                if(vm.classifyId === ""){
                    vm.$message.warning("请选择方案所在目录！")
                }else if(vm.form.transName === ""){
                    vm.$message.warning("请输入方案名称！")
                }else if(vm.form.instance === ""){
                    vm.$message.warning("请选择方案运行实例！")
                }else{
                    // String transName, String runMode, String transId, String instanceCode, String handleMode, String classifyId
                    let data ={
                        transName:this.form.transName,
                        runMode:this.form.mode,
                        transId:this.rowData.transId,
                        instanceCode:this.form.instance,
                        handleMode:this.form.processMode,
                        classifyId:this.classifyId,
                        dirType:"DIG_DIR_MF"
                    };
                    settings.loading = true;
                    services.updateDataMiningTrans(data , settings).then(res =>{
                        if(res.data.status === 0){
                            if(res.data.data !== "该名称已存在！"){
                                // vm.activeJob();
                                globalBus.$emit('changeName', vm.form.transName,vm.rowData.transId);
                                vm.$emit("setIcons");
                                vm.dialogFormVisible = false;
                                if(vm.isModel){
                                    services.save(vm.rowData.transId).then(res=>{
                                        if(res.data.status === 0){
                                            globalBus.$emit("modelSaveSuccess",vm.rowData.transId);
                                            this.$message.success("保存成功！");
                                        }
                                    });//保存模板
                                }
                            }else {
                                vm.$message.warning(res.data.data);
                            }
                        }
                    })
                }
            },//transSchedule
            activeJob(){
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                services.activeJob(vm.rowData.transId).then(res =>{
                    if(res.data.status === 0){
                        this.$message.success("激活成功！");
                    }
                })
            },
            clearD(){
                this.openDialog = false;
            },
            stop(){
                this.dialogFormVisible = false;
            },
            initSelectValue(){ // 没有值的时候初始化第一条数据， 有值再从接口读取值
                const vm = this, {modelingServices, modelingMock} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                services.baseSettingPage(vm.rowData.transId).then(res =>{
                    if(res.data.status === 0){
                        //处理模式
                        vm.form.processMode =res.data.data.mode;
                        vm.optSolveModeData = [];
                        res.data.data.handleModes.forEach((item,i) =>{
                            if(i < 2){
                                let model = {
                                    value:item.code,
                                    label:item.name,
                                };
                                vm.optSolveModeData.push(model);
                            }
                        });
                        //运行模式
                        vm.form.mode = res.data.data.distributed;
                        //运行实例
                        vm.optInstanceData = [];
                        res.data.data.instanceList.forEach(item =>{
                            let instance = {
                                value:item,
                                label:item,
                            };
                            vm.optInstanceData.push(instance);
                        });
                    }

                     vm.form.instance = vm.optInstanceData[0].value;
                })
            },
        },
    }
</script>

<style scoped>
    /*.mainDialog{
        height: 500px;
    }*/
    .tree-height .dataTree {
        height: 300px;
        margin: 0;
    }
</style>
