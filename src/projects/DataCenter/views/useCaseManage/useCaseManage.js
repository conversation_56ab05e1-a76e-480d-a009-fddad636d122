
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi_3/index.vue"
import treeCard from "@/components/layout/tree-card"
import List from "./list"
import Tree from "./tree"
import {TreeEdit} from "@/projects/DataCenter/views/useCaseManage/tree/tree-edit";

export default {
    name: "modeling" ,
    mixins : [  commonMixins ,TreeEdit],
    components : {
        TreeAndList ,
        List,
        treeCard,
        Tree,
    },
    data(){
        return {
            cardName : "API列表",
            sortMenu : {
                time : {label : "按时间排序" , icon : "iconshijianpaixutubiao"} ,
                letter : {label : "按字母排序", icon : "iconsort"}
            },
            sortVal : "time",
            showAddBox : false ,
            treeData:[],
        }
    },
    methods : {
        setTreeData(data) {
            this.treeData = data;
        },
        treeDir(value, id , node) {
            this.$refs.modelingCenterTable.setDir(value, id , node);
        },
        initTable(data){
            this.$refs.modelingCenterTable.setDir('',data.id);
        },
        moveTo(row ){
            this.$refs.move.open(row);
        },
        update(){
            this.$refs.modelingCenterTable.changePage(1);
        },
        //判断当前节点有几层
        getTreeDeep(treeData) {
            let arr = [];
            arr.push(treeData);
            let depth = 0;
            while (arr.length > 0) {
                let temp = [];
                for (let i = 0; i < arr.length; i++) {
                    temp.push(arr[i]);
                }
                arr = [];
                for (let i = 0; i < temp.length; i++) {
                    if (temp[i].children && temp[i].children.length > 0) {
                        for (let j = 0; j < temp[i].children.length; j++) {
                            arr.push(temp[i].children[j]);
                        }
                    }
                }
                if (arr.length >= 0) {
                    depth++;
                }
            }
            return depth;
        },
        /**
         * 显示移动弹窗
         * @param data
         * @param node
         */
        moveToDir(data,node){
            let level = this.treeLimitLevel-1;
            let deep = this.getTreeDeep(data);
            level = this.treeLimitLevel - deep;
            //level = data.children && data.children.length || this.getTreeDeep(data.children) >= this.treeLimitLevel  ? node.level - 1 : this.treeLimitLevel-1;
            this.$refs.treeDir.show(data , 'move' , level );
        },
        /**
         * 排序树
         * @param val
         */
        setTreeSort(val){
            this.showAddBox = false;
            this.sortVal = val;
            this.$refs.dataTree.sortTreeData(val);
        },
        filterTree(val){
            this.$refs.dataTree.setFilterText(val);
        },
        /**
         * 显示添加目录弹窗
         * @param data
         */
        showAddDialog(data){
            this.$refs.treeDir.show(data);
        },
    },
    created(){
        let {rapidShowName} = this.$route.params;
        const vm = this;
        if (rapidShowName) {
            vm.$nextTick(()=>{
                this.$refs.modelingCenterTable.APICreate();
            })
        }
    }
}
