<!--
    @Describe: 数据集 选择 弹窗
    @Author: wangjt
    @Date: 2024/9/10
-->
<template>
    <el-popover
            v-model="visible"
            width="400"
            trigger="click"
            @after-enter="showEnter"
    >
        <el-select
                slot="reference"
                v-model="val"
                v-bind="selectProp"
                @change="selectChange"
        >
            <el-option v-for="opt in options"
                       :key="opt.value"
                       :value="opt.value"
                       :label="opt.label"></el-option>
            <span slot="empty"></span>
        </el-select>
        <TreePanel ref="panel"
                   v-on="$listeners"
                   :show-checkbox="$attrs.multiple"
                   @updateVal="updateVal"
                   @updateOptions="updateOptions"/>
    </el-popover>

</template>

<script>
import TreePanel from "./tree-panel.vue";
import { valueEquals } from 'element-ui/src/utils/util';

export default {
    name: "dataset-pop",
    componentName: 'ElFormItem',
    components: {
        TreePanel,
    },
    props: {
        value: {
            type: [Array, String],
            default: "",
        }
    },
    computed: {
        val: {
            get() {
                return this.value;
            },
            set(v) {
                this.$emit("input", v);
            }
        },
        options: {
            get() {
                return this.selOptions;
            },
            set(v) {
                this.selOptions = v;
            }
        }
    },
    provide(){
        return {
            setVal: () => this.val
        }
    },
    watch: {
        value() {
            this.setCurOptions();
        }
    },
    data() {
        return {
            visible: false,
            selOptions: [],
            allOptions: [],
            selectProp: {}
        }
    },
    created() {
        this.mergeProps();
    },
    methods: {
        reloadTree(){
            return this.$refs.panel?.initTree();
        },
        showEnter(){
            this.selectChange(this.val);
        },
        mergeProps() {
            this.selectProp = Object.assign({}, this.$attrs, {
                "popper-append-to-body": false,
                "popper-class": "dataset-popper",
                "filterable": false,
            })
        },
        updateVal(data, close = false) {
            const {multiple} = this.$attrs;
            let newVal;
            if (multiple !== undefined) {
                newVal = data.map(o => o.id);
                this.options = [...data].map(o => {
                    return {
                        ...o,
                        label: o.label,
                        value: o.id
                    }
                });
            } else {
                newVal = data.id;
                this.options = [{...data}].map(o => {
                    return {
                        ...o,
                        label: o.label,
                        value: o.id
                    }
                })
            }
            this.emitChange(newVal);
            this.val = newVal;
            if (close) this.visible = false;
        },
        emitChange(val) {
            if (!valueEquals(this.value, val)) {
                this.$emit('change', val);
            }
        },
        setCurOptions() {
            const {val, $attrs, allOptions: data} = this;
            let selData = [];
            if (val) {
                if ($attrs.multiple !== undefined) {
                    selData = data.filter(o => val.includes(o.id));
                } else {
                    selData = data.filter(o => val === o.id);
                }
                this.selectChange(val);
            }
            this.options = [...selData].map(o => {
                return {
                    ...o,
                    label: o.label,
                    value: o.id
                }
            });
        },
        updateOptions(data) {
            this.allOptions = [...data];
            this.setCurOptions();
        },
        selectChange(v) {
            v && this.$refs.panel.setSelectedVal(v);
        },
        /**
         * 返回勾选的数据集
         * @returns {*}
         */
        getCheckedNodes(){
            return this.options;
        },
        getCurrentNode(){
            return this.options.length ? this.options[0] : null;
        }
    }
}
</script>

<style scoped lang="less">
/deep/ .dataset {
    &-popper {
        display: none;
        visibility: hidden;
        opacity: 0;
    }
}
</style>
