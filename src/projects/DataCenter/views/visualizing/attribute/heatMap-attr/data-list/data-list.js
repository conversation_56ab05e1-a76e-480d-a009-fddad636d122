import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";
export default {
    name: "dataList" ,
    props: {
        node: Object,
        dir_dataSetId : String
    },
    mixins : [attrMixins],
    data(){
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show : ()=>true,
            }
        };
        return {
            showDrill:false,
            axisList : {
                xAxis: {
                    label: '度量',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动度量字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加度量', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                yAxis: {
                    label: '维度/x轴',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                zAxis: {
                    label: '维度/y轴',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                ...formList
            },
            query : null
        }
    },
    computed: {
        chartData() {
            return {
                xAxis: this.axisList.xAxis.value,
                xData: this.axisList.xAxis.options,
                yAxis: this.axisList.yAxis.value, 
                yData: this.axisList.yAxis.options, 
                zAxis: this.axisList.zAxis.value,
                zData: this.axisList.zAxis.options,
                filterData: this.filterSelects,
                datasetId: this.datasetId,
                classifierStatId: this.classifierStatId,
                dataset: this.dataset ,
                filterList : this.query
            };
        }
    },
    methods : {
        resetList(datasetId, dataset, isNew) { //重置数据
            const vm = this;
            if (isNew) {
                this.axisList.xAxis.value = [];
                this.axisList.xAxis.options = [];
                this.axisList.yAxis.value = [];
                this.axisList.yAxis.options = [];
                this.axisList.zAxis.value = [];
                this.axisList.zAxis.options = [];
                this.filterSelects = [];
                this.chartAttr = [];
                if(this.axisList.drilling){
                    this.axisList.drilling.list = [];
                }
            }
            //设置 datasetId 、tableCode
            this.datasetId = datasetId;
            // this.classifierStatId = datasetId;
            vm.$nextTick(()=>{
                let getDataset;
                if(!dataset){
                    getDataset = vm.$refs.nodeTree.getDataSet();
                }else {
                    getDataset = dataset;
                }
                getDataset && getDataset.classifierStatId ? vm.classifierStatId =  getDataset.classifierStatId  : true;
                getDataset ? vm.dataset = vm.copyArrayObj(getDataset) : true;
            })

        },
        //别名重置XY轴
        editAlias(fieldId ,val){
            this.setOptionsAlias('xAxis' , fieldId , val);
            this.setOptionsAlias('yAxis' , fieldId , val);
            this.setOptionsAlias('zAxis' , fieldId , val);
        },
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            /*if (this.node.code !== 'TableChartWidget') {
                if (this.axisList[inx].prop === 'xAxis' && this.dragTreeNode.indexType === "DIMENSION") {
                    this.$message.warning('请拖入维度输入框');
                    return;
                } else if (this.axisList[inx].prop === 'yAxis' && this.dragTreeNode.indexType === "MEASURE") {
                    this.$message.warning('请拖入度量输入框');
                    return;
                }
            }*/
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx , this.dragTreeNode.indexType , 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        initValue(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let {beforeData , query} = JSON.parse(vm.node.widget),nodeData = JSON.parse(beforeData);
          
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId :true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.xAxis.options = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.zAxis.options = nodeData.zData ? JSON.parse(JSON.stringify(nodeData.zData)) : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];
                vm.axisList.zAxis.value = nodeData.zAxis ? nodeData.zAxis : [];
                vm.axisList.xAxis.value = nodeData.xAxis ? nodeData.xAxis : [];
                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                //查询
                vm.query  = [];
                //隐藏下拉
                vm.dropDownHide([vm.axisList.xAxis.options , vm.axisList.yAxis.options, vm.axisList.zAxis.options]);
                vm.inputData();
                vm.addAxisName("","",vm.axisList);
            })
        },
        validate(){
            if (this.chartData.xAxis.length === 0 ) {
                this.$message.info('请添加度量!');
                return;
            }else if (this.chartData.yAxis.length === 0) {
                this.$message.info('请添加X轴维度!');
                return;
            }else if (this.chartData.zAxis.length === 0) {
                this.$message.info('请添加Y轴维度!');
                return;
            }else if(this.chartData.yAxis[0] === this.chartData.zAxis[0] ) {
                this.$message.info('X轴与Y轴字段不允许相同!');
                return;
            }
        
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setHeatMapPanel', this.node.id);
        },
    }
}
