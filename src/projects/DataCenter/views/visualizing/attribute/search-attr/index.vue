<template>
    <div class="attrSearch chartAttr ">
        <el-tabs v-model="activeName" size="mini" type="border-card" class="ce-tabs_two" >
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-attr_body">
            <data-list ref="data_list" v-on="$listeners" :nodeList="nodeList"  v-show="activeName === 'data'"></data-list>
            <style-list ref="style_list" :active="activeName === 'style'"  v-show="activeName === 'style'"></style-list>
        </div>
        <div class="ce-attr_foot">
            <el-button type="primary" class="pct40" size="mini" :class="{'pct60' : activeName === 'style'}" @click="save">{{btnLabel}}</el-button>
            <el-button type="primary" class="pct40" size="mini" v-if="activeName === 'data'" @click="clear">{{btnClear}}</el-button>
        </div>
    </div>
</template>

<script src="./search-attr.js"></script>
<style scoped lang="less" src="../css/attr.less"></style>
<style scoped lang="less" src="./search-attr.less"></style>