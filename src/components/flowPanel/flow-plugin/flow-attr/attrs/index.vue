<!-- 
    linxp
    2021.06.11
-->
<template>
    <div class="attrs">
        <h3 class="attrs__title" v-if="sourceData.keyWord === 'cicadaStandardSqlOutput'"><span class="outWord">目标数据库 {{":" + sourceDb}}</span></h3>
        <h3 class="attrs__title" v-if="sourceData.keyWord === 'cicadaStandardSqlOutput' && resultData&&resultData.tableName"><span class="outWord">目标数据表 {{":" + resultData.tableName + "(" + resultData.tableCode + ")"}}</span></h3>
        <h3 class="attrs__title" v-if="sourceData.keyWord === 'cicadaStandardSqlInput'"><span>来源数据库 {{":" + sourceDb}}</span></h3>
        <!-- <h3 class="attrs__title" v-if="sourceData.keyWord === 'cicadaStandardSqlInput' && resultData">
            <span class="outWord" :title="(resultData.tableName || resultData.tableCode) + '(' + resultData.tableCode + ')'">
                来源数据表 {{":" + (resultData.tableName || resultData.tableCode) + "(" + resultData.tableCode + ")"}}
            </span></h3> -->
        <h3 class="attrs__title" v-if="sourceData.keyWord === 'cicadaStandardSqlInput' && resultData">
            <span class="outWord" :title="(resultData.logicName || resultData.logicCode) + '(' + resultData.logicCode + ')'">
                数据集名称 {{":" + (resultData.logicName || resultData.logicCode) + "(" + resultData.logicCode + ")"}}
            </span></h3>
        <h3 class="attrs__title"><span>组件类型</span></h3>
        <el-input v-model="type" placeholder="组件类型" disabled/>
        <h3 class="attrs__title" style="margin-top: 24px">
            <span class="is-require">别名</span>
            <p>长度不超过50个字符</p>
        </h3>
        <el-input v-model.trim="name" v-input-limit:fieldName :maxlength="50"
                  placeholder="请输入别名(长度不能超过50个字符)" :disabled="!!rowData.caseId" @change="attrChange"></el-input>
        <h3 class="attrs__title" style="margin-top: 24px">
            <span>描述</span>
            <p>长度不超过255个字符</p>
        </h3>
        <el-input v-model.trim="desc" @change="attrChange" :disabled="!!rowData.caseId" :maxlength="225" placeholder="请输入详细描述信息" type="textarea" :rows="5"></el-input>
    </div>
</template>
<script>
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";

export default {
    name: "attrs",
    mixins: [servicesMixins, listMixins],
    data() {
        return {
            type: "",
            name: "",
            desc: "",
            sourceDb : "",
            resultData : null,
        };
    },
    props: {
        sourceData: Object,
        rowData: Object ,
        loadParams:Object
    },
    watch: {
        "sourceData.id": {
            handler(val) {
                this.init({id:val});
            },
            deep: true
        },
        "sourceData.name" : {
            handler(val){
                this.name = val;
            },
            deep : true
        }
    },
    methods: {
        init(val) {
            const vm = this, {services} = vm ,{id} = val;
            services.getPluginDec(id).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.resultData = res.data.data;
                    vm.sourceDb = result.dbName || "";
                    vm.type = result.pluginTypeName;
                    vm.name = result.name;
                    vm.desc = result.memo || "";
                }
            })
        },
        save() {
            const vm = this, {services, sourceData, name, desc , loadParams} = vm, {id} = sourceData;
            services.savePluginDec(id, name, desc , loadParams).then(res => {
                if (res.data.status === 0) {
                    vm.$emit("nodeChange" , name , desc);
                    vm.$message.success("保存成功");
                }
            })
        },
        attrChange(){
            const vm = this , {name} = vm;
            if(!name) {
                vm.$message.warning("别名不能为空");
                return ;
            }
            vm.save();
        }
    },
    created() {
        this.init(this.sourceData);
    }
};
</script>
<style lang="less" scoped>
.outWord {
    width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.attrs {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    overflow: auto;
    flex: 1;

    &__title {
        width: 100%;
        display: flex;
        align-items: flex-end;
        margin-bottom: 12px;

        span {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            font-weight: 400;
        }

        p {
            font-size: 12px;
            padding-left: 14px;
            color: rgba(0, 0, 0, 0.25);
        }
    }

    .is-require {
        position: relative;

        &::before {
            content: "*";
            position: absolute;
            top: 0;
            left: calc(100% + 2px);
            color: #F5222D;
        }
    }
}
</style>
