<!--排序list插件-->
<template>
    <ul class="SortList">
        <li class="SortList_item" v-for="(item , i) in data" :key="i"
            :class="{'descending' : activeLi === item.code && !order , 'ascending' : activeLi === item.code && order}"
            @click="sortChange(item)"
        >
            <span>{{ item.label }}</span>
            <span class="caret-wrapper">
                <i class="sort-caret ascending"></i>
                <i class="sort-caret descending"></i>
            </span>
        </li>
    </ul>
</template>

<script>
export default {
    name: "SortList",
    props: {
        data: {
            type: Array,
            default: () => []
        },
        activeItem : String ,
        orderVal : Boolean , // true 升序 false 降序
    },
    data(){
        return {
            activeLi : "" ,
            order : false
        }
    },
    methods : {
        init(){
            this.activeLi = this.activeItem || this.data.length && this.data[0].code;
            this.order = this.orderVal !== undefined ? this.orderVal : false;
            this.emitChange();
        },
        sortChange(item){
            if(item.code === this.activeLi){
                this.order = !this.order;
            }else {
                this.activeLi = item.code;
                this.order = false;
            }
            this.emitChange();
        },
        emitChange(){
            this.$emit("change" , this.activeLi , this.order);
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.SortList {
    display: flex;

    &_item {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        padding-right: 24px;
        cursor: pointer;
    }
}

.caret-wrapper {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    height: 34px;
    width: 24px;
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
}
.sort-caret {
    width: 0;
    height: 0;
    border: 5px solid transparent;
    position: absolute;
    left: 7px;
}
.sort-caret.ascending {
    border-bottom-color: #c0c4cc;
    top: 5px;
}
.sort-caret.descending {
    border-top-color: #c0c4cc;
    bottom:7px;
  }
.ascending .sort-caret.ascending {
    border-bottom-color: #409eff;
}
.descending .sort-caret.descending {
    border-top-color: #409eff;
}
</style>
