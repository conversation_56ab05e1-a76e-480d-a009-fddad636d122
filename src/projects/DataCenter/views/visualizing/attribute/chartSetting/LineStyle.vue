<template>
    <div class="lineStyle">
        <StyleComponent :list="list" ref="styleCom" :node="node" />
    </div>
</template>

<script>
    import StyleComponent from './StyleComponent'
    export default {
        name: "LineStyle" ,
        components : {
            StyleComponent
        },
        props : {
            node : Object
        },
        data(){
            return {
                list : {
                    title: {
                        label: '图表名称',
                        value: true,
                        radioOptions:
                            [{
                                label: '显示',
                                value: true
                            },{
                                label: '不显示',
                                value: false
                            } ]
                    },
                    name: {
                        label: '轴标题',
                        value: true,
                        radioOptions:
                            [{
                                label: '不显示',
                                value: false
                            }, {
                                label: '显示',
                                value: true
                            }]
                    },
                    dataLabel : {
                        label: '数据标签',
                        value: true,
                        radioOptions : [
                            {
                                label: '显示',
                                value: true
                            }, {
                                label: '不显示',
                                value: false
                            }
                        ]
                    },
                    legend: {
                        label: '图例',
                        value: 'top',
                        radioOptions:
                            [{
                                label: '顶部',
                                value: 'top'
                            },{
                                label: '右侧',
                                value: 'right'
                            }, {
                                label: '左侧',
                                value: 'left'
                            },  {
                                label: '底部',
                                value: 'bottom'
                            }, {
                                label: '不显示',
                                value: false
                            }]
                    }
                },
                inputData : true
            }
        },
        methods : {
            initStyle(){
                this.$nextTick(() =>{
                    if(!this.node.style) return;
                    this.inputData = true;
                    this.node.style.title !== '' ? this.list.title.value = this.node.style.title : true;
                    this.node.style.name !== '' ? this.list.name.value = this.node.style.name : true;
                    this.node.style.legend !== '' ?this.list.legend.value = this.node.style.legend : true;
                    this.$refs.styleCom.init(this.list , this.inputData);
                })

            }
        },
        created() {
            this.initStyle();
        }
    }
</script>

<style scoped>
    .lineStyle {
        height: calc(100vh - 155px);
        overflow: auto;
        padding: 10px;
    }
</style>