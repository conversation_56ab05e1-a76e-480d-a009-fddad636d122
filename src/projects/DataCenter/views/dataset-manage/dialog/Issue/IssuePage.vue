<template>
    <div class="main">
        <div v-show="!isNextStep">
            <div class="issueHead">
                <div style="height: 100%">
                    <el-container>
                        <el-header height="35px"><span style="color: red">*</span>服务接口：</el-header>
                        <div class="serviceInterfaceClass">
                            <el-form class="serviceForm">
                                <el-row>
                                    <el-col :span="10">
                                        <el-form-item label="服务英文名:" label-width="120px" prop="serviceName">
                                            <el-input v-model.trim="serviceName" size="mini"
                                                      :placeholder="service_p.serviceName"
                                                      maxlength="100"
                                                      @input="serviceName = serviceName.replace(/[^a-zA-Z]+/g,'')"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="服务中文名:" label-width="120px" prop="serviceChName">
                                            <el-input v-model.trim="serviceChName"
                                                      v-input-filter="serviceChName" size="mini" :placeholder="service_p.serviceChName"
                                                      maxlength="100"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </el-container>
                    <el-container>
                        <el-header height="34px">
                            <span class="conHead"><span style="color: red">*</span>调入参数：</span>
                            <el-button type="primary"
                                       size="mini"
                                       class="addButton"
                                       @click="addEvent('callParam')"
                            >添加
                            </el-button>
                        </el-header>
                        <div class="callParamClass">
                            <common-table
                                    class="ce-table"
                                    :pagination="false"
                                    :data="callParamData"
                                    :columns="paramHeadData"
                                    size="mini"
                            >
                                <template slot="isMust" slot-scope="scope">
                                    <el-checkbox size="mini" v-model="scope.row.isMust"></el-checkbox>
                                </template>
                                <template slot="defaultValue" slot-scope="scope">
                                    <el-input size="mini" v-model="scope.row.defaultValue"></el-input>
                                </template>
                            </common-table>
                        </div>

                    </el-container>
                    <el-container>
                        <el-header height="34px">
                            <span class="conHead"> <span style="color: red">*</span>调出参数：</span>
                            <el-button type="primary"
                                       size="mini"
                                       @click="addEvent('callParticipate')"
                                       class="addButton"
                            >添加
                            </el-button>
                        </el-header>
                        <div class="callParamClass">
                            <common-table
                                    class="ce-table"
                                    :data="callParticipateData"
                                    :columns="participateHeadData"
                                    :pagination="false"
                                    size="mini"
                            ></common-table>
                        </div>
                    </el-container>
                    <!--                    <el-container>-->
                    <!--                        <el-header height="34px">告警设置：</el-header>-->
                    <!--                        <div class="reportClass"></div>-->
                    <!--                    </el-container>-->
                </div>
            </div>
            <div class="issueFooter ">
                <!--                <div class="alarmRules">-->
                <!--                    <el-form style="width: 25%">-->
                <!--                        <el-form-item size="mini" label-width="120px" label="报警规则名称：">-->
                <!--                            <el-select v-model="ruleName">-->
                <!--                                <el-option-->
                <!--                                        v-for="item in ruleOption"-->
                <!--                                        :label="item.label"-->
                <!--                                        :value="item.value"-->
                <!--                                        :key="item.value"-->
                <!--                                ></el-option>-->
                <!--                            </el-select>-->
                <!--                        </el-form-item>-->
                <!--                    </el-form>-->
                <!--                </div>-->
                <div align="center">
                    <el-button
                            type="primary"
                            size="mini"
                            @click="nextStep"
                    >下一步
                    </el-button>

                    <el-button
                            size="mini"
                            @click="closeService"
                    >关闭
                    </el-button>
                </div>
            </div>
        </div>
        <div v-show="isNextStep">
            <div align="center" style="margin-top: 50px">
                <el-form :model="serviceInfo" :rules="serviceRules">
                    <el-form-item label="模型中文名:" class="frameWidth" label-width="160px" prop="chName">
                        <el-input size="mini" v-model.trim="serviceInfo.chName"
                                  :placeholder="service_p.chName"
                                  maxlength="100"
                                  v-input-filter="serviceInfo.chName" ></el-input>
                    </el-form-item>
                    <el-form-item label="模型英文名:" class="frameWidth" label-width="160px" prop="engName">
                        <el-input size="mini" v-model.trim="serviceInfo.engName"
                                  :placeholder="service_p.engName"
                                  maxlength="100"
                                  @input="serviceInfo.engName = serviceInfo.engName.replace(/[^a-zA-Z]+/g,'')"></el-input>
                    </el-form-item>
                    <el-form-item label="模型版本号:" class="frameWidth" label-width="160px" prop="version">
                        <el-input size="mini" v-model.trim="serviceInfo.version"
                                  :placeholder="service_p.version"
                                  maxlength="100"
                                  @input="serviceInfo.version = serviceInfo.version.replace(/\D/g,'')"></el-input>
                    </el-form-item>
                    <el-form-item label="服务类型:" class="frameWidth" label-width="160px" prop="type">
                        <el-select size="mini" v-model="serviceInfo.type">
                            <el-option v-for="item in serviceTypeOption"
                                       :label="item.label"
                                       :value="item.value"
                                       :key="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="服务说明:" class="frameWidth" label-width="160px">
                        <el-input size="mini" :placeholder="service_p.explain" v-model="serviceInfo.explain" type="textarea"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div class="issueFooter">
                <div align="center">
                    <el-button
                            v-for="(btn , inx) in buttons"
                            :key="inx"
                            size="mini"
                            :disabled="btn.disabled"
                            :type="btn.type"
                            @click="btn.clickFn(btn.name)"
                    >{{btn.name}}
                    </el-button>
                </div>
            </div>
        </div>
        <!--<div>
            <el-dialog
                    title="配置信息"
                    :visible.sync="dialogVisible"
                    width="30%"
                    :before-close="handleClose"
                    append-to-body
                    destroy-on-close
            >
                <ConfigurationInfo ref="config"/>
                <div slot="footer">
                    <el-button @click="confirm" type="primary" size="mini">确定</el-button>
                    <el-button @click="closeDialog" size="mini">取消</el-button>
                </div>
            </el-dialog>
        </div>-->
        <common-dialog
                custom-class="addP"
                :title="addTitle"
                :visible.sync="addVisible"
                :before-close="handleClose"
        >
            <ParamAddPage ref="paramAdd" :addType="addType" :row="row"/>
            <div slot="footer">
                <el-button @click="closeAddDialog" size="mini">取消</el-button>
                <el-button @click="addConfirm" type="primary" size="mini">确定</el-button>
            </div>
        </common-dialog>
        <!--        服务测试页面-->
        <common-dialog
                custom-class="test"
                title="服务测试"
                :visible.sync="serviceTestVisible"
                fullscreen
                :before-close="serviceTestClose"
        >
            <ServiceTest ref="serviceTest"/>
            <div slot="footer">
                <!--                    <el-button @click="previousStep" type="primary" size="mini">上一步</el-button>-->
                <el-button @click="closeServiceTest" size="mini">关闭</el-button>
                <!--                    <el-button @click="online" type="primary" size="mini">上线</el-button>-->
            </div>
        </common-dialog>
    </div>
</template>

<script>
    import ConfigurationInfo from './ConfigurationInfo'
    import ParamAddPage from './ParamAddPage'
    import ServiceTest from './ServiceTest'
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {common} from "@/api/commonMethods/common"
    import {servicesMixins} from "../../service-mixins/service-mixins";
    export default {
        name: "IssuePage",
        components: {
            ConfigurationInfo,
            ParamAddPage,
            ServiceTest
        },
        mixins: [commonMixins, servicesMixins, common],
        props: {
            row: Object,
        },
        data() {
            return {
                service_p :{
                    serviceName: "请输入服务中文名(限100个字符)",
                    serviceChName: "请输入服务英文名(限100个字符)",
                    chName: "请输入模型中文名(限100个字符)",
                    engName: "请输入模型中文名(限100个字符)",
                    version: "请输入模型版本号(限100个字符)",
                    explain: "请输入服务说明",
                },
                //入参用于传入后台的数据
                resFilter: [],
                //条件过滤
                callFilterParam: [],
                //服务测试
                serviceTestVisible: false,

                addTitle: "",
                addType: "",
                addVisible: false,
                saveAndIssue: "",
                dialogVisible: false,
                serviceTypeOption: [
                    {
                        label: "查询服务",
                        value: "4",
                    },

                ],

                serviceRules: {
                    chName: [{required: true, message: '服务中文名不能为空', trigger: 'change'}],
                    engName: [{required: true, message: '服务英文名不能为空', trigger: 'change'}],
                    version: [{required: true, message: '服务版本不能为空', trigger: 'change'}],
                    type: [{required: true, message: '服务类型不能为空', trigger: 'change'}],
                },
                serviceInfo: {
                    chName: "",
                    engName: "",
                    version: "",
                    type: "4",
                    explain: "",
                },

                buttons: [
                    {
                        name: '上一步',
                        clickFn: this.lastStepFn,
                        type: 'primary'
                    }, {
                        name: '暂存',
                        clickFn: this.saveAndIssueFn,
                        type: 'primary',
                        disabled: true,
                    },
                    {
                        name: '发布',
                        clickFn: this.saveAndIssueFn,
                        type: 'primary',
                    },
                    {
                        name: '关闭',
                        clickFn: this.closeService,
                        type: '',
                    }
                ],

                serviceName: "",
                serviceChName: "",

                isNextStep: false,
                ruleName: "",
                ruleOption: [
                    {
                        label: "规则1",
                        value: "value1",
                    },
                    {
                        label: "规则2",
                        value: "value2",
                    },
                ],

                callParamData: [
                    // {
                    //     devParamName: "开发参数名",
                    //     paramChName: "参数中文名",
                    //     paramType: "参数类型",
                    //     mappingType: "映射方式",
                    //     isMust: true,
                    //     defaultValue: "默认值1"
                    // },
                    // {
                    //     devParamName: "开发参数名",
                    //     paramChName: "参数中文名",
                    //     paramType: "参数类型",
                    //     mappingType: "映射方式",
                    //     isMust: false,
                    //     defaultValue: "默认值2"
                    // },
                ],//调入参数表格数据
                paramHeadData: [
                    {
                        prop: "devParamName",
                        label: "开发参数名",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "paramChName",
                        label: "参数中文名",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "paramType",
                        label: "参数类型",
                        minWidth: 160,
                        align: "center",
                    },
                    // {
                    //     prop: "mappingType",
                    //     label: "映射方式",
                    //     minWidth: 160,
                    //     align: "center"
                    // },
                    {
                        prop: 'isMust',
                        label: '是否必须',
                        width: 160,
                        align: "center"
                    }, {
                        prop: 'defaultValue',
                        label: '默认值',
                        width: 160,
                        align: "center",
                        'show-overflow-tooltip' : false,
                    }
                ],

                callParticipateData: [
                    // {
                    //     devParamName: "dataRow",
                    //     paramChName: "数据行",
                    //     paramType: "Row",
                    //     // mappingType: "映射方式",
                    //     isMust: "是",
                    // }
                ],
                participateHeadData: [
                    {
                        prop: "devParamName",
                        label: "开发参数名",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "paramChName",
                        label: "参数中文名",
                        minWidth: 160,
                        align: "center",
                    },
                    {
                        prop: "paramType",
                        label: "参数类型",
                        minWidth: 160,
                        align: "center",
                    },
                    // {
                    //     prop: "mappingType",
                    //     label: "映射方式",
                    //     minWidth: 160,
                    //     align: "center"
                    // },
                    // {
                    //     prop: 'isMust',
                    //     label: '是否必须',
                    //     width: 160,
                    //     align: "center"
                    // }
                ],

            }
        },
        methods: {
//服务测试
            online() {
            },
            closeServiceTest() {
                this.serviceTestVisible = false;
                this.closeService();
            },
            previousStep() {
                this.serviceTestVisible = false;
            },


            checkParamType(type) {
                //time、datetime、timestamp和year
                let typeList = ["DATE", "date", "time", "Time", "datetime", "DATETIME", "TIMESTAMP", "timestamp", "YEAR", "year"];
                if (typeList.indexOf(type) > -1) {
                    return true;
                }
                return false;
            },
            addConfirm() {
                const _this = this;
                let checkNullFlag = false;
                let checkParamTypeFlag = false;
                let nonParamType = "";
                let inParam = this.$refs.paramAdd.getInParam();
                if(inParam === undefined){
                    this.$message.warning("中文名称不能重复！");
                    return ;
                }
                this.resFilter = inParam.resFilter;
                if (inParam.param.length === 0) {
                    this.$message.warning("未添加参数");
                    return;
                }


                let checkLength = false;
                if (this.addType === "callParam") {

                    inParam.param.forEach(item => {
                        if (item.paramName === "" || item.paramChName === "") {
                            checkNullFlag = true;
                        }
                        if (_this.checkParamType(item.dataType)) {
                            checkParamTypeFlag = true;
                            nonParamType = item.dataType;
                        }
                        if (item.paramChName.length > 100) {
                            checkLength = true;
                        }

                    });

                    if (checkLength) {
                        _this.$message.warning("中文名称长度不能超过100！");
                        return;
                    }

                    if (checkNullFlag) {
                        _this.$message.warning("参数名和参数中文名不能为空");
                        return;
                    }

                    if (checkLength) {
                        _this.$message.warning("中文名称长度不能超过100！");
                        return;
                    }

                    if (checkParamTypeFlag) {
                        _this.$message.warning("目前不支持【" + nonParamType + "】类型的参数");
                        return;
                    }


                    _this.callParamData = [];
                    _this.callFilterParam = inParam.filter;
                    inParam.param.forEach(val => {
                        _this.callParamData.push({
                            devParamName: val.paramName,
                            paramChName: val.paramChName,
                            paramType: val.dataType,
                            // mappingType: "映射方式",
                            isMust: true,
                            defaultValue: ""
                        })
                    });
                } else {

                    inParam.param.forEach(item => {
                        if (item.callParamName === "" || item.callParamChName === "") {
                            checkNullFlag = true;
                        }
                        if (_this.checkParamType(item.dataType)) {
                            checkParamTypeFlag = true;
                            nonParamType = item.dataType;
                        }

                        if (item.callParamChName.length > 100) {
                            checkLength = true;
                        }

                    });
                    if (checkNullFlag) {
                        _this.$message.warning("参数名和参数中文名不能为空");
                        return;
                    }

                    if (checkParamTypeFlag) {
                        _this.$message.warning("目前不支持【" + nonParamType + "】类型的参数");
                        return;
                    }

                    _this.callParticipateData = [];
                    inParam.param.forEach(val => {
                        this.callParticipateData.push({
                            devParamName: val.callParamName,
                            paramChName: val.callParamChName,
                            paramType: val.callDataType,
                            // mappingType: "映射方式",
                            isMust: "",
                        });
                    })
                }

                this.addVisible = false;

            },
            closeAddDialog() {
                this.addVisible = false;
            },
            confirm() {


                // this.$refs.config.showServiceTest();
                // let configInfo = this.$refs.config.getConfigInfo();
                //发布
                // if (this.saveAndIssue === "发布") {
                //
                // } else {
                //     //暂存
                // }
                // this.dialogVisible = false;
            },
            closeDialog() {
                this.dialogVisible = false;
            },

            handleClose() {
                this.addVisible = false;
            },
            serviceTestClose() {
                this.serviceTestVisible = false;
                this.closeService();
            },
            async checkServiceNameAndCode() {
                let checkResult = false;
                if (this.serviceInfo.chName === "") {
                    this.$message.warning("请输入服务中文名");
                    return checkResult;
                }
                if (this.serviceInfo.engName === "") {
                    this.$message.warning("请输入服务英文名");
                    return checkResult;
                }
                if (this.serviceInfo.version === "") {
                    this.$message.warning("请输入服务版本号");
                    return checkResult;
                }
                if (this.serviceInfo.chName === "") {
                    this.$message.warning("请选择服务类型");
                    return checkResult;
                }
                checkResult = true;
                return checkResult;

                // await this.$axios.get("/publish/isExistCodeAndName?name=" + this.serviceInfo.chName + "&code=" + this.serviceInfo.engName).then(res => {
                //     let result = res.data;
                //     if (result.code === 0) {
                //         checkResult = true;
                //     } else {
                //         this.$message.warning(result.msg);
                //     }
                // }).catch(err => {
                //     console.log(err);
                //     this.$message.error("服务器异常，请联系管理员！");
                // });
                // return checkResult;
            },
            //发布
            async saveAndIssueFn(type) {
                this.saveAndIssue = type;
                let flag = await this.checkServiceNameAndCode();
                if (flag) {
                    this.commitPublish();
                }

            },
            async commitPublish() {
                const vm = this, {datasetServices, datasetMock } = this;
                let services = vm.getServices(datasetServices, datasetMock);
                // this.serviceTestVisible = true;
                //调入参数
                let paramList = [];
                this.callParamData.forEach(item => {
                    paramList.push({
                        defaultValue: item.defaultValue,
                        isMust: item.isMust,
                        paramCode: item.devParamName,
                        paramName: item.paramChName,
                        type: item.paramType,
                        // type: "String",
                    })
                });
                //调出参数
                let ginseng = [];
                this.callParticipateData.forEach(item => {
                    ginseng.push({
                        isMust: item.isMust,
                        paramCode: item.devParamName,
                        paramName: item.paramChName,
                        type: item.paramType,

                    });
                });

                let json = JSON.stringify(this.resFilter);
                let param = {
                    implChineseName: this.serviceInfo.chName,
                    implEnglishName: this.serviceInfo.engName,
                    implVersion: this.serviceInfo.version,
                    interfaceChineseName: this.serviceChName,
                    interfaceEnglishName: this.serviceName,
                    modelId: this.row.id,
                    paramList: paramList,
                    ginsengList: ginseng,
                    serviceType: this.serviceInfo.type,
                    dbType: this.row.db_type,
                    tableName: this.row.code,
                    filterJson: json ,
                    memo : this.serviceInfo.explain
                };

                await services.createService(param).then(res => {
                    let result = res.data;
                    if (result.status === 0) {
                        vm.$message.success("发布成功!");
                        vm.serviceTestVisible = true;
                        vm.$nextTick(() => {
                            vm.$refs.serviceTest.setServiceTest(vm.serviceInfo, vm.serviceName, vm.callParamData, result.data);
                        });

                    }
                });

            },
            lastStepFn() {
                this.isNextStep = false;
            },
            nextStep() {
                if (this.serviceName === '') {
                    this.$message.warning("服务英文名称未输入！");
                    return;
                }
                if (this.serviceChName.trim() === '') {
                    this.$message.warning("服务中文名称未输入！");
                    return;
                }
                if (this.callParamData.length <= 0) {
                    this.$message.warning("输入参数不能为空！");
                    return;
                }
                if (this.callParticipateData.length <= 0) {
                    this.$message.warning("输出参数不能为空！");
                    return;
                }
                 this.checkName(this.serviceChName,this.serviceName);
                // this.isNextStep = true;
                //
                // console.log(this.row)
                // if(this.callParamData.length <=0){
                //     this.$message.warning("未添加调入参数!");
                //     re   turn;
                // }
                // if(this.callParticipateData.length <=0){
                //     this.$message.warning("未添加调出参数!");
                //     return;
                // }
                // window.open(this.servicePublicationUrl + '/ServiceConfig/structure?id=123321');

            },
             checkName(name,code){
                 const vm = this, {datasetServices, datasetMock } = this;
                 let services = vm.getServices(datasetServices, datasetMock);
                 services.checkServiceName(name , code).then(res =>{
                    vm.isNextStep = res.data.status === 0;
                })
            },
            closeService() {
                this.$emit("closeService")
            },
            addEvent(type) {
                this.addType = type;
                this.addVisible = true;
                if (type === "callParam") {
                    this.addTitle = "调入参数设置";
                    if (this.callParamData.length !== 0) {
                        this.$refs.paramAdd.setTableData(this.callParamData,type, this.callFilterParam, this.resFilter);
                    }
                } else {
                    this.addTitle = "调出参数设置";
                    if (this.callParticipateData.length !== 0) {
                        this.$refs.paramAdd.setTableData(this.callParticipateData,type);
                    }
                }


            },

        }
    }
</script>

<style scoped>
    .el-header {
        background-color: #EBEBEB;
        color: #333;
        text-align: left;
        line-height: 30px;
    }

    .el-main {
        background-color: #FFFFFF;
        color: #333;
        text-align: center;
        line-height: 160px;
    }

    .main {
        position: fixed;
        left: 0;
        top: 4rem;
        width: 100%;
        height: calc(100% - 4rem);
        background: #f5f5f5;
        z-index: 30;
    }

    .issueHead {
        padding: 5px 5px 5px 5px;
        height: 70%;
    }

    .issueFooter {
        padding: 5px 5px 5px 5px;
        height: 10%;
    }

    .addButton {
        margin-top: 2px;
    }

    .conHead {
        margin-right: calc(100% - 200px);
    }

    .callParamClass {
        padding: 5px 5px 5px 5px;
    }

    .serviceInterfaceClass {
        background-color: white;
    }

    .serviceForm {
        margin-top: 5px;
    }

    .reportClass {
        background-color: white;
        height: 100px;
    }

    .alarmRules {
        margin-top: 0px;
    }

    .frameWidth {
        width: 25%;
    }
</style>