import {treeMethods} from "@/api/treeNameCheck/treeName"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../service-mixins/service-mixins";
export default {
    name: "formPage" ,
    mixins : [commonMixins , servicesMixins],
    data(){
        return {
            analyse_data : {
                name : "" ,
                site : ""
            },
            analyse_form :{
                name : {
                    type : "input" ,
                    label : "名称" ,
                    placeholder : "请输入名称(限100个字符)",
                    maxlength : 100
                },
                site : {
                    type :"select_drop" ,
                    label :"位置" ,
                }
            },
            rules :{
                name : [
                    {required : true , message : "请输入名称" , trigger : ["blur" , "change"]}
                ],
                site : [
                    {required : true , message : "请选择保存位置" , trigger : ["blur" , "change"]}
                ]
            },
            treeBind : {
                "default-expand-all" : true
            },
            siteDatas : [],
            defaultProps: {
                children: "children",
                label: "label" ,
                value : "id"
            },
            nodeId : ""
        }
    },
    methods : {
        filterNode : treeMethods.methods.filterNode ,
        init(){
            const vm = this , {datasetServices , datasetMock ,settings } = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;
            services.queryDataSetTree(false , settings).then(res => {
                if (res.data.status === 0){
                    vm.siteDatas = res.data.data[0].children.map(item => {
                        if(item.code === 'DATASET_DIR_MY'){
                            vm.analyse_data.site = vm.nodeId || item.id;
                        }
                        return item;
                    });
                }
            })
        },
        validate(...arg){
            this.$refs.form.validate(...arg);
        },
        setNodeId(nodeId){
            this.nodeId = nodeId;
        }
    },
    created(){
        this.init();
    }
}