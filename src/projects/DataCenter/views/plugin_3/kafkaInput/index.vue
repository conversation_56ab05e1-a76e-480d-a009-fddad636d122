<template>
    <div class="ovh">
        <el-form ref="form" :model="baseForm" label-position="right" label-width="170px" :rules="baseRules">
            <el-form-item v-for="(item , k) in baseCon" :key="k" :prop="k" :label="item.label + ' :'" v-if="item.show(baseForm)">
                <div v-if="item.type === 'select'">
                    <dg-select
                        v-model="baseForm[k]"
                        class="selectC"
                        :data="item.option"
                        output-format="String"
                        @change="handleChange"
                    />
                </div>
                <div v-if="item.type === 'input'">
                    <el-input
                        v-model="baseForm[k]"
                    />
                </div>
                <div v-if="item.type === 'mulcSelect'">
                    <dg-select
                        v-model="baseForm[k]"
                        class="selectC"
                        multiple
                        default-first-option
                        filterable
                        allow-create
                        :data="item.option"
                        output-format="String"
                        @change="handleChange"
                    />
                </div>
                <div v-if="item.type === 'kafkaTree'">
                    <dg-tree-drop
                            class="selectC"
                            clearable
                            check-strictly
                            ref="pos_tree"
                            :placeholder="item.placeholder"
                            :props="item.defaultProps"
                            filterable
                            :filterNodeMethod="filterNode"
                            v-model="baseForm[k]"
                            :data="item.option"
                            @node-click="selectTreeChange($event, k)"
                    ></dg-tree-drop>
                </div>
            </el-form-item>
        </el-form>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";
    import {common} from "@/api/commonMethods/common"
    import PreView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {servicesMixins} from "../service-mixins/service-mixins"
    export default {
        name: "kafkaConsumption",
        mixins: [common, commonMixins, servicesMixins, treeMethods],
        props: {
            sourceData: Object,
            rowData: Object
        },
        components: {
            PreView,
        },
        data(){
            return {
                tHeadData : [],
                tableData : [],
                baseForm: {
                    kafkaSourceId : "",
                    groupId : "",
                    kafkaSourceName : "",
                    topic : "",
                    bootstrapServers : [],
                    zookeeperConnect  : [],
                    kafkaExtraConfig  : [],
                    cicadaOutputFields  : [],
                },
                baseCon: {
                    kafkaSourceId: {
                        type: 'kafkaTree',
                        label: "kafka实例名称",
                        placeholder: "请选择kafka实例名称",
                        option: [
                            {value: "f1a9cb13ac7c439abd0fdc4de16e5ea3", id: "f1a9cb13ac7c439abd0fdc4de16e5ea3", label: "kafka测试"}
                        ],
                        defaultProps: {
                            children: "children",
                            label: "name",
                            value: "schemaId",
                            disabled : 'parent'
                        },
                        show :()=> true,
                    },
                    groupId: {
                        type: 'input',
                        label: "组名称",
                        placeholder: "请输入组名称",
                        option: [],
                        show :()=> true,
                    },
                    topic: {
                        type: 'mulcSelect',
                        label: "消息主题名称",
                        placeholder: "请选择消息主题名称",
                        option: [],
                        show :()=> true,
                    },
                },
                baseRules: {
                    kafkaSourceId: [
                        {required: true, message: "请选择kafka实例名称", trigger: ['blur', 'change']}
                    ],
                    groupId: [
                        {required: true, message: "请选择组名称", trigger: ['blur', 'change']}
                    ],
                    topic: [
                        {required: true, message: "请选择消息主题名称", trigger: ['blur', 'change']}
                    ],
                },

            }
        },
        methods : {
            filterNode: treeMethods.methods.filterNode,
            selectTreeChange(val, e) {
                this.baseForm.kafkaSourceId = val.schemaId;
                this.baseForm.kafkaSourceName = val.name;
            },
            handleChange() {

            },
            preview() {
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            save() {
                const vm = this , {settings} = vm;
                settings.loading = true;
                let services = this.$services("plugin3");
                // vm.baseForm.kafkaSourceName = vm.baseCon.kafkaSourceId.option.filter(item => item.id === vm.baseForm.kafkaSourceId)[0].label;
                vm.baseForm.topics = vm.baseForm.topic.join(",");
                let data ={
                    stepId: vm.sourceData.id, 
                    settings , 
                    ...vm.baseForm
                };
                services.kafkaInputStepConfigSaveOrUpdate(data).then(res => {
                    vm.$message.success("保存成功");
                    vm.$emit("setPreviewAble");
                    //vm.$emit('reloadCanvas') //刷新画布
                })
            },
            async getKafkaList() {
                const vm = this, {settings} = vm;
                let services = this.$services("plugin3");
                settings.loading = true;
                await services.getKafkaInstanceTree({ settings }).then(res => {
                    vm.baseCon.kafkaSourceId.option = res.data.data;
                })
            },
            async getTopicsInfo(codes) {
                const vm = this, {settings} = vm;
                let services = this.$services("plugin3");
                settings.loading = true;
                await services.getFactNameByCode({codes, settings }).then(res => {
                    vm.baseForm.topic = res.data.data;
                })
            },
            /**
             * 详情回显
             */
            getDetail() {
                const vm = this , {settings} = vm;
                let services = this.$services("plugin3");
                settings.loading = true;
                services.getKafkaInputDetail({stepId: vm.sourceData.id, settings}).then(async res => {
                    let topics = res.data.data.topics.split(',');
                    await vm.getTopicsInfo(topics);
                    if(res.data.data.kafkaSourceId) {
                        vm.baseForm.kafkaSourceId = res.data.data.kafkaSourceId;
                        vm.baseForm.groupId = res.data.data.groupId;
                    }
                })
            },
            async init() {
                await this.getKafkaList();
                await this.getDetail();
            }
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped lang="less">
    

</style>