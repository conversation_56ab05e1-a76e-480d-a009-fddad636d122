import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import {transList} from "@/projects/DataCenter/views/modeling/dialog/rapid-analysis/model/model-panel/trans-list-mixins";

export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common, listMixins, coustTableH , transList],
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        }
    },
    data() {
        return {
            modelTip: '提供全程可视化的模型搭建，通过拖拽的交互方式，采用引导式及“搭积木”般的画图式界面帮助用户实现数据资源、组件的快速组合，快速完成数据分析模型的搭建。',
            analyseTip: "提供实时在线分析能力，适用于快速搭建小数据量的数据碰撞和数据统计场景的业务模型，建模结果不落地存储。",
            inputValueTable: "",
            buttonTxt: "流程建模",
            tableData: [],
            tHeadData: [
                {
                    prop: "modelName",
                    label: "名称",
                    minWidth: 150,
                    align: "left"
                },
                {
                    prop: "path",
                    label: "所属目录",
                    minWidth: 150,
                    resizable: false
                },
                {
                    prop: "userName",
                    label: "创建人",
                    minWidth: 130,
                    align: "center"
                },
                {
                    prop: "editorAndDate",
                    label: "更新时间",
                    minWidth: 160,
                    align: "center",
                    sortable: false,
                    resizable: false
                }, {
                    prop: 'operate',
                    label: '操作',
                    width: 260,
                    align: "center",
                    resizable: false
                }
            ],
            total: 0,
            operateIcons: [
                // {
                //     icon: "&#xe650;",
                //     name: "启动",
                //     clickFn: this.carryOut,
                //     show: () => true,
                //     condition: (right) => right.indexOf($right["processModelingStartJob"]) > -1
                // },
                {
                    icon: "&#xe6c0;",
                    name: "编辑",
                    clickFn: this.editModel,
                    show: () => true,
                    condition: (right) => right.indexOf($right["fastAnalysisEdit"]) > -1
                },
                /*{
                    icon: "&#xe656;",
                    tip: "配置",
                    clickFn: this.baseSetting,
                    condition: (right) => right.indexOf($right["processModelingSaseSettingPage"]) > -1
                },*/
                {
                    icon: "&#xe605;",
                    name: "重命名",
                    clickFn: this.reName,
                    show: () => true,
                    condition: (right) => right.indexOf($right["fastAnalysisUpdateTransName"]) > -1
                },
                /*{
                    icon: "<i class='el-icon-share'></i>",
                    name: "分享",
                    clickFn: this.share,
                    condition: (right) => true
                },*/
                /* {
                     name: "转建模方案",
                     clickFn: this.changeToModeling,
                     show: (right , row) =>row.taskgroup === "QUICK_SPARK",
                     condition: (right) => true
                 },*/
                // {
                //     icon: "&#xe790;",
                //     name: "预览结果",
                //     clickFn: this.previewResult,
                //     show: () => true,
                //     condition: (right) => right.indexOf($right["processModelingGetDataSets"]) > -1
                // },
                {
                    icon: "&#xe6f4;",
                    name: "另存为",
                    clickFn: this.copySolution,
                    show: () => true,
                    condition: (right) => right.indexOf($right["fastAnalysisCopyTrans"]) > -1
                },
                {
                    icon: "&#xe6bf;",
                    name: "移动到",
                    clickFn: this.moveTo,
                    show: () => true,
                    condition: (right) => right.indexOf($right["fastAnalysisQueryTransTree"]) > -1
                },/*{
                    icon: "&#xe856;",
                    name: "移交",
                    clickFn: this.transfer,
                    condition: (right) => true
                },
                {
                    icon: "&#xe886;",
                    name: "发布",
                    clickFn: this.serveIssue,
                    condition: () => true
                },*/
                //分割线
                /*{
                    name: "divider",
                    show: () => true,
                    condition: (right) => right.indexOf($right["fastAnalysisQueryTransTree"]) > -1 && right.indexOf($right["fastAnalysisDeleteTrans"]) > -1
                },*/
                {
                    icon: "&#xe65f;",
                    name: "删除",
                    clickFn: this.deleteModel,
                    show: () => true,
                    condition: (right) => right.indexOf($right["fastAnalysisDeleteTrans"]) > -1
                },
            ],
            dirId: "-1",
            dirType: "TRANS_DIR_MF",
            analyseR:$right['fastAnalysisSaveTempTrans'],
            analyseTxt: "快速分析",
            outputTxt: "导出模型",
            inputTxt: "导入模型",
        }
    },
    methods: {
        /**
         * 转建模
         * @param row
         * @param index
         */
        changeToModeling(row , index){
            const vm = this;
            vm.confirm('转建模方案' , `确定将快速分析 "${row.modelName}" 转为建模方案` , async()=>{
                await vm.getTransList(row);
            })
        },
        async getTransList(rowData){
            const vm = this, {services ,settings} = this;
            settings.loading = true;
            const {data} = await services.loadTransPage(rowData.transId);
            if (data.status === 0) {
                const resD = data.data;
                let allNodes = [];
                let firstTransId = resD.firstTransId;
                vm.setLineList(resD);
                allNodes = await vm.setAllNodes(resD.transMetaVo.children);
                await vm.pushNodeByLine(resD.transMetaVo.hops , allNodes , firstTransId);
                let plugins = vm.setNodesPosition(allNodes);
                const result = await services.quickTransToProcessTrans(rowData.transId , plugins);
                if(result.data.status === 0){
                    vm.$message.success("转换成功");
                }
                vm.changePage(1);
            }
        },

        /**
         * 导入
         */
        inputModel() {
        },
        /**
         * 导出
         */
        outputModel() {
        },
        /**
         * 快速分析
         */
        rapidAnalysis() {
            const vm = this;
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/rapid-analysis/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                area: ["100%", "100%"],
                props: {
                    dirId: this.dirId
                },
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab({}, false, this.dirId);
        },
        serveIssue(row) {
            this.$emit("serveIssue", row)
        },
        share(row) {
            this.$emit("share", row);
        },
        transfer(row) {
        },
        previewResult(row) {
            this.$emit("previewResult", row);
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
        deleteInit(data) {
            this.dirId = data.id;
            this.dirType = data.name === "我的" ? "TRANS_DIR_MF" : data.dirType;
            this.changePage(1);
        },
        update() {
            this.changePage(this.currentPage);
        },
        changePage(index) {
            const vm = this, {rapidAnalysisServices , rapidAnalysisMock, settings} = this;
            let services = vm.getServices(rapidAnalysisServices , rapidAnalysisMock);
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = { // 查询的条件写这里 , 条件
                pageSize: vm.paginationProps.pageSize,
                pageNum: index,
                rapidAnaName: vm.inputValueTable,
                classifyId: vm.dirId,
            };
            vm.tableData = [];
            services.getRapidAnaPage(data, settings).then(res => {
                if (res.data.status === 0) {
                    let callbackData = res.data.data;
                    vm.tableData = [];
                    if (callbackData.dataList.length) {
                        callbackData.dataList.forEach(item => {
                            let data = {
                                modelName: item.name,
                                userName: item.username,
                                editorAndDate: item.updateTime,
                                releaseDate: item.updateTime,
                                modelType: item.classifyname,
                                modelSpec: item.memo,
                                statusCode: 0,//&#xe66b
                                transId: item.id,
                                dirParentId: item.parentClassifyId,
                                isNew: false,
                                taskgroup: item.taskGroup,
                                path: item.path
                            };
                            vm.tableData.push(data);
                        });
                        vm.total = callbackData.totalCount;
                    }
                }
            })

        },
        searchTableEvent() {
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        openNewModel() {
            // this.$emit('openNewModel' , this.dirId);
            const vm = this;
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: this.dirId
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab({}, false, this.dirId);
        },
        //操作
        carryOut(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            vm.confirm('提示', '此操作将执行任务, 是否继续?', () => {
                vm.$message.success("开始执行！");
                services.runJob(row.transId).then(res => {
                    console.log(res.data.data);
                })
            })
        },
        startJob(row) {
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            services.startJob(row.transId).then(res => {
                if (res.data.status === 0) {
                    vm.$message({
                        type: 'success',
                        message: "开始执行",
                    })
                }
            })
        },
        editModel(row) {
            // this.$emit("modelEdit", row);
            let page = require("@/projects/DataCenter/views/modeling/dialog/rapid-analysis/index.vue");

            const vm = this;
            let layer = this.$dgLayer({
                content: page,
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: this.dirId
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        vm.changePage(1);
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab(row, false);
        },
        baseSetting(row) {
            this.$emit("tableItemEvent", row);
        },
        copySolution(row) {
            this.$emit("saveOther", row , 'list');
            // const vm = this, {modelingServices, modelingMock} = this;
            // let services = vm.getServices(modelingServices, modelingMock);
            // vm.confirm("复制", `确认复制 \'${row.modelName}\' 方案？`, () => {
            //     services.copyTrans(row.transId, row.dirParentId).then(res => {
            //         if (res.data.status === 0) {
            //             vm.$message({
            //                 type: 'success',
            //                 message: "复制成功",
            //             })
            //             this.changePage(1);
            //         }
            //     })
            // })
        },
        reName(row, index) {
            this.$emit("reName", row, index,'QUICK_SPARK');
        },
        moveTo(row) {
            this.$emit("moveTo", row, "dataCenter", "move", "QUICK_SPARK");
        },
        deleteModel(row) {
            const vm = this, {rapidAnalysisServices , rapidAnalysisMock} = this;
            let services = vm.getServices(rapidAnalysisServices , rapidAnalysisMock);

            vm.confirm('提示', `此操作将删除 \"${row.modelName}\" 方案, 是否继续?`, () => {
                services.deleteTransRapid(row.transId).then(res => {
                    if (res.data.status === 0) {
                        vm.$message({
                            type: 'success',
                            message: '删除成功!',
                        });
                        vm.changePage(1);
                    }
                })
            })
        },
        setDirType(value, id, node) {
            if (id === "0") {
                this.dirId = "-1";
                this.dirType = "TRANS_DIR_MF";
            } else if ((value === "我的" && node.data.pId === "0")) {
                this.dirId = id;
                this.dirType = "TRANS_DIR_MF";
            } else if (value === "标准模型") {
                this.dirId = id;
                this.dirType = node.data.dirType;
            } else {
                this.dirId = id;
                this.dirType = value;
            }
            this.changePage(1);
        },
        tDataChangeWithName(newName, index) {
            const vm = this;
            vm.tableData.forEach((tab, i) => {
                if (i === index) {
                    tab.modelName = newName
                }
            });
        },
        getJobStatus() {
            this.tableData.forEach((item, i) => {
                this.$axios.get("/transSchedule/jobStatus?transId=" + item.transId)
                    .then(res => {
                        // console.log("状态码：",res.data,this.tableData.length);
                        // item.statusCode = res.data.status;
                        if (res.data.status === 0) {
                            if (res.data.data.status === "none") {
                                item.statusCode = 0;
                            } else if (res.data.data.status === "success") {
                                item.statusCode = 1;
                            } else if (res.data.data.status === "execing") {
                                item.statusCode = 5;
                            } else {
                                item.statusCode = 2;
                            }
                        }

                    })
                    .catch(err => {
                        console.log(err);
                    })
            });
        },
    },
    created() {
        // this.changePage(1);
    }
}
