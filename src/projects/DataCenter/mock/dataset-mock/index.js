const data = {
    queryDataSetTree: {
        "data": {
            "status": 0,
            "code": 0,
            "msg": null,
            "data": [{
                "id": "a06a65efd6e2476987acfcf76fdwipq",
                "pId": "-1",
                "name": "全部",
                "code": "DATA_SET_DIR",
                "extendedType": null,
                "level": 1,
                "isParent": false,
                "open": true,
                "childOuter": false,
                "children": [{
                    "id": "3033f5380cc440769f4cde53a2142848",
                    "pId": "a06a65efd6e2476987acfcf76fdwipq",
                    "name": "我的",
                    "code": "DATASET_DIR_MY",
                    "extendedType": "全部",
                    "level": 2,
                    "isParent": true,
                    "open": false,
                    "childOuter": false,
                    "children": [{
                        "id": "f947bde29d7b443093cbbf6bbe491f2e",
                        "pId": "3033f5380cc440769f4cde53a2142848",
                        "name": "电诈",
                        "code": "DATASET_DIR",
                        "extendedType": null,
                        "level": null,
                        "isParent": false,
                        "open": true,
                        "childOuter": false,
                        "children": null,
                        "label": "电诈",
                        "instanceType": null,
                        "schemaId": null,
                        "catalogId": null,
                        "machineIp": null,
                        "belongType": null,
                        "dbType": null,
                        "globalCode": null,
                        "fields": null
                    }, {
                        belongType: null,
                        catalogId: null,
                        childOuter: false,
                        children: [],
                        code: "DATASET_DIR",
                        dataObjId: null,
                        dbType: null,
                        extendedType: null,
                        fields: [],
                        globalCode: null,
                        id: "92f1090b9ac24c45b9cd92311aee54e4",
                        instanceType: null,
                        isParent: false,
                        label: "涉稳群体",
                        level: null,
                        machineIp: null,
                        msg: null,
                        name: "涉稳群体",
                        open: true,
                        pId: "e8383342b644471b821757f2bfea6600",
                        schemaId: null,
                        treeType: null,
                    }],
                    "label": "我的",
                    "instanceType": null,
                    "schemaId": null,
                    "catalogId": null,
                    "machineIp": null,
                    "belongType": null,
                    "dbType": null,
                    "globalCode": null,
                    "fields": null
                }],
                "label": "全部",
                "instanceType": null,
                "schemaId": null,
                "catalogId": null,
                "machineIp": null,
                "belongType": null,
                "dbType": null,
                "globalCode": null,
                "fields": null
            }]
        }

    },
    addDataSetTreeNode: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "400",
            "msg": "[1],已存在，请重新输入！",
            "detailErrorMsg": null,
            "data": null
        }

    },
    reNameDataSetTreeNode: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "400",
            "msg": "[]已存在，请重新输入！",
            "detailErrorMsg": null,
            "data": null
        }

    },
    deleteDataSetTreeNode: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "节点不存在",
            "detailErrorMsg": "com.code.common.utils.assertion.IllegalLogicException: 节点不存在",
            "data": null
        }

    },
    accreditLogicDataObj: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-405",
            "msg": "不允许使用请求指定的方法(Method Not Allowed)",
            "detailErrorMsg": null,
            "data": null
        }

    },
    checkHasDataSet: {},
    createLogicDataSet: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": {
                "name": "1",
                "id": "64336adf22cc4946b345ade971353996"
            }
        }

    },
    deleteDataSet: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "逻辑数据集不能为空!",
            "detailErrorMsg": "java.lang.IllegalArgumentException: 逻辑数据集不能为空!",
            "data": null
        }

    },
    getDataSetColumn: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": []
        }
    },
    getDataSetPage: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": {
                "staInfo": null,
                "totalCount": 2,
                "pageSize": 10,
                "pageCount": 25,
                "pageIndex": 1,
                "limitPageCount": 0,
                "sortField": "1",
                "sortFieldMap": null,
                "sortDirect": "DESC",
                "dataList": [
                    {
                        belong_type: "PHYSICAL",
                        code: "tj_qz_dw.t_js_ry_zq_qtsjslbhl",
                        db_type: "GREENPLUM",
                        id: "49424c61e5a44c02aed692ad6144ca57",
                        is_fast: "0",
                        name: "周期群体事件数量变化率",
                        operateUserName: "admin",
                        operate_time: "2021-01-07 15:33:18",
                        operate_user_id: "40289754739d4e7e11729d4e682b2020",
                        owner_id: "db6a4035cadb43708304947e90fbaa78",
                        parentid: "92f1090b9ac24c45b9cd92311aee54e4",
                        user: "无"
                }, {
                        belong_type: "PHYSICAL",
                        code: "tj_qz_dw.t_js_ry_yssrymx",
                        db_type: "GREENPLUM",
                        id: "3280f61e89d34860a589500d8d10d94d",
                        is_fast: "0",
                        name: "易受损人群明细表",
                        operateUserName: "admin",
                        operate_time: "2020-12-16 16:21:43",
                        operate_user_id: "40289754739d4e7e11729d4e682b2020",
                        owner_id: "d7ac406e1d9e41dea3e22a3970511e96",
                        parentid: "c0beb30a3eab4fe28354ec49501bf329",
                        user: "无"
                }]
            }
        }


    },
    queryDataSetById: {},
    updateDataSet: {},
    addDataSetColumn: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-405",
            "msg": "不允许使用请求指定的方法(Method Not Allowed)",
            "detailErrorMsg": null,
            "data": null
        }

    },
    changeIndexType: {},
    editDataSetColumn: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-405",
            "msg": "不允许使用请求指定的方法(Method Not Allowed)",
            "detailErrorMsg": null,
            "data": null
        }

    },
    getDataSetColumnInfo: {},
    getDataSetStepRelation: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": [{
                "dataSetStepId": "7037f741a9054ba09c7d3b308a87c489",
                "name": "编辑字段",
                "code": "editColumn"
            }]
        }

    },
    getLogicDataColumn: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": {
                "name": "金融_财务数据_财务指标",
                "dataSetId": "a5d643546a7d46e6841ea64779771670",
                "code": "T_JR_CWSJ_CWZB",
                "relationId": null,
                "dimension": [{
                    "name": "公司名称",
                    "code": "GSMC",
                    "id": "0bfea6db15124e08bcc7dd583eb9242e",
                    "indexType": "DIMENSION",
                    "memo": "公司名称",
                    "dataTypeId": "4028f9fc59a545110159a545736a009c",
                    "format": "TEXT",
                    "numberFormat": null
                }, {
                    "name": "巨龙_分区日期",
                    "code": "JL_FQRQ",
                    "id": "6571d5aa00aa404c8444ee73395e4b16",
                    "indexType": "DIMENSION",
                    "memo": "巨龙_分区日期",
                    "dataTypeId": "4028f9fc59a545110159a545736b00a5",
                    "format": "",
                    "numberFormat": null
                }, {
                    "name": "巨龙_入库时间",
                    "code": "JL_RKSJ",
                    "id": "071da09324ad44e2b75afdea52f34dbc",
                    "indexType": "DIMENSION",
                    "memo": "巨龙_入库时间",
                    "dataTypeId": "4028f9fc59a545110159a545736b00a5",
                    "format": "",
                    "numberFormat": null
                }, {
                    "name": "数据来源",
                    "code": "SJLY",
                    "id": "7caa2efe4b9749a6a8388ec73f12cbdb",
                    "indexType": "DIMENSION",
                    "memo": "数据来源",
                    "dataTypeId": "4028f9fc59a545110159a545736a009c",
                    "format": "TEXT",
                    "numberFormat": null
                }, {
                    "name": "填表日期",
                    "code": "TBRQ",
                    "id": "a33e8235aba34198867d100fbe79e8aa",
                    "indexType": "DIMENSION",
                    "memo": "填表日期",
                    "dataTypeId": "4028f9fc59a545110159a545736b00a5",
                    "format": "",
                    "numberFormat": null
                }, {
                    "name": "证券代码",
                    "code": "ZQDM",
                    "id": "1ff99107ccb742278285be4291409d27",
                    "indexType": "DIMENSION",
                    "memo": "证券代码",
                    "dataTypeId": "4028f9fc59a545110159a545736a009c",
                    "format": "TEXT",
                    "numberFormat": null
                }],
                "measure": [{
                    "name": "3年以内_应收帐款_元",
                    "code": "3NYN_YSZK_YUAN",
                    "id": "4dcd75a8554d48268f97e2b74bf63167",
                    "indexType": "MEASURE",
                    "memo": "3年以内_应收帐款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "成本费用利润率",
                    "code": "CBFYLRL",
                    "id": "992ecce12d514f00a8d8f17954b6cf4f",
                    "indexType": "MEASURE",
                    "memo": "成本费用利润率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "存货周转率_次",
                    "code": "CHZZL_CI",
                    "id": "0c5ad5bfae86465cb226ca61107b0adb",
                    "indexType": "MEASURE",
                    "memo": "存货周转率_次",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "存货周转天数",
                    "code": "CHZZTS",
                    "id": "5f66870ab43a485baf4ebc8cba26ff86",
                    "indexType": "MEASURE",
                    "memo": "存货周转天数",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "产权比率",
                    "code": "CQBL",
                    "id": "9709a480a0674e8e82801ff4ea5e66b4",
                    "indexType": "MEASURE",
                    "memo": "产权比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期负债比率",
                    "code": "CQFZBL",
                    "id": "cf09d771d81b4820ad6d80dae2c0d154",
                    "indexType": "MEASURE",
                    "memo": "长期负债比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期负债股东权益比率",
                    "code": "CQFZGDQYBL",
                    "id": "9548fbc171d24aadac6f12c0ef7b7d04",
                    "indexType": "MEASURE",
                    "memo": "长期负债股东权益比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期债务与营运资金比率",
                    "code": "CQFZYYYZJBL",
                    "id": "bc4d7818df8b45f3bcc8f3fa37ff828f",
                    "indexType": "MEASURE",
                    "memo": "长期债务与营运资金比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期股票投资_元",
                    "code": "CQGPTZ_YUAN",
                    "id": "448361f30b40417b91b0c27bdcd3f757",
                    "indexType": "MEASURE",
                    "memo": "长期股票投资_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期其它经营性投资_元",
                    "code": "CQJTJYXTZ_YUAN",
                    "id": "c66342fb44be420b98419a32448aecee",
                    "indexType": "MEASURE",
                    "memo": "长期其它经营性投资_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期资产与长期资金比率",
                    "code": "CQZCYCQZJBL",
                    "id": "49ec1f1fead94d93a2bc6dca34f6c201",
                    "indexType": "MEASURE",
                    "memo": "长期资产与长期资金比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "长期债券投资_元",
                    "code": "CQZQTZ_YUAN",
                    "id": "b4bdd3b0bb9c4f86ba4b773d64eb3643",
                    "indexType": "MEASURE",
                    "memo": "长期债券投资_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "短期股票投资_元",
                    "code": "DQGPTZ_YUAN",
                    "id": "53b5831b4f0f4871b7e70c9f8e25cafe",
                    "indexType": "MEASURE",
                    "memo": "短期股票投资_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "短期其它经营性投资_元",
                    "code": "DQQTJYXTZ_YUAN",
                    "id": "0df1c6167e4c4aec9f98010154b9ef64",
                    "indexType": "MEASURE",
                    "memo": "短期其它经营性投资_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "短期债券投资_元",
                    "code": "DQZQTZ_YUAN",
                    "id": "78dd7a750fc24b73ac35dfef2b49927c",
                    "indexType": "MEASURE",
                    "memo": "短期债券投资_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "非主营比重",
                    "code": "FZYBZ",
                    "id": "8cbeb068c6984555ad83ebcc492c9e63",
                    "indexType": "MEASURE",
                    "memo": "非主营比重",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "负债与净资产比率",
                    "code": "FZYJZCBL",
                    "id": "516d1716e5b34f65b597c78c89edae13",
                    "indexType": "MEASURE",
                    "memo": "负债与净资产比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "股本报酬率",
                    "code": "GBBCL",
                    "id": "27b9627c206346659099159ef6a74d87",
                    "indexType": "MEASURE",
                    "memo": "股本报酬率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "1到2年以内_其它应收款_元",
                    "code": "1D2NN_QTYSK_YUAN",
                    "id": "8b8941b5922a4c12bd01ff5d561fb944",
                    "indexType": "MEASURE",
                    "memo": "1到2年以内_其它应收款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "1到2年以内_预付货款_元",
                    "code": "1D2NN_YFHK_YUAN",
                    "id": "7264d079aaf34344b47b98f173b0d85d",
                    "indexType": "MEASURE",
                    "memo": "1到2年以内_预付货款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "1到2年以内_应收帐款_元",
                    "code": "1D2NN_YSZK_YUAN",
                    "id": "ad23bbe013914adb846246f0939214b0",
                    "indexType": "MEASURE",
                    "memo": "1到2年以内_应收帐款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "1年内_其它应收款_元",
                    "code": "1NN_QTYSK_YUAN",
                    "id": "cc026cb4eece4a67b9213c75f394b456",
                    "indexType": "MEASURE",
                    "memo": "1年内_其它应收款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "1年内_预付货款_元",
                    "code": "1NN_YFHK_YUAN",
                    "id": "6d9b66daa00c4c2e806692130acc812f",
                    "indexType": "MEASURE",
                    "memo": "1年内_预付货款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "1年内_应收帐款_元",
                    "code": "1NN_YSZK_YUAN",
                    "id": "c601cf015fa444d5a6502914da95ed86",
                    "indexType": "MEASURE",
                    "memo": "1年内_应收帐款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "2到3年以内_其它应收款_元",
                    "code": "2D3NYN_QTYSK_YUAN",
                    "id": "ca8b1f6ddd4549b19d27eccfc59f0ac6",
                    "indexType": "MEASURE",
                    "memo": "2到3年以内_其它应收款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "2到3年以内_预付货款_元",
                    "code": "2D3NYN_YFHK_YUAN",
                    "id": "a0c9c0f17e874945ac20d031d74a39c2",
                    "indexType": "MEASURE",
                    "memo": "2到3年以内_预付货款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "2到3年以内_应收帐款_元",
                    "code": "2D3NYN_YSZK_YUAN",
                    "id": "acc533a80cc14a268091279f3a7563d3",
                    "indexType": "MEASURE",
                    "memo": "2到3年以内_应收帐款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "3年以内_其它应收款_元",
                    "code": "3NYN_QTYSK_YUAN",
                    "id": "efbee1b6bec7419a8ecc177eaba6b061",
                    "indexType": "MEASURE",
                    "memo": "3年以内_其它应收款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "3年以内_预付货款_元",
                    "code": "3NYN_YFHK_YUAN",
                    "id": "0fa9b25fb288412eab5b39fb4c133dcd",
                    "indexType": "MEASURE",
                    "memo": "3年以内_预付货款_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "固定资产比重",
                    "code": "GUDZCBZ",
                    "id": "a7830f323e244c06a5069dd546178065",
                    "indexType": "MEASURE",
                    "memo": "固定资产比重",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "固定资产净值率",
                    "code": "GUDZCJZL",
                    "id": "1e663830040042b9a1414e4448d38f08",
                    "indexType": "MEASURE",
                    "memo": "固定资产净值率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "固定资产周转率_次",
                    "code": "GUDZCZZL_CI",
                    "id": "678d78f5a98242138d3a953761f0f443",
                    "indexType": "MEASURE",
                    "memo": "固定资产周转率_次",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "股息发放率",
                    "code": "GXFFL",
                    "id": "ff7d824b92f148c9a18fcdf2fa020055",
                    "indexType": "MEASURE",
                    "memo": "股息发放率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净利润增长率",
                    "code": "JLRZZL",
                    "id": "0ed396ade5154ac4b2632616f6a65397",
                    "indexType": "MEASURE",
                    "memo": "净利润增长率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "加权净资产收益率",
                    "code": "JQJZCSYL",
                    "id": "be94a0d3918f456c9d9ff43f7d061433",
                    "indexType": "MEASURE",
                    "memo": "加权净资产收益率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "加权每股收益_元",
                    "code": "JQMGSY_YUAN",
                    "id": "2ef2223495764d18a26bb3c67b3293fb",
                    "indexType": "MEASURE",
                    "memo": "加权每股收益_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "经营现金净流量对负债比率",
                    "code": "JYXJJLLDFZBL",
                    "id": "94fdb743c5c44c5a8b9707c1213559fc",
                    "indexType": "MEASURE",
                    "memo": "经营现金净流量对负债比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "经营现金净流量对销售收入比率",
                    "code": "JYXJJLLDXSSRBL",
                    "id": "cc8ce1ace77448ca87f6b647607f826f",
                    "indexType": "MEASURE",
                    "memo": "经营现金净流量对销售收入比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "经营现金净流量与净利润比率",
                    "code": "JYXJJLLYJLRBL",
                    "id": "5ed12088c92f4cf8ad673efebaece16d",
                    "indexType": "MEASURE",
                    "memo": "经营现金净流量与净利润比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净资产报酬率",
                    "code": "JZCBCL",
                    "id": "10350473a74349cc941b73644c864ea4",
                    "indexType": "MEASURE",
                    "memo": "净资产报酬率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净资产比率",
                    "code": "JZCBL",
                    "id": "597d7bc2c77e461a8290f3f3452d53d7",
                    "indexType": "MEASURE",
                    "memo": "净资产比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净资产收益率",
                    "code": "JZCSYL",
                    "id": "04ab0c642ab14786b66baa233eed94bb",
                    "indexType": "MEASURE",
                    "memo": "净资产收益率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净资产与固定资产比率",
                    "code": "JZCYGDZCBL",
                    "id": "2f5d64cc962d446387a28777ae09399e",
                    "indexType": "MEASURE",
                    "memo": "净资产与固定资产比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净资产周转率_次",
                    "code": "JZCZHZL_CI",
                    "id": "01d4f584961446efa1b6864c6587e054",
                    "indexType": "MEASURE",
                    "memo": "净资产周转率_次",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "净资产增长率",
                    "code": "JZCZZL",
                    "id": "1493b269a7804e619ebc51d4e57db920",
                    "indexType": "MEASURE",
                    "memo": "净资产增长率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "扣除非经常性损益_净利润_元",
                    "code": "KCFJCXSY_JLR_YUAN",
                    "id": "f7cacc25ae2b45b18fd925ecb4ae210b",
                    "indexType": "MEASURE",
                    "memo": "扣除非经常性损益_净利润_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "扣除非经常性损益_每股收益_元",
                    "code": "KCFJCXSY_MGSY_YUAN",
                    "id": "c14c54556153407fb152cdba93f7c52e",
                    "indexType": "MEASURE",
                    "memo": "扣除非经常性损益_每股收益_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "流动比率",
                    "code": "LDBL",
                    "id": "53b5aa4e7acd455fb47e0d8e4ae93def",
                    "indexType": "MEASURE",
                    "memo": "流动比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "流动资产周转率_次",
                    "code": "LDZCZZL_CI",
                    "id": "36743cf423454c1d8ca4a1bb428add02",
                    "indexType": "MEASURE",
                    "memo": "流动资产周转率_次",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "流动资产周转天数",
                    "code": "LDZCZZTS",
                    "id": "0aad98776a9649e789151efac44a4d88",
                    "indexType": "MEASURE",
                    "memo": "流动资产周转天数",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "利息支付倍数",
                    "code": "LXZFBS",
                    "id": "195e0b6ab1d14270aad68af371fe1421",
                    "indexType": "MEASURE",
                    "memo": "利息支付倍数",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "每股经营性现金流_元",
                    "code": "MGJYXXJL_YUAN",
                    "id": "d6f429599d6044ef98965cd263391574",
                    "indexType": "MEASURE",
                    "memo": "每股经营性现金流_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "每股未分配利润_元",
                    "code": "MGWFPLR_YUAN",
                    "id": "a5434fd8c63b4672906046f8184ccea1",
                    "indexType": "MEASURE",
                    "memo": "每股未分配利润_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "每股资本公积金_元",
                    "code": "MGZBGJJ_YUAN",
                    "id": "d7632bb2c67043b28af8abbf1a83f86d",
                    "indexType": "MEASURE",
                    "memo": "每股资本公积金_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "清算价值比率",
                    "code": "QSJZBL",
                    "id": "33c3cbacee72478cacb9d070bcdc705d",
                    "indexType": "MEASURE",
                    "memo": "清算价值比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "速动比率",
                    "code": "SDBL",
                    "id": "fcf083cab5a746fe9ccc5ef773a0e155",
                    "indexType": "MEASURE",
                    "memo": "速动比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "三项费用比重",
                    "code": "SXFYBZ",
                    "id": "bf0b05ac1c714daaabca87281e9228a3",
                    "indexType": "MEASURE",
                    "memo": "三项费用比重",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "摊薄每股收益_元",
                    "code": "TBMGSY_YUAN",
                    "id": "f4d830271b9545ccb2e07a3fc56a4420",
                    "indexType": "MEASURE",
                    "memo": "摊薄每股收益_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "调整后_每股净资产_元",
                    "code": "TZH_MGJZC_YUAN",
                    "id": "765767bcea254c08bfbba40909fad377",
                    "indexType": "MEASURE",
                    "memo": "调整后_每股净资产_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "调整后_每股收益_元",
                    "code": "TZH_MGSY_YUAN",
                    "id": "46e418fdee974c67b9296f42add427e1",
                    "indexType": "MEASURE",
                    "memo": "调整后_每股收益_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "调整前_每股净资产_元",
                    "code": "TZQ_MGJZC_YUAN",
                    "id": "100132fa448f4834b3491ee785b09909",
                    "indexType": "MEASURE",
                    "memo": "调整前_每股净资产_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "投资收益率",
                    "code": "TZSYL",
                    "id": "99544899ca724a92b7ba986c868bb286",
                    "indexType": "MEASURE",
                    "memo": "投资收益率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "现金比率",
                    "code": "XJBL",
                    "id": "2e6b9bccde554724b79efbda6b3023d6",
                    "indexType": "MEASURE",
                    "memo": "现金比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "现金流量比率",
                    "code": "XJLLBL",
                    "id": "837036546d4343039099e8432dbbc910",
                    "indexType": "MEASURE",
                    "memo": "现金流量比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "销售净利率",
                    "code": "XSJLL",
                    "id": "926957e7e9bf4953b44b0784c23dce89",
                    "indexType": "MEASURE",
                    "memo": "销售净利率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "销售毛利率",
                    "code": "XSMLL",
                    "id": "8614cafac6a54d0d9a1b332d933e2897",
                    "indexType": "MEASURE",
                    "memo": "销售毛利率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "应收账款周转率_次",
                    "code": "YSZKZZL_CI",
                    "id": "69798a0a82e34aedb14c29b5939c3659",
                    "indexType": "MEASURE",
                    "memo": "应收账款周转率_次",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "应收账款周转天数",
                    "code": "YSZKZZTS",
                    "id": "c4483a06cf914caaa68ffeba02518801",
                    "indexType": "MEASURE",
                    "memo": "应收账款周转天数",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "营业利润率",
                    "code": "YYLRL",
                    "id": "69e23bc997f24953a3381bcaa216231d",
                    "indexType": "MEASURE",
                    "memo": "营业利润率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "资本固定化比率",
                    "code": "ZBGDHBL",
                    "id": "2c69b647cf6f4b14aa653bca5c8698b0",
                    "indexType": "MEASURE",
                    "memo": "资本固定化比率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "资产负债率",
                    "code": "ZCFZL",
                    "id": "18019603188b4c2cba62e7b1628108b5",
                    "indexType": "MEASURE",
                    "memo": "资产负债率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "资产经营现金流量回报率",
                    "code": "ZCJYXJLLHBL",
                    "id": "f6d4585141884fa9aa6f737aeae7bb4f",
                    "indexType": "MEASURE",
                    "memo": "资产经营现金流量回报率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "主营利润比重",
                    "code": "ZYLRBZ",
                    "id": "17d3e1f831264bbdbf70cf31a6ba78d2",
                    "indexType": "MEASURE",
                    "memo": "主营利润比重",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "主营业务成本率",
                    "code": "ZYYWCBL",
                    "id": "02e7e9a0eebd4eaca748461998db7761",
                    "indexType": "MEASURE",
                    "memo": "主营业务成本率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "主营业务利润率",
                    "code": "ZYYWLRL",
                    "id": "5fec06ea9f034e759a140d73263fc265",
                    "indexType": "MEASURE",
                    "memo": "主营业务利润率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "主营业务利润_元",
                    "code": "ZYYWLR_YUAN",
                    "id": "fcb25d42ce43494d8b1039139459e0e0",
                    "indexType": "MEASURE",
                    "memo": "主营业务利润_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "主营业务收入增长率",
                    "code": "ZYYWSRZZL",
                    "id": "617fa0816a414121b60d450cbab8ee55",
                    "indexType": "MEASURE",
                    "memo": "主营业务收入增长率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产报酬率",
                    "code": "ZZCBCL",
                    "id": "67fa1a5e51774f13a89ad34c178d7c83",
                    "indexType": "MEASURE",
                    "memo": "总资产报酬率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产净利润率",
                    "code": "ZZCJLRL",
                    "id": "53fe71e24cda42709fe98582459b3986",
                    "indexType": "MEASURE",
                    "memo": "总资产净利润率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产利润率",
                    "code": "ZZCLRL",
                    "id": "0b15b5d38894498ab4afbf3f7b99a598",
                    "indexType": "MEASURE",
                    "memo": "总资产利润率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产_元",
                    "code": "ZZC_YUAN",
                    "id": "ba770ee80f774d41857897b14ed0c172",
                    "indexType": "MEASURE",
                    "memo": "总资产_元",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产周转率_次",
                    "code": "ZZCZHZL_CI",
                    "id": "cdbe2a9901e648279a13a55dc102e22d",
                    "indexType": "MEASURE",
                    "memo": "总资产周转率_次",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产周转天数",
                    "code": "ZZCZHZTS",
                    "id": "0daab74941814ea4b920b7f3b834ea7e",
                    "indexType": "MEASURE",
                    "memo": "总资产周转天数",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }, {
                    "name": "总资产增长率",
                    "code": "ZZCZZL",
                    "id": "912f950aad8448d7b0eb0132f9c44027",
                    "indexType": "MEASURE",
                    "memo": "总资产增长率",
                    "dataTypeId": "4028f9fc59a545110159a545736a009e",
                    "format": "NUMBER",
                    "numberFormat": null
                }],
                "newColumn": null
            }
        }

    },
    getSyncLogicDataColumn: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": {
                "name": "test08244",
                "dataSetId": "a66855fd69a84ca180e45ed51797a2e9",
                "code": "test08244",
                "relationId": null,
                "dimension": null,
                "measure": null,
                "newColumn": []
            }
        }
    },
    previewLogicDataSet: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-405",
            "msg": "不允许使用请求指定的方法(Method Not Allowed)",
            "detailErrorMsg": null,
            "data": null
        }

    },
    save: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-405",
            "msg": "不允许使用请求指定的方法(Method Not Allowed)",
            "detailErrorMsg": null,
            "data": null
        }

    },
    saveLogicDataColumn: {},
    sqlColumns: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "请检查SQL语句是否正确!",
            "detailErrorMsg": "com.code.common.utils.assertion.IllegalLogicException: 请检查SQL语句是否正确!",
            "data": null
        }

    },
    sqlCreate: {},
    sqlPreview: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "查询异常，请检查SQL语句是否正确!",
            "detailErrorMsg": "com.code.common.utils.assertion.IllegalLogicException: 查询异常，请检查SQL语句是否正确!",
            "data": null
        }

    },
    registered: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": {
                "registerFinish": true,
                "registerInfos": [{
                    "globalCode": "GB_ORCL_CICADA_BM_TSZC",
                    "label": "BM_TSZC",
                    "code": "BM_TSZC",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_TX",
                    "label": "BM_TX",
                    "code": "BM_TX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_10",
                    "label": "gbase_operate_log_10",
                    "code": "gbase_operate_log_10",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_TEST0824",
                    "label": "test0824",
                    "code": "test0824",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_INSTANCE_QQY_0820_ZHONG4DIAN3REN2",
                    "label": "instance_qqy_0820_zhong4dian3ren2",
                    "code": "instance_qqy_0820_zhong4dian3ren2",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GSGG",
                    "label": "金融_公司资料_公司高管",
                    "code": "T_JR_GSZL_GSGG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_58B547C6DAC8440791FC77C22DFC2456_TAO2PAO3",
                    "label": "58b547c6dac8440791fc77c22dfc2456_tao2pao3",
                    "code": "58b547c6dac8440791fc77c22dfc2456_tao2pao3",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_GA_JZXYR_RENYUSHOUJI",
                    "label": "T_GA_JZXYR_RENYUSHOUJI",
                    "code": "T_GA_JZXYR_RENYUSHOUJI",
                    "tableType": "ENTITY",
                    "fast": true,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH27_ADMIN",
                    "label": "service_publish27_admin",
                    "code": "service_publish27_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_LKCRJBS",
                    "label": "旅客出入境标识",
                    "code": "BM_LKCRJBS",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_TEST08245",
                    "label": "test08245",
                    "code": "test08245",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_LZ_TEST_0713",
                    "label": "lz_test_0713",
                    "code": "lz_test_0713",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_TEST08244",
                    "label": "test08244",
                    "code": "test08244",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_TEST08246",
                    "label": "test08246",
                    "code": "test08246",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY",
                    "label": "警员信息表",
                    "code": "T_BASIC_JY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HYZK",
                    "label": "BM_HYZK",
                    "code": "BM_HYZK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_XGFX",
                    "label": "金融_发行分配_新股发行",
                    "code": "T_JR_FXFP_XGFX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR35_ADMIN",
                    "label": "weimr35_admin",
                    "code": "weimr35_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JCZW",
                    "label": "BM_JCZW",
                    "code": "BM_JCZW",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_JHYS_0821",
                    "label": "JHYS_0821",
                    "code": "JHYS_0821",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_ONE",
                    "label": "T_BASIC_JY_ONE",
                    "code": "T_BASIC_JY_ONE",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_ZSZTC_LXZ",
                    "label": "BM_ZSZTC_LXZ",
                    "code": "BM_ZSZTC_LXZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DC_POSTGRES_PUBLIC_DOMAIN_DATAPROPERTY_DEFAULT",
                    "label": "public.domain_dataproperty_default",
                    "code": "public.domain_dataproperty_default",
                    "tableType": "ENTITY",
                    "fast": true,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_IOF_TEST_1",
                    "label": "iof_test_1",
                    "code": "iof_test_1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_BZ",
                    "label": "BM_BZ",
                    "code": "BM_BZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_DBJB",
                    "label": "BM_DBJB",
                    "code": "BM_DBJB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH30_ADMIN",
                    "label": "service_publish30_admin",
                    "code": "service_publish30_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_PT_CZRK_0509",
                    "label": "PT_CZRK_0509",
                    "code": "PT_CZRK_0509",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_A12SSSZ",
                    "label": "A12SSSZ",
                    "code": "A12SSSZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR22_ADMIN",
                    "label": "weimr22_admin",
                    "code": "weimr22_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_AAZXC1AA",
                    "label": "AAZXC1AA",
                    "code": "AAZXC1AA",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR50_ADMIN",
                    "label": "weimr50_admin",
                    "code": "weimr50_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HYLB",
                    "label": "BM_HYLB",
                    "code": "BM_HYLB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_GA_LDRK_LXDH",
                    "label": "T_GA_LDRK_LXDH",
                    "code": "T_GA_LDRK_LXDH",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH18",
                    "label": "service_publish18",
                    "code": "service_publish18",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_HQZS_NBJY",
                    "label": "金融_行情走势_内部交易",
                    "code": "T_JR_HQZS_NBJY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_CZHKSZDLX",
                    "label": "常住_户口所在地类型",
                    "code": "BM_CZHKSZDLX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HX",
                    "label": "BM_HX",
                    "code": "BM_HX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR10_ADMIN",
                    "label": "weimr10_admin",
                    "code": "weimr10_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_MD_JY_REL_TEST",
                    "label": "T_MD_JY_REL_TEST",
                    "code": "T_MD_JY_REL_TEST",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_GA_CZRK_HU",
                    "label": "T_GA_CZRK_HU",
                    "code": "T_GA_CZRK_HU",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_0813",
                    "label": "LXZ_OUT_0813",
                    "code": "LXZ_OUT_0813",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_TRIP_MHDP",
                    "label": "TRIP_MHDP",
                    "code": "TRIP_MHDP",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBXXLISH25_ADMIN",
                    "label": "service_pubxxlish25_admin",
                    "code": "service_pubxxlish25_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_PT_CZRK_123_25",
                    "label": "PT_CZRK_123_25",
                    "code": "PT_CZRK_123_25",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WBSWRY",
                    "label": "wbswry",
                    "code": "wbswry",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DC_POSTGRES_PUBLIC_DOMAIN_ONTOLOGY_ASSOCIATION_CLA",
                    "label": "public.domain_ontology_association_cla",
                    "code": "public.domain_ontology_association_cla",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_CHREC_125",
                    "label": "t_chrec_125",
                    "code": "t_chrec_125",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_BEH",
                    "label": "T_BASIC_JY_BEH",
                    "code": "T_BASIC_JY_BEH",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR26_ADMIN",
                    "label": "weimr26_admin",
                    "code": "weimr26_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_IOF_PT_CZRK",
                    "label": "iof_pt_czrk",
                    "code": "iof_pt_czrk",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_XTS_BUILD_1",
                    "label": "XTS_BUILD_1",
                    "code": "XTS_BUILD_1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_PG",
                    "label": "金融_发行分配_配股",
                    "code": "T_JR_FXFP_PG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_0827",
                    "label": "LXZ_OUT_0827",
                    "code": "LXZ_OUT_0827",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_ENTITY_FUSION_MAPPING_QQY_0820",
                    "label": "entity_fusion_mapping_qqy_0820",
                    "code": "entity_fusion_mapping_qqy_0820",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_0828",
                    "label": "LXZ_OUT_0828",
                    "code": "LXZ_OUT_0828",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_APEX_030200_APEX_APPLICATION_BC_ENTRIES",
                    "label": "Identifies Breadcrumb Entries which map to a Page and identify a pages parent",
                    "code": "APEX_030200.APEX_APPLICATION_BC_ENTRIES",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT",
                    "label": "gbase_fwbp_out",
                    "code": "gbase_fwbp_out",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_APEX_030200_APEX_APPLICATION_BREADCRUMBS",
                    "label": "Identifies the definition of a collection of Breadcrumb Entries which are used t",
                    "code": "APEX_030200.APEX_APPLICATION_BREADCRUMBS",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_LOG_PKG_STD",
                    "label": "标准表刷新日志表",
                    "code": "T_LOG_PKG_STD",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_REL",
                    "label": "T_BASIC_JY_REL",
                    "code": "T_BASIC_JY_REL",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_XSJJ",
                    "label": "金融_发行分配_限售解禁",
                    "code": "T_JR_FXFP_XSJJ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_MD_RRJBXX",
                    "label": "T_MD_RRJBXX",
                    "code": "T_MD_RRJBXX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_GA_XZ_XZXT_ASJ",
                    "label": "T_GA_XZ_XZXT_ASJ",
                    "code": "T_GA_XZ_XZXT_ASJ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_IOF_PT_CZRK",
                    "label": "IOF_PT_CZRK",
                    "code": "IOF_PT_CZRK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GBGD_ZYGD",
                    "label": "金融_股本股东_主要股东",
                    "code": "T_JR_GBGD_ZYGD",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_INSTANCE_YY_DOMAIN_DONG4WU4",
                    "label": "instance_yy_domain_dong4wu4",
                    "code": "instance_yy_domain_dong4wu4",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_IOF_IRIS",
                    "label": "IOF_IRIS",
                    "code": "IOF_IRIS",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_FX",
                    "label": "BM_FX",
                    "code": "BM_FX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_1",
                    "label": "gbase_operate_log_1",
                    "code": "gbase_operate_log_1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_2",
                    "label": "gbase_operate_log_2",
                    "code": "gbase_operate_log_2",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_192_168_125_5_CREAT_TEST2",
                    "label": "creat_test2",
                    "code": "creat_test2",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_3",
                    "label": "gbase_operate_log_3",
                    "code": "gbase_operate_log_3",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_IOF_IRIS_EXT_1596077116988",
                    "label": "iof_iris_ext_1596077116988",
                    "code": "iof_iris_ext_1596077116988",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT",
                    "label": "LXZ_OUT",
                    "code": "LXZ_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_FZSUM",
                    "label": "fzsum",
                    "code": "fzsum",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_INSTANCE_YY_DOMAIN_DI4LI3WEI4ZHI4",
                    "label": "instance_yy_domain_di4li3wei4zhi4",
                    "code": "instance_yy_domain_di4li3wei4zhi4",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_ZFGP",
                    "label": "金融_发行分配_增发股票",
                    "code": "T_JR_FXFP_ZFGP",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_ZSZTB_LXZ",
                    "label": "BM_ZSZTB_LXZ",
                    "code": "BM_ZSZTB_LXZ",
                    "tableType": "ENTITY",
                    "fast": true,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_CZRK_0511_PT",
                    "label": "czrk_0511_pt",
                    "code": "czrk_0511_pt",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR31_ADMIN",
                    "label": "weimr31_admin",
                    "code": "weimr31_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_LG_DP_JIAOJ",
                    "label": "LG_DP_JIAOJ",
                    "code": "LG_DP_JIAOJ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_PT_CZRK_1000W",
                    "label": "pt_czrk_1000w",
                    "code": "pt_czrk_1000w",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_DBFX",
                    "label": "金融_财务数据_杜邦分析",
                    "code": "T_JR_CWSJ_DBFX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_YJYG",
                    "label": "金融_财务数据_业绩预告",
                    "code": "T_JR_CWSJ_YJYG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBXXLISH27_ADMIN",
                    "label": "service_pubxxlish27_admin",
                    "code": "service_pubxxlish27_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_EX",
                    "label": "BM_EX",
                    "code": "BM_EX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_ZJCX",
                    "label": "准驾车型",
                    "code": "BM_ZJCX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_TRIP_TIELU",
                    "label": "TRIP_TIELU",
                    "code": "TRIP_TIELU",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR48_ADMIN",
                    "label": "weimr48_admin",
                    "code": "weimr48_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_BF09E0DC9DE747D58201A249A9AA69E7_TAO2PAO3",
                    "label": "bf09e0dc9de747d58201a249a9aa69e7_tao2pao3",
                    "code": "bf09e0dc9de747d58201a249a9aa69e7_tao2pao3",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_BYZK",
                    "label": "BM_BYZK",
                    "code": "BM_BYZK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_A1SXZZZ",
                    "label": "A1SXZZZ",
                    "code": "A1SXZZZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_CHREC",
                    "label": "t_chrec",
                    "code": "t_chrec",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_RRRRRR",
                    "label": "rrrrrr",
                    "code": "rrrrrr",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_XUTS_BUILDTEST1",
                    "label": "xuts_buildtest1",
                    "code": "xuts_buildtest1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_XJLLB",
                    "label": "金融_财务数据_现金流量表",
                    "code": "T_JR_CWSJ_XJLLB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_NZF",
                    "label": "BM_NZF",
                    "code": "BM_NZF",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JMSFZ_SLYY",
                    "label": "BM_JMSFZ_SLYY",
                    "code": "BM_JMSFZ_SLYY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR37_ADMIN",
                    "label": "weimr37_admin",
                    "code": "weimr37_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_AB",
                    "label": "ab",
                    "code": "ab",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_REL_RYA",
                    "label": "REL_RYA",
                    "code": "REL_RYA",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_NEW_ORACLE",
                    "label": "NEW_ORACLE",
                    "code": "NEW_ORACLE",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_KY",
                    "label": "BM_KY",
                    "code": "BM_KY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_DTTZ",
                    "label": "BM_DTTZ",
                    "code": "BM_DTTZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_T_CHREC",
                    "label": "T_CHREC",
                    "code": "T_CHREC",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR44_ADMIN",
                    "label": "weimr44_admin",
                    "code": "weimr44_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_BS_RK_CZRK_CSB_P",
                    "label": "t_bs_rk_czrk_csb_p",
                    "code": "t_bs_rk_czrk_csb_p",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH32_ADMIN",
                    "label": "service_publish32_admin",
                    "code": "service_publish32_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_FZTJ_OUT",
                    "label": "LXZ_FZTJ_OUT",
                    "code": "LXZ_FZTJ_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_TEST_ZSZTB_LXZ",
                    "label": "TEST_ZSZTB_LXZ",
                    "code": "TEST_ZSZTB_LXZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR32_ADMIN",
                    "label": "weimr32_admin",
                    "code": "weimr32_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBXXLISH26_ADMIN",
                    "label": "service_pubxxlish26_admin",
                    "code": "service_pubxxlish26_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_LZ_OUT_CWSJ_CWZB",
                    "label": "lz_out_cwsj_cwzb",
                    "code": "lz_out_cwsj_cwzb",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_M_HZ_CZRKJBXX",
                    "label": "m_hz_czrkjbxx",
                    "code": "m_hz_czrkjbxx",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_0605_OUT",
                    "label": "LXZ_0605_OUT",
                    "code": "LXZ_0605_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HLX",
                    "label": "BM_HLX",
                    "code": "BM_HLX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_VW_PY_JR_FXFP_PG",
                    "label": "VW_PY_JR_FXFP_PG",
                    "code": "VW_PY_JR_FXFP_PG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_WBSWRY",
                    "label": "网吧上网人员信息",
                    "code": "WBSWRY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DC_POSTGRES_PUBLIC_DOMAIN_ONTOLOGY_ASSOCIATION",
                    "label": "public.domain_ontology_association",
                    "code": "public.domain_ontology_association",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_FWCS",
                    "label": "BM_FWCS",
                    "code": "BM_FWCS",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_OPERATE_LOG",
                    "label": "OPERATE_LOG",
                    "code": "OPERATE_LOG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_POSTGRES_GPADMIN_PT_CZRK_123_25_BAK",
                    "label": "pt_czrk_123_25_bak",
                    "code": "pt_czrk_123_25_bak",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GNBK",
                    "label": "金融_公司资料_概念板块",
                    "code": "T_JR_GSZL_GNBK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_EEEEE",
                    "label": "eeeee",
                    "code": "eeeee",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR11_ADMIN",
                    "label": "weimr11_admin",
                    "code": "weimr11_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_INSTANCE_QQY_0820_DI4LI3WEI4ZHI4",
                    "label": "instance_qqy_0820_di4li3wei4zhi4",
                    "code": "instance_qqy_0820_di4li3wei4zhi4",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_MD_JY_REL",
                    "label": "T_MD_JY_REL",
                    "code": "T_MD_JY_REL",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_XTS_BUILDNULL",
                    "label": "XTS_BUILDNULL",
                    "code": "XTS_BUILDNULL",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_ZSZTA_LXZ",
                    "label": "BM_ZSZTA_LXZ",
                    "code": "BM_ZSZTA_LXZ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_FH",
                    "label": "金融_发行分配_分红",
                    "code": "T_JR_FXFP_FH",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_BS_RK_GNLK_CSB",
                    "label": "t_bs_rk_gnlk_csb",
                    "code": "t_bs_rk_gnlk_csb",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_CSDJLB",
                    "label": "BM_CSDJLB",
                    "code": "BM_CSDJLB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_192_168_125_5_CREATE3",
                    "label": "create3",
                    "code": "create3",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JC",
                    "label": "BM_JC",
                    "code": "BM_JC",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_CZRK_HU_OUT",
                    "label": "LXZ_CZRK_HU_OUT",
                    "code": "LXZ_CZRK_HU_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_HQZS_DZJY",
                    "label": "金融_行情走势_大宗交易",
                    "code": "T_JR_HQZS_DZJY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH31_ADMIN",
                    "label": "service_publish31_admin",
                    "code": "service_publish31_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_0820",
                    "label": "LXZ_0820",
                    "code": "LXZ_0820",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_TRIP_MHLG",
                    "label": "trip_mhlg",
                    "code": "trip_mhlg",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR45_ADMIN",
                    "label": "weimr45_admin",
                    "code": "weimr45_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT1",
                    "label": "gbase_fwbp_out1",
                    "code": "gbase_fwbp_out1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_TRIP_MHLG",
                    "label": "TRIP_MHLG",
                    "code": "TRIP_MHLG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_ZHFS",
                    "label": "BM_ZHFS",
                    "code": "BM_ZHFS",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_AAAAQQZZCW",
                    "label": "aaaaqqzzcw",
                    "code": "aaaaqqzzcw",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_POSTGRES_GPADMIN_PT_CZRK_123_25",
                    "label": "pt_czrk_123_25",
                    "code": "pt_czrk_123_25",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GSJJ",
                    "label": "金融_公司资料_公司简介",
                    "code": "T_JR_GSZL_GSJJ",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH1",
                    "label": "service_publish1",
                    "code": "service_publish1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_TRIP_MHDP",
                    "label": "trip_mhdp",
                    "code": "trip_mhdp",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_CZRK_123_25",
                    "label": "CZRK_123_25",
                    "code": "CZRK_123_25",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH7",
                    "label": "service_publish7",
                    "code": "service_publish7",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_819",
                    "label": "LXZ_OUT_819",
                    "code": "LXZ_OUT_819",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH6",
                    "label": "service_publish6",
                    "code": "service_publish6",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_INSTANCE_YY_DOMAIN_DUI4XIANG4",
                    "label": "instance_yy_domain_dui4xiang4",
                    "code": "instance_yy_domain_dui4xiang4",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_HYBK",
                    "label": "金融_公司资料_行业板块",
                    "code": "T_JR_GSZL_HYBK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH33_ADMIN",
                    "label": "service_publish33_admin",
                    "code": "service_publish33_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_A111VVSX",
                    "label": "A111VVSX",
                    "code": "A111VVSX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JSZZT",
                    "label": "驾驶证状态",
                    "code": "BM_JSZZT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_PUBLISH1",
                    "label": "publish1",
                    "code": "publish1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_XZQH",
                    "label": "BM_XZQH",
                    "code": "BM_XZQH",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_TEST_JHYS_OUT",
                    "label": "TEST_JHYS_OUT",
                    "code": "TEST_JHYS_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_TEST_OUT_SPARK",
                    "label": "TEST_OUT_SPARK",
                    "code": "TEST_OUT_SPARK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_BS_RK_CZRK_CSB",
                    "label": "t_bs_rk_czrk_csb",
                    "code": "t_bs_rk_czrk_csb",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_CHREC_GX",
                    "label": "t_chrec_gx",
                    "code": "t_chrec_gx",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_JHYS_OUT",
                    "label": "JHYS_OUT",
                    "code": "JHYS_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_GNLG_OUT_0710",
                    "label": "码表-国内旅客",
                    "code": "LXZ_GNLG_OUT_0710",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_LXZ_TEST1",
                    "label": "lxz_test1",
                    "code": "lxz_test1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_LXZ_TEST2",
                    "label": "lxz_test2",
                    "code": "lxz_test2",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_SPRAK",
                    "label": "LXZ_OUT_SPRAK",
                    "code": "LXZ_OUT_SPRAK",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JIBINGFENLEI",
                    "label": "BM_JIBINGFENLEI",
                    "code": "BM_JIBINGFENLEI",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WJTGBASE2",
                    "label": "wjtgbase2",
                    "code": "wjtgbase2",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_192_168_125_5_XUTS_BUILD1112",
                    "label": "xuts_build1112",
                    "code": "xuts_build1112",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_A_DC_0611_1592185330600",
                    "label": "a_dc_0611_1592185330600",
                    "code": "a_dc_0611_1592185330600",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_CWZB",
                    "label": "金融_财务数据_财务指标",
                    "code": "T_JR_CWSJ_CWZB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_TRIP_TIELU",
                    "label": "trip_tielu",
                    "code": "trip_tielu",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR40_ADMIN",
                    "label": "weimr40_admin",
                    "code": "weimr40_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG",
                    "label": "gbase_operate_log",
                    "code": "gbase_operate_log",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_XXX",
                    "label": "xxx",
                    "code": "xxx",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_XTS_O_G",
                    "label": "xts_o_g",
                    "code": "xts_o_g",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_GA_LDRK_RENYUAN",
                    "label": "T_GA_LDRK_RENYUAN",
                    "code": "T_GA_LDRK_RENYUAN",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_CWZY",
                    "label": "金融_财务数据_财务摘要",
                    "code": "T_JR_CWSJ_CWZY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_2",
                    "label": "gbase_fwbp_out_2",
                    "code": "gbase_fwbp_out_2",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_1",
                    "label": "gbase_fwbp_out_1",
                    "code": "gbase_fwbp_out_1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_3",
                    "label": "gbase_fwbp_out_3",
                    "code": "gbase_fwbp_out_3",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_CREAT_TEST",
                    "label": "CREAT_TEST",
                    "code": "CREAT_TEST",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_GPTEST1",
                    "label": "gptest1",
                    "code": "gptest1",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_192_168_125_5_ZZM0806",
                    "label": "zzm0806",
                    "code": "zzm0806",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_MIST",
                    "label": "LXZ_OUT_MIST",
                    "code": "LXZ_OUT_MIST",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JSRLY",
                    "label": "驾驶人来源",
                    "code": "BM_JSRLY",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_T_CHREC_GPLOAD",
                    "label": "t_chrec_gpload",
                    "code": "t_chrec_gpload",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH28_ADMIN",
                    "label": "service_publish28_admin",
                    "code": "service_publish28_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_LRB",
                    "label": "金融_财务数据_利润表",
                    "code": "T_JR_CWSJ_LRB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_XTS_BUILD11W",
                    "label": "XTS_BUILD11W",
                    "code": "XTS_BUILD11W",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_GNLG",
                    "label": "码表-国内旅客",
                    "code": "BM_GNLG",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_ZYLJ_OUT",
                    "label": "ZYLJ_OUT",
                    "code": "ZYLJ_OUT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_AAAB",
                    "label": "AAAB",
                    "code": "AAAB",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_IOF_PT_CZRK_5000W",
                    "label": "IOF_PT_CZRK_5000W",
                    "code": "IOF_PT_CZRK_5000W",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_WWWJT",
                    "label": "WWWJT",
                    "code": "WWWJT",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_AAAA",
                    "label": "AAAA",
                    "code": "AAAA",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WEIMR42_ADMIN",
                    "label": "weimr42_admin",
                    "code": "weimr42_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_LZ_IRIS",
                    "label": "LZ_IRIS",
                    "code": "LZ_IRIS",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_XXX",
                    "label": "XXX",
                    "code": "XXX",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_SERVICE_PUBLISH29_ADMIN",
                    "label": "service_publish29_admin",
                    "code": "service_publish29_admin",
                    "tableType": "ENTITY",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ADMIN_20200827_028DAD",
                    "label": "公安戒毒所人员数据",
                    "code": "admin_20200827_028dad",
                    "tableType": "FILE",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ADMIN_20200827_D924A7",
                    "label": "各种匹配数值类型的格式",
                    "code": "admin_20200827_d924a7",
                    "tableType": "FILE",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ADMIN_20200827_176571",
                    "label": "常规数据",
                    "code": "admin_20200827_176571",
                    "tableType": "FILE",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_LZ_TEST_0713_LOGIC",
                    "label": "lz_test_0713",
                    "code": "lz_test_0713",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_WBSWRY_LOGIC",
                    "label": "wbswry",
                    "code": "wbswry",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_BS_RK_CZRK_CSB_P_LOGIC",
                    "label": "t_bs_rk_czrk_csb_p",
                    "code": "t_bs_rk_czrk_csb_p",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_CWZB_LOGIC_1598495934234",
                    "label": "金融_财务数据_财务指标",
                    "code": "T_JR_CWSJ_CWZB",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_IOF_IRIS_LOGIC_1598335295505",
                    "label": "IOF_IRIS",
                    "code": "IOF_IRIS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_MD_RRJBXX_LOGIC_1598515016642",
                    "label": "T_MD_RRJBXX",
                    "code": "T_MD_RRJBXX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_BEH_LOGIC_1598578425685",
                    "label": "T_BASIC_JY_BEH",
                    "code": "T_BASIC_JY_BEH",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_GNLG_LOGIC_1598248570663",
                    "label": "码表-国内旅客",
                    "code": "BM_GNLG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_3_LOGIC_1598515035086",
                    "label": "gbase_operate_log_3",
                    "code": "gbase_operate_log_3",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_DTTZ_LOGIC",
                    "label": "BM_DTTZ",
                    "code": "BM_DTTZ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GSGG_LOGIC_1598234170858",
                    "label": "金融_公司资料_公司高管",
                    "code": "T_JR_GSZL_GSGG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_1_LOGIC_1598515034944",
                    "label": "gbase_operate_log_1",
                    "code": "gbase_operate_log_1",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_PT_CZRK_1000W_LOGIC_1598320373506",
                    "label": "pt_czrk_1000w",
                    "code": "pt_czrk_1000w",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_LZ_OUT_CWSJ_CWZB_LOGIC",
                    "label": "lz_out_cwsj_cwzb",
                    "code": "lz_out_cwsj_cwzb",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_MIST_LOGIC_logic",
                    "label": "LXZ_OUT_MIST",
                    "code": "LXZ_OUT_MIST",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_CSDJLB_LOGIC_1598248570655",
                    "label": "BM_CSDJLB",
                    "code": "BM_CSDJLB",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_EX_LOGIC_1598317948245",
                    "label": "BM_EX",
                    "code": "BM_EX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_TRIP_MHLG_LOGIC",
                    "label": "trip_mhlg",
                    "code": "trip_mhlg",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_EX_LOGIC_1598259230354",
                    "label": "BM_EX",
                    "code": "BM_EX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_HQZS_DZJY_LOGIC_1598234170766",
                    "label": "金融_行情走势_大宗交易",
                    "code": "T_JR_HQZS_DZJY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_OPERATE_LOG_LOGIC_1598578425602",
                    "label": "OPERATE_LOG",
                    "code": "OPERATE_LOG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_PT_CZRK_123_25_LOGIC_1598578425670",
                    "label": "PT_CZRK_123_25",
                    "code": "PT_CZRK_123_25",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GSJJ_LOGIC_1598234170866",
                    "label": "金融_公司资料_公司简介",
                    "code": "T_JR_GSZL_GSJJ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_FH_LOGIC_1598234170799",
                    "label": "金融_发行分配_分红",
                    "code": "T_JR_FXFP_FH",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_PG_LOGIC_1598234170808",
                    "label": "金融_发行分配_配股",
                    "code": "T_JR_FXFP_PG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_LZ_IRIS_LOGIC_1598337952324",
                    "label": "LZ_IRIS",
                    "code": "LZ_IRIS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_TRIP_TIELU_LOGIC",
                    "label": "TRIP_TIELU",
                    "code": "TRIP_TIELU",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_SPRAK_LOGIC_logic",
                    "label": "LXZ_OUT_SPRAK",
                    "code": "LXZ_OUT_SPRAK",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HLX_LOGIC",
                    "label": "BM_HLX",
                    "code": "BM_HLX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_FX_LOGIC",
                    "label": "BM_FX",
                    "code": "BM_FX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_LOGIC_1598515034931",
                    "label": "gbase_operate_log",
                    "code": "gbase_operate_log",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_T_CHREC_GPLOAD_LOGIC_1598333211916",
                    "label": "t_chrec_gpload",
                    "code": "t_chrec_gpload",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_XGFX_LOGIC_1598234170816",
                    "label": "金融_发行分配_新股发行",
                    "code": "T_JR_FXFP_XGFX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_IOF_IRIS_LOGIC_1598335277975",
                    "label": "IOF_IRIS_1",
                    "code": "IOF_IRIS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_IOF_PT_CZRK_LOGIC_1598855631897",
                    "label": "iof_pt_czrk",
                    "code": "iof_pt_czrk",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GNBK_LOGIC_1598234170849",
                    "label": "金融_公司资料_概念板块",
                    "code": "T_JR_GSZL_GNBK",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_LOGIC_1598578425643",
                    "label": "警员信息表",
                    "code": "T_BASIC_JY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_INSTANCE_YY_DOMAIN_DONG4WU4_LOGIC_1598598165066",
                    "label": "instance_yy_domain_dong4wu4",
                    "code": "instance_yy_domain_dong4wu4",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_JSZZT_LOGIC_1598426796947",
                    "label": "驾驶证状态",
                    "code": "BM_JSZZT",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_TRIP_TIELU_LOGIC",
                    "label": "trip_tielu",
                    "code": "trip_tielu",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_CZHKSZDLX_LOGIC",
                    "label": "常住_户口所在地类型",
                    "code": "BM_CZHKSZDLX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_POSTGRES_GPADMIN_PT_CZRK_123_25_LOGIC_1598509002266",
                    "label": "pt_czrk_123_25",
                    "code": "pt_czrk_123_25",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_T_CHREC_LOGIC",
                    "label": "T_CHREC",
                    "code": "T_CHREC",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_PT_CZRK_1000W_LOGIC",
                    "label": "pt_czrk_1000w",
                    "code": "pt_czrk_1000w",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_XZQH_LOGIC_1598248570624",
                    "label": "BM_XZQH",
                    "code": "BM_XZQH",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_XSJJ_LOGIC_1598248232944",
                    "label": "金融_发行分配_限售解禁",
                    "code": "T_JR_FXFP_XSJJ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GBGD_ZYGD_LOGIC_1598248232983",
                    "label": "金融_股本股东_主要股东",
                    "code": "T_JR_GBGD_ZYGD",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_LXZ_OUT_0828_LOGIC_1598606538256",
                    "label": "LXZ_OUT_0828",
                    "code": "LXZ_OUT_0828",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_LXZ_TEST1_LOGIC_1598855363024",
                    "label": "lxz_test1",
                    "code": "lxz_test1",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_IOF_IRIS_LOGIC_1598247203366",
                    "label": "IOF_IRIS",
                    "code": "IOF_IRIS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GSJJ_LOGIC_1598248232991",
                    "label": "金融_公司资料_公司简介",
                    "code": "T_JR_GSZL_GSJJ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_TRIP_MHLG_LOGIC",
                    "label": "TRIP_MHLG",
                    "code": "TRIP_MHLG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_YJYG_LOGIC_1598234170791",
                    "label": "金融_财务数据_业绩预告",
                    "code": "T_JR_CWSJ_YJYG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_HYBK_LOGIC_1598238093807",
                    "label": "金融_公司资料_行业板块",
                    "code": "T_JR_GSZL_HYBK",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_XJLLB_LOGIC_1598234170722",
                    "label": "金融_财务数据_现金流量表",
                    "code": "T_JR_CWSJ_XJLLB",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_TEST08244_LOGIC_1598333125372",
                    "label": "test08244",
                    "code": "test08244",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_PT_CZRK_1000W_LOGIC_1598855631965",
                    "label": "pt_czrk_1000w",
                    "code": "pt_czrk_1000w",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_CWZB_LOGIC_1598234170774",
                    "label": "金融_财务数据_财务指标",
                    "code": "T_JR_CWSJ_CWZB",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_1_LOGIC_1598515034865",
                    "label": "gbase_fwbp_out_1",
                    "code": "gbase_fwbp_out_1",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_WBSWRY_LOGIC_1598577279476",
                    "label": "网吧上网人员信息_1",
                    "code": "WBSWRY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GBGD_ZYGD_LOGIC_1598238093717",
                    "label": "金融_股本股东_主要股东",
                    "code": "T_JR_GBGD_ZYGD",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_ZFGP_LOGIC_1598234170833",
                    "label": "金融_发行分配_增发股票",
                    "code": "T_JR_FXFP_ZFGP",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_WBSWRY_LOGIC_logic_logic",
                    "label": "网吧上网人员信息",
                    "code": "WBSWRY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_CZHKSZDLX_LOGIC_1598426796931",
                    "label": "常住_户口所在地类型",
                    "code": "BM_CZHKSZDLX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_GNLG_LOGIC",
                    "label": "码表-国内旅客",
                    "code": "BM_GNLG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_10_LOGIC_1598515034990",
                    "label": "gbase_operate_log_10",
                    "code": "gbase_operate_log_10",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_CZRK_0511_PT_LOGIC_1598341034532",
                    "label": "czrk_0511_pt",
                    "code": "czrk_0511_pt",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_IOF_TEST_1_LOGIC_1598341034604",
                    "label": "iof_test_1",
                    "code": "iof_test_1",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_LXZ_TEST1_LOGIC_1598855393875",
                    "label": "lxz_test1_1",
                    "code": "lxz_test1",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_BS_RK_CZRK_CSB_LOGIC",
                    "label": "t_bs_rk_czrk_csb",
                    "code": "t_bs_rk_czrk_csb",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_HYBK_LOGIC_1598234170874",
                    "label": "金融_公司资料_行业板块",
                    "code": "T_JR_GSZL_HYBK",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_MD_JY_REL_TEST_LOGIC_1598336061407",
                    "label": "T_MD_JY_REL_TEST",
                    "code": "T_MD_JY_REL_TEST",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_DBJB_LOGIC",
                    "label": "BM_DBJB",
                    "code": "BM_DBJB",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_DTTZ_LOGIC_logic",
                    "label": "BM_DTTZ",
                    "code": "BM_DTTZ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_TRIP_MHDP_LOGIC",
                    "label": "trip_mhdp",
                    "code": "trip_mhdp",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_BS_RK_GNLK_CSB_LOGIC",
                    "label": "t_bs_rk_gnlk_csb",
                    "code": "t_bs_rk_gnlk_csb",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_EX_LOGIC_1598493827145",
                    "label": "BM_EX",
                    "code": "BM_EX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_FWCS_LOGIC",
                    "label": "BM_FWCS",
                    "code": "BM_FWCS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_FXFP_XSJJ_LOGIC_1598234170824",
                    "label": "金融_发行分配_限售解禁",
                    "code": "T_JR_FXFP_XSJJ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_AAAAQQZZCW_LOGIC_1598515034836",
                    "label": "aaaaqqzzcw",
                    "code": "aaaaqqzzcw",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HX_LOGIC",
                    "label": "BM_HX",
                    "code": "BM_HX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_PT_CZRK_123_25_LOGIC",
                    "label": "PT_CZRK_123_25",
                    "code": "PT_CZRK_123_25",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_WBSWRY_LOGIC",
                    "label": "网吧上网人员信息",
                    "code": "WBSWRY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_WBSWRY_LOGIC_logic",
                    "label": "网吧上网人员信息",
                    "code": "WBSWRY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ELASTICSEARCH_IOF_TEST_ENTITY_FUSION_MAPPING_QQY_0820_LOGIC_1598606279084",
                    "label": "entity_fusion_mapping_qqy_0820",
                    "code": "entity_fusion_mapping_qqy_0820",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_T_CHREC_LOGIC",
                    "label": "t_chrec",
                    "code": "t_chrec",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_FX_LOGIC_1598238678908",
                    "label": "BM_FX",
                    "code": "BM_FX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_LOGIC",
                    "label": "警员信息表",
                    "code": "T_BASIC_JY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_T_CHREC_GPLOAD_LOGIC_1598335251249",
                    "label": "t_chrec_gpload",
                    "code": "t_chrec_gpload",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_TRIP_MHDP_LOGIC",
                    "label": "TRIP_MHDP",
                    "code": "TRIP_MHDP",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_LOGIC_1598515034848",
                    "label": "gbase_fwbp_out",
                    "code": "gbase_fwbp_out",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_2_LOGIC_1598515034894",
                    "label": "gbase_fwbp_out_2",
                    "code": "gbase_fwbp_out_2",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_ZSZTC_LXZ_LOGIC_1598259230462",
                    "label": "BM_ZSZTC_LXZ",
                    "code": "BM_ZSZTC_LXZ",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_REL_LOGIC_1598578425661",
                    "label": "T_BASIC_JY_REL",
                    "code": "T_BASIC_JY_REL",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_T_BASIC_JY_ONE_LOGIC_1598578425653",
                    "label": "T_BASIC_JY_ONE",
                    "code": "T_BASIC_JY_ONE",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HX_LOGIC_logic",
                    "label": "BM_HX",
                    "code": "BM_HX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_APEX_030200_APEX_APPLICATION_BREADCRUMBS_LOGIC",
                    "label": "Identifies the definition of a collection of Breadcrumb Entries which are used t",
                    "code": "APEX_030200.APEX_APPLICATION_BREADCRUMBS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_AB_LOGIC_1598515034795",
                    "label": "ab",
                    "code": "ab",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT_3_LOGIC_1598515034906",
                    "label": "gbase_fwbp_out_3",
                    "code": "gbase_fwbp_out_3",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_TEST_OUT_SPARK_LOGIC_1598506314588",
                    "label": "TEST_OUT_SPARK",
                    "code": "TEST_OUT_SPARK",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_LRB_LOGIC_1598234170783",
                    "label": "金融_财务数据_利润表",
                    "code": "T_JR_CWSJ_LRB",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_APEX_030200_APEX_APPLICATION_BC_ENTRIES_LOGIC",
                    "label": "Identifies Breadcrumb Entries which map to a Page and identify a pages parent",
                    "code": "APEX_030200.APEX_APPLICATION_BC_ENTRIES",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_FX_LOGIC_logic",
                    "label": "BM_FX",
                    "code": "BM_FX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_DMC_ROOT_M_HZ_CZRKJBXX_LOGIC",
                    "label": "m_hz_czrkjbxx",
                    "code": "m_hz_czrkjbxx",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_T_CHREC_GPLOAD_LOGIC_1598335225665",
                    "label": "t_chrec_gpload",
                    "code": "t_chrec_gpload",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GBGD_ZYGD_LOGIC_1598234170841",
                    "label": "金融_股本股东_主要股东",
                    "code": "T_JR_GBGD_ZYGD",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HX_LOGIC_1598238678949",
                    "label": "BM_HX",
                    "code": "BM_HX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_IOF_PT_CZRK_LOGIC",
                    "label": "iof_pt_czrk",
                    "code": "iof_pt_czrk",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_CWSJ_CWZY_LOGIC_1598234170676",
                    "label": "金融_财务数据_财务摘要",
                    "code": "T_JR_CWSJ_CWZY",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_IOF_IOF_IRIS_LOGIC_1598335264696",
                    "label": "IOF_IRIS",
                    "code": "IOF_IRIS",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_HLX_LOGIC_1598248570671",
                    "label": "BM_HLX",
                    "code": "BM_HLX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_BM_EX_LOGIC_1598851887477",
                    "label": "BM_EX_1",
                    "code": "BM_EX",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_FINANCE_T_JR_GSZL_GSGG_LOGIC_1598238093740",
                    "label": "金融_公司资料_公司高管",
                    "code": "T_JR_GSZL_GSGG",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_OPERATE_LOG_2_LOGIC_1598515035015",
                    "label": "gbase_operate_log_2",
                    "code": "gbase_operate_log_2",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_ORCL_CICADA_ZYLJ_OUT_LOGIC_1598506314613",
                    "label": "ZYLJ_OUT",
                    "code": "ZYLJ_OUT",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_GPADMIN_GPADMIN_PT_CZRK_1000W_LOGIC_1598333212022",
                    "label": "pt_czrk_1000w_1",
                    "code": "pt_czrk_1000w",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_QQY_ROOT_GBASE_FWBP_OUT1_LOGIC_1598515034856",
                    "label": "gbase_fwbp_out1",
                    "code": "gbase_fwbp_out1",
                    "tableType": "LOGIC",
                    "fast": false,
                    "success": true
                }, {
                    "globalCode": "GB_HAPPY_20200821_BE5CAD",
                    "label": "记事本_一列有数据无首行字段",
                    "code": "Happy_20200821_be5cad",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_7153E0",
                    "label": "某列只有字段英文名",
                    "code": "Happy_20200821_7153e0",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_45E46C",
                    "label": "Excel_常规_文本数值数据",
                    "code": "Happy_20200821_45e46c",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_DD81ED",
                    "label": "2010_常规文本数值文件",
                    "code": "Happy_20200821_dd81ed",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_1A0891",
                    "label": "xls文件大小_3M",
                    "code": "Happy_20200821_1a0891",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_815D86",
                    "label": "公安戒毒所人员数据",
                    "code": "Happy_20200821_815d86",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_1A7ED6",
                    "label": "2010_常规文件_1M",
                    "code": "Happy_20200821_1a7ed6",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_474A43",
                    "label": "2003_常规文本数据",
                    "code": "Happy_20200821_474a43",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_2AEF1F",
                    "label": "excel_常规文本",
                    "code": "Happy_20200821_2aef1f",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }, {
                    "globalCode": "GB_HAPPY_20200821_C1B6C9",
                    "label": "常规数据_xls_更新",
                    "code": "Happy_20200821_c1b6c9",
                    "tableType": "FILE",
                    "fast": false,
                    "success": false
                }]
            }
        }

    },
    sqlSave: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "Table or view not found: user; line 1 pos 12",
            "detailErrorMsg": "com.code.common.spark.exception.SparkSqlAnalysisException: Table or view not found: user; line 1 pos 12",
            "data": null
        }

    },
    sqlQuery: {},
    getFunction: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": [{
                "name": "加法",
                "use": "+",
                "example": "1+1=2"
            }, {
                "name": "减法",
                "use": "-",
                "example": "2-1=1"
            }, {
                "name": "乘法",
                "use": "*",
                "example": "2*2=4"
            }, {
                "name": "除法",
                "use": "/",
                "example": "4/2=2"
            }, {
                "name": "绝对值",
                "use": "ABS(X)",
                "example": "ABS(2.1)=2"
            }, {
                "name": "立方根",
                "use": "CBRT(DP)",
                "example": "CBRT(27.0)=3"
            }, {
                "name": "IF",
                "use": "IF(field > 1,1,0)",
                "example": "IF(age>20,'青年','少年')"
            }]
        }

    },
    getLogicColumn: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": [{
                "name": "3年以内_应收帐款_元",
                "code": "3NYN_YSZK_YUAN",
                "id": "4dcd75a8554d48268f97e2b74bf63167",
                "indexType": "MEASURE",
                "memo": "3年以内_应收帐款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "成本费用利润率",
                "code": "CBFYLRL",
                "id": "992ecce12d514f00a8d8f17954b6cf4f",
                "indexType": "MEASURE",
                "memo": "成本费用利润率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "存货周转率_次",
                "code": "CHZZL_CI",
                "id": "0c5ad5bfae86465cb226ca61107b0adb",
                "indexType": "MEASURE",
                "memo": "存货周转率_次",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "存货周转天数",
                "code": "CHZZTS",
                "id": "5f66870ab43a485baf4ebc8cba26ff86",
                "indexType": "MEASURE",
                "memo": "存货周转天数",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "产权比率",
                "code": "CQBL",
                "id": "9709a480a0674e8e82801ff4ea5e66b4",
                "indexType": "MEASURE",
                "memo": "产权比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期负债比率",
                "code": "CQFZBL",
                "id": "cf09d771d81b4820ad6d80dae2c0d154",
                "indexType": "MEASURE",
                "memo": "长期负债比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期负债股东权益比率",
                "code": "CQFZGDQYBL",
                "id": "9548fbc171d24aadac6f12c0ef7b7d04",
                "indexType": "MEASURE",
                "memo": "长期负债股东权益比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期债务与营运资金比率",
                "code": "CQFZYYYZJBL",
                "id": "bc4d7818df8b45f3bcc8f3fa37ff828f",
                "indexType": "MEASURE",
                "memo": "长期债务与营运资金比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期股票投资_元",
                "code": "CQGPTZ_YUAN",
                "id": "448361f30b40417b91b0c27bdcd3f757",
                "indexType": "MEASURE",
                "memo": "长期股票投资_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期其它经营性投资_元",
                "code": "CQJTJYXTZ_YUAN",
                "id": "c66342fb44be420b98419a32448aecee",
                "indexType": "MEASURE",
                "memo": "长期其它经营性投资_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期资产与长期资金比率",
                "code": "CQZCYCQZJBL",
                "id": "49ec1f1fead94d93a2bc6dca34f6c201",
                "indexType": "MEASURE",
                "memo": "长期资产与长期资金比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "长期债券投资_元",
                "code": "CQZQTZ_YUAN",
                "id": "b4bdd3b0bb9c4f86ba4b773d64eb3643",
                "indexType": "MEASURE",
                "memo": "长期债券投资_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "短期股票投资_元",
                "code": "DQGPTZ_YUAN",
                "id": "53b5831b4f0f4871b7e70c9f8e25cafe",
                "indexType": "MEASURE",
                "memo": "短期股票投资_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "短期其它经营性投资_元",
                "code": "DQQTJYXTZ_YUAN",
                "id": "0df1c6167e4c4aec9f98010154b9ef64",
                "indexType": "MEASURE",
                "memo": "短期其它经营性投资_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "短期债券投资_元",
                "code": "DQZQTZ_YUAN",
                "id": "78dd7a750fc24b73ac35dfef2b49927c",
                "indexType": "MEASURE",
                "memo": "短期债券投资_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "非主营比重",
                "code": "FZYBZ",
                "id": "8cbeb068c6984555ad83ebcc492c9e63",
                "indexType": "MEASURE",
                "memo": "非主营比重",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "负债与净资产比率",
                "code": "FZYJZCBL",
                "id": "516d1716e5b34f65b597c78c89edae13",
                "indexType": "MEASURE",
                "memo": "负债与净资产比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "股本报酬率",
                "code": "GBBCL",
                "id": "27b9627c206346659099159ef6a74d87",
                "indexType": "MEASURE",
                "memo": "股本报酬率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "公司名称",
                "code": "GSMC",
                "id": "0bfea6db15124e08bcc7dd583eb9242e",
                "indexType": "DIMENSION",
                "memo": "公司名称",
                "dataTypeId": "4028f9fc59a545110159a545736a009c",
                "format": "TEXT",
                "numberFormat": null
            }, {
                "name": "1到2年以内_其它应收款_元",
                "code": "1D2NN_QTYSK_YUAN",
                "id": "8b8941b5922a4c12bd01ff5d561fb944",
                "indexType": "MEASURE",
                "memo": "1到2年以内_其它应收款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "1到2年以内_预付货款_元",
                "code": "1D2NN_YFHK_YUAN",
                "id": "7264d079aaf34344b47b98f173b0d85d",
                "indexType": "MEASURE",
                "memo": "1到2年以内_预付货款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "1到2年以内_应收帐款_元",
                "code": "1D2NN_YSZK_YUAN",
                "id": "ad23bbe013914adb846246f0939214b0",
                "indexType": "MEASURE",
                "memo": "1到2年以内_应收帐款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "1年内_其它应收款_元",
                "code": "1NN_QTYSK_YUAN",
                "id": "cc026cb4eece4a67b9213c75f394b456",
                "indexType": "MEASURE",
                "memo": "1年内_其它应收款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "1年内_预付货款_元",
                "code": "1NN_YFHK_YUAN",
                "id": "6d9b66daa00c4c2e806692130acc812f",
                "indexType": "MEASURE",
                "memo": "1年内_预付货款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "1年内_应收帐款_元",
                "code": "1NN_YSZK_YUAN",
                "id": "c601cf015fa444d5a6502914da95ed86",
                "indexType": "MEASURE",
                "memo": "1年内_应收帐款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "2到3年以内_其它应收款_元",
                "code": "2D3NYN_QTYSK_YUAN",
                "id": "ca8b1f6ddd4549b19d27eccfc59f0ac6",
                "indexType": "MEASURE",
                "memo": "2到3年以内_其它应收款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "2到3年以内_预付货款_元",
                "code": "2D3NYN_YFHK_YUAN",
                "id": "a0c9c0f17e874945ac20d031d74a39c2",
                "indexType": "MEASURE",
                "memo": "2到3年以内_预付货款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "2到3年以内_应收帐款_元",
                "code": "2D3NYN_YSZK_YUAN",
                "id": "acc533a80cc14a268091279f3a7563d3",
                "indexType": "MEASURE",
                "memo": "2到3年以内_应收帐款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "3年以内_其它应收款_元",
                "code": "3NYN_QTYSK_YUAN",
                "id": "efbee1b6bec7419a8ecc177eaba6b061",
                "indexType": "MEASURE",
                "memo": "3年以内_其它应收款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "3年以内_预付货款_元",
                "code": "3NYN_YFHK_YUAN",
                "id": "0fa9b25fb288412eab5b39fb4c133dcd",
                "indexType": "MEASURE",
                "memo": "3年以内_预付货款_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "固定资产比重",
                "code": "GUDZCBZ",
                "id": "a7830f323e244c06a5069dd546178065",
                "indexType": "MEASURE",
                "memo": "固定资产比重",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "固定资产净值率",
                "code": "GUDZCJZL",
                "id": "1e663830040042b9a1414e4448d38f08",
                "indexType": "MEASURE",
                "memo": "固定资产净值率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "固定资产周转率_次",
                "code": "GUDZCZZL_CI",
                "id": "678d78f5a98242138d3a953761f0f443",
                "indexType": "MEASURE",
                "memo": "固定资产周转率_次",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "股息发放率",
                "code": "GXFFL",
                "id": "ff7d824b92f148c9a18fcdf2fa020055",
                "indexType": "MEASURE",
                "memo": "股息发放率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "巨龙_分区日期",
                "code": "JL_FQRQ",
                "id": "6571d5aa00aa404c8444ee73395e4b16",
                "indexType": "DIMENSION",
                "memo": "巨龙_分区日期",
                "dataTypeId": "4028f9fc59a545110159a545736b00a5",
                "format": "",
                "numberFormat": null
            }, {
                "name": "巨龙_入库时间",
                "code": "JL_RKSJ",
                "id": "071da09324ad44e2b75afdea52f34dbc",
                "indexType": "DIMENSION",
                "memo": "巨龙_入库时间",
                "dataTypeId": "4028f9fc59a545110159a545736b00a5",
                "format": "",
                "numberFormat": null
            }, {
                "name": "净利润增长率",
                "code": "JLRZZL",
                "id": "0ed396ade5154ac4b2632616f6a65397",
                "indexType": "MEASURE",
                "memo": "净利润增长率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "加权净资产收益率",
                "code": "JQJZCSYL",
                "id": "be94a0d3918f456c9d9ff43f7d061433",
                "indexType": "MEASURE",
                "memo": "加权净资产收益率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "加权每股收益_元",
                "code": "JQMGSY_YUAN",
                "id": "2ef2223495764d18a26bb3c67b3293fb",
                "indexType": "MEASURE",
                "memo": "加权每股收益_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "经营现金净流量对负债比率",
                "code": "JYXJJLLDFZBL",
                "id": "94fdb743c5c44c5a8b9707c1213559fc",
                "indexType": "MEASURE",
                "memo": "经营现金净流量对负债比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "经营现金净流量对销售收入比率",
                "code": "JYXJJLLDXSSRBL",
                "id": "cc8ce1ace77448ca87f6b647607f826f",
                "indexType": "MEASURE",
                "memo": "经营现金净流量对销售收入比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "经营现金净流量与净利润比率",
                "code": "JYXJJLLYJLRBL",
                "id": "5ed12088c92f4cf8ad673efebaece16d",
                "indexType": "MEASURE",
                "memo": "经营现金净流量与净利润比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "净资产报酬率",
                "code": "JZCBCL",
                "id": "10350473a74349cc941b73644c864ea4",
                "indexType": "MEASURE",
                "memo": "净资产报酬率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "净资产比率",
                "code": "JZCBL",
                "id": "597d7bc2c77e461a8290f3f3452d53d7",
                "indexType": "MEASURE",
                "memo": "净资产比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "净资产收益率",
                "code": "JZCSYL",
                "id": "04ab0c642ab14786b66baa233eed94bb",
                "indexType": "MEASURE",
                "memo": "净资产收益率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "净资产与固定资产比率",
                "code": "JZCYGDZCBL",
                "id": "2f5d64cc962d446387a28777ae09399e",
                "indexType": "MEASURE",
                "memo": "净资产与固定资产比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "净资产周转率_次",
                "code": "JZCZHZL_CI",
                "id": "01d4f584961446efa1b6864c6587e054",
                "indexType": "MEASURE",
                "memo": "净资产周转率_次",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "净资产增长率",
                "code": "JZCZZL",
                "id": "1493b269a7804e619ebc51d4e57db920",
                "indexType": "MEASURE",
                "memo": "净资产增长率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "扣除非经常性损益_净利润_元",
                "code": "KCFJCXSY_JLR_YUAN",
                "id": "f7cacc25ae2b45b18fd925ecb4ae210b",
                "indexType": "MEASURE",
                "memo": "扣除非经常性损益_净利润_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "扣除非经常性损益_每股收益_元",
                "code": "KCFJCXSY_MGSY_YUAN",
                "id": "c14c54556153407fb152cdba93f7c52e",
                "indexType": "MEASURE",
                "memo": "扣除非经常性损益_每股收益_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "流动比率",
                "code": "LDBL",
                "id": "53b5aa4e7acd455fb47e0d8e4ae93def",
                "indexType": "MEASURE",
                "memo": "流动比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "流动资产周转率_次",
                "code": "LDZCZZL_CI",
                "id": "36743cf423454c1d8ca4a1bb428add02",
                "indexType": "MEASURE",
                "memo": "流动资产周转率_次",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "流动资产周转天数",
                "code": "LDZCZZTS",
                "id": "0aad98776a9649e789151efac44a4d88",
                "indexType": "MEASURE",
                "memo": "流动资产周转天数",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "利息支付倍数",
                "code": "LXZFBS",
                "id": "195e0b6ab1d14270aad68af371fe1421",
                "indexType": "MEASURE",
                "memo": "利息支付倍数",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "每股经营性现金流_元",
                "code": "MGJYXXJL_YUAN",
                "id": "d6f429599d6044ef98965cd263391574",
                "indexType": "MEASURE",
                "memo": "每股经营性现金流_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "每股未分配利润_元",
                "code": "MGWFPLR_YUAN",
                "id": "a5434fd8c63b4672906046f8184ccea1",
                "indexType": "MEASURE",
                "memo": "每股未分配利润_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "每股资本公积金_元",
                "code": "MGZBGJJ_YUAN",
                "id": "d7632bb2c67043b28af8abbf1a83f86d",
                "indexType": "MEASURE",
                "memo": "每股资本公积金_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "清算价值比率",
                "code": "QSJZBL",
                "id": "33c3cbacee72478cacb9d070bcdc705d",
                "indexType": "MEASURE",
                "memo": "清算价值比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "速动比率",
                "code": "SDBL",
                "id": "fcf083cab5a746fe9ccc5ef773a0e155",
                "indexType": "MEASURE",
                "memo": "速动比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "数据来源",
                "code": "SJLY",
                "id": "7caa2efe4b9749a6a8388ec73f12cbdb",
                "indexType": "DIMENSION",
                "memo": "数据来源",
                "dataTypeId": "4028f9fc59a545110159a545736a009c",
                "format": "TEXT",
                "numberFormat": null
            }, {
                "name": "三项费用比重",
                "code": "SXFYBZ",
                "id": "bf0b05ac1c714daaabca87281e9228a3",
                "indexType": "MEASURE",
                "memo": "三项费用比重",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "摊薄每股收益_元",
                "code": "TBMGSY_YUAN",
                "id": "f4d830271b9545ccb2e07a3fc56a4420",
                "indexType": "MEASURE",
                "memo": "摊薄每股收益_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "填表日期",
                "code": "TBRQ",
                "id": "a33e8235aba34198867d100fbe79e8aa",
                "indexType": "DIMENSION",
                "memo": "填表日期",
                "dataTypeId": "4028f9fc59a545110159a545736b00a5",
                "format": "",
                "numberFormat": null
            }, {
                "name": "调整后_每股净资产_元",
                "code": "TZH_MGJZC_YUAN",
                "id": "765767bcea254c08bfbba40909fad377",
                "indexType": "MEASURE",
                "memo": "调整后_每股净资产_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "调整后_每股收益_元",
                "code": "TZH_MGSY_YUAN",
                "id": "46e418fdee974c67b9296f42add427e1",
                "indexType": "MEASURE",
                "memo": "调整后_每股收益_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "调整前_每股净资产_元",
                "code": "TZQ_MGJZC_YUAN",
                "id": "100132fa448f4834b3491ee785b09909",
                "indexType": "MEASURE",
                "memo": "调整前_每股净资产_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "投资收益率",
                "code": "TZSYL",
                "id": "99544899ca724a92b7ba986c868bb286",
                "indexType": "MEASURE",
                "memo": "投资收益率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "现金比率",
                "code": "XJBL",
                "id": "2e6b9bccde554724b79efbda6b3023d6",
                "indexType": "MEASURE",
                "memo": "现金比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "现金流量比率",
                "code": "XJLLBL",
                "id": "837036546d4343039099e8432dbbc910",
                "indexType": "MEASURE",
                "memo": "现金流量比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "销售净利率",
                "code": "XSJLL",
                "id": "926957e7e9bf4953b44b0784c23dce89",
                "indexType": "MEASURE",
                "memo": "销售净利率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "销售毛利率",
                "code": "XSMLL",
                "id": "8614cafac6a54d0d9a1b332d933e2897",
                "indexType": "MEASURE",
                "memo": "销售毛利率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "应收账款周转率_次",
                "code": "YSZKZZL_CI",
                "id": "69798a0a82e34aedb14c29b5939c3659",
                "indexType": "MEASURE",
                "memo": "应收账款周转率_次",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "应收账款周转天数",
                "code": "YSZKZZTS",
                "id": "c4483a06cf914caaa68ffeba02518801",
                "indexType": "MEASURE",
                "memo": "应收账款周转天数",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "营业利润率",
                "code": "YYLRL",
                "id": "69e23bc997f24953a3381bcaa216231d",
                "indexType": "MEASURE",
                "memo": "营业利润率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "资本固定化比率",
                "code": "ZBGDHBL",
                "id": "2c69b647cf6f4b14aa653bca5c8698b0",
                "indexType": "MEASURE",
                "memo": "资本固定化比率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "资产负债率",
                "code": "ZCFZL",
                "id": "18019603188b4c2cba62e7b1628108b5",
                "indexType": "MEASURE",
                "memo": "资产负债率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "资产经营现金流量回报率",
                "code": "ZCJYXJLLHBL",
                "id": "f6d4585141884fa9aa6f737aeae7bb4f",
                "indexType": "MEASURE",
                "memo": "资产经营现金流量回报率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "证券代码",
                "code": "ZQDM",
                "id": "1ff99107ccb742278285be4291409d27",
                "indexType": "DIMENSION",
                "memo": "证券代码",
                "dataTypeId": "4028f9fc59a545110159a545736a009c",
                "format": "TEXT",
                "numberFormat": null
            }, {
                "name": "主营利润比重",
                "code": "ZYLRBZ",
                "id": "17d3e1f831264bbdbf70cf31a6ba78d2",
                "indexType": "MEASURE",
                "memo": "主营利润比重",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "主营业务成本率",
                "code": "ZYYWCBL",
                "id": "02e7e9a0eebd4eaca748461998db7761",
                "indexType": "MEASURE",
                "memo": "主营业务成本率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "主营业务利润率",
                "code": "ZYYWLRL",
                "id": "5fec06ea9f034e759a140d73263fc265",
                "indexType": "MEASURE",
                "memo": "主营业务利润率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "主营业务利润_元",
                "code": "ZYYWLR_YUAN",
                "id": "fcb25d42ce43494d8b1039139459e0e0",
                "indexType": "MEASURE",
                "memo": "主营业务利润_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "主营业务收入增长率",
                "code": "ZYYWSRZZL",
                "id": "617fa0816a414121b60d450cbab8ee55",
                "indexType": "MEASURE",
                "memo": "主营业务收入增长率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产报酬率",
                "code": "ZZCBCL",
                "id": "67fa1a5e51774f13a89ad34c178d7c83",
                "indexType": "MEASURE",
                "memo": "总资产报酬率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产净利润率",
                "code": "ZZCJLRL",
                "id": "53fe71e24cda42709fe98582459b3986",
                "indexType": "MEASURE",
                "memo": "总资产净利润率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产利润率",
                "code": "ZZCLRL",
                "id": "0b15b5d38894498ab4afbf3f7b99a598",
                "indexType": "MEASURE",
                "memo": "总资产利润率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产_元",
                "code": "ZZC_YUAN",
                "id": "ba770ee80f774d41857897b14ed0c172",
                "indexType": "MEASURE",
                "memo": "总资产_元",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产周转率_次",
                "code": "ZZCZHZL_CI",
                "id": "cdbe2a9901e648279a13a55dc102e22d",
                "indexType": "MEASURE",
                "memo": "总资产周转率_次",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产周转天数",
                "code": "ZZCZHZTS",
                "id": "0daab74941814ea4b920b7f3b834ea7e",
                "indexType": "MEASURE",
                "memo": "总资产周转天数",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }, {
                "name": "总资产增长率",
                "code": "ZZCZZL",
                "id": "912f950aad8448d7b0eb0132f9c44027",
                "indexType": "MEASURE",
                "memo": "总资产增长率",
                "dataTypeId": "4028f9fc59a545110159a545736a009e",
                "format": "NUMBER",
                "numberFormat": null
            }]
        }

    },
    deleteColumn: {},
    filterCondition: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-405",
            "msg": "不允许使用请求指定的方法(Method Not Allowed)",
            "detailErrorMsg": null,
            "data": null
        }
    },
    format: {},
    getDataSetStepInfo: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": {
                "id": null,
                "dataSetId": null,
                "userId": null,
                "dataSetAlias": null,
                "code": "editColumn",
                "columnId": "0bfea6db15124e08bcc7dd583eb9242e",
                "showColumnName": "公司名称",
                "metaShowColumnName": null,
                "columnCode": "GSMC",
                "comment": "公司名称",
                "metaComment": null,
                "indexType": "DIMENSION"
            }
        }

    },
    join: {},
    numberFormat: {},
    syncColumn: {},
    union: {},
    deleteDataStep: {},
    editDataStep: {},
    isIssue: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": "success",
            "detailErrorMsg": null,
            "data": true
        }

    },
    createService: {},
    queryElasticsColumns: {},
    checkServiceName: {},
    testService: {},
    moveLogicDataSet: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "请选择不同的目录树！",
            "detailErrorMsg": "com.code.common.utils.assertion.IllegalLogicException: 请选择不同的目录树！",
            "data": null
        }

    },
    changeIsFast: {
        "data": {
            "status": 1,
            "code": 1,
            "errorCode": "HTTP-500",
            "msg": "Failed to invoke the method cacheTable in the service com.code.common.spark.service.ISparkClientService. Tried 3 times of the providers [**************:20880] (1/1) from the registry **************:2181 on the consumer ************** using the dubbo version 2.6.2. Last error is: Failed to invoke remote method: cacheTable, provider: dubbo://**************:20880/com.code.common.spark.service.ISparkClientService?anyhost=true&application=spark-consumer&check=false&dubbo=2.6.2&generic=false&interface=com.code.common.spark.service.ISparkClientService&methods=registerAll,query,registerTable,count,queryStructSchema,unCacheTable,cacheTable,isTableFinishRegister,registerInformation,dropTable&pid=24673&register.ip=**************&remote.timestamp=1598854646789&revision=1.0.0&side=consumer&timeout=60000000&timestamp=1598854733782&version=1.0.0, cause: java.io.IOException: Response data error, expect Throwable, but get {stop={line={x=1}, startPosition={x=0}}, line={x=1}, start={line={x=1}, startPosition={x=0}}, suppressedExceptions=[], cause=null, stackTrace=[Ljava.lang.StackTraceElement;@4829d, detailMessage=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, message=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, startPosition={x=0}, command={x=222}}\njava.io.IOException: Response data error, expect Throwable, but get {stop={line={x=1}, startPosition={x=0}}, line={x=1}, start={line={x=1}, startPosition={x=0}}, suppressedExceptions=[], cause=null, stackTrace=[Ljava.lang.StackTraceElement;@4829d, detailMessage=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, message=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, startPosition={x=0}, command={x=222}}\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DecodeableRpcResult.decode(DecodeableRpcResult.java:94)\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DecodeableRpcResult.decode(DecodeableRpcResult.java:113)\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DubboCodec.decodeBody(DubboCodec.java:89)\n\tat com.alibaba.dubbo.remoting.exchange.codec.ExchangeCodec.decode(ExchangeCodec.java:124)\n\tat com.alibaba.dubbo.remoting.exchange.codec.ExchangeCodec.decode(ExchangeCodec.java:84)\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DubboCountCodec.decode(DubboCountCodec.java:46)\n\tat com.alibaba.dubbo.remoting.transport.netty.NettyCodecAdapter$InternalDecoder.messageReceived(NettyCodecAdapter.java:133)\n\tat org.jboss.netty.channel.SimpleChannelUpstreamHandler.handleUpstream(SimpleChannelUpstreamHandler.java:70)\n\tat org.jboss.netty.channel.DefaultChannelPipeline.sendUpstream(DefaultChannelPipeline.java:564)\n\tat org.jboss.netty.channel.DefaultChannelPipeline.sendUpstream(DefaultChannelPipeline.java:559)\n\tat org.jboss.netty.channel.Channels.fireMessageReceived(Channels.java:268)\n\tat org.jboss.netty.channel.Channels.fireMessageReceived(Channels.java:255)\n\tat org.jboss.netty.channel.socket.nio.NioWorker.read(NioWorker.java:88)\n\tat org.jboss.netty.channel.socket.nio.AbstractNioWorker.process(AbstractNioWorker.java:109)\n\tat org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:312)\n\tat org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:90)\n\tat org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)\n\tat org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)\n\tat org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n",
            "detailErrorMsg": "com.alibaba.dubbo.rpc.RpcException: Failed to invoke the method cacheTable in the service com.code.common.spark.service.ISparkClientService. Tried 3 times of the providers [**************:20880] (1/1) from the registry **************:2181 on the consumer ************** using the dubbo version 2.6.2. Last error is: Failed to invoke remote method: cacheTable, provider: dubbo://**************:20880/com.code.common.spark.service.ISparkClientService?anyhost=true&application=spark-consumer&check=false&dubbo=2.6.2&generic=false&interface=com.code.common.spark.service.ISparkClientService&methods=registerAll,query,registerTable,count,queryStructSchema,unCacheTable,cacheTable,isTableFinishRegister,registerInformation,dropTable&pid=24673&register.ip=**************&remote.timestamp=1598854646789&revision=1.0.0&side=consumer&timeout=60000000&timestamp=1598854733782&version=1.0.0, cause: java.io.IOException: Response data error, expect Throwable, but get {stop={line={x=1}, startPosition={x=0}}, line={x=1}, start={line={x=1}, startPosition={x=0}}, suppressedExceptions=[], cause=null, stackTrace=[Ljava.lang.StackTraceElement;@4829d, detailMessage=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, message=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, startPosition={x=0}, command={x=222}}\njava.io.IOException: Response data error, expect Throwable, but get {stop={line={x=1}, startPosition={x=0}}, line={x=1}, start={line={x=1}, startPosition={x=0}}, suppressedExceptions=[], cause=null, stackTrace=[Ljava.lang.StackTraceElement;@4829d, detailMessage=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, message=mismatched input '222' expecting {'SELECT', 'FROM', 'ADD', 'AS', 'ALL', 'DISTINCT', 'WHERE', 'GROUP', 'BY', 'GROUPING', 'SETS', 'CUBE', 'ROLLUP', 'ORDER', 'HAVING', 'LIMIT', 'AT', 'OR', 'AND', 'IN', NOT, 'NO', 'EXISTS', 'BETWEEN', 'LIKE', RLIKE, 'IS', 'NULL', 'TRUE', 'FALSE', 'NULLS', 'ASC', 'DESC', 'FOR', 'INTERVAL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'JOIN', 'CROSS', 'OUTER', 'INNER', 'LEFT', 'SEMI', 'RIGHT', 'FULL', 'NATURAL', 'ON', 'LATERAL', 'WINDOW', 'OVER', 'PARTITION', 'RANGE', 'ROWS', 'UNBOUNDED', 'PRECEDING', 'FOLLOWING', 'CURRENT', 'FIRST', 'AFTER', 'LAST', 'ROW', 'WITH', 'VALUES', 'CREATE', 'TABLE', 'DIRECTORY', 'VIEW', 'REPLACE', 'INSERT', 'DELETE', 'INTO', 'DESCRIBE', 'EXPLAIN', 'FORMAT', 'LOGICAL', 'CODEGEN', 'COST', 'CAST', 'SHOW', 'TABLES', 'COLUMNS', 'COLUMN', 'USE', 'PARTITIONS', 'FUNCTIONS', 'DROP', 'UNION', 'EXCEPT', 'MINUS', 'INTERSECT', 'TO', 'TABLESAMPLE', 'STRATIFY', 'ALTER', 'RENAME', 'ARRAY', 'MAP', 'STRUCT', 'COMMENT', 'SET', 'RESET', 'DATA', 'START', 'TRANSACTION', 'COMMIT', 'ROLLBACK', 'MACRO', 'IGNORE', 'BOTH', 'LEADING', 'TRAILING', 'IF', 'POSITION', 'DIV', 'PERCENT', 'BUCKET', 'OUT', 'OF', 'SORT', 'CLUSTER', 'DISTRIBUTE', 'OVERWRITE', 'TRANSFORM', 'REDUCE', 'SERDE', 'SERDEPROPERTIES', 'RECORDREADER', 'RECORDWRITER', 'DELIMITED', 'FIELDS', 'TERMINATED', 'COLLECTION', 'ITEMS', 'KEYS', 'ESCAPED', 'LINES', 'SEPARATED', 'FUNCTION', 'EXTENDED', 'REFRESH', 'CLEAR', 'CACHE', 'UNCACHE', 'LAZY', 'FORMATTED', 'GLOBAL', TEMPORARY, 'OPTIONS', 'UNSET', 'TBLPROPERTIES', 'DBPROPERTIES', 'BUCKETS', 'SKEWED', 'STORED', 'DIRECTORIES', 'LOCATION', 'EXCHANGE', 'ARCHIVE', 'UNARCHIVE', 'FILEFORMAT', 'TOUCH', 'COMPACT', 'CONCATENATE', 'CHANGE', 'CASCADE', 'RESTRICT', 'CLUSTERED', 'SORTED', 'PURGE', 'INPUTFORMAT', 'OUTPUTFORMAT', DATABASE, DATABASES, 'DFS', 'TRUNCATE', 'ANALYZE', 'COMPUTE', 'LIST', 'STATISTICS', 'PARTITIONED', 'EXTERNAL', 'DEFINED', 'REVOKE', 'GRANT', 'LOCK', 'UNLOCK', 'MSCK', 'REPAIR', 'RECOVER', 'EXPORT', 'IMPORT', 'LOAD', 'ROLE', 'ROLES', 'COMPACTIONS', 'PRINCIPALS', 'TRANSACTIONS', 'INDEX', 'INDEXES', 'LOCKS', 'OPTION', 'ANTI', 'LOCAL', 'INPATH', IDENTIFIER, BACKQUOTED_IDENTIFIER}, startPosition={x=0}, command={x=222}}\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DecodeableRpcResult.decode(DecodeableRpcResult.java:94)\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DecodeableRpcResult.decode(DecodeableRpcResult.java:113)\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DubboCodec.decodeBody(DubboCodec.java:89)\n\tat com.alibaba.dubbo.remoting.exchange.codec.ExchangeCodec.decode(ExchangeCodec.java:124)\n\tat com.alibaba.dubbo.remoting.exchange.codec.ExchangeCodec.decode(ExchangeCodec.java:84)\n\tat com.alibaba.dubbo.rpc.protocol.dubbo.DubboCountCodec.decode(DubboCountCodec.java:46)\n\tat com.alibaba.dubbo.remoting.transport.netty.NettyCodecAdapter$InternalDecoder.messageReceived(NettyCodecAdapter.java:133)\n\tat org.jboss.netty.channel.SimpleChannelUpstreamHandler.handleUpstream(SimpleChannelUpstreamHandler.java:70)\n\tat org.jboss.netty.channel.DefaultChannelPipeline.sendUpstream(DefaultChannelPipeline.java:564)\n\tat org.jboss.netty.channel.DefaultChannelPipeline.sendUpstream(DefaultChannelPipeline.java:559)\n\tat org.jboss.netty.channel.Channels.fireMessageReceived(Channels.java:268)\n\tat org.jboss.netty.channel.Channels.fireMessageReceived(Channels.java:255)\n\tat org.jboss.netty.channel.socket.nio.NioWorker.read(NioWorker.java:88)\n\tat org.jboss.netty.channel.socket.nio.AbstractNioWorker.process(AbstractNioWorker.java:109)\n\tat org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:312)\n\tat org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:90)\n\tat org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)\n\tat org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)\n\tat org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n",
            "data": null
        }

    },
    getJoinAndFilter: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": null
        }

    },
    checkDataSetUserInVisual: {
        "data": {
            "status": 0,
            "code": 0,
            "errorCode": null,
            "msg": null,
            "detailErrorMsg": null,
            "data": "success"
        }

    },
    saveAs: {}
};
export default data;