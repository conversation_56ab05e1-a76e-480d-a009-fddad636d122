
<template>
    <div class="chartCard" v-loading="loading">
        <div v-if="data_empty" class="ce-data-none">暂无数据</div>
        <!-- <div  v-for="(item , key) in data" :key="key" class="all_style">
            <div class="key_style">{{item.key}} </div> <div class="value_style"> {{item.value}}</div>
        </div> -->
        <div class="ce-padding " ref="tab_charts" :style="{'height': `calc( 100% - ${titleH + tabH + 15}px)`}" v-else>
            <div class="listTitle" :style="[titleStyle , {'line-height' : titleH + 'px'}]" v-show="showTitle">{{title}}</div>
            <table border="" class="ce-simple_table" >
                <tbody v-for="(data , index1) in formData" :key="index1">
                    <tr v-for="(item ,key, index) in data" :key="index">
                        <template v-if="index%2==0&&!showOneColumn">
                            <th :title="key" :width="columnWidth" v-if="showKey" :height="height" :class="{'omit':isEll,'newline':!isEll}">
                                {{key}}
                            </th>
                            <td :title="item" :class="{'omit':isEll,'newline':!isEll}"  :height="height">
                                <span :class="{'pre-wrap':!isEll}" v-html="item" v-if="item"></span>
                                <div class="empty-box" v-else></div>
                            </td>
                            <th :title="Object.keys(data)[index+1]" v-if="showKey" :width="columnWidth" :height="height" :class="{'omit':isEll,'newline':!isEll}" >
                                {{Object.keys(data)[index+1]}}
                            </th>
                            <td :title="data[Object.keys(data)[index+1]]" :class="{'omit':isEll,'newline':!isEll}"  :height="height" >
                                <span :class="{'pre-wrap':!isEll}" v-html="data[Object.keys(data)[index+1]]" v-if="data[Object.keys(data)[index+1]]"></span>
                                <div class="empty-box" v-else></div>
                            </td>
                        </template>
                        <template v-if="showOneColumn">
                            <th :title="key" :class="{'omit':isEll,'newline':!isEll}" v-if="showKey" :width="columnWidth" :height="height">
                                {{key}}
                            </th>
                            <td :title="item" :class="{'omit':isEll,'newline':!isEll}" :height="height">
                                <span :class="{'pre-wrap':!isEll}" v-html="item" v-if="item"></span>
                                <div class="empty-box" v-else></div>
                            </td>
                        </template>
                    </tr>
                </tbody>
                    
            </table>
        </div>
    </div>
</template>

<script src="./form-card.js"></script>
<style scoped lang="less" src="../css/chart.less"></style>
<style scoped lang="less" src="./form-card.less"></style>
