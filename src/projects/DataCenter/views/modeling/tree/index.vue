<template>
    <div class="tree" v-loading="settings.loading">
        <dg-tree
                :data="data"
                node-key="id"
                :expand-on-click-node="false"
                :props="defaultProps"
                :filter-node-method="filterNode"
                ref="tree"
                check-strictly
                :highlight-current="true"
                :default-expanded-keys="expandData"
                @node-click="nodeClick"
        >
                    <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }"
                          :class="{'disabled': disableClick(data)}"
                          @mouseenter="rightMenu($event , node , data)">
                        <span class="node-label"
                              :title="data.name"
                              :class="{
                            'el-icon-folder' : !node.expanded ,
                            'el-icon-folder-opened' : node.expanded,
                          }"
                        >{{ data.name }}</span>

                        <span class="label__action" v-if="data.id !== '0' && !hasSave && nodeEditable(data)">
                            <dg-button type="text" :title="addBtnTxt" class="el-icon-plus b"
                                       @click.stop="addChildren($event , node , data)"
                                       v-if="data.dirType === 'TRANS_DIR_MF_MY' && data.pId === '0' || data.dirType === 'TRANS_DIR_STANDARD' "></dg-button>
                            <dir-edit-action v-else-if="countHasRightMenu > 0 && nodeEditable(data)"
                                             :value="node.data"
                                             @command="menuCommand($event , node , data)" :data="treeMenu"
                                             :rights="rights" :node="node">
                            </dir-edit-action>
                        </span>
                    </span>
        </dg-tree>
    </div>
</template>

<script src="./tree.js"></script>
<style scoped lang="less" src="./tree.less"></style>
