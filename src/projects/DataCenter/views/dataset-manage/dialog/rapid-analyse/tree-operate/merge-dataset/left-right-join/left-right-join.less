

.merge-tabs-leftright {
  .m-t-l-item {
    display: flex;
    align-items: center;
    margin-bottom: 19px;
    line-height: 32px;
    label {
      min-width: 80px;
      text-align: right;
      padding-right: 10px;
    }
  }
  .m-t-l-item:first-child {
    .m-t-l-i-content {
      width: 860px;
    }
  }
  .m-t-l-item:last-child {
    align-items: flex-start;
    .m-t-l-i-content {
      flex: 1;
      width: 860px;
    }
  }
}

.expect-select {
  /deep/.el-select {
    display: block;
  }
}
.ce-table .el-table--border td:last-child {
  border-right: 1px;
}