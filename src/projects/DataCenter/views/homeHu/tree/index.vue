<template>
    <div class="tree" v-loading="settings.loading">
        <dg-tree
                :data="data"
                node-key="id"
                :expand-on-click-node="false"
                :props="defaultProps"
                :filter-node-method="filterNode"
                :default-expand-all="true"
                check-strictly
                ref="tree"
                :default-expanded-keys="expandData"
                :highlight-current="true"
                @node-click="getCenterTable"
        >
                    <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }"
                          :class="{'disabled': disableClick(data)}"
                          @mouseenter="rightMenu($event , node , data)">
                        <span class="node-label"
                              :title="data.label"
                              :class="{
                            'el-icon-folder' : !node.expanded ,
                            'el-icon-folder-opened' : node.expanded,
                          }"
                        >{{ data.label }}</span>

                        <span class="label__action" v-if="data.id !== '0' && data.id !== '2' && data.pId !== '-1' && !hasSave && nodeEditable(data)">
                            <dg-button type="text" :title="addBtnTxt" class="el-icon-plus b" @click.stop="addChildren($event , node , data)" v-if="data.code === 'PORTAL_DIR_MY' || data.code ==='POR_DIR_STANDARD'"></dg-button>
                            <dir-edit-action v-else-if="countHasRightMenu > 0"
                                             :value="node.data"
                                             @command="menuCommand($event , node , data)" :data="treeMenu"
                                             :rights="rights" :node="node">
                            </dir-edit-action>
                        </span>
                    </span>
        </dg-tree>
    </div>
</template>

<script src="./tree.js"></script>

<style scoped lang="less" src="./tree.less"></style>
