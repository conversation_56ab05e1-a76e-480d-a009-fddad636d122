<template>
    <div class="BusinessDomainManage">
        <ce-list-table
                :border="false"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                :operate-icons="operateIcons"
                :filter-placeholder="filterPlaceholder"
                @changePage="changePage"
        >
            <template slot="button">
                <el-button type="primary" @click="addClassify" >{{addTxt}}</el-button>
            </template>
        </ce-list-table>
    </div>
</template>

<script>
import {common} from "@/api/commonMethods/common";

export default {
    name: "BusinessDomainManage",
    mixins:[common],
    data(){
        return {
            filterPlaceholder:"请输入分类名称搜索",
            filterText : '',
            tHeadData:[
                {
                    prop:"scopeName" ,
                    label : "业务域分类名称",
                    minWidth : "160px"
                },{
                    prop:"code" ,
                    label : "标识编码",
                    minWidth : "100px"
                },{
                    prop:"memo" ,
                    label : "备注",
                    minWidth : "100px"
                },{
                    prop:"operate" ,
                    label : "操作",
                    width : "140px"
                },
            ],
            tableData:[],
            addTxt:"新建",
            operateIcons : [
                {
                    name: "编辑",
                    clickFn: this.edit,
                    show: () => true,
                    condition: (right) => true
                },{
                    name: "删除",
                    clickFn: this.deleteM,
                    show: () => true,
                    condition: (right) => true
                },
            ] ,
        }
    },
    methods : {
        /**
         * 编辑
         */
        edit (row ,inx){} ,
        /**
         * 删除
         */
        deleteM (row ,inx){
            const vm = this;
            vm.confirm('删除提示' , `删除后所有标签无该业务域分类管理,你还要继续吗？` , ()=>{

            })
        } ,
        /**
         * 分页
         */
        changePage(inx){

        },
        /**
         * 新建
         */
        addClassify(){
            const vm = this;
            let layer = vm.$dgLayer({
                title : "新建分类" ,
                content : require("../components/addClassification.vue"),
                maxmin:false,
                move:false,
                area : ['668px' ,'380px'],
                props: {
                    typeName : "业务域"
                },
                on : {
                    submit(form){
                        console.log(form)
                    }
                }
            })
        }
    }
}
</script>

<style scoped>

</style>
