import {dialog} from "@/api/commonMethods/dialog-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
import treePage from "./tree-page"
export default {
    name: "index" ,
    mixins :[commonMixins , dialog  ],
    components : {treePage} ,
    data(){
        return {
            title : "移动" ,
            width : "316px" ,
            rowData : {},
            services : {},
            treeNode :{}
        }
    },
    methods : {
        show(row , services){
            const vm = this;
            vm.visible =true;
            vm.reNew = true;
            vm.rowData = row;
            vm.services = services;
        },
        submit(){
            const vm = this , {services ,rowData , treeNode} = this;
            if(vm.treeNode.id === undefined){
                vm.$message.warning("请选择数据集目录！");
                return;
            }
            services.moveLogicDataSet(rowData.id , treeNode.id ).then(res => {
                if(res.data.status === 0){
                    vm.$emit("moveSuccess" , treeNode.id);
                    vm.visible = false;
                    vm.$message.success("移动成功");
                }
            })
        },
        nodeClick(label,value,node){
            this.treeNode = node.data;
        }
    }

}