<template>
    <div class="searchTree">
        <el-input clearable :placeholder="placeholder" v-model="filterText" :size="size" class="ce-search">
<!--            <i slot="suffix" class="el-input__icon el-icon-search"></i>-->
        </el-input>
        <div class="ce-tree selectn">
            <el-tree
                    ref="tree"
                    v-on="$listeners"
                    v-bind="Object.assign({} , $attrs)"

            >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span draggable="true" v-if="data.dbtype" @dragstart="setDragData( $event , data )" class="el-tree-node__label"
                          :title="`${node.label}(${data.dbtype})`"
                    >{{ node.label }}
                    (<span :class="'custom-tree-node__'+(data.indexType === 'DIMENSION' ? 'dim':'mea')">{{data.dbtype}}</span>)
                    </span>
                    <span v-else  :title="node.label" class="el-tree-node__label">{{ node.label }}</span>
                </span>
            </el-tree>
        </div>

    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    // import {treeMethods} from "@/api/treeNameCheck/treeName"
    export default {
        name: "SearchTree",
        props: {
            size: {
                type: String,
                default() {
                    return 'mini';
                }
            }
        },
        watch:
            {
                filterText(val) {
                    this.$refs.tree.filter(val);
                }
            },
        data() {
            return {
                placeholder: '名称过滤',
                filterText: '',

            }
        },
        methods : {
            /*filterNode(value, data) {
                if (!value) return true;
                return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },*/
            setDragData( e , data ){
                globalBus.$emit('treeNode' , data );//限制不允许放置画布
                e.dataTransfer.setData('node',data );
            }
        }
    }
</script>

<style scoped lang="less">
    .searchTree {
        padding: 0 5px;
        height: 100%;
    }
    .tree {
        float: left;
        margin: 10px 0 0 10px;
        width: 230px;
        padding: 10px;
        background: #fff;
        border: 1px solid #ddd;
        height: calc(100% - 88px);
    }

    .ce-tree {
        margin-top: 12px;
        height: calc(100% - 40px);
        overflow: auto;
    }

    .ce-tree__menu {
        position: fixed;
        top: 0;
        min-width: 80px;
        text-align: left;
        border: 1px solid #ccc;
        background: #fff;
        padding: 0;
        z-index: 100;
        box-shadow: 2px 2px 3px rgba(0, 0, 0, .15);
        border-radius: 2px;
    }

    .ce-tree__menu li {
        cursor: pointer;
        list-style: none outside none;
        font-size: 12px;
        white-space: nowrap;
        border-bottom: 1px solid #eee;
        padding: 0 12px;
        height: 28px;
        line-height: 28px;
        color: #666;
    }

    .ce-tree__menu li:hover {
        color: @font-color;
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
        &__dim {
            color:@sysMainColor;
        }
        &__mea {
            color:@secColorG1;
        }

    }

    .node-label {
        max-width: 166px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .node-label::before {
        padding-right: 5px;
    }
</style>
