<!--
    @Describe: 模型申请
    @Author: wangjt
    @Date: 2024/4/17
-->
<template>
    <div class="apply-outer" v-loading="settings.loading">
        <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="100px">
            <el-form-item v-for="(item, k) in formData" :key="k" :label="`${item.label} :`" :prop="item.prop">
                <el-input v-model="form[k]" :disabled="item.disabled" v-if="item.formType === 'input'"
                    :maxlength="item.maxlength" show-word-limit v-input-limit:[item.limit] :class="item.className"
                    :placeholder="item.placeholder"></el-input>
                <dg-select v-model="form[k]" v-if="item.formType === 'select'" :disabled="item.disabled"
                    :data="item.data()" :placeholder="item.placeholder"></dg-select>
                <span v-if="item.formType === 'span'">{{ form[k] }}</span>
                <el-input v-model="form[k]" type="textarea" :maxlength="item.maxlength" show-word-limit
                    v-if="item.formType === 'textarea'" :rows="item.rows" :placeholder="item.placeholder"></el-input>
            </el-form-item>
        </el-form>
        <dialogFooterBtn v-if="showBtn" v-footer :data="btnGroup" />
    </div>

</template>

<script>
import { commonMixins } from "dc-front-plugin/src/api/commonMethods/common-mixins"
import marketUse from "@store/modules/market/market-constant"
import DialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn";
export default {
    name: "applyForModel",
    mixins: [commonMixins, marketUse],
    props: {
        modelDataInfo: Object,
        userInfo: Object,
        showBtn: {
            type: Boolean,
            default: true
        },
        // 申请类型 1：模型复用  0：模型发布
        applyType: {
            type: String,
            default: "1",
        }
    },
    components: { DialogFooterBtn },
    computed: {
        modelType() {
            return this.m_market_get_model_types();
        }
    },
    data() {
        return {
            form: {
                resourceType: "",
                resourceId: "",
                resourceName: "",
                applyUserId: "",
                applyUserName: "",
                auditUserId: "",
                auditUserName: "",
                modelEntry: "",
                applyReason: "",
            },
            rules: {
                applyReason: [
                    { required: true, message: '请输入申请原因', trigger: ['blur', 'change'] }
                ],
                modelEntry: [
                    // { required: true, message: '请输入模型入口', trigger: ['blur', 'change'] },
                    { validator: this.checkedCommon, reg: /^(?!100$)/, message: "限制填写100", trigger: 'blur' },
                ]
            },
            formData: {
                resourceType: {
                    label: '资源类型',
                    prop: "resourceType",
                    formType: "select",
                    placeholder: "请选择资源类型",
                    data: () => this.modelType,
                    disabled: true,
                },
                resourceName: {
                    label: '资源名称',
                    prop: "resourceName",
                    formType: "input",
                    placeholder: "请输入资源名称",
                    disabled: true,
                },
                applyUserName: {
                    label: '申请人',
                    prop: "applyUserName",
                    formType: "input",
                    disabled: true,
                },
                auditUserName: {
                    label: '审批人',
                    prop: "auditUserName",
                    formType: "input",
                    disabled: true,
                },
                modelEntry: {
                    label: '模型入口',
                    prop: "modelEntry",
                    formType: "input",
                    limit: 'trim',
                    maxlength: 100,
                    placeholder: "请输入模型入口",
                    className: 'enter-inp',
                },
                applyReason: {
                    label: '申请原因',
                    prop: "applyReason",
                    formType: "textarea",
                    maxlength: 500,
                    rows: 5,
                    placeholder: "请输入申请原因",
                }
            },
            btnGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '提交',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.saveFn,
                    disabledFn: () => this.settings.loading,
                    show: () => true
                },
            ]
        }
    },
    methods: {
        validate() {
            let flag = true;
            this.$refs['form'].validate((valid) => {
                if (valid) {
                } else {
                    flag = false;
                    return false;
                }
            })
            return flag;
        },
        /**
         * 申请类型有（模型发布，模型复用），用枚举即可 ，   这边默认为：模型复用类型
         */
        async saveFn() {
            const vm = this, { form, settings, applyType } = this;
            const params = {
                applyType: applyType, //申请类型
                resourceType: form.resourceType,
                resourceId: form.resourceId,
                resourceName: form.resourceName,
                auditUserId: form.auditUserId,
                applyReson: form.applyReason,
                applyUserId: form.applyUserId,
                modelEntry: form.modelEntry,
            };
            await new Promise((resolve, reject) => {
                vm.$refs.form.validate(async valid => {
                    if (valid) {
                        settings.loading = true;
                        vm.$services('market').resourceApply(params, settings).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("申请成功")
                                vm.$emit("submit");
                                resolve()
                            }
                        }).catch(() => { reject() })
                    } else reject()
                })
            })
        },
        init() {
            const { modelDataInfo, userInfo } = this;
            this.form.resourceType = modelDataInfo.type;
            this.form.resourceId = modelDataInfo.transId;
            this.form.resourceName = modelDataInfo.transName;
            this.form.applyUserId = userInfo.id;
            this.form.applyUserName = userInfo.objName;
            this.form.auditUserId = modelDataInfo.transUserId;
            this.form.auditUserName = modelDataInfo.publishUser.userName;
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.apply-outer {
    height: 100%;
    overflow: hidden;
}

/deep/ .el-input__count-inner,
.el-textarea /deep/.el-input__count {
    background-color: #fff;
}

.enter-inp {
    /deep/ .el-input__inner {
        padding-right: 60px;
    }
}
</style>
