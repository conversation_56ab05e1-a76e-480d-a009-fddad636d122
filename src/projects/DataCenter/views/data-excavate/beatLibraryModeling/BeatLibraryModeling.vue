<template>
    <div class="home">
        <Breadcrumb :crumb="defaultCrumb"/>
        <BeatLibraryModelingTree @initTable="initTable" ref="tree" @getCenterTable="getTableData" :has-save="false" @showNewNamePanel="showNewNamePanel"/>
        <div class="ce-right_cont">
            <BeatLibraryModelingTable @updateDirName="updateDirName" @init="treeInit" @showPanel="showPanel" ref="beatLibraryModelingTable" />
        </div>
        <NewMode ref="newModel_panel" @closePlane="closeEditPanel" v-if="showEditPanel" :rowData="rowData" ></NewMode>

    </div>
</template>sqlEditPage

<script>
    import Breadcrumb from "@/components/crumb/Breadcrumb";
    import BeatLibraryModelingTable from '../beatLibraryModeling/BeatLibraryModelingTable'
    import BeatLibraryModelingTree from '../beatLibraryModeling/BeatLibraryModelingTree'
    import NewMode from './NewModel'

    export default {
        name: "Home",
        props: {
            crumb: Array
        },
        components: {
            BeatLibraryModelingTree,
            Breadcrumb,
            BeatLibraryModelingTable,
            NewMode,
        },
        data() {
            return {
                defaultCrumb : ['数据建模' , 'SQL建模'],
                showEditPanel: false,
                rowData:{},
            }
        },
        methods: {
            updateDirName(val){
                this.$refs.tree.updateDirName(val);
            },
            initTable(){
                this.$refs.beatLibraryModelingTable.changePage(1);
            },
            treeInit(){
                this.$refs.tree.initTree();
            },
            showPanel(type,row){
                this.rowData = row;
                this.showEditPanel  = true;
            },

            showNewNamePanel(val,data){
                this.$refs.beatLibraryModelingTable.showDirNewName(val,data);

            },
            getTableData(treeNode){
                this.$refs.beatLibraryModelingTable.initTable(treeNode);
            },
            closeEditPanel() {
                this.showEditPanel = false;
                this.$refs.beatLibraryModelingTable.changePage(1);
            },
        }

    }
</script>

<style scoped>
    .home {
        height: 100%;
    }


</style>
<style>
    .ce-body_cont {
        background: #fff;
        margin: 10px 10px 0 10px;
        padding: 10px;
        height: calc(100% - 88px);
        border: 1px solid #ddd;
        overflow: auto;
    }

    .ce-right_cont {
        background: #fff;
        margin: 10px 10px 0 270px;
        padding: 10px;
        height: calc(100% - 88px);
        border: 1px solid #ddd;
        overflow: auto;
    }
</style>