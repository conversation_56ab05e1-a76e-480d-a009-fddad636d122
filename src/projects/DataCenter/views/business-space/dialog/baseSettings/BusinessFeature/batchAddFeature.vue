<template>
    <div class="batchAddFeature fixed-size-form">
        <div class="batchTop">
            <el-button v-for="(btn , k) in buttons"
                       :key="k"
                       :type="btn.type"
                       @click="btn.clickFn"
                       v-text="btn.label"
            ></el-button>
        </div>
        <el-form ref="form">
            <common-table
                    :data="feaData"
                    :columns="feaHead"
                    height="412px"
                    :pagination="false"
                    class="ce-form-table_float-tip"
            >
                <template slot="header-operate">
                    <el-button type="text" title="新增" icon="el-icon-circle-plus" @click="addFeature"></el-button>
                </template>
                <template slot="operate" slot-scope="{row , $index}">
                    <el-button
                            v-if="deleteCondition(row)"
                            type="text"
                            @click="deleteCode(row , $index)"
                    >{{ btnDeleteTxt }}
                    </el-button>
                    <el-popconfirm
                            v-else
                            size="mini"
                            title="确认删除?"
                            @confirm="deleteCode(row ,$index)">
                        <el-button slot="reference" type="text">{{ btnDeleteTxt }}</el-button>
                    </el-popconfirm>
                </template>
                <template slot="name" slot-scope="{row ,$index}">
                    <el-form-item prop="name" :rules="[
                                ...checkEmpty($index , '请输入特征名称' , feaData , 'name') ,
                                ...checkSameName($index , '请勿输入相同特征名称' , feaData , 'name')]">
                        <el-input
                                v-model.trim="row.name"
                                :ref="`input_name${$index}`"
                                :maxlength="20"
                                size="mini"
                                placeholder="请输入特征名称"
                                v-input-limit:fieldName
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="code" slot-scope="{row ,$index}">
                    <el-form-item prop="code" :rules="[
                                ...checkEmpty($index , '请输入英文名称' , feaData , 'code') ,
                                ...checkSameName($index , '请勿输入相同英文名称' , feaData , 'code')]">
                        <el-input
                                v-model.trim="row.code"
                                :ref="`input_code${$index}`"
                                :maxlength="20"
                                size="mini"
                                placeholder="请输入英文名称"
                                v-input-limit:fieldCode
                        ></el-input>
                    </el-form-item>
                </template>
                <template slot="businessEle" slot-scope="{row ,$index}">
                    <dg-select class="pct100" size="mini"
                               placeholder="请选择关联业务元"
                               v-model="row.businessEle" :data="eleOption" filterable ></dg-select>
                </template>
            </common-table>
        </el-form>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";

export default {
    name: "batchAddFeature",
    mixins: [commonMixins],
    props: {
        eleOption: {
            type: Array,
            default: () => []
        },
    },
    methods: {
        deleteCondition(row) {
            return row.code === '' && row.name === '' && row.businessEle === '';
        },
        /**
         * 取消
         */
        close() {
            this.$emit("close");
        },
        /**
         * 确认提交
         */
        submit() {

        },
        /**
         * 业务元导入
         */
        businessEleInput() {
        },
        /**
         * 表结构导入
         */
        tableStructureInput() {
        },
        /**
         * excel 导入
         */
        excelInput() {
            const vm = this;
            let layer = vm.$dgLayer({
                title : 'Excel导入',
                content : require('@views/business-space/dialog/baseSettings/components/excelImport.vue'),
                maxmin:false,
                move:false,
                area : ["640px" , "260px"],
                on : {
                    submit(list){

                    }
                }
            })
        },
        /**
         * 自定义添加
         */
        addFeature() {
            const vm = this;
            vm.feaData.push({
                name: "",
                code: "",
                businessEle: ""
            })
            vm.$nextTick(() => {
                vm.$refs[`input_name${(vm.feaData.length - 1)}`].focus();
            });
        },
        /**
         * 删除
         */
        deleteCode(row, inx) {
            this.feaData.splice(inx, 1);
        },
    },
    data() {
        return {
            feaData: [],
            feaHead: [
                {
                    type: "index",
                    label: "#",
                    align: 'center',
                    width: "70px"
                },
                {
                    prop: "name",
                    label: "特征名称",
                }, {
                    prop: "code",
                    label: "英文名称"
                }, {
                    prop: "businessEle",
                    label: "关联业务元"
                }, {
                    prop: 'operate',
                    width: '90px',
                    align: 'center'
                }
            ],
            buttons: {
                businessEleTxt: {
                    label: '业务元导入',
                    type: 'primary',
                    clickFn: this.businessEleInput
                },
                tableStructureTxt: {
                    label: '表结构导入',
                    type: '',
                    clickFn: this.tableStructureInput
                },
                excelTxt: {
                    label: 'Excel导入',
                    type: '',
                    clickFn: this.excelInput
                }
            }

        }
    }
}
</script>

<style scoped>
.batchTop {
    margin-bottom: 10px;
}
</style>
