
export const servicesMixins = {
    data(){
        const {isMock} = this;
        let pluginServices = require("@/projects/DataCenter/services/plugin-services/plugin-services") ,
            pluginMock = isMock ? require("@/projects/DataCenter/mock/plugin-mock/plugin-mock"):{};
        return {
            pluginServices ,
            pluginMock ,
            services : this.getServices(pluginServices , pluginMock)
        }
    }
}
