<template>
    <div class="ce-share" v-loading="settings.loading">

        <el-tabs class="ce-share_tab" v-model="activeName" @tab-click="tabChange">
            <el-tab-pane v-for="(tab , inx) in tabPanes" :key="inx" :label="tab.label" :name="tab.value">

            </el-tab-pane>
        </el-tabs>
        <div class="box">
            <div :class="['ShareItem', activeIndex===i?'active':'']" @click="itemClick(i)" v-for="(data, i) in typeList" :key="i">
                <img class="ShareItem_img" :src="data.url"/>
                <ul class="ShareItem_list"  >
                    {{data.name}}
                </ul>
            </div>
        </div>
        <!--添加数据源-->
        <contact-add ref="contactAdd" @refresh="refresh" @updateAllTypes="updateAllTypes" :selectTreeNode="selectTreeNode" :treeNode="treeNode" :tabActive="tabActive"/>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" @click="nextStep">{{ nextStepTxt }}</dg-button>
    </div>
</template>

<script>
    import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
    import {common} from "@/api/commonMethods/common"
    import contactAdd from "./contactAdd";
    import mysqlImg from "@/assets/images/dataBase/f-MySQL.svg"
    import oracleImg from "@/assets/images/dataBase/f-Oracle.svg"
    import postrgelImg from "@/assets/images/dataBase/f-Postrgel.svg"
    import greenplumlImg from "@/assets/images/dataBase/f-Greenplum.svg"
    import hiveImg from "@/assets/images/dataBase/f-Tencent Hive.svg"
    import kafkaImg from "@/assets/images/dataBase/f-kafka.svg"
    import redisImg from "@/assets/images/dataBase/f-Redis.svg"
    import elasticImg from "@/assets/images/dataBase/f-elastic.svg"
    import tbaseImg from "@/assets/images/dataBase/f-tbase 4.svg"
    import hwmppImg from "@/assets/images/dataBase/l-Greenplum.svg"
    import verticaImg from "@/assets/images/dataBase/vertica.svg"
    import hbaseImg from "@/assets/images/dataBase/f-hbase.svg"
    import gbaseImg from "@/assets/images/dataBase/f-gbase.svg"
    import {servicesMixins} from "@/projects/DataCenter/views/dataSpace/service-mixins/service-mixins";
    export default {
        name: "addDataSet",
        components:{
            contactAdd,
        },
        props : {
            tabActive : String,
            treeNode : Array ,
            selectTreeNode : Object,
        },
        mixins : [ commonMixins , servicesMixins , common],

        data(){
            return {
                typeList : [],
                nextStepTxt : "下一步",
                activeIndex : null,
                relationDataBaseTypeList : [
                    {name :"MYSQL", url : mysqlImg, value :"MYSQL"}, 
                    {name :"ORACLE", url : oracleImg, value :"ORACLE"}, 
                    {name :"POSTGRESQL", url : postrgelImg, value :"POSTGRESQL"}, 
                ],   
                dataStorageTypeList : [
                    {name :"GREENPLUM", url : greenplumlImg, value :"GREENPLUM"}, 
                    {name :"Hive", url : hiveImg, value :"Hive"}, 
                    {name :"tbase", url : tbaseImg, value :"tbase"}, 
                    {name :"vertica", url : verticaImg, value :"Vertica"},
                    {name :"Libra", url : hwmppImg, value :"hwmpp"}, 
                    {name :"Hbase", url : hbaseImg, value :"hbase"},
                    {name :"Gbase", url : gbaseImg, value :"GBASE"},
                ],
                messageQueueTypeList : [
                    {name :"kafka", url : kafkaImg, value :"kafka"}, 
                ],  
                NoSQLTypeList : [
                    {name :"redis", url : redisImg, value :"redis"}, 
                    {name :"ELASTICSEARCH", url : elasticImg, value :"ELASTICSEARCH"}, 
                ],              
                activeName : "all",
                tabPanes : [
                    {
                        label : "全部",
                        value : "all"
                    },{
                        label : "关系型数据库",
                        value : "relationDataBase"
                    },{
                        label : "大数据存储",
                        value : "dataStorage"
                    },{
                        label : "消息队列",
                        value : "messageQueue"
                    },{
                        label : "NoSQL",
                        value : "NoSQL"
                    },
                ],
                dataType : "",
                allTypes : [],
            }
        },
        methods:{
            tabChange() {
                this.clear();
                switch (this.activeName) {
                    case "all":
                        this.typeList = [...this.relationDataBaseTypeList, ...this.dataStorageTypeList, ...this.messageQueueTypeList, ...this.NoSQLTypeList];
                        break;
                    case "relationDataBase":
                        this.typeList = this.relationDataBaseTypeList;
                        break;
                    case "dataStorage":
                        this.typeList = this.dataStorageTypeList;
                        break;
                    case "messageQueue":
                        this.typeList = this.messageQueueTypeList;
                        break;
                    case 'NoSQL' :
                        this.typeList = this.NoSQLTypeList;
                        break;
                    default:

                        break;
                }
            },
            refresh() {
                this.close();
                this.$emit('submit');
            },
            close() {
                this.$emit('close');
            },
            nextStep() {
                if (this.dataType === '') {
                    this.$message.warning('请选择数据源类型');
                    return;
                }
                this.$refs.contactAdd.show(this.dataType);
            },
            clear() {
                this.activeIndex = null;
                this.dataType = '';
            },
            itemClick(i) {
                this.activeIndex = i;
                let actType = this.typeList[i].value,
                    type = this.allTypes.find(o => o.value.toLowerCase() == actType.toLowerCase());
                this.dataType = type?.value || actType;
            },
            updateAllTypes(data){
                this.allTypes = data;
            }
        },
        created() {
            this.typeList = [...this.relationDataBaseTypeList, ...this.dataStorageTypeList, ...this.messageQueueTypeList, ...this.NoSQLTypeList];
        }
    }
</script>

<style scoped lang="less">
    .active{
        border: 1px solid #0088FF;
        box-shadow: 0 5px 10px 0 rgba(24,144,255,0.30);
    }
    .box {
        display: grid;
        grid-row-gap: 14px;
        grid-column-gap: 14px;
        grid-template-columns: repeat(4, 23%);
    }
    .ShareItem {
        border: 1px solid rgba(0,0,0,0.09);
        padding: 0 10px;
        height: 100px;
        border-radius: 2px;
        cursor: pointer;
        &:hover, &.active{
            border: 1px solid #0088FF;
            box-shadow: 0 5px 10px 0 rgba(24,144,255,0.30);
        }
        &_img {
            height: 60px;
            width: 100%;
        }
        &_list {
            // margin-left: 90px;
            padding: 8px 10px;
            text-align: center;
        }
        &_item {
            line-height: 28px;
            color: rgba(0,0,0,0.65);
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            > i {
                padding-right: 4px;
            }
        }
        &_name {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #000;
            text-align: center;
        }
    }
</style>
