/**
 * AI建模接口
 */

import * as reqUtil from '@/api/reqUtil'
const root = '/transOperator/ai/';

/**
 * 初始化树
 */
export async function getAiModelDir( settings) {
    return await reqUtil.get({
        url : root + "getAiModelDir",
        settings : settings
    })
}
/**
 * 新建树目录
 */
export async function addAiModelDir(params, settings) {
    return await reqUtil.post({
        url : root + "addAiModelDir",
        settings : settings
    }, params)
}

/**
 * 删除树目录
 * @param {*} classifyId 
 * @param {*} settings 
 */
export async function deleteAiModelDir(classifyId, settings) {
    return await reqUtil.get({
        url : root + "deleteAiModelDir?classifyId=" + classifyId,
        settings : settings
    })
}

/**
 * 重命名树目录
 * @param {*} classifyId 
 * @param {*} name 
 * @param {*} settings 
 */
export async function renameAiModelDir(classifyId, name, settings) {
    return await reqUtil.get({
        url : root + "renameAiModelDir?classifyId=" + classifyId + "&newClassifyName=" + name,
        settings : settings
    })
}

/**
 * 移动树目录
 * @param {*} curryClassifyId 
 * @param {*} newParentClassifyId 
 * @param {*} settings 
 */
export async function moveAiModelDir(curryClassifyId, newParentClassifyId, settings) {
    return await reqUtil.get({
        url : root + "moveAiModelDir?curryClassifyId=" + curryClassifyId + "&newParentClassifyId=" + newParentClassifyId,
        settings : settings
    })
}

/**
 * 右侧列表显示
 * @param {*} params 
 * @param {*} settings 
 */
export async function getAiModelList(params, settings) {
    return await reqUtil.post({
        url : root + "getAiModelList",
        settings : settings
    },params)
}

/**
 * 列表启动
 * @param {*} scriptId 
 * @param {*} settings 
 */
export async function startAndRun(scriptId, settings) {
    return await reqUtil.get({
        url : root + "startAndRun?scriptId=" + scriptId,
        settings : settings
    })
}
/**
 * 列表重命名
 * @param {*} scriptId 
 * @param {*} newModelName 
 * @param {*} settings 
 */
export async function renameAiModel(scriptId, newModelName, settings) {
    return await reqUtil.get({
        url : root + "renameAiModel?scriptId=" + scriptId + "&newModelName=" + newModelName,
        settings : settings
    })
}
/**
 * 列表另存为
 * @param {*} scriptId 
 * @param {*} classifyId 
 * @param {*} settings 
 */
export async function copyAiModel(scriptId, classifyId, name, description, settings) {
    return await reqUtil.get({
        url : root + "copyAiModel?scriptId=" + scriptId + "&classifyId=" + classifyId + "&name=" + name,
        settings : settings
    })
}
/**
 * 列表删除
 * @param {*} scriptId 
 * @param {*} settings 
 */
export async function deleteAiModel(scriptId, settings) {
    return await reqUtil.get({
        url : root + "deleteAiModel?scriptId=" + scriptId,
        settings : settings
    })
}
/**
 * 列表移动
 * @param {*} scriptId 
 * @param {*} classifyId 
 * @param {*} settings 
 */
export async function moveAiModel(scriptId, classifyId, settings) {
    return await reqUtil.get({
        url : root + "moveAiModel?scriptId=" + scriptId + "&classifyId=" + classifyId,
        settings : settings
    })
}
/**
 * 列表编辑
 * @param {*} params 
 * @param {*} settings 
 */
export async function editAiModelMsg(params, settings) {
    return await reqUtil.post({
        url : root + "editAiModelMsg",
        settings : settings
    }, params)
}
/**
 * 保存模型（新建模型第一步）
 * @param {*} params 
 * @param {*} settings 
 */
export async function newAiModel(params, settings) {
    return await reqUtil.post({
        url : root + "newAiModel",
        settings : settings
    },params)
}

/**
 * 编码开发 - 初始化
 * @param {*} id 
 * @param {*} settings 
 */
export async function startScript(id, settings) {
    return await reqUtil.get({
        url : root + "startScript?scriptId=" + id,
        settings : settings
    })
}
/**
 * 编码开发 （保存模型）
 * @param {*} id 
 * @param {*} settings 
 */
export async function saveScript(id, settings) {
    return await reqUtil.get({
        url : root + "saveScript?scriptId=" + id,
        settings : settings
    })
}

/**
 * 编码开发 （开始训练）
 * @param {*} id 
 * @param {*} settings 
 */
export async function runScript(id, settings) {
    return await reqUtil.get({
        url : root + "runScript?scriptId=" + id,
        settings : settings
    })
}


/**
 * 编码开发 （获取训练结果）
 * @param {*} id 
 * @param {*} settings 
 */
export async function getEvaluateRst(id, settings) {
    return await reqUtil.get({
        url : root + "getEvaluateRst?logId=" + id,
        settings : settings
    })
}

/**
 * 获取训练数据集
 * @param {*} id 
 * @param {*} settings 
 */
export async function getEvaRstDataSet(id, settings) {
    return await reqUtil.get({
        url : root + "getEvaRstDataSet?logId=" + id,
        settings : settings
    })
}
/**
 * 模型训练 - 训练历史
 * @param {*} params 
 * @param {*} settings 
 */
export async function getAiModelRunHistory(params, settings) {
    return await reqUtil.post({
        url : root + "getAiModelRunHistory",
        settings : settings
    }, params)
}

/**
 * 模型训练 - 查看历史
 * @param {*} logId 
 * @param {*} settings 
 */
export async function getLogDetail(logId, settings) {
    return await reqUtil.get({
        url : root + "getLogDetail?logId=" + logId,
        settings : settings
    })
}

/**
 * 模型训练 - 删除历史
 * @param {*} params 
 * @param {*} settings 
 */
export async function deleteLogById(logId, settings) {
    return await reqUtil.get({
        url : root + "deleteLogById?logId=" + logId,
        settings : settings
    })
}

/**
 * 获取模型目录
 * @param {*} params
 * @param {*} settings
 */
export async function getAiModelDetail(id, settings) {
    return await reqUtil.get({
        url : root + "getAiModelDetail?scriptId=" + id,
        settings : settings
    })
}
