.tree {
  --type-h: calc(2rem + 10px);
  height:100%;
  overflow: auto;
}
.ce-left_tree .tree{
  height:calc(100% - 30px);
}
.tree-type {
  padding:0 20px 10px;
}
.tree-content {
  height:calc(100% - var(--type-h));
  overflow: auto;
}
@media screen and (max-width: 1365px){
  .d-s-l-bottom ,.d-s-l-b-tree {
    height: calc(100% - 24px);
  }
}

@media screen and (max-width: 1680px) and (min-width: 1366px){
  .d-s-l-bottom ,.d-s-l-b-tree {
    height: calc(100% - 28px);
  }
}

@media screen and (min-width: 1681px) {
  .d-s-l-bottom ,.d-s-l-b-tree {
    height: calc(100% - 32px);
  }
}
.tree_cont {
  padding-top: 10px;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}
.custom-tree-node  {
  width: calc(100% - 32px) ;
  overflow: hidden;
}
.node-label {
  overflow: hidden;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.el-dropdown-link-rote {
  transform: rotate(90deg);
  display: flex;
  align-items: center;
}

.upload-row {
  p {
    line-height: 1.4;
  }
  .upload-row-p1 {
    color: #cccccc;
    font-size: 14px;
    margin-top: 10px;
  }
  .upload-row-p2 {
    margin-top: 20px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
  }
}

.node-label::before {
  padding-right: 5px;
}
