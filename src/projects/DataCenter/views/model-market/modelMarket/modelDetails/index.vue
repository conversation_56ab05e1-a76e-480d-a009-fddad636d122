<template>
    <div class="market-details" v-loading="settings.loading">
        <dg-scrollbar>
            <div class="market-details_body">
                <div class="market-details_body_left">
                    <div class="market-details_body_left_top">
                        <details-left-top :modelInfo="modelInfo" :modelDataInfo="modelDataInfo" @focusClick="focusClick"
                            @reload="init"></details-left-top>
                    </div>
                    <div class="market-details_body_left_content">
                        <el-tabs class="model-publish_tab" v-model="activeName" @tab-click="tabClick">
                            <el-tab-pane v-for="(tab, inx) in tabPanes" :key="inx"
                                :label="setLabel(tab.label, tab.value)" :name="tab.value">
                            </el-tab-pane>
                        </el-tabs>
                        <component ref="tableList" class="list_cont" :is="activeName" :modelDataInfo="modelDataInfo"
                            :modelEvaluationInfo="modelEvaluationInfo" @evaluateSave="evaluationModel"
                            @evaluateQuery="queryModelEvaluationList" @getEvaluationInfo="getEvaluationInfo">
                        </component>
                    </div>
                </div>
                <div class="market-details_body_right">
                    <details-right :otherModel="otherModel" :userList="userList" :userDataInfo="userDataInfo"
                        @getUserList="focusedUserlList"></details-right>
                </div>
            </div>
        </dg-scrollbar>
    </div>
</template>

<script>
import { commonMixins } from "@/api/commonMethods/common-mixins";
import { servicesMixins } from "../../service-mixins/service-mixins";
import NoData from "@/components/no-data";
import modelShare from "@/projects/DataCenter/views/model-market/modelMarket/components/modelShare";
import modelEvaluation from "@/projects/DataCenter/views/model-market/modelMarket/components/modelEvaluation";
import detailsRight from "@/projects/DataCenter/views/model-market/modelMarket/modelDetails/detailsRight"
import detailsLeftTop from "@/projects/DataCenter/views/model-market/modelMarket/modelDetails/detailsLeftTop"
export default {
    name: "modelDetails",
    mixins: [commonMixins, servicesMixins],
    components: {
        NoData,
        modelEvaluation,
        modelShare,
        detailsRight,
        detailsLeftTop
    },
    data() {
        return {
            isBase64: /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,
            activeName: "modelShare",
            tabPanes: [
                { label: "模型分享", value: "modelShare" },
                { label: "累计评价", value: "modelEvaluation" },
            ],
            modelInfo: {
                modelName: '',
                desc: '',
                label: [],
                otherInfo: {
                    rate: { num: 0, title: '评分', color: '#FF9D02', type: 'rate', numClass: 'num-rate' },
                    focus: { num: 0, title: '已关注', icon: 'icon-follow-b', type: 'icon' },
                    browse: { num: 0, title: '已浏览', icon: 'icon-browse', type: 'icon' },
                    active: { num: 0, title: '活跃度', icon: 'icon-gesture', type: 'icon' },
                },
                type: '',
                logo: '',
                focusType: '',
                transId: ''
            },
            modelDataInfo: {},
            otherModel: [],
            userList: [],
            userDataInfo: {
                dataList: [],
                totalCount: 0
            },
            modelEvaluationInfo: {
                data: [],
                totalCount: 0,
            },
            userFocusVisible: false,
        }
    },
    methods: {
        setLabel(label, value) {
            return value === 'modelEvaluation' ? label + '(' + this.modelEvaluationInfo.data.length + ')' : label
        },
        getModelInfo(id, addView) {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            settings.loading = true;
            let data = {
                id: id,
                addView: addView //true是增加，false是不增加
            }
            services.viewMarkModelDetail(data, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    result.avgScore = result.avgScore ? parseFloat(result.avgScore.toFixed(2)) : 0;

                    vm.modelDataInfo = result;
                    vm.modelInfo.modelName = result.transName;
                    vm.modelInfo.logo = result.logo;
                    vm.modelInfo.transId = result.transId;
                    vm.modelInfo.focusType = result.focusType;
                    vm.modelInfo.type = result.type;
                    vm.modelInfo.desc = result.introduction;
                    vm.modelInfo.label = result.labels ? result.labels.map(n => n.labelName) : [];
                    vm.modelInfo.otherInfo.rate.num = result.avgScore;
                    vm.modelInfo.otherInfo.rate.tip = result.scoreLevel;
                    vm.modelInfo.otherInfo.focus.num = result.focusCount;
                    vm.modelInfo.otherInfo.browse.num = addView ? parseFloat(result.browseCount) + 1 : result.browseCount;
                    vm.modelInfo.otherInfo.active.num = result.browseLevel;

                    vm.sameTypeModelList();
                    vm.focusedUserlList(1, 6);
                    vm.queryModelEvaluationList();
                }
            })
        },
        //同类推荐查询-查询前5条
        sameTypeModelList() {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                type: vm.modelDataInfo.type,
                pageNum: 1,
                pageSize: 5,
                objTypeCode: vm.modelDataInfo.objectTypeCode,
                appTypeCode: vm.modelDataInfo.applicationTypeCode,
                polTypeCode: vm.modelDataInfo.policeTypeCode,
                id: vm.modelDataInfo.id
            }
            services.sameTypeModelList(data).then(res => {
                if (res.data.status === 0) {
                    vm.otherModel = res.data.data.dataList;
                }
            })
        },
        //已关注用户列表
        focusedUserlList(num, pageSize, type) {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                pageNum: num,
                pageSize: pageSize,
                id: vm.modelDataInfo.id
            }
            services.focusedUserlList(data).then(res => {
                if (res.data.status === 0) {
                    if (type === 'dialog') {
                        vm.userDataInfo = {
                            dataList: res.data.data.dataList,
                            totalCount: res.data.data.totalCount
                        }
                    }
                    else {
                        vm.userList = res.data.data.dataList;
                    }
                }
            })
        },

        //查询累计评价列表
        queryModelEvaluationList(num, size) {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                pageNum: num ? num : 1,
                pageSize: size ? size : 5,
                id: vm.modelDataInfo.id
            }
            services.queryModelEvaluationList(data).then(res => {
                if (res.data.status === 0) {
                    vm.modelEvaluationInfo = {
                        data: res.data.data.dataList,
                        totalCount: res.data.data.totalCount
                    };
                }
            })
        },
        //评价模型
        evaluationModel(form) {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                score: form.score,
                operateContent: form.content,
                id: form.id
            }
            services.evaluationModel(data).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success('评价成功！');
                    this.$refs.tableList.closeEvaluation();
                    vm.$nextTick(() => {
                        vm.getModelInfo(form.id, false);
                    })
                }
            })
        },
        //获取评价信息
        getEvaluationInfo() {
            const vm = this, { manageServices, manageMock } = this;
            let services = vm.getServices(manageServices, manageMock);
            services.modelEvaluationDetail({ id: vm.modelDataInfo.id }).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    let data = {
                        id: res.data.data.id,
                        content: '',
                        score: ''
                    }
                    if (result) {
                        data.content = result.operateContent;
                        data.score = result.score;
                    }
                    this.$refs.tableList.toEvaluation(data)
                }
            })
        },
        tabClick() {

        },
        //关注或取消关注
        focusClick() {
            const vm = this, { manageServices, manageMock, settings } = this;
            let services = vm.getServices(manageServices, manageMock);
            let data = {
                focusType: vm.modelDataInfo.focusType === '0' ? '1' : '0',
                id: vm.modelDataInfo.id
            }
            services.focusOrUnFocusModel(data).then(res => {
                if (res.data.status === 0) {
                    vm.modelDataInfo.focusType = data.focusType;
                    vm.modelInfo.focusType = data.focusType;
                    vm.getModelInfo(vm.$route.query.id, false);
                }
            })
        },
        init() {
            if (this.$route.query.id) {
                this.getModelInfo(this.$route.query.id, true)
            }
        }
    },
    created() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.market-details {
    height: 100%;
    overflow: hidden;

    .list-card_color {
        fill: rgba(0, 136, 255, 0.8);
    }

    &_body {
        display: flex;
        height: calc(100vh - 120px);
        box-sizing: border-box;

        &_left {
            flex: 1;
            margin-right: 14px;
            border-radius: 2px;
            height: 100%;

            &_top {
                background: #fff;
                padding: 0 25px;
                margin-bottom: 14px;

            }

            &_content {
                background: #fff;
                padding: 0 15px 30px 15px;
            }
        }

        &_right {
            width: 20rem;
            padding-right: 10px;
        }

        &_box {
            background: #fff;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;
            align-content: flex-start;
            padding: 0 21px 14px;

            .list-card {
                width: calc(33.3% - 16px);
                margin: 7px;
            }
        }
    }

    .list_cont {
        padding: 0 20px;
    }
}

.overLineClamp2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.overLineClamp1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.grid-content {
    border-radius: 2px;
    background: rgba(0, 136, 255, 0.04);
    height: 82px;
    line-height: 82px;
    padding: 0 21px;
    display: flex;
    justify-content: space-between;
}

.ce-share_tab {

    &.el-tabs--top /deep/ .el-tabs__item.is-bottom:nth-child(2),
    &.el-tabs--top /deep/ .el-tabs__item.is-top:nth-child(2) {
        padding-left: 30px;
    }

    &.el-tabs--top /deep/ .el-tabs__item {
        line-height: 56px;
        height: 56px;
    }
}
</style>
