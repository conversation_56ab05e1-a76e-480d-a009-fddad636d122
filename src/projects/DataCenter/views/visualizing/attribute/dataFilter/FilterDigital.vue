<template>
    <div class="FilterDigital">
        <el-form class="ce-form__m0" size="small" label-position="right" :label-width="showLabel ? '80px' : '0'">
            <el-form-item :label="showLabel ? label+' :' : ''" >
                <div class="ovh">
                    <div class="ce-col" v-if="type === 'interval_digital'" :class="{'ce-interval' : type === 'interval_digital'}" >
                        <el-input style="vertical-align: middle" size="small"
                                  clearable
                                  @change="changeToNum(value1 , 'value1')"
                                  v-model.trim="value1">
                            <el-select class="digital_select" v-model="log1" slot="append" @change="changeFilter">
                                <el-option v-for="opt in logicalOpt1"
                                           :key="opt.value"
                                           :value="opt.value"
                                           :label="opt.label"
                                           :title="opt.title"
                                ></el-option>
                            </el-select>
                        </el-input>
                    </div>
                    <div class="ce-col lh32" v-if="type === 'interval_digital'">值</div>
                    <div class="ce-col"  :class="{'ce-interval' : type === 'interval_digital'}">
                        <el-input  style="vertical-align: middle" size="small"
                                   clearable
                                   @change="changeToNum(value , 'value')"
                                   v-model.trim="value">
                            <el-select class="digital_select" v-model="log" slot="prepend" @change="changeFilter">
                                <el-option v-for="opt in logicalOpt"
                                           :key="opt.value"
                                           :value="opt.value"
                                           :label="opt.label"
                                           :title="opt.title"
                                ></el-option>
                            </el-select>
                        </el-input>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "FilterDigital" ,
        props :{
            label :{
                type :String ,
                default: ''
            } ,
            data : Object ,
            type : String ,
            showLabel : {
                type : Boolean ,
                default : true
            }
        },
        watch : {
            type(val) {
                this.value = this.data.leftV.value;
                this.value1 = this.data.rightV.value;
                this.log = this.data.leftV.logical;
                this.log1 = this.data.rightV.logical;
            }
        },
        data(){
            return {
                value : this.data.leftV.value ,//值没有分大小，只是命名
                value1 : this.data.rightV.value ,
                log : this.data.leftV.logical ,
                log1 : this.data.rightV.logical ,
                logicalOpt : [
                    {
                        label: '=',
                        value: '=' ,
                        title :'等于'
                    }, {
                        label: '<',
                        value: '<',
                        title :'小于'
                    }, {
                        label: '>',
                        value: '>',
                        title :'大于'
                    }, {
                        label: '≤',
                        value: '<=',
                        title :'小于等于'
                    }, {
                        label: '≥',
                        value: '>=',
                        title :'大于等于'
                    }, {
                        label: '≠',
                        value: '!=',
                        title :'不等于'
                    }
                ],
                logicalOpt1 :[
                    {
                        label: '=',
                        value: '=' ,
                        title :'等于'
                    }, {
                        label: '<',
                        value: '>',
                        title :'大于'
                    }, {
                        label: '>',
                        value: '<',
                        title :'小于'
                    }, {
                        label: '≤',
                        value: '>=',
                        title :'大于等于'
                    }, {
                        label: '≥',
                        value: '<=',
                        title :'小于等于'
                    }, {
                        label: '≠',
                        value: '!=',
                        title :'不等于'
                    }
                ]
            }
        },
        methods : {
            typeCheck(val , target) {
                let reg = /^(-?\d+)(\.\d+)?$/;
                if (!reg.test(val)) {
                    this.$message.warning('请输入数字');
                }
            },
            changeToNum(val, target) {
                this.typeCheck(val,target);
                if(val !== "") this[target] = isNaN( parseFloat(val)) ? 0 :  parseFloat(parseFloat(val)) ;

                this.changeFilter();
            },
            changeFilter(){
                this.$emit('changeFilter' , this.value , this.log ,this.value1 , this.log1);
            }
        }
    }
</script>

<style scoped>
    .ce-col {
        float: left;
        box-sizing:border-box;
        padding: 0 5px;
    }
    .ce-interval {
        width: 45%;
    }
    .el-form-item .el-select.digital_select {
        width: 60px;
    }
</style>
