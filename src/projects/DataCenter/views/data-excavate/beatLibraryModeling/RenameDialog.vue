<template>
    <div>
        <el-form :model="form" size="mini">
            <el-form-item label="中文名称:" label-width="80px">
                <el-input v-model="form.renameValue"></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "RenameDialog",
        props:{
            rowData:Object
        },
        data(){
            return{
                form:{
                    renameValue:"",
                }
            }
        },
        methods:{
            getNewName(){
                return this.form.renameValue;
            },
        },
        created() {
            this.form.renameValue = this.rowData.name;
        }
    }
</script>

<style scoped>

</style>