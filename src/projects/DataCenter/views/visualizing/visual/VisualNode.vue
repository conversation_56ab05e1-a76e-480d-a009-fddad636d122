<template>
    <div class="visualNode" :class="{'ce-node_active':menu_active}">
        <div class="ce-header"> <!--:class="{'ce-header_tab' : isTab}"-->
            <div class="ce-header_title" v-if="!isPreview" :class="{'ce-cursor_move':!isPreview}"><span>{{title}}</span>
            </div>
            <em class="ce-header_icon"
                v-for="(icon , inx ) in header_icon"
                :key="inx"
                v-if="inx === 0"
                :title="icon.label"
                :class="icon.ico"
                @click.stop="icon.fn"
            ></em>
            <el-dropdown class="r" trigger="click" @visible-change="menuShow" @command="menuCommand">
                                <span class="el-dropdown-link" title="更多">
                                    <i class="el-icon-more ce-header_icon"></i>
                                </span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                            v-for="(m_icon , i) in header_icon"
                            :key="i"
                            :command="{m_icon}"
                            :title="m_icon.label"
                            v-if="i >= 1"
                    >
                        <dg-button type="text"
                                   class="icon"
                                   size="medium"
                                   v-html="m_icon.ico"
                                   v-if="m_icon.show(node)"
                        ></dg-button>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
        <div class="ce-contain"
             :class="{'ce-contain_h': !isPreview }"> <!--:class="{'ce-contain_h': !isPreview ?  !isPreview && !isTab : !isPreview }"-->
            <dg-button type="text" class="icon exportBtn" size="medium" v-html="'&#xe639'" @click="exportExcel" v-if="isPreview && node.code === 'TableChartWidget'" title="导出Excel"></dg-button>
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default {
        name: "VisualNode",
        props: {
            label: String,
            id: String,
            node: Object,
            isView: Boolean,
            isPreview: Boolean,
            isTab: {
                type: Boolean,
                default: false
            }
        },
      computed: {
          title (){
            return this.label;
          }
      },
        data() {
            return {
                header_icon: [
                    {
                        label: '删除',
                        ico: 'el-icon-close',
                        fn: this.deleteNode,
                        show: () => true
                    },
                    {
                        label: '编辑',
                        ico: '&#xe6c0;',
                        fn: this.editPanel,
                        show: () => true
                    }, {
                        label: '复制',
                        ico: '&#xe6f4;',
                        fn: this.copyPanel,
                        show: (node) =>  node.code !== "TabWidget"
                    }, {
                        label: "查看SQL",
                        ico: "sql",
                        fn: this.getSql,
                        show: (node) => node.code !== "TextWidget" && node.code !== "SelectWidget" && node.code !== "TabWidget"
                    },
                    {
                        label: "导出Excel",
                        ico: "&#xe639",
                        fn: this.exportExcel,
                        show: (node) => node.code === "TableChartWidget"
                    }
                ],
                menu_active: false
            }
        },
        methods: {
            exportExcel(){
                this.$emit('exportExcel', this.id, this.node);
            },
            getSql() {
                this.$emit('getSql', this.id, this.node);
            },
            copyPanel() {
                this.$emit('copyPanel', this.id, this.node);
            },
            menuCommand(command) {
                command.m_icon.fn();
            },
            deleteNode() {
                this.$emit('deleteNode', this.node);
            },
            editPanel() {
                this.$emit('editNode', this.id, this.node);
            },
            menuShow(val) {
                this.menu_active = val;
            }
        }
    }
</script>

<style scoped lang="less">
    .visualNode {
        height: 100%;
        /*<!--border : 2px solid @bg-color;-->*/
        /*background: #fff;*/
        //border-radius: 4px;
        position: relative;
    }

    .visualNode_hover:hover, .ce-node_active {
        box-shadow: #66a6e0 0 0 12px 0;
    }

    .visualNode_hover:hover > .ce-header, .ce-node_active > .ce-header {
        background: @bg-color;
    }

    .visualNode_hover:hover > .ce-header .ce-header_title, .ce-node_active > .ce-header .ce-header_title {
        color: #fff;
    }

    .visualNode_hover:hover > .ce-header .ce-header_icon, .ce-node_active >.ce-header .ce-header_icon {
        display: block;
    }

    .ce-header {
        background: #fff;
        overflow: hidden;
        line-height: 30px;
        border-radius: 4px 4px 0 0;
    }

    .ce-header_tab {
        width: 100%;
        position: absolute;
        top: -30px;
        display: none;
        z-index: 2;
    }

    .visualNode_hover:hover > .ce-header_tab, .ce-node_active > .ce-header_tab {
        top: 0;
        display: block;
    }

    .ce-header_title {
        height: 30px;
        float: left;
        color: @bg-color;
        padding-left: 10px;
        font-size: 14px;
        width: calc(100% - 72px);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .ce-cursor_move {
        cursor: move;
    }

    .ce-header_icon {
        float: right;
        padding: 0 5px;
        font-size: 16px;
        line-height: 30px;
        color: #fff;
        cursor: pointer;
        position: relative;
        z-index: 1;
        display: none;
    }

    .ce-contain {
        position: relative;
        z-index: 0;
        height: 100%;

        .exportBtn {
            position: absolute;
            right: 10px;
        }
    }

    .ce-contain_h {
        height: calc(100% - 30px);
    }
</style>
