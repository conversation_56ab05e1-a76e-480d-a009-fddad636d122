<template>
    <div class="roles-manage ce-manage_list" v-loading="settings.loading">
        <div class="roles-header">
            <div class="ce-table_btns">
                <dg-button type="primary" v-if='rights.indexOf(addGroupRight) > -1' @click="addUserGroup">添加</dg-button>
            </div>
            <div class="ce-table__search">
                <el-input
                        size="mini"
                        placeholder="请输入用户组ID/名称搜索"
                        v-model.trim="searchVal"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'searchVal')"
                        @keyup.enter.native="isMock ? searchTableData() : changePage(1)"
                >
                    <i class="el-icon-search el-input__icon poi" slot="suffix"
                       @click="isMock ? searchTableData() : changePage(1)"></i>
                </el-input>
            </div>
        </div>
        <div class="roles-content">
            <common-table
                    :data="tableData"
                    :columns="tableHead"
                    :max-height="tableBodyH"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    :border="false"
                    noneImg
                    class="width100 ce-table-h__auto"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                 <template slot="operate" slot-scope="{row}">
                        <span class="r-c-action" v-for="(opt , inx) in operator" :key="inx"
                              v-if="opt.condition(row , rights)">
                            <dg-button type="text" v-html="opt.label" :title="opt.label"
                                       @click="opt.clickFn(row)"></dg-button>
                        </span>
                    </template>
            </common-table>
        </div>
        <!-- 编辑功能弹窗begin -->
        <UserFormPop
                :title="userForm.title"
                @save="saveUser"
                @update="updateUser"
                width="700px"
                :functionCode="userForm.functionCode"
                :label="userForm.label"
                :state="userForm.state"
                :form-data="userFormData"
                :visible.sync="userForm.visible"
                v-loading="userForm.loading"
                :close-on-click-modal="false"
        ></UserFormPop>
        <!-- 编辑功能弹窗begin -->
    </div>
</template>
<script lang="js" src="./user-group-manage.js"></script>
<style lang="less" scoped src="../roles-manage/roles-manage.less"></style>
