<template>
    <el-form ref="form" class="baseInfo" label-position="right" label-width="160px" :model="baseInfo">
        <el-form-item v-for="(item , key) in formList"
                      :key="key"
                      :prop="key"
                      :rules="item.rules"
                      :label="item.label+':'"
        >
            <template v-if="item.limit">
                <el-input :type="item.type"
                          v-model="baseInfo[key]"
                          :maxlength="item.maxLength"
                          :placeholder="item.placeholder"
                          v-input-limit:[item.limit]
                ></el-input>
            </template>
            <el-input v-else :type="item.type"
                      v-model="baseInfo[key]"
                      :maxlength="item.maxLength"
                      :placeholder="item.placeholder"
                      resize="none"
                      rows="5"
                      show-word-limit
            ></el-input>
        </el-form-item>
    </el-form>
</template>

<script>
export default {
    name: "baseInfo" ,
    data(){
        return {
            baseInfo: {
                name : "" ,
                code : "" ,
                description : ""
            },
            formList : {
                name : {
                    label : "业务板块名称" ,
                    type : "text" ,
                    limit : "fieldName" ,
                    maxLength : 30 ,
                    placeholder : "请输入业务板块名称" ,
                    rules : [
                        {required : true , message : "请输入业务板块名称" ,trigger : ["change" ,"blur"]}
                    ]
                },
                code : {
                    label : "业务板块英文名称" ,
                    type : "text" ,
                    placeholder : "请输入业务板块英文名称" ,
                    limit : "fieldCode" ,
                    maxLength : 30,
                    rules : [
                        {required : true , message : "请输入业务板块英文名称" ,trigger : ["change" ,"blur"]}
                    ]
                },
                description : {
                    label : "业务描述" ,
                    type : "textarea" ,
                    placeholder: "请输入业务描述信息" ,
                    maxLength : 300,
                    rules :[]
                }
            }
        }
    },
    methods : {
        validate(...arg){
            this.$refs.form.validate(...arg);
        }
    }
}
</script>

<style scoped>
    .baseInfo {
        width: 80%;
    }
</style>
