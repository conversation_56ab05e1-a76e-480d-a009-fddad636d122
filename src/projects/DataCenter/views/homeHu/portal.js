import {servicesMixins} from "./service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi_3/index.vue"
import Tree from "./tree/index.vue"
import List from "./list/index.vue"
import EditPanel from "./edit-panel"
import treeCard from "@/components/layout/tree-card"
export default {
    name: "portal",
    mixins: [servicesMixins, commonMixins] ,
    components : {
        TreeAndList,
        Tree,
        List ,
        EditPanel,
        treeCard
    },
    data(){
        return {
            showEditPanel: false ,
            datasetId : "",
            cardName : "门户列表",
            isCase  :  false,//是否场景案例跳转
            listLoad: false,// 列表是否在加载
        }
    },
    methods : {
        /**
         * 更新列表加载状态
         * @param val
         */
        renewListLoad(val){
            this.listLoad = val;
        },
        //新建目录树
        showAddDialog(data){
            data = data ? data : this.$refs.tree.curData
            this.$refs.treeDir.show(data);
        },
        addTreeDir(name, node){
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            services.addOrUpdate(name , "" , node.id ).then(res => {
                if (res.data.status === 0) {
                    let data = res.data.data;
                    vm.successAddTip(name);
                    let newTreeNode = {
                        children: [],
                        id: data.id,
                        label: data.name,
                        isParent: false,
                        pId: node.id,
                    };
                    node.children.unshift(newTreeNode);
                    vm.$refs.treeDir.stop();
                    vm.$refs.tree.initTree();
                }
            })
        },
        /**
         * 树点击事件
         * @param name
         * @param id
         * @param node
         */
        nodeClick(name , id , node) {
            this.$refs.treeDir.nodeClick(name , id , node);
        },
        filterTree(val){
            this.$refs.tree.setFilterText(val);
        },
        closeEditPanel( filterTxt ,groupId) {
            this.showEditPanel = false;
            this.$refs.portal_table.getData(groupId,filterTxt);
        },
        getTableData(treeNode){
            this.$refs.portal_table.initTableData(treeNode);
        },
        openEditPanel( filterText , groupId , row , nodeId ) {
            this.showEditPanel = true;
            this.$nextTick(()=>{
                this.$refs.protal_panel.panelInit( filterText , groupId ,row , nodeId);
            })
        },
        issue(row){
            this.$refs.portal_table.release(row );
        },
        changeNodeKey(id){
            this.$refs.tree.setCheckedNode(id);
        },
        init(){
            const vm = this;
            let {datasetId , rowList} = this.$route.params;
            if(datasetId){
                this.datasetId = datasetId;
                let name = rowList.name || rowList.code;
                vm.$nextTick(()=>{
                    vm.$refs.portal_table.addVisualPanel(name , datasetId);
                })

            }
        }
    },
    created(){
        this.init();
        // const vm = this;
        // let {node} = this.$route.params;
        // if (node) {
        //     node.id = node.caseId;
        //     vm.isCase = true;
        //     vm.$nextTick(()=>{
        //         this.$refs.portal_table.editPanel(node);
        //     })
        // }
    }
}
