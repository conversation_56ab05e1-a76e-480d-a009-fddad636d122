.chartCard {
  width: 100%;
  height: 100%;
}
.ce-data-none {
  text-align: center;
  vertical-align: middle;
  height: calc(100% - 30px);
  box-sizing: border-box;
  color: #888;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tableCont {
  padding:0 10px;
  overflow: auto;
  user-select: text;
}
.listTitle {
  padding:0 10px;
  font-size: 16px;
  font-weight: bold;

  color: #666;
  text-align: center;
}
.secTitle {
  padding:0 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
  white-space: pre-wrap;
}
.chartTip {
  position: absolute;
  left: 0;
  top: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  padding:5px 0 5px 6px;
  width: calc(100% - 6px);
}
