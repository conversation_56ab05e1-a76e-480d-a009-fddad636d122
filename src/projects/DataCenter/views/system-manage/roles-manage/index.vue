<template>
    <div class="roles-manage ce-manage_list" v-loading="settings.loading">
        <div class="roles-header">
            <div class="ce-table_btns">
                <dg-button type="primary" v-if=" rights.indexOf(addRoleRight) > -1" @click="addRole">添加
                </dg-button>
            </div>
            <div class="ce-table__search">
                <el-input
                        size="mini"
                        placeholder="请输入角色ID/名称搜索"
                        v-model.trim="searchVal"
                        v-input-limit:trim
                        @input="inputFilterSpecial($event , 'searchVal')"
                        @keyup.enter.native="isMock ? searchTableData() : changePage(1)"
                >
                    <i class="el-icon-search el-input__icon poi"
                       slot="suffix" @click="isMock ? searchTableData() : changePage(1)"></i>
                </el-input>
            </div>

        </div>
        <div class="roles-content">
            <common-table
                    :data="tableData"
                    :columns="tableHead"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100 ce-table-h__auto"
                    :max-height="tableBodyH"
                    :border="false"
                    noneImg
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
                 <template slot="updatetime" slot-scope="{row}">{{ row.updateTime | formatDate }}</template>
                 <template slot="operate" slot-scope="{row}">
                        <span class="r-c-action" v-for="(opt , inx) in operator" :key="inx"
                              v-if="opt.condition(row , rights)">
                            <dg-button type="text"  :title="opt.label" v-html="opt.label"
                                          @click="opt.clickFn(row)">{{opt.label}}</dg-button>
                        </span>
                    </template>
            </common-table>
        </div>
        <!-- 添加角色弹窗begin -->
        <RoleFormPop
                title="角色权限设置"
                width="982px"
                ref="roleFormPop"
                :popTableData="popTableData"
                @save="saveRole"
                :showPrev="showPrev"
                :isEdited="isEdited"
                :selectedRight="selectedRight"
                :form-data="roleFormData"
                @prev="prevRole"
                :visible.sync="addVisible"
                v-loading="roleForm.loading"
                :close-on-click-modal="false"
        ></RoleFormPop>
        <!-- 添加角色弹窗end -->
        <!-- 编辑功能弹窗begin -->
        <UserFormPop
                :title="roleForm.title"
                :functionCode="roleForm.functionCode"
                @save="saveUser"
                @update="updateRole"
                :label="roleForm.label"
                width="700px"
                :state="roleForm.state"
                :form-data="roleFormData"
                :visible.sync="roleForm.visible"
                v-loading="roleForm.loading"
                :close-on-click-modal="false"
        ></UserFormPop>
        <!-- 编辑功能弹窗begin -->
    </div>
</template>

<script lang="js" src="./roles-manage.js"></script>
<style lang="less" src="./roles-manage.less"></style>
