<template>
    <div class="tree">
        <div class="d-s-l-top">
            <el-input
                    size="mini"
                    placeholder="请输入名称搜索"
                    suffix-icon="el-icon-search"
                    v-model.trim="filterText"
            ></el-input>
        </div>
        <div class="d-s-l-bottom">
            <div class="d-s-l-b-tree tree_cont" v-loading="settings.loading">
                <dg-tree
                        :data="data"
                        node-key="id"
                        :expand-on-click-node="false"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        :default-expand-all="true"
                        ref="tree"
                        :highlight-current="true"
                        @node-click="nodeClick"
                >
                    <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }" @mouseenter="rightMenu($event , node , data)">
                        <span class="node-label"
                              :title="data.name"
                              :class="{
                            'el-icon-folder' : !node.expanded && ( data.children.length || !notFirst ) ,
                            'el-icon-folder-opened' : node.expanded && (data.children.length || !notFirst),
                            'el-icon-document' : !data.isParent && notFirst
                          }"
                        >{{ data.name }}</span>

                        <span class="label__action" v-if="data.id !== '0'">
                            <dg-button type="text" :title="addBtnTxt" class="el-icon-plus b" @click.stop="addChildren($event , node , data)" v-if="data.name === '我的' && data.pId === '0' || data.name === '标准模型' "></dg-button>
                            <el-dropdown trigger="click" @command="menuCommand($event , node , data)" v-else-if="countHasRightMenu > 0">
                                <div class="el-dropdown-link" @click.stop>
                                    <i class="el-icon-more el-dropdown-link-rote"></i>
                                </div>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item v-for="(item , i ) in treeMenu" :key="i" :command="item" v-if="item.show(rights)" >
                                        <span class="drop-item">{{item.name}}</span>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </span>
                    </span>
                </dg-tree>
            </div>
        </div>
    </div>
</template>

<script src="./tree.js"></script>
<style scoped lang="less" src="./tree.less"></style>