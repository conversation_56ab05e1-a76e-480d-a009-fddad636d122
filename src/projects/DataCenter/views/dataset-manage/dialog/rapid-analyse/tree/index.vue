<template>
    <div class="d-s-c-left tree">
        <div class="d-s-l-top" ref="other">
            <el-input
                    size="mini"
                    placeholder="请输入名称搜索"
                    suffix-icon="el-icon-search"
                    v-model="filterText"
            ></el-input>
        </div>
        <div class="d-s-l-bottom" v-loading="settings.loading || !reNew">
            <div class="d-s-l-b-tree tree_cont">
                <dg-tree
                        v-if="reNew"
                        :data="data"
                        node-key="id"
                        :expand-on-click-node="false"
                        :default-expand-all="true"
                        :filter-node-method="filterNode"
                        ref="tree"
                        :highlight-current="true"
                >
                     <span class="custom-tree-node el-tree-node__label"  slot-scope="{ node, data }" @click="rightMenu($event , node , data)">
                        <span class="node-label"
                              :class="{
                                        'el-icon-tickets' : data.format === 'TEXT' ,
                                        'el-icon-data-analysis' : data.format === 'NUMBER' ,
                                        'el-icon-date' : data.format !== undefined && data.format !== 'TEXT' && data.format !== 'NUMBER'

                                    }"
                              :title="data.label">{{ data.label }}</span>

                        <span class="label__action">
                            <dg-button type="text" :title="data.code === 'dimension' ? addBtnTxt[0] : addBtnTxt[1]" class="el-icon-plus b"
                                       @click.stop="addColumn($event , data.code , data)" v-if="data.code === 'dimension' || data.code === 'measure'"></dg-button>
                            <el-popover
                                    v-if="data.indexType === 'MEASURE' || data.indexType === 'DIMENSION'"
                                    popper-class="ce-popover_notp"
                                    width="200"
                                    trigger="click"
                            >
                                <DropdownOpt :data="treeMenu" @changeVal="changeVal" @open="openFn" :value="activeTab" />
                                <i class="el-icon-more el-dropdown-link-rote" slot="reference" @mouseenter="hideMenu"></i>
                            </el-popover>
                        </span>
                    </span>
                </dg-tree>
            </div>
        </div>
        <tree-edit ref="treeEdit" :datasetId="datasetId" @reNewColumn="getTree" @setColumn="setColumn"></tree-edit>
        <add-column :datasetId="datasetId" ref="addColumn" @reNewColumn="getTree" @appendColumns="appendColumns"></add-column>
    </div>
</template>

<script src="./tree.js"></script>
<style scoped lang="less" src="./tree.less"></style>