<template>
    <div class="height100">
        <api-test-page ref="param" :row="row" @loadChange="loadChange" :pageId="row.id || row.servicePublicationId"/>
        <dg-button v-footer @click="close">{{ btnCancelTxt }}</dg-button>
        <dg-button v-footer type="primary" :disabled="submitDisable" @click="submit">{{ btnCheckTxt }}</dg-button>
    </div>
</template>

<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import ApiTestPage from "@/components/common/api-test-page/ApiTestPage";

import {common} from "dc-front-plugin/src/api/commonMethods/common";

export default {
    name: 'params',
    props: {
        row: Object,
    },
    components: {
        ApiTestPage,
    },
    mixins: [commonMixins, common],
    data() {
        return {
            submitDisable: true,
        };
    },
    methods: {
        loadChange(data) {
            this.submitDisable = data.loading;
        },
        close() {
            this.$emit("close");
        },
        submit() {
            const vm = this;
            if (this.$refs.param.$refs.params.tableValidate(true)) {
                this.$refs.param.$refs.params.setTestVo();//更新参数数据
                this.$emit('submit', this.$refs.param , this.$refs.param.$refs.params,);
                vm.close();
            }
        },
    },
    mounted() {

    }
};
</script>
