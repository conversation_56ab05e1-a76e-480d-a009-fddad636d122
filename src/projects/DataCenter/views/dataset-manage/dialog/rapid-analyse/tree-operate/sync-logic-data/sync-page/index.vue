<template>
    <el-form class="syncPage" label-position="right" label-width="130px">
        <el-form-item v-for="(item , i) in form_data" :key="i">
            <div slot="label" class="ce-form_label" :title="item.label">
                <span class="ce-label_name">{{item.label}}</span>
                <span class="ce-label_sym">:</span>
            </div>
            <dg-select v-model="form[i]" :disabled="disabled" :data="item.data" multiple></dg-select>
        </el-form-item>
    </el-form>
</template>

<script src="./sync-page.js"></script>
<style scoped lang="less" src="./sync-page.less"></style>