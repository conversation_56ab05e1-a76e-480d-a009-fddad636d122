import area from "@/assets/data/area.json"

export const attrStyle = {
    data() {
        return {}
    },
    methods: {
        transFormArea(code, array) {
            let mapArea = [];
            let areaJson = area[code];
            const vm = this;
            for (let i in areaJson) {
                let node = {
                    label: areaJson[i],
                    value: i
                };
                let hideCode = ['710000' , '810000' ,'820000' ,'990000', '0,710000,710000' , '0,810000,810000' , '0,820000,820000' , '0,110000' ,"0,120000" ,"0,310000","0,500000"];
                if (hideCode.indexOf(i) > -1) continue;
                let key = code + ',' + i;
                if (area[key] !== [] && area[key] !== undefined && key.split(',').length <= 2 && hideCode.indexOf(key) === -1 ) { //台湾，香港 ，澳门
                    node.children = vm.transFormArea(key);
                } else {
                    node.children = null;
                }
                mapArea.push(node);
            }
            return mapArea;
        },
        styleRenew(val , key){
            const vm = this;
            vm.emitStyle[key] = val;
            vm.$emit('nodeDataRenew', this.node.id);
        },
        setAxisTxt(node, key){
            let names = [];
            const vm = this;
            if(node && key){
                node.forEach(no => {
                    let name = no.alias || no.label;
                    names.push(name);
                });
                let axis = key === 'xAxis' ? 'yAxis' : 'xAxis';
                if(vm.styleList[axis]){
                    let axis_list;
                    if(vm.emitStyle[axis]){
                        axis_list = JSON.parse(JSON.stringify(vm.emitStyle[axis]));
                    }else {
                        axis_list = JSON.parse(JSON.stringify(vm.styleList[axis].form));
                    }
                    axis_list.text = names.join(' > ');
                    vm.styleList[axis].form = axis_list;
                }
            }
        },
        setAxisName(node, key, lists) {
            const vm = this;
            vm.setAxisTxt(node , key);
            vm.setFields(lists , key);
        },
        /**
         * 更新维度度量字段数据
         * @param data
         * @param key => 组合图需要判断 度量 才更新
         */
        setFields(data , key) {
            const vm = this;
            if(vm.styleList.fieldSetting){
                vm.styleList.fieldSetting.form.alias = "";
                vm.styleList.fieldSetting.form.fields = "";
                const options = vm.getAllFields(data);
                vm.styleList.fieldSetting.form.options = options;
                for(let opt of options ) {
                    vm.styleList.fieldSetting.form[opt.code] = opt.alias || "";
                }
            }
        },
        getAllFields(data) {
            const vm = this;
            let xData = vm.joinField(data , 'xAxis'),
                yData = vm.joinField(data , 'yAxis');
            return [...xData , ...yData];
        },
        joinField(data , axis){
            let fields = [];
            if(data[axis].options){
                data[axis].options.forEach(item => {
                    let field = {...item.data};
                    field.label = item.label;
                    field.value = item.value;
                    field.alias = item.alias;
                    field.fieldId = item.data.id;
                    fields.push(field);
                });
            }
            return fields;
        },
        setPieAxisName(node, key, lists){
            const vm = this;
            if (vm.node && vm.node.code === 'RingPieChartWidget'){
                if(lists.yAxis.value.length === 1){
                    vm.setNormalPieSettings();
                }else {
                    vm.setRingPieSettings();
                }
            }
            vm.setFields(lists);
            if(node && node.length && key){
                if(key === 'yAxis'){
                    let seriesName = vm.styleList.legend.form.seriesName;
                    vm.styleList.legend.form.seriesName = [];
                    node.forEach(no => {
                        if( !seriesName || !Array.isArray(seriesName) ){
                            vm.styleList.legend.form.seriesName = [];
                            vm.styleList.legend.form.seriesName.push(no.label);
                        }else {
                            vm.styleList.legend.form.seriesName.push(no.label);
                        }
                    });
                    vm.emitStyle.legend.seriesName = vm.styleList.legend.form.seriesName;
                }else {
                    vm.styleList.legend.form.measure = node[0].label;
                    vm.emitStyle.legend.measure = node[0].label;
                }
            }
        },
        /**
         * 柱状图折线图默认Legend参数配置
         * 图表切换时 当缺少某个样式配置，补上默认的
         */
        setBarOrLineDefaultLegend(legend){
            const vm = this;
            if(legend && (legend.show_dataZoom === undefined)){
                let de_legend = Object.assign(vm.styleList.legend.form , legend);
                vm.emitStyle.legend = vm.styleList.legend.form = de_legend;
            }else {
                legend ? vm.emitStyle.legend = vm.styleList.legend.form = legend : vm.emitStyle.legend = vm.styleList.legend.form;
            }
        },
        /**
         * 图表切换，由柱状图、折线图 => 饼图 需要补充缺失的样式属性
         */
        setPieDefaultLegend(legend){
            const vm = this;
            if(legend && (legend.label_cont === undefined || legend.radius_i === undefined )){
                let de_legend = Object.assign(vm.styleList.legend.form , legend);
                vm.emitStyle.legend = vm.styleList.legend.form = de_legend;
            }else {
                legend ? vm.emitStyle.legend = vm.styleList.legend.form = legend : vm.emitStyle.legend = vm.styleList.legend.form;
            }
        },
        /**
         * 设置默认Grid
         * @param top
         * @param right
         * @param bottom
         * @param left
         * @param legend
         */
        setDefaultGrid(top , right , bottom , left ,legend){
            legend.top = top;
            legend.right = right;
            legend.bottom = bottom;
            legend.left = left;
        }
    }
};
