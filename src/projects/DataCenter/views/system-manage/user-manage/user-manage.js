import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import $right from "@/assets/data/right-data/right-data"
import secret from "@/api/secret"
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {servicesMixins} from "../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import * as $ from "jquery";
import {coustTableH} from "@/api/commonMethods/count-table-h";
export default {
    name: "user-manage",
    components: {UserFormPop},
    mixins: [commonMixins , servicesMixins ,listMixins,coustTableH],
    filters: {
        state(val) {
            if (val === "0") {
                return "启用";
            } else if(val === "1") {
                return "禁用";
            }
        }
    },
    computed: {
        ...mapGetters(["userInfo" , "userRight"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    },
    data() {
        return {
            //角色管理搜索
            searchVal: "",
            //编辑弹窗控制显示与否
            editVisible: false,
            //角色信息编辑
            userformTitle: "基本信息",
            userformTitle1: "角色分配",
            // 角色管理表格数据
            tableData: [],
            tableHead : [
                {
                    prop: "objCode",
                    label: "登录账号",
                    minWidth: "160",
                    align: "left"
                },
                {
                    prop: "objName",
                    label: "姓名",
                    align: "left"
                },
                {
                    prop: "belong",
                    label: "所属用户组",
                    align: "center"
                },
                {
                    prop: "enableState",
                    label: "状态",
                    align: "center",
                    resizable :false
                },
                {
                    prop: "operate",
                    label: "操作",
                    align: "center",
                    width: 220 ,
                    resizable :false
                },
            ],
            addUserRight : $right["userManagementAddUser"],
            //操作栏
            operator: [
                {
                    label: '编辑',
                    clickFn: this.edit,
                    condition: (row ,right) =>  row.objCode !== this.$userCode &&  right.indexOf($right["userManagementEditUser"]) > -1,
                    icon: "&#xe6ad;"
                }, {
                    label: '详情',
                    clickFn: this.rowDetail,
                    condition: (row ,right) => right.indexOf($right["userManagementQueryUser"]) > -1,
                    icon: "&#xe6c5;"
                }, {
                    label: '删除',
                    clickFn: this.deleteRow,
                    condition: (row , right) => row.objCode !== this.$userCode && right.indexOf($right["userManagementDeleteUser"]) > -1,
                    icon: "&#xe847;"
                }, {
                    label: '重置',
                    clickFn: this.reset,
                    condition: (row ,right) =>  row.objCode !== this.$userCode && right.indexOf($right["userManagementResetPassword"]) > -1,
                    icon: "&#xe6e3;"
                }
            ],
            //分页
            paginationProps: {
                currentPage: 1,
                pageSizes: [10, 20],
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            total: 0,
            statusList: [
                {
                    label: "启用",
                    value: "0"
                },
                {
                    label: "禁用",
                    value: "1"
                }
            ],
            fromAuthList: [],
            defaultProps: {
                value: 'id',
                label: 'name',
                children: 'children'
            },
            account : [
                {required: true, message: "登录账号不能为空", trigger: ["blur" , "change"]},
                { validator : this.checkedCommon , trigger : "blur" , reg : /^[a-zA-Z0-9_]*$/ , message : "请输入字母、数字、下划线" } ,
                {validator: this.checkExistUser , trigger:  "blur" }
            ],
            editAccount : [
                {required: true, message: "登录账号不能为空", trigger: ["blur" , "change"]},
                { validator : this.checkedCommon , trigger : "blur" , reg : /^[a-zA-Z0-9_]*$/ , message : "请输入字母、数字、下划线" } ,
            ],
            rules: {

                name: [
                    {required: true, message: "姓名不能为空", trigger: ["blur","change"]},
                    {
                        validator: this.checkedCommon,
                        trigger: "blur",
                        reg: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
                        message: "请输入中文、字母、数字、下划线"
                    }
                ],
                fromAuth: [
                    {required: true, message: "所属用户组不能为空", trigger: 'change'}
                ],
                email: [
                    {type: 'email', trigger: 'blur', message: "邮箱地址格式错误"}
                ],
                phone: [
                    {
                        validator: this.checkedCommon, trigger: 'blur',
                        reg: /(^0\d{2,3}\-\d{7,8}$)|(^1[3|4|5|6|7|8|9][0-9]{9}$)/,
                        message: "电话号码格式错误"
                    }
                ],
                certificateNumber : [
                    {required: true, message: "身份证号不能为空", trigger: "change"},
                    {
                        validator: this.checkedCommon, trigger: 'blur',
                        reg: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
                        message: "身份证号格式错误"
                    }
                ],
                status: [
                    {required: true, message: "状态不能为空", trigger: "change"}
                ],
                roleVal : [
                    {required:true , message : "角色不能为空" , trigger: "change"}
                ]
            },
            //用户信息新增弹窗form数据
            currentData: {
                id: "",
                account: "",
                name: "",
                fromAuth: "",
                email: "",
                phone: "",
                status: "",
                certificateNumber :"",
                //用户信息新增弹窗复选框绑定值
                roleVal: [],
                password : ""
            },
            //用户信息新增弹窗复选框组
            roleData: [],
            //控制用户信息新增弹窗显隐参数
            userForm: {
                title: "",
                visible: false,
                loading: false,
                state: "",
                functionCode: "",
            },
            showF : false,
            showTip : false
        };
    },
    methods: {
        filterNode : treeMethods.methods.filterNode ,
        clearFormData() {
            this.currentData = {
                id: "",
                account: "",
                name: "",
                fromAuth: "",
                email: "",
                phone: "",
                status: "",
                certificateNumber :"",
                roleVal: [],
                password : ""
            };
            this.showF = false;
        },
        //校验
        validate(...arg) {
            this.$refs.form.validate(...arg);
        },
        reNewData() {
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.form.clearValidate();
                vm.showF = true;
            })

        },
        //获取用户组
        async getUserGroups() {
            const vm = this , {groupServices, groupMock} = this;
            let services = vm.getServices(groupServices, groupMock);
            await services.queryAllUserGroups().then(res => {
                if (res.data.status === 0) {
                    vm.fromAuthList = res.data.data;
                }
            })
        },
        //获取角色
        async getRoles() {
            const vm = this , {rolesServices, rolesMock,$loginWay} = this;
            let services = vm.getServices(rolesServices, rolesMock);
            await services.queryAllRole($loginWay).then(res => {
                if (res.data.status === 0) {
                    vm.roleData = res.data.data.map(r => {
                        r.label = r.objName;
                        r.value = r.id;
                        return r;
                    })
                }
            })
        },
        errorScrollTop(top){
            if (this.$refs.userForm.scroll) {
                this.$refs.userForm.scroll({top , behavior: "smooth"});
            } else {
                $(this.$refs.userForm).animate({'scrollTop': top}, 100);
            }
        },
        /**
         * 校验单个字段
         * @param currentData
         */
        formValidFields(currentData){
            const vm = this;
            let accountCheck = false , nameCheck = false;
            vm.$refs.form.validateField("account", async (valid) => {
                accountCheck = valid === "";
                if (valid !== "") vm.$refs.account.focus();
                /*
                * 存在校验请求 只能等账号校验结束 后
                * */
                vm.$refs.form.validateField("name", valid => {
                    nameCheck = valid === "";
                    if (valid !== "" && accountCheck) vm.$refs.name.focus();
                });
                if(currentData.roleVal.length === 0 && nameCheck && accountCheck){
                    let h = vm.$refs.userForm.clientHeight , f_h = vm.$refs.f_body.clientHeight;
                    vm.errorScrollTop(f_h - h);
                }
            });
        },
        /**
         * 整体校验
         */
        formValid(data , userForm , isNew , services){
            const vm = this;
            vm.validate(valid =>{
                if(valid){
                    userForm.loading = true;
                    if (isNew) {
                        services.addUser(data, userForm).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("添加成功");
                                vm.changePage(1);
                                userForm.visible = false;
                            }
                        })
                    } else {
                        services.editUser(data, userForm).then(res => {
                            if (res.data.status === 0) {
                                vm.$message.success("保存成功");
                                vm.changePage(1);
                                userForm.visible = false;
                            }
                        })
                    }
                }
            })
        },
        //提交添加
        async addUserSubmit(isNew) {
            const vm = this;
            let {currentData, userForm ,userServices, userMock } = this;
            let services = vm.getServices(userServices, userMock);
            let data = {
                id: currentData.id,
                objCode: currentData.account,
                objName: currentData.name,
                userName: currentData.account,
                password: currentData.password,
                roles: vm.setSelectId(currentData.roleVal),
                belongGroup: {id : currentData.fromAuth },
                email: currentData.email,
                phone: currentData.phone,
                enableState: currentData.status,
                editor: vm.userInfo.id,
                certificateNumber : currentData.certificateNumber ,
            };
            vm.setTipShow(false);
            vm.formValidFields(currentData);
            vm.formValid(data , userForm , isNew , services);
        },
        setSelectId(data) {
            let result = [];
            data.forEach(d => {
                if(d){
                    result.push({id: d});
                }
            });
            return result;
        },
        //添加用户
        async addUser() {
            const vm = this;
            let {userForm ,userServices, userMock} = this;
            userForm.title = "用户信息新增";
            userForm.state = "add";
            userForm.visible = true;
            userForm.loading = true;
            userForm.functionCode = "userManagementAddUser";
            await vm.getRoles();
            await vm.getUserGroups();
            let services = vm.getServices(userServices, userMock);
            let password = secret.encrypt("123456");
            services.getUserRandom(userForm).then(res => {
                if (res.data.status === 0) {
                    vm.currentData.account = res.data.data;
                    vm.currentData.status = "0";
                    vm.currentData.fromAuth = vm.fromAuthList && vm.fromAuthList.length ?  vm.fromAuthList[0].id : "";
                    vm.currentData.password = password;

                }
            })
        },
        //详情
        rowDetail(row) {
            this.edit(row, 'detail', '用户信息详情');
        },
        //重置密码
        reset(row) {
            const vm = this , {userServices, userMock} = this;
            let services = vm.getServices(userServices, userMock);
            vm.$dgAlert('确认重置密码吗？', '提示', {
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true
            }).then(() => {
                let new_p = secret.encrypt("123456");
                services.resetPassword(row.id , new_p).then(res=>{
                    if(res.data.status === 0){
                        vm.$message.success("密码已重置");
                        vm.changePage(1);
                    }
                })
            }).catch(_ => {});
        },
        //编辑表格
        //编辑弹窗功能显示与否
        async edit(row, state, title) {
            const vm = this;
            let {userForm, currentData} = this;
            userForm.state = state || 'edit';
            userForm.title = title || "用户信息编辑";
            userForm.functionCode = title ? "userManagementQueryUser" : "userManagementEditUser";
            currentData.id = row.id;
            await vm.getRoles();
            await vm.getUserGroups();

            currentData.account = row.objCode || "";
            currentData.name = row.objName || "";
            currentData.fromAuth = row.belong_id || "";
            currentData.email = row.email || "";
            currentData.phone = row.phone || "";
            currentData.status = row.enableState || "";
            currentData.password = row.password || "";
            currentData.roleVal = [];
            currentData.certificateNumber = row.certificateNumber || "";
            row.roleList.forEach(role => {
                currentData.roleVal.push(role.id);
            });
            userForm.visible = true;

        },
        //删除表格行
        deleteRow(row) {
            const vm = this , {userServices, userMock} = this;
            let services = vm.getServices(userServices, userMock);
            vm.$dgAlert("你确定删除\""+row.objName+"\"？", "提示", {
                type: "warning",
                showCancelButton: true,
                showConfirmButton: true
            }).then(() => {
                services.deleteUser(row.id).then(res => {
                    if (res.data.status === 0) {
                        if(res.data.data === "删除成功"){
                            vm.$message.success(res.data.data);
                            vm.changePage(1);
                        }else if(res.data.data === "此用户有操作纪录不能删除") {
                            vm.$message.warning(`\"${row.objName}\"已存在操作记录，不允许删除`);
                        }
                    }
                })
            }).catch(_ => {
            });
        },
        getBelong(data) {
            const {tableData} = this;
            data.forEach((d, inx) => {
                let objRelSet = d.tSysAuthObjRelSet;
                if (!tableData[inx].roleList) tableData[inx].roleList = [];
                if (objRelSet.length) {
                    objRelSet.forEach(rel => {
                        if (rel.toAuthObj) {
                            if (rel.relationType === "0") {
                                tableData[inx].belong = rel.toAuthObj.objName;
                                tableData[inx].belong_code = rel.toAuthObj.objCode;
                                tableData[inx].belong_id = rel.toAuthObj.id;
                            } else if (rel.relationType === "1") {
                                tableData[inx].roleList.push(
                                    {
                                        objName: rel.toAuthObj.objName,
                                        objCode: rel.toAuthObj.objCode,
                                        id: rel.toAuthObj.id,
                                    }
                                )
                            }
                        }
                    })
                }
            })
        },
        async changePage(index) {
            const vm = this;
            let {settings, searchVal , userServices, userMock} = this;
            settings.loading = true;
            let pageSize = vm.paginationProps.pageSize;
            let services = vm.getServices(userServices, userMock);
            let data = {
                code: searchVal,
                name: searchVal,
                pageNum: index,
                pageSize: pageSize
            };
            await services.queryUserList(data, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data.dataList;
                    vm.tableData = result;
                    vm.total = res.data.data.totalCount;
                    vm.paginationProps.currentPage = res.data.data.pageIndex;
                    vm.getBelong(result);
                }
            })
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        //mock 查询列表数据
        async searchTableData(){
            const vm = this; let searchVal = vm.searchVal.toLowerCase();
            await vm.changePage(1);
            vm.tableData = vm.tableData.filter(tab => {
                return tab.objName.toLowerCase().indexOf(searchVal) > -1 ||  tab.objCode.toLowerCase().indexOf(searchVal) > -1;
            })
        },

        async isExistUser(showTip){
            let vm = this;
            let {currentData , userServices, userMock} = this;
            let services = vm.getServices(userServices, userMock);
            let userCode = currentData.account;
            let errMsg = "";
            await services.isExistUser(userCode).then(res => {
                if (res.data.status === 0) {
                    if(showTip)vm.$message.success("可用此账号");
                }else {
                    errMsg = res.data.msg;
                    if(showTip)vm.$message.error(res.data.msg);
                }
            })
            return errMsg;
        },
        setTipShow(show){
            this.showTip = show;
        },
        async checkExistUser(rule , val , callback){
            let errMsg = await this.isExistUser(this.showTip);
            this.setTipShow(false);
            if(errMsg){
                callback(new Error(errMsg));
            }else {
                callback();
            }
        }
    },
    created() {
        this.changePage(1);
    }
};
