<template>
    <div class="height100">
        <common-dialog
                class="params-dialog"
                title="设置参数"
                width="1000px"
                height="100%"
                :visible.sync="visible"
                @closed="clear_data"
        >
            <div class="setParams">
                <div class="setParams-dataset" v-if="singleOrManyTable === '1'">
                    <div>数据集列表</div>
                    <div class="setParams-dataset_list" v-for="(item,k) in dataSetColumnList"
                         :key="k" :class="{'active' : selectDataSet.id === item.id}" @click="changeSet(item)">
                        {{item.dataSet}}
                    </div>
                </div>
                <div class="setParams-column" :style="{'width': singleOrManyTable === '0'? '100%' :'calc(100% - 200px)'}">
                    <span v-if="singleOrManyTable === '1'">设置参数</span>
                    <el-input placeholder="请输入名称"
                              @keyup.enter.native="columnSearch"
                              v-input-limit:trim v-model.trim="columnCode"
                              size="medium"
                              @click="columnSearch" class="ce-search searchClass">
                        <i slot="suffix" class="el-input__icon el-icon-search" @click="columnSearch"></i>
                    </el-input>
                    <common-table
                            :data="data"
                            :columns="setHeadInfo"
                            :pagination="false"
                            class="width100"
                            :border="false"
                            height="calc(75vh - 230px)"
                            v-if="showParams"
                    >
                        <template slot="setSearchColumn" slot-scope="{row , $index}">
                            <el-checkbox v-model="row.setSearchColumn" @change="handleCheckedChange($event, row)"></el-checkbox>
                        </template>
                        <template slot="header-setSearchColumn"  slot-scope="{row , $index}">
                            <el-checkbox label="设为查询字段"
                                         v-model="checkAll"
                                         :indeterminate="isIndeterminate"
                                         @change="selectAll"
                            ></el-checkbox>
                        </template>
                        <template slot="setResponseColumn" slot-scope="{row , $index}">
                            <el-checkbox v-model="row.setResponseColumn" @change="setResponseColumnHandleCheckedChange($event, row)"></el-checkbox>
                        </template>
                        <template slot="header-setResponseColumn"  slot-scope="{row , $index}">
                            <el-checkbox label="设为返回字段"
                                         v-model="setResponseColumnCheckAll"
                                         :indeterminate="isSetResponseColumnIndeterminate"
                                         @change="setResponseColumnSelectAll"
                            ></el-checkbox>
                        </template>

                        <template slot="setSortColumn" slot-scope="{row , $index}">
                            <el-checkbox v-model="row.setSortColumn" @change="setSortColumnHandleCheckedChange($event, row)"></el-checkbox>
                        </template>
                        <template slot="header-setSortColumn"  slot-scope="{row , $index}">
                            <el-checkbox label="设为排序字段"
                                         v-model="setSortColumnCheckAll"
                                         :indeterminate="isSetSortColumnIndeterminate"
                                         @change="setSortColumnSelectAll"
                            ></el-checkbox>
                        </template>
                    </common-table>
                </div>
            </div>
            <div slot="footer">
                <el-button size="mini" @click="visible = false">取消</el-button>
                <el-button size="mini" @click="save">保存</el-button>
            </div>
        </common-dialog>
    </div>
</template>

<script>
    export default {
        name: "setParams",
        props :{
            columnInfo : Array,
            region: String,
            singleOrManyTable: String,
            responseTableData : Array,
            requestTableData : Array,
            sortTableData : Array,
            dataSetColumnList: Array,
        },
        data(){
            return {
                columnCode : "",//字段搜索
                checkAll: false,
                isIndeterminate: false,
                setResponseColumnCheckAll : false,
                isSetResponseColumnIndeterminate : false,
                isSetSortColumnIndeterminate : false,
                setSortColumnCheckAll:false,
                showParams :false,
                visible :false,
                data : [],
                tHeadData :[
                    {
                        prop: "paramValue",
                        label: "字段名",
                        minWidth: 150,
                    },
                    {
                        prop: "name",
                        label: "字段中文名",
                        minWidth: 100,
                    },
                    {
                        prop: "dataType",
                        label: "字段类型",
                        width: 100,
                    },
                ],
                setSearchColumn:{
                    prop: "setSearchColumn",
                    label: "设为查询字段",
                    minWidth: 100,
                },
                setResponseColumn: {
                    prop: "setResponseColumn",
                    label: "设为返回字段",
                    minWidth: 100,
                },
                setSortColumn: {
                    prop: "setSortColumn",
                    label: "设为排序字段",
                    minWidth: 100,
                },
                requestChooseList : [],//选中的查询字段
                responseChooseList : [], // 选中的返回字段
                sortChooseList : [], // 选中的排序字段
                selectDataSet : {},
            }
        },
        computed:{
            setHeadInfo(){
                const vm = this;
                let head = [...vm.tHeadData];
                if(vm.singleOrManyTable === '0' && vm.region === '4'){
                    head = [...head, vm.setSearchColumn, vm.setResponseColumn, vm.setSortColumn];
                }
                else if(vm.singleOrManyTable === '1' && vm.region === '4'){
                    head = [...head, vm.setResponseColumn, vm.setSortColumn];
                }
                else if(this.singleOrManyTable === '0' && vm.region === '6'){
                    head = [...head, vm.setSearchColumn];
                }
                return head;
            },
        },
        methods : {
            /**
             * 字段搜索
             */
            columnSearch(){
                let info = this.singleOrManyTable === '0' ? this.columnInfoCopy : this.selectDataSet.column;
                this.data = info.filter(element=>{
                    return element.code && element.code.toLowerCase().indexOf(this.columnCode.toLowerCase()) > -1 ||
                        element.name && element.name.toLowerCase().indexOf(this.columnCode.toLowerCase()) > -1;
                })
                this.setCheckState();
            },
            selectAll(value) {
                this.data.forEach(item => {
                    item.setSearchColumn = value;
                    this.columnInfoCopy.find(n=>n.paramValue === item.paramValue).setSearchColumn = value;
                })
                this.isIndeterminate = false;
            },
            handleCheckedChange(value, row) {
                if(row)
                    this.columnInfoCopy.find(n=>n.paramValue === row.paramValue && row.dataSetId == n.dataSetId).setSearchColumn = row.setSearchColumn;
                this.requestChooseList = this.columnInfoCopy.filter(n=>n.setSearchColumn);
                this.setCheckState();
            },
            setResponseColumnSelectAll(value){
                this.data.forEach(item => {
                    item.setResponseColumn = value;
                    this.columnInfoCopy.find(n=>n.paramValue === item.paramValue && item.dataSetId == n.dataSetId).setResponseColumn = value;
                })
                this.isSetResponseColumnIndeterminate = false;
            },
            setResponseColumnHandleCheckedChange(value, row) {
                if(row){
                    this.columnInfoCopy.find(n=>n.paramValue === row.paramValue && row.dataSetId == n.dataSetId).setResponseColumn = row.setResponseColumn;
                }
                this.responseChooseList = this.columnInfoCopy.filter(n=>n.setResponseColumn);
                this.setCheckState();
            },
            setCheckState(){
                let i = 0, p = 0, j = 0;
                this.data.forEach(item => {
                    if (item.setResponseColumn) i++;
                    if (item.setSearchColumn) p++;
                    if (item.setSortColumn) j++;
                })
                let h = this.data.length;
                this.checkAll = p === h;
                this.isIndeterminate = p > 0 && p < h;
                this.setResponseColumnCheckAll = i === h;
                this.isSetResponseColumnIndeterminate =  i > 0 && i < h;
                this.setSortColumnCheckAll = j === h;
                this.isSetSortColumnIndeterminate =  j > 0 && j < h;
            },
            setSortColumnSelectAll(value) {
                this.data.forEach(item => {
                    item.setSortColumn = value;
                    this.columnInfoCopy.find(n=>n.paramValue === item.paramValue  && item.dataSetId == n.dataSetId).setSortColumn = value;
                })
                this.isSetSortColumnIndeterminate = false;
            },
            setSortColumnHandleCheckedChange(value, row){
                if(row){
                    this.columnInfoCopy.find(n=>n.paramValue === row.paramValue && row.dataSetId == n.dataSetId).setSortColumn = row.setSortColumn;
                }
                this.sortChooseList = this.columnInfoCopy.filter(n=>n.setSortColumn);
                this.setCheckState();
            },
            clear_data(){
                this.visible = false;
                this.showParams = false;
            },
            show(){
                this.visible = true;
                this.showParams = true;
                this.columnCode = '';
                this.requestChooseList  = JSON.parse(JSON.stringify(this.requestTableData));//选中的查询字段
                this.responseChooseList = JSON.parse(JSON.stringify(this.responseTableData)); // 选中的返回字段
                this.sortChooseList  = JSON.parse(JSON.stringify(this.sortTableData)); // 选中的排序字段
                this.columnInfoCopy = JSON.parse(JSON.stringify(this.columnInfo));
                if(this.singleOrManyTable === '0'){//单表
                    this.data = JSON.parse(JSON.stringify(this.columnInfoCopy));
                }
                else{
                    this.selectDataSet = this.dataSetColumnList.length>0 ?  this.dataSetColumnList[0] : {};
                    this.data = this.selectDataSet.column;
                }
                this.setCheckState();
            },
            //数据集点击
            changeSet(item){
                this.columnCode = '';
                this.selectDataSet = item;
                this.data = item.column;
                this.setCheckState();
            },
            save(){
                this.$emit("setTableData", this.columnInfoCopy);
                this.visible = false;
                this.showParams = false;
            }
        },
    }
</script>

<style scoped lang="less" src="../index.less"></style>
<style scoped lang="less">
    .setParams{
        display: flex;
        height: 100%;

        &-dataset{
            width:200px;
            &_list{
                background-color: rgba(0,0,0,0.1);
                padding: 10px;
                margin-bottom: 10px;
                white-space: nowrap; //禁止换行
                overflow: hidden;
                text-overflow: ellipsis;
                color: #333;
                cursor: pointer;
            }
            &_list.active{
                color: #1890ff;
                background-color: rgba(24,144,255,0.15);
            }
        }

        &-column{
            width:calc(100% - 200px);
            padding-left: 10px;
        }
    }
    /deep/.ce-common_dialog > .el-dialog__body{
        overflow: hidden;
    }
</style>