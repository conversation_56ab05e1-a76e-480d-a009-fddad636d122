<template>
    <div class="modelPage">
        <ModelPlugIn :modelParams="modelParams" isExcavate /> <!--isExcavate 判断挖掘页面-->
        <DataSource :modelParams="modelParams" :tabId="tabId" :isTemplate="isTemplate" />
        <Attribute :treeData="treeData" :rowData="rowData" :tabId="tabId" />
        <ModelPanel v-bind="$props" :modelParams="modelParams" />
    </div>
</template>

<script>
    import ModelPlugIn from '@/components/flowPanel/flow-plugin/ModelPlugIn'
    import DataSource from '@/components/flowPanel/flow-plugin/DataSource'
    import ModelPanel from './flowPanel/ModelPanel'
    import Attribute from '@/components/flowPanel/flow-plugin/Attribute'
    import {ModelMixins} from "@/components/flowPanel/mixins/model-mixins";
    export default {
        name: "ModelPage",
        mixins : [ModelMixins],
        components : {
            ModelPlugIn,
            DataSource,
            ModelPanel,
            Attribute
        },
        props :{
            rowData : Object ,
            tabId : String,
            treeData:Array,
            isTemplate :Boolean,
            focusTab : String
        },
    }
</script>

<style scoped>
    .modelPage {
        min-width: 1360px;
        height: calc(100vh - 40px - 4rem);
        background: #f5f4f6;
    }


</style>
