import * as reqUtil from '@/api/reqUtil'

const root = '/apiTest/';

/**
 * 在线测试 api 列表树
 * @param settings
 * @return {Promise<*>}
 */
export async function serviceTreeList (settings){
    return await reqUtil.get({
        url : root + 'serviceTreeList',
        settings
    })
}

/**
 * 在线 测试
 * 如果是模型分析 比对订阅 数据碰撞 的发送请求 将参数塞在testDataJson里
 *
 * @param params
 * @param settings
 * @return {Promise<*>}
 */
export async function testSingleService ({params , settings}){
    return await reqUtil.post({
        url : root + 'testSingleService',
        settings
    },params)
}

/**
 * 批量测试
 * @param params
 * @param settings
 * @return {Promise<*>}
 */
export async function testBatchService ({params , settings}){
    return await reqUtil.post({
        url : root + 'testBatchService',
        settings
    },params)
}

/**
 * 导入用例树
 * @param params
 * @return {Promise<*>}
 */
export async function getCaseTreeWithElement (settings){
    return await reqUtil.get({
        url :'useCaseManagement/getCaseTreeWithElement',
        settings
    })
}

/**
 * 导入用例 获取参数
 * @param params
 * @param settings
 * @return {Promise<*>}
 */
export async function importUseCase({params , settings}){
    return await reqUtil.post({
        url : root + 'importUseCase',
        settings
    },params)
}



