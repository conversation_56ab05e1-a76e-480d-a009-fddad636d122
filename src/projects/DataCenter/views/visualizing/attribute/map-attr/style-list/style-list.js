import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";
import area from "@/assets/data/area.json"

export default {
    name: "styleList",
    components: {
        chartStyle,
    },
    mixins: [attrStyle],
    props: {
        node: Object,
        dir_dataSetId: String
    },
    data() {
        let mapOptions = this.transFormArea(0) ,
            mapCode = this.$map_area_code;
        return {
            activeTabs: ["title", "legend", "funcSettings","fieldSetting"],
            styleList: {
                title: {
                    label: "标题内容",
                    settingForm: {
                        show: {
                            label: "显示标题",
                            type: "checkbox",
                        },
                        custom_style: {
                            label: "字体样式",
                            type: "sty_radio"
                        },
                        fontFamily: {
                            type: "select_font",
                            isLine: true
                        },
                        fontSize: {
                            type: "select_size",
                            isLine: true
                        },
                        fontWeight: {
                            type: "select_w",
                            isLine: true
                        },
                        fontStyle: {
                            type: "checkBtn",
                            isLine: true
                        },
                        color: {
                            type: "font_color",
                            isLine: true
                        },
                        align: {
                            type: "font_align",
                            isLine: true
                        },
                        text: {
                            type : "form_input" ,
                            pos : "right" ,
                            label : "标题名称" ,
                            maxlength :30 ,
                            width : "76px",
                            placeholder :"请輸入标题名称(限30个字符)"
                        },
                    },
                    form: {
                        show: true,
                        custom_style: "default",
                        fontFamily: "sans-serif",
                        fontSize: "12px",
                        fontWeight: "normal",
                        fontStyle: [],
                        color: "#666",
                        text: "",
                        align: "center"
                    }
                },
                legend: {
                    label: "图表样式",
                    settingForm: {
                        label_show: {
                            label: "显示地名",
                            type: "checkbox",
                        },
                        /*label_show_t: {
                            type: "radio_btns",
                            align: "left",
                            options: [
                                {
                                    label: '智能显示',
                                    value: 'hover'
                                }, {
                                    label: '全量显示',
                                    value: 'always'
                                }
                            ]
                        },*/
                        visualMap : {},
                        rangeColor :{},
                        legend_pos: {
                            title: "图例设置",
                            type: "radio_btns",
                            align: "center",
                            options: [
                                {
                                    label: '上方',
                                    value: 'top'
                                }, {
                                    label: '下方',
                                    value: 'bottom'
                                }, {
                                    label: '右侧',
                                    value: 'right'
                                }, {
                                    label: '左侧',
                                    value: 'left'
                                }, {
                                    label: '无',
                                    value: 'none'
                                }
                            ]
                        },
                        orient: {
                            label: "图例列表布局",
                            type: "form_select",
                            width: "100px",
                            pos: "right",
                            options: [
                                {value: "horizontal", label: "水平"},
                                {value: "vertical", label: "垂直"},
                            ]
                        },
                        color: {
                            label: "配色方案",
                            type: "legend_color",
                            width : "100px",
                        },
                        top: {
                            label: "上边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        },
                        bottom: {
                            label: "下边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        },
                        left: {
                            label: "左边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        },
                        right: {
                            label: "右边距",
                            type: "number_slider",
                            step: 1,
                            min: 0,
                        }
                    },
                    form: {
                        label_show: true,
                        // label_show_t: "hover",
                        min: 0,
                        max: 200,
                        minAuto: true,
                        maxAuto: true,
                        splitNumber: 5,
                        rangeColor : [],
                        color: [],
                        color_theme :"default",
                        rangeColor_theme :"default",
                        color_type :"custom",
                        rangeColor_type :"custom",
                        orient: "horizontal",
                        legend_pos: "top",
                        top_type: "abs",
                        top: 66,
                        bottom_type: "abs",
                        bottom: 26,
                        left_type: "abs",
                        left: 40,
                        right_type: "abs",
                        right: 100,
                        minSpan: 0,
                        start: 0,
                        end: 100,
                    }
                },
                funcSettings: {
                    label: "功能配置",
                    settingForm: {
                        mapCode: {
                            label: '选择地图',
                            type: 'cascader',
                            placeholder: '请选择地图',
                            width: "100px",
                            pos: "right",
                            props: {checkStrictly: true},
                            options: mapOptions
                        },
                    },
                    form: {
                        mapCode: ""
                    }
                },
                fieldSetting: {
                    label: "系列设置",
                    settingForm: {
                        alias: {
                            label: "别名",
                            type: "field_alias",
                            maxlength: 100,
                            width: "120px",
                            pos: "right",
                            placeholder: "请输入别名(限100个字符)"
                        }
                    },
                    form: {
                        options: [],
                    }
                }
            },
            emitStyle: {},
        }
    },
    methods: {
        getAllStyle() {
            const {emitStyle} = this;
            let styles = {};
            for (let k in emitStyle) {
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        colorOpt(){
            const vm = this;
            let visualMap = {} ,rangeColor = {} ;
            if(vm.node && vm.node.code === 'ColourMap'){
                visualMap = {
                    type: "color_map",
                    label: "色阶配置",
                    tipMin: "最小值",
                    tipMax: "最大值",
                    tipLabel: "自动",
                    splitTip: "区间个数"
                };
                rangeColor = {
                    label: "配色方案",
                    type: "legend_color",
                    width : "90px"
                };
                vm.styleList.legend.settingForm.visualMap = visualMap;
                vm.styleList.legend.settingForm.rangeColor = rangeColor;
            }
        },
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                vm.colorOpt();
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData) ;
                let {title, legend ,funcSettings, fieldSetting } = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                legend ? vm.emitStyle.legend = vm.styleList.legend.form = legend : vm.emitStyle.legend = vm.styleList.legend.form;
                funcSettings ? vm.emitStyle.funcSettings = vm.styleList.funcSettings.form = funcSettings : vm.emitStyle.funcSettings = vm.styleList.funcSettings.form;
                fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form;
                vm.node.setData = false;
            })

        }
    },
}
