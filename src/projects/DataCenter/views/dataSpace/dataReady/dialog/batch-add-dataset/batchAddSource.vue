<!--批量添加 源数据集-->
<template>
    <div class="batchAddSource batch-outer" v-loading="settings.loading">
        <div class="batch-left">
            <div class="batch-title pb10">{{ sourceTxt }}</div>
            <div class="batch-left-cont batch-cont">
                <ce-list-tree :isEdited="()=>false" :filterPlaceholder="filter_p"
                              :search-style="{'padding' : '10px 12px' }" :treeProps="paramProps"
                              :getTreeInterface="getSourceList"
                              @node-click="getSourceTable"
                              :treeBind="{
                                'expand-on-click-node' : true
                                }">
                    <template slot="nodeLabel" slot-scope="{node,  data }">
                        <span :title="data.label" :class="{
                           'el-icon-folder' : !node.expanded  ,
                            'el-icon-folder-opened' : node.expanded ,
                        }" class="node-label">{{ data.label }}</span>
                    </template>
                </ce-list-tree>
            </div>
        </div>
        <div class="batch-right" v-loading="pageLoad.loading">
            <div class="batch-bar pb10">
                <div class="batch-title is-required">{{ sourceTypeTxt }}</div>
                <dg-tree-drop
                        class="pct100"
                        v-model="dataSetDsTypeTreeNodeId"
                        placeholder="请选择"
                        :props="defaultProps"
                        :data="dataTypeList"
                        :tree-props="{
                            'default-expanded-keys': dataSetDsTypeTreeNodeId ? [...expandKeys,dataSetDsTypeTreeNodeId] : expandKeys,
                            'current-node-key': dataSetDsTypeTreeNodeId,
                            'nodeKey': 'id',
                    }"
                        check-leaf
                        filterable
                        check-strictly
                        visible-type="leaf"
                ></dg-tree-drop>
            </div>
            <div class="batch-group pb10">
                <div class="batch-title is-required" v-text="`${tableAllTxt}(${selection.length}) , ${tableTxt}(${currentChoose.length}/${total})`"></div>
                <el-input v-model="filterTxt" v-input-limit:trim clearable :placeholder="filter_p" suffix-icon="el-icon-search"
                          class="batch-search">
                </el-input>
            </div>
            <div class="batch-cont">
                <common-table ref="table"
                              row-key="id"
                              @select="selectChange"
                              @select-all="selectChange"
                              :height="showTableList.length ? 'calc(100% - 42px)' : '100%'"
                              paging-type="client"
                              :pagination="true"
                              :pagination-props="paginationProps"
                              :data="showTableList" :columns="tableHead">
                </common-table>
            </div>
        </div>
        <dialogFooterBtn v-footer v-show="!settings.loading" :data="footerBtn"/>
    </div>
</template>

<script>
import dialogFooterBtn from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn"
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins"
import CeListTree from "dc-front-plugin/src/components/CeListTree/CeListTree"
import {mapGetters,mapState} from "vuex";

export default {
    name: "batchAddSource",
    components: {
        dialogFooterBtn,
        CeListTree
    },
    mixins: [commonMixins],
    data() {
        return {
            pageLoad: {loading: false},
            footerBtn: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.submit,
                    show: () => true
                },
            ],
            sourceTxt: '选择数据源',
            tableTxt: '当前库选择数据表',
            tableAllTxt: '总选择',
            filterTxt: '',
            filter_p: '请输入关键字搜索',
            treeData: '',
            tableData: [],
            tableHead: [
                {
                    prop: 'code',
                    label: '数据表名称',
                    minWidth: '160px'
                },
                {
                    prop: 'name',
                    label: '数据表中文名',
                    minWidth: '160px'
                }, {
                    type: 'selection',
                    'reserve-selection': true
                }
            ],
            placeholder: '',
            paramProps: {
                label: "label",
                children: "children",
                id: 'id',
            },
            selection: [],
            allColumnObj: {},
            sourceTypeTxt: "数据来源类型",
            dataSetDsTypeTreeNodeId: "",
            defaultProps: {
                children: "children",
                label: "label",
                value: "id"
            },
        }
    },
    props: {
        dirId: String
    },
    computed: {
        ...mapGetters(["userInfo", "userRight"]),
        rights() {
            const vm = this;
            let rights = [];
            if (vm.userRight) rights = vm.userRight.map(r => r.funcCode);
            return rights;
        },
        showTableList() {
            return this.tableData.filter(li => !this.filterTxt || li.code.toLowerCase().includes(this.filterTxt.toLowerCase()) || li.name.toLowerCase().includes(this.filterTxt.toLowerCase()));
        },
        ...mapState({
            dataTypeList:(state) => state.plans.dataTypeList.filter(l => l.id !== state.plans.shareId && l.id !== state.plans.otherUserCatalogId),
        }),
        expandKeys(){
            return this.dataTypeList.length > 0 ? [this.dataTypeList[0].id] :[]
        },
        currentChoose(){
            const ids = this.selection.map(o => o.id);
            return this.tableData.filter(t => ids.includes(t.id));
        }
    },
    methods: {
        submit() {
            const vm = this, {settings, dataSetDsTypeTreeNodeId} = this;
            if (!vm.selection.length) return vm.$message.warning("请选择添加的数据表!");
            if (!dataSetDsTypeTreeNodeId) return vm.$message.warning("请选择数据来源类型");
            let services = this.$services('dataReady');
            settings.loading = true;
            let params = vm.getSaveVo();
            services.batchSaveAll({params, settings}).then(res => {
                if (res.data.code === 0) {
                    vm.$message.success("添加成功");
                    vm.$emit("success");
                }
            })
        },
        getSaveVo() {
            const vm = this, {dataSetDsTypeTreeNodeId} = vm;
            return this.selection.map((li) => {
                return {
                    /*columns: vm.allColumnObj[li.id],*/
                    tableMapping: {
                        tableId: li.id,
                        tableMappingId: li.tableMappingId,
                    },
                    dataObjIds: [li.tableMappingId],
                    name: li.name || li.code,
                    dataSetTreeNodeId: vm.dirId,
                    dataSetDsTypeTreeNodeId
                }
            })
        },
        getSourceList(settings) {
            let services = this.$services('dataSource');
            if (this.rights.includes("dataConnectionDataWarehouse")) {
                return services.getDataPreparationTree("", "", "", settings).then(res => {
                    if (res.data.code === 0) {
                        return res.data.data;
                    }
                }).catch(err => {
                })
            } else {
                return services.queryDirTree("", "", "", settings).then(res => {
                    if (res.data.code === 0) {
                        return res.data.data;
                    }
                }).catch(err => {
                })
            }
        },
        getSourceTable(node) {
            const vm = this, {pageLoad} = this;
            vm.filterTxt = '';
            let services = vm.$services('dataContact');
            let data = {
                pageSize: -1,
                pageIndex: 1,
                name: '',
                sortField: "name",
                sortOrder: "desc",
                dwDbId: node.id,
                isStdlib: false,
            };
            pageLoad.loading = true;
            services.queryDataBaseTable(data).then(async res => {
                if (res.data.code === 0) {
                    let result = res.data.data;
                    vm.tableData = result.dataList ? result.dataList.map(li => {
                        li.name = li.name || li.code;
                        return li;
                    }) : [];
                    // if (vm.tableData.length) {
                    //     let ids = vm.tableData.map(li => li.id);
                    //     await vm.getColumns(ids);
                    // }
                    // eslint-disable-next-line require-atomic-updates
                    vm.total = result.totalCount;
                    pageLoad.loading = false;
                }
            }).catch(() => {
                pageLoad.loading = false;
            })
        },
        async getColumns(ids) {
            const vm = this;
            let services = vm.$services('dataReady');
            let promise = ids.map(async id => {
                return await services.getClassifyFeatures(id);
            });
            await Promise.all(promise).then(res => {
                res.forEach(re => {
                    vm.setColumns(re);
                })
            }).catch(() => {
                vm.settings.loading = false;
            })
        },
        setColumns(res) {
            if (res.data.code === 0 && res.data.data.length) {
                let {dimension, measure, id} = res.data.data[0];
                dimension = dimension || [];
                measure = measure || [];
                this.allColumnObj[id] = [...dimension, ...measure];
            }
        },
        selectChange(selection) {
            this.selection = selection;
        },
        /**
         *  回显勾选字段
         */
        backSelectFields() {
            const vm = this;
            this.$nextTick(() => {
                vm.selection.forEach(row => {
                    vm.$refs.table.$refs.table.toggleRowSelection(row);
                })
            })
        }
    }
}
</script>

<style scoped lang="less">
.batch {
    &-outer {
        height: 100%;
        display: flex;
    }

    &-cont {
        height: calc(100% - 4rem - 20px);
    }
    &-left {
        width: 240px;
        height: 100%;
        flex-shrink: 0;

        &-cont {
            border: 1px solid #ddd;
            box-sizing: border-box;
            height: calc(100% - 2rem - 10px);
        }
    }

    &-right {
        padding-left: 14px;
        height: 100%;
        flex-grow: 1;
        flex-shrink: 0;
        width: calc(100% - 254px);
    }

    &-title {
        color: rgba(0, 0, 0, .65);
        line-height: 2rem;
        font-size: 14px;
        font-weight: bold;
        white-space:nowrap;
        &.is-required::before {
            content: '* ';
            color: #F56C6C;
        }
    }


    &-search {
        width: 240px;
    }

    &-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &-bar {
        display: flex;
        gap: 10px;
        align-items: center;
    }

}
</style>
