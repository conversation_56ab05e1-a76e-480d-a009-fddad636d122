<template>
    <div class="params">
      <!--  <el-form style="width: 100%;" label-position="top">
            <el-form-item prop="sort">
                <div slot="label"> <span style="color: red"> * </span>选择排序字段</div>
                <el-button class="width100" type="primary" @click="show">选择排序字段</el-button>
            </el-form-item>
        </el-form>
        <common-dialog
                class="params-dialog"
                :title="title"
                :width="width"
                :visible.sync="visible"
                @closed="clear_data"
                v-loading="loading"
        >-->
            <!--参数配置弹窗内容页-->
            <params-page
                          class="width100"
                          v-if="showDialog"
                          :fieldOpt="fieldOpt"
                          :sortOpt="sortOpt"
                          :streamingData="streamingData"
                          @updateConfig = "updateConfig"
                          ref="dataSortDialog"
            ></params-page>

         <!--   <div slot="footer">
                <el-button size="mini" type="primary" @click="saveDialog">确定</el-button>
                <el-button size="mini" @click="visible = false">取消</el-button>
            </div>
        </common-dialog>-->
        <pre-view ref="preview"></pre-view>
    </div>
</template>
<script>
import paramsPage from "./conds/index"
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
export default {
    name: "params",
    mixins : [pluginMixins , servicesMixins],
    components: {
            preView,
            paramsPage
        },
    props : {
        sourceData: Object,
        rowData: Object,
        loadParams: Object,
    },
    data() {
        return {
            showDialog:false,
            form: {
                sort: ''
            },
            title:"数据排序",
            rules: {
                sort: [
                    { required: true, message: '请选择数据排序', trigger: 'change' }, 
                ]
            },
            fieldOpt:[],
            sortOpt: [
                {
                    label: '升序',
                    value: 'ASC'
                }, {
                    label: '降序',
                    value: 'DESC'
                },
            ],
            streamingData: [],
        };
    },
    methods: {
        save(...args){
            this.saveFn(...args);
        },
        preview(){
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        updateConfig(e){
          this.streamingData = e;
        },
        initData(){
            const vm = this , {pluginServices , pluginMock ,settings, sourceData,loadParams} = this;
            let services = vm.getServices( pluginServices , pluginMock);
            loadParams.loading = true;
            services.dataSortPluginPage(sourceData.id , loadParams).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    vm.fieldOpt = result.columns.map(col => {
                        col.label = col.columnZhName || col.columnName;
                        col.value = col.columnName;
                        col.type = col.columnType;
                        return col;
                    });
                    vm.sortOpt = result.orderTypes.map(order => {
                        order.value = order.CODE;
                        order.label = order.NAME;
                        return order;
                    });
                    vm.streamingData = [];
                    if(result.dataSortPluginFieldExps.length){
                        let dataExp = result.dataSortPluginFieldExps;
                        for(var i = 0; i < dataExp.length - 1; i++){
                            //每轮比较的次数
                            for(var j = i + 1; j < dataExp.length; j++){
                                if(dataExp[i].calauSortNo > dataExp[j].calauSortNo){
                                    var tmp = dataExp[i];
                                    dataExp[i] = dataExp[j];
                                    dataExp[j] = tmp;
                                }
                            }
                        }
                        dataExp.forEach(exp => {
                            vm.streamingData.push({
                                field : exp.dataSortFileName,
                                type : exp.dataSortFileDataType,
                                order : exp.dataSortType
                            })
                        })
                    }
                }
                vm.showDialog = true;
            })
        },
        saveFn(saveName,showTip){
            if(!this.$refs.dataSortDialog.saveChanges()){
                return;
            }
            const vm = this , {pluginServices , pluginMock ,settings, sourceData,loadParams} = this;
            let services = vm.getServices( pluginServices , pluginMock);
            loadParams.loading = true;
            let dataVo = vm.setDataVo();
            if (dataVo.dataSortPluginFieldExps.length < 1 || dataVo.dataSortPluginFieldExps === null || dataVo.dataSortPluginFieldExps === undefined){
                vm.$message.warning("请配置排序字段");
                loadParams.loading = false;
                return;
            }
            services.saveDataSortPlugin(sourceData.id , dataVo ,  loadParams).then(res => {
                if(res.data.status === 0){
                    showTip && vm.$message.success("保存成功");
                    if(saveName)saveName();
                    vm.$emit("setPreviewAble");
                }
            })

        },
        setDataVo(){
            const vm = this , {streamingData} = this;
            let dataVo = [];
            streamingData.forEach((item , i) => {
                dataVo.push({
                    dataSortFileName : item.field ,
                    dataSortFileDataType : item.type ,
                    dataSortType : item.order ,
                    calauSortNo : i+1
                })
            });
            return {dataSortPluginFieldExps : dataVo} ;
        },
        saveDialog(){
            if(!this.$refs.dataSortDialog.saveChanges()){
                return;
            }
            this.close();
        }
    },
    created() {
        this.initData();
    }
};
</script>
<style lang="less" scoped>
.params {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    overflow: auto;
    flex: 1;
    &__title {
        display: inline-block;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 12px;
        position: relative;
        padding-left: 10px;
        &::before {
            content: "*";
            position: absolute;
            top: 0;
            left: 0;
            color: #f5222d;
        }
    }
    &__condition-row {
        margin-bottom: 6px;
    }
    &__condition-col {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    &__prev {
        margin-top: auto;
    }
    .params-btn {
        align-self: stretch;
    }
}
</style>
