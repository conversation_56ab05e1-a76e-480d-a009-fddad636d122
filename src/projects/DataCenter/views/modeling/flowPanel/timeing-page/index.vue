<template>
    <div class="timing">
        <common-dialog custom-class="timing"
                    :width="width"
                    :title="title"
                    :visible.sync="visible"
                    @closed="clearData"
        >
            <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="documentCode"></help-document></div>
            <time-page ref="timePage" v-if="reNew" :rowData="rowData || row" :isAIModel="isAIModel" @save="save"/>
            <span slot="footer">
                <dg-button @click="visible = false">{{ btnCancelTxt }}</dg-button>
                <dg-button type="primary" @click="saveFn">{{ saveBtnTxt }}</dg-button>
            </span>
        </common-dialog>
    </div>
</template>

<script>
import {dialog} from "@/api/commonMethods/dialog-mixins";
import timePage from "./time-page"
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/data-excavate/service-mixins/service-mixins"

export default {
    name: "timing",
    mixins :[dialog,commonMixins, servicesMixins],
    components : {timePage},
    props: {
        rowData: Object,
        isAIModel : Boolean
    },
    data() {
        return {
            width: "1000px",
            pageLoad: false,
            documentCode: '',
        }
    },
    methods: {
        show(type) {
            this.title = "设置定时作业";
            this.visible = true;
            this.reNew = true;
            this.documentCode = type === 'ai'? 'AIModelingTimedTask' : 'processModelingSaseSettingPage'
        },
        saveFn(){
            this.$refs.timePage.setSaveVo();
        },
        save(vo){
            const vm = this, {modelingServices, modelingMock} = this;
            let services = vm.getServices(modelingServices, modelingMock);
            if (this.isAIModel) {
                services.saveScheduledTask(vo).then(res => {
                    if (res.data.status === 0) {//保存成功
                        vm.$message.success("保存成功！");
                        this.visible = false;
                        this.reNew = false;
                        this.$emit("changePage");
                    }
                })
            } else {
                services.updateDataMiningScheduleWithSubTrans(vo).then(res => {
                    if (res.data.status === 0) {//保存成功
                        vm.$message.success("保存成功！");
                        this.visible = false;
                        this.reNew = false;
                        this.$emit("changePage");
                    }
                })
            }
            
        },
        
    },
    created(){
    }
}
</script>

<style scoped lang="less">

</style>
