.tree {
  height: 100%;
}
@media screen and (max-width: 1365px){
  .d-s-l-bottom  {
    height: calc(100% - 68px);
  }
}

@media screen and (max-width: 1680px) and (min-width: 1366px){
  .d-s-l-bottom  {
    height: calc(100% - 76px);
  }
}

@media screen and (min-width: 1681px) {
  .d-s-l-bottom  {
    height: calc(100% - 84px);
  }
}
.tree_cont {
  padding-top: 10px;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}
.custom-tree-node  {
  width: calc(100% - 32px) ;
  overflow: hidden;
}
.node-label {
  overflow: hidden;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.el-dropdown-link-rote {
  transform: rotate(90deg);
  display: flex;
  align-items: center;
}

.upload-row {
  p {
    line-height: 1.4;
  }
  .upload-row-p1 {
    color: #cccccc;
    font-size: 14px;
    margin-top: 10px;
  }
  .upload-row-p2 {
    margin-top: 20px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
  }
}

.node-label::before {
  padding-right: 5px;
}
.d-v-l-head {
  padding: 10px 0;
}

.ce-radio_two /deep/.el-radio-button__inner {
  width: 104px;
}