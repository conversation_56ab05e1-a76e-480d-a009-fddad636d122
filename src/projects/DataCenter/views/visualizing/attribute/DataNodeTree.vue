<template>
    <div class="dataNodeTree" v-loading="settings.loading">
        <div class="ce-dataset">
            <el-form class="ce-form__m0" label-position="right" size="mini" label-width="90px">
                <el-form-item :label="data_tip + ' :'">
<!--                    <ce-select-drop
                            scrollbar-class="dataset-drop"
                            class="expect-select"
                            ref="pos_tree"
                            placeholder="请选择数据集"
                            :props="defaultProps"
                            filterable
                            check-leaf
                            check-strictly
                            :tree-props="{...treeBind ,
                                'default-expanded-keys': expandData
                            }"
                            :filterNodeMethod="filterNode"
                            v-model="dataSetVal"
                            @current-change="getDatasetFields"
                            :data="dataSet"
                    >
                    </ce-select-drop>-->
                    <dataset-pop
                        class="expect-select"
                        v-model="dataSetVal"
                        placeholder="请选择数据集"
                        @current-change="getDatasetFields"
                    />
                </el-form-item>
            </el-form>
        </div>

        <div class="ce-search_tree" v-loading="loading">
            <search-tree
                    ref="tree"
                    :data="treeData"
                    :default-expand-all="true"
                    @node-contextmenu="rightMenu"
                    :props="defaultProps"
                    :filter-node-method="filterNode"
            >
            </search-tree>
            <ul ref="ce_menu" class="ce-tree__menu" v-show="menuVisible">
                <li v-for="(item , i) in treeMenu" :key="i" @click="item.fn">{{ item.name }}</li>
            </ul>
        </div>

    </div>

</template>

<script>
import SearchTree from '../../component/tree/SearchTree'
import {treeMethods} from "@/api/treeNameCheck/treeName"
import {datasetMixins} from "../components/datasetMixins/dataset"
import {servicesMixins} from "../service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import DatasetPop from "@views/dataSpace/dataReady/dialog/dataset-tree-panel/dataset-pop.vue";

export default {
    name: "DataNodeTree",
    components: {
        DatasetPop,
        SearchTree
    },
    mixins: [datasetMixins, servicesMixins, commonMixins],
    data() {
        return {
            treeMenu: [],
            menuVisible: false,
            loading: false,
            treeData: [],
            data_tip: '数据集',
            dataSetVal: '',
            dataSet: [],
            bkDataset: [],
            defaultProps: {
                value: "id",
                label: "label",
                children: "children"
            },
            treeBind: {
                // "default-expand-all" : true
            },
            expandData:[]
        }
    },

    watch: {
        dataSetVal(val, oldVal) {
            //let classifierStatId = this.getClassifierStatId(val);
            let classifierStatId = val;
            if (oldVal === '') {
                this.$emit('resetList', classifierStatId, this.getDataSet(), false);
            } else {
                this.$emit('resetList', classifierStatId, this.getDataSet(), true);
            }
        }
    },
    methods: {
        /**
         * 设置默认展开层级
         * @param {*} data
         */
        setExpandData(data){
            this.expandData.push(...data.map(item => item.id))
        },
        filterNode: treeMethods.methods.filterNode,
        showMenu(event, object, node) {
            this.menuVisible = true;
            let menu = this.$refs.ce_menu;

            this.$nextTick(() => {
                let menuH = this.treeMenu.length * 29, docH = document.body.offsetHeight,
                        x = event.clientX, y = menuH + event.clientY < docH ? event.clientY : event.clientY - menuH;
                menu.style.left = x + 10 + "px";
                menu.style.top = y - 10 + "px";
            });
        },
        hideMenu() {
            this.menuVisible = false;
        },
        rightMenu(evt, data, node, ele) {
            /*if(node.level < 2 ) return ;
            this.showMenu(evt , data , node );
            if( data.fieldType === 1){
                this.treeMenu = [
                    {
                        name : '转为度量' ,
                        fn : this.toMetric
                    }
                ];
            }else {
                this.treeMenu = [
                    {
                        name : '转为维度' ,
                        fn : this.toDimension
                    }
                ];
            }*/

        },
        toMetric() {
            this.hideMenu();
        },
        toDimension() {
            this.hideMenu();
        },
        inputData() {
            this.$emit('inputData');
        },
        setDataSetId(id) {
            this.$nextTick(() => {
                this.store_datasetId = id;
            })
        },
        setAllFieldData(result, dbType){
            const data = [];
            for (let j = 0; j < result.length; j++) {
                let f = result[j];
                let fieldAlias = f.fieldAlias ? f.fieldAlias : "";
                data.push({
                    id: f.id,
                    label: fieldAlias || f.name || f.code,
                    mbColumn: f.mbColumn,
                    code: f.code,
                    indexType: f.indexType,
                    dbtype: f.dbtype,
                    dbType: dbType,
                    dataType: f.dbtype,
                    format: f["format"],
                    alias: fieldAlias,
                    exp: f.exp
                })
            }
            this.treeData = data;
        },
        /**
         * 设置字段数据
         * */
        setFieldData(result, dbType) {
            this.setAllFieldData(result,dbType)
            /*const vm = this;
            let dim = {id: '1', label: "维度", children: []};
            let ms = {id: '2', label: "度量", children: []};
            for (let j = 0; j < result.length; j++) {
                let f = result[j];
                let fieldAlias = f.fieldAlias ? f.fieldAlias : "";
                if (f.indexType === 'DIMENSION') {
                    dim.children.push({
                        id: f.id,
                        label: fieldAlias || f.name || f.code,
                        mbColumn: f.mbColumn,
                        code: f.code,
                        indexType: f.indexType,
                        dbtype: f.dbtype,
                        dbType: dbType,
                        dataType: f.dbtype,
                        format: f["format"],
                        alias: fieldAlias,
                        exp: f.exp
                    })
                } else if (f.indexType === 'MEASURE') {
                    ms.children.push({
                        id: f.id,
                        label: fieldAlias || f.name || f.code,
                        mbColumn: f.mbColumn,
                        code: f.code,
                        indexType: f.indexType,
                        dbtype: f.dbtype,
                        dbType: dbType,
                        dataType: f.dbtype,
                        format: f["format"],
                        alias: fieldAlias,
                        exp: f.exp,
                        numberFormat: f.numberFormat
                    });
                }
            }
            vm.treeData.push(dim);
            vm.treeData.push(ms);*/
        },
        /**
         * 已弃用
         * @param val
         */
        initTree(val) {
            if (!val) return;
            this.treeData = [];
            let dataset;
            for (let i = 0; i < this.bkDataset.length; i++) {
                let d = this.bkDataset[i];
                if (val === d.id) {
                    dataset = d;
                    break;
                }
            }
            if (!dataset) return;

            let dbType = dataset.dbType;
            let dim = {id: '1', label: "维度", children: []};
            let ms = {id: '2', label: "度量", children: []};
            for (let j = 0; j < dataset.fields.length; j++) {
                let f = dataset.fields[j];
                let fieldAlias = f.fieldAlias ? f.fieldAlias : "";
                if (f.indexType === 'DIMENSION') {
                    dim.children.push({
                        id: f.id,
                        label: f.name,
                        code: f.code,
                        indexType: f.indexType,
                        dbtype: dbType,
                        format: f["format"],
                        alias: fieldAlias,
                        exp: f.exp
                    })
                } else if (f.indexType === 'MEASURE') {
                    ms.children.push({
                        id: f.id,
                        label: f.name,
                        code: f.code,
                        indexType: f.indexType,
                        dbtype: dbType,
                        format: f["format"],
                        alias: fieldAlias,
                        exp: f.exp,
                        numberFormat: f.numberFormat
                    });
                }
            }
            this.treeData.push(dim);
            this.treeData.push(ms);
        },

        dataSetChange(val) {
            this.inputData();
            this.initTree(val);
        },
        getClassifierStatId(id) {
            return this.bkDataset.filter(d => {
                return d.value === id;
            })[0].classifierStatId;
        },
        getDataSetVal() {
            return this.dataSetVal;
        },
        getDataSet() {
            let dataset;
            for (let i = 0; i < this.bkDataset.length; i++) {
                if (this.dataSetVal === this.bkDataset[i].id) {
                    dataset = this.bkDataset[i];
                }
            }
            return dataset;
        },
        clickOther(e) {
            this.hideMenu();
        },
    },
    created() {
        this.initDataSet();
        document.addEventListener('click', this.clickOther);
    },
    destroyed() {
        document.removeEventListener('click', this.clickOther);
    }
}
</script>

<style lang="less" scoped>


.dataNodeTree {
    height: 100%;
}

.ce-search_tree {
    height: calc(100% - 38px);
    position: relative;
}

.ce-dataset {
    margin-bottom: 8px;
}

.ce-tree {
    margin-top: 12px;
    height: calc(100% - 50px);
    overflow: auto;
}

.ce-tree__menu {
    position: fixed;
    top: 0;
    min-width: 80px;
    text-align: left;
    border: 1px solid #ccc;
    background: #fff;
    padding: 0;
    z-index: 100;
    box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.15);
    border-radius: 2px;
}

.ce-tree__menu li {
    cursor: pointer;
    list-style: none outside none;
    font-size: 12px;
    white-space: nowrap;
    border-bottom: 1px solid #eee;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
    color: #666;
}

.ce-tree__menu li:hover {
    color: @font-color;
}

</style>
