import {commonMixins} from "@/api/commonMethods/common-mixins"
import DatasetTree from "./dataset-tree/index.vue"
import {servicesMixins} from "../../service-mixins/service-mixins";

export default {
    name: "addDataset" ,
    mixins : [commonMixins , servicesMixins],
    components :{
        DatasetTree
    },
    props : {
        d_title : {
            type  :String ,
            default : "添加数据集"
        },
        datasetId : String,
        is_union : String
    },
    data(){
        return {
            tableList : [],
            title : this.d_title ,
            width :"360px" ,
            visible : false ,
            reNew : false ,
            curId : "",
            addDId:""
        }
    },
    methods :{
        clear_data(){
            this.reNew = false;
        },
        show(curId,addDId){
            const vm = this;
            vm.visible = true;
            vm.reNew = true;
            vm.curId = curId;
            vm.addDId = addDId;
        },
        submit(){
            const vm = this;
            let nodes = this.$refs.dataset.getSelectNode();
            if (this.is_union == "union_dataSet") {
                this.unionSubmit(nodes);
            } else {
                this.commonSubmit(nodes);
            }
        },
        commonSubmit(nodes) {
            if(nodes.length === 0){
                this.$message.warning("请选择数据集");
                return;
            }
            const vm = this , {datasetServices , datasetMock ,datasetId, settings} = this;
            let services = vm.getServices(datasetServices , datasetMock);
            settings.loading = true;
            let addDatasetId = nodes[0].id;
            services.getLogicDataColumn( addDatasetId ,datasetId , settings).then(res => {
                if(res.data.status === 0){
                    vm.$emit("initColumnsTree" , res.data.data ,addDatasetId);
                    vm.visible = false;
                }
            })
        },
        unionSubmit(nodes) {
            if(nodes.length === 0){
                this.$message.warning("请选择数据集");
                return;
            }
            const vm = this
            this.$emit("tijiao",nodes);
            vm.visible = false;
        },
    }
}