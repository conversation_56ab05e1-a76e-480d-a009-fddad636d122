import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {mapGetters} from "vuex";
import {common} from "@/api/commonMethods/common";
import $right from "@/assets/data/right-data/right-data"
import {servicesMixins} from "../service-mixins/service-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import addService from "../bus-manage/dialog/add-service/index"
import addInterface from "../bus-manage/dialog/add-interface-service/index"
import twoDialog from "../bus-manage/dialog/index"
import {coustTableH} from "@/api/commonMethods/count-table-h";

export default {
    name: "user-group-manage",
    mixins : [commonMixins , common, servicesMixins ,listMixins,coustTableH],
    components: {UserFormPop, addService, addInterface, twoDialog},
    computed : {
        ...mapGetters(["userInfo" , "userRight"]),
        rights (){
            let rights = [];
            if(this.userRight){
                this.userRight.forEach(r =>{
                    rights.push(r.funcCode)
                });
            }
            return rights;
        }
    } ,
    data() {
        return {
            bus_version:"",
            request_id:"",
            http_address:"",
            //角色管理搜索
            searchVal: "",
            //编辑弹窗控制显示与否
            editVisible: false,
            //角色信息编辑
            userFormData: {
                objCode:"",
                objName: "",
                fromAuthObj: "",
                userGroups: [],
                editUser: ""
            },
            // 角色管理表格数据
            tableData: [],
            addGroupRight : $right["accessBusManagementAddNewService"] ,
            saveGroupRight : $right["accessBusManagementSaved"] ,
            functionCode:"",
            //操作栏
            operator : [
                {
                    label : '编辑',
                    clickFn : this.edit,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementEdit"]) > -1,
                    icon : "&#xe6ad;"
                },{
                    label : '详情',
                    clickFn : this.serviceDetail,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementDetails"]) > -1,
                    icon : "&#xe6c5;"
                },{
                    label : '删除',
                    clickFn : this.deleteService,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementDelete"]) > -1,
                    icon : "&#xe847;"
                },{
                    label : '添加接口服务',
                    clickFn : this.view,
                    condition: (row , right) => row.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementAddServiceInterface"]) > -1,
                    icon : "&#xe6e1;"
                }
            ],
            //分页
            paginationProps : {
                currentPage: 1,
                pageSizes: [10, 20],
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            total : 0 ,
            userForm : {
                title : "" ,
                visible : false ,
                loading : false ,
                state : "" ,
                label :""
            },
            userGroups : [],
            expands: [],
            visible:false,
            busVersionOptions:[],
            busInfo:[],
            title:"",
            dialogFormVisible:false,
            editRow_name:"",
            rowData:{},
            interfaceOperator : [
                {
                    label : '编辑',
                    clickFn : this.interfaceEdit,
                    condition: (secondScope , right) => secondScope.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementMsgInfoEdit"]) > -1,
                    icon : "&#xe6ad;"
                },{
                    label : '详情',
                    clickFn : this.interfaceDetail,
                    condition: (secondScope , right) => secondScope.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementMsgInfoDetails"]) > -1,
                    icon : "&#xe6c5;"
                },{
                    label : '删除',
                    clickFn : this.deleteInterface,
                    condition: (secondScope , right) => secondScope.objCode !== this.$userGroupCode && right.indexOf($right["accessBusManagementMsgInfoDelete"]) > -1,
                    icon : "&#xe847;"
                },
            ],
            serviceDetailShow:false,
            interfaceDetailShow:false,
            submitShow:true,
            busId:"" ,
            tableHead : [
                {
                    type : 'expand' ,
                },{
                    prop : "serviceId" ,
                    label : "服务方ID"
                },{
                    prop : "name" ,
                    label : "服务方名称"
                },{
                    prop : "operate" ,
                    label : "操作",
                    width : "280" ,
                    align : "center"
                }
            ]
        };
    },
    methods: {
        closeDialog(){
            this.interfaceDetailShow = false;
            this.serviceDetailShow = false;
            this.submitShow =true;
        },
        interfaceDetail(index, row, scope) {
            this.interfaceEdit(index, row, scope);
            this.interfaceDetailShow = true;
            this.title = "接口服务详情"
            this.submitShow = false;
            this.functionCode = "accessBusManagementMsgInfoDetails";
        },
        serviceDetail(row) {
            this.edit(row , 'accessBusManagementDetails');
            this.title = "服务详情"
            this.serviceDetailShow = true;
            this.submitShow = false;
        },
        interfaceEdit(index, row, scope) {
            this.view(scope.row)
            this.rowData = row;
            this.title = "编辑接口服务"
            this.interfaceDetailShow = false;
            this.submitShow = true;
            this.functionCode = "accessBusManagementMsgInfoEdit";
        },
        edit(row , code) {
            const vm = this;
            this.title="编辑服务方"
            this.submitShow = true;
            this.serviceDetailShow = false;
            this.functionCode = code || "accessBusManagementEdit";
            vm.$nextTick(()=>{
                vm.$refs.twoDialogS.show("service");
            });
            this.rowData = row;
        },
        deleteService(row) {
            const vm = this , {busServices , busMock} = this;
            let services = vm.getServices(busServices ,busMock);
            this.$dgAlert(`<span>你确定删除\"${row.name}\"吗? </span>`, "提示", { //<br>（同时会删除用户拥有的角色）
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true,
                dangerouslyUseHTMLString: true
            }).then(()=>{
                services.deleteServiceById(row.id).then(async res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功");
                        this.changePage(1);
                    }
                });
            }).catch(_=>{});

        },
        deleteInterface(index, row) {
            const vm = this , {busServices , busMock} = this;
            let services = vm.getServices(busServices ,busMock);
            this.$dgAlert(`<span>你确定删除\"${row.name}\"吗? </span>`, "提示", { //<br>（同时会删除用户拥有的角色）
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true,
                dangerouslyUseHTMLString: true
            }).then(()=>{
                services.deleteInterfaceBodyById(row.id).then(async res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功");
                        this.changePage(1);
                    }
                });
            }).catch(_=>{});

        },
        getBusInfo() {
            const vm = this , {busServices , busMock} = this;
            let services = vm.getServices(busServices ,busMock );
            services.getBusInfo(this.userInfo.id).then(async res => {
                if (res.data.status === 0&&res.data.data.length!==0) {
                    this.busInfo = res.data.data[0];
                    this.busId = res.data.data[0].id;
                    this.http_address = res.data.data[0].httpUrl;
                    this.bus_version = res.data.data[0].name;
                }
            });
        },
        savaBusInfo() {
            const vm = this , {busServices , busMock} = this;
            const reg = new RegExp(
                /^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/
            )
            if (!reg.test(this.http_address)) {
                vm.$message.warning("请输入正确的HTTP地址");
                return;
            }
            if(!this.bus_version) {
                vm.$message.warning("请选择总线版本");
                return;
            }
            let busInfoVo = {
                busVersion: this.bus_version,
                httpUrl: this.http_address,
                senderId: this.request_id,
                id:this.busInfo.id||""
            };
            let services = vm.getServices(busServices ,busMock );
            services.saveOrUpdateBusInfo(busInfoVo).then(async res => {
                if (res.data.status === 0) {
                    vm.$message.success("保存成功");
                    this.busId = this.busId || res.data.data;
                }
            });
        },
        busVersionGet() {
            const vm = this , {busServices , busMock} = this;
            let services = vm.getServices(busServices ,busMock);
            services.getBusVersion().then(async res => {
                if(res.data.status === 0){
                    res.data.data.forEach(element => {
                        let options = {
                            value: element.code,
                            label: element.name
                        };
                        vm.busVersionOptions.push(options)
                    });
                    vm.getBusInfo();
                    vm.changePage(1);
                }
            });
        },
        view(row) {
            const vm = this;
            this.submitShow = true;
            this.serviceDetailShow = false;
            this.rowData = {};
            this.functionCode = "accessBusManagementAddServiceInterface";
            this.title = "添加接口服务"
            vm.$nextTick(()=>{
                vm.$refs.twoDialogS.show("interface", row);
            });
        },
        addNew() {
            const vm = this;
            this.submitShow = true;
            this.rowData = {};
            this.visible = true;
            this.serviceDetailShow = false;
            this.functionCode = "accessBusManagementAddNewService";
            this.title="添加服务方"
            vm.$nextTick(()=>{
                vm.$refs.twoDialogS.show("service");
            });
        },
        getRowKeys(row) {
            return row.id;
        },
        clickRowHandle(row, column, event) {
            const vm = this;
            if (vm.expands.includes(row.id)) {
                vm.expands = vm.expands.filter(val => val !== row.id);
            } else {
                vm.expands.push(row.id);
            }
        },
        async changePage(index){
            const vm = this , {busServices , busMock, settings} = this;
            let services = vm.getServices(busServices ,busMock );
            settings.loading = true;
            let pageSize = vm.paginationProps.pageSize;
            let data = {
                keyWord : this.searchVal ,
                index : index ,
                pageSize:pageSize
            };
            this.tableData = [];
            await services.getServiceInfoPage(data , settings).then(res =>{
                if (res.data.status === 0 && res.data.data.dataList !== null) {
                    res.data.data.dataList.forEach(element => {
                        element.serviceInfo.rowInterfaceInfo = element.interfaceInfo
                        this.tableData.push(element.serviceInfo)
                    });
                    this.total = res.data.data.totalCount
                }
            });
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        //删除表格行
        deleteRow(row) {
            const vm = this, {groupServices , busMock} = this;
            let services = vm.getServices(groupServices , busMock);
            this.$dgAlert(`<span>你确定删除\"${row.objName}\"吗? </span>`, "提示", { //<br>（同时会删除用户拥有的角色）
                type: 'warning',
                showCancelButton: true,
                showConfirmButton: true,
                dangerouslyUseHTMLString: true
            }).then(()=>{
                services.deleteUserGroup(row.id).then(res=>{
                    if(res.data.status === 0){
                        if(res.data.data === "删除成功"){
                            vm.$message.success(res.data.data);
                            vm.changePage(1);
                        }else {
                            vm.$message.warning(res.data.data);
                        }
                    }
                })
            }).catch(_=>{});
        },
        //mock 查询列表数据
        async searchTableData(){
            const vm = this; let searchVal = vm.searchVal.toLowerCase();
            await vm.changePage(1);
            vm.tableData = vm.tableData.filter(tab => {
                return tab.objName.toLowerCase().indexOf(searchVal) > -1 ||  tab.objCode.toLowerCase().indexOf(searchVal) > -1;
            })
        }
    },
    created(){
        this.busVersionGet();
    }
};
