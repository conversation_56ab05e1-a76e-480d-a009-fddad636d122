<template>
    <div class="treeAndList">
        <slot name="crumb"></slot>
        <div v-show="showLeft" class="ce-left_tree" :style="{width: widthPx}">
            <slot name="left"></slot>
        </div>
        <div class="ce-left_list" :style="{'margin-left': mainMarginPx}">
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default {
        name: "TreeAndList",
        props: {
            showLeft: {
                type: Boolean,
                default: true
            },
            leftWidth: {
                type: Number,
                default: 230
            }
        },
        computed: {
            widthPx() {
                const {showLeft, leftWidth} = this;
                if (showLeft && leftWidth > 0) {
                    return `${leftWidth}px`
                } else {
                    return 0;
                }
            },
            mainMarginPx() {
                const {showLeft, leftWidth} = this;
                if (showLeft && leftWidth > 0) {
                    return `${leftWidth + 18}px`
                } else {
                    return '10px';
                }
            }
        }
    }
</script>

<style scoped lang="less" src="./style.less"></style>