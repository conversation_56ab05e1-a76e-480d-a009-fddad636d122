<template>
    <div class="tree" v-loading="settings.loading">
        <div class="d-s-l-top">
            <el-input
                    size="mini"
                    placeholder="请输入名称搜索"
                    suffix-icon="el-icon-search"
                    v-model.trim="filterText"
            ></el-input>
        </div>
        <!-- <div class="d-v-l-head">
             <dg-button
                     v-if="rights.indexOf(rightOfAdd) > -1"
                     type="primary"
                     icon="el-icon-circle-check"
                     @click="empowerShow"
             >{{btnTxt}}</dg-button>
         </div>-->
        <!--<el-tabs v-model="activeName" type="border-card" class="ce-tabs_two mt5">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>-->
        <el-radio-group class="ce-radio_group" v-model="activeName">
            <el-radio-button class="ce-radio_btn" v-for="tab in tab_panel" :key="tab.name" :label="tab.name" >{{tab.label}}</el-radio-button>
        </el-radio-group>
        <div class="d-s-l-bottom" v-show="activeName === 'custom_dataset'">
            <div class="d-s-l-b-tree tree_cont" >
                <dg-tree
                        :data="data"
                        node-key="id"
                        :expand-on-click-node="false"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        :default-expand-all="true"
                        ref="tree"
                        :highlight-current="true"
                        @node-click="nodeClick"
                >
                    <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }"
                          @mouseenter="rightMenu($event , node , data)">

                        <span class="node-label" v-if="hasDataObj && node.level === 4" draggable="true"
                              @dragend="dragend()"
                              :title="node.label"
                              @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data  })"
                        >{{ node.label }}</span>
                        <span class="node-label" v-if="hasDataObj && node.level !== 4"
                                :title="node.label"
                        >{{ node.label }}</span>

                        <span class="node-label" v-if="!hasDataObj"
                              :title="data.label"
                              :class="{
                            'el-icon-folder' : !node.expanded && node.level <= 3 ,
                            'el-icon-folder-opened' : node.expanded && node.level <= 3,
                            'el-icon-document' : data.isParent
                          }"
                        >{{ data.label }}</span>

                        <span class="label__action"
                              v-if="data && data.pId !== '-1' && data.code !== 'DATASET_DIR_SHARE' && !hasDataObj ">
                            <dg-button type="text" :title="addBtnTxt" class="el-icon-plus b"
                                       @click.stop="addChildren($event , node , data)"
                                       v-if="data.code === 'DATASET_DIR_MY' || data.name ==='标准模型' "></dg-button>
                            <el-dropdown trigger="click" @command="menuCommand($event , node , data)" v-else-if="countHasRightMenu > 0">
                                <div class="el-dropdown-link" @click.stop>
                                    <i class="el-icon-more el-dropdown-link-rote"></i>
                                </div>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item v-for="(item , i ) in treeMenu" :key="i" :command="item"
                                                      v-if="item.show(rights)">
                                        <span class="drop-item">{{item.name}}</span>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </span>
                    </span>
                </dg-tree>
            </div>
        </div>

        <div class="d-s-l-bottom" v-show="activeName === 'source_dataset'">

            <!--<div class="div-refresh">
                <el-button size="mini" type="primary" @click="refreshSourceDataset()">刷新</el-button>
            </div>-->
            <div class="d-s-l-b-tree tree_cont">
                <dg-tree
                        :data="sourceDatasetTree"
                        node-key="id"
                        :expand-on-click-node="true"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        :default-expand-all="false"
                        ref="dgtree"
                        :default-expanded-keys="expandedKeys"
                        :highlight-current="true"
                        @node-click="dgNodeClick"
                >

               <span class="custom-tree-node el-tree-node__label" slot-scope="{ node, data }">
                    <span class="node-label" v-if="hasDataObj && node.level === 5" draggable="true"
                          @dragend="dragend()"
                          :title="node.label"
                          @dragstart="addNode( {label : node.label , icon : '#iconshuju_huaban' , data : data  })"
                    >{{ node.label }}</span>
               <span class="node-label" v-if="hasDataObj && node.level !== 5"
                        :title="node.label"
               >{{ node.label }}</span>
                        <span class="node-label"
                              v-if="!hasDataObj"
                              :title="node.label"
                              :class="{
                            'el-icon-folder' : !node.expanded && data.isParent ,
                            'el-icon-folder-opened' : node.expanded && data.isParent ,
                            'el-icon-document' : !data.isParent
                          }"
                        >{{ node.label }}</span>
               </span>
                </dg-tree>
            </div>
        </div>

    </div>

</template>

<script src="./tree.js"></script>
<style scoped lang="less" src="./tree.less"></style>