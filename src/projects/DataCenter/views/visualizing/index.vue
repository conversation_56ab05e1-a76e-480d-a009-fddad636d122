<template>
    <tree-and-list left-width="15.8%">
        <tree-card ref="treeCard" slot="left" :card-name="cardName" @filterData="filterTree">
            <tree  slot="tree"
                  ref="tree"
                  @getCenterTable="getTableData"
                  :has-save="false"
                  @addTreeDir="showAddDialog"
                  @reLoad="closeEditPanel"
            ></tree>
        </tree-card>

        <list ref="visual_table"
              @showPanel="openEditPanel"
              @changeNodeKey="changeNodeKey"
              :isCase="isCase"
        ></list>
        <div slot="other">
            <transition name="el-zoom-in-top">
                <VisualizationEdit ref="visual_panel" v-if="showEditPanel" @closePanel="closeEditPanel" :isCase="isCase" @issue="issue"/>
            </transition>
            <add-tree-dir ref="treeDir" v-loading="settings.loading" @addTreeDir="addTreeDir" >
                <tree slot="tree" ref="editTree" slot-scope="scope" v-bind="scope" @nodeClick="nodeClick" />
            </add-tree-dir>
        </div>
        
    </tree-and-list>
</template>

<script src="./visual.js"></script>
