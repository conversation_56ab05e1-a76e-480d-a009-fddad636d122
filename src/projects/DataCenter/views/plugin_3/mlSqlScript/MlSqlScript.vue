<template>
    <div class="MlSqlScript">

            <!--参数配置弹窗内容页-->
            <MlSqlEdit
                    ref="mlSqlEdit"
                    :saveScript="saveScript"
                    :mlSqlScript="mlSqlScript"
                    :stepTableName="stepTableName"
                    :transId="transId"
                    :stepId="stepId"
                    :stepTableColumns="stepTableColumns"
                    :stepTableSql="stepTableSql"
                    :loadParams="loadParams"
            ></MlSqlEdit>

     <!--   <common-dialog
                class="params-dialog"
                :width="width"
                :visible.sync="showEditOutputDialog"
                @closed="closeOutputEdit"
                v-loading="loading"
        >
            <div class="field-title" slot="title">
                <h3>设置输出字段</h3>
            </div>
            &lt;!&ndash;参数配置弹窗内容页&ndash;&gt;
            <MlSqlOutput ref="mlSqlOutput"
                         v-if="showMlSql"
                         :optionArr="optionArr"
                         :ruleTableData="ruleTableData"
                         :ruleTypeOptions="ruleTypeOptions"
                         :inputColumn="inputColumn"
                         @updateOutputData="updateOutputData"
            ></MlSqlOutput>
            &lt;!&ndash; 弹窗按钮&ndash;&gt;
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" style="margin-left: 750px" @click="saveOutputEdit">确 定</el-button>
                <el-button @click="closeOutputEdit">取 消</el-button>
            </span>
        </common-dialog>-->
        <preView ref="preview"></preView>
    </div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import MlSqlEdit from "./MlSqlEdit"
    import TaskLog from "../../modeling/flowPanel/TaskLog"
    import MlSqlOutput from "./MlSqlOutput"
    import preView from "@/projects/DataCenter/views/plugin/component/PreView"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {common} from "@/api/commonMethods/common"

    export default {
        name: "MlSqlScript",
        mixins : [pluginMixins , servicesMixins,common, commonMixins,],
        components: {
            MlSqlEdit,
            // TaskLog,
            // MlSqlOutput,
            preView
        },
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return{
                flag:false,
                inputColumn:[],
                showMlSql:false,
                title:'SQL脚本',
                showSqlScriptDialog:false,
                showEditOutputDialog:false,
                tableOption: [],
                defaultProps: {
                    value: 'id',
                    label: "label",
                    children: "children",
                },
                cacheTableId: '',
                groupId: '',
                stepId: '',
                transId: '',
                stepTableSql: '',
                stepTableColumns: [],
                isEda: false,
                canSave: false,
                isSeniorShow: {},
                results: [],
                addTxt: '点击添加字段处理',
                activeName: 'setting_base',
                ruleTableData: [],
                ruleTypeOptions: [
                    {}
                ],
                graphIds: [],
                row: {
                    columnCode: 'scriptMeta',
                    columnId: '',
                    columnName: 'scriptMeta',
                    length: '',
                    precision: '',
                    serviceOrgId: '',
                    type: ''
                },
                mlSqlScript: "",
                cacheTableList: [],
                stepTableName: "",
                ruleDataTmp:[],
                optionArr:[],
            }
        },
        methods: {
            updateOutputData(e){
                let array = e;
                this.ruleDataTmp = [];
                this.ruleTableData = [];
                array.forEach(item => {
                    this.ruleDataTmp.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        type : item.type,
                    });
                });
                this.ruleDataTmp.forEach(item =>{
                    this.ruleTableData.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        type : item.type,
                    });
                });
            },
            save(...args){
                this.saveFn(...args);
            },
            preview(){
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            showSqlScript(){
                this.showSqlScriptDialog = true;
                this.editLabelMark();
            },
            showOutputEdit(){
                this.showMlSql = true;
                this.showEditOutputDialog = true;
            },
            saveSqlScript(){
                return  this.$refs.mlSqlEdit.save();
                //this.showSqlScriptDialog = false;
            },
            closeSqlScript(){
                this.showSqlScriptDialog = false;
            },
            saveOutputEdit(){
                if (!this.$refs.mlSqlOutput.saveChanges()){
                    return;
                }
                this.showMlSql = false;
                this.showEditOutputDialog = false;
            },
            closeOutputEdit(){
                this.ruleTableData = [];
                this.ruleDataTmp.forEach(item => {
                    this.ruleTableData.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        type : item.type,
                    });
                });
                this.showMlSql = false;
                this.showEditOutputDialog = false
            },
            async editLabelMark(data) {
                const vm = this,{ settings} = this;
                this.stepTableColumns = [];
                this.stepTableName = '';
                this.stepTableSql = '';
                settings.loading = true;
                let res = await vm.$axios.get("/mlsql/getAfterTableName?id=" + vm.transId + "&stepId=" + vm.stepId);
                if (res.data.status === 0) {
                    this.stepTableName = res.data.data.tableName;
                    this.stepTableSql = res.data.data.sql;
                    if (res.data.data.columns !== null && res.data.data.columns.length > 0) {
                        this.stepTableColumns = res.data.data.columns.replace("[", "").replace("]", "").split(",");
                        let tableCol = [];
                        this.stepTableColumns.forEach(item => {
                            let replace = item.replace(/(^\s*)|(\s*$)/g, "");
                            tableCol.push(replace);
                        });
                        this.stepTableColumns = tableCol;
                        this.stepTableColumns.sort();
                    }
                }
                settings.loading = false;
                if (vm.mlSqlScript === '' || vm.mlSqlScript === undefined || vm.mlSqlScript === null) {

                }else {
                    this.$refs.mlSqlEdit.show(this.rowData);
                }

            },
            init(node) {
                const vm = this, {pluginServices, pluginMock, loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                loadParams.loading = true;
                vm.stepId = node.id;
                vm.transId = vm.rowData.transId;
                vm.editLabelMark();
                services.queryScriptData(node.id, loadParams).then(res => {
                    vm.inputColumn = res.data.data.inputColumn;
                    vm.results = res.data.data.inputColumn;
                    vm.results.forEach(item => {
                        if (item.columnName === null) {
                            item.columnName = item.columnCode;
                        }
                        item.value = item.columnName;
                    });
                    vm.mlSqlScript = res.data.data.mlSqlScript;
                    // let tableName = '';
                    // let params = {
                    //     "executeMode": "analyze",
                    //     "sql": vm.mlSqlScript,
                    //     "transStepId": vm.transId
                    // };
                    // this.$axios.post("/mlsql/runScript",params).then(res => {
                    //     if (res.data.status === 0) {
                    //         let resultData = JSON.parse(res.data.data);
                    //         resultData.forEach(item => {
                    //             if (item.inputTableName !== null && item.inputTableName !== undefined) {
                    //                 tableName = item.inputTableName;
                    //             } else if (item.tableName !== null && item.tableName !== undefined) {
                    //                 tableName = item.tableName;
                    //             }
                    //         });
                    //         services.getSchema(tableName).then(res => {
                    //             if (res.data.status === 0) {
                    //                 let array = [];
                    //                 array = JSON.parse(res.data.data);
                    //                 array.forEach((item, i) => {
                    //                     vm.optionArr.push({
                    //                         columnName: item.col_name,
                    //                         type: item.data_type,
                    //                         length: 200,
                    //                         precision: 0,
                    //                         columnCode: item.col_name,
                    //                         columnId: i,
                    //                     })
                    //                 });
                    //             }
                    //         });
                    //     }
                    // });

                    vm.ruleTypeOptions = res.data.data.types;
                    res.data.data.outputColumns.forEach((item, i) => {
                        vm.ruleTableData.push({
                            columnName: item.columnName,
                            type: item.type,
                            length: item.length,
                            precision: item.precision,
                            columnCode: item.columnCode,
                            columnId: item.columnId,
                        });
                    });
                    /*vm.ruleDataTmp = [];
                    vm.ruleTableData.forEach(item => {
                        vm.ruleDataTmp.push({
                            columnCode : item.columnCode,
                            columnId : item.columnId,
                            columnName : item.columnName,
                            length : item.length,
                            precision : item.precision,
                            type : item.type,
                        });
                    })*/
                });
                vm.ruleTableData.sort((a, b) => a.columnCode.charCodeAt(0) - b.columnCode.charCodeAt(0)); //a~z 排序
                services.getParameterTypes().then(res => {
                    vm.ruleTypeOptions = res.data.data;
                });
            },
            getOutId(type, serviceOrgId) {
                for (let k = 0; k < this.ruleTableData.length; k++) {
                    if (serviceOrgId === this.ruleTableData[k].ruleTableData) {
                        this.ruleTableData[k].type = type;
                    }
                }
            },
            getTableName(id, option) {
                for (let i = 0; i < option.length; i++) {
                    if (option[i].id === id) {
                        let tableInfo = {
                            tableId: id,
                            tableName: option[i].code
                        };
                        this.cacheTableList.push(tableInfo);
                    } else if (option[i].children !== null && option[i].children.length !== 0) {
                        this.getTableName(id, option[i].children);
                    }
                }
            },
            querySearch(queryString, cb) {
                let filterResult = queryString ? this.results.filter(this.createStateFilter(queryString)) : this.results;
                cb(filterResult);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            nameSelect(item, row) {
                console.log(item);
                row.length = item.length;
                row.precision = item.precision;
                row.columnName = item.columnName;
                row.type = this.ruleTypeOptions.filter(rule => {
                    return rule.label === item.type;
                })[0].value;
                row.columnCode = item.columnCode;
                row.columnId = item.columnId;
                row.serviceOrgId = item.serviceOrgId;
                // console.log(row);
            },
            async saveScript(script) {
                const vm = this;
                return new Promise( (resolve,reject) => {
                    vm.mlSqlScript = script;
                    if (script !== '' && script !== undefined) {
                        vm.getOutputColumn(this.stepTableSql + script).then(()=>{
                            resolve()
                        }).catch(()=>{
                            reject()
                        })
                        //vm.flag = true;
                    } else {
                        vm.$message.warning("请配置sql脚本！");
                        vm.flag = false;
                        resolve();
                    }
                })
            },
            async getOutputColumn(mlSqlScript) {
                let columnData = [];
                let tableName = null;
                const vm = this, {pluginServices, pluginMock, settings} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                settings.loading = true;
                let params = {
                    "executeMode": "analyze",
                    "sql": mlSqlScript,
                    "transStepId": vm.transId
                };
                await services.runScript(params).then(res => {
                    if (res.data.status === 0) {
                        if (res.data.data.indexOf("error") !== -1) {
                            vm.flag = false;
                            return this.$message.error("获取输出字段失败！");
                        }
                        let resultData = JSON.parse(res.data.data);
                        resultData.forEach(item => {
                            if (item.inputTableName !== null && item.inputTableName !== undefined) {
                                tableName = item.inputTableName;
                            } else if (item.tableName !== null && item.tableName !== undefined) {
                                tableName = item.tableName;
                            }
                        });
                        vm.flag = true;
                    } else {
                        vm.flag = false;
                        throw new Error(res.data.msg);
                    }
                });
                await services.getSchema(tableName).then(res => {
                    if (res.data.status === 0) {
                        vm.ruleTableData = [];
                        vm.optionArr = [];
                        columnData = JSON.parse(res.data.data);
                        columnData.forEach((item, i) => {
                            vm.ruleTableData.push({
                                columnName: item.col_name,
                                type: item.data_type,
                                length: 200,
                                precision: 0,
                                columnCode: item.col_name,
                                columnId: i,
                            })


                            vm.optionArr.push({
                                columnName: item.col_name,
                                type: item.data_type,
                                length: 200,
                                precision: 0,
                                columnCode: item.col_name,
                                columnId: i,
                            })

                        });
                        columnData = vm.ruleTableData;
                    } else  {
                        vm.flag = false;
                        throw new Error(res.data.msg);
                    }
                });
                await services.transStandardType(columnData).then(resultData => {
                    if (resultData.data.status === 0) {
                        vm.ruleTableData = [];
                        resultData.data.data.forEach((item, i) => {
                            vm.ruleTableData.push({
                                columnName: item.columnName,
                                type: item.type,
                                length: item.length,
                                precision: item.precision,
                                columnCode: item.columnName,
                                columnId: item.columnId,
                            })
                        })
                    }
                })
                for (let i = 0; i < this.ruleTableData.length; i++) {
                    for (let j = 0; j < this.inputColumn.length; j++) {
                        if (this.ruleTableData[i].columnCode === this.inputColumn[j].columnCode){
                            this.ruleTableData[i].type = this.inputColumn[j].type;
                            this.ruleTableData[i].length = this.inputColumn[j].length;
                            this.ruleTableData[i].precision = this.inputColumn[j].precision;
                            /*if (this.inputColumn[j].columnName !== ''||this.inputColumn[j].columnName !== null || this.inputColumn[j].columnName!==undefined){
                                this.ruleTableData[i].columnName = this.inputColumn[j].columnName;
                            }*/
                        }
                    }
                }
                this.ruleTableData.sort((a, b) => a.columnCode.charCodeAt(0) - b.columnCode.charCodeAt(0)); //a~z 排序
                settings.loading = false;
                /*vm.ruleDataTmp = [];
                vm.ruleTableData.forEach(item => {
                    vm.ruleDataTmp.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        type : item.type,
                    });
                })*/
            },

            async runScript(){
                this.settings.loading = true;
                await this.$refs.mlSqlEdit.runScript();
                clearTimeout(this.timer);
                this.timer = setTimeout(()=>{
                    this.settings.loading = false;
                },1000);

            },
            async saveFn(saveName,showTip) { //保存后才能预览
                const vm = this, {pluginServices, pluginMock,loadParams} = this;
                loadParams.loading = true;

                try {
                    await vm.saveSqlScript();
                    if (!vm.flag) {
                        loadParams.loading = false;
                        return;
                    }
                    let services = vm.getServices(pluginServices, pluginMock);
                    for (let k = 0; k < vm.ruleTableData.length; k++) {
                        for (let key in vm.ruleTableData[k]) {
                            if (key !== 'serviceOrgId' && key !== 'columnCode' && (vm.ruleTableData[k][key] === ''|| vm.ruleTableData[k][key] === undefined || vm.ruleTableData[k][key] === null)) {
                                if (key === 'columnName') {
                                    vm.$message.warning("输出字段不能为空!");
                                    return;
                                } else if (key === 'type') {
                                    vm.$message.warning("输出类型不能为空!");
                                    return;
                                } else if (key === 'length') {
                                    vm.$message.warning("长度不能为空!");
                                    return;
                                }
                            }
                        }
                    }
                    /* if (!vm.flag){
                         vm.$message.warning("请配置正确的sql语句!");
                         return;
                     }*/

                    let serviceOrgOutputs = [];
                    vm.ruleTableData.forEach((item, i) => {
                        let serviceOrgOutput = {
                            columnName: item.columnName,
                            type:vm.getDateType(item.type),
                            length: item.length,
                            precision: item.precision,
                            columnCode: (item.columnCode === '' || item.columnCode === null) ? item.columnName : item.columnCode,
                            columnId: item.columnId,
                        };
                        serviceOrgOutputs.push(serviceOrgOutput)
                    });
                    let scriptMeta = {
                        mlSqlScript: vm.mlSqlScript,
                        cicadaScriptOutputs: serviceOrgOutputs
                    };


                    services.saveScriptExp(vm.sourceData.id, scriptMeta, loadParams).then(res => {
                        if (res.data.status === 0) {
                            showTip && this.$message.success("保存成功!");
                            if(saveName)saveName();
                            vm.$emit("setPreviewAble");
                        }
                    })
                }  finally {
                    loadParams.loading = false;
                }
            },
            getDateType(type){
                this.ruleTypeOptions.forEach(rule => {
                    if(rule.value === type ){
                        type = rule.label;
                    }
                });
                return type;
            },
        },
        created() {
            this.init(this.sourceData);

        }
    }
</script>

<style scoped>
    .ce-normal_item.is-require::before , .ce-common_item.is-require::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }
    .field-title {
        display: flex;
        align-items: center;
    }
    h3 {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
    }
</style>
<style>
    .ce-disabled_model .ace_editor .ace_scrollbar, .ce-disabled_model .MlSqlScript .el-textarea{
        opacity: 1;
        pointer-events: all;
        cursor: auto;
    }
</style>
