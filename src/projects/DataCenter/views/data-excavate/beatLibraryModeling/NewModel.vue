<template>
    <div class="div_main">
        <div class="ce-panel_title-bg">
            <div class="ce-panel_title"><i class="icon pr6">&#xe604;</i>
                <input disabled @keyup="titleChange(title)"  ref="title"  maxlength="30" class="ce-visual_title" type="text" v-model="title"></div>
            <div class="ce-buttons_group">
                <el-button v-for="btn in btns" :key="btn.txt" class="ce-btn" :type="btn.type" size="mini"
                           @click="btn.fn">{{btn.txt}}
                </el-button>
            </div>
        </div>
        <div class="right" ref="divRight">
            <el-input
                    type="textarea"
                    :rows="4"
                    resize="none"
                    placeholder="请输入SQL语句"
                    v-model="textarea"
                    class="ta_selet_content"
            ></el-input>
            <div ref="getTableH" class="getTableH" >
                <ModelingCenterTable ref="modelingCenterTable" class="right_table"/>
            </div>

            <div class="div_bottom">
                <el-select v-model="value" placeholder="缓存级别" class="el_select_value" size="mini">
                    <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    ></el-option>
                </el-select>
                <el-button type="primary" size="mini" class="btn" @click="btnAnalyze">分析</el-button>
                <el-button type="primary" size="mini" class="btn" @click="btnDataSetEvent">Spark注册表</el-button>
                <el-button type="primary" size="mini" class="btn" @click="query(textarea)">预览数据集</el-button>
                <el-button type="primary" size="mini" class="btn" @click="btnSaveEvent">保存</el-button>
            </div>
        </div>
        <el-dialog
                title="预览数据"
                :visible.sync="preview.isVisible"
                :close-on-click-modal="false"
                top="10vh"
                width="1000px"
                @closed="reSetLoading"
                append-to-body
        >
            <div style="margin-bottom: 5px">
                <el-row>
                    <el-col style="margin-right: 5px" :span="5"><el-input @input="changeNum" v-model="tableSize" size="mini" placeholder="请输入预览数量"></el-input></el-col>
                    <el-col :span="5"><el-button type="primary" @click="query" size="mini">刷新</el-button></el-col>
                </el-row>
            </div>
            <transition name="el-fade-in-linear" >
                <keep-alive>
                    <common-table v-if="preview.isVisible"
                                  :data="preview.list"
                                  :columns="preview.columns"
                                  v-loading="loading"
                                  :highlight-current-row="true"
                                  cell-class-name="ce-ellipsis"
                                  tooltip-effect="light"
                                  height="398px"
                    >
                    </common-table>
                </keep-alive>
            </transition>
        </el-dialog>
        <el-dialog
                :visible.sync="dialogVisible"
                destroy-on-close
                fullscreen
                append-to-body
                title="Spark注册表"
        >
<!--        <ExistingDataSet />-->
        <PreviewSparkTable />
        </el-dialog>
        <el-dialog
                custom-class="ce-prompt"
                append-to-body
                title="提示"
                :visible.sync="isExist"
                width="30%"
               >
            <span>{{this.info}}</span>
            <span slot="footer" class="dialog-footer">
    <el-button size="small" @click="isExist = false">取 消</el-button>
    <el-button size="small" type="primary" @click="save">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    import ModelingCenterTable from "./ModelCenterTable";
    import PreviewSparkTable from './PreviewSparkTable';
    // import ExistingDataSet from './ExistingDataSet';
    import CommonTable from '@/components/common/CommonTable'

    export default {
        name: "NewModel",
        components: {
            ModelingCenterTable,
            // ExistingDataSet,
            PreviewSparkTable,
            CommonTable
        },
        props: {
            crumb: Array,
            rowData:Object,
        },
        data() {
            return {
                tableSize: 10,
                loading:false,
                preview: {
                    isVisible: false,
                    list: [],
                    columns: [],
                },
                isExist:false,
                info:"",
                dialogVisible:false,
                options: [
                    {
                        value: "DISK_ONLY",
                        label: "硬盘"
                    },
                    {
                        value: "MEMORY_ONLY",
                        label: "内存"
                    },
                    {
                        value: "NONE",
                        label: "无缓存"
                    }
                ],
                value: "NONE",
                inputValueTree: "",
                textarea: "" ,
                btns: [
                    {
                        txt: '返回',
                        type: '',
                        fn: this.backFn
                    }
                ],
                title : ""
            };
        },
        methods: {
            changeNum(){
                // this.tableSize = this.tableSize.replace(/^[1-9]\d*$/g,"");
                // this.tableSize = this.tableSize.replace(/^((?!^[1-9]\d*$).)*/g,"");
                let strs = this.tableSize.match(/./g);
                // let reg = /^[1-9]\d*$/g;
                strs.forEach(item =>{
                    if(isNaN(item)){
                        this.tableSize = this.tableSize.replace(item,'');
                    }
                })


            },
            query(val){
                let sql =  "";
                if(this.textarea.indexOf("limit") > -1){
                    if(typeof(val) === "string"){
                        sql = val;
                    }else{
                        let temSql = this.textarea.substring(0,this.textarea.indexOf("limit")+5);
                        sql = temSql + " " +this.tableSize;
                    }

                }else{
                    if(typeof(val) === "string"){
                        this.tableSize = 10;
                    }
                    sql = this.textarea + " limit " + this.tableSize;

                }

                this.btnPreviewEvent(sql);
            },
            save(){
                const _this = this;
                let table = this.$refs.modelingCenterTable.getTableData();
                if(this.textarea === null || this.textarea === ''){
                    this.$message.warning("请先输入SQL语句！");
                    this.isExist = false;
                    return;
                }
                let resqustData = [];

                table.forEach(item =>{
                    // let disp = _this.$refs.modelingCenterTable.getAdaptiveControl(item.adaptiveControl);
                    resqustData.push(
                        {
                            'fieldName':item.name,
                            'dataType':item.type,
                            'dataTypeId':item.dataTypeId,
                        }
                    /*{
                        'fieldName':item.name,
                        'disp':disp.label,
                        'dispId':disp.value,
                        'dataType':item.type,
                        'dataTypeId':item.dataTypeId,
                        'codeMapId':item.encodeSetting,
                    }*/
                    )
                });

                let data = {
                    cacheLevel:this.value,
                    sql:this.textarea,
                    colDispTypeList:JSON.stringify(resqustData),
                    sqlObjId:this.rowData.id,
                };
                this.$axios.post("/manage/sql/saveSqlObj",data).then(res =>{
                    if(res.data.status === 0){
                        this.$message.success("保存成功！");
                    }else {
                        this.$message.error("保存失败！");
                    }
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                });
                this.isExist = false;
            },
            titleChange(val){
                this.$refs.title.style.minWidth = val.length * 16 + 'px';
                if(val.length >= 30){
                    this.$message.info('标题限30个字符以内!')
                }
            },
            backFn(){
                this.$emit('closePlane');
            },
            handleSizeChange(val) {
                this.$refs.modelingCenterTable.refPageChange(val);
                //加载数据
            },
            handleCurrentChange(val) {
                this.$refs.modelingCenterTable.refPageChange(val);
                //加载数据
            },
            searchTreeEvent() {
                this.$message("tree");
            },
            changeTableData(vals){
                vals.sqlDataObjColumns.forEach((item,i) =>{
                    let index = 1;
                    vals.sqlDataObjColumns.forEach((val,j) =>{
                       if(item.colName === val.colName && i != j ){
                            val.colName += index++;
                        }
                    })
                });
                this.$refs.modelingCenterTable.setTableData(vals);
            },
            btnAnalyze(){
                const _this = this ;
                if(this.textarea === null || this.textarea === ''){
                    this.$message.warning("请先输入SQL语句！");
                    return;
                }
                if(this.value === null){
                    this.$message.warning("请选择缓存！");
                    return;
                }
                let query = "query";
                this.$axios.get("/manage/sql/sqlEditPage?type="+query+"&id="+this.rowData.id+"&sql="+this.textarea+"&cacheLevelId="+this.value+"&savedSql="+this.textarea).then(res =>{
                    if(res.data.status === 0){
                        if(res.data.data.sqlDataObjColumns === null){
                            this.$message.warning("查询数据为空，可能连接出现异常！");
                        }else{
                            if(_this.checkCol(res.data.data)){
                                _this.$message.warning("分析结果中存在相同字段名称，请使用别名区分！");
                                return ;
                            }else{
                                this.$refs.modelingCenterTable.setTableData(res.data.data)
                            }
                            // _this.changeTableData(res.data.data);

                        }
                    }
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                })
            },
            btnDataSetEvent(){
                this.dialogVisible = true;
            },
            btnPreviewEvent(val){
                if(val === null || val === ""){
                    this.$message.warning("请输入SQL语句！");
                    return ;
                }
                let data = {
                  sql:val,
                };
                const _this = this;
                this.$axios.post("/manage/sql/previewData?sql",data).then(res =>{
                    console.log(res);
                    if(res.data.status === 0){
                        let resData = res.data.data;//集合
                        if(resData !== null){
                            // 渲染表头数据
                            _this.preview.columns == [];
                            _this.tableSize = resData.length;
                            for (let key in resData[0]){
                                _this.preview.columns .push({
                                    prop : key ,
                                    label : key
                                });
                            }
                            _this.preview.list = [];
                            //渲染表格数据
                            resData.forEach(item =>{
                                _this.preview.list.push(item);
                            }) ;
                        }
                        this.loading = false;
                        this.preview.isVisible = true;
                    }
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                });

            },
            checkCol(vals){
                let hash = {} ;
                let flag = false;
                vals.sqlDataObjColumns.forEach(item =>{
                    if(hash[item.colName] === undefined){
                        hash[item.colName] = item.colName
                    }else {
                        flag = true;
                    }
                });
                return flag;
            },
            btnSaveEvent() {
                if(this.value === null ){
                    this.$message.warning("请选择缓存方式！");
                    return;
                }
                if(this.textarea.trim() === ""){
                    this.$message.warning("请输入SQL语句！");
                    return;
                }

                this.$axios.get("manage/sql/isExistInFastCompare").then(res =>{
                    if(res.data.data === false){
                        this.info = "是否保存?";
                    }else {
                        this.info = "保存操作会清空快速比对方案配置，是否继续?";
                    }
                    this.isExist = true;
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                });

            },
            getTableHeight() {
                //if(!this.$refs.getTableH) return ;
                this.$nextTick(()=>{
                    let tableHeight = this.$refs.getTableH.clientHeight;
                    this.$refs.modelingCenterTable.resizeTableHeight(tableHeight);
                })

            },
            countHeight(){
                let $this = this;
                let resizeTimer = null;
                if(resizeTimer) clearTimeout(resizeTimer);
                resizeTimer = setTimeout(() =>{
                    $this.getTableHeight();
                },300)
            },
            editSqlPage(){
                this.$axios.get("manage/sql/sqlEditPage?id="+this.rowData.id).then(res =>{

                    if(res.data.status === 0){
                        this.textarea = res.data.data.savedSql ? res.data.data.savedSql : "" ;
                        if(res.data.data.cacheLevelId !== null ){
                            this.value = res.data.data.cacheLevelId;
                        }
                        this.$refs.modelingCenterTable.setTableData(res.data.data)
                    }
                }).catch(err =>{
                    console.log(err);
                    this.$message.error("服务器异常，请联系管理员！");
                })
            },
            reSetLoading(){
                this.loading = true;
                this.preview.columns = [];
                this.preview.list = [];
            },

        },
        created(){
            this.title = this.rowData.name;
            this.editSqlPage();
        },
        mounted() {
            this.getTableHeight();
            window.addEventListener(
                "resize", this.countHeight, false
            );
        },
        beforeDestroy() {
            //解绑 resize
            window.removeEventListener("resize", this.countHeight);
        },

    };
</script>
<style scoped lang="less">

    @textH : 96px ;
    @tableMt : 10px;
    @btnH : 30px;
    .div_main {
        position: fixed;
        left: 0;
        top: 50px;
        width: 100%;
        height:calc(100% - 50px);
        overflow: hidden;
        background: #f5f5f5;
        z-index: 30;
    }

    .right {
        background: #fff;
        margin: 10px;
        padding: 10px;
        height: calc(100% - 88px);
        border: 1px solid #ddd;
    }

    .right_table {
        margin-top: 10px;
    }

    /*.div_main /deep/ .el-textarea__inner {
        max-height: 96px;
    }*/

    .div_bottom {
        position: absolute;
        left: 10px;
        bottom: @btnH;
    }

    .ta_selet_content {
        /*margin-top: 5px;*/
        height: @textH;
    }

    .el_select_value {
        /*float: left;*/
        display: inline-block;
        width: 100px;
        margin-left: 10px;
    }

    .btn {
        /*float: left;*/
        margin-left: 10px;
    }
    .getTableH {
        height: calc(100% - @textH - @btnH - @btnH);
    }
    .ce-panel_title-bg {
        overflow: hidden;
        background: #fafafa;
        border-bottom: 1px solid #ddd;
    }

    .ce-panel_title {
        line-height: 40px;
        float: left;
        color: @font-color;
        font-size: 16px;
        padding: 0 16px;
    }
    .ce-buttons_group {
        float: right;
        padding: 5px 10px 0 0;
    }
    .ce-visual_title {
        border:none;
        outline: none;
        border-bottom: 1px solid #ccc;
        background: none;
        color: #289bf1;
    }
</style>

