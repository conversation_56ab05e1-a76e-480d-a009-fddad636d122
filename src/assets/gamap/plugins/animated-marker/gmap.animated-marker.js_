GMap.AnimatedMarker=GMap.Marker.extend({options:{distance:200,interval:1e3,autoStart:!0,onEnd:function(){},clickable:!1},initialize:function(t,i){this.setLine(t),GMap.Marker.prototype.initialize.call(this,t[0],i)},_chunk:function(t){var i,n=t.length,s=[];for(i=1;i<n;i++){var a=t[i-1],o=t[i],e=a.distanceTo(o),l=this.options.distance/e,h=l*(o.lat-a.lat),r=l*(o.lng-a.lng);if(e>this.options.distance)for(;e>this.options.distance;)a=new GMap.LatLng(a.lat+h,a.lng+r),e=a.distanceTo(o),s.push(a);else s.push(a)}return s.push(t[n-1]),s},onAdd:function(t){GMap.Marker.prototype.onAdd.call(this,t),this.options.autoStart&&this.start()},animate:function(){var t=this,i=this._latlngs.length,n=this.options.interval;this._i<i&&this._i>0&&(n=this._latlngs[this._i-1].distanceTo(this._latlngs[this._i])/this.options.distance*this.options.interval),GMap.DomUtil.TRANSITION&&(this._icon&&(this._icon.style[GMap.DomUtil.TRANSITION]="all "+n+"ms linear"),this._shadow&&(this._shadow.style[GMap.DomUtil.TRANSITION]="all "+n+"ms linear")),this.setLatLng(this._latlngs[this._i]),this._i++,this._tid=setTimeout(function(){t._i===i?t.options.onEnd.apply(t,Array.prototype.slice.call(arguments)):t.animate()},n)},start:function(){this.animate()},stop:function(){this._tid&&clearTimeout(this._tid)},setLine:function(t){GMap.DomUtil.TRANSITION?this._latlngs=t:(this._latlngs=this._chunk(t),this.options.distance=10,this.options.interval=30),this._i=0}}),GMap.animatedMarker=function(t,i){return new GMap.AnimatedMarker(t,i)};