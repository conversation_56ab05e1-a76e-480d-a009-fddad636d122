<template>
    <div>
        <div v-if="methodCode==='postagOperator'||methodCode==='stopwordDelete'">
            <postag-and-stop-word-delete-operator-table
                    :ref="methodCode"
                    :params="params"
                    :methodCode="methodCode"
                    :showData="showData"
                    @checkDataType="checkDataType"
            ></postag-and-stop-word-delete-operator-table>
        </div>
        <div v-else-if="methodCode==='wordFilter'">
            <word-filter-operator-table
                    :ref="methodCode"
                    :params="params"
                    :methodCode="methodCode"
                    :showData="showData"
                    @checkDataType="checkDataType"
            ></word-filter-operator-table>
        </div>

        <!-- JSON类型 -->
        <div v-else-if="methodCode === 'AviatorStringOperator.split'||methodCode === 'AviatorCollectionOperator.sort'||methodCode === 'AviatorCollectionOperator.filter'">
            <el-table
                    height="188px"
                    :data="paramTableData"
                    style="width: 100%"
                    size="small"
                    cell-class-name="ec-text__center"
                    header-cell-class-name="ec-text__center"
            >
                <el-table-column
                        v-for="param in params"
                        :key="param.code"
                        :label="param.name"
                        :prop="param.code"
                >
                    <template slot-scope="scope">
                        <el-input size="small"
                                  v-model="scope.row[param.code]">
                            <i slot="suffix" class="el-input__icon el-icon-circle-plus hover-i"
                               @click="showJSONTable"></i>
                        </el-input>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!--以下是通用表-->
        <div v-else>
            <el-table
                    height="188px"
                    :data="paramTableData"
                    style="width: 100%"
                    size="small"
                    cell-class-name="ec-text__center"
                    header-cell-class-name="ec-text__center"
                    border
            >
                <el-table-column
                        v-for="param in params"
                        :key="param.code"
                        :label="param.name"
                        :prop="param.code"
                >
                    <template slot-scope="scope">

<!--                        <el-select-->
<!--                                v-if="param.code==='zipperTableName'"-->
<!--                                size="small"-->
<!--                                v-model="scope.row[param.code]"-->
<!--                                placeholder="请选择"-->
<!--                                filterable-->
<!--                        >-->
<!--                            <el-option-->
<!--                                    v-for="item in tableList"-->
<!--                                    :key="item.tableId"-->
<!--                                    :label="item.tableName"-->
<!--                                    :value="item.tableId">-->
<!--                            </el-option>-->
<!--                        </el-select>-->

                        <el-select
                                v-if="notStringParam.indexOf(param.code)!==-1"
                                size="small"
                                v-model="scope.row[param.code]"
                                placeholder="请选择"
                                filterable
                        >
                            <el-option
                                    v-for="item in newFeatures"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>

                        <el-input
                                v-if="checkDataType(param.dataType) === 'String' && param.name!=='条件表达式'&& notStringParam.indexOf(param.code)===-1"
                                size="small"
                                v-model="scope.row[param.code]">
                        </el-input>

                        <el-input v-if="checkDataType(param.dataType) === 'number'"
                                  size="small"
                                  type="number"
                                  v-model="scope.row[param.code]">
                        </el-input>

                        <el-input v-if="param.name=='条件表达式'"
                                  size="small"
                                  type="text"
                                  placeholder="请输入表达式"
                                  v-model.trim="scope.row[param.code]">
                            <i class="el-icon-edit el-input__icon"
                               slot="suffix"
                               @click="editLabelMark(scope.row)">
                            </i>
                        </el-input>

                        <el-switch
                                v-if="checkDataType(param.dataType) === 'boolean'"
                                active-text="是"
                                inactive-text="否"
                                v-model="scope.row[param.code]">
                        </el-switch>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <AviatorStringOperatorSplitTable v-if="methodCode === 'AviatorStringOperator.split'" :newFeatures="newFeatures"
                                         :ref="methodCode"
                                         :params="params"
                                         :methodCode="methodCode"
                                         :showData="showData"
                                         @checkDataType="checkDataType"
                                         @submit="getJsonnData"/>
        <AviatorCollectionOperatorSortTable v-else-if="methodCode === 'AviatorCollectionOperator.sort'"
                                            :newFeatures="newFeatures"
                                            :ref="methodCode"
                                            :params="params"
                                            :methodCode="methodCode"
                                            :showData="showData"
                                            @checkDataType="checkDataType"
                                            @submit="getJsonnData"/>
        <AviatorCollectionOperatorFilterTable v-else-if="methodCode === 'AviatorCollectionOperator.filter'"
                                              :newFeatures="newFeatures"
                                              :ref="methodCode"
                                              :params="params"
                                              :methodCode="methodCode"
                                              :showData="showData"
                                              @checkDataType="checkDataType"
                                              @submit="getJsonnData"/>
    </div>
</template>

<script>

    import PostagAndStopWordDeleteOperatorTable from './specialTable/PostagAndStopWordDeleteOperatorTable'
    import WordFilterOperatorTable from './specialTable/WordFilterOperatorTable'
    import AviatorStringOperatorSplitTable from './specialTable/AviatorStringOperatorSplitTable'
    import AviatorCollectionOperatorSortTable from './specialTable/AviatorCollectionOperatorSortTable'
    import AviatorCollectionOperatorFilterTable from './specialTable/AviatorCollectionOperatorFilterTable'

    export default {
        name: "ParamTablePage",
        components: {
            PostagAndStopWordDeleteOperatorTable,
            WordFilterOperatorTable,
            AviatorStringOperatorSplitTable,
            AviatorCollectionOperatorSortTable,
            AviatorCollectionOperatorFilterTable
        },
        props: {
            newFeatures: Array,
            params: Array,
            methodCode: String,
            showData: Object,
            tableList: Array
        },
        data() {
            return {
                paramTableData: [],
                selectVal:[],
                isLabelMark: false,
                notStringParam: ['zipperStatus', 'zipperTime', 'zipperTableName','zipperGroupName','groupKey','operateKey','reduction','minuend','addend'],
            }
        },

        methods: {
            init() {
                let vm = this;
                let ref = vm.$refs[vm.methodCode];

                if (vm.checkMethodCode(vm.methodCode) || ref === undefined) {
                    if (!vm.showData.method) {
                        vm.paramTableData = [];
                        let paramProps = {};
                        vm.params.forEach(v => {
                            let code = v.code;
                            paramProps[code] = '';
                        });
                        vm.paramTableData.push(paramProps)
                    } else {
                        vm.paramTableData = [];
                        let table = {};
                        if (vm.methodCode === vm.showData.method) {
                            for (let paramCode in vm.showData.features[0].paramValues) {
                                table[paramCode] = vm.showData.features[0].paramValues[paramCode];
                            }
                            vm.paramTableData.push(table);
                        } else {
                            vm.params.forEach(v => {
                                let code = v.code;
                                table[code] = '';
                            });
                            vm.paramTableData.push(table)
                        }
                    }
                } else {
                    ref.init();
                }
            },
            //json的methodCode需要加入校验
            checkMethodCode(code) {
                let codeList = [
                    'AviatorStringOperator.split',
                    'AviatorCollectionOperator.sort',
                    'AviatorCollectionOperator.filter',
                ];
                if (codeList.indexOf(code) != -1) {
                    return true;
                } else {
                    return false;
                }
            },
            editLabelMark(data) {
                this.$dgPrompt('请输入条件表达式', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    //  inputValidator: , //校验函数
                    inputErrorMessage: '表达式格式不正确',
                    inputValue: data.expression,
                    inputType:"textarea"
                }).then(({value}) => {
                    data.expression = value;
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '取消输入'
                    });
                });
            },

            checkDataType(dataType) {
                dataType = this.checkDataType1(dataType);
                if (dataType === 'long' || dataType === 'double' || dataType === 'java.lang.Double') {
                    return 'number';
                } else if (dataType === 'boolean') {
                    return 'boolean';
                } else if (dataType === 'java.lang.String' || dataType === 'String') {
                    return 'String';
                }else if(dataType === 'Date'){
                    return 'Date';
                } else {
                    return 'String';
                }
                // return this.$emit('checkDataType', dataType);
            },
            checkDataType1(dataType) {
                if (dataType === 'double' || dataType === 'float') {
                    return 'double';
                } else if (dataType === 'long' || dataType === 'int' || dataType === 'byte') {
                    return 'long';
                } else if (dataType === 'boolean') {
                    return 'boolean';
                } else if (dataType === 'java.lang.String' || dataType === 'String' || dataType === 'string') {
                    return 'String';
                }
                return dataType;
                // return this.$emit('checkDataType1', dataType);
            },


            /**
             * JSON类型表格
             */
            showJSONTable() {
                let vm = this;
                let ref = vm.$refs[vm.methodCode];
                ref.init(vm.paramTableData[0]["json"]);
            },

            getJsonnData(data) {
                let vm = this;
                vm.paramTableData[0]["json"] = data;
            },

            /**
             * 提供给外部获取参数表格数据
             */
            getParamTableData() {
                let vm = this;
                let ref = vm.$refs[vm.methodCode];
                if (vm.paramTableData[0]["json"] !== undefined || ref === undefined) {
                    return vm.paramTableData;
                } else {
                    return ref.getChildrenParamTableData();
                }
            }
        },
        watch: {
            params: {
                handler() {
                    this.init();
                },
                deep: true
            },
            methodCode: {
                handler() {
                    this.init();
                },
                deep: true
            },
            showData: {
                handler() {
                    this.init();
                },
                deep: true
            },
            paramTableData: {
                handler() {

                }
            }
        },
        created() {
            this.init();
        },
    }
</script>
<style src="../css/param.css" scoped></style>
<style scoped>
    .labelMark {
        position: relative;
        height: calc(100% - 110px);
        overflow: auto;
        padding: 0px;
        width: 100px;
    }

    .labelMark0 {
        position: relative;
        height: calc(100% - 110px);
        overflow: auto;
        padding: 0px;

    }
</style>
