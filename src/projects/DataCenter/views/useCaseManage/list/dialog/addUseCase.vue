<template>
    <div class="" v-loading="settings.loading">
        <span class="labelC">
            {{ baseInfoText }}
        </span>
        <el-form ref="form" :model="form" label-position="right" label-width="150px">
            <el-form-item
                    v-for="(item , k) in layoutForm"
                    :key="k"
                    :label="`${item.label}:`"
                    :prop="k"
                    :rules="item.rules"
                    size="mini"
            >
                <el-input v-if="item.type === 'input'"
                          size="mini"
                          v-model.trim="form[k]"
                          :maxlength="31"
                          :placeholder="item.placeholder"></el-input>
                <el-input v-if="item.type === 'textarea'"
                          size="mini"
                          type="textarea"
                          v-model.trim="form[k]"
                          :maxlength="255"
                          :placeholder="item.placeholder"></el-input>
                <ce-select-drop
                        v-if="item.type === 'selectTree'"
                        ref="pos_tree"
                        :placeholder="item.placeholder"
                        :props="defaultProps"
                        filterable
                        clearable
                        check-strictly
                        visible-type="leaf"
                        :filterNodeMethod="filterNode"
                        v-model="form[k]"
                        :data="item.option"
                ></ce-select-drop>
            </el-form-item>
        </el-form>
        <span class="labelC">
            {{ indexText }}
        </span>
        <!-- <el-form ref="indexForm" :model="indexForm" label-position="right" label-width="150px"> -->
        <div class="add-table">
            <!-- row-key="codeId" 避免id重复报错-->
            <common-table
                    ref="list"
                    height="100%"
                    row-key="codeId"
                    :pagination="false"
                    :data="tableData"
                    :columns="tHeadData"
            >
                <template slot="operate" slot-scope="{row , $index}">
                            <span class="p5" v-for="(col , inx) in operateIcons"
                                  :key="inx">
                                <el-popconfirm
                                        v-if="col.type === 'delete'"
                                        title="确认删除?"
                                        @confirm="col.clickFn(row,$index)"
                                >
                                    <el-button type="text"
                                               slot="reference"
                                               :title="col.tip">
                                        <span v-html="col.tip"></span>
                                    </el-button>
                                </el-popconfirm>
                                <el-button type="text"
                                           v-else
                                           :title="col.tip"
                                           @click="col.clickFn(row,$index)">
                                    <span v-html="col.tip"></span>
                                </el-button>

                            </span>
                </template>
            </common-table>
        </div>
        <el-button type="text" @click="addApi">添加API</el-button>
        <!-- </el-form> -->
        <span v-footer v-show="!settings.loading">
            <dg-button @click="close">{{ btnCancelTxt }}</dg-button>
            <dg-button type="primary" @click="submit">{{ btnCheckTxt }}</dg-button>
        </span>
    </div>
</template>
<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import {treeMethods} from "dc-front-plugin/src/api/treeNameCheck/treeName";
import {common} from "dc-front-plugin/src/api/commonMethods/common";
import {RequestDetail} from "./requestDetail";

export default {
    name: "addUseCase",
    mixins: [commonMixins, common, RequestDetail],
    props: {
        row: Object,
        currentNodeId: String,
    },
    components: {},
    data() {
        return {
            detailInfo: null,
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "API名称",
                    align: 'left'
                }, {
                    prop: "state",
                    label: "服务状态",
                    align: 'left'
                }, {
                    label: "操作",
                    prop: "operate",
                    align: "center",
                }
            ],
            operateIcons: [
                {
                    icon: "&#xe6f4;",
                    tip: "设置参数",
                    type: "copy",
                    clickFn: this.setParam
                },
                {
                    icon: "&#xe6f4;",
                    tip: "复制",
                    type: "copy",
                    clickFn: this.copyVar
                },
                {
                    icon: "&#xe65f;",
                    tip: "删除",
                    type: "delete",
                    clickFn: this.deleteVar
                },
            ],
            baseInfoText: "基本信息",
            indexText: "API测试",
            defaultProps: {
                value: 'id',
                label: "name",
                children: "children",
                disabled: "disabled",
            },
            form: {
                useCaseName: "",
                classifyId: "",
                memo: "",
            },
            layoutForm: {
                useCaseName: {
                    label: "用例名称",
                    type: 'input',
                    placeholder: '请输入用例名称',
                    option: [],
                    rules: [
                        {required: true, message: '请输入用例名称', trigger: ['change']}
                    ]
                },
                classifyId: {
                    label: "保存到目录",
                    type: 'selectTree',
                    placeholder: '请选择目录',
                    option: [],
                    rules: [
                        {required: true, message: '请选择目录', trigger: ['change']}
                    ]
                },
                memo: {
                    label: "用例描述",
                    type: 'textarea',
                    placeholder: '请输入用例描述',
                    option: [],
                    rules: [
                        {required: false, message: '请输入用例描述', trigger: ['change']}
                    ]
                },
            },
            indexForm: {
                atomIndexId: "",
            },
            indexLayoutForm: {},
            apiTreeInfo: null,
            userCaseParam: {},
            useCaseApiParams: [],
        }
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        submit() {
            const vm = this , {settings , tableData ,form , detailInfo} = vm;
            this.$refs.form.validate(valid => {
                if (valid) {
                    settings.loading = true;
                    let params = !!detailInfo ? [tableData, form , detailInfo , settings] :[tableData, form , settings];
                    vm.$emit('submit', ...params);
                }
            })
        },
        setParam(row) {
            const vm = this;
            let layer = vm.$dgLayer({
                title: "设置参数",
                content: require("./params.vue"),
                maxmin: false,
                move: false,
                props: {
                    row: row,
                },
                area: ["1200px", "720px"],
                on: {
                    submit(requestData , params) {
                        row.paramObject = {
                            servicePublicationId: requestData.apiRow.id,
                            apiType: params.form.interName,
                            useCaseApiParams: params.useCaseApiParams,
                        }
                        row.requestTableData = params.requestTableData;
                    }
                }
            })
        },
        /**
         * 已弃用
         * 设置参数(requestData : 参数列表，form : 参数界面上半部表单)
         */
        setRowParamObject(result, row) {
            const vm = this;
            let paramObject = {};
            vm.useCaseApiParams = [];
            if (result.serviceType === '比对订阅' || result.serviceType === '模型分析' || result.serviceType === '数据碰撞' || result.serviceType === '信息核查') {
                vm.getUseCaseParams(result.requestTableData);
            } else {
                let itemObject = {};
                result.requestTableData.forEach(item => {
                    itemObject[item.paramCode] = item.defaultValue;
                })
                paramObject.parentParamCode = "";
                paramObject.params = [itemObject];
                vm.useCaseApiParams.push(paramObject);
            }
            let object = {
                servicePublicationId: row.id,
                apiType: result.interName,
                useCaseApiParams: vm.useCaseApiParams,
            };
            row.paramObject = object;
        },
        getUseCaseParams(data) {
            const vm = this;
            data.forEach(item => {
                if (item.paramCode === 'params') {
                    let object = {}, objectList = [];
                    item.children.forEach(e => {
                        object[e.paramCode] = e.defaultValue;
                        objectList.push(object);
                    })
                    let itemObject = {
                        "parentParamCode": 'params',
                        "params": objectList
                    };
                    vm.useCaseApiParams.push(itemObject);
                } else {
                    if (item.children && item.children.length > 0) vm.getUseCaseParams(item.children);
                    else {
                        let itemObject = {
                            "parentParamCode": item.paramcode,
                            "params": item.setDataList
                        }
                        vm.useCaseApiParams.push(itemObject);
                    }
                }

            })
        },
        close() {
            this.$emit("close");
        },
        async getApiTreeInfo() {
            const vm = this, {settings} = this;
            let services = vm.$services("apiTest");
            settings.loading = true;
            await services.serviceTreeList(settings).then(res => {
                vm.apiTreeInfo = res.data.data;
            })
        },
        async addApi() {
            const vm = this;
            await vm.getApiTreeInfo();
            let layer = vm.$dgLayer({
                title: "添加API",
                content: require("./api.vue"),
                maxmin: false,
                move: false,
                props: {
                    apiTreeInfo: vm.apiTreeInfo,
                },
                area: ['800px', '500px'],
                on: {
                    submit(result) {
                        vm.setApiTable(result);
                    }
                }
            })
        },
        async setApiTable(result) {
            result.forEach(async element => {
                await this.getModelServicesInfo(element.others.serviceMetaId, element);
                element.state = element.others.apiStatus === '1' ? "启用" : "已停用";
                this.tableData.push(element);
            });
        },
        /**
         * 删除变量
         * @param row
         * @param inx
         */
        deleteVar(row, inx) {
            this.tableData.splice(inx, 1);
        },
        copyVar(row, inx) {
            let newRow = JSON.parse(JSON.stringify(row)) //this.copyArrayObj(row);
            this.tableData.push(newRow);
        },
        async init() {
            const vm = this;
            vm.settings.loading = true;
            await vm.$axios.all([
                this.getUserTreeInfo(),
                this.getUserCaseInfo()
            ]).catch(()=>{
                vm.settings.loading = false;
            })
            vm.settings.loading = false;

            vm.form.classifyId = vm.currentNodeId;


            if (!vm.detailInfo) {
                return;
            }
            vm.form.useCaseName = vm.detailInfo.useCaseName;
            vm.form.memo = vm.detailInfo.memo;
            vm.form.classifyId = vm.detailInfo.classifyId;
            vm.tableData = [];
            vm.detailInfo.useCaseApis.forEach(item => {
                let paramOb = {
                    useCaseApiParams: item.useCaseApiParams,
                    apiType: item.apiType,
                    servicePublicationId: item.servicePublicationId
                };
                vm.tableData.push({
                    id: item.servicePublicationId,
                    name: item.apiName,
                    state: item.apiStatus === '1' ? "启用" : "已停用",
                    paramObject: paramOb
                })
            })
        },
        async getUserTreeInfo() {
            let services = this.$services("userCaseManagement");
            return await services.getUseCaseClassify().then(res => {
                this.layoutForm.classifyId.option = res.data.data;
            })
        },
        //获取用例
        async getUserCaseInfo() {
            const vm = this, {row} = this;
            if (!row) return Promise.resolve();
            let services = vm.$services("userCaseManagement");
            return await services.getUseCaseDetail(row.id).then(res => {
                vm.detailInfo = res.data.data;
            })
        },
    },
    created() {
        this.init();
    }
}
</script>
<style scoped lang="less">
.add-table {
    height: 220px;
}
</style>
