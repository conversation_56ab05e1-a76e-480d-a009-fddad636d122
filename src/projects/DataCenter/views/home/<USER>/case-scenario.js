import all from "@/projects/DataCenter/views/home/<USER>/detail/all";
import ShareCard from "@/projects/DataCenter/views/home/<USER>/share/ShareCard";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "../service-mixins/service-mixins";
import NoData from "@/components/no-data"
import dataDetails from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/dataDetails";
import detailDialog from "@/projects/DataCenter/views/serviceManage/dialog/detailDialog"
import modelDetail from "@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"
import VisualizationEdit from '@/projects/DataCenter/views/visualizing/edit-panel/VisualizationEdit'
import EditPanel from "@/projects/DataCenter/views/homeHu/edit-panel"
import emptyImg from "@/assets/images/common/empty-img.png"
import {countPage} from "@/projects/DataCenter/views/home/<USER>/share-page/countSharePage";
export default {
    name: "caseScenario",
    mixins: [servicesMixins, commonMixins],
    components: {
        all, 
        ShareCard, 
        NoData, 
        dataDetails, 
        detailDialog,
        modelDetail,
        VisualizationEdit,
        EditPanel,
    },
    data() {
        return {
            emptyImg,
            childrenActiveName : "",
            childrenTabPanes : [],
            activeName: "",
            tabPanes: [],
            caseName:"",
            shareData:[],
            paginationProps: {
                currentPage: 1,
                pageSizes: [10 ,20, 30,40],
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper"
            },
            emptyTxt: "暂无场景案例",
            //数据建模模块是否展示
            showModelingEditPanel :false,
            //可视化详情模块是否展示
            showEditPanel :false,
            //主题门户详情模块是否展示
            showHomehuEditPanel : false,
            isCase : true,
        }
    },
    methods: {
        countSize : countPage.methods.countSize ,
        /**
         * 场景案例 - 建模跳转
         * @param {} data 
         */
        openModelingPanel(data){
            this.showModelingEditPanel = true;
            const vm = this;
            data.transId = data.caseId;
            data.modelName = data.name;
            data.isNew = false;
            let layer = this.$dgLayer({
                content: require("@/projects/DataCenter/views/modeling/dialog/newModel/index.vue"),
                skin: "new-model",
                maxmin: true,
                noneBtnField: true,
                props: {
                    dirId: "-1",
                    isCase : true,
                },
                area: ["100%", "100%"],
                on: {
                    close() {
                        vm.showModelingEditPanel = false;
                        layer.close(layer.dialogIndex);
                    }
                },
            });
            layer.$children[0].addTab(data, false);
        },
        /**
         * 场景案例 - 可视化跳转
         * @param {*} data 
         */
        openVisualizingPanel(data){
            data.id = data.caseId;
            this.showEditPanel = true;
            this.$nextTick(()=>{
                this.$refs.visual_panel.getRouterData( data.id ,"" , "" , '');
            })
        },
        closeEditPanel(groupId , filterTxt) {
            this.showEditPanel = false;
        },
        openHomehuPanel( data ) {
            this.showHomehuEditPanel = true;
            data.id = data.caseId;
            this.$nextTick(()=>{
                this.$refs.protal_panel.panelInit( "" , data.ownerId ,data , "");
            })
        },
        closeHomehuEditPanel(groupId , filterTxt) {
            this.showHomehuEditPanel = false;
        },
        openAIModelingPanel (data) {
            const vm = this;
            let layer = this.$dgLayer({
                title: data.name,
                content: require("@/projects/DataCenter/views/AIModeling/dialog/new-model/index.vue"),
                maxmin: false,
                props: {
                    isCase : true,
                },
                area: ['100%', "100%"],
                on: {
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
                // cancel() {
                //     vm.changePage(1);
                // }
            });
            layer.$children[0].caseShow(data);
        },
        caseShareCardClick(data) {
            if(data.parentType.name === '数据模型') {
                this.openModelingPanel(data);
            }else if(data.parentType.name === '仪表盘') {
                this.openVisualizingPanel(data);
            }else if(data.parentType.name === '主题门户') {
                this.openHomehuPanel(data);
                // this.$router.push({name : "themePortal" , params : {node : data}});
            }else if (data.parentType.name === "AI建模") {
                this.openAIModelingPanel(data);
            }
        },
        /**
         * 获取子children
         */
        childrenTab(value){
            this.childrenTabPanes = [];
            let object = this.tabPanes.filter(item =>{
                    return item.value === value.name || item.value === value.id || item.label === value.name;
                }
            )
            if (!(object && object.length)) return;
            if(object[0].children.length > 0){
                this.childrenTabPanes = [{
                    label : "全部",
                    value : value.id || object[0].value
                }];
            }
            this.childrenActiveName = value.id || object[0].value;
            object[0].children.forEach(element=>{
                this.childrenTabPanes.push({
                    label : element.name,
                    value : element.id,
                })
            });

            this.queryScenarioCaseData(1);
        },
        async queryCaseType() {
            const vm = this, {homeServices, homeMock, settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices, homeMock);
            await services.queryParentCaseType(settings).then(res => {
                if (res.data.status === 0) {
                    res.data.data.forEach(element => {
                        let info = {
                            label : element.name,
                            value : element.id,
                            children : element.children,
                        }
                        if(element.name != 'AI建模'){
                            vm.tabPanes.push(info)
                        }
                        else{
                            vm.$hideAI ? '' : vm.tabPanes.push(info);
                        }
                    });
                    if(res.data.data.length)this.activeName = res.data.data[0].id;
                }
            })
        },
        changeSize(val){
            this.paginationProps.pageSize = val;
            this.queryScenarioCaseData();
        },
        queryScenarioCaseData(index =1) {
            const vm = this, {homeServices, homeMock, settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices, homeMock);
            let params = {
                "pageSize":this.paginationProps.pageSize ,
                "pageNum":index,
                "scenarioName": this.caseName,
                "caseTypeId": this.childrenActiveName
            }
            this.shareData = [];
            services.queryScenarioCase(params, settings).then(res => {
                if (res.data.status === 0 && res.data.data) {
                    let result = res.data.data;
                    this.shareData = result.dataList.map(item =>{
                        item.description = item.description || "-";
                        item.sourceType = item.caseType? item.caseType.name : "";
                        item.resourceType = item.caseType ? item.caseType.code : "";
                        return item;
                    });
                    this.paginationProps.pageSize = result.pageSize;
                    this.paginationProps.currentPage = result.pageIndex;
                    this.total = result.totalCount;
                }
            })
        },
        async init(){
            this.countSize();
            await this.queryCaseType();
            // this.queryScenarioCaseData();
            let {item} = this.$route.params;
            if (item) {
                this.activeName = item.id;
                this.childrenTab(item);
            }else {
                this.childrenTab({id : this.activeName});
            }
        }
    },
    created() {
        this.init();
    }
}
