<template>
    <div>
        <el-form
                :model="plug_data"
                size="mini"
                label-position="right"
                :label-width="width"
                class="attrForm"
                :hide-required-asterisk="true"
        >
            <el-form-item
                    v-for="(attr , i ) in attrsList"
                    :key="i"
                    :label="attr.label + ' :'"
                    :prop="attr.props"
                    :rules="attr.rule"
                    v-if="attr.show === false ? is_increment : true"
            >
                <el-switch
                        v-if="attr.type === 'switch'"
                        v-model="attr.value"
                >
                </el-switch>
                <div v-if="attr.type ==='input' && attr.inputType === 'number'" >
                    <el-input class="ce-attr_item"
                              :placeholder="attr.placeholder"
                              v-model.trim.number="attr.value"
                    ></el-input>
                    <i class="el-icon-info ce-tip" v-if="attr.hasTip" :title="attr.tip"></i>
                </div>
                <div v-if="attr.type ==='select'">
                    <i class="el-icon-info ce-tip" v-if="attr.hasTip" :title="attr.tip"></i>
                    <el-select class="ce-attr_item" v-model="attr.value" :placeholder="attr.placeholder" >
                        <el-option
                                v-for="item in attr.option"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
        </el-form>
        <RelationCondition />
    </div>
</template>

<script>
    import RelationCondition from '../../../plugin/metaDataInput/standardSql/StandardSqlInput'
    export default {
        name: "ResourceDataBase",
        components :{
            RelationCondition
        },
        computed: {
            plug_data() {
                return {
                    increment : this.attrsList[0].value ,
                    symbol : this.attrsList[1].value ,
                    in_storage : this.attrsList[2].value ,
                    renew_time : this.attrsList[3].value ,
                    data_count : this.attrsList[4].value ,
                }
            },
            is_increment(){
                return this.plug_data.increment;
            }
        },
        data() {
            return {
                width: '110px',
                attrsList: [
                    {
                        label: '是否增量分析',
                        type: 'switch',
                        value: false,
                        props: 'increment',
                        rule: []
                    },{
                        label : '增量比较符',
                        type : 'select' ,
                        value : '' ,
                        props : 'symbol',
                        show : false ,
                        option : [
                            {
                                label : '大于等于' ,
                                value :'greater_equal'
                            }, {
                                label : '大于' ,
                                value :'greater'
                            }
                        ]
                    },{
                        label : '入库时间字段',
                        type : 'select' ,
                        value : '' ,
                        props : 'in_storage',
                        show : false ,
                        option : []
                    },{
                        label : '更新时间字段',
                        type : 'select' ,
                        value : '' ,
                        props : 'renew_time',
                        show : false ,
                        option : [],
                    }, {
                        label: '分析数据量',
                        type: 'input',
                        inputType : 'number',
                        value: 0 ,
                        props: 'data_count',
                        placeholder: '请输入分析数据量' ,
                        hasTip : true ,
                        tip : '分析数量为0时,不限制抽取数量' ,
                        rule : [
                            {required: true, message: '请输入分析数据量', trigger: 'blur'},
                            {type: 'number', message: '请输入整数' , trigger: 'blur'}
                        ]
                    }
                ]
            }
        }
    }
</script>

<style scoped lang="less">
    @import "../../../plugin/css/attrForm";
</style>