import {servicesMixins} from "./service-mixins/service-mixins";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import TreeAndList from "@/components/layout/treeAndListUi/index.vue"
import Tree from "./tree"
import List from "./list"
import empowerDialog from "./dialog/empower/empower-dialog.vue"
export default {
    name: "dataset" ,
    mixins : [servicesMixins , commonMixins],
    components : {
        TreeAndList,
        List ,
        Tree ,
        empowerDialog
    },
    methods : {
       empowerShow(selectDataId) {
           this.$refs.empower.show(selectDataId);
       },
        getTableData(node){
           this.$refs.list.getDataSetPageByTree(node);
        },
        reNewList(key){
           this.$refs.dataTree.setCurrentKey(key);
           this.$refs.dataTree.nodeClickByKey(key);
        },
        moveSuccess(id){
           this.$refs.dataTree.nodeClick(id);
        }
    }
}