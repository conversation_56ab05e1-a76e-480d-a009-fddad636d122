<template>
    <div class="selectTable" v-loading="settings.loading" :class="{'ce-disabled_model' : type === 'edit' }">
        <div class="selectTable-top">
            <div>
                <p>资源合并</p>
                <div v-for="(item,k) in tableList" :key="k" class="selectTable-top_info">
                    <div>
                        <div>{{ k === 0 ? '左表' : (k != tableList.length - 1 ? '左右表(上下)' : '右表')}}</div>
                        <div>
                            <div class="mb10 selectTable-top_table">
                                <ce-select-drop
                                        v-if="k === 0 ? tableDataList.length : dataSetTree.length"
                                        class="expect-select mr10"
                                        v-model="item.tableID"
                                        :data="k === 0 ? tableDataList : dataSetTree"
                                        :tree-props="{
                                            'default-expanded-keys': [item.tableID],
                                            'current-node-key':item.tableID
                                        }"
                                        :props="defaultProps"
                                        :filterNodeMethod="filterNode"
                                        placeholder="请选择数据资源"
                                        @node-click="nodeClick(item, k, $event)"
                                        filterable
                                        check-leaf
                                        check-strictly
                                >
                                </ce-select-drop>
                                <dg-tree-drop
                                        class="expect-select mr10"
                                        :props="defaultProps"
                                        v-model="item.tableID"
                                        :data="[]"
                                        filterable
                                        check-leaf
                                        check-strictly
                                        :filterNodeMethod="filterNode"
                                        placeholder="请选择数据资源"
                                        v-else
                                >
                                </dg-tree-drop>
                                <dg-button type="text" icon="el-icon-circle-plus-outline" @click="addTable(k)" v-if="tableList.length < 5"></dg-button>
                                <dg-button type="text" icon="el-icon-remove-outline" @click="deleteTable(k)" v-if="tableList.length > 2"></dg-button>
                            </div>
                            <el-button-group>
                                <dg-button size="mini" v-for="(info, index) in radioList"
                                           :key="index"
                                           :type="info.option === item.relation ? 'primary': ''"
                                           @click="relationChange(item,k, info)"
                                           v-if="tableList.length -1 != k"
                                           :class="{'activeBtn':selectCondition.index === k && selectCondition.value === info.option}"
                                >
                                    {{ info.name }}
                                </dg-button>
                            </el-button-group>
                        </div>
                    </div>

                    <div v-show="selectCondition.index === k">
                        <div class="selectTable-top_con">
                            <p>合并条件</p>
                            <div>
                                <dg-radio-group v-model="item.joinOnOperate" :data="radioData" size="small" call-off class="mr30"></dg-radio-group>
                                <dg-button type="text" @click="addCon(item)" class="ml30">添加条件</dg-button>
                                <div  class="selectTable-top_filter" :style="{'border': item.mergeInfo.length ? '1px solid #dcdcdc' : 'none','max-height': 45 * tableList.length + 40 * (tableList.length - 1) + 'px'}">
                                    <div v-for="(par,index) in item.mergeInfo" class="mb10">
                                        <dg-select v-model="par.leftColumnCode"
                                                   :data="leftColumn"
                                                   group-name="tableName"
                                                   group
                                                   value-name="value"
                                                   filterable
                                                   label-name="name"
                                                   @change="itemColumnChange(par,'left')"
                                        ></dg-select>
                                        <span class="pl5 pr5">等于</span>
                                        <dg-select v-model="par.rightColumnCode"
                                                   group-name="tableName"
                                                   group
                                                   filterable
                                                   :data="rightColumn" class="mr5"
                                                   value-name="value" label-name="name"
                                                   @change="itemColumnChange(par,'right')"
                                        ></dg-select>
                                        <dg-button type="text" icon="el-icon-remove-outline" @click="deleteCon(index, item)" v-if="item.mergeInfo.length > 1"></dg-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="selectTable-bottom">
            <p>结果过滤</p>
            <div>
                <filter-plugin :filter-columns="filterColumns" ref="filter"
                               showValueType
                               plug-type="service"
                               @change="filterChange"
                ></filter-plugin>
            </div>
        </div>

        <el-button v-footer size="mini" @click="clear" v-if="type != 'edit'">取消</el-button>
        <el-button v-footer size="mini" @click="save" v-if="type != 'edit'">保存</el-button>
    </div>
</template>

<script>
    import {treeMethods} from "@/api/treeNameCheck/treeName"
    import filterPlugin from "@/components/flowPanel/flow-plugin/filterPlugin/filterPlugin"
    import {commonMixins} from "@/api/commonMethods/common-mixins"
    import {common} from "@/api/commonMethods/common"
    export default {
        name: "selectTable",
        components : {filterPlugin},
        mixins: [commonMixins, common,],
        props:{
            tableDataList:Array,
            info: Object,
            type:String,
            selectDataSet: Object,
        },
        data(){
            return {
                defaultProps: {
                    value: 'id',
                    label: 'label',
                    children: 'children',
                    title: 'label',
                },
                treeBind : {
                    // "default-expand-all" : true ,
                    nodeKey: "id",
                    isFrozenRoot: true,
                },
                tableList:[],
                radioList: [
                    {
                        name: "左右合并",
                        option: "左右合并"
                    },
                    {
                        name: "右合并",
                        option: "右合并"
                    },
                    {
                        name: "左合并",
                        option: "左合并"
                    },
                    {
                        name: "交集",
                        option: "交集"
                    },
                    {
                        name: "左差集",
                        option: "左差集"
                    },
                    {
                        name: "右差集",
                        option: "右差集"
                    }
                ],
                radioData: [
                    {
                        label: "满足全部条件",
                        value: 'AND'
                    },
                    {
                        label: "满足任意条件",
                        value: 'OR'
                    }
                ],
                selectBtn:'',
                filterList:[],
                selectCondition:{index: 0, value:'左右合并' },
                tableColumn1: [],
                tableColumn2: [],
                tableColumn3: [],
                tableColumn4: [],
                tableColumn5: [],
                leftColumn: [],
                rightColumn: [],
                selectInfo : this.info,
                dateFormat : "yyyy-MM-dd",
                mergeInitInfo:[{ leftColumnCode:'', leftTableId: '',rightColumnCode:'', rightTableId: ''}],
                filterColumns:[],
                dataSetTree:[],
            }
        },
        created(){
            this.tableList = [
                {tableID: '' , tableName: '', tableCode:'', relation: '左右合并', joinOnOperate:'AND', mergeInfo: this.getMergeInitInfo()},
                {tableID: '', tableName: '', tableCode:'', relation: '', joinOnOperate:'AND',mergeInfo: this.getMergeInitInfo()},
            ];
            if(this.selectInfo.manyJoinMetas && this.selectInfo.manyJoinMetas.length){
                this.setTableInfo();
            }
            else if(this.selectDataSet && this.selectDataSet.id){
                this.tableList[0].tableID = this.selectDataSet.id;
                this.nodeClick(this.tableList[0], 0, this.selectDataSet, true)
            }
        },
        methods:{
            filterNode : treeMethods.methods.filterNode ,
            clear(){
                this.$emit('close')
            },
            save(){
                const vm = this;
                if(vm.tableList.find(n=> !n.tableID)){
                    return vm.$message.warning('请选择表资源');
                }
                if(!vm.selectCondition.value){
                    return vm.$message.warning('请选择合并方式');
                }
                for(let i =0; i< vm.tableList.length -1 ; i++){
                    if(vm.tableList[i].mergeInfo.find(n=> !n.leftColumnCode)){
                        return vm.$message.warning('请设置合并条件');
                    }
                    if(vm.tableList[i].mergeInfo.find(n=> !n.rightColumnCode)){
                        return vm.$message.warning('请设置合并条件');
                    }
                }
                if(vm.tableList.filter(n=> !n.mergeInfo.length).length>1){
                    return vm.$message.warning('请设置合并条件');
                }
                let valid = vm.$refs.filter.validate();
                if(!valid) return;
                this.getMoreTableInfo();
                this.$emit('submit',this.tableList, this.selectInfo);
                this.clear()
            },
            nodeClick(item, index, e, init){
                const vm = this;
                let len = vm.tableList.length;
                for(let i = index; i< len; i++){
                    //i > 0 ? vm.tableList[i-1].mergeInfo = vm.getMergeInitInfo() : '';
                    vm.tableList[i].mergeInfo = vm.getMergeInitInfo();
                }
                let length = vm.tableList.filter(n=>n.tableID == item.tableID).length;
                if(length > 1){
                    item.tableID = "";
                    return vm.$message.warning("不能选择相同的资源");
                }
                item.tableName = e.name || e.label;
                item.dbType = e.dbType || e.db_type;

                let belongTableName = init ? e.code : e.belongTableName || e.label;
                let arr = belongTableName.split('.');
                item.tableCode = arr[arr.length - 1];

                if(index === 0 && vm.type != 'edit'){
                    vm.selectCondition = {index: 0, value:item.relation };
                    vm.getPublishDataSetTree(e.id);
                    vm.tableList.forEach((n,i)=>{
                        if(i!== 0){
                            n.tableID = '' ;
                            n.tableName = '';
                            n.tableCode = '';
                            n.relation = '';
                            n.joinOnOperate = 'AND';
                            n.mergeInfo = vm.getMergeInitInfo();
                            vm['tableColumn' + (i+1)] = [];
                        }
                    })
                }
                vm.getDataSetColumnList(item.tableID, item.tableName, index, belongTableName);

            },
            getPublishDataSetTree(id){
                const vm = this,{settings} = this;
                let service = this.$services("serviceManage");
                vm.dataSetTree = [];
                let param = {
                    hasDataObj: true,
                    isLogic: true,
                    filterDataSetId: id,
                    hasDataWarehouse: true,
                }
                settings.loading = true;
                service.getPublishDataSetTree(param,settings).then(async res=>{
                    if (res.data.status === 0) {
                        let result = vm.setSourceNode(res.data.data);
                        result.forEach(item => {
                            vm.dataSetTree.push(item);
                        })
                    }
                })
            },
            setSourceNode(nodes) {
                const vm = this;
                if (nodes !== null) {
                    return nodes.filter(item => {
                        if(item.children && item.children.length !== 0){
                            item.children = vm.setSourceNode(item.children);
                        }
                        else{
                            item.children = [];
                        }
                        return  (item.belongType || item.children && item.children.length !== 0) && item.id ;
                    })
                }
            },
            //添加资源表
            addTable(index){
                const vm = this;
                let tableLength = vm.tableList.length;
                for(let i = tableLength; i>0 ;i--){
                    if(i >= index +2){
                        vm['tableColumn'+ (i+1)] = vm['tableColumn'+ i];
                    }
                }
                vm.tableList.splice(index+1,0 , {tableID: '', tableName: '', tableCode:'', relation: '', joinOnOperate:'AND', mergeInfo: vm.getMergeInitInfo()});
                vm['tableColumn'+ (index+2)] = [];
                if(vm.selectCondition.index === index){
                    vm.rightColumn = [];
                    vm.tableList[index].mergeInfo.forEach(n=>{
                        n.rightColumnCode = '';
                    })
                }
            },
            getMergeInitInfo(){
                return JSON.parse(JSON.stringify(this.mergeInitInfo))
            },
            //删除资源表
            deleteTable(index){
                const vm = this;
                let tableLength = vm.tableList.length;
                for(let i = 0; i< 5 ;i++){
                    if(i > index && i<= tableLength - 1){
                        vm['tableColumn'+ i] = vm['tableColumn'+ (i+1)];
                    }
                    if(i > tableLength - 1){
                        vm['tableColumn'+ i] = [];
                    }
                }
                vm.tableList.splice(index, 1);
                if(index === vm.selectCondition.index){
                    vm.selectCondition = {
                        index: 0,
                        value: '左右合并'
                    }
                    vm.tableList[0].relation = '左右合并'
                }
                vm.setConditionSelectColumn(vm.selectCondition.index)
            },
            //添加合并条件
            addCon(item){
                item.mergeInfo.push({ leftColumnCode:'', leftTableId: '',rightColumnCode:'', rightTableId: ''})
            },
            //删除合并条件
            deleteCon(index, item){
                item.mergeInfo.splice(index, 1);
            },
            /**
             * 条件更新
             * */
            filterChange(condition){
                this.conditions = condition;
            },
            //设置合并条件左右下拉框
            setConditionSelectColumn(index){
                const vm = this;
                vm.leftColumn = vm['tableColumn'+(index+1)];
                vm.rightColumn = vm['tableColumn'+(index+2)];
                vm.setFilterList();
            },
            setFilterList(){
                this.filterColumns = [];
                this.filterList = [...this.tableColumn1,...this.tableColumn2,...this.tableColumn3,...this.tableColumn4,...this.tableColumn5].map(n=>{
                    let arr = n.belongTableName.split('.');
                    n.label = n.name || n.code;
                    n.value = arr[arr.length - 1]+'.'+ n.code;

                    let obj = this.filterColumns.find(p=>p.value === n.belongTableName);
                    if(obj){
                        obj.children.push(n)
                    }
                    else{
                        let name = n.tableName.slice(0,n.tableName.indexOf('('))
                        this.filterColumns.push({label:name,value: n.belongTableName,children:[n]});
                    }
                    return n;
                });
            },
            relationChange(item, index, info){
                const vm = this;
                let arr = item.mergeInfo.filter(n=> {return  n.leftColumnCode || n.rightColumnCode});
                if(arr.length && item.relation != info.option && vm.selectCondition.index === index){
                    vm.confirm('提示', `确定要更换合并选项?`, () => {
                        item.relation = info.option;
                        vm.selectCondition = {index: index, value: info.option };
                    })
                }
                else{
                    item.relation = info.option;
                    vm.selectCondition = {index: index, value:info.option };
                }
                vm.setConditionSelectColumn(index);
            },
            //获取表字段
            async getDataSetColumnList(tableID,tableName, index, belongTableName){
                const vm = this;
                let service = this.$services("serviceManage");
                vm['tableColumn'+ (index+1)] = [];
                await service.getDataSetColumnList(tableID).then(res => {
                    if (res.data.status === 0) {
                        res.data.data.forEach(n=>{
                            n.name = n.name || n.code;
                            n.tableID = tableID;
                            n.tableName = tableName;
                            n.belongTableName = belongTableName;
                        })
                        vm['tableColumn'+ (index+1)] = res.data.data;
                        vm.setConditionSelectColumn(vm.selectCondition.index);
                    }
                });
            },
            //合并条件下拉变更
            itemColumnChange(item, type,){
                const vm = this;
                if(type === 'left'){
                    let info = vm.leftColumn.find(n=> n.value === item.leftColumnCode);
                    item.leftTableId = info.tableID;
                }
                else if(type === 'right'){
                    let info = vm.rightColumn.find(n=> n.value === item.rightColumnCode);
                    item.rightTableId = info.tableID;
                }
            },
            async setTableInfo(){
                const vm = this, {settings} = this;
                vm.tableList = [];
                settings.loading = true;
                let promiseArr =  vm.selectInfo.manyJoinMetas.map(async (n,i)=>{
                    let info = {
                        tableID: n.leftStepId,
                        tableName: n.leftStepName || n.leftStepCode,
                        tableCode: n.leftStepCode,
                        relation: n.op,
                        joinOnOperate: n.joinOnOperate,
                        mergeInfo: n.joinOnColumns,
                        dbType:n.dbType,
                    };
                    if(i === 0){
                        vm.selectCondition = {index: 0, value:info.relation };
                    }
                    vm.tableList.push(info);
                    await vm.getDataSetColumnList(info.tableID, info.tableName, i, info.tableCode);
                    if(i === vm.selectInfo.manyJoinMetas.length - 1){
                        info = {
                            tableID: n.rightStepId,
                            tableName: n.rightStepName || n.rightStepCode,
                            tableCode: n.rightStepCode,
                            relation: '',
                            joinOnOperate: 'AND',
                            mergeInfo: [],
                            dbType:n.dbType,
                        }
                        vm.tableList.push(info);
                        await vm.getDataSetColumnList(info.tableID, info.tableName, i+1, info.tableCode);
                    }

                })
                await Promise.all(promiseArr);
                settings.loading = false;
                let currentConditions = vm.initCondition(vm.selectInfo.conditionInfo, vm);
                vm.$nextTick(()=>{
                    vm.$refs.filter.isResouceShow = vm.$refs.filter.isResouceShow + 1;
                    vm.$refs.filter.initCondition(currentConditions);
                })
                if(vm.tableList.length && vm.tableList[0].tableID && vm.type!= 'edit'){
                    vm.getPublishDataSetTree(vm.tableList[0].tableID)
                }
                if(vm.type === 'edit'){
                    vm.dataSetTree = vm.tableDataList;
                }
            },
            getMoreTableInfo(){
                const vm = this;
                vm.selectInfo = {
                    manyJoinMetas : [],
                    conditionInfo:[],
                    dbType:'',
                }
                vm.tableList.forEach((n, i) =>{
                    if(i>0){
                        let up = vm.tableList[i-1] || {};
                        let data = {
                            index: i ,
                            leftStepId: up.tableID,//左表id
                            leftStepCode: up.tableCode,//左表表英文名
                            leftStepName: up.tableName,//左表表中文名
                            rightStepId: n.tableID,
                            rightStepCode: n.tableCode,
                            rightStepName: n.tableName,
                            op: up.relation,// join操作类型：left join 等
                            joinOnOperate: up.joinOnOperate || 'AND',//join on 是 AND 还是 OR
                            joinOnColumns: up.mergeInfo, // join on 的字段
                        };
                        vm.selectInfo.manyJoinMetas.push(data);
                    }
                    else{
                        vm.selectInfo.dbType = n.dbType;
                    }
                })
                let conditionInfo = vm.$refs.filter.getConditions();
                for (let i = 0; i < conditionInfo.length; i++) {
                    let judgCondition = [];
                    conditionInfo[i].children.forEach(judg =>{
                        let [condition, conditionType] = judg.condition.split('|');
                        let value = judg.value;
                        let otherTimeType = judg.defaultType.toLowerCase() === 'time';
                        let startTime , endTime = judg.valueEnd;
                        if (judg.valueType === 'constValue') {
                            if (otherTimeType) judg.defaultType = 'Timestamp';
                            if((judg.defaultType === 'Timestamp' || otherTimeType)){
                                value = judg.value.valueOf() / 1000;
                            }else if(['Date','date'].includes(judg.defaultType)){
                                value = judg.value ? new Date(judg.value).Format(vm.dateFormat):  "";
                                //value = judg.value ? new Date(judg.value).Format(vm.dateFormat) :  "";
                            }

                            if(condition === "in" && (judg.defaultType === 'Timestamp' || otherTimeType)){
                                condition = "between";
                            }else if(condition === "not in" && (judg.defaultType === 'Timestamp' || otherTimeType)){
                                condition = "not between";
                            }
                            if((judg.defaultType === 'Timestamp' || otherTimeType)){
                                startTime = judg.value.valueOf() / 1000;
                                endTime = judg.valueEnd.valueOf() / 1000;
                            }else if(judg.defaultType === 'Date'){
                                startTime = judg.value ? new Date(judg.value).Format(vm.dateFormat) :  "";
                                endTime = judg.valueEnd ? new Date(judg.valueEnd).Format(vm.dateFormat):  "";
                            }
                        }
                        judgCondition.push({
                            field : judg.field[1],
                            condition : condition,
                            value : value,
                            fieldType : judg.defaultType,
                            conditionType : conditionType,
                            valueType : judg.valueType ,
                            startTime ,
                            endTime
                        });
                    });

                    let outRelationContion = '';
                    if (i === conditionInfo.length-1){
                        outRelationContion = '';
                    }else {
                        outRelationContion = conditionInfo[i+1].logic;
                    }

                    vm.selectInfo.conditionInfo.push({
                        no : i,
                        relationCondition : conditionInfo[i].childrenLogic,
                        outRelationContion : outRelationContion,
                        judgCondition : JSON.stringify(judgCondition)
                    });
                }
            },
            //回填过滤条件
            initCondition(pluginMeta, vm){
                if (pluginMeta){
                    let enJudgContion = pluginMeta.sort(vm.compare("no",true));
                    let currentConditions = [];
                    for (let i = 0; i < enJudgContion.length; i++) {
                        let {no} = enJudgContion[i];
                        let children = [];
                        let parse = JSON.parse(enJudgContion[i].judgCondition);
                        parse.forEach(judg => {
                            let {value , endTime ,valueType ,fieldType , condition ,conditionType } = judg;
                            if(valueType === 'constValue'){
                                if(fieldType === 'Timestamp'){
                                    value = value * 1000;
                                    if(condition === "between"){
                                        condition = "in";
                                        endTime = endTime * 1000;
                                    }else if(condition === "not between"){
                                        condition = "not in";
                                        endTime = endTime * 1000;
                                    }
                                }
                            }
                            let fieldD = vm.filterList.find(col =>  col.value === judg.field);
                            children.push({
                                field : [judg.field.slice(0,judg.field.indexOf('.')),judg.field],
                                condition : condition + '|' + conditionType,
                                condLabel: conditionType,
                                condValue: condition,
                                value : value,
                                defaultType : fieldD.dataType,
                                valueType  ,
                                valueEnd : endTime

                            });
                        });
                        let outRelationContion = '';
                        if (no === 0){
                            outRelationContion = 'and';
                        }else {
                            outRelationContion = enJudgContion[no-1].outRelationContion;
                        }

                        currentConditions.push({
                            no : no,
                            childrenLogic : enJudgContion[no].relationCondition,
                            logic :outRelationContion,
                            children : children
                        });
                    }
                    return currentConditions;
                }
            },
        }
    }
</script>

<style scoped lang="less">
    .selectTable{
        height: 100%;
        p{
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
        }
        &-top{

            &_info{
                display: grid;
                grid-template-columns: 56% 44%;
                margin-bottom: 10px;
                /deep/.el-input,/deep/ .dg-tree-select{
                    width: 500px;
                }
            }
            &_con{
                position: absolute;
                top:20px;
                /deep/.el-input{
                    width: 210px;
                }
            }
            &_filter{
                padding: 10px;
                max-height: 300px;
                overflow-y: auto;
            }
            &_table{
                display: flex;
            }
        }
        .activeBtn{
            border:2px solid  #f7bd51;
        }
        .el-button-group .el-button--primary:not(:first-child):not(:last-child){
            border-left-color:#f7bd51;
            border-right-color:#f7bd51;
        }
    }
    .ce-disabled_model .el-button-group .el-button--primary{
        opacity: 1;
        pointer-events: all;
        cursor: pointer;
    }
</style>