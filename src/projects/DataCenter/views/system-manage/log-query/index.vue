<template>
    <div class="log">
        <div>
            <el-tabs class="ce-share_tab" v-model="activeName">
                <el-tab-pane v-for="(tab , inx) in tabs" :key="inx" :label="tab.label" :name="tab.name"></el-tab-pane>
            </el-tabs>
        </div>
        <div class="log-content">
            <system-log ref="table" v-show="activeName === 'systemLog'"></system-log>
            <service-log v-show="activeName === 'serviceLog'"></service-log>
        </div>
    </div>
</template>

<script>
import systemLog from "@/projects/DataCenter/views/system-manage/log-query/list";
import serviceLog from "@/projects/DataCenter/views/system-manage/log-query/serviceLog";
export default {
    name: "logQuery" ,
    components : {
        systemLog,
        serviceLog
    },
    data(){
        return {
            activeName:'systemLog',
            tabs:[
                { name: 'systemLog', label: '系统日志'},
                { name: 'serviceLog', label: '服务日志'},
            ]
        }
    }
}
</script>

<style scoped lang="less">
 .log{
     height: calc(100% - 14px);
     background: #fff;
     border-radius: 4px;
     padding: 28px 28px 14px 28px;
     -webkit-box-sizing: border-box;
     box-sizing: border-box;
     &-content{
         height:calc(100% - 60px);
     }
 }
</style>
