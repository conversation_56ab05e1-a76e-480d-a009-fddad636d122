<template>
    <div class="upDown">
        
        <div >
            <div class="m-t-u-button">
            <dg-button :disabled="disabled" type="primary" icon="el-icon-edit" @click="addDataSet">编辑合并表</dg-button>
            </div>
            <div class="m-t-u-content">
            <div class="m-t-u-c-item" v-for="item in mergeUpDownData" :key="item.id">
                <div class="m-t-u-c-i-head">
                <label>{{item.title}}
                    <span v-if="!disabled">
                        <dg-button
                                v-if="item.del"
                                @click="deleteMergeData(item)"
                                type="text"
                                icon="el-icon-delete"
                        ></dg-button>
                    </span>
                </label>
                </div>
                <div class="m-t-u-c-i-box">
                <div class="m-t-u-c-i-b-item" v-for="(v, i) in item.list" :key="v.id">
                    <el-input v-if="!v.isSelect"
                    @input="inputChange(i)"
                    :disabled="disabled"
                    v-model="v.value" placeholder="请输入内容">
                    </el-input>
                    <dg-select
                    :disabled="disabled"
                    :class="[v.iscompatible?'on ':'off ']"
                    v-else v-model="v.value" :data="v.selectList"
                    @change="selectChange(v, i, item.title)">
                    </dg-select>

                </div>
                </div>
            </div>
            </div>

            <addDataSet  @tijiao="editUnionTable" :is_union="union_dataSet" ref=dataset></addDataSet>
        </div>

    </div>
</template>

<script src="./up-down.js">
</script>

<style scoped lang="less" src="./up-down.less">
</style>