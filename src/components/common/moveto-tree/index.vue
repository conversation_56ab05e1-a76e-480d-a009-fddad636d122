<template>
    <common-dialog
            custom-class="model-dialog"
            v-loading="settings.loading"
            :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <tree-page v-if="reNew" :services="{services}" :rowData="rowData" @nodeClick="nodeClick"></tree-page>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button type="primary" @click="submit">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>

<script src="./moveto.js"></script>
<style>
    .model-dialog .dataTree {
        height: calc( 75vh - 192px);
    }
</style>
