<<template>
    <div class=" mediation">
        <ce-list-table
                ref="table"
                :filter-text.sync="filterText"
                :t-head-data="tHeadData"
                :table-data="tableData"
                @changePage="changePage"
                :operate-icons="operateIcons"
                :filter-placeholder="filterPlaceholder"
        >
            <template slot="button">
                <el-button type="primary" @click="mediationOption('add')">{{ addTxt }}</el-button>
            </template>
        </ce-list-table>
    </div>
</template>
<script lang="js" src="./service-mediation.js"></script>
<style>
    .mediation{
        height: calc(100% - 14px);
        background: #fff;
        border-radius: 4px;
        padding: 28px 28px 14px 28px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }
</style>
