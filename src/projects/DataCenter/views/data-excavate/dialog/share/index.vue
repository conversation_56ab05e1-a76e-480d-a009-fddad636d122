<template>
    <common-dialog :title="title" :width="width" :visible.sync="visible" @closed="clearData">
        <share v-if="reNew" />
        <span slot="footer" class="dialog-footer">
            <dg-button size="mini" @click="visible = false">取 消</dg-button>
            <dg-button size="mini" type="primary" @click="visible = false">确 定</dg-button>
        </span>
    </common-dialog>
</template>

<script>
    import share from "./share.vue"
    export default {
        name: "index" ,
        components : {
            share
        },
        data() {
            return {
                title: "分享",
                visible: false,
                reNew: false ,
                width : "520px"
            }
        },
        methods: {
            clearData() {
                this.reNew = false;
            },
            show(row) {
                this.visible = true;
                this.reNew = true;
            }
        }
    }
</script>

<style scoped>

</style>