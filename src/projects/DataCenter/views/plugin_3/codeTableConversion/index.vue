<template>
    <div class="height100" v-loading="settings.loading">
        <el-form
            class="height100"
                ref="form"
                :model="{ list: tableData }"
                @submit.native.prevent>
            <common-table
                    ref="list"
                    height="100%"
                    :pagination="false"
                    :data="tableData"
                    :columns="tHeadData"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <el-form-item>
                            <span class="p5" v-for="(col , inx) in operateIcons"
                                    :key="inx">
                        <el-popconfirm
                                v-if="col.type === 'delete'"
                                title="确认删除?"
                                @confirm="col.clickFn(row,$index)"
                        >
                            <el-button type="text"
                                        slot="reference"
                                        :title="col.tip">
                                <span v-html="col.tip"></span>
                            </el-button>
                        </el-popconfirm>
                        <el-button type="text"
                                    v-else
                                    :title="col.tip"
                                    @click="col.clickFn(row,$index)">
                            <span v-html="col.tip"></span>
                        </el-button>

                    </span>
                    </el-form-item>
                </template>
                <template slot="header-operate">
                    <el-button type="text" :title="headerIcon.title" @click="headerIcon.clickFn">
                        <i class="model_status" :class="headerIcon.icon"></i>
                    </el-button>
                </template>
                <!-- <template slot="header-name" slot-scope="{row , $index}">
                    <div>{{tHeadData[0].label}}<span class="red">*</span></div>
                </template>
                <template slot="header-varValue" slot-scope="{row , $index}">
                    <div>{{tHeadData[5].label}}<span class="red">*</span></div>
                </template> -->
                <template slot="header-toConvertColumnCode" slot-scope="{row , $index , column}">
                    <span class="is-require">{{column.label}}</span>
                </template>
                <template slot="header-relationTableId" slot-scope="{row , $index , column}">
                    <span class="is-require">{{column.label}}</span>
                </template>
                <template slot="header-codeTableColumn" slot-scope="{row , $index , column}">
                    <span class="is-require">{{column.label}}</span>
                </template>
                <template slot="header-codeTableName" slot-scope="{row , $index , column}">
                    <span class="is-require">{{column.label}}</span>
                </template>
                <template slot="toConvertColumnCode" slot-scope="{row , $index}">
                    <dg-select class="width100" v-model="row.toConvertColumnCode" :data="convertColumnCodeData" filterable
                                    @change="varChange($event , row)"></dg-select>
                </template>

                <template slot="isNewColumns" slot-scope="{row , $index}">
                    <el-checkbox v-model="row.isNewColumns"></el-checkbox>
                    <el-input v-model="row.newColumnName" v-input-limit:fieldCode :placeholder="newColumnTxt" v-if="row.isNewColumns" style="width:130px;"></el-input>
                </template>
                <template slot="relationTableId" slot-scope="{row , $index}">
                    <div>
                        <ce-select-drop
                            ref="tree"
                            class="treeSelectC"
                            :props="defaultProps"
                            check-leaf
                            check-strictly
                            v-model="row.relationTableId"
                            :data="codeTableData"
                            @current-change="selectTreeChange($event, row)"
                            @node-click="nodeClick"
                            :filterNodeMethod="filterNode"
                            filterable
                        ></ce-select-drop>
                        <el-button type="text" title="关联代码表配置" icon="el-icon-s-tools" @click="showTableSettings(row, $index)"></el-button>
                    </div>
                    
                </template>
                <template slot="codeTableColumn" slot-scope="{row , $index}">
                    <dg-select class="width100" v-model="row.codeTableColumn" :data="row.codeTableColumnData" filterable
                                    ></dg-select>
                </template>
                <template slot="codeTableName" slot-scope="{row , $index}">
                    <dg-select class="width100" v-model="row.codeTableName" :data="row.codeTableNameData" filterable
                                    ></dg-select>
                </template>
            </common-table>
        </el-form>
        <preView ref="preview"></preView>
    </div>
</template>

<script>
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {listMixins} from "@/api/commonMethods/list-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/dataset-manage/service-mixins/service-mixins";
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
import {treeMethods} from "@/api/treeNameCheck/treeName";
export default {
    name: "codeTableConversion",
    mixins: [commonMixins, servicesMixins, listMixins],
    components : {
        preView,
    },
    props: {
        sourceData: Object,
        rowData: Object,
    },
    data() {
        return {
            defaultProps :{
                value: 'id',
                label: 'name',
                children: 'children',
                disabled:"disabled",
            },
            convertColumnCodeData : [],
            codeTableNameData : [],
            codeTableColumnData : [],
            codeTableData : [],
            rules: {},
            newColumnTxt: "请输入字段名",
            title: "参数设置1",
            width: "1000px",
            tHeadData: [
                {
                    prop: "toConvertColumnCode",
                    label: "待转换代码字段",
                    align: 'left'
                },{
                    prop: "isNewColumns",
                    label: "是否新增名称字段",
                    width : '220',
                    align: 'left'
                }, {
                    prop: "relationTableId",
                    align: "left",
                    label: "关联代码表",
                }, {
                    prop: "codeTableColumn",
                    label: "代码表代码字段",
                }, {
                    prop: "codeTableName",
                    label: "代码表代码名称",
                },{
                    prop: "operate",
                    align: "center",
                    width: "120"
                }
            ],
            dataTypeOpt: [],
            tableData: [
                {
                    "toConvertColumnCode":"",
                    "isNewColumns": false,
                    "newColumnName":"",
                    "relationTableId":"",
                    "codeTypeColumn":"",
                    "codeTable":"",
                    "codeTableColumn":"",
                    "codeTableName":"",
                },
            ],
            operateIcons: [
                {
                    icon: "&#xe65f;",
                    tip: "删除",
                    type: "delete",
                    clickFn: this.deleteVar
                },
            ],
            headerIcon: {
                title: "添加参数",
                icon: "el-icon-circle-plus",
                clickFn: this.addVar
            },
        }
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        preview() {
            this.$refs.preview.show(this.rowData, this.sourceData);
        },
        nodeClick() {

        },
        async selectTreeChange(val, row, isInit = false) {
            const vm = this, {settings} = vm;
            let services = vm.$services("dataReady");
            let data = {
                addDataSetId:'',
                currentDataSetId:val.id,
            };
            if(!isInit){ 
                row.codeTableColumn = "", 
                row.codeTableName = "";
                row.codeTypeColumn = "";
                row.codeTable = "";
            }
            settings.loading = true;
            row.codeTableNameData = [],row.codeTableColumnData = [], row.typeData = [];
            await services.getLogicDataColumn(data,settings).then(res => {
                if(res.data.status === 0) {
                    let result = res.data.data;
                    result.columns[0].dimension.forEach(item => {
                        let object = {
                            label : item.name || item.code,
                            value : item.code,
                        }, codeObject = {
                            label : item.name || item.code,
                            value : item.code,
                        }, typeObject = {
                            label : item.name || item.code,
                            value : item.code,
                        };
                        row.codeTableNameData.push(object);
                        row.codeTableColumnData.push(codeObject);
                        row.typeData.push(typeObject);
                    })
                }
            })
        },
        showTableSettings(row, index) {
            const vm=this;
            let layer = vm.$dgLayer({
                title : "关联代码表" ,
                content : require("./dialog/settings.vue"),
                maxmin:false,
                move:false,
                props : {
                    rowData : JSON.parse(JSON.stringify(row)),
                    codeTableData : JSON.parse(JSON.stringify(vm.codeTableData)),
                },
                area : ["604px" , "420px"],
                on : {
                    submit(form){
                        if(form.relationTableId !== row.relationTableId) vm.selectTreeChange({id :  form.relationTableId}, row);
                        vm.tableData[index].codeTypeColumn = form.codeTypeColumn;
                        vm.tableData[index].codeTable = form.codeTable;
                        vm.tableData[index].relationTableId = form.relationTableId;
                    },
                    // tableChange(val) {
                    //     vm.tableData[index].relationTableId = val.id;
                    //     vm.selectTreeChange(val, row);
                    // }
                }
            })
        },
        /**
         * 类型变化 重置变量值
         * */
        varChange(val, row) {
            row.varValue = "";
        },
        addVar() {
            this.tableData.push({
                "toConvertColumnCode":"",
                "isNewColumns": false,
                "newColumnName":"",
                "relationTableId":"",
                "codeTypeColumn":"",
                "codeTable":"",
                "codeTableColumn":"",
                "codeTableName":""
            })
        },
        /**
         * 删除变量
         * @param row
         * @param inx
         */
        deleteVar(row, inx) {
            this.tableData.splice(inx, 1);
        },
        clearD() {

        },
        changePage() {
        },
        /**
         * 参数名称输入校验
         */
        checkSelectIndex(val, index, key) {
            let reg = new RegExp(/^[A-Za-z_][A-Za-z_0-9]*$/);
            if (!reg.test(val)) {
                this.$message.warning("索引由数字、字母、下划线组成且不以数字开头");
                this.tableData[index].name = "";
            }
        },
        validate(...arg) {
            return this.$refs.form.validate(...arg);
        },
        save(fn,showTip) {
            const vm = this, {settings} = this;
            let services = this.$services("plugin3");
            let isFullFill = true;
            vm.tableData.forEach(item => {
                if (item.toConvertColumnCode === '' ||
                    item.relationTableId === '' ||
                    item.codeTableColumn === '' ||
                    item.codeTableName === '') isFullFill = false;
                item.isNewColumn = item.isNewColumns ? "1" : "0";
            })
            if (!isFullFill) {
                this.$message.warning("请填写完整信息");
                return;
            }
            settings.loading = true;
            services.codeSavePlugin({tranStepId:vm.sourceData.id, cicadaCodeConversionSettings : vm.tableData, settings}).then(res => {
                if (res.data.status === 0) {
                    showTip && this.$message.success("保存成功");
                    fn && fn();
                    vm.$emit("setPreviewAble");
                    this.visible = false;
                }
            })
        },
        async init() {
            await this.getCodeTableData();
            await this.getDataSourceTreeData();
            const vm = this, {modelingServices, modelingMock, settings} = this;
            let services = this.$services("plugin3");
            vm.convertColumnCodeData = [];
            services.codeQueryData(vm.sourceData.id, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    result.inputColumn.forEach(item => {
                        let object = {
                            label : item.columnZhName || item.columnName,
                            value : item.columnName,
                        }
                        vm.convertColumnCodeData.push(object);
                    });
                    vm.tableData = result.pluginMeta.cicadaCodeConversionSettings.map(item => {
                        vm.selectTreeChange({id : item.relationTableId},item, true);
                        item.isNewColumns = item.isNewColumn === "1";
                        return item;
                    });
                    if (vm.tableData.length === 0) vm.addVar();
                }
            })
        },
        async getCodeTableData() {
            const vm = this, {datasetServices, datasetMock,settings} = this;
            let services = this.$services("dataset");
            settings.loading = true;
            vm.codeTableData = [];
            await services.queryDataSetTree(true, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    if(result && result.length){
                        let treeData = vm.filterChildren( result[0].children);
                        vm.codeTableData = treeData.map(item =>{
                            // item.disabled = true;
                            return item;
                        });
                        // vm.setExpandData(vm.data);
                    }
                }
            });
        },
        async getDataSourceTreeData(){
            const vm = this , { listApi , listMock , settings } = this;
            let services = this.$services("dataSource");
            settings.loading = true;
            await services.getDataSourceTree(settings,true).then(async res =>{
                if (res.data.status === 0){
                    let result = await vm.filterChildren(res.data.data);
                    vm.codeTableData.push(result[0]);
                }
            });
        },
        filterChildren(data){
            const vm = this;
            return data.filter(child => {
                child.name = child.label;
                if(child.children && child.children.length) child.children = vm.filterChildren(child.children);
                if(child.instanceType){
                    return child.instanceType.toLowerCase() !== "elasticsearch" && (child.children && child.children.length || child.belongType);
                }else {
                    return child.children && child.children.length || child.belongType;
                }

            })
        },
        filterChildren(data){
             const vm = this;
            return data.filter(child => {
                if(child.children && child.children.length) child.children = vm.filterChildren(child.children);
                 return child.children && child.children.length || child.belongType;
            })
        },
        preview() {
            const vm = this;
            vm.$nextTick(()=>{
                vm.$refs.preview.show(this.rowData, this.sourceData);
            });
            
        },
    },
    mounted() {
        this.init();
    }
}
</script>

<style scoped lang="less">
.is-require {
    &::before {
        content: "*";
        color: #F5222D;
        margin-right: 4px;
    }
}
.treeSelectC{
    display: inline-block;
}
.model_status {
    padding: 0 5px;
    font-size: 18px;
}
.ce-date {
    width: 100%;
}
.ce-number {
    width: 100%;
    &.el-input-number/deep/ .el-input__inner {
        text-align: left;
    }
}
    .red{
        color:red;
    }

</style>
