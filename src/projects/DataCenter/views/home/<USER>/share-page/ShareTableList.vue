<template>
    <div class="MyShareList ce-list">
        <!-- <ce-tab-panel :value.sync="activeN" :data="tabData" @tabClick="tabChange"></ce-tab-panel> -->

        <div class="ce-list_filter">
            <dg-select v-model="activeN" group-name="tag" class='wid20'
                            group :data="tabData" @change="tabChange"></dg-select>
            <div>
                <el-button type="primary" @click="showShare">{{ shareTxt }}</el-button>
                <el-button @click="batchCancelShare" v-if="!showStyle">{{ cancelShare }}</el-button>

                <el-input
                        class="ce-list_input"
                        size="mini"
                        placeholder="请输入资源名称搜索"
                        v-model.trim="filterSource"
                        v-input-limit:trim
                        @keyup.enter.native="searchTable"
                >
                    <i
                            class="el-icon-search el-input__icon poi"
                            slot="suffix"
                            @click="searchTable">
                    </i>
                </el-input>

                <el-input
                        class="ce-list_com"
                        size="mini"
                        :placeholder="`请输入${filterLabel}名称搜索`"
                        v-model.trim="filterTxt"
                        v-input-limit:trim
                        @keyup.enter.native="searchTable"
                >
                    <el-select slot="prepend" class="ce-list_preSel" v-model="filterType" @change="searchTable">
                        <el-option v-for="opt in filterOpt"
                                   :key="opt.value"
                                   :label="opt.label"
                                   :value="opt.value"
                        ></el-option>
                    </el-select>
                    <i
                            class="el-icon-search el-input__icon poi"
                            slot="suffix"
                            @click="searchTable">
                    </i>
                </el-input>
                <el-radio-group class="ml10" v-model="showType">
                    <el-radio-button v-for="radio in typeOpt" :key="radio.label" :title="radio.tip"
                                     :label="radio.label"><em class="icon" v-html="radio.icon"></em></el-radio-button>
                </el-radio-group>
            </div>
        </div>
        <div class="ce-list_table">
            <share-table-page
                    :page-size="pageSize"
                    :page-sizes="pageSizes"
                    ref="share"
                    @getSelectVo="getSelectVo"
                    :showStyle="showStyle" is-edited></share-table-page>
        </div>
    </div>
</template>

<script>
import ShareTablePage from "@/projects/DataCenter/views/home/<USER>/share/ShareTablePage";
import {countPage} from "@/projects/DataCenter/views/home/<USER>/share-page/countSharePage";
// import CeTabPanel from "@/components/signed-components/ceTabPanel/CeTabPanel";
import {common} from "@/api/commonMethods/common";

export default {
    name: "MyShareList",
    components: {
        // CeTabPanel,
        ShareTablePage
    },
    mixins: [countPage ,common],
    data() {
        return {
            shareTxt: "添加分享",
            cancelShare: "取消分享",
            filterSource: "",
            filterType: "",
            filterOpt: [
                {label: "全部", value: ""},
                {label: "用户", value: "user"},
                {label: "角色", value: "role"},
            ],
            filterTxt: "",
            multipleSelection: [],//表格多选信息
            tabDataInfo : [
                {
                    label: "全部",
                    value: ""
                }, {
                    value: "dataSet",
                    label: "数据集",
                    tag: '数据集'
                },
                {
                    value: "serviceAll",
                    label: "全部服务",
                    tag: '服务'
                },
                {
                    value: "dataService",
                    label: "数据查询",
                    tag: '服务'
                },
                {
                    value : "dashboard",
                    label : "仪表盘",
                    tag: '仪表盘'
                },{
                    value : "modelService",
                    label : "模型分析",
                    tag: '服务'
                },{
                    value : "compareService",
                    label : "比对订阅",
                    tag: '服务'
                },{
                    value : "informationVerification",
                    label : "信息核查",
                    tag: '服务'
                }
                ,{
                    value : "dataCollision",
                    label : "数据碰撞",
                    tag: '服务'
                }
            ],
        }
    },
    computed: {
        filterLabel() {
            const vm = this , {filterType} = vm;
            if(filterType) return this.filterOpt.find(op => op.value === this.filterType).label;
            else return "被分享对象";
        }
    },
    methods: {
        searchTable() {
            const {activeN, filterSource, filterTxt, filterType} = this;
            // 参数 资源类型 资源名称关键字 用户名 角色名
            let user = "", role = "";
            filterType === 'user' ? user = filterTxt : role = filterTxt;
            this.$refs.share.getTableData(activeN, filterSource, filterTxt, filterType);
        },
        clearAndSearch() {
            this.sourceType = "";
            this.filterSource = "";
            this.filterTxt = "";
            this.searchTable();
        },
        getSelectVo(value) {
            this.multipleSelection = value;
        },
        /**
         * 批量取消分享
         * */
        batchCancelShare() {
            const vm = this;
            if(!vm.multipleSelection.length){
                this.$message.warning("请选择取消分享的资源");
                return ;
            }
            vm.confirm("取消分享", "取消分享后，资源将无法使用！确认取消分享?",()=>{
                vm.$refs.share.cancelShareSource(vm.multipleSelection);
                vm.multipleSelection = [];
            })
        },
        /**
         * 添加分享
         */
        showShare() {
            const vm = this;
            let layer = this.$dgLayer({
                title: "添加分享",
                content: require("@/projects/DataCenter/views/home/<USER>/share-page/AddShare"),
                move: false,
                btn: ['确定', '取消'],
                props : {
                    activeN : this.activeN === "serviceAll" ? "" : this.activeN
                },
                on: {
                    refresh() {
                        vm.searchTable();
                    },
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
                yes: function (index, layero) {
                    layer.$children[0].save(index);
                },
                cancel: function (index, layero) {
                    // 关闭对应弹窗的ID
                    layer.close(index);
                    return false;
                },
                area: ["1000px", "600px"],
                btnAlign: 'r',

            });
        }
    },
    created() {
        this.countSize();
    }
}
</script>

<style scoped lang="less">
.wid20 {
    width: 20%;
}
.ce-list {

    &_filter {
        height: 32px;
        text-align: right;
        padding: 10px 0;
        display: flex;
        justify-content: space-between;
    }

    &_input {
        width: 20rem;
        margin-left: 10px;
    }

    &_table {
        height: calc(100% - 20px - 2rem);
    }

    &_com {
        width: 24rem;
        margin-left: 10px;
    }

    &_preSel {
        width: 100px;
    }
}
</style>
