<template>
    <div class="nodeStyle">
        <HistogramStyle :node="editNode" v-if="editNode.code === 'BarChartWidget' || editNode.code === 'StackBarChartWidget'" />
        <LineStyle :node="editNode" v-if="editNode.code === 'LineChartWidget'" />
        <PieStyle :node="editNode" v-if="editNode.code === 'PieChartWidget'" />
        <TableStyle :node="editNode" v-if="editNode.code === 'TableChartWidget'" />
        <MapStyle :node="editNode" v-if="editNode.code === 'MapChartWidget'" />
    </div>
</template>

<script>
    import {globalBus} from "@/api/globalBus";
    import HistogramStyle from './chartSetting/HistogramStyle'
    import LineStyle from './chartSetting/LineStyle'
    import PieStyle from './chartSetting/PieStyle'
    import TableStyle from './chartSetting/TableStyle'
    import MapStyle from './chartSetting/MapStyle'
    export default {
        name: "NodeStyle" ,
        components : {
            HistogramStyle ,
            LineStyle,
            PieStyle ,
            TableStyle ,
            MapStyle
        },
        props : {
            plug :Object
        },
        data(){
            return {
                editNode : {}
            }
        },
        methods : {
            setVisualPanel() {
                globalBus.$on('editVisualPanel', this.getVisualId );
            },
            getVisualId(node) {
                this.editNode = node;
            }
        },
        created() {
            this.setVisualPanel();
        },
        destroyed() {
            globalBus.$off('editVisualPanel', this.getVisualId);
        }
    }
</script>

<style scoped>

</style>