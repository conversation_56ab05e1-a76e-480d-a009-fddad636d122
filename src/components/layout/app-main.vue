<template>
    <section class="l-main-content">

        <el-row>
            <transition name="fade-transform" mode="out-in">
                <!-- 页面布局 -->
                <div
                        class="dg-container-box"
                        v-if="$route.meta.layout == 'page'"
                        :style="$route.path == '/dashboard' ? 'padding:0px;margin:0 10px;' : ''"
                >
                    <keep-alive :include="cachedViews">
                        <router-view :key="key"/>
                    </keep-alive>
                </div>

                <!-- 卡片布局 -->
                <dg-card
                        class="l-card-box"
                        shadow="never"
                        none-border
                        :header="$route.meta.pageTitle || $route.meta.title"
                        v-else
                >
                    <keep-alive :include="cachedViews">
                        <router-view :key="key"/>
                    </keep-alive>
                </dg-card>
            </transition>
        </el-row>
        <!--            <el-row class="copyright">Copyright ©2019 厦门市巨龙信息科技有限公司 UI体验策划部出品</el-row>-->

    </section>
</template>

<script>
    export default {
        name: "AppMain",

        computed: {
            cachedViews() {
                return this.$store.state.tagsView.cachedViews;
            },
            key() {
                return this.$route.fullPath;
            }
        },

        // create author: tangdm descript: repaire the scrollbar goto up;
        watch: {
            $route() {
                // this.$refs.scrollbar.wrap.scrollTop = 0;
                // this.$refs.scrollbar.moveY = 0;
            }
        }
    };
</script>
<style scoped>
    .l-main-content {
        height: calc(100% - 48px);
        overflow: hidden;
    }
    .dg-container-box {
        height: calc(100vh - 5rem - 48px);
        margin:0 1rem;
    }

</style>
<style>
    .l-card-box > .el-card__body {
        height: calc(100vh - 5rem - 140px);
        overflow: hidden;
    }
</style>
