/**
 * 数据准备 接口
 * @Author: qiuzx
 * @Date: 2021-07-14
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/dataSetOperation/",d_root = "/manage/dataWarehouse/";
const root1 = "/editDataSet/";

/*
* 初始化树
*@params
* @returns {Promise<*>}
* */

export async function queryDataSetTree(hasDataObj ,settings,currentDataSetId) {
    return await reqUtil.get({
        url : root + "queryDataSetTree",
        settings : settings
    },{hasDataObj,currentDataSetId})
}

/*
* 获取数据集分页
*@params id ,name , page , pageSize ,
* @returns {Promise<*>}
* */

export async function getDataSetPage(params , settings ) {
    return await reqUtil.get({
        url : root + "getDataSetPage",
        settings : settings
    }, params)
}

/*
*  校验是否被可视化使用
 * @param param type:join 左右关联,filter 过滤
 * @returns {Promise<*>}
* */
export async function checkDataSetUserInVisual( id ) {
    return await reqUtil.get({
        url: root +  'checkDataSetUserInVisual'
    } , {id});
}

/*
* 删除数据集
*@params
* @returns {Promise<*>}
* */

export async function deleteDataSet(id ) {
    return await reqUtil.get({
        url : root + "deleteDataSet"
    }, { id })
}


/**
 *
 */
export async function queryDatasetBaseTableC(params , settings) {
    return await reqUtil.post({
        url: d_root + 'queryDatasetBaseTableC',
        settings : settings
    }, params );
}

/*
*  查询数仓 树及对象
 * @param
 * @returns {Promise<*>}
* */
export async function getDatabaseDataObjectTree(settings) {
    return await reqUtil.get({
        url: root + 'getDatabaseDataObjectTree',
        settings : settings
    });
}

/*
* 授权数据集
*@params
* @returns {Promise<*>}
* */

export async function accreditLogicDataObj(dataObjIds ,dataSetTreeNodeId ,settings  ) {
    return await reqUtil.post({
        url : root + "accreditLogicDataObj",
        settings : settings
    }, {dataObjIds , dataSetTreeNodeId})
}

/*
*添加数据集到面板
*@params id
* @returns {Promise<*>}
* */

export async function getLogicDataColumn( data, settings) {
    return await reqUtil.get({
        url : root1 + "getLogicDataColumn",
        settings : settings
    },data )
}

/*
* 添加树节点
*@params
* @returns {Promise<*>}
* */

export async function addDataSetTreeNode(busiId , name) {
    return await reqUtil.get({
        url : root + "addDataSetTreeNode"
    }, {busiId , name})
}

/*
* 树节点删除
*@params
* @returns {Promise<*>}
* */

export async function deleteDataSetTreeNode(nodeId ) {
    return await reqUtil.get({
        url : root + "deleteDataSetTreeNode"
    }, {nodeId})
}

/*
* 树节点重命名
*@params
* @returns {Promise<*>}
* */

export async function reNameDataSetTreeNode(classifyId , name , oldName) {
    return await reqUtil.get({
        url : root + "reNameDataSetTreeNode"
    }, {classifyId , name , oldName})
}

/*
* 添加数据源里的根据树节点获取列表
*@params
* @returns {Promise<*>}
* */

export async function getClassifyFeatures(dataObjId,settings) {
    return await reqUtil.get({
        url : root + "getClassifyFeatures",
        settings
    }, {dataObjId })
}

/*
*数据集预览
*@params maps {dataSetId:数据集id,fields：[]字段的名称}
* @returns {Promise<*>}
* */

export async function previewLogicDataSet(previewVo , settings) {
    return await reqUtil.post({
        url : root1 + "previewLogicDataSet" ,
        settings : settings,
        errorMsg : false
    }, previewVo  )
}

/*
*  预览（列表）
 * @param tableId , tableName ,limit , dbType
 * @returns {Promise<*>}
* */
export async function dataSetPreview( previewVo,settings) {
    return await reqUtil.post({
        url : "/dataSetOperation/preview",
        settings : settings
    }, previewVo);
}

/*
*数据集保存
*@params maps {column,name,dataObjId,dataSetTreeNodeId}
* @returns {Promise<*>}
* */

export async function saveAll(data , settings) {
    return await reqUtil.post({
        url : root + "saveAll" ,
        settings : settings
    }, data)
}

/**
 * 批量添加数据集
 * @param params
 * @param settings
 * @return {Promise<*>}
 */
export async function batchSaveAll({params , settings}){
    return await reqUtil.post({
        url : root + "batchSaveAll" ,
        settings
    }, params)
}
/*
*获取数据类型
* * */
export async function getAllTypes( settings) {
    return await reqUtil.get({
        url : "/udfOperatorManage/getParameterTypes" ,
        settings : settings
    } )
}

/*
*获取数据类型  新
* * */
export async function getDataSetType( settings) {
    return await reqUtil.get({
        url : "/dataSetOperation/getDataSetType" ,
        settings : settings
    } )
}

//保存过滤条件
export async function filterCondition(params,settings ) {
    return await reqUtil.post({
        url : root1 + "filter",
        settings : settings
    }, params )
}

/*
*  获取数据集（源数据集和自定义数据集 合并的树）
 * @param
 * @returns {Promise<*>}
* */

export async function datasetList(settings,currentDataSetId) {
    return await reqUtil.get({
        url : "/dataSet/list" ,
        settings : settings
    },{currentDataSetId})
}

/*
* 查询字段
*@params
* @returns {Promise<*>}
* */

export async function getLogicColumn(condition="" , id , settings) {
    return await reqUtil.get({
        url : root1 + "getLogicColumn",
        settings : settings
    }, {condition ,id} )
}

/*
* 新建自助数据集
*@params
* @returns {Promise<*>}
* */

export async function createLogicDataSet(id ,name ,settings) {
    return await reqUtil.get({
        url : root1 + "createLogicDataSet",
        settings : settings
    }, { id, name  })
}

/*
* 左右关联
*@params
*
* @returns {Promise<*>}
* */

export async function join(params ,settings) {
    return await reqUtil.post({
        url : root1 + "join",
        settings : settings
    }, params )
}

/*
*  左右关联 ，条件过滤回显
 * @param param type:join 左右关联,filter 过滤
 * @returns {Promise<*>}
* */
export async function getJoinAndFilter( dataSetId , type ) {
    return await reqUtil.get({
        url: root1 +  'getJoinAndFilter'
    } , {dataSetId , type});
}

/*
* 保存
*@params columns  , logicDataSetId , metaDataObjIds , name
* @returns {Promise<*>}
* */

export async function save(saveDataSetVo ,settings ) {
    return await reqUtil.post({
        url : root1 + "save",
        settings :settings
    }, saveDataSetVo )
}

/*
*添加数据集到面板
*@params id
* @returns {Promise<*>}
* */

export async function getLogicDatasetColumns( logicDatasetId , settings) {
    return await reqUtil.get({
        url : root1 + "getLogicDatasetColumns",
        settings : settings
    }, {logicDatasetId } )
}

/*
* 另保存
*@params columns  , logicDataSetId , metaDataObjIds , name
* @returns {Promise<*>}
* */

export async function saveAs (saveDataSetVo ,settings ) {
    return await reqUtil.post({
        url : root1 + "saveAs",
        settings :settings
    }, saveDataSetVo )
}

/*
* 修改保存
* */
export async function editColumns (dataMap,name,dataSetId ,settings ) {
    return await reqUtil.post({
        url : root1 + "editColumns?dataSetId="+dataSetId+"&name="+name,
        settings :settings
    }, dataMap)
}

/*
*获取同步字段
*@params syncColumnVo
* @returns {Promise<*>}
* */

export async function getSyncLogicDataColumn(logicDataObjId) {
    return await reqUtil.get({
        url : root1 + "getSyncLogicDataColumn"
    }, {logicDataObjId} )
}

/*
* 同步数据结构
*@params
*
* @returns {Promise<*>}
* */
//原来的
export async function syncColumn( params ,settings ) {
    return await reqUtil.post({
        url : root1 + "syncColumn" ,
        settings : settings
    } , params)
}
//数据准备
export async function syncColumnDCThree( params ,settings ) {
    return await reqUtil.post({
        url : root1 + "syncColumnDCThree" ,
        settings : settings
    } , params)
}

/*
*  查询所有授权角色
 * @param isDataSet , dataSetId
 * @returns {Promise<*>}
* */
export async function queryAllRoleAuth( dataSetId  ) {
    return await reqUtil.post({
        url: '/dataSetAuth/queryAllRoleAuth'
    } , dataSetId );
}

/*
*  查询所有授权用户
 * @param isDataSet , dataSetId
 * @returns {Promise<*>}
* */
export async function getAllUserAuth(  dataSetId  ) {
    return await reqUtil.post({
        url: '/dataSetAuth/getAllUserAuth'
    } , dataSetId);
}

//添加数据对象  （授权前使用）
export async function dataSetDCThreeAuthRegister( dataSetAuthVo ,settings ) {
    return await reqUtil.post({
        url: '/dataSetAuth/dataSetDCThreeAuthRegister',
        settings : settings
    } , dataSetAuthVo);
}

/*
*  批量授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function dataDCThreeSetsAuth( dataSetAuthVo ,settings ) {
    return await reqUtil.post({
        url: '/dataSetOperation/dataDCThreeSetsAuth',
        settings : settings
    } , dataSetAuthVo);
}

/*
*  单独授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function addDCThreeDataSetAuth( dataSetAuthVo , settings ) {
    return await reqUtil.post({
        url: '/dataSetOperation/addDCThreeDataSetAuth' ,
        settings : settings
    } , dataSetAuthVo);
}

/*
*  查询所有授权数据
 * @param id
 * @returns {Promise<*>}
* */
export async function queryDataSetTableAllByUserOrRole( dwId , ids ) {
    return await reqUtil.post({
        url:  '/dataSetAuth/queryDataSetTableAllByUserOrRole'
    } , {dwId , ids});
}

/*
*  查询所有授权数据
 * @param dwid
 * @returns {Promise<*>}
* */
export async function queryDataBaseTableAll( dwId ) {
    return await reqUtil.get({
        url: d_root + 'queryDataBaseTableAll'
    } , {dwId});
}

/*
*  批量取消授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function cancelDCThreeDataSetsAuth( dataSetAuthVo  ) {
    return await reqUtil.post({
        url: '/dataSetOperation/cancelDCThreeDataSetsAuth'
    } , dataSetAuthVo);
}

export async function getDataSetForJurisdiction( id  ,settings) {
    return await reqUtil.get({
        url: '/dataSetOperation/getDataSetForJurisdiction?id='+id,
        settings : settings
    });
}


export async function getDataPreparationTree( id  ,settings) {
    return await reqUtil.get({
        url:d_root+ 'getDataPreparationTree',
        settings : settings
    });
}

/*
*  移动
 * @param param dataSetID treeNodeId
 * @returns {Promise<*>}
* */
export async function moveLogicDataSet( data,settings) {
    return await reqUtil.get({
        url: root +  'moveLogicDataSet',
        settings : settings
    } , data);
}

export async function getDataSourceTree  (settings,isLogic,dataSpace) {
    return await reqUtil.get({
        url: root + 'getDataSourceTree  ',
        settings : settings
    },{isLogic,dataSpace});
}


/*
*  获取报错字段
* @param logicid
* */
export async function getErrorColumns( data,settings) {
    return await reqUtil.get({
        url: root1 +  'getErrorColumns',
        settings : settings
    } , data);
}

/*
*  获取同步字段
* @param logicDataObjId
* */
export async function getSyncLogicDataColumns( data,settings) {
    return await reqUtil.get({
        url: root1 +  'getSyncLogicDataColumns',
        settings : settings
    } , data);
}

/*
*  同步字段
* @param DELETE  ADD EDIT
* */
export async function syncColumnDataSet( data,settings) {
    return await reqUtil.post({
        url: root1 +  'syncColumnDataSet',
        settings : settings
    } , data);
}

/*
*  同步字段 - 覆盖同步
* @param
* */
export async function syncColumns( data,settings) {
    return await reqUtil.get({
        url:  '/metadata/syncColumns',
        settings : settings
    } , data);
}

/** 仪表盘分享 */
/**
 * 添加仪表盘对象到功能表
 * @param {*} settings
 */
export async function dashboardAuthRegister(params, settings) {
    return await reqUtil.post({
        url : "/dashboardAuth/dashboardAuthRegister",
        settings
    }, params)
}
/**
 * 添加仪表盘授权

 * @param {*} settings
 */
export async function addDashboardAuth(params, settings) {
    return await reqUtil.post({
        url : "/dashboardAuth/addDashboardAuth",
        settings
    }, params)
}

/**
 * 自助 数据集 保存
 * @param params
 *
 * @param settings
 * @return {Promise<*>}
 */
export async function saveSelfHelpDataSet(params , settings){
    return await reqUtil.post({
        url : 'dataSetOperation/saveSelfHelpDataSet',
        settings
    },params)
}

/**
 * 回显 自助数据集 join 配置
 * @param type
 * @param dataSetId
 * @param settings
 * @return {Promise<*>}
 */
export async function getJoinAndFilters(type ,dataSetId, settings){
    return await reqUtil.get({
        url : 'editDataSet/getJoinAndFilters',
        settings
    },{type , dataSetId})
}

/**
 * 更新自助数据集
 * @param params
 * @param settings
 * @return {Promise<*>}
 */
export async function editSelfHelpDataSet(params , settings){
    return await reqUtil.post({
        url : 'editDataSet/editSelfHelpDataSet ',
        settings
    },params)
}

