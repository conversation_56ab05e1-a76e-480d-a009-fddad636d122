<template>
    <div class="plugDetail" :style="{'margin-left': marginL }">
        <div class="arrow-i" :style="{'left':arrowP+'px'}"></div>
        <table>
            <tr v-for="(list , index) in detail" :key="index">
                <td valign="top" class="bdd p5">
                    <h1 class="b f14">{{list.title}}:</h1>
                    <div align="center"><img :src="list.img_path" width="400"></div>
                </td>
                <td valign="top" class="bdd p5">
                    <h1 class="b f14">说明:</h1>
                    <ol class="nav-ol">
                        <li v-for="(item , inx) in list.description" :key="inx">{{item}}</li>
                    </ol>
                </td>
            </tr>
        </table>
    </div>
</template>

<script>


    export default {
        name: "PlugDetail",
        data() {
            return {

            }
        },
        props: {
            detail: Array,
            arrowP : Number,
            marginL : String
        },
        methods: {

        }
    }
</script>

<style scoped>
    .plugDetail {
        position: absolute;
        top: 100% ;
        left:0;
        border: 1px solid #ddd;
        background: #fff;
        padding: 10px;
        box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.1);
        width: 600px;
        display: none;
        z-index: 100;
        color: #666;
    }

    .arrow-i {
        width: 16px;
        height: 11px;
        background: url("../../../assets/images/arrow/arrow.svg") no-repeat;
        position: absolute;
        top: -11px;
        left: 50%;
        margin-left: -8px;
    }
    .nav-ol {
        margin-bottom:5px;
        line-height:24px;
        padding-left:15px;
    }
    .nav-ol li {
        font-size:14px;
        list-style:decimal;
        text-align: left;
    }
</style>
