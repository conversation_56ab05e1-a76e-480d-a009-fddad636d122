<template>
    <div class="classificationCounting ce-plug_cont" v-loading="loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont" v-show="activeName === 'setting_base'">
            <div class="ce-plug_attr">
                <el-form size="mini" class="ce-form__m0" ref="fieldOptions">
                    <el-form-item v-for="(left) in fieldOptions">
                        <el-form-item v-for="(item) in left" :prop=item.prop>
                            <el-row :gutter="5">
                                <el-col v-if="item.is_require" class="ce-normal_item is-require " :span="7">
                                    <span>{{item.name}}</span>
                                </el-col>
                                <el-col v-if="!item.is_require" class="ce-normal_item " :span="7">
                                    <span>{{item.name}}</span>
                                </el-col>
                                <el-col :span="8">
                                    <el-select
                                            size="small"
                                            v-model="item.value"
                                            placeholder="请选择"
                                            filterable
                                    >
                                        <el-option
                                                v-for="item in columns"
                                                :key="item.code"
                                                :label="item.name"
                                                :value="item.code">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-row>
                        </el-form-item>
                    </el-form-item>
                </el-form>
            </div>
            <div class="attr_btn">
                <el-button
                        v-for="(btn , inx) in buttons"
                        :key="inx"
                        size="mini"
                        :type="btn.type"
                        @click="btn.clickFn"
                >{{btn.name}}
                </el-button>
            </div>
        </div>
        <div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
        <PlugSetting ref="setting" @saveFn="saveRowData"/><!--组件配置弹窗-->
    </div>
</template>

<script>
    import PlugSetting from '../ruleEditing/PlugSetting'
    import SettingSenior from '../component/SettingSenior'
    import PreView from "../component/PreView"

    export default {
        name: "AddressCleanPlugin",
        components: {
            PlugSetting,
            SettingSenior,
            PreView
        },
        props: {
            sourceData: Object,
            row: Object
        },
        data() {
            return {
                addbtn: "添加",
                loading: false,
                dataTypes: ['Float', 'Double', 'BigDecimal'],
                isSeniorShow: {},
                activeName: 'setting_base',
                tab_panel: [
                    {
                        label: '基本设置',
                        name: 'setting_base'
                    }, {
                        label: '高级设置',
                        name: 'setting_senior'
                    }
                ],
                buttons: [
                    {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary'
                    }, {
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                ],
                status: false,
                fieldOptions: {
                    value: {
                        detailAddress: {name: "详细地址", value: "", prop: "detailAddress", is_require: true},
                        provinceName: {name: "省", value: "", prop: "provinceName", is_require: false},
                        cityName: {name: "市", value: "", prop: "cityName", is_require: false},
                        areaName: {name: "区县", value: "", prop: "areaName", is_require: false},
                        streetName: {name: "街道", value: "", prop: "streetName", is_require: false},
                    }
                },
                inputType: [],
                rowData: '',
                groupFieldName: '',
                columns: []
            }
        },
        methods: {
            saveRowData() {
                this.dataDispose = this.rowData.serviceOrgId;
            },
            editFn(data) {
                let rowData = {
                    columnCode: '数据处理表达式code',
                    columnName: '数据处理表达式',
                    serviceOrgId: data,
                };
                this.rowData = rowData;
                this.$refs.setting.openSetting(this.sourceData, this.rowData);
            },

            selectDataType(val) {
                val.hasPrecision = this.checkDataType(val.outputTypeValue);
            },
            init() {
                let _this = this;
                this.stepId = this.sourceData.id;
                this.$axios.get("/addressClean/getAddressCleanPluginPage?tranStepId=" + this.stepId).then(res => {
                    if (res.data.status === 1) {
                        _this.$message.error(res.data.msg);
                    } else {
                        let pluginMeta = res.data.data.pluginMeta;
                        res.data.data.column.forEach(i => {
                            let column = {
                                name: i.columnZhName !== null ? i.columnZhName : i.columnName,
                                code: i.columnName,
                                type: i.columnType
                            };
                            _this.columns.push(column);
                        });
                        _this.unWrapPluginMeta(pluginMeta);

                    }
                }).catch(err => {
                    console.log(err);
                    _this.$message.error("服务器发生异常，请联系管理员")
                })
            },
            // 分组统计删除操作
            deleteClassificationData(row, index) {
                this.jbxxFieldMappers = this.fieldOptions.filter(e => e !== row);
            },
            unWrapPluginMeta(pluginMeta) {
                for (const key1 in this.fieldOptions) {
                    for (const key2 in this.fieldOptions[key1]) {
                        this.fieldOptions[key1][key2].value = pluginMeta[this.fieldOptions[key1][key2].prop];
                    }
                }
            },
            wrapPluginMeta(fieldOptions) {
                let pluginMeta = {};
                for (const k in fieldOptions.value) {
                    pluginMeta[fieldOptions.value[k].prop] = fieldOptions.value[k].value;
                }
                return pluginMeta;
            },
            validParam(fieldOptions) {
                for (const key1 in fieldOptions) {
                    for (const key2 in fieldOptions[key1]) {
                        let temp = fieldOptions[key1][key2];
                        if (temp.is_require && !temp.value) {
                            this.$message.error("请先选择" + temp.name);
                            return false;
                        }
                    }
                }
                return true;
            },
            saveFn() {
                let _this = this;
                if (!_this.validParam(_this.fieldOptions)) {
                    return;
                }
                let pluginMeta = _this.wrapPluginMeta(_this.fieldOptions);
                _this.loading = true;
                this.$axios.post("/addressClean/saveAddressClean?stepId=" + this.stepId, pluginMeta).then(res => {
                    if (res.data.status === 1) {
                        _this.$message.error(res.data.msg);
                    } else {
                        if (this.$refs.settingSenior.fieldTableData.length <= 0) {
                            this.initSettingSenior();
                        } else {
                            this.saveColumn();
                        }
                        _this.$message.success("保存成功")
                    }
                    _this.loading = false;
                }).catch(err => {
                    _this.loading = false;
                    _this.$message.error("服务器发生异常，请联系管理员")
                })

            },
            initSettingSenior() {
                this.$refs.settingSenior.initValue();
            },
            saveColumn() {
                this.$refs.settingSenior.saveFieldTable();
            },
            checkDataType(val) {
                if (this.dataTypes.indexOf(val) > -1) {
                    return true;
                } else {
                    return false;
                }
            },
            previewFn() {
                this.$refs.preview.show(this.row, this.sourceData.id);
            },
            add() {
                this.jbxxFieldMappers.push(
                    {
                        name: "", relation_type: "", value: "", operateIcons: [
                            {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteClassificationData}
                        ]
                    }
                );
            }
        },
        created() {
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: true,
                isSampleExpr: true,
                isThread: true,
                isException: true,
                notTransition: true,
            };
            this.init();
        }
    }
</script>

<style scoped>
    .el-row {
        margin-bottom: 5px;
    }

    .countingRadio {
        width: 100%;
        text-align: center;
    }

    .countingContent {
        margin-top: 10px;
    }

    .countingSelect {
        height: 5px;
    }

    .btn_new_model {
        float: right;
        margin-bottom: 5px;
        margin-top: -27px;
        margin-left: 3px;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>