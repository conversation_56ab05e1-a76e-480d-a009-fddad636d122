<template>
    <common-dialog
            custom-class="ce-common_dialog ce-dialog__body-p0"
            :width="width"
            title="详情"
            :visible.sync="dialogVisible"
            @closed="clearD"
    >
        <CodeTableDetail v-if="reNew" :row="row"/>
        <div slot="footer">
            <el-button @click="closeEditDialog" size="mini">关闭</el-button>
        </div>
    </common-dialog>
</template>

<script>
    import CodeTableDetail from "./CodeTableDetail";
    import CommonDialog from "@/components/common/dialog/CommonDialog"

    export default {
        name: "DetailDialog",
        components: {
            CodeTableDetail,
            CommonDialog
        },
        data() {
            return {
                dialogVisible: false,
                width: '600px',
                reNew: false,
                row: {}
            }
        },
        methods: {
            show(row) {
                this.dialogVisible = true;
                this.reNew = true;
                this.row = row;
            },
            clearD() {
                this.reNew = false
            },
            closeEditDialog() {
                this.dialogVisible = false;
            }
        }
    }
</script>

<style scoped>

</style>