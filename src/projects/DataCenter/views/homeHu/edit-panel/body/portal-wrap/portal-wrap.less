.cont {
  height: 100%;
  /deep/.el-header {
    display: flex;
    align-items: center;
    padding: 0;
  }
  /deep/.el-aside {
    height: 100%;
  }
  /deep/.el-main {
    padding: 16px;
    overflow: hidden;
    box-sizing: border-box;
    background-color: rgb(232, 232, 232);
  }
  /deep/.el-container {
    overflow: hidden;
  }
  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #ffff;
    img {
      width: 21rem;
    }
    p {
      font-size: 1rem;
      color: rgba( #000000, 0.85);
    }
  }
}