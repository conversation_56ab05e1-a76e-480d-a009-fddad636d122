<!-- 
设置结果分析库 弹窗 
-->
<template>
    <el-form :model="form" label-width="110px" label-position="right" label-suffix=":" :rules="rules" ref="form">
        <div class=" ce-form__tip">{{ tip }}</div>
        <el-form-item prop="id" label="分析结果库">
            <dg-tree-drop ref="selectTree" v-model="form.id" :props="defaultProps" @current-change="selectTreeChange"
                :data="dataSourceData" :filterNodeMethod="filterNode" filterable check-leaf check-strictly :tree-props="{
                    'default-expanded-keys': [form.id]
                }" placeholder="请选择分析结果库"></dg-tree-drop>
        </el-form-item>
        <el-form-item prop="schema" label="模式" v-if="isShowModel">
            <el-input v-model.trim="form.schema" placeholder="请输入模式(限50个字符)" maxlength="50"
                v-input-limit:chartCode></el-input>
        </el-form-item>
        <dialog-btn-group v-footer :data="btnGroup" />
    </el-form>
</template>
<script>
import { mapGetters } from "vuex";
import { outputMixins } from "@/api/commonMethods/output";
import { treeMethods } from "@/api/treeNameCheck/treeName";
import dialogBtnGroup from "dc-front-plugin/src/components/DialogFooterBtn/DialogFooterBtn.vue";
export default {
    name: 'setAnalysisPage',
    mixins: [outputMixins],
    components: {
        dialogBtnGroup
    },
    data() {
        return {
            tip: "分析结果库用于存储模型分析结果数据。只能存储关系型数据",
            form: {
                id: "",
                schema: ""
            },
            defaultProps: {
                value: 'id',
                label: "label",
                children: "children",
            },
            dataSourceData: [],
            dbType: ["ORACLE", "GBASE", "MYSQL", "POSTGRESQL", "LIBRA", "HWMPP", "GREENPLUM", "HIVE", "TBASE", 'VERTICA'],
            selectDbType: "",
            rules: {
                id: [
                    { required: true, message: '请选择分析结果库', trigger: 'change' }
                ],
                schema: [
                    { required: true, message: '请选择模式', trigger: 'change' }
                ]
            },
            btnGroup: [
                {
                    name: '取消',
                    clickFn: this.cancelFn,
                    show: () => true
                },
                {
                    name: '确定',
                    btnBind: {
                        type: 'primary'
                    },
                    clickFn: this.submitFn,
                    show: () => true
                },
            ],
            settings: {
                loading: false
            }
        }
    },
    computed: {
        ...mapGetters(["userRight"]),
        rights() {
            const vm = this;
            let rights = [];
            if (vm.userRight) rights = vm.userRight.map(r => r.funcCode);
            return rights;
        },
        isShowModel() {
            return ['GREENPLUM', 'TBASE', 'POSTGRESQL', 'HWMPP', 'LIBRA', 'VERTICA','GBASE'].includes(this.selectDbType.toUpperCase())
        }
    },
    methods: {
        filterNode: treeMethods.methods.filterNode,
        cancelFn() {
            this.$emit("close")
        },
        submitFn() {
            const vm = this, { $services, settings } = vm, { id, schema } = vm.form;
            vm.$refs.form.validate(async valid => {
                if (valid) {
                    settings.loading = true;
                    $services("dataContact").saveAnalysis(id, schema, settings).then(res => {
                        if (res.data.code === 0) {
                            vm.$message.success("保存成功");
                            vm.$emit("success");
                        }
                    })
                }
            })
        },
        getSourceDataById(id, data) {
            let res = null;
            for (let it of data) {
                if (it.id === id) {
                    res = it;
                    break;
                }
                if (it.children?.length) {
                    res = this.getSourceDataById(id, it.children);
                    if (res) break;
                }
            }

            return res;
        },
        async getDataSourceData() {
            const vm = this, { $services, rights } = this, dw_service = $services("dataSource");
            let res = await dw_service.querySuperAllDirTree();
            if (res.data.code !== 0) return;
            vm.dataSourceData = vm.resTreeDataDispose(res.data.data);
        },
        selectTreeChange(val, node) {
            this.selectDbType = node.data.instanceType;
            this.form.schema = "";
        },
        getAnalysisForm() {
            const vm = this, { $services } = vm;
            return $services("dataContact").getAnalysisResult();
        },
        init() {
            const vm = this, { settings } = vm;
            settings.loading = true;
            Promise.all([
                vm.getAnalysisForm(),
                vm.getDataSourceData()
            ]).then(([res]) => {
                if (res.data.code === 0 && vm.dataSourceData.length) {
                    const result = res.data.data;
                    let sourceData;
                    if (result.id) {
                        sourceData = vm.getSourceDataById(result.id, vm.dataSourceData);
                    }
                    if (!sourceData) return;
                    vm.form.id = result.id || "";
                    vm.form.schema = result.schema || "";
                    vm.selectDbType = sourceData.instanceType;
                }
            }).finally(() => {
                settings.loading = false;
            })
        }
    },
    created() {
        this.init();
    }
}
</script>
<style lang="less" scoped>
.ce-form {
    &__tip {
        margin-bottom: 10px;
        font-size: .9rem;
        color: #999;
        padding: 0 20px;
    }
}
</style>
