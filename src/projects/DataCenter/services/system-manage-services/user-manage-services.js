/**
 * 用户管理 接口
 * @Author: wangjt
 * @Date: 2020-06-01
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'

const root = "/user/";

/*
*  获取用户列表
 * @param name , code , pageNum , pageSize,
 * @returns {Promise<*>}
* */
export async function queryUserList(params, settings) {
    return await reqUtil.post({
        url: root + 'queryUserList',
        settings: settings
    }, params);
}


/*
*  用户ID
 * @param
 * @returns {Promise<*>}
* */
export async function getUserRandom(settings) {
    return await reqUtil.post({
        url: root + 'getUserRandom',
        settings
    });
}

/*
*  ID 获取用户数据
 * @param
 * @returns {Promise<*>}
* */
export async function queryUserById(userId) {
    return await reqUtil.post({
        url: root + 'queryUserById',
    }, userId);
}

/*
*  添加 用户数据
 * @param
 * @returns {Promise<*>}
* */
export async function addUser(params, settings) {
    return await reqUtil.post({
        url: root + 'addUser',
        settings: settings
    }, params);
}

/*
*  删除 用户数据
 * @param
 * @returns {Promise<*>}
* */
export async function deleteUser(userId) {
    return await reqUtil.post({
        url: root + 'deleteUser'
    }, userId);
}

/*
*  重置密码
 * @param
 * @returns {Promise<*>}
* */
export async function resetPassword(userId, passWord) {
    return await reqUtil.get({
        url: root + 'resetPassword'
    }, {userId, passWord});
}

/*
*  编辑 用户数据
 * @param
 * @returns {Promise<*>}
* */
export async function editUser(params, settings) {
    return await reqUtil.post({
        url: root + 'editUser',
        settings: settings
    }, params);
}

/*
*  获取所有用户
 * @param loginWay
 * @returns {Promise<*>}
* */
export async function getAllUser(loginWay , userId) {
    let url = loginWay === 'dids' ? 'getAllUserNotIncludeItself?userId='+userId : 'getAllUser';
    return await reqUtil.get({
        url: root + url
    });

}


/*
*  校验用户名是否已存在
 * @param
 * @returns {Promise<*>}
* */
export async function isExistUser(userCode) {
    return await reqUtil.get({
        url: root + 'isExistUser',
        errorMsg :false
    }, {userCode});
}
