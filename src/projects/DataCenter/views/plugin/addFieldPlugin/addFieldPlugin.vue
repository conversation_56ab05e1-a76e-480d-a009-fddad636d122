<template>
    <div class="classificationCounting ce-plug_cont" v-loading="loading">
        <el-tabs v-model="activeName" type="border-card" class="ce-tabs_two">
            <el-tab-pane v-for="tab in tab_panel" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
        <div class="ce-tab_cont" v-show="activeName === 'setting_base'">

            <el-row :gutter="5">
                <el-col class="ce-normal_item is-require" :span="7">
                    <span>分组字段: </span>
                </el-col>
                <el-col :span="8">
                    <el-select
                            size="small"
                            v-model="groupFieldName"
                            placeholder="请选择"
                            filterable
                    >
                        <el-option
                                v-for="item in columns"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                        </el-option>
                    </el-select>
                </el-col>

            </el-row>


        </div>
        <div class="ce-tab_cont" v-show="activeName === 'setting_senior'">
            <div class="ce-plug_attr">
                <SettingSenior ref="settingSenior" :sourceData="sourceData" :isShow="isSeniorShow"/>
            </div>
        </div>
        <pre-view ref="preview"></pre-view>
        <PlugSetting ref="setting" @saveFn="saveRowData"/><!--组件配置弹窗-->
    </div>
</template>

<script>
    import PlugSetting from '../ruleEditing/PlugSetting'
    import SettingSenior from '../component/SettingSenior'
    import PreView from "../component/PreView"
    export default {
        name: "addField",
        components: {
            PlugSetting,
            SettingSenior,
            PreView
        },
        props: {
            sourceData: Object,
            row : Object
        },
        data() {
            return {
                addbtn: "添加",
                loading: false,
                dataTypes: ['Float', 'Double', 'BigDecimal'],
                isSeniorShow: {},
                activeName: 'setting_base',
                tab_panel: [
                    {
                        label: '基本设置',
                        name: 'setting_base'
                    }, {
                        label: '高级设置',
                        name: 'setting_senior'
                    }
                ],
                buttons: [
                    {
                        name: '保存',
                        clickFn: this.saveFn,
                        type: 'primary'
                    }, {
                        name: '预览',
                        clickFn: this.previewFn,
                        type: '',
                    }
                ],
                status: false,

                dataDispose: "",//数据处理表达式输入框绑定的数据
                //分组统计
                classificationFormData: [],
                inputType: [],
                rowData: '',
                groupFieldName:'',
                columns:[]
            }
        },
        methods: {
            saveRowData() {
                this.dataDispose = this.rowData.serviceOrgId;
            },
            editFn(data) {
                let rowData = {
                    columnCode: '数据处理表达式code',
                    columnName: '数据处理表达式',
                    serviceOrgId: data,
                };
                this.rowData = rowData;
                this.$refs.setting.openSetting(this.sourceData, this.rowData);
            },

            selectDataType(val) {
                val.hasPrecision = this.checkDataType(val.outputTypeValue);
            },
            init() {
                let _this = this;
                this.stepId = this.sourceData.id;
                this.$axios.get("/plugin/group/view?tranStepId=" + this.stepId)
                    .then(res => {
                        if (res.data.status === 1) {
                            _this.$message.error(res.data.msg);
                        } else {
                            let pluginMeta = res.data.data;
                            pluginMeta.types.forEach(item => {
                                let node = {
                                    lable: item.NAME,
                                    value: item.CODE,
                                };
                                _this.inputType.push(node);
                            });

                            pluginMeta.columns.forEach(i=>{
                                let column = {
                                    name: i.columnZhName!==null?i.columnZhName:i.columnName,
                                    code: i.columnName,
                                    type: i.columnType
                                };
                                _this.columns.push(column);
                            });

                            this.dataDispose = pluginMeta.pluginMeta.flatMapExpression;
                            let exprs = pluginMeta.aggregatorFieldExps;
                            this.groupFieldName=pluginMeta.groupFieldName,
                                _this.classificationFormData = [];
                            if (!exprs.length) {
                                this.add();
                            } else {
                                for (let i = 0; i < exprs.length; i++) {
                                    let hasPrecision = this.checkDataType(exprs[i].valType);
                                    _this.classificationFormData.push({
                                        tableData: [
                                            {
                                                num: i + 1,
                                            }
                                        ],
                                        hasPrecision: hasPrecision,
                                        algorithmValue: exprs[i].mergeExpression,
                                        inputValue: exprs[i].newFieldName,
                                        outputTypeValue: exprs[i].valType,
                                        lengthValue: exprs[i].length,
                                        precisionValue: exprs[i].precision,
                                        operateIcons: [
                                            {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteClassificationData}
                                        ]
                                    })
                                }
                            }


                        }
                    }).catch(err => {
                    console.log(err);
                    _this.$message.error("服务器发生异常，请联系管理员")
                })
            },
            // 分组统计删除操作
            deleteClassificationData(row, index) {
                this.classificationFormData.splice(index, 1);
            },
            saveFn() {
                if (this.countingModel === '简单统计') {
                    console.log("简单统计的数据：", this.simpleFormData);
                } else {
                    let _this = this;
                    let pluginMeta = {'flatMapExpression': this.dataDispose};
                    let reduceFieldExps = [];

                    if(_this.groupFieldName===''||_this.groupFieldName===null){
                        _this.$message.error("请先选择分组字段！");
                        return;
                    }

                    if (this.classificationFormData.length === 0) {
                        _this.$message.error("必须至少配置一个分组规则！");
                        return;
                    }
                    for (let i = 0; i < this.classificationFormData.length; i++) {
                        let d = this.classificationFormData[i];

                        if (d.algorithmValue === '' || d.inputValue === '' ||
                            d.lengthValue === '' || d.precisionValue === '' || d.outputTypeValue === '') {
                            _this.$message.error("规则没有输入完整！");
                            return;
                        }

                        if (isNaN(parseInt(d.lengthValue)) || isNaN(parseInt(d.precisionValue))) {
                            _this.$message.error("长度、精度必须输入数字！");
                            return;
                        }

                        if (!this.checkDataType(d.outputTypeValue)) {
                            d.precisionValue = 0;
                        }
                        reduceFieldExps.push({
                            'newFieldName': d.inputValue,
                            'valType': d.outputTypeValue,
                            'length': d.lengthValue,
                            'precision': d.precisionValue,
                            'replaceFieldName': '',
                            'mergeExpression': d.algorithmValue
                        });
                    }

                    pluginMeta['groupFieldName'] =  this.groupFieldName;
                    pluginMeta['reduceFieldExps'] = reduceFieldExps;
                    _this.loading = true;
                    this.$axios.post("/plugin/group/save?tranStepId=" + this.stepId, pluginMeta).then(res => {
                        if (res.data.status === 1) {
                            _this.$message.error(res.data.msg);
                        } else {
                            if (this.$refs.settingSenior.fieldTableData.length <= 0) {
                                this.initSettingSenior();
                            } else {
                                this.saveColumn();
                            }
                            _this.$message.success("保存成功")
                        }
                        _this.loading = false;
                    }).catch(err => {
                        _this.loading = false;
                        _this.$message.error("服务器发生异常，请联系管理员")
                    })
                }
            },
            initSettingSenior() {
                this.$refs.settingSenior.initValue();
            },
            saveColumn() {
                this.$refs.settingSenior.saveFieldTable();
            },
            checkDataType(val) {
                if (this.dataTypes.indexOf(val) > -1) {
                    return true;
                } else {
                    return false;
                }
            },
            previewFn() {
                this.$refs.preview.show(this.row , this.sourceData.id);
            },
            add() {
                this.classificationFormData.push(
                    {
                        tableData: [
                            {
                                num: this.classificationFormData.length + 1,
                            }
                        ],
                        algorithmValue: "",
                        inputValue: "",
                        outputTypeValue: "",
                        lengthValue: "",
                        precisionValue: 0,
                        hasPrecision: false,

                        operateIcons: [
                            {icon: "&#xe65f;", tip: "删除", clickFn: this.deleteClassificationData}
                        ],

                    }
                );
            }
        },
        created() {
            this.isSeniorShow = {
                partitionShow: false,
                fieldInput: false,
                fieldOutPut: true,
                isCopyOrDispense: true,
                isSampleExpr: true,
                isThread: true,
                isException: true,
                notTransition: true,
            };
            this.init();
        }
    }
</script>

<style scoped>
    .el-row {
        margin-bottom: 5px;
    }

    .countingRadio {
        width: 100%;
        text-align: center;
    }

    .countingContent {
        margin-top: 10px;
    }

    .countingSelect {
        height: 5px;
    }

    .btn_new_model {
        float: right;
        margin-bottom: 5px;
        margin-top: -27px;
        margin-left: 3px;
    }
</style>
<style lang="less" scoped src="../css/attrForm.less"></style>
<style lang="less" scoped src="../css/plugin.less"></style>