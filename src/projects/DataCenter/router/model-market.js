/*
* 模型市场路由
* */

import Index from "@/projects/DataCenter/views/model-market/Index";

export const marketRouter = [
    {
        path: "/market",
        component: Index,
        redirect: "/market/home",
        name: "Market",
        meta: {
            title: "主页",
            icon: "icon-Homee",
            noCache: true,
            breadcrumb: false
        },
        children: [
            {
                path: "/market/home",
                component: () => import("@/projects/DataCenter/views/model-market/home"),
                name: "MarketHome",
                meta: {
                    title: "",
                    icon: "iconf-homee",
                    noCache: true,
                    breadcrumb: false
                }
            }
        ],
    },
    {
        path: "/marketModelMarket",
        component: Index,
        redirect: "/market/modelMarketIndex",
        name: "marketModelMarket",
        meta: { title: "模型市场", icon: "icon-model-a", noCache: true, breadcrumb: false },
        children: [
            {
                path: "/market/modelMarketIndex",
                component: () => import("@/projects/DataCenter/views/model-market/modelMarket/index"),
                name: "MarketModelMarketIndex",
                meta: {
                    title: "",
                    icon: "icon-setting",
                    noCache: true,
                    breadcrumb: false
                },
            },
            {
                path: "/market/modelMarket",
                component: () => import("@/projects/DataCenter/views/model-market/modelMarket/modelDetails/index"),
                name: "MarketModelMarket",
                meta: {
                    title: "模型详情",
                    icon: "icon-setting",
                    noCache: true,
                    breadcrumb: false
                }
            },
        ]
    },
    {
        path: "/marketAttention",
        component: Index,
        redirect: "/market/attention",
        name: "marketAttention",
        meta: { title: "我的关注", icon: "icon-follow-a", noCache: true, breadcrumb: false },
        children: [
            {
                path: "/market/attention",
                component: () => import("@/projects/DataCenter/views/model-market/attention"),
                name: "MarketAttention",
                meta: {
                    title: "",
                    icon: "icon-setting",
                    noCache: true,
                    breadcrumb: false
                }
            },
        ]
    },
    {
        path: "/marketManagement",
        component: Index,
        redirect: "/market/management",
        name: "marketManagement",
        meta: { title: "我的模型", icon: "icon-user", noCache: true, breadcrumb: false },
        children: [
            {
                path: "/market/management",
                component: () => import("@/projects/DataCenter/views/model-market/management"),
                name: "MarketManagement",
                meta: {
                    title: "",
                    icon: "icon-setting",
                    noCache: true,
                    breadcrumb: false
                }
            },
        ]
    },
]
