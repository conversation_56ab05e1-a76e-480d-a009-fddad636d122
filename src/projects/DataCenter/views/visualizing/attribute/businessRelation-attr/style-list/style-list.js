import chartStyle from "../../chart-style"
import {attrStyle} from "../../attr-mixins/attr-style";

export default {
    name: "styleList" ,
    components : {
        chartStyle,
    },
    mixins :[attrStyle],
    props: {
        node: Object,
        dir_dataSetId : String
    },
    data(){
        return {
            activeTabs:["title", "legend"] ,
            styleList : {
                title :{
                    label :"标题内容",
                    settingForm :{
                        show : {
                            label : "显示标题" ,
                            type : "checkbox",
                        },
                        custom_style : {
                            label : "字体样式" ,
                            type : "sty_radio"
                        },
                        fontFamily : {
                            type : "select_font",
                            isLine : true
                        },
                        fontSize :{
                            type : "select_size" ,
                            isLine : true
                        },
                        fontWeight : {
                            type : "select_w",
                            isLine : true
                        },
                        fontStyle : {
                            type : "checkBtn" ,
                            isLine : true
                        },
                        color : {
                            type : "font_color" ,
                            isLine : true
                        },
                        align :{
                            type :"font_align" ,
                            isLine : true
                        },
                        text : {
                            type : "form_input" ,
                            pos : "right" ,
                            label : "标题名称" ,
                            maxlength :30 ,
                            width : "76px",
                            placeholder :"请輸入标题名称(限30个字符)"
                        },
                    },
                    form : {
                        show : true ,
                        custom_style : "default",
                        fontFamily : "sans-serif",
                        fontSize : "12px",
                        fontWeight : "normal",
                        fontStyle : [],
                        color : "#666" ,
                        text : "" ,
                        align : "center"
                    }
                },
                legend :{
                    label : "图表样式" ,
                    settingForm :{
                        
                        line_color : {
                            label : "配色方案" ,
                            type : "legend_color",
                            width : "90px"
                        },
                        node_show : {
                            label : "显示节点数据标签" ,
                            type : "checkbox",
                        },
                        edge_show : {
                            label : "显示边数据标签" ,
                            type : "checkbox",
                        },
                    },
                    form : {
                        line_color_type :"custom",
                        line_color_theme :"default",
                        line_color : [] ,
                        edge_show : false ,
                        node_show : false ,
                    }
                },
                
            },
            emitStyle :{}
        }
    },
    methods :{
        getAllStyle(){
            const {emitStyle} = this;
            let styles = {};
            for(let k in emitStyle){
                styles[k] = emitStyle[k];
            }
            return styles;
        },
        setTransAxisLabel(){
            this.styleList.xAxis.form.axisLabel = 0;
        },
        initValue(){
            const vm = this ;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                vm.styleList.title.form.text = vm.node.label;
                if (!vm.node.widget) return;
                let transverseCode = ['TransverseStackBarChartWidget' , 'TransverseBarChartWidget'];
                if(transverseCode.indexOf(vm.node.code) > -1 ){
                    vm.setTransAxisLabel();
                }
                let {style} = JSON.parse(JSON.parse(vm.node.widget).beforeData) ;
                let {title , legend , xAxis ,yAxis , fieldSetting } = vm.copyArrayObj(style);
                title ? vm.emitStyle.title = vm.styleList.title.form = title : vm.emitStyle.title = vm.styleList.title.form;
                legend ? vm.emitStyle.legend = vm.styleList.legend.form = legend : vm.emitStyle.legend = vm.styleList.legend.form;
                // xAxis ? vm.emitStyle.xAxis = vm.styleList.xAxis.form = xAxis : vm.emitStyle.xAxis = vm.styleList.xAxis.form ;
                // yAxis ? vm.emitStyle.yAxis = vm.styleList.yAxis.form = yAxis : vm.emitStyle.yAxis = vm.styleList.yAxis.form ;
                // fieldSetting ? vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form = fieldSetting : vm.emitStyle.fieldSetting = vm.styleList.fieldSetting.form ;
                vm.node.setData = false;
            })
        },
    },

}
