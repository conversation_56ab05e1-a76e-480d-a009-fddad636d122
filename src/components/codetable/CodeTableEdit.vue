<template>
    <div class="CodeTableEdit">
        <el-form ref="form" class="ce-form__m0 mb15" :model="form" size="small" :inline="true" label-width="120px">
            <el-form-item v-for="(item , key) in formData"
                          :key="key"
                          :prop="key"
                          :label="item.label + ' :'"
                          :rules="item.rules"
            >
                <el-input maxLength="31" v-model.trim="form[key]" :placeholder="item.placeholder"  :disabled="isSearching"></el-input>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="pl10 mb10">
            <el-col :span="5" v-for="(input , key) in searchO">
                <dg-tree-select
                        v-if="key === 'base'"
                        :placeholder="input.placeholder"
                        v-model="search[key]"
                        @change="input.nodeSelect"
                        :props="treeProps"
                        :data="input.option"
                        size="small"
                        check-leaf
                        check-strictly
                        filterable
                        :disabled="isSearching || !!row"
                ></dg-tree-select>
                <el-select
                        v-if="key !== 'base'"
                        size="small"
                        v-model="search[key]"
                        :placeholder="input.placeholder"
                        @change="input.nodeSelect"
                        filterable
                        :disabled="isSearching || !!row"
                >
                    <el-option
                            v-for="opt in input.option"
                            :key="opt.value"
                            :label="opt.name"
                            :value="opt.value"
                    ></el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-button size="mini" type="primary"  :disabled="isSearching" @click="searchNodes('')">查询</el-button>
            </el-col>
        </el-row>
        <simple-table
                class="mb5"
                :data="tableData"
                :columns="tableHeader"
                height="301px"
                v-loading="loading"
                :element-loading-text="'查询中'"
        >
            <template slot="header-operate">
                <em class="icon ce_link" title="添加" @click="addCode">&#xe6e1;</em>
            </template>
            <template slot="operate" slot-scope="scope">
                <em v-if="scope.row.code === '' && scope.row.codeValue === '' " slot="reference"
                    class="icon ce_link" title="删除"
                    @click="deleteCode(scope.row , scope.$index)"
                >&#xe65f;</em>
                <el-popconfirm
                        v-else
                        size="mini"
                        title="确认删除?"
                        @confirm="deleteCode(scope.row , scope.$index)"

                >
                    <em  slot="reference" class="icon ce_link" title="删除">&#xe65f;</em>
                </el-popconfirm>
            </template>
            <template slot="codeValue" slot-scope="scope">
                <div v-if="scope.row.isCustom">
                    <el-input v-if="scope.row.customEdited"
                              :ref="'input_key'+scope.$index"
                              size="mini"
                              @blur="scope.row.customEdited = false"
                              v-model.trim="scope.row.codeValue"
                              placeholder="请输入码key"></el-input>
                    <div v-else :title="scope.row.codeValue" class="ce-edit_box" @click="editCode(scope)">
                        <span>{{scope.row.codeValue}}</span>
                    </div>
                </div>
                <span v-else :title="scope.row.codeValue">{{scope.row.codeValue}}</span>
            </template>
            <template slot="code" slot-scope="scope">
                <el-input v-if="scope.row.isEdited"
                          :ref="'input_val'+scope.$index"
                          size="mini"
                          @blur="scope.row.isEdited = false"
                          v-model.trim="scope.row.code"
                          placeholder="请输入码值"></el-input>
                <div v-else :title="scope.row.code" class="ce-edit_box" @click="editValue(scope)">
                    <span>{{scope.row.code}}</span>
                </div>
            </template>
        </simple-table>
        <!--<CommonTable
                :data="tableData"
                :columns="tableHeader"
                cell-class-name="ce-ellipsis"
                height="334px"
                tooltip-effect="light"
                v-loading="loading"
                ref="table"
        >
            <template slot="header-operate" slot-scope="scope">
                <em class="icon ce_link" title="添加" @click="addCode">&#xe6e1;</em>
            </template>
            <template slot="operate" slot-scope="scope">
                <em v-if="scope.row.code === '' && scope.row.codeValue === '' " slot="reference"
                    class="icon ce_link" title="删除"
                    @click="deleteCode(scope.row , scope.$index)"
                >&#xe65f;</em>
                <el-popconfirm
                        v-else
                        size="mini"
                        title="确认删除?"
                        @confirm="deleteCode(scope.row , scope.$index)"

                >
                    <em  slot="reference" class="icon ce_link" title="删除">&#xe65f;</em>
                </el-popconfirm>


            </template>
            <template slot="codeValue" slot-scope="scope">
                <div v-if="scope.row.isCustom">
                    <el-input v-if="scope.row.customEdited"
                              :ref="'input_key'+scope.$index"
                              size="mini"
                              @blur="scope.row.customEdited = false"
                              v-model.trim="scope.row.codeValue"
                              placeholder="请输入码key"></el-input>
                    <div v-else :title="scope.row.codeValue" class="ce-edit_box" @click="editCode(scope)">
                        <span>{{scope.row.codeValue}}</span>
                    </div>
                </div>
                <span v-else :title="scope.row.codeValue">{{scope.row.codeValue}}</span>
            </template>
            <template slot="code" slot-scope="scope">
                <el-input v-if="scope.row.isEdited"
                          :ref="'input_val'+scope.$index"
                          size="mini"
                          @blur="scope.row.isEdited = false"
                          v-model.trim="scope.row.code"
                          placeholder="请输入码值"></el-input>
                <div v-else :title="scope.row.code" class="ce-edit_box" @click="editValue(scope)">
                    <span>{{scope.row.code}}</span>
                </div>
            </template>
        </CommonTable>-->
    </div>
</template>

<script>
    import CommonTable from "@/components/common/CommonTable";
    import dgTreeSelect from "ui-component-v4/lib/tree-select"
    import SimpleTable from "@/components/common/simpleTable/SimpleTable"
    export default {
        props: {row: Object},
        name: "CodeTableEdit",
        components: {
            CommonTable,
            dgTreeSelect ,
            SimpleTable
        },
        data() {
            return {
                dbType:["hbase"],
                isSearching : false ,
                loading :false ,
                treeProps: {
                    value: 'id',
                    label: "label",
                    children: "children",
                },

                form: {
                    cnName: '',
                    enName: '',
                },
                formData: {
                    cnName: {
                        label: '码表中文名',
                        placeholder: '请输入码表中文名',
                        rules: [
                            {required: true, message: '请输入码表中文名', trigger: 'blur'},
                            {max : 30 , message : "码表中文名限30个字符" , trigger : 'change'}
                        ]
                    },
                    enName: {
                        label: '码表英文名',
                        placeholder: '请输入码表英文名',
                        rules: [
                            {required: true, message: '请输入码表英文名', trigger: 'blur'},
                            {max : 30 , message : "码表英文名限30个字符" , trigger : 'change'}
                        ]
                    }
                },
                search: {
                    base: '',
                    chart: '',
                    nodes: '',
                    nodeV: ''
                },
                searchO: {
                    base: {
                        placeholder: '库名',
                        option: [],
                        nodeSelect: this.baseSelect
                    },
                    chart: {
                        placeholder: '数据源表名',
                        option: [],
                        nodeSelect: this.chartSelect
                    },
                    nodes: {
                        placeholder: 'key数据源字段名',
                        option: [],
                        nodeSelect: this.nodeItemSelect
                    },
                    nodeV: {
                        placeholder: 'val数据源字段名',
                        option: [],
                        nodeSelect: this.nodeVItemSelect
                    }
                },
                tableData: [],
                tableHeader: [
                    // {
                    //     label: '字段值',
                    //     prop: 'nodeValue',
                    //     align: 'center',
                    // },
                    {
                        label: '码key',
                        prop: 'codeValue',
                        align: 'center',
                        'show-overflow-tooltip': false
                    },
                    {
                        label: '码val',
                        prop: 'code',
                        align: 'center',
                        'show-overflow-tooltip': false
                    }, {
                        label: '',
                        prop: 'operate',
                        align: 'center',
                        width: '50px',
                        'show-overflow-tooltip': false
                    }
                ],
                total: 0,//查询的数据总数
                allNodesItem: [],//点查询后，将所有的字段赋值 [{value : 'node'} , {value : ''}]
                storeNodesItem: [],//存供可选择字段值
                storeSelected: [],
                storeOldValue: ''
            }
        },
        methods: {
            editFocus(inx , target){
                const vm = this;
                if(target === 'code'){
                    vm.tableData[inx].isEdited = true;
                    vm.$nextTick(()=>{
                        vm.$refs['input_val'+inx].focus();
                    });
                }else if(target === 'codeValue' ) {
                    if(vm.tableData[inx].customEdited !== undefined){
                        vm.tableData[inx].customEdited = true;
                        vm.$nextTick(()=>{
                            vm.$refs['input_key'+inx].focus();
                        });
                    }
                }
            },
            editCode(scope){
                scope.row.customEdited = true;
                const vm = this;
                vm.$nextTick(()=>{
                    vm.$refs['input_key'+scope.$index].focus();
                })
            },
            editValue(scope){
                scope.row.isEdited = true;
                const vm = this;
                vm.$nextTick(()=>{
                    vm.$refs['input_val'+scope.$index].focus();
                })

            },
            validate(){
                const vm = this;
                vm.$refs.form.validate(value =>{});
                if(vm.form.cnName === "" || vm.form.enName === ""){

                }else if( vm.search.base === ""){
                    vm.$message.warning("请选择库");
                }else if(vm.search.chart === ""){
                    vm.$message.warning("请选择数据源表名");
                }else if(vm.search.nodes === ""){
                    vm.$message.warning("请选择key数据源字段名");
                }else if(vm.search.nodeV === ""){
                    vm.$message.warning("请选择val数据源字段名");
                }
            },
            initEditInfo() {
                if (this.row && this.row.codeCnName) {
                    this.form.cnName = this.row.codeCnName;
                }
                if (this.row && this.row.codeEnName) {
                    this.form.enName = this.row.codeEnName;
                }
                if (this.row && this.row.keyFiledId && this.row.valFiledId) {
                    this.searchNodes("init");
                }
            },
            packageData(val) {
                const _this = this;
                let isHasDB = true;
                for (let i = val.length - 1; i >= 0; i--) {
                    if (val[i].children !== null) {
                        let isHas = _this.packageData(val[i].children);
                        if (!isHas) {
                            val.splice(i, 1)
                        }
                    } else {
                        val[i].children = [];
                        let flag = false;
                        for (let j = 0; j < _this.dbType.length; j++) {
                            let dbItem = _this.dbType[j].toLowerCase();
                            let instanceName = val[i].instanceType.toLowerCase();
                            if (instanceName.indexOf(dbItem) <= -1) {
                                flag = true;
                                break;
                            }
                        }
                        if (!flag) {
                            val.splice(i, 1);
                        }
                    }
                }

                if (val.length <= 0) {
                    isHasDB = false;
                }
                return isHasDB;
            },
            resTreeDataDispose(data) {
                const _this = this;
                for (let i = data.length - 1; i >= 0; i--) {
                    if (data[i].code !== "DATAWAREHOUSE_DIR") {
                        data.splice(i, 1);
                    } else {
                        let isHas = _this.packageData(data[i].children);
                        if (!isHas) {
                            data.splice(i, 1)
                        }
                    }
                }
                this.searchO["base"].option = data;
            },
            async init() {
                const _this = this;
                if(this.row){
                    this.loading = true;
                    this.isSearching = true;
                }
                await this.$axios.get("/manage/dataWarehouse/queryDirTree?id=&pId=&keyword=").then(res => {
                    if(res.data.status === 0){
                        _this.resTreeDataDispose(res.data.data);
                        if(_this.row && _this.row.dwInstanceId && _this.row.dwTableId) {
                            _this.selectChart(_this.row.dwInstanceId);
                            _this.selectField(_this.row.dwTableId);
                        }
                    }else {
                        _this.$message.warning(res.data.msg);
                    }
                });
                //console.log(_this.row)
                if (_this.row && _this.row.dwInstanceId && _this.row.dwTableId && _this.row.keyFiledId && _this.row.valFiledId) {
                    _this.search.base = _this.row.dwInstanceId;
                    _this.search.chart = _this.row.dwTableId;
                    _this.search.nodes = _this.row.keyFiledId;
                    _this.search.nodeV = _this.row.valFiledId;
                    _this.initEditInfo();
                }else if(_this.row && _this.row.id){
                    _this.searchNodes("init");
                    if (this.row && this.row.codeCnName) {
                        this.form.cnName = this.row.codeCnName;
                    }
                    if (this.row && this.row.codeEnName) {
                        this.form.enName = this.row.codeEnName;
                    }
                }else {
                    this.loading = false;
                    this.isSearching = false;
                }
            },
            addCode() {
                const vm = this;
                /*if (vm.tableData.length < vm.total) {
                    let nodes = vm.storeNodesItem[0];
                    nodes.isEdited = true;
                    vm.tableData.push(nodes);
                    vm.$nextTick(()=>{
                        let main = vm.$refs.table.$children[0].$refs.bodyWrapper,
                            child = vm.$refs.table.$children[0].$refs.bodyWrapper.children[0],
                            height =  child.clientHeight - main.clientHeight ;
                        main.scrollTop = height > 0 ? height : 0;
                        vm.$refs['input_val'+(vm.tableData.length - 1)].focus();
                    });
                    vm.storeNodesItem.splice(0 , 1);
                }else if(vm.tableData.length === vm.total){

                } else {
                    this.$message.info('暂无字段添加');
                }*/
                vm.tableData.push({
                    code :"" ,
                    nodeValue :"",
                    codeValue : "" ,
                    isCustom : true ,
                    customEdited : true ,
                    isEdited : false ,
                    orgValue : ""
                });
                vm.$nextTick(()=>{
                    vm.$refs['input_key'+(vm.tableData.length - 1)].focus();
                });
            },
            deleteCode(row, index) {
                this.tableData.splice(index, 1);
                if(!row.code) return;
                let node = this.allNodesItem.filter(n => {
                    return n.rowId === row.rowId;
                });
                if (node.length !== 0) {
                    this.storeNodesItem.push(node[0]);
                }
            },
            searchNodes(val) {
                let id = "";
                if (this.row) {
                    id = this.row.id;
                }
                this.loading = true;
                this.isSearching = true;
                this.storeNodesItem = [];
                let s = {mbId: id, valFiledId: this.search.nodes, keyFiledId: this.search.nodeV,type:val};
                this.$axios.post("/standermb/selectCodeValList", s).then(res => {
                    if (res.data.status === 0) {
                        res.data.data.forEach(d => {
                            d.isEdited =  false;
                            d.rowId = d.code + d.nodeValue;
                        });
                        this.tableData = res.data.data;
                    }
                    this.loading = false;
                    this.isSearching = false;
                }).catch(err =>{
                    this.loading = false;
                    this.isSearching = false;
                });
            },

            getAllNode(data) {
                this.allNodesItem = [];
                for (let i = 0; i < data.length; i++) {
                    let tableD = data[i];
                    this.allNodesItem.push(tableD);
                }
            },
            //搜索表
            baseSelect(val , node) {
                this.selectChart(val);
                this.search.chart = "";
                this.search.nodes = "";
                this.search.nodeV = "";
                this.tableData = [];
                this.searchO.nodes.option = [];
                this.searchO.nodeV.option = [];
                this.searchO.chart.option = [];
            },
            async selectChart(id) {
                const _this = this;
                await _this.$axios.get("/manage/dataWarehouse/queryDataBaseTableAll?dwId=" + id).then(res => {
                    if (res.data.status === 0) {
                        let datas = res.data.data;
                        _this.searchO.chart.option = [];
                        datas.forEach(item => {
                            let tableName = "";
                            if (item.name === "null" || item.name === null || item.name==="") {
                                tableName = item.code;
                            } else {
                                tableName = item.name + "(" + item.code + ")";
                            }
                            let node = {
                                name: tableName,
                                value: item.id,
                                id: item.id,
                            };
                            _this.searchO.chart.option.push(node);
                           /* if (_this.row && _this.row.chartId) {
                                _this.search.chart = _this.row.chartId;
                                _this.selectField(_this.row.chartId);
                            }*/
                        })
                    }
                })
            },
            //搜索字段
            chartSelect(val) {
                this.selectField(val);
                this.search.nodes = "";
                this.search.nodeV = "";
                this.searchO.nodes.option = [];
                this.searchO.nodeV.option = [];
                this.tableData = [];


            },
            selectField(id) {
                const _this = this;
                _this.$axios.get("/standermb/getFiledList?id=" + id).then(res => {
                    if (res.data.status === 0) {
                        if (_this.row && _this.row.keyFiledId && _this.row.keyFiledId !== "" && _this.row.valFiledId && _this.row.valFiledId !== "") {
                            _this.searchO.nodes.option = res.data.data;
                            _this.searchO.nodeV.option = res.data.data;
                        } else {
                            _this.searchO.nodes.option = res.data.data;
                            _this.searchO.nodeV.option = res.data.data;
                        }
                    }
                });
            },
            nodeItemSelect(item) {
                // this.searchNodes();
            },
            nodeVItemSelect(item) {
                // this.searchNodes();
            },


        },
        created() {
            this.init();

        }
    }
</script>

<style scoped>
    .input-insert {
        height: 32px
    }
    .ce-edit_box {
        min-height: 28px;
        line-height: 28px;
        border:1px solid #DCDFE6;
        border-radius: 4px;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
    }
    .ce-edit_box:hover {
        border-color: #C0C4CC;
        cursor: pointer;
    }

</style>
