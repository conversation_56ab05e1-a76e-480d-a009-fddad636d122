/**
 * 角色管理 接口
 * @Author: wangjt
 * @Date: 2020-05-06
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/role/" , f_root= "/function/";

/*
*  获取列表
 * @param code , name , pageNum , pageSize
 * @returns {Promise<*>}
* */
export async function queryRoleList(params, settings ) {
    return await reqUtil.post({
        url: root + 'queryRoleList',
        settings : settings
    },  params );
}


/*
*  添加角色
 * @param  functionId , roleCode , roleId , roleName
 * @returns {Promise<*>}
* */
export async function addRole(params, settings ) {
    return await reqUtil.post({
        url: root + 'addRole',
        settings : settings
    },  params );
}

/*
*  更新角色
 * @param  functionId , roleCode , roleId , roleName
 * @returns {Promise<*>}
* */
export async function updataRole(params, settings ) {
    return await reqUtil.post({
        url: root + 'updataRole',
        settings : settings
    },  params );
}
/*
*  更新角色权限
 * @param  functionId , roleCode , roleId , roleName
 * @returns {Promise<*>}
* */
export async function setAuth(params, settings ) {
    return await reqUtil.post({
        url: root + 'setAuth',
        settings : settings
    },  params );
}

/*
*  获取角色ID
 * @param
 * @returns {Promise<*>}
* */
export async function getRoleRandom() {
    return await reqUtil.post({
        url: root + 'getRoleRandom'
    });
}

/**
*  功能菜单
 * @param systemType
 * @returns {Promise<*>}
* */
export async function queryFunctionTree(systemType) {
    return await reqUtil.post({
        url: f_root + 'queryFunctionTree'
    },{systemType});
}



/*
*  删除角色
 * @param
 * @returns {Promise<*>}
* */
export async function deleteRole(roleId ) {
    return await reqUtil.post({
        url: root + 'deleteRole'
    },  roleId );
}

/*
*  获取 角色数据
 * @param
 * @returns {Promise<*>}
* */
export async function queryByRoleId(roleId ) {
    return await reqUtil.post({
        url: root + 'queryByRoleId'
    },  roleId );
}
/*
*  获取 角色数据
 * @param
 * @returns {Promise<*>}
* */
export async function queryRole(roleId ) {
    return await reqUtil.post({
        url: root + 'queryRole'
    },  roleId );
}

/*
*  获取 除admin所有角色数据
 * @param
 * @returns {Promise<*>}
* */
export async function queryAllRole( loginWay) {
    let url = loginWay === 'dids' ? 'queryDidsRoles' :'queryAllRole';
    return await reqUtil.post({
        url: root + url
    });
}

/*
*  获取所有用户
 * @param
 * @returns {Promise<*>}
* */
export async function getAllUser(loginWay ,userId , settings) {
    let url = loginWay === 'dids' ? 'getAllUserNotIncludeItself?userId='+userId : 'getAllUser';
    return await reqUtil.get({
        url: '/user/'+url,
        settings
    });
}
