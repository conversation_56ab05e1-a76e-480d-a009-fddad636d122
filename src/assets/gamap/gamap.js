!function (t = {}, i) {
    "object" == typeof exports && "undefined" != typeof module ? i(exports) : "function" == typeof define && define.amd ? define(["exports"], i) : i(t.L = {})
}(this, function (t) {
    "use strict";

    function i(t) {
        var i, e, n, o;
        for (e = 1, n = arguments.length; e < n; e++) {
            o = arguments[e];
            for (i in o) t[i] = o[i]
        }
        return t
    }

    function e(t, i) {
        var e = Array.prototype.slice;
        if (t.bind) return t.bind.apply(t, e.call(arguments, 1));
        var n = e.call(arguments, 2);
        return function () {
            return t.apply(i, n.length ? n.concat(e.call(arguments)) : arguments)
        }
    }

    function n(t) {
        return t._gmap_id = t._gmap_id || ++ci, t._gmap_id
    }

    function o(t, i, e) {
        var n, o, s, a;
        return a = function () {
            n = !1, o && (s.apply(e, o), o = !1)
        }, s = function () {
            n ? o = arguments : (t.apply(e, arguments), setTimeout(a, i), n = !0)
        }
    }

    function s(t, i, e) {
        var n = i[1], o = i[0], s = n - o;
        return t === n && e ? t : ((t - o) % s + s) % s + o
    }

    function a() {
        return !1
    }

    function r(t, i) {
        var e = Math.pow(10, void 0 === i ? 6 : i);
        return Math.round(t * e) / e
    }

    function h(t) {
        return t.trim ? t.trim() : t.replace(/^\s+|\s+$/g, "")
    }

    function l(t) {
        return h(t).split(/\s+/)
    }

    function u(t, i) {
        t.hasOwnProperty("options") || (t.options = t.options ? ui(t.options) : {});
        for (var e in i) t.options[e] = i[e];
        return t.options
    }

    function c(t, i, e) {
        var n = [];
        for (var o in t) n.push(encodeURIComponent(e ? o.toUpperCase() : o) + "=" + encodeURIComponent(t[o]));
        return (i && -1 !== i.indexOf("?") ? "&" : "?") + n.join("&")
    }

    function d(t, i) {
        return t.replace(di, function (t, e) {
            var n = i[e];
            if (void 0 === n) throw new Error("No value provided for variable " + t);
            return "function" == typeof n && (n = n(i)), n
        })
    }

    function _(t, i) {
        for (var e = 0; e < t.length; e++) if (t[e] === i) return e;
        return -1
    }

    function p(t) {
        return window["webkit" + t] || window["moz" + t] || window["ms" + t]
    }

    function m(t) {
        var i = +new Date, e = Math.max(0, 16 - (i - mi));
        return mi = i + e, window.setTimeout(t, e)
    }

    function f(t, i, n) {
        if (!n || fi !== m) return fi.call(window, e(t, i));
        t.call(i)
    }

    function g(t) {
        t && gi.call(window, t)
    }

    function v() {
    }

    function y(t) {
        if ("undefined" != typeof L && GAMap && GAMap.Mixin) {
            t = _i(t) ? t : [t];
            for (var i = 0; i < t.length; i++) t[i] === GAMap.Mixin.Events && console.warn("Deprecated include of GAMap.Mixin.Events: this property will be removed in future releases, please inherit from GAMap.Evented instead.", (new Error).stack)
        }
    }

    function x(t, i, e) {
        this.x = e ? Math.round(t) : t, this.y = e ? Math.round(i) : i
    }

    function w(t, i, e) {
        return t instanceof x ? t : _i(t) ? new x(t[0], t[1]) : void 0 === t || null === t ? t : "object" == typeof t && "x" in t && "y" in t ? new x(t.x, t.y) : new x(t, i, e)
    }

    function b(t, i) {
        if (t) for (var e = i ? [t, i] : t, n = 0, o = e.length; n < o; n++) this.extend(e[n])
    }

    function P(t, i) {
        return !t || t instanceof b ? t : new b(t, i)
    }

    function T(t, i) {
        if (t) for (var e = i ? [t, i] : t, n = 0, o = e.length; n < o; n++) this.extend(e[n])
    }

    function M(t, i) {
        return t instanceof T ? t : new T(t, i)
    }

    function C(t, i, e) {
        if (isNaN(t) || isNaN(i)) throw new Error("Invalid LatLng object: (" + t + ", " + i + ")");
        this.lat = +t, this.lng = +i, void 0 !== e && (this.alt = +e)
    }

    function A(t, i, e) {
        return t instanceof C ? t : _i(t) && "object" != typeof t[0] ? 3 === t.length ? new C(t[0], t[1], t[2]) : 2 === t.length ? new C(t[0], t[1]) : null : void 0 === t || null === t ? t : "object" == typeof t && "lat" in t ? new C(t.lat, "lng" in t ? t.lng : t.lon, t.alt) : void 0 === i ? null : new C(t, i, e)
    }

    function z(t, i, e, n) {
        if (_i(t)) return this._a = t[0], this._b = t[1], this._c = t[2], void (this._d = t[3]);
        this._a = t, this._b = i, this._c = e, this._d = n
    }

    function S(t, i, e, n) {
        return new z(t, i, e, n)
    }

    function k(t) {
        return document.createElementNS("http://www.w3.org/2000/svg", t)
    }

    function E(t, i) {
        var e, n, o, s, a, r, h = "";
        for (e = 0, o = t.length; e < o; e++) {
            for (n = 0, s = (a = t[e]).length; n < s; n++) r = a[n], h += (n ? "L" : "M") + r.x + " " + r.y;
            h += i ? re ? "z" : "x" : ""
        }
        return h || "M0 0"
    }

    function I(t) {
        return navigator.userAgent.toLowerCase().indexOf(t) >= 0
    }

    function B(t, i, e, n) {
        return "touchstart" === i ? O(t, e, n) : "touchmove" === i ? F(t, e, n) : "touchend" === i && H(t, e, n), this
    }

    function Z(t, i, e) {
        var n = t["_gmap_" + i + e];
        return "touchstart" === i ? t.removeEventListener(ue, n, !1) : "touchmove" === i ? t.removeEventListener(ce, n, !1) : "touchend" === i && (t.removeEventListener(de, n, !1), t.removeEventListener(_e, n, !1)), this
    }

    function O(t, i, n) {
        var o = e(function (t) {
            if ("mouse" !== t.pointerType && t.MSPOINTER_TYPE_MOUSE && t.pointerType !== t.MSPOINTER_TYPE_MOUSE) {
                if (!(pe.indexOf(t.target.tagName) < 0)) return;
                Q(t)
            }
            N(t, i)
        });
        t["_gmap_touchstart" + n] = o, t.addEventListener(ue, o, !1), fe || (document.documentElement.addEventListener(ue, R, !0), document.documentElement.addEventListener(ce, j, !0), document.documentElement.addEventListener(de, D, !0), document.documentElement.addEventListener(_e, D, !0), fe = !0)
    }

    function R(t) {
        me[t.pointerId] = t, ge++
    }

    function j(t) {
        me[t.pointerId] && (me[t.pointerId] = t)
    }

    function D(t) {
        delete me[t.pointerId], ge--
    }

    function N(t, i) {
        t.touches = [];
        for (var e in me) t.touches.push(me[e]);
        t.changedTouches = [t], i(t)
    }

    function F(t, i, e) {
        var n = function (t) {
            (t.pointerType !== t.MSPOINTER_TYPE_MOUSE && "mouse" !== t.pointerType || 0 !== t.buttons) && N(t, i)
        };
        t["_gmap_touchmove" + e] = n, t.addEventListener(ce, n, !1)
    }

    function H(t, i, e) {
        var n = function (t) {
            N(t, i)
        };
        t["_gmap_touchend" + e] = n, t.addEventListener(de, n, !1), t.addEventListener(_e, n, !1)
    }

    function W(t, i, e) {
        function n(t) {
            var i;
            if (ie) {
                if (!Bi || "mouse" === t.pointerType) return;
                i = ge
            } else i = t.touches.length;
            if (!(i > 1)) {
                var e = Date.now(), n = e - (s || e);
                a = t.touches ? t.touches[0] : t, r = n > 0 && n <= h, s = e
            }
        }

        function o(t) {
            if (r && !a.cancelBubble) {
                if (ie) {
                    if (!Bi || "mouse" === t.pointerType) return;
                    var e, n, o = {};
                    for (n in a) e = a[n], o[n] = e && e.bind ? e.bind(a) : e;
                    a = o
                }
                a.type = "dblclick", i(a), s = null
            }
        }

        var s, a, r = !1, h = 250;
        return t[xe + ve + e] = n, t[xe + ye + e] = o, t[xe + "dblclick" + e] = i, t.addEventListener(ve, n, !1), t.addEventListener(ye, o, !1), t.addEventListener("dblclick", i, !1), this
    }

    function G(t, i) {
        var e = t[xe + ve + i], n = t[xe + ye + i], o = t[xe + "dblclick" + i];
        return t.removeEventListener(ve, e, !1), t.removeEventListener(ye, n, !1), Bi || t.removeEventListener("dblclick", o, !1), this
    }

    function U(t, i, e, n) {
        if ("object" == typeof i) for (var o in i) q(t, o, i[o], e); else for (var s = 0, a = (i = l(i)).length; s < a; s++) q(t, i[s], e, n);
        return this
    }

    function V(t, i, e, n) {
        if ("object" == typeof i) for (var o in i) X(t, o, i[o], e); else if (i) for (var s = 0, a = (i = l(i)).length; s < a; s++) X(t, i[s], e, n); else {
            for (var r in t[we]) X(t, r, t[we][r]);
            delete t[we]
        }
        return this
    }

    function q(t, i, e, o) {
        var s = i + n(e) + (o ? "_" + n(o) : "");
        if (t[we] && t[we][s]) return this;
        var a = function (i) {
            return e.call(o || t, i || window.event)
        }, r = a;
        ie && 0 === i.indexOf("touch") ? B(t, i, a, s) : !ee || "dblclick" !== i || !W || ie && Fi ? "addEventListener" in t ? "mousewheel" === i ? t.addEventListener("onwheel" in t ? "wheel" : "mousewheel", a, !1) : "mouseenter" === i || "mouseleave" === i ? (a = function (i) {
            i = i || window.event, ot(t, i) && r(i)
        }, t.addEventListener("mouseenter" === i ? "mouseover" : "mouseout", a, !1)) : ("click" === i && Oi && (a = function (t) {
            st(t, r)
        }), t.addEventListener(i, a, !1)) : "attachEvent" in t && t.attachEvent("on" + i, a) : W(t, a, s), t[we] = t[we] || {}, t[we][s] = a
    }

    function X(t, i, e, o) {
        var s = i + n(e) + (o ? "_" + n(o) : ""), a = t[we] && t[we][s];
        if (!a) return this;
        ie && 0 === i.indexOf("touch") ? Z(t, i, s) : !ee || "dblclick" !== i || !G || ie && Fi ? "removeEventListener" in t ? "mousewheel" === i ? t.removeEventListener("onwheel" in t ? "wheel" : "mousewheel", a, !1) : t.removeEventListener("mouseenter" === i ? "mouseover" : "mouseleave" === i ? "mouseout" : i, a, !1) : "detachEvent" in t && t.detachEvent("on" + i, a) : G(t, s), t[we][s] = null
    }

    function Y(t) {
        return t.stopPropagation ? t.stopPropagation() : t.originalEvent ? t.originalEvent._stopped = !0 : t.cancelBubble = !0, nt(t), this
    }

    function J(t) {
        return q(t, "mousewheel", Y), this
    }

    function K(t) {
        return U(t, "mousedown touchstart dblclick", Y), q(t, "click", et), this
    }

    function Q(t) {
        return t.preventDefault ? t.preventDefault() : t.returnValue = !1, this
    }

    function $(t) {
        return Q(t), Y(t), this
    }

    function tt(t, i) {
        if (!i) return new x(t.clientX, t.clientY);
        var e = i.getBoundingClientRect(), n = e.width / i.offsetWidth || 1, o = e.height / i.offsetHeight || 1;
        return new x(t.clientX / n - e.left - i.clientLeft, t.clientY / o - e.top - i.clientTop)
    }

    function it(t) {
        return Bi ? t.wheelDeltaY / 2 : t.deltaY && 0 === t.deltaMode ? -t.deltaY / Le : t.deltaY && 1 === t.deltaMode ? 20 * -t.deltaY : t.deltaY && 2 === t.deltaMode ? 60 * -t.deltaY : t.deltaX || t.deltaZ ? 0 : t.wheelDelta ? (t.wheelDeltaY || t.wheelDelta) / 2 : t.detail && Math.abs(t.detail) < 32765 ? 20 * -t.detail : t.detail ? t.detail / -32765 * 60 : 0
    }

    function et(t) {
        be[t.type] = !0
    }

    function nt(t) {
        var i = be[t.type];
        return be[t.type] = !1, i
    }

    function ot(t, i) {
        var e = i.relatedTarget;
        if (!e) return !0;
        try {
            for (; e && e !== t;) e = e.parentNode
        } catch (t) {
            return !1
        }
        return e !== t
    }

    function st(t, i) {
        var e = t.timeStamp || t.originalEvent && t.originalEvent.timeStamp, n = Ti && e - Ti;
        n && n > 100 && n < 500 || t.target._simulatedClick && !t._simulated ? $(t) : (Ti = e, i(t))
    }

    function at(t) {
        return "string" == typeof t ? document.getElementById(t) : t
    }

    function rt(t, i) {
        var e = t.style[i] || t.currentStyle && t.currentStyle[i];
        if ((!e || "auto" === e) && document.defaultView) {
            var n = document.defaultView.getComputedStyle(t, null);
            e = n ? n[i] : null
        }
        return "auto" === e ? null : e
    }

    function ht(t, i, e) {
        var n = document.createElement(t);
        return n.className = i || "", e && e.appendChild(n), n
    }

    function lt(t) {
        var i = t.parentNode;
        i && i.removeChild(t)
    }

    function ut(t) {
        for (; t.firstChild;) t.removeChild(t.firstChild)
    }

    function ct(t) {
        var i = t.parentNode;
        i.lastChild !== t && i.appendChild(t)
    }

    function dt(t) {
        var i = t.parentNode;
        i.firstChild !== t && i.insertBefore(t, i.firstChild)
    }

    function _t(t, i) {
        if (void 0 !== t.classList) return t.classList.contains(i);
        var e = gt(t);
        return e.length > 0 && new RegExp("(^|\\s)" + i + "(\\s|$)").test(e)
    }

    function pt(t, i) {
        if (void 0 !== t.classList) for (var e = l(i), n = 0, o = e.length; n < o; n++) t.classList.add(e[n]); else if (!_t(t, i)) {
            var s = gt(t);
            ft(t, (s ? s + " " : "") + i)
        }
    }

    function mt(t, i) {
        void 0 !== t.classList ? t.classList.remove(i) : ft(t, h((" " + gt(t) + " ").replace(" " + i + " ", " ")))
    }

    function ft(t, i) {
        void 0 === t.className.baseVal ? t.className = i : t.className.baseVal = i
    }

    function gt(t) {
        return void 0 === t.className.baseVal ? t.className : t.className.baseVal
    }

    function vt(t, i) {
        "opacity" in t.style ? t.style.opacity = i : "filter" in t.style && yt(t, i)
    }

    function yt(t, i) {
        var e = !1, n = "DXImageTransform.Microsoft.Alpha";
        try {
            e = t.filters.item(n)
        } catch (t) {
            if (1 === i) return
        }
        i = Math.round(100 * i), e ? (e.Enabled = 100 !== i, e.Opacity = i) : t.style.filter += " progid:" + n + "(opacity=" + i + ")"
    }

    function xt(t) {
        for (var i = document.documentElement.style, e = 0; e < t.length; e++) if (t[e] in i) return t[e];
        return !1
    }

    function wt(t, i, e) {
        var n = i || new x(0, 0);
        t.style[Te] = (qi ? "translate(" + n.x + "px," + n.y + "px)" : "translate3d(" + n.x + "px," + n.y + "px,0)") + (e ? " scale(" + e + ")" : "")
    }

    function Lt(t, i) {
        t._gmap_pos = i, Ji ? wt(t, i) : (t.style.left = i.x + "px", t.style.top = i.y + "px")
    }

    function bt(t) {
        return t._gmap_pos || new x(0, 0)
    }

    function Pt() {
        U(window, "dragstart", Q)
    }

    function Tt() {
        V(window, "dragstart", Q)
    }

    function Mt(t) {
        for (; -1 === t.tabIndex;) t = t.parentNode;
        t.style && (Ct(), ze = t, Se = t.style.outline, t.style.outline = "none", U(window, "keydown", Ct))
    }

    function Ct() {
        ze && (ze.style.outline = Se, ze = void 0, Se = void 0, V(window, "keydown", Ct))
    }

    function At(t, i) {
        if (!i || !t.length) return t.slice();
        var e = i * i;
        return t = Et(t, e), t = St(t, e)
    }

    function zt(t, i, e) {
        return Math.sqrt(Rt(t, i, e, !0))
    }

    function St(t, i) {
        var e = t.length, n = new (typeof Uint8Array != void 0 + "" ? Uint8Array : Array)(e);
        n[0] = n[e - 1] = 1, kt(t, n, i, 0, e - 1);
        var o, s = [];
        for (o = 0; o < e; o++) n[o] && s.push(t[o]);
        return s
    }

    function kt(t, i, e, n, o) {
        var s, a, r, h = 0;
        for (a = n + 1; a <= o - 1; a++) (r = Rt(t[a], t[n], t[o], !0)) > h && (s = a, h = r);
        h > e && (i[s] = 1, kt(t, i, e, n, s), kt(t, i, e, s, o))
    }

    function Et(t, i) {
        for (var e = [t[0]], n = 1, o = 0, s = t.length; n < s; n++) Ot(t[n], t[o]) > i && (e.push(t[n]), o = n);
        return o < s - 1 && e.push(t[s - 1]), e
    }

    function It(t, i, e, n, o) {
        var s, a, r, h = n ? Fe : Zt(t, e), l = Zt(i, e);
        for (Fe = l; ;) {
            if (!(h | l)) return [t, i];
            if (h & l) return !1;
            r = Zt(a = Bt(t, i, s = h || l, e, o), e), s === h ? (t = a, h = r) : (i = a, l = r)
        }
    }

    function Bt(t, i, e, n, o) {
        var s, a, r = i.x - t.x, h = i.y - t.y, l = n.min, u = n.max;
        return 8 & e ? (s = t.x + r * (u.y - t.y) / h, a = u.y) : 4 & e ? (s = t.x + r * (l.y - t.y) / h, a = l.y) : 2 & e ? (s = u.x, a = t.y + h * (u.x - t.x) / r) : 1 & e && (s = l.x, a = t.y + h * (l.x - t.x) / r), new x(s, a, o)
    }

    function Zt(t, i) {
        var e = 0;
        return t.x < i.min.x ? e |= 1 : t.x > i.max.x && (e |= 2), t.y < i.min.y ? e |= 4 : t.y > i.max.y && (e |= 8), e
    }

    function Ot(t, i) {
        var e = i.x - t.x, n = i.y - t.y;
        return e * e + n * n
    }

    function Rt(t, i, e, n) {
        var o, s = i.x, a = i.y, r = e.x - s, h = e.y - a, l = r * r + h * h;
        return l > 0 && ((o = ((t.x - s) * r + (t.y - a) * h) / l) > 1 ? (s = e.x, a = e.y) : o > 0 && (s += r * o, a += h * o)), r = t.x - s, h = t.y - a, n ? r * r + h * h : new x(s, a)
    }

    function jt(t) {
        return !_i(t[0]) || "object" != typeof t[0][0] && void 0 !== t[0][0]
    }

    function Dt(t) {
        return console.warn("Deprecated use of _flat, please use GAMap.LineUtil.isFlat instead."), jt(t)
    }

    function Nt(t, i, e) {
        var n, o, s, a, r, h, l, u, c, d = [1, 4, 2, 8];
        for (o = 0, l = t.length; o < l; o++) t[o]._code = Zt(t[o], i);
        for (a = 0; a < 4; a++) {
            for (u = d[a], n = [], o = 0, s = (l = t.length) - 1; o < l; s = o++) r = t[o], h = t[s], r._code & u ? h._code & u || ((c = Bt(h, r, u, i, e))._code = Zt(c, i), n.push(c)) : (h._code & u && ((c = Bt(h, r, u, i, e))._code = Zt(c, i), n.push(c)), n.push(r));
            t = n
        }
        return t
    }

    function Ft(t, i) {
        var e, n, o, s, a = "Feature" === t.type ? t.geometry : t, r = a ? a.coordinates : null, h = [],
            l = i && i.pointToLayer, u = i && i.coordsToLatLng || Ht;
        if (!r && !a) return null;
        switch (a.type) {
            case"Point":
                return e = u(r), l ? l(t, e) : new hn(e);
            case"MultiPoint":
                for (o = 0, s = r.length; o < s; o++) e = u(r[o]), h.push(l ? l(t, e) : new hn(e));
                return new on(h);
            case"LineString":
            case"MultiLineString":
                return n = Wt(r, "LineString" === a.type ? 0 : 1, u), new dn(n, i);
            case"Polygon":
            case"MultiPolygon":
                return n = Wt(r, "Polygon" === a.type ? 1 : 2, u), new _n(n, i);
            case"GeometryCollection":
                for (o = 0, s = a.geometries.length; o < s; o++) {
                    var c = Ft({geometry: a.geometries[o], type: "Feature", properties: t.properties}, i);
                    c && h.push(c)
                }
                return new on(h);
            default:
                throw new Error("Invalid GeoJSON object.")
        }
    }

    function Ht(t) {
        return new C(t[1], t[0], t[2])
    }

    function Wt(t, i, e) {
        for (var n, o = [], s = 0, a = t.length; s < a; s++) n = i ? Wt(t[s], i - 1, e) : (e || Ht)(t[s]), o.push(n);
        return o
    }

    function Gt(t, i) {
        return i = "number" == typeof i ? i : 6, void 0 !== t.alt ? [r(t.lng, i), r(t.lat, i), r(t.alt, i)] : [r(t.lng, i), r(t.lat, i)]
    }

    function Ut(t, i, e, n) {
        for (var o = [], s = 0, a = t.length; s < a; s++) o.push(i ? Ut(t[s], i - 1, e, n) : Gt(t[s], n));
        return !i && e && o.push(o[0]), o
    }

    function Vt(t, e) {
        return t.feature ? i({}, t.feature, {geometry: e}) : qt(e)
    }

    function qt(t) {
        return "Feature" === t.type || "FeatureCollection" === t.type ? t : {
            type: "Feature",
            properties: {},
            geometry: t
        }
    }

    function Xt(t, i) {
        return new pn(t, i)
    }

    function Yt(t, i) {
        return new Pn(t, i)
    }

    function Jt(t) {
        return ae ? new Cn(t) : null
    }

    function Kt(t) {
        return re || he ? new kn(t) : null
    }

    function Qt(t) {
        var i = [], e = encodeURIComponent;
        for (var n in t) null !== t[n] && i.push(e(n) + "=" + e(t[n]));
        return i.join("&")
    }

    function $t(t) {
        if (Ei) {
            var i = new ActiveXObject("Microsoft.XMLDOM");
            return i.loadXML(t), i
        }
        return (new DOMParser).parseFromString(t, "text/xml")
    }

    function ti() {
    }

    function ii(t) {
        return JSON.parse(t)
    }

    function ei(t) {
        return t.type && "jsonp" == t.type ? new Wn(t) : new Gn(t)
    }

    function getNowFormatDate() {
        var a = new Date,
            b = "";
        var d = a.getFullYear();
        var c = a.getMonth() + 1;
        a = a.getDate();
        b += d + "-";
        b = 10 <= c ? b + (c + "-") : b + ("0" + c + "-");
        return 10 <= a ? b + a : b + ("0" + a)
    }

    function ckd() {
        var a = getNowFormatDate();
        return 1577807999000 <= (new Date(a.replace("-", ","))).getTime() ? !1 : !0
    }

    function ck() {
        if (!ckd()) return alert("\u60a8\u7684\u8f6f\u4ef6\u8bd5\u7528\u65f6\u95f4\u5df2\u7ecf\u5230\u671f\uff0c\u8bf7\u8054\u7cfb\u4f9b\u5e94\u5546!!!"),
            $("#mapDiv").remove(),
            window.close(),
            !1
    }

    function ni(t) {
        return parseInt(t, 16)
    }

    function oi(t) {
        var i;
        return 4 === t.length ? (i = t.replace("#", "").split(""), {
            r: ni(i[0] + i[0]),
            g: ni(i[1] + i[1]),
            b: ni(i[2] + i[2])
        }) : {r: ni(t.slice(1, 3)), g: ni(t.slice(3, 5)), b: ni(t.slice(5))}
    }

    function si(t) {
        var i, e = t.slice(t.indexOf("(") + 1, t.indexOf(")")).split(","), n = !1;
        return e = e.map(function (t, i) {
            return 3 !== i ? parseInt(t, 10) : n = !0, parseFloat(t)
        }), i = {r: e[0], g: e[1], b: e[2]}, n && (i.a = e[3]), i
    }

    function ai(t) {
        var i = t.slice(0, 1);
        return "#" === i ? oi(t) : "r" === i.toLowerCase() ? si(t) : (console.log("!Ooops! RGBvalues.color(" + t + ") : HEX, RGB, or RGBa strings only"), null)
    }

    function ri() {
    }

    function hi(t, i) {
        return null !== t && null !== i && -1 !== t.indexOf(i, t.length - i.length)
    }

    var li = Object.freeze;
    Object.freeze = function (t) {
        return t
    };
    var ui = Object.create || function () {
            function t() {
            }

            return function (i) {
                return t.prototype = i, new t
            }
        }(), ci = 0, di = /\{ *([\w_-]+) *\}/g, _i = Array.isArray || function (t) {
            return "[object Array]" === Object.prototype.toString.call(t)
        }, pi = "data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=", mi = 0,
        fi = window.requestAnimationFrame || p("RequestAnimationFrame") || m,
        gi = window.cancelAnimationFrame || p("CancelAnimationFrame") || p("CancelRequestAnimationFrame") || function (t) {
            window.clearTimeout(t)
        }, vi = (Object.freeze || Object)({
            freeze: li,
            extend: i,
            create: ui,
            bind: e,
            lastId: ci,
            stamp: n,
            throttle: o,
            wrapNum: s,
            falseFn: a,
            formatNum: r,
            trim: h,
            splitWords: l,
            setOptions: u,
            getParamString: c,
            template: d,
            isArray: _i,
            indexOf: _,
            emptyImageUrl: pi,
            requestFn: fi,
            cancelFn: gi,
            requestAnimFrame: f,
            cancelAnimFrame: g
        });
    v.extend = function (t) {
        var e = function () {
            this.initialize && this.initialize.apply(this, arguments), this.callInitHooks()
        }, n = e.__super__ = this.prototype, o = ui(n);
        o.constructor = e, e.prototype = o;
        for (var s in this) this.hasOwnProperty(s) && "prototype" !== s && "__super__" !== s && (e[s] = this[s]);
        return t.statics && (i(e, t.statics), delete t.statics), t.includes && (y(t.includes), i.apply(null, [o].concat(t.includes)), delete t.includes), o.options && (t.options = i(ui(o.options), t.options)), i(o, t), o._initHooks = [], o.callInitHooks = function () {
            if (!this._initHooksCalled) {
                n.callInitHooks && n.callInitHooks.call(this), this._initHooksCalled = !0;
                for (var t = 0, i = o._initHooks.length; t < i; t++) o._initHooks[t].call(this)
            }
        }, e
    }, v.include = function (t) {
        return i(this.prototype, t), this
    }, v.mergeOptions = function (t) {
        return i(this.prototype.options, t), this
    }, v.addInitHook = function (t) {
        var i = Array.prototype.slice.call(arguments, 1), e = "function" == typeof t ? t : function () {
            this[t].apply(this, i)
        };
        return this.prototype._initHooks = this.prototype._initHooks || [], this.prototype._initHooks.push(e), this
    };
    var yi = {
        on: function (t, i, e) {
            if ("object" == typeof t) for (var n in t) this._on(n, t[n], i); else for (var o = 0, s = (t = l(t)).length; o < s; o++) this._on(t[o], i, e);
            return this
        }, off: function (t, i, e) {
            if (t) if ("object" == typeof t) for (var n in t) this._off(n, t[n], i); else for (var o = 0, s = (t = l(t)).length; o < s; o++) this._off(t[o], i, e); else delete this._events;
            return this
        }, _on: function (t, i, e) {
            this._events = this._events || {};
            var n = this._events[t];
            n || (n = [], this._events[t] = n), e === this && (e = void 0);
            for (var o = {
                fn: i,
                ctx: e
            }, s = n, a = 0, r = s.length; a < r; a++) if (s[a].fn === i && s[a].ctx === e) return;
            s.push(o)
        }, _off: function (t, i, e) {
            var n, o, s;
            if (this._events && (n = this._events[t])) if (i) {
                if (e === this && (e = void 0), n) for (o = 0, s = n.length; o < s; o++) {
                    var r = n[o];
                    if (r.ctx === e && r.fn === i) return r.fn = a, this._firingCount && (this._events[t] = n = n.slice()), void n.splice(o, 1)
                }
            } else {
                for (o = 0, s = n.length; o < s; o++) n[o].fn = a;
                delete this._events[t]
            }
        }, fire: function (t, e, n) {
            if (!this.listens(t, n)) return this;
            var o = i({}, e, {type: t, target: this, sourceTarget: e && e.sourceTarget || this});
            if (this._events) {
                var s = this._events[t];
                if (s) {
                    this._firingCount = this._firingCount + 1 || 1;
                    for (var a = 0, r = s.length; a < r; a++) {
                        var h = s[a];
                        h.fn.call(h.ctx || this, o)
                    }
                    this._firingCount--
                }
            }
            return n && this._propagateEvent(o), this
        }, listens: function (t, i) {
            var e = this._events && this._events[t];
            if (e && e.length) return !0;
            if (i) for (var n in this._eventParents) if (this._eventParents[n].listens(t, i)) return !0;
            return !1
        }, once: function (t, i, n) {
            if ("object" == typeof t) {
                for (var o in t) this.once(o, t[o], i);
                return this
            }
            var s = e(function () {
                this.off(t, i, n).off(t, s, n)
            }, this);
            return this.on(t, i, n).on(t, s, n)
        }, addEventParent: function (t) {
            return this._eventParents = this._eventParents || {}, this._eventParents[n(t)] = t, this
        }, removeEventParent: function (t) {
            return this._eventParents && delete this._eventParents[n(t)], this
        }, _propagateEvent: function (t) {
            for (var e in this._eventParents) this._eventParents[e].fire(t.type, i({
                layer: t.target,
                propagatedFrom: t.target
            }, t), !0)
        }
    };
    yi.addEventListener = yi.on, yi.removeEventListener = yi.clearAllEventListeners = yi.off, yi.addOneTimeEventListener = yi.once, yi.fireEvent = yi.fire, yi.hasEventListeners = yi.listens;
    var xi = v.extend(yi), wi = Math.trunc || function (t) {
        return t > 0 ? Math.floor(t) : Math.ceil(t)
    };
    x.prototype = {
        clone: function () {
            return new x(this.x, this.y)
        }, add: function (t) {
            return this.clone()._add(w(t))
        }, _add: function (t) {
            return this.x += t.x, this.y += t.y, this
        }, subtract: function (t) {
            return this.clone()._subtract(w(t))
        }, _subtract: function (t) {
            return this.x -= t.x, this.y -= t.y, this
        }, divideBy: function (t) {
            return this.clone()._divideBy(t)
        }, _divideBy: function (t) {
            return this.x /= t, this.y /= t, this
        }, multiplyBy: function (t) {
            return this.clone()._multiplyBy(t)
        }, _multiplyBy: function (t) {
            return this.x *= t, this.y *= t, this
        }, scaleBy: function (t) {
            return new x(this.x * t.x, this.y * t.y)
        }, unscaleBy: function (t) {
            return new x(this.x / t.x, this.y / t.y)
        }, round: function () {
            return this.clone()._round()
        }, _round: function () {
            return this.x = Math.round(this.x), this.y = Math.round(this.y), this
        }, floor: function () {
            return this.clone()._floor()
        }, _floor: function () {
            return this.x = Math.floor(this.x), this.y = Math.floor(this.y), this
        }, ceil: function () {
            return this.clone()._ceil()
        }, _ceil: function () {
            return this.x = Math.ceil(this.x), this.y = Math.ceil(this.y), this
        }, trunc: function () {
            return this.clone()._trunc()
        }, _trunc: function () {
            return this.x = wi(this.x), this.y = wi(this.y), this
        }, distanceTo: function (t) {
            var i = (t = w(t)).x - this.x, e = t.y - this.y;
            return Math.sqrt(i * i + e * e)
        }, equals: function (t) {
            return (t = w(t)).x === this.x && t.y === this.y
        }, contains: function (t) {
            return t = w(t), Math.abs(t.x) <= Math.abs(this.x) && Math.abs(t.y) <= Math.abs(this.y)
        }, toString: function () {
            return "Point(" + r(this.x) + ", " + r(this.y) + ")"
        }
    }, b.prototype = {
        extend: function (t) {
            return t = w(t), this.min || this.max ? (this.min.x = Math.min(t.x, this.min.x), this.max.x = Math.max(t.x, this.max.x), this.min.y = Math.min(t.y, this.min.y), this.max.y = Math.max(t.y, this.max.y)) : (this.min = t.clone(), this.max = t.clone()), this
        }, getCenter: function (t) {
            return new x((this.min.x + this.max.x) / 2, (this.min.y + this.max.y) / 2, t)
        }, getBottomLeft: function () {
            return new x(this.min.x, this.max.y)
        }, getTopRight: function () {
            return new x(this.max.x, this.min.y)
        }, getTopLeft: function () {
            return this.min
        }, getBottomRight: function () {
            return this.max
        }, getSize: function () {
            return this.max.subtract(this.min)
        }, contains: function (t) {
            var i, e;
            return (t = "number" == typeof t[0] || t instanceof x ? w(t) : P(t)) instanceof b ? (i = t.min, e = t.max) : i = e = t, i.x >= this.min.x && e.x <= this.max.x && i.y >= this.min.y && e.y <= this.max.y
        }, intersects: function (t) {
            t = P(t);
            var i = this.min, e = this.max, n = t.min, o = t.max, s = o.x >= i.x && n.x <= e.x,
                a = o.y >= i.y && n.y <= e.y;
            return s && a
        }, overlaps: function (t) {
            t = P(t);
            var i = this.min, e = this.max, n = t.min, o = t.max, s = o.x > i.x && n.x < e.x,
                a = o.y > i.y && n.y < e.y;
            return s && a
        }, isValid: function () {
            return !(!this.min || !this.max)
        }
    }, T.prototype = {
        extend: function (t) {
            var i, e, n = this._southWest, o = this._northEast;
            if (t instanceof C) i = t, e = t; else {
                if (!(t instanceof T)) return t ? this.extend(A(t) || M(t)) : this;
                if (i = t._southWest, e = t._northEast, !i || !e) return this
            }
            return n || o ? (n.lat = Math.min(i.lat, n.lat), n.lng = Math.min(i.lng, n.lng), o.lat = Math.max(e.lat, o.lat), o.lng = Math.max(e.lng, o.lng)) : (this._southWest = new C(i.lat, i.lng), this._northEast = new C(e.lat, e.lng)), this
        }, pad: function (t) {
            var i = this._southWest, e = this._northEast, n = Math.abs(i.lat - e.lat) * t,
                o = Math.abs(i.lng - e.lng) * t;
            return new T(new C(i.lat - n, i.lng - o), new C(e.lat + n, e.lng + o))
        }, getCenter: function () {
            return new C((this._southWest.lat + this._northEast.lat) / 2, (this._southWest.lng + this._northEast.lng) / 2)
        }, getSouthWest: function () {
            return this._southWest
        }, getNorthEast: function () {
            return this._northEast
        }, getNorthWest: function () {
            return new C(this.getNorth(), this.getWest())
        }, getSouthEast: function () {
            return new C(this.getSouth(), this.getEast())
        }, getWest: function () {
            return this._southWest.lng
        }, getSouth: function () {
            return this._southWest.lat
        }, getEast: function () {
            return this._northEast.lng
        }, getNorth: function () {
            return this._northEast.lat
        }, contains: function (t) {
            t = "number" == typeof t[0] || t instanceof C || "lat" in t ? A(t) : M(t);
            var i, e, n = this._southWest, o = this._northEast;
            return t instanceof T ? (i = t.getSouthWest(), e = t.getNorthEast()) : i = e = t, i.lat >= n.lat && e.lat <= o.lat && i.lng >= n.lng && e.lng <= o.lng
        }, intersects: function (t) {
            t = M(t);
            var i = this._southWest, e = this._northEast, n = t.getSouthWest(), o = t.getNorthEast(),
                s = o.lat >= i.lat && n.lat <= e.lat, a = o.lng >= i.lng && n.lng <= e.lng;
            return s && a
        }, overlaps: function (t) {
            t = M(t);
            var i = this._southWest, e = this._northEast, n = t.getSouthWest(), o = t.getNorthEast(),
                s = o.lat > i.lat && n.lat < e.lat, a = o.lng > i.lng && n.lng < e.lng;
            return s && a
        }, toBBoxString: function () {
            return [this.getWest(), this.getSouth(), this.getEast(), this.getNorth()].join(",")
        }, equals: function (t, i) {
            return !!t && (t = M(t), this._southWest.equals(t.getSouthWest(), i) && this._northEast.equals(t.getNorthEast(), i))
        }, isValid: function () {
            return !(!this._southWest || !this._northEast)
        }
    }, C.prototype = {
        equals: function (t, i) {
            return !!t && (t = A(t), Math.max(Math.abs(this.lat - t.lat), Math.abs(this.lng - t.lng)) <= (void 0 === i ? 1e-9 : i))
        }, toString: function (t) {
            return "LatLng(" + r(this.lat, t) + ", " + r(this.lng, t) + ")"
        }, distanceTo: function (t) {
            return bi.distance(this, A(t))
        }, wrap: function () {
            return bi.wrapLatLng(this)
        }, toBounds: function (t) {
            var i = 180 * t / 40075017, e = i / Math.cos(Math.PI / 180 * this.lat);
            return M([this.lat - i, this.lng - e], [this.lat + i, this.lng + e])
        }, clone: function () {
            return new C(this.lat, this.lng, this.alt)
        }
    };
    var Li = {
        latLngToPoint: function (t, i) {
            var e = this.projection.project(t), n = this.scale(i);
            return this.transformation._transform(e, n)
        }, pointToLatLng: function (t, i) {
            var e = this.scale(i), n = this.transformation.untransform(t, e);
            return this.projection.unproject(n)
        }, project: function (t) {
            return this.projection.project(t)
        }, unproject: function (t) {
            return this.projection.unproject(t)
        }, scale: function (t) {
            return 256 * Math.pow(2, t)
        }, zoom: function (t) {
            return Math.log(t / 256) / Math.LN2
        }, getProjectedBounds: function (t) {
            if (this.infinite) return null;
            var i = this.projection.bounds, e = this.scale(t);
            return new b(this.transformation.transform(i.min, e), this.transformation.transform(i.max, e))
        }, infinite: !1, wrapLatLng: function (t) {
            var i = this.wrapLng ? s(t.lng, this.wrapLng, !0) : t.lng;
            return new C(this.wrapLat ? s(t.lat, this.wrapLat, !0) : t.lat, i, t.alt)
        }, wrapLatLngBounds: function (t) {
            var i = t.getCenter(), e = this.wrapLatLng(i), n = i.lat - e.lat, o = i.lng - e.lng;
            if (0 === n && 0 === o) return t;
            var s = t.getSouthWest(), a = t.getNorthEast();
            return new T(new C(s.lat - n, s.lng - o), new C(a.lat - n, a.lng - o))
        }
    }, bi = i({}, Li, {
        wrapLng: [-180, 180], R: 6371e3, distance: function (t, i) {
            var e = Math.PI / 180, n = t.lat * e, o = i.lat * e, s = Math.sin((i.lat - t.lat) * e / 2),
                a = Math.sin((i.lng - t.lng) * e / 2), r = s * s + Math.cos(n) * Math.cos(o) * a * a,
                h = 2 * Math.atan2(Math.sqrt(r), Math.sqrt(1 - r));
            return this.R * h
        }
    }), Pi = {
        R: 6378137, MAX_LATITUDE: 85.0511287798, project: function (t) {
            var i = Math.PI / 180, e = this.MAX_LATITUDE, n = Math.max(Math.min(e, t.lat), -e), o = Math.sin(n * i);
            return new x(this.R * t.lng * i, this.R * Math.log((1 + o) / (1 - o)) / 2)
        }, unproject: function (t) {
            var i = 180 / Math.PI;
            return new C((2 * Math.atan(Math.exp(t.y / this.R)) - Math.PI / 2) * i, t.x * i / this.R)
        }, bounds: function () {
            var t = 6378137 * Math.PI;
            return new b([-t, -t], [t, t])
        }()
    };
    z.prototype = {
        transform: function (t, i) {
            return this._transform(t.clone(), i)
        }, _transform: function (t, i) {
            return i = i || 1, t.x = i * (this._a * t.x + this._b), t.y = i * (this._c * t.y + this._d), t
        }, untransform: function (t, i) {
            return i = i || 1, new x((t.x / i - this._b) / this._a, (t.y / i - this._d) / this._c)
        }
    };
    var Ti, Mi, Ci, Ai, zi = i({}, bi, {
            code: "EPSG:3857", projection: Pi, transformation: function () {
                var t = .5 / (Math.PI * Pi.R);
                return S(t, .5, -t, .5)
            }()
        }), Si = i({}, zi, {code: "EPSG:900913"}), ki = document.documentElement.style, Ei = "ActiveXObject" in window,
        Ii = Ei && !document.addEventListener, Bi = "msLaunchUri" in navigator && !("documentMode" in document),
        Zi = I("webkit"), Oi = I("android"), Ri = I("android 2") || I("android 3"),
        ji = parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1], 10),
        Di = Oi && I("Google") && ji < 537 && !("AudioNode" in window), Ni = !!window.opera, Fi = I("chrome"),
        Hi = I("gecko") && !Zi && !Ni && !Ei, Wi = !Fi && I("safari"), Gi = I("phantom"), Ui = "OTransition" in ki,
        Vi = 0 === navigator.platform.indexOf("Win"), qi = Ei && "transition" in ki,
        Xi = "WebKitCSSMatrix" in window && "m11" in new window.WebKitCSSMatrix && !Ri, Yi = "MozPerspective" in ki,
        Ji = !window.L_DISABLE_3D && (qi || Xi || Yi) && !Ui && !Gi,
        Ki = "undefined" != typeof orientation || I("mobile"), Qi = Ki && Zi, $i = Ki && Xi,
        te = !window.PointerEvent && window.MSPointerEvent, ie = !(!window.PointerEvent && !te),
        ee = !window.L_NO_TOUCH && (ie || "ontouchstart" in window || window.DocumentTouch && document instanceof window.DocumentTouch),
        ne = Ki && Ni, oe = Ki && Hi,
        se = (window.devicePixelRatio || window.screen.deviceXDPI / window.screen.logicalXDPI) > 1,
        ae = !!document.createElement("canvas").getContext,
        re = !(!document.createElementNS || !k("svg").createSVGRect), he = !re && function () {
            try {
                var t = document.createElement("div");
                t.innerHTML = '<v:shape adj="1"/>';
                var i = t.firstChild;
                return i.style.behavior = "url(#default#VML)", i && "object" == typeof i.adj
            } catch (t) {
                return !1
            }
        }(), le = (Object.freeze || Object)({
            ie: Ei,
            ielt9: Ii,
            edge: Bi,
            webkit: Zi,
            android: Oi,
            android23: Ri,
            androidStock: Di,
            opera: Ni,
            chrome: Fi,
            gecko: Hi,
            safari: Wi,
            phantom: Gi,
            opera12: Ui,
            win: Vi,
            ie3d: qi,
            webkit3d: Xi,
            gecko3d: Yi,
            any3d: Ji,
            mobile: Ki,
            mobileWebkit: Qi,
            mobileWebkit3d: $i,
            msPointer: te,
            pointer: ie,
            touch: ee,
            mobileOpera: ne,
            mobileGecko: oe,
            retina: se,
            canvas: ae,
            svg: re,
            vml: he
        }), ue = te ? "MSPointerDown" : "pointerdown", ce = te ? "MSPointerMove" : "pointermove",
        de = te ? "MSPointerUp" : "pointerup", _e = te ? "MSPointerCancel" : "pointercancel",
        pe = ["INPUT", "SELECT", "OPTION"], me = {}, fe = !1, ge = 0,
        ve = te ? "MSPointerDown" : ie ? "pointerdown" : "touchstart",
        ye = te ? "MSPointerUp" : ie ? "pointerup" : "touchend", xe = "_gmap_", we = "_gmap_events",
        Le = Vi && Fi ? 2 * window.devicePixelRatio : Hi ? window.devicePixelRatio : 1, be = {},
        Pe = (Object.freeze || Object)({
            on: U,
            off: V,
            stopPropagation: Y,
            disableScrollPropagation: J,
            disableClickPropagation: K,
            preventDefault: Q,
            stop: $,
            getMousePosition: tt,
            getWheelDelta: it,
            fakeStop: et,
            skipped: nt,
            isExternalTarget: ot,
            addListener: U,
            removeListener: V
        }), Te = xt(["transform", "WebkitTransform", "OTransform", "MozTransform", "msTransform"]),
        Me = xt(["webkitTransition", "transition", "OTransition", "MozTransition", "msTransition"]),
        Ce = "webkitTransition" === Me || "OTransition" === Me ? Me + "End" : "transitionend";
    if ("onselectstart" in document) Mi = function () {
        U(window, "selectstart", Q)
    }, Ci = function () {
        V(window, "selectstart", Q)
    }; else {
        var Ae = xt(["userSelect", "WebkitUserSelect", "OUserSelect", "MozUserSelect", "msUserSelect"]);
        Mi = function () {
            if (Ae) {
                var t = document.documentElement.style;
                Ai = t[Ae], t[Ae] = "none"
            }
        }, Ci = function () {
            Ae && (document.documentElement.style[Ae] = Ai, Ai = void 0)
        }
    }
    var ze, Se, ke = (Object.freeze || Object)({
        TRANSFORM: Te,
        TRANSITION: Me,
        TRANSITION_END: Ce,
        get: at,
        getStyle: rt,
        create: ht,
        remove: lt,
        empty: ut,
        toFront: ct,
        toBack: dt,
        hasClass: _t,
        addClass: pt,
        removeClass: mt,
        setClass: ft,
        getClass: gt,
        setOpacity: vt,
        testProp: xt,
        setTransform: wt,
        setPosition: Lt,
        getPosition: bt,
        disableTextSelection: Mi,
        enableTextSelection: Ci,
        disableImageDrag: Pt,
        enableImageDrag: Tt,
        preventOutline: Mt,
        restoreOutline: Ct
    }), Ee = xi.extend({
        run: function (t, i, e, n) {
            this.stop(), this._el = t, this._inProgress = !0, this._duration = e || .25, this._easeOutPower = 1 / Math.max(n || .5, .2), this._startPos = bt(t), this._offset = i.subtract(this._startPos), this._startTime = +new Date, this.fire("start"), this._animate()
        }, stop: function () {
            this._inProgress && (this._step(!0), this._complete())
        }, _animate: function () {
            this._animId = f(this._animate, this), this._step()
        }, _step: function (t) {
            var i = +new Date - this._startTime, e = 1e3 * this._duration;
            i < e ? this._runFrame(this._easeOut(i / e), t) : (this._runFrame(1), this._complete())
        }, _runFrame: function (t, i) {
            var e = this._startPos.add(this._offset.multiplyBy(t));
            i && e._round(), Lt(this._el, e), this.fire("step")
        }, _complete: function () {
            g(this._animId), this._inProgress = !1, this.fire("end")
        }, _easeOut: function (t) {
            return 1 - Math.pow(1 - t, this._easeOutPower)
        }
    }), Ie = xi.extend({
        options: {
            crs: zi,
            center: void 0,
            zoom: void 0,
            minZoom: void 0,
            maxZoom: void 0,
            layers: [],
            maxBounds: void 0,
            renderer: void 0,
            zoomAnimation: !0,
            zoomAnimationThreshold: 4,
            fadeAnimation: !0,
            markerZoomAnimation: !0,
            transform3DLimit: 8388608,
            zoomSnap: 1,
            zoomDelta: 1,
            trackResize: !0
        },
        initialize: function (t, i) {
            i = u(this, i), this._initContainer(t), this._initLayout(), this._onResize = e(this._onResize, this), this._initEvents(), i.maxBounds && this.setMaxBounds(i.maxBounds), void 0 !== i.zoom && (this._zoom = this._limitZoom(i.zoom)), i.center && void 0 !== i.zoom && this.setView(A(i.center), i.zoom, {reset: !0}), this._handlers = [], this._layers = {}, this._zoomBoundLayers = {}, this._sizeChanged = !0, this.callInitHooks(), this._zoomAnimated = Me && Ji && !ne && this.options.zoomAnimation, this._zoomAnimated && (this._createAnimProxy(), U(this._proxy, Ce, this._catchTransitionEnd, this)), this._addLayers(this.options.layers)
        },
        setView: function (t, e, n) {
            return e = void 0 === e ? this._zoom : this._limitZoom(e), t = this._limitCenter(A(t), e, this.options.maxBounds), n = n || {}, this._stop(), this._loaded && !n.reset && !0 !== n && (void 0 !== n.animate && (n.zoom = i({animate: n.animate}, n.zoom), n.pan = i({
                animate: n.animate,
                duration: n.duration
            }, n.pan)), this._zoom !== e ? this._tryAnimatedZoom && this._tryAnimatedZoom(t, e, n.zoom) : this._tryAnimatedPan(t, n.pan)) ? (clearTimeout(this._sizeTimer), this) : (this._resetView(t, e), this)
        },
        setZoom: function (t, i) {
            return this._loaded ? this.setView(this.getCenter(), t, {zoom: i}) : (this._zoom = t, this)
        },
        zoomIn: function (t, i) {
            return t = t || (Ji ? this.options.zoomDelta : 1), this.setZoom(this._zoom + t, i)
        },
        zoomOut: function (t, i) {
            return t = t || (Ji ? this.options.zoomDelta : 1), this.setZoom(this._zoom - t, i)
        },
        setZoomAround: function (t, i, e) {
            var n = this.getZoomScale(i), o = this.getSize().divideBy(2),
                s = (t instanceof x ? t : this.latLngToContainerPoint(t)).subtract(o).multiplyBy(1 - 1 / n),
                a = this.containerPointToLatLng(o.add(s));
            return this.setView(a, i, {zoom: e})
        },
        _getBoundsCenterZoom: function (t, i) {
            i = i || {}, t = t.getBounds ? t.getBounds() : M(t);
            var e = w(i.paddingTopLeft || i.padding || [0, 0]), n = w(i.paddingBottomRight || i.padding || [0, 0]),
                o = this.getBoundsZoom(t, !1, e.add(n));
            if ((o = "number" == typeof i.maxZoom ? Math.min(i.maxZoom, o) : o) === 1 / 0) return {
                center: t.getCenter(),
                zoom: o
            };
            var s = n.subtract(e).divideBy(2), a = this.project(t.getSouthWest(), o),
                r = this.project(t.getNorthEast(), o);
            return {center: this.unproject(a.add(r).divideBy(2).add(s), o), zoom: o}
        },
        fitBounds: function (t, i) {
            if (!(t = M(t)).isValid()) throw new Error("Bounds are not valid.");
            var e = this._getBoundsCenterZoom(t, i);
            return this.setView(e.center, e.zoom, i)
        },
        fitWorld: function (t) {
            return this.fitBounds([[-90, -180], [90, 180]], t)
        },
        panTo: function (t, i) {
            return this.setView(t, this._zoom, {pan: i})
        },
        panBy: function (t, i) {
            if (t = w(t).round(), i = i || {}, !t.x && !t.y) return this.fire("moveend");
            if (!0 !== i.animate && !this.getSize().contains(t)) return this._resetView(this.unproject(this.project(this.getCenter()).add(t)), this.getZoom()), this;
            if (this._panAnim || (this._panAnim = new Ee, this._panAnim.on({
                step: this._onPanTransitionStep,
                end: this._onPanTransitionEnd
            }, this)), i.noMoveStart || this.fire("movestart"), !1 !== i.animate) {
                pt(this._mapPane, "gamap-pan-anim");
                var e = this._getMapPanePos().subtract(t).round();
                this._panAnim.run(this._mapPane, e, i.duration || .25, i.easeLinearity)
            } else this._rawPanBy(t), this.fire("move").fire("moveend");
            return this
        },
        flyTo: function (t, i, e) {
            function n(t) {
                var i = (g * g - m * m + (t ? -1 : 1) * x * x * v * v) / (2 * (t ? g : m) * x * v),
                    e = Math.sqrt(i * i + 1) - i;
                return e < 1e-9 ? -18 : Math.log(e)
            }

            function o(t) {
                return (Math.exp(t) - Math.exp(-t)) / 2
            }

            function s(t) {
                return (Math.exp(t) + Math.exp(-t)) / 2
            }

            function a(t) {
                return o(t) / s(t)
            }

            function r(t) {
                return m * (s(w) / s(w + y * t))
            }

            function h(t) {
                return m * (s(w) * a(w + y * t) - o(w)) / x
            }

            function l(t) {
                return 1 - Math.pow(1 - t, 1.5)
            }

            function u() {
                var e = (Date.now() - L) / P, n = l(e) * b;
                e <= 1 ? (this._flyToFrame = f(u, this), this._move(this.unproject(c.add(d.subtract(c).multiplyBy(h(n) / v)), p), this.getScaleZoom(m / r(n), p), {flyTo: !0})) : this._move(t, i)._moveEnd(!0)
            }

            if (!1 === (e = e || {}).animate || !Ji) return this.setView(t, i, e);
            this._stop();
            var c = this.project(this.getCenter()), d = this.project(t), _ = this.getSize(), p = this._zoom;
            t = A(t), i = void 0 === i ? p : i;
            var m = Math.max(_.x, _.y), g = m * this.getZoomScale(p, i), v = d.distanceTo(c) || 1, y = 1.42, x = y * y,
                w = n(0), L = Date.now(), b = (n(1) - w) / y, P = e.duration ? 1e3 * e.duration : 1e3 * b * .8;
            return this._moveStart(!0, e.noMoveStart), u.call(this), this
        },
        flyToBounds: function (t, i) {
            var e = this._getBoundsCenterZoom(t, i);
            return this.flyTo(e.center, e.zoom, i)
        },
        setMaxBounds: function (t) {
            return (t = M(t)).isValid() ? (this.options.maxBounds && this.off("moveend", this._panInsideMaxBounds), this.options.maxBounds = t, this._loaded && this._panInsideMaxBounds(), this.on("moveend", this._panInsideMaxBounds)) : (this.options.maxBounds = null, this.off("moveend", this._panInsideMaxBounds))
        },
        setMinZoom: function (t) {
            var i = this.options.minZoom;
            return this.options.minZoom = t, this._loaded && i !== t && (this.fire("zoomlevelschange"), this.getZoom() < this.options.minZoom) ? this.setZoom(t) : this
        },
        setMaxZoom: function (t) {
            var i = this.options.maxZoom;
            return this.options.maxZoom = t, this._loaded && i !== t && (this.fire("zoomlevelschange"), this.getZoom() > this.options.maxZoom) ? this.setZoom(t) : this
        },
        panInsideBounds: function (t, i) {
            this._enforcingBounds = !0;
            var e = this.getCenter(), n = this._limitCenter(e, this._zoom, M(t));
            return e.equals(n) || this.panTo(n, i), this._enforcingBounds = !1, this
        },
        invalidateSize: function (t) {
            if (!this._loaded) return this;
            t = i({animate: !1, pan: !0}, !0 === t ? {animate: !0} : t);
            var n = this.getSize();
            this._sizeChanged = !0, this._lastCenter = null;
            var o = this.getSize(), s = n.divideBy(2).round(), a = o.divideBy(2).round(), r = s.subtract(a);
            return r.x || r.y ? (t.animate && t.pan ? this.panBy(r) : (t.pan && this._rawPanBy(r), this.fire("move"), t.debounceMoveend ? (clearTimeout(this._sizeTimer), this._sizeTimer = setTimeout(e(this.fire, this, "moveend"), 200)) : this.fire("moveend")), this.fire("resize", {
                oldSize: n,
                newSize: o
            })) : this
        },
        stop: function () {
            return this.setZoom(this._limitZoom(this._zoom)), this.options.zoomSnap || this.fire("viewreset"), this._stop()
        },
        locate: function (t) {
            if (t = this._locateOptions = i({
                timeout: 1e4,
                watch: !1
            }, t), !("geolocation" in navigator)) return this._handleGeolocationError({
                code: 0,
                message: "Geolocation not supported."
            }), this;
            var n = e(this._handleGeolocationResponse, this), o = e(this._handleGeolocationError, this);
            return t.watch ? this._locationWatchId = navigator.geolocation.watchPosition(n, o, t) : navigator.geolocation.getCurrentPosition(n, o, t), this
        },
        stopLocate: function () {
            return navigator.geolocation && navigator.geolocation.clearWatch && navigator.geolocation.clearWatch(this._locationWatchId), this._locateOptions && (this._locateOptions.setView = !1), this
        },
        _handleGeolocationError: function (t) {
            var i = t.code,
                e = t.message || (1 === i ? "permission denied" : 2 === i ? "position unavailable" : "timeout");
            this._locateOptions.setView && !this._loaded && this.fitWorld(), this.fire("locationerror", {
                code: i,
                message: "Geolocation error: " + e + "."
            })
        },
        _handleGeolocationResponse: function (t) {
            var i = new C(t.coords.latitude, t.coords.longitude), e = i.toBounds(t.coords.accuracy),
                n = this._locateOptions;
            if (n.setView) {
                var o = this.getBoundsZoom(e);
                this.setView(i, n.maxZoom ? Math.min(o, n.maxZoom) : o)
            }
            var s = {latlng: i, bounds: e, timestamp: t.timestamp};
            for (var a in t.coords) "number" == typeof t.coords[a] && (s[a] = t.coords[a]);
            this.fire("locationfound", s)
        },
        addHandler: function (t, i) {
            if (!i) return this;
            var e = this[t] = new i(this);
            return this._handlers.push(e), this.options[t] && e.enable(), this
        },
        remove: function () {
            if (this._initEvents(!0), this._containerId !== this._container._gmap_id) throw new Error("Map container is being reused by another instance");
            try {
                delete this._container._gmap_id, delete this._containerId
            } catch (t) {
                this._container._gmap_id = void 0, this._containerId = void 0
            }
            void 0 !== this._locationWatchId && this.stopLocate(), this._stop(), lt(this._mapPane), this._clearControlPos && this._clearControlPos(), this._clearHandlers(), this._loaded && this.fire("unload");
            var t;
            for (t in this._layers) this._layers[t].remove();
            for (t in this._panes) lt(this._panes[t]);
            return this._layers = [], this._panes = [], delete this._mapPane, delete this._renderer, this
        },
        createPane: function (t, i) {
            var e = ht("div", "gamap-pane" + (t ? " gamap-" + t.replace("Pane", "") + "-pane" : ""), i || this._mapPane);
            return t && (this._panes[t] = e), e
        },
        getCenter: function () {
            return this._checkIfLoaded(), this._lastCenter && !this._moved() ? this._lastCenter : this.layerPointToLatLng(this._getCenterLayerPoint())
        },
        getZoom: function () {
            return this._zoom
        },
        getBounds: function () {
            var t = this.getPixelBounds();
            return new T(this.unproject(t.getBottomLeft()), this.unproject(t.getTopRight()))
        },
        getMinZoom: function () {
            return void 0 === this.options.minZoom ? this._layersMinZoom || 0 : this.options.minZoom
        },
        getMaxZoom: function () {
            return void 0 === this.options.maxZoom ? void 0 === this._layersMaxZoom ? 1 / 0 : this._layersMaxZoom : this.options.maxZoom
        },
        getBoundsZoom: function (t, i, e) {
            t = M(t), e = w(e || [0, 0]);
            var n = this.getZoom() || 0, o = this.getMinZoom(), s = this.getMaxZoom(), a = t.getNorthWest(),
                r = t.getSouthEast(), h = this.getSize().subtract(e),
                l = P(this.project(r, n), this.project(a, n)).getSize(), u = Ji ? this.options.zoomSnap : 1,
                c = h.x / l.x, d = h.y / l.y, _ = i ? Math.max(c, d) : Math.min(c, d);
            return n = this.getScaleZoom(_, n), u && (n = Math.round(n / (u / 100)) * (u / 100), n = i ? Math.ceil(n / u) * u : Math.floor(n / u) * u), Math.max(o, Math.min(s, n))
        },
        getSize: function () {
            return this._size && !this._sizeChanged || (this._size = new x(this._container.clientWidth || 0, this._container.clientHeight || 0), this._sizeChanged = !1), this._size.clone()
        },
        getPixelBounds: function (t, i) {
            var e = this._getTopLeftPoint(t, i);
            return new b(e, e.add(this.getSize()))
        },
        getPixelOrigin: function () {
            return this._checkIfLoaded(), this._pixelOrigin
        },
        getPixelWorldBounds: function (t) {
            return this.options.crs.getProjectedBounds(void 0 === t ? this.getZoom() : t)
        },
        getPane: function (t) {
            return "string" == typeof t ? this._panes[t] : t
        },
        getPanes: function () {
            return this._panes
        },
        getContainer: function () {
            return this._container
        },
        getZoomScale: function (t, i) {
            var e = this.options.crs;
            return i = void 0 === i ? this._zoom : i, e.scale(t) / e.scale(i)
        },
        getScaleZoom: function (t, i) {
            var e = this.options.crs;
            i = void 0 === i ? this._zoom : i;
            var n = e.zoom(t * e.scale(i));
            return isNaN(n) ? 1 / 0 : n
        },
        project: function (t, i) {
            return i = void 0 === i ? this._zoom : i, this.options.crs.latLngToPoint(A(t), i)
        },
        unproject: function (t, i) {
            return i = void 0 === i ? this._zoom : i, this.options.crs.pointToLatLng(w(t), i)
        },
        layerPointToLatLng: function (t) {
            var i = w(t).add(this.getPixelOrigin());
            return this.unproject(i)
        },
        latLngToLayerPoint: function (t) {
            return this.project(A(t))._round()._subtract(this.getPixelOrigin())
        },
        wrapLatLng: function (t) {
            return this.options.crs.wrapLatLng(A(t))
        },
        wrapLatLngBounds: function (t) {
            return this.options.crs.wrapLatLngBounds(M(t))
        },
        distance: function (t, i) {
            return this.options.crs.distance(A(t), A(i))
        },
        containerPointToLayerPoint: function (t) {
            return w(t).subtract(this._getMapPanePos())
        },
        layerPointToContainerPoint: function (t) {
            return w(t).add(this._getMapPanePos())
        },
        containerPointToLatLng: function (t) {
            var i = this.containerPointToLayerPoint(w(t));
            return this.layerPointToLatLng(i)
        },
        latLngToContainerPoint: function (t) {
            return this.layerPointToContainerPoint(this.latLngToLayerPoint(A(t)))
        },
        mouseEventToContainerPoint: function (t) {
            return tt(t, this._container)
        },
        mouseEventToLayerPoint: function (t) {
            return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))
        },
        mouseEventToLatLng: function (t) {
            return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))
        },
        _initContainer: function (t) {
            var i = this._container = at(t);
            if (!i) throw new Error("Map container not found.");
            if (i._gmap_id) throw new Error("Map container is already initialized.");
            U(i, "scroll", this._onScroll, this), this._containerId = n(i)
        },
        _initLayout: function () {
            var t = this._container;
            this._fadeAnimated = this.options.fadeAnimation && Ji, pt(t, "gamap-container" + (ee ? " gamap-touch" : "") + (se ? " gamap-retina" : "") + (Ii ? " gamap-oldie" : "") + (Wi ? " gamap-safari" : "") + (this._fadeAnimated ? " gamap-fade-anim" : ""));
            var i = rt(t, "position");
            "absolute" !== i && "relative" !== i && "fixed" !== i && (t.style.position = "relative"), this._initPanes(), this._initControlPos && this._initControlPos()
        },
        _initPanes: function () {
            var t = this._panes = {};
            this._paneRenderers = {}, this._mapPane = this.createPane("mapPane", this._container), Lt(this._mapPane, new x(0, 0)), this.createPane("tilePane"), this.createPane("shadowPane"), this.createPane("overlayPane"), this.createPane("markerPane"), this.createPane("tooltipPane"), this.createPane("popupPane"), this.options.markerZoomAnimation || (pt(t.markerPane, "gamap-zoom-hide"), pt(t.shadowPane, "gamap-zoom-hide"))
        },
        _resetView: function (t, i) {
            Lt(this._mapPane, new x(0, 0));
            var e = !this._loaded;
            this._loaded = !0, i = this._limitZoom(i), this.fire("viewprereset");
            var n = this._zoom !== i;
            this._moveStart(n, !1)._move(t, i)._moveEnd(n), this.fire("viewreset"), e && this.fire("load")
        },
        _moveStart: function (t, i) {
            return t && this.fire("zoomstart"), i || this.fire("movestart"), this
        },
        _move: function (t, i, e) {
            void 0 === i && (i = this._zoom);
            var n = this._zoom !== i;
            return this._zoom = i, this._lastCenter = t, this._pixelOrigin = this._getNewPixelOrigin(t), (n || e && e.pinch) && this.fire("zoom", e), this.fire("move", e)
        },
        _moveEnd: function (t) {
            return t && this.fire("zoomend"), this.fire("moveend")
        },
        _stop: function () {
            return g(this._flyToFrame), this._panAnim && this._panAnim.stop(), this
        },
        _rawPanBy: function (t) {
            Lt(this._mapPane, this._getMapPanePos().subtract(t))
        },
        _getZoomSpan: function () {
            return this.getMaxZoom() - this.getMinZoom()
        },
        _panInsideMaxBounds: function () {
            this._enforcingBounds || this.panInsideBounds(this.options.maxBounds)
        },
        _checkIfLoaded: function () {
            if (!this._loaded) throw new Error("Set map center and zoom first.")
        },
        _initEvents: function (t) {
            this._targets = {}, this._targets[n(this._container)] = this;
            var i = t ? V : U;
            i(this._container, "click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress", this._handleDOMEvent, this), this.options.trackResize && i(window, "resize", this._onResize, this), Ji && this.options.transform3DLimit && (t ? this.off : this.on).call(this, "moveend", this._onMoveEnd)
        },
        _onResize: function () {
            g(this._resizeRequest), this._resizeRequest = f(function () {
                this.invalidateSize({debounceMoveend: !0})
            }, this)
        },
        _onScroll: function () {
            this._container.scrollTop = 0, this._container.scrollLeft = 0
        },
        _onMoveEnd: function () {
            var t = this._getMapPanePos();
            Math.max(Math.abs(t.x), Math.abs(t.y)) >= this.options.transform3DLimit && this._resetView(this.getCenter(), this.getZoom())
        },
        _findEventTargets: function (t, i) {
            for (var e, o = [], s = "mouseout" === i || "mouseover" === i, a = t.target || t.srcElement, r = !1; a;) {
                if ((e = this._targets[n(a)]) && ("click" === i || "preclick" === i) && !t._simulated && this._draggableMoved(e)) {
                    r = !0;
                    break
                }
                if (e && e.listens(i, !0)) {
                    if (s && !ot(a, t)) break;
                    if (o.push(e), s) break
                }
                if (a === this._container) break;
                a = a.parentNode
            }
            return o.length || r || s || !ot(a, t) || (o = [this]), o
        },
        _handleDOMEvent: function (t) {
            if (this._loaded && !nt(t)) {
                var i = t.type;
                "mousedown" !== i && "keypress" !== i || Mt(t.target || t.srcElement), this._fireDOMEvent(t, i)
            }
        },
        _mouseEvents: ["click", "dblclick", "mouseover", "mouseout", "contextmenu"],
        _fireDOMEvent: function (t, e, n) {
            if ("click" === t.type) {
                var o = i({}, t);
                o.type = "preclick", this._fireDOMEvent(o, o.type, n)
            }
            if (!t._stopped && (n = (n || []).concat(this._findEventTargets(t, e))).length) {
                var s = n[0];
                "contextmenu" === e && s.listens(e, !0) && Q(t);
                var a = {originalEvent: t};
                if ("keypress" !== t.type) {
                    var r = s.getLatLng && (!s._radius || s._radius <= 10);
                    a.containerPoint = r ? this.latLngToContainerPoint(s.getLatLng()) : this.mouseEventToContainerPoint(t), a.layerPoint = this.containerPointToLayerPoint(a.containerPoint), a.latlng = r ? s.getLatLng() : this.layerPointToLatLng(a.layerPoint)
                }
                for (var h = 0; h < n.length; h++) if (n[h].fire(e, a, !0), a.originalEvent._stopped || !1 === n[h].options.bubblingMouseEvents && -1 !== _(this._mouseEvents, e)) return
            }
        },
        _draggableMoved: function (t) {
            return (t = t.dragging && t.dragging.enabled() ? t : this).dragging && t.dragging.moved() || this.boxZoom && this.boxZoom.moved()
        },
        _clearHandlers: function () {
            for (var t = 0, i = this._handlers.length; t < i; t++) this._handlers[t].disable()
        },
        whenReady: function (t, i) {
            return this._loaded ? t.call(i || this, {target: this}) : this.on("load", t, i), this
        },
        _getMapPanePos: function () {
            return bt(this._mapPane) || new x(0, 0)
        },
        _moved: function () {
            var t = this._getMapPanePos();
            return t && !t.equals([0, 0])
        },
        _getTopLeftPoint: function (t, i) {
            return (t && void 0 !== i ? this._getNewPixelOrigin(t, i) : this.getPixelOrigin()).subtract(this._getMapPanePos())
        },
        _getNewPixelOrigin: function (t, i) {
            var e = this.getSize()._divideBy(2);
            return this.project(t, i)._subtract(e)._add(this._getMapPanePos())._round()
        },
        _latLngToNewLayerPoint: function (t, i, e) {
            var n = this._getNewPixelOrigin(e, i);
            return this.project(t, i)._subtract(n)
        },
        _latLngBoundsToNewLayerBounds: function (t, i, e) {
            var n = this._getNewPixelOrigin(e, i);
            return P([this.project(t.getSouthWest(), i)._subtract(n), this.project(t.getNorthWest(), i)._subtract(n), this.project(t.getSouthEast(), i)._subtract(n), this.project(t.getNorthEast(), i)._subtract(n)])
        },
        _getCenterLayerPoint: function () {
            return this.containerPointToLayerPoint(this.getSize()._divideBy(2))
        },
        _getCenterOffset: function (t) {
            return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())
        },
        _limitCenter: function (t, i, e) {
            if (!e) return t;
            var n = this.project(t, i), o = this.getSize().divideBy(2), s = new b(n.subtract(o), n.add(o)),
                a = this._getBoundsOffset(s, e, i);
            return a.round().equals([0, 0]) ? t : this.unproject(n.add(a), i)
        },
        _limitOffset: function (t, i) {
            if (!i) return t;
            var e = this.getPixelBounds(), n = new b(e.min.add(t), e.max.add(t));
            return t.add(this._getBoundsOffset(n, i))
        },
        _getBoundsOffset: function (t, i, e) {
            var n = P(this.project(i.getNorthEast(), e), this.project(i.getSouthWest(), e)), o = n.min.subtract(t.min),
                s = n.max.subtract(t.max);
            return new x(this._rebound(o.x, -s.x), this._rebound(o.y, -s.y))
        },
        _rebound: function (t, i) {
            return t + i > 0 ? Math.round(t - i) / 2 : Math.max(0, Math.ceil(t)) - Math.max(0, Math.floor(i))
        },
        _limitZoom: function (t) {
            var i = this.getMinZoom(), e = this.getMaxZoom(), n = Ji ? this.options.zoomSnap : 1;
            return n && (t = Math.round(t / n) * n), Math.max(i, Math.min(e, t))
        },
        _onPanTransitionStep: function () {
            this.fire("move")
        },
        _onPanTransitionEnd: function () {
            mt(this._mapPane, "gamap-pan-anim"), this.fire("moveend")
        },
        _tryAnimatedPan: function (t, i) {
            var e = this._getCenterOffset(t)._trunc();
            return !(!0 !== (i && i.animate) && !this.getSize().contains(e)) && (this.panBy(e, i), !0)
        },
        _createAnimProxy: function () {
            var t = this._proxy = ht("div", "gamap-proxy gamap-zoom-animated");
            this._panes.mapPane.appendChild(t), this.on("zoomanim", function (t) {
                var i = Te, e = this._proxy.style[i];
                wt(this._proxy, this.project(t.center, t.zoom), this.getZoomScale(t.zoom, 1)), e === this._proxy.style[i] && this._animatingZoom && this._onZoomTransitionEnd()
            }, this), this.on("load moveend", function () {
                var t = this.getCenter(), i = this.getZoom();
                wt(this._proxy, this.project(t, i), this.getZoomScale(i, 1))
            }, this), this._on("unload", this._destroyAnimProxy, this)
        },
        _destroyAnimProxy: function () {
            lt(this._proxy), delete this._proxy
        },
        _catchTransitionEnd: function (t) {
            this._animatingZoom && t.propertyName.indexOf("transform") >= 0 && this._onZoomTransitionEnd()
        },
        _nothingToAnimate: function () {
            return !this._container.getElementsByClassName("gamap-zoom-animated").length
        },
        _tryAnimatedZoom: function (t, i, e) {
            if (this._animatingZoom) return !0;
            if (e = e || {}, !this._zoomAnimated || !1 === e.animate || this._nothingToAnimate() || Math.abs(i - this._zoom) > this.options.zoomAnimationThreshold) return !1;
            var n = this.getZoomScale(i), o = this._getCenterOffset(t)._divideBy(1 - 1 / n);
            return !(!0 !== e.animate && !this.getSize().contains(o)) && (f(function () {
                this._moveStart(!0, !1)._animateZoom(t, i, !0)
            }, this), !0)
        },
        _animateZoom: function (t, i, n, o) {
            this._mapPane && (n && (this._animatingZoom = !0, this._animateToCenter = t, this._animateToZoom = i, pt(this._mapPane, "gamap-zoom-anim")), this.fire("zoomanim", {
                center: t,
                zoom: i,
                noUpdate: o
            }), setTimeout(e(this._onZoomTransitionEnd, this), 250))
        },
        _onZoomTransitionEnd: function () {
            this._animatingZoom && (this._mapPane && mt(this._mapPane, "gamap-zoom-anim"), this._animatingZoom = !1, this._move(this._animateToCenter, this._animateToZoom), f(function () {
                this._moveEnd(!0)
            }, this))
        }
    }), Be = v.extend({
        options: {position: "topright"}, initialize: function (t) {
            u(this, t)
        }, getPosition: function () {
            return this.options.position
        }, setPosition: function (t) {
            var i = this._map;
            return i && i.removeControl(this), this.options.position = t, i && i.addControl(this), this
        }, getContainer: function () {
            return this._container
        }, addTo: function (t) {
            this.remove(), this._map = t;
            var i = this._container = this.onAdd(t), e = this.getPosition(), n = t._controlCorners[e];
            return pt(i, "gamap-control"), -1 !== e.indexOf("bottom") ? n.insertBefore(i, n.firstChild) : n.appendChild(i), this
        }, remove: function () {
            return this._map ? (lt(this._container), this.onRemove && this.onRemove(this._map), this._map = null, this) : this
        }, _refocusOnMap: function (t) {
            this._map && t && t.screenX > 0 && t.screenY > 0 && this._map.getContainer().focus()
        }
    }), Ze = function (t) {
        return new Be(t)
    };
    Ie.include({
        addControl: function (t) {
            return t.addTo(this), this
        }, removeControl: function (t) {
            return t.remove(), this
        }, _initControlPos: function () {
            function t(t, o) {
                var s = e + t + " " + e + o;
                i[t + o] = ht("div", s, n)
            }

            var i = this._controlCorners = {}, e = "gamap-",
                n = this._controlContainer = ht("div", e + "control-container", this._container);
            t("top", "left"), t("top", "right"), t("bottom", "left"), t("bottom", "right")
        }, _clearControlPos: function () {
            for (var t in this._controlCorners) lt(this._controlCorners[t]);
            lt(this._controlContainer), delete this._controlCorners, delete this._controlContainer
        }
    });
    var Oe = Be.extend({
        options: {
            collapsed: !0,
            position: "topright",
            autoZIndex: !0,
            hideSingleBase: !1,
            sortLayers: !1,
            sortFunction: function (t, i, e, n) {
                return e < n ? -1 : n < e ? 1 : 0
            }
        }, initialize: function (t, i, e) {
            u(this, e), this._layerControlInputs = [], this._layers = [], this._lastZIndex = 0, this._handlingClick = !1;
            for (var n in t) this._addLayer(t[n], n);
            for (n in i) this._addLayer(i[n], n, !0)
        }, onAdd: function (t) {
            this._initLayout(), this._update(), this._map = t, t.on("zoomend", this._checkDisabledLayers, this);
            for (var i = 0; i < this._layers.length; i++) this._layers[i].layer.on("add remove", this._onLayerChange, this);
            return this._container
        }, addTo: function (t) {
            return Be.prototype.addTo.call(this, t), this._expandIfNotCollapsed()
        }, onRemove: function () {
            this._map.off("zoomend", this._checkDisabledLayers, this);
            for (var t = 0; t < this._layers.length; t++) this._layers[t].layer.off("add remove", this._onLayerChange, this)
        }, addBaseLayer: function (t, i) {
            return this._addLayer(t, i), this._map ? this._update() : this
        }, addOverlay: function (t, i) {
            return this._addLayer(t, i, !0), this._map ? this._update() : this
        }, removeLayer: function (t) {
            t.off("add remove", this._onLayerChange, this);
            var i = this._getLayer(n(t));
            return i && this._layers.splice(this._layers.indexOf(i), 1), this._map ? this._update() : this
        }, expand: function () {
            pt(this._container, "gamap-control-layers-expanded"), this._form.style.height = null;
            var t = this._map.getSize().y - (this._container.offsetTop + 50);
            return t < this._form.clientHeight ? (pt(this._form, "gamap-control-layers-scrollbar"), this._form.style.height = t + "px") : mt(this._form, "gamap-control-layers-scrollbar"), this._checkDisabledLayers(), this
        }, collapse: function () {
            return mt(this._container, "gamap-control-layers-expanded"), this
        }, _initLayout: function () {
            var t = "gamap-control-layers", i = this._container = ht("div", t), e = this.options.collapsed;
            i.setAttribute("aria-haspopup", !0), K(i), J(i);
            var n = this._form = ht("form", t + "-list");
            e && (this._map.on("click", this.collapse, this), Oi || U(i, {
                mouseenter: this.expand,
                mouseleave: this.collapse
            }, this));
            var o = this._layersLink = ht("a", t + "-toggle", i);
            o.href = "#", o.title = "Layers", ee ? (U(o, "click", $), U(o, "click", this.expand, this)) : U(o, "focus", this.expand, this), e || this.expand(), this._baseLayersList = ht("div", t + "-base", n), this._separator = ht("div", t + "-separator", n), this._overlaysList = ht("div", t + "-overlays", n), i.appendChild(n)
        }, _getLayer: function (t) {
            for (var i = 0; i < this._layers.length; i++) if (this._layers[i] && n(this._layers[i].layer) === t) return this._layers[i]
        }, _addLayer: function (t, i, n) {
            this._map && t.on("add remove", this._onLayerChange, this), this._layers.push({
                layer: t,
                name: i,
                overlay: n
            }), this.options.sortLayers && this._layers.sort(e(function (t, i) {
                return this.options.sortFunction(t.layer, i.layer, t.name, i.name)
            }, this)), this.options.autoZIndex && t.setZIndex && (this._lastZIndex++, t.setZIndex(this._lastZIndex)), this._expandIfNotCollapsed()
        }, _update: function () {
            if (!this._container) return this;
            ut(this._baseLayersList), ut(this._overlaysList), this._layerControlInputs = [];
            var t, i, e, n, o = 0;
            for (e = 0; e < this._layers.length; e++) n = this._layers[e], this._addItem(n), i = i || n.overlay, t = t || !n.overlay, o += n.overlay ? 0 : 1;
            return this.options.hideSingleBase && (t = t && o > 1, this._baseLayersList.style.display = t ? "" : "none"), this._separator.style.display = i && t ? "" : "none", this
        }, _onLayerChange: function (t) {
            this._handlingClick || this._update();
            var i = this._getLayer(n(t.target)),
                e = i.overlay ? "add" === t.type ? "overlayadd" : "overlayremove" : "add" === t.type ? "baselayerchange" : null;
            e && this._map.fire(e, i)
        }, _createRadioElement: function (t, i) {
            var e = '<input type="radio" class="gamap-control-layers-selector" name="' + t + '"' + (i ? ' checked="checked"' : "") + "/>",
                n = document.createElement("div");
            return n.innerHTML = e, n.firstChild
        }, _addItem: function (t) {
            var i, e = document.createElement("label"), o = this._map.hasLayer(t.layer);
            t.overlay ? ((i = document.createElement("input")).type = "checkbox", i.className = "gamap-control-layers-selector", i.defaultChecked = o) : i = this._createRadioElement("gamap-base-layers", o), this._layerControlInputs.push(i), i.layerId = n(t.layer), U(i, "click", this._onInputClick, this);
            var s = document.createElement("span");
            s.innerHTML = " " + t.name;
            var a = document.createElement("div");
            return e.appendChild(a), a.appendChild(i), a.appendChild(s), (t.overlay ? this._overlaysList : this._baseLayersList).appendChild(e), this._checkDisabledLayers(), e
        }, _onInputClick: function () {
            var t, i, e = this._layerControlInputs, n = [], o = [];
            this._handlingClick = !0;
            for (var s = e.length - 1; s >= 0; s--) t = e[s], i = this._getLayer(t.layerId).layer, t.checked ? n.push(i) : t.checked || o.push(i);
            for (s = 0; s < o.length; s++) this._map.hasLayer(o[s]) && this._map.removeLayer(o[s]);
            for (s = 0; s < n.length; s++) this._map.hasLayer(n[s]) || this._map.addLayer(n[s]);
            this._handlingClick = !1, this._refocusOnMap()
        }, _checkDisabledLayers: function () {
            for (var t, i, e = this._layerControlInputs, n = this._map.getZoom(), o = e.length - 1; o >= 0; o--) t = e[o], i = this._getLayer(t.layerId).layer, t.disabled = void 0 !== i.options.minZoom && n < i.options.minZoom || void 0 !== i.options.maxZoom && n > i.options.maxZoom
        }, _expandIfNotCollapsed: function () {
            return this._map && !this.options.collapsed && this.expand(), this
        }, _expand: function () {
            return this.expand()
        }, _collapse: function () {
            return this.collapse()
        }
    }), Re = Be.extend({
        options: {
            position: "topleft",
            zoomInText: "+",
            zoomInTitle: "Zoom in",
            zoomOutText: "&#x2212;",
            zoomOutTitle: "Zoom out"
        }, onAdd: function (t) {
            var i = "gamap-control-zoom", e = ht("div", i + " gamap-bar"), n = this.options;
            return this._zoomInButton = this._createButton(n.zoomInText, n.zoomInTitle, i + "-in", e, this._zoomIn), this._zoomOutButton = this._createButton(n.zoomOutText, n.zoomOutTitle, i + "-out", e, this._zoomOut), this._updateDisabled(), t.on("zoomend zoomlevelschange", this._updateDisabled, this), e
        }, onRemove: function (t) {
            t.off("zoomend zoomlevelschange", this._updateDisabled, this)
        }, disable: function () {
            return this._disabled = !0, this._updateDisabled(), this
        }, enable: function () {
            return this._disabled = !1, this._updateDisabled(), this
        }, _zoomIn: function (t) {
            !this._disabled && this._map._zoom < this._map.getMaxZoom() && this._map.zoomIn(this._map.options.zoomDelta * (t.shiftKey ? 3 : 1))
        }, _zoomOut: function (t) {
            !this._disabled && this._map._zoom > this._map.getMinZoom() && this._map.zoomOut(this._map.options.zoomDelta * (t.shiftKey ? 3 : 1))
        }, _createButton: function (t, i, e, n, o) {
            var s = ht("a", e, n);
            return s.innerHTML = t, s.href = "#", s.title = i, s.setAttribute("role", "button"), s.setAttribute("aria-label", i), K(s), U(s, "click", $), U(s, "click", o, this), U(s, "click", this._refocusOnMap, this), s
        }, _updateDisabled: function () {
            var t = this._map;
            mt(this._zoomInButton, "gamap-disabled"), mt(this._zoomOutButton, "gamap-disabled"), (this._disabled || t._zoom === t.getMinZoom()) && pt(this._zoomOutButton, "gamap-disabled"), (this._disabled || t._zoom === t.getMaxZoom()) && pt(this._zoomInButton, "gamap-disabled")
        }
    });
    Ie.mergeOptions({zoomControl: !0}), Ie.addInitHook(function () {
        this.options.zoomControl && (this.zoomControl = new Re, this.addControl(this.zoomControl))
    });
    var je = Be.extend({
        options: {position: "bottomleft", maxWidth: 100, metric: !0, imperial: !0}, onAdd: function (t) {
            var i = ht("div", "gamap-control-scale"), e = this.options;
            return this._addScales(e, "gamap-control-scale-line", i), t.on(e.updateWhenIdle ? "moveend" : "move", this._update, this), t.whenReady(this._update, this), i
        }, onRemove: function (t) {
            t.off(this.options.updateWhenIdle ? "moveend" : "move", this._update, this)
        }, _addScales: function (t, i, e) {
            t.metric && (this._mScale = ht("div", i, e)), t.imperial && (this._iScale = ht("div", i, e))
        }, _update: function () {
            var t = this._map, i = t.getSize().y / 2,
                e = t.distance(t.containerPointToLatLng([0, i]), t.containerPointToLatLng([this.options.maxWidth, i]));
            this._updateScales(e)
        }, _updateScales: function (t) {
            this.options.metric && t && this._updateMetric(t), this.options.imperial && t && this._updateImperial(t)
        }, _updateMetric: function (t) {
            var i = this._getRoundNum(t), e = i < 1e3 ? i + " m" : i / 1e3 + " km";
            this._updateScale(this._mScale, e, i / t)
        }, _updateImperial: function (t) {
            var i, e, n, o = 3.2808399 * t;
            o > 5280 ? (i = o / 5280, e = this._getRoundNum(i), this._updateScale(this._iScale, e + " mi", e / i)) : (n = this._getRoundNum(o), this._updateScale(this._iScale, n + " ft", n / o))
        }, _updateScale: function (t, i, e) {
            t.style.width = Math.round(this.options.maxWidth * e) + "px", t.innerHTML = i
        }, _getRoundNum: function (t) {
            var i = Math.pow(10, (Math.floor(t) + "").length - 1), e = t / i;
            return e = e >= 10 ? 10 : e >= 5 ? 5 : e >= 3 ? 3 : e >= 2 ? 2 : 1, i * e
        }
    }), De = Be.extend({
        options: {position: "bottomright", prefix: ""}, initialize: function (t) {
            u(this, t), this._attributions = {}
        }, onAdd: function (t) {
            t.attributionControl = this, this._container = ht("div", "gamap-control-attribution"), K(this._container);
            for (var i in t._layers) t._layers[i].getAttribution && this.addAttribution(t._layers[i].getAttribution());
            return this._update(), this._container
        }, setPrefix: function (t) {
            return this.options.prefix = t, this._update(), this
        }, addAttribution: function (t) {
            return t ? (this._attributions[t] || (this._attributions[t] = 0), this._attributions[t]++, this._update(), this) : this
        }, removeAttribution: function (t) {
            return t ? (this._attributions[t] && (this._attributions[t]--, this._update()), this) : this
        }, _update: function () {
            if (this._map) {
                var t = [];
                for (var i in this._attributions) this._attributions[i] && t.push(i);
                var e = [];
                this.options.prefix && e.push(this.options.prefix), t.length && e.push(t.join(", ")), this._container.innerHTML = e.join(" | ")
            }
        }
    });
    Ie.mergeOptions({attributionControl: !0}), Ie.addInitHook(function () {
        this.options.attributionControl && (new De).addTo(this)
    });
    Be.Layers = Oe, Be.Zoom = Re, Be.Scale = je, Be.Attribution = De, Ze.layers = function (t, i, e) {
        return new Oe(t, i, e)
    }, Ze.zoom = function (t) {
        return new Re(t)
    }, Ze.scale = function (t) {
        return new je(t)
    }, Ze.attribution = function (t) {
        return new De(t)
    };
    var Ne = v.extend({
        initialize: function (t) {
            this._map = t
        }, enable: function () {
            return this._enabled ? this : (this._enabled = !0, this.addHooks(), this)
        }, disable: function () {
            return this._enabled ? (this._enabled = !1, this.removeHooks(), this) : this
        }, enabled: function () {
            return !!this._enabled
        }
    });
    Ne.addTo = function (t, i) {
        return t.addHandler(i, this), this
    };
    var Fe, He = {Events: yi}, We = ee ? "touchstart mousedown" : "mousedown",
        Ge = {mousedown: "mouseup", touchstart: "touchend", pointerdown: "touchend", MSPointerDown: "touchend"},
        Ue = {mousedown: "mousemove", touchstart: "touchmove", pointerdown: "touchmove", MSPointerDown: "touchmove"},
        Ve = xi.extend({
            options: {clickTolerance: 3}, initialize: function (t, i, e, n) {
                u(this, n), this._element = t, this._dragStartTarget = i || t, this._preventOutline = e
            }, enable: function () {
                this._enabled || (U(this._dragStartTarget, We, this._onDown, this), this._enabled = !0)
            }, disable: function () {
                this._enabled && (Ve._dragging === this && this.finishDrag(), V(this._dragStartTarget, We, this._onDown, this), this._enabled = !1, this._moved = !1)
            }, _onDown: function (t) {
                if (!t._simulated && this._enabled && (this._moved = !1, !_t(this._element, "gamap-zoom-anim") && !(Ve._dragging || t.shiftKey || 1 !== t.which && 1 !== t.button && !t.touches || (Ve._dragging = this, this._preventOutline && Mt(this._element), Pt(), Mi(), this._moving)))) {
                    this.fire("down");
                    var i = t.touches ? t.touches[0] : t;
                    this._startPoint = new x(i.clientX, i.clientY), U(document, Ue[t.type], this._onMove, this), U(document, Ge[t.type], this._onUp, this)
                }
            }, _onMove: function (t) {
                if (!t._simulated && this._enabled) if (t.touches && t.touches.length > 1) this._moved = !0; else {
                    var i = t.touches && 1 === t.touches.length ? t.touches[0] : t,
                        e = new x(i.clientX, i.clientY).subtract(this._startPoint);
                    (e.x || e.y) && (Math.abs(e.x) + Math.abs(e.y) < this.options.clickTolerance || (Q(t), this._moved || (this.fire("dragstart"), this._moved = !0, this._startPos = bt(this._element).subtract(e), pt(document.body, "gamap-dragging"), this._lastTarget = t.target || t.srcElement, window.SVGElementInstance && this._lastTarget instanceof SVGElementInstance && (this._lastTarget = this._lastTarget.correspondingUseElement), pt(this._lastTarget, "gamap-drag-target")), this._newPos = this._startPos.add(e), this._moving = !0, g(this._animRequest), this._lastEvent = t, this._animRequest = f(this._updatePosition, this, !0)))
                }
            }, _updatePosition: function () {
                var t = {originalEvent: this._lastEvent};
                this.fire("predrag", t), Lt(this._element, this._newPos), this.fire("drag", t)
            }, _onUp: function (t) {
                !t._simulated && this._enabled && this.finishDrag()
            }, finishDrag: function () {
                mt(document.body, "gamap-dragging"), this._lastTarget && (mt(this._lastTarget, "gamap-drag-target"), this._lastTarget = null);
                for (var t in Ue) V(document, Ue[t], this._onMove, this), V(document, Ge[t], this._onUp, this);
                Tt(), Ci(), this._moved && this._moving && (g(this._animRequest), this.fire("dragend", {distance: this._newPos.distanceTo(this._startPos)})), this._moving = !1, Ve._dragging = !1
            }
        }), qe = (Object.freeze || Object)({
            simplify: At,
            pointToSegmentDistance: zt,
            closestPointOnSegment: function (t, i, e) {
                return Rt(t, i, e)
            },
            clipSegment: It,
            _getEdgeIntersection: Bt,
            _getBitCode: Zt,
            _sqClosestPointOnSegment: Rt,
            isFlat: jt,
            _flat: Dt,
            segmentsIntersect: function (t, i, e, n) {
                return this._checkCounterclockwise(t, e, n) !== this._checkCounterclockwise(i, e, n) && this._checkCounterclockwise(t, i, e) !== this._checkCounterclockwise(t, i, n)
            },
            _checkCounterclockwise: function (t, i, e) {
                return (e.y - t.y) * (i.x - t.x) > (i.y - t.y) * (e.x - t.x)
            }
        }), Xe = (Object.freeze || Object)({clipPolygon: Nt}), Ye = {
            project: function (t) {
                return new x(t.lng, t.lat)
            }, unproject: function (t) {
                return new C(t.y, t.x)
            }, bounds: new b([-180, -90], [180, 90])
        }, Je = {
            R: 6378137,
            R_MINOR: 6356752.314245179,
            bounds: new b([-20037508.34279, -15496570.73972], [20037508.34279, 18764656.23138]),
            project: function (t) {
                var i = Math.PI / 180, e = this.R, n = t.lat * i, o = this.R_MINOR / e, s = Math.sqrt(1 - o * o),
                    a = s * Math.sin(n), r = Math.tan(Math.PI / 4 - n / 2) / Math.pow((1 - a) / (1 + a), s / 2);
                return n = -e * Math.log(Math.max(r, 1e-10)), new x(t.lng * i * e, n)
            },
            unproject: function (t) {
                for (var i, e = 180 / Math.PI, n = this.R, o = this.R_MINOR / n, s = Math.sqrt(1 - o * o), a = Math.exp(-t.y / n), r = Math.PI / 2 - 2 * Math.atan(a), h = 0, l = .1; h < 15 && Math.abs(l) > 1e-7; h++) i = s * Math.sin(r), i = Math.pow((1 - i) / (1 + i), s / 2), r += l = Math.PI / 2 - 2 * Math.atan(a * i) - r;
                return new C(r * e, t.x * e / n)
            }
        }, Ke = (Object.freeze || Object)({LonLat: Ye, Mercator: Je, SphericalMercator: Pi}), Qe = i({}, bi, {
            code: "EPSG:3395", projection: Je, transformation: function () {
                var t = .5 / (Math.PI * Je.R);
                return S(t, .5, -t, .5)
            }()
        }), $e = i({}, bi, {code: "EPSG:4326", projection: Ye, transformation: S(1 / 180, 1, -1 / 180, .5)}),
        tn = i({}, Li, {
            projection: Ye, transformation: S(1, 0, -1, 0), scale: function (t) {
                return Math.pow(2, t)
            }, zoom: function (t) {
                return Math.log(t) / Math.LN2
            }, distance: function (t, i) {
                var e = i.lng - t.lng, n = i.lat - t.lat;
                return Math.sqrt(e * e + n * n)
            }, infinite: !0
        });
    Li.Earth = bi, Li.EPSG3395 = Qe, Li.EPSG3857 = zi, Li.EPSG900913 = Si, Li.EPSG4326 = $e, Li.Simple = tn;
    var en = xi.extend({
        options: {pane: "overlayPane", attribution: null, bubblingMouseEvents: !0},
        addTo: function (t) {
            return t.addLayer(this), this
        },
        remove: function () {
            return this.removeFrom(this._map || this._mapToAdd)
        },
        removeFrom: function (t) {
            return t && t.removeLayer(this), this
        },
        getPane: function (t) {
            return this._map.getPane(t ? this.options[t] || t : this.options.pane)
        },
        addInteractiveTarget: function (t) {
            return this._map._targets[n(t)] = this, this
        },
        removeInteractiveTarget: function (t) {
            return delete this._map._targets[n(t)], this
        },
        getAttribution: function () {
            return this.options.attribution
        },
        _layerAdd: function (t) {
            var i = t.target;
            if (i.hasLayer(this)) {
                if (this._map = i, this._zoomAnimated = i._zoomAnimated, this.getEvents) {
                    var e = this.getEvents();
                    i.on(e, this), this.once("remove", function () {
                        i.off(e, this)
                    }, this)
                }
                this.onAdd(i), this.getAttribution && i.attributionControl && i.attributionControl.addAttribution(this.getAttribution()), this.fire("add"), i.fire("layeradd", {layer: this})
            }
        }
    });
    Ie.include({
        addLayer: function (t) {
            if (!t._layerAdd) throw new Error("The provided object is not a Layer.");
            var i = n(t);
            return this._layers[i] ? this : (this._layers[i] = t, t._mapToAdd = this, t.beforeAdd && t.beforeAdd(this), this.whenReady(t._layerAdd, t), this)
        }, removeLayer: function (t) {
            var i = n(t);
            return this._layers[i] ? (this._loaded && t.onRemove(this), t.getAttribution && this.attributionControl && this.attributionControl.removeAttribution(t.getAttribution()), delete this._layers[i], this._loaded && (this.fire("layerremove", {layer: t}), t.fire("remove")), t._map = t._mapToAdd = null, this) : this
        }, hasLayer: function (t) {
            return !!t && n(t) in this._layers
        }, eachLayer: function (t, i) {
            for (var e in this._layers) t.call(i, this._layers[e]);
            return this
        }, _addLayers: function (t) {
            for (var i = 0, e = (t = t ? _i(t) ? t : [t] : []).length; i < e; i++) this.addLayer(t[i])
        }, _addZoomLimit: function (t) {
            !isNaN(t.options.maxZoom) && isNaN(t.options.minZoom) || (this._zoomBoundLayers[n(t)] = t, this._updateZoomLevels())
        }, _removeZoomLimit: function (t) {
            var i = n(t);
            this._zoomBoundLayers[i] && (delete this._zoomBoundLayers[i], this._updateZoomLevels())
        }, _updateZoomLevels: function () {
            var t = 1 / 0, i = -1 / 0, e = this._getZoomSpan();
            for (var n in this._zoomBoundLayers) {
                var o = this._zoomBoundLayers[n].options;
                t = void 0 === o.minZoom ? t : Math.min(t, o.minZoom), i = void 0 === o.maxZoom ? i : Math.max(i, o.maxZoom)
            }
            this._layersMaxZoom = i === -1 / 0 ? void 0 : i, this._layersMinZoom = t === 1 / 0 ? void 0 : t, e !== this._getZoomSpan() && this.fire("zoomlevelschange"), void 0 === this.options.maxZoom && this._layersMaxZoom && this.getZoom() > this._layersMaxZoom && this.setZoom(this._layersMaxZoom), void 0 === this.options.minZoom && this._layersMinZoom && this.getZoom() < this._layersMinZoom && this.setZoom(this._layersMinZoom)
        }
    });
    var nn = en.extend({
        initialize: function (t, i) {
            u(this, i), this._layers = {};
            var e, n;
            if (t) for (e = 0, n = t.length; e < n; e++) this.addLayer(t[e])
        }, addLayer: function (t) {
            var i = this.getLayerId(t);
            return this._layers[i] = t, this._map && this._map.addLayer(t), this
        }, removeLayer: function (t) {
            var i = t in this._layers ? t : this.getLayerId(t);
            return this._map && this._layers[i] && this._map.removeLayer(this._layers[i]), delete this._layers[i], this
        }, hasLayer: function (t) {
            return !!t && (t in this._layers || this.getLayerId(t) in this._layers)
        }, clearLayers: function () {
            return this.eachLayer(this.removeLayer, this)
        }, invoke: function (t) {
            var i, e, n = Array.prototype.slice.call(arguments, 1);
            for (i in this._layers) (e = this._layers[i])[t] && e[t].apply(e, n);
            return this
        }, onAdd: function (t) {
            this.eachLayer(t.addLayer, t)
        }, onRemove: function (t) {
            this.eachLayer(t.removeLayer, t)
        }, eachLayer: function (t, i) {
            for (var e in this._layers) t.call(i, this._layers[e]);
            return this
        }, getLayer: function (t) {
            return this._layers[t]
        }, getLayers: function () {
            var t = [];
            return this.eachLayer(t.push, t), t
        }, setZIndex: function (t) {
            return this.invoke("setZIndex", t)
        }, getLayerId: function (t) {
            return n(t)
        }
    }), on = nn.extend({
        addLayer: function (t) {
            return this.hasLayer(t) ? this : (t.addEventParent(this), nn.prototype.addLayer.call(this, t), this.fire("layeradd", {layer: t}))
        }, removeLayer: function (t) {
            return this.hasLayer(t) ? (t in this._layers && (t = this._layers[t]), t.removeEventParent(this), nn.prototype.removeLayer.call(this, t), this.fire("layerremove", {layer: t})) : this
        }, setStyle: function (t) {
            return this.invoke("setStyle", t)
        }, bringToFront: function () {
            return this.invoke("bringToFront")
        }, bringToBack: function () {
            return this.invoke("bringToBack")
        }, getBounds: function () {
            var t = new T;
            for (var i in this._layers) {
                var e = this._layers[i];
                t.extend(e.getBounds ? e.getBounds() : e.getLatLng())
            }
            return t
        }
    }), sn = v.extend({
        options: {popupAnchor: [0, 0], tooltipAnchor: [0, 0]}, initialize: function (t) {
            u(this, t)
        }, createIcon: function (t) {
            return this._createIcon("icon", t)
        }, createShadow: function (t) {
            return this._createIcon("shadow", t)
        }, _createIcon: function (t, i) {
            var e = this._getIconUrl(t);
            if (!e) {
                if ("icon" === t) throw new Error("iconUrl not set in Icon options (see the docs).");
                return null
            }
            var n = this._createImg(e, i && "IMG" === i.tagName ? i : null);
            return this._setIconStyles(n, t), n
        }, _setIconStyles: function (t, i) {
            var e = this.options, n = e[i + "Size"];
            "number" == typeof n && (n = [n, n]);
            var o = w(n), s = w("shadow" === i && e.shadowAnchor || e.iconAnchor || o && o.divideBy(2, !0));
            t.className = "gamap-marker-" + i + " " + (e.className || ""), s && (t.style.marginLeft = -s.x + "px", t.style.marginTop = -s.y + "px"), o && (t.style.width = o.x + "px", t.style.height = o.y + "px")
        }, _createImg: function (t, i) {
            return i = i || document.createElement("img"), i.src = t, i
        }, _getIconUrl: function (t) {
            return se && this.options[t + "RetinaUrl"] || this.options[t + "Url"]
        }
    }), an = sn.extend({
        options: {
            iconUrl: "marker-icon.png",
            iconRetinaUrl: "marker-icon-2x.png",
            shadowUrl: "marker-shadow.png",
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            tooltipAnchor: [16, -28],
            shadowSize: [41, 41]
        }, _getIconUrl: function (t) {
            return an.imagePath || (an.imagePath = this._detectIconPath()), (this.options.imagePath || an.imagePath) + sn.prototype._getIconUrl.call(this, t)
        }, _detectIconPath: function () {
            var t = ht("div", "gamap-default-icon-path", document.body),
                i = rt(t, "background-image") || rt(t, "backgroundImage");
            return document.body.removeChild(t), i = null === i || 0 !== i.indexOf("url") ? "" : i = i.replace(/^url\(["']?/, "").replace(/marker-icon\.png["']?\)$/, "")
        }
    }), rn = Ne.extend({
        initialize: function (t) {
            this._marker = t
        }, addHooks: function () {
            var t = this._marker._icon;
            this._draggable || (this._draggable = new Ve(t, t, !0)), this._draggable.on({
                dragstart: this._onDragStart,
                predrag: this._onPreDrag,
                drag: this._onDrag,
                dragend: this._onDragEnd
            }, this).enable(), pt(t, "gamap-marker-draggable")
        }, removeHooks: function () {
            this._draggable.off({
                dragstart: this._onDragStart,
                predrag: this._onPreDrag,
                drag: this._onDrag,
                dragend: this._onDragEnd
            }, this).disable(), this._marker._icon && mt(this._marker._icon, "gamap-marker-draggable")
        }, moved: function () {
            return this._draggable && this._draggable._moved
        }, _adjustPan: function (t) {
            var i = this._marker, e = i._map, n = this._marker.options.autoPanSpeed,
                o = this._marker.options.autoPanPadding, s = GAMap.DomUtil.getPosition(i._icon), a = e.getPixelBounds(),
                r = e.getPixelOrigin(), h = P(a.min._subtract(r).add(o), a.max._subtract(r).subtract(o));
            if (!h.contains(s)) {
                var l = w((Math.max(h.max.x, s.x) - h.max.x) / (a.max.x - h.max.x) - (Math.min(h.min.x, s.x) - h.min.x) / (a.min.x - h.min.x), (Math.max(h.max.y, s.y) - h.max.y) / (a.max.y - h.max.y) - (Math.min(h.min.y, s.y) - h.min.y) / (a.min.y - h.min.y)).multiplyBy(n);
                e.panBy(l, {animate: !1}), this._draggable._newPos._add(l), this._draggable._startPos._add(l), GAMap.DomUtil.setPosition(i._icon, this._draggable._newPos), this._onDrag(t), this._panRequest = f(this._adjustPan.bind(this, t))
            }
        }, _onDragStart: function () {
            this._oldLatLng = this._marker.getLatLng(), this._marker.closePopup().fire("movestart").fire("dragstart")
        }, _onPreDrag: function (t) {
            this._marker.options.autoPan && (g(this._panRequest), this._panRequest = f(this._adjustPan.bind(this, t)))
        }, _onDrag: function (t) {
            var i = this._marker, e = i._shadow, n = bt(i._icon), o = i._map.layerPointToLatLng(n);
            e && Lt(e, n), i._latlng = o, t.latlng = o, t.oldLatLng = this._oldLatLng, i.fire("move", t).fire("drag", t)
        }, _onDragEnd: function (t) {
            g(this._panRequest), delete this._oldLatLng, this._marker.fire("moveend").fire("dragend", t)
        }
    }), hn = en.extend({
        options: {
            icon: new an,
            interactive: !0,
            draggable: !1,
            autoPan: !1,
            autoPanPadding: [50, 50],
            autoPanSpeed: 10,
            keyboard: !0,
            title: "",
            alt: "",
            zIndexOffset: 0,
            opacity: 1,
            riseOnHover: !1,
            riseOffset: 250,
            pane: "markerPane",
            bubblingMouseEvents: !1
        }, initialize: function (t, i) {
            u(this, i), this._latlng = A(t)
        }, onAdd: function (t) {
            this._zoomAnimated = this._zoomAnimated && t.options.markerZoomAnimation, this._zoomAnimated && t.on("zoomanim", this._animateZoom, this), this._initIcon(), this.update()
        }, onRemove: function (t) {
            this.dragging && this.dragging.enabled() && (this.options.draggable = !0, this.dragging.removeHooks()), delete this.dragging, this._zoomAnimated && t.off("zoomanim", this._animateZoom, this), this._removeIcon(), this._removeShadow()
        }, getEvents: function () {
            return {zoom: this.update, viewreset: this.update}
        }, getLatLng: function () {
            return this._latlng
        }, setLatLng: function (t) {
            var i = this._latlng;
            return this._latlng = A(t), this.update(), this.fire("move", {oldLatLng: i, latlng: this._latlng})
        }, setZIndexOffset: function (t) {
            return this.options.zIndexOffset = t, this.update()
        }, setIcon: function (t) {
            return this.options.icon = t, this._map && (this._initIcon(), this.update()), this._popup && this.bindPopup(this._popup, this._popup.options), this
        }, getElement: function () {
            return this._icon
        }, update: function () {
            if (this._icon && this._map) {
                var t = this._map.latLngToLayerPoint(this._latlng).round();
                this._setPos(t)
            }
            return this
        }, _initIcon: function () {
            var t = this.options, i = "gamap-zoom-" + (this._zoomAnimated ? "animated" : "hide"),
                e = t.icon.createIcon(this._icon), n = !1;
            e !== this._icon && (this._icon && this._removeIcon(), n = !0, t.title && (e.title = t.title), "IMG" === e.tagName && (e.alt = t.alt || "")), pt(e, i), t.keyboard && (e.tabIndex = "0"), this._icon = e, t.riseOnHover && this.on({
                mouseover: this._bringToFront,
                mouseout: this._resetZIndex
            });
            var o = t.icon.createShadow(this._shadow), s = !1;
            o !== this._shadow && (this._removeShadow(), s = !0), o && (pt(o, i), o.alt = ""), this._shadow = o, t.opacity < 1 && this._updateOpacity(), n && this.getPane().appendChild(this._icon), this._initInteraction(), o && s && this.getPane("shadowPane").appendChild(this._shadow)
        }, _removeIcon: function () {
            this.options.riseOnHover && this.off({
                mouseover: this._bringToFront,
                mouseout: this._resetZIndex
            }), lt(this._icon), this.removeInteractiveTarget(this._icon), this._icon = null
        }, _removeShadow: function () {
            this._shadow && lt(this._shadow), this._shadow = null
        }, _setPos: function (t) {
            Lt(this._icon, t), this._shadow && Lt(this._shadow, t), this._zIndex = t.y + this.options.zIndexOffset, this._resetZIndex()
        }, _updateZIndex: function (t) {
            this._icon.style.zIndex = this._zIndex + t
        }, _animateZoom: function (t) {
            var i = this._map._latLngToNewLayerPoint(this._latlng, t.zoom, t.center).round();
            this._setPos(i)
        }, _initInteraction: function () {
            if (this.options.interactive && (pt(this._icon, "gamap-interactive"), this.addInteractiveTarget(this._icon), rn)) {
                var t = this.options.draggable;
                this.dragging && (t = this.dragging.enabled(), this.dragging.disable()), this.dragging = new rn(this), t && this.dragging.enable()
            }
        }, setOpacity: function (t) {
            return this.options.opacity = t, this._map && this._updateOpacity(), this
        }, _updateOpacity: function () {
            var t = this.options.opacity;
            vt(this._icon, t), this._shadow && vt(this._shadow, t)
        }, _bringToFront: function () {
            this._updateZIndex(this.options.riseOffset)
        }, _resetZIndex: function () {
            this._updateZIndex(0)
        }, _getPopupAnchor: function () {
            return this.options.icon.options.popupAnchor
        }, _getTooltipAnchor: function () {
            return this.options.icon.options.tooltipAnchor
        }
    }), ln = en.extend({
        options: {
            stroke: !0,
            color: "#3388ff",
            weight: 3,
            opacity: 1,
            lineCap: "round",
            lineJoin: "round",
            dashArray: null,
            dashOffset: null,
            fill: !1,
            fillColor: null,
            fillOpacity: .2,
            fillRule: "evenodd",
            interactive: !0,
            bubblingMouseEvents: !0
        }, beforeAdd: function (t) {
            this._renderer = t.getRenderer(this)
        }, onAdd: function () {
            this._renderer._initPath(this), this._reset(), this._renderer._addPath(this)
        }, onRemove: function () {
            this._renderer._removePath(this)
        }, redraw: function () {
            return this._map && this._renderer._updatePath(this), this
        }, setStyle: function (t) {
            return u(this, t), this._renderer && this._renderer._updateStyle(this), this
        }, bringToFront: function () {
            return this._renderer && this._renderer._bringToFront(this), this
        }, bringToBack: function () {
            return this._renderer && this._renderer._bringToBack(this), this
        }, getElement: function () {
            return this._path
        }, _reset: function () {
            this._project(), this._update()
        }, _clickTolerance: function () {
            return (this.options.stroke ? this.options.weight / 2 : 0) + this._renderer.options.tolerance
        }
    }), un = ln.extend({
        options: {fill: !0, radius: 10}, initialize: function (t, i) {
            u(this, i), this._latlng = A(t), this._radius = this.options.radius
        }, setLatLng: function (t) {
            return this._latlng = A(t), this.redraw(), this.fire("move", {latlng: this._latlng})
        }, getLatLng: function () {
            return this._latlng
        }, setRadius: function (t) {
            return this.options.radius = this._radius = t, this.redraw()
        }, getRadius: function () {
            return this._radius
        }, setStyle: function (t) {
            var i = t && t.radius || this._radius;
            return ln.prototype.setStyle.call(this, t), this.setRadius(i), this
        }, _project: function () {
            this._point = this._map.latLngToLayerPoint(this._latlng), this._updateBounds()
        }, _updateBounds: function () {
            var t = this._radius, i = this._radiusY || t, e = this._clickTolerance(), n = [t + e, i + e];
            this._pxBounds = new b(this._point.subtract(n), this._point.add(n))
        }, _update: function () {
            this._map && this._updatePath()
        }, _updatePath: function () {
            this._renderer._updateCircle(this)
        }, _empty: function () {
            return this._radius && !this._renderer._bounds.intersects(this._pxBounds)
        }, _containsPoint: function (t) {
            return t.distanceTo(this._point) <= this._radius + this._clickTolerance()
        }
    }), cn = un.extend({
        initialize: function (t, e, n) {
            if ("number" == typeof e && (e = i({}, n, {radius: e})), u(this, e), this._latlng = A(t), isNaN(this.options.radius)) throw new Error("Circle radius cannot be NaN");
            this._mRadius = this.options.radius
        }, setRadius: function (t) {
            return this._mRadius = t, this.redraw()
        }, getRadius: function () {
            return this._mRadius
        }, getBounds: function () {
            var t = [this._radius, this._radiusY || this._radius];
            return new T(this._map.layerPointToLatLng(this._point.subtract(t)), this._map.layerPointToLatLng(this._point.add(t)))
        }, setStyle: ln.prototype.setStyle, _project: function () {
            var t = this._latlng.lng, i = this._latlng.lat, e = this._map, n = e.options.crs;
            if (n.distance === bi.distance) {
                var o = Math.PI / 180, s = this._mRadius / bi.R / o, a = e.project([i + s, t]),
                    r = e.project([i - s, t]), h = a.add(r).divideBy(2), l = e.unproject(h).lat,
                    u = Math.acos((Math.cos(s * o) - Math.sin(i * o) * Math.sin(l * o)) / (Math.cos(i * o) * Math.cos(l * o))) / o;
                (isNaN(u) || 0 === u) && (u = s / Math.cos(Math.PI / 180 * i)), this._point = h.subtract(e.getPixelOrigin()), this._radius = isNaN(u) ? 0 : h.x - e.project([l, t - u]).x, this._radiusY = h.y - a.y
            } else {
                var c = n.unproject(n.project(this._latlng).subtract([this._mRadius, 0]));
                this._point = e.latLngToLayerPoint(this._latlng), this._radius = this._point.x - e.latLngToLayerPoint(c).x
            }
            this._updateBounds()
        }
    }), dn = ln.extend({
        options: {smoothFactor: 1, noClip: !1}, initialize: function (t, i) {
            u(this, i), this._setLatLngs(t)
        }, getLatLngs: function () {
            return this._latlngs
        }, setLatLngs: function (t) {
            return this._setLatLngs(t), this.redraw()
        }, isEmpty: function () {
            return !this._latlngs.length
        }, closestLayerPoint: function (t) {
            for (var i, e, n = 1 / 0, o = null, s = Rt, a = 0, r = this._parts.length; a < r; a++) for (var h = this._parts[a], l = 1, u = h.length; l < u; l++) {
                var c = s(t, i = h[l - 1], e = h[l], !0);
                c < n && (n = c, o = s(t, i, e))
            }
            return o && (o.distance = Math.sqrt(n)), o
        }, getCenter: function () {
            if (!this._map) throw new Error("Must add layer to map before using getCenter()");
            var t, i, e, n, o, s, a, r = this._rings[0], h = r.length;
            if (!h) return null;
            for (t = 0, i = 0; t < h - 1; t++) i += r[t].distanceTo(r[t + 1]) / 2;
            if (0 === i) return this._map.layerPointToLatLng(r[0]);
            for (t = 0, n = 0; t < h - 1; t++) if (o = r[t], s = r[t + 1], e = o.distanceTo(s), (n += e) > i) return a = (n - i) / e, this._map.layerPointToLatLng([s.x - a * (s.x - o.x), s.y - a * (s.y - o.y)])
        }, getBounds: function () {
            return this._bounds
        }, addLatLng: function (t, i) {
            return i = i || this._defaultShape(), t = A(t), i.push(t), this._bounds.extend(t), this.redraw()
        }, _setLatLngs: function (t) {
            this._bounds = new T, this._latlngs = this._convertLatLngs(t)
        }, _defaultShape: function () {
            return jt(this._latlngs) ? this._latlngs : this._latlngs[0]
        }, _convertLatLngs: function (t) {
            for (var i = [], e = jt(t), n = 0, o = t.length; n < o; n++) e ? (i[n] = A(t[n]), this._bounds.extend(i[n])) : i[n] = this._convertLatLngs(t[n]);
            return i
        }, _project: function () {
            var t = new b;
            this._rings = [], this._projectLatlngs(this._latlngs, this._rings, t);
            var i = this._clickTolerance(), e = new x(i, i);
            this._bounds.isValid() && t.isValid() && (t.min._subtract(e), t.max._add(e), this._pxBounds = t)
        }, _projectLatlngs: function (t, i, e) {
            var n, o, s = t[0] instanceof C, a = t.length;
            if (s) {
                for (o = [], n = 0; n < a; n++) o[n] = this._map.latLngToLayerPoint(t[n]), e.extend(o[n]);
                i.push(o)
            } else for (n = 0; n < a; n++) this._projectLatlngs(t[n], i, e)
        }, _clipPoints: function () {
            var t = this._renderer._bounds;
            if (this._parts = [], this._pxBounds && this._pxBounds.intersects(t)) if (this.options.noClip) this._parts = this._rings; else {
                var i, e, n, o, s, a, r, h = this._parts;
                for (i = 0, n = 0, o = this._rings.length; i < o; i++) for (e = 0, s = (r = this._rings[i]).length; e < s - 1; e++) (a = It(r[e], r[e + 1], t, e, !0)) && (h[n] = h[n] || [], h[n].push(a[0]), a[1] === r[e + 1] && e !== s - 2 || (h[n].push(a[1]), n++))
            }
        }, _simplifyPoints: function () {
            for (var t = this._parts, i = this.options.smoothFactor, e = 0, n = t.length; e < n; e++) t[e] = At(t[e], i)
        }, _update: function () {
            this._map && (this._clipPoints(), this._simplifyPoints(), this._updatePath())
        }, _updatePath: function () {
            this._renderer._updatePoly(this)
        }, _containsPoint: function (t, i) {
            var e, n, o, s, a, r, h = this._clickTolerance();
            if (!this._pxBounds || !this._pxBounds.contains(t)) return !1;
            for (e = 0, s = this._parts.length; e < s; e++) for (n = 0, o = (a = (r = this._parts[e]).length) - 1; n < a; o = n++) if ((i || 0 !== n) && zt(t, r[o], r[n]) <= h) return !0;
            return !1
        }
    });
    dn._flat = Dt;
    var _n = dn.extend({
        options: {fill: !0}, isEmpty: function () {
            return !this._latlngs.length || !this._latlngs[0].length
        }, getCenter: function () {
            if (!this._map) throw new Error("Must add layer to map before using getCenter()");
            var t, i, e, n, o, s, a, r, h, l = this._rings[0], u = l.length;
            if (!u) return null;
            for (s = a = r = 0, t = 0, i = u - 1; t < u; i = t++) e = l[t], n = l[i], o = e.y * n.x - n.y * e.x, a += (e.x + n.x) * o, r += (e.y + n.y) * o, s += 3 * o;
            return h = 0 === s ? l[0] : [a / s, r / s], this._map.layerPointToLatLng(h)
        }, _convertLatLngs: function (t) {
            var i = dn.prototype._convertLatLngs.call(this, t), e = i.length;
            return e >= 2 && i[0] instanceof C && i[0].equals(i[e - 1]) && i.pop(), i
        }, _setLatLngs: function (t) {
            dn.prototype._setLatLngs.call(this, t), jt(this._latlngs) && (this._latlngs = [this._latlngs])
        }, _defaultShape: function () {
            return jt(this._latlngs[0]) ? this._latlngs[0] : this._latlngs[0][0]
        }, _clipPoints: function () {
            var t = this._renderer._bounds, i = this.options.weight, e = new x(i, i);
            if (t = new b(t.min.subtract(e), t.max.add(e)), this._parts = [], this._pxBounds && this._pxBounds.intersects(t)) if (this.options.noClip) this._parts = this._rings; else for (var n, o = 0, s = this._rings.length; o < s; o++) (n = Nt(this._rings[o], t, !0)).length && this._parts.push(n)
        }, _updatePath: function () {
            this._renderer._updatePoly(this, !0)
        }, _containsPoint: function (t) {
            var i, e, n, o, s, a, r, h, l = !1;
            if (!this._pxBounds.contains(t)) return !1;
            for (o = 0, r = this._parts.length; o < r; o++) for (s = 0, a = (h = (i = this._parts[o]).length) - 1; s < h; a = s++) e = i[s], n = i[a], e.y > t.y != n.y > t.y && t.x < (n.x - e.x) * (t.y - e.y) / (n.y - e.y) + e.x && (l = !l);
            return l || dn.prototype._containsPoint.call(this, t, !0)
        }
    }), pn = on.extend({
        initialize: function (t, i) {
            u(this, i), this._layers = {}, t && this.addData(t)
        }, addData: function (t) {
            var i, e, n, o = _i(t) ? t : t.features;
            if (o) {
                for (i = 0, e = o.length; i < e; i++) ((n = o[i]).geometries || n.geometry || n.features || n.coordinates) && this.addData(n);
                return this
            }
            var s = this.options;
            if (s.filter && !s.filter(t)) return this;
            var a = Ft(t, s);
            return a ? (a.feature = qt(t), a.defaultOptions = a.options, this.resetStyle(a), s.onEachFeature && s.onEachFeature(t, a), this.addLayer(a)) : this
        }, resetStyle: function (t) {
            return t.options = i({}, t.defaultOptions), this._setLayerStyle(t, this.options.style), this
        }, setStyle: function (t) {
            return this.eachLayer(function (i) {
                this._setLayerStyle(i, t)
            }, this)
        }, _setLayerStyle: function (t, i) {
            "function" == typeof i && (i = i(t.feature)), t.setStyle && t.setStyle(i)
        }
    }), mn = {
        toGeoJSON: function (t) {
            return Vt(this, {type: "Point", coordinates: Gt(this.getLatLng(), t)})
        }
    };
    hn.include(mn), cn.include(mn), un.include(mn), dn.include({
        toGeoJSON: function (t) {
            var i = !jt(this._latlngs), e = Ut(this._latlngs, i ? 1 : 0, !1, t);
            return Vt(this, {type: (i ? "Multi" : "") + "LineString", coordinates: e})
        }
    }), _n.include({
        toGeoJSON: function (t) {
            var i = !jt(this._latlngs), e = i && !jt(this._latlngs[0]), n = Ut(this._latlngs, e ? 2 : i ? 1 : 0, !0, t);
            return i || (n = [n]), Vt(this, {type: (e ? "Multi" : "") + "Polygon", coordinates: n})
        }
    }), nn.include({
        toMultiPoint: function (t) {
            var i = [];
            return this.eachLayer(function (e) {
                i.push(e.toGeoJSON(t).geometry.coordinates)
            }), Vt(this, {type: "MultiPoint", coordinates: i})
        }, toGeoJSON: function (t) {
            var i = this.feature && this.feature.geometry && this.feature.geometry.type;
            if ("MultiPoint" === i) return this.toMultiPoint(t);
            var e = "GeometryCollection" === i, n = [];
            return this.eachLayer(function (i) {
                if (i.toGeoJSON) {
                    var o = i.toGeoJSON(t);
                    if (e) n.push(o.geometry); else {
                        var s = qt(o);
                        "FeatureCollection" === s.type ? n.push.apply(n, s.features) : n.push(s)
                    }
                }
            }), e ? Vt(this, {geometries: n, type: "GeometryCollection"}) : {type: "FeatureCollection", features: n}
        }
    });
    var fn = Xt, gn = en.extend({
        options: {opacity: 1, alt: "", interactive: !1, crossOrigin: !1, errorOverlayUrl: "", zIndex: 1, className: ""},
        initialize: function (t, i, e) {
            this._url = t, this._bounds = M(i), u(this, e)
        },
        onAdd: function () {
            this._image || (this._initImage(), this.options.opacity < 1 && this._updateOpacity()), this.options.interactive && (pt(this._image, "gamap-interactive"), this.addInteractiveTarget(this._image)), this.getPane().appendChild(this._image), this._reset()
        },
        onRemove: function () {
            lt(this._image), this.options.interactive && this.removeInteractiveTarget(this._image)
        },
        setOpacity: function (t) {
            return this.options.opacity = t, this._image && this._updateOpacity(), this
        },
        setStyle: function (t) {
            return t.opacity && this.setOpacity(t.opacity), this
        },
        bringToFront: function () {
            return this._map && ct(this._image), this
        },
        bringToBack: function () {
            return this._map && dt(this._image), this
        },
        setUrl: function (t) {
            return this._url = t, this._image && (this._image.src = t), this
        },
        setBounds: function (t) {
            return this._bounds = M(t), this._map && this._reset(), this
        },
        getEvents: function () {
            var t = {zoom: this._reset, viewreset: this._reset};
            return this._zoomAnimated && (t.zoomanim = this._animateZoom), t
        },
        setZIndex: function (t) {
            return this.options.zIndex = t, this._updateZIndex(), this
        },
        getBounds: function () {
            return this._bounds
        },
        getElement: function () {
            return this._image
        },
        _initImage: function () {
            var t = "IMG" === this._url.tagName, i = this._image = t ? this._url : ht("img");
            pt(i, "gamap-image-layer"), this._zoomAnimated && pt(i, "gamap-zoom-animated"), this.options.className && pt(i, this.options.className), i.onselectstart = a, i.onmousemove = a, i.onload = e(this.fire, this, "load"), i.onerror = e(this._overlayOnError, this, "error"), this.options.crossOrigin && (i.crossOrigin = ""), this.options.zIndex && this._updateZIndex(), t ? this._url = i.src : (i.src = this._url, i.alt = this.options.alt)
        },
        _animateZoom: function (t) {
            var i = this._map.getZoomScale(t.zoom),
                e = this._map._latLngBoundsToNewLayerBounds(this._bounds, t.zoom, t.center).min;
            wt(this._image, e, i)
        },
        _reset: function () {
            var t = this._image,
                i = new b(this._map.latLngToLayerPoint(this._bounds.getNorthWest()), this._map.latLngToLayerPoint(this._bounds.getSouthEast())),
                e = i.getSize();
            Lt(t, i.min), t.style.width = e.x + "px", t.style.height = e.y + "px"
        },
        _updateOpacity: function () {
            vt(this._image, this.options.opacity)
        },
        _updateZIndex: function () {
            this._image && void 0 !== this.options.zIndex && null !== this.options.zIndex && (this._image.style.zIndex = this.options.zIndex)
        },
        _overlayOnError: function () {
            this.fire("error");
            var t = this.options.errorOverlayUrl;
            t && this._url !== t && (this._url = t, this._image.src = t)
        }
    }), vn = gn.extend({
        options: {autoplay: !0, loop: !0}, _initImage: function () {
            var t = "VIDEO" === this._url.tagName, i = this._image = t ? this._url : ht("video");
            if (pt(i, "gamap-image-layer"), this._zoomAnimated && pt(i, "gamap-zoom-animated"), i.onselectstart = a, i.onmousemove = a, i.onloadeddata = e(this.fire, this, "load"), t) {
                for (var n = i.getElementsByTagName("source"), o = [], s = 0; s < n.length; s++) o.push(n[s].src);
                this._url = n.length > 0 ? o : [i.src]
            } else {
                _i(this._url) || (this._url = [this._url]), i.autoplay = !!this.options.autoplay, i.loop = !!this.options.loop;
                for (var r = 0; r < this._url.length; r++) {
                    var h = ht("source");
                    h.src = this._url[r], i.appendChild(h)
                }
            }
        }
    }), yn = en.extend({
        options: {offset: [0, 7], className: "", pane: "popupPane"}, initialize: function (t, i) {
            u(this, t), this._source = i
        }, onAdd: function (t) {
            this._zoomAnimated = t._zoomAnimated, this._container || this._initLayout(), t._fadeAnimated && vt(this._container, 0), clearTimeout(this._removeTimeout), this.getPane().appendChild(this._container), this.update(), t._fadeAnimated && vt(this._container, 1), this.bringToFront()
        }, onRemove: function (t) {
            t._fadeAnimated ? (vt(this._container, 0), this._removeTimeout = setTimeout(e(lt, void 0, this._container), 200)) : lt(this._container)
        }, getLatLng: function () {
            return this._latlng
        }, setLatLng: function (t) {
            return this._latlng = A(t), this._map && (this._updatePosition(), this._adjustPan()), this
        }, getContent: function () {
            return this._content
        }, setContent: function (t) {
            return this._content = t, this.update(), this
        }, getElement: function () {
            return this._container
        }, update: function () {
            this._map && (this._container.style.visibility = "hidden", this._updateContent(), this._updateLayout(), this._updatePosition(), this._container.style.visibility = "", this._adjustPan())
        }, getEvents: function () {
            var t = {zoom: this._updatePosition, viewreset: this._updatePosition};
            return this._zoomAnimated && (t.zoomanim = this._animateZoom), t
        }, isOpen: function () {
            return !!this._map && this._map.hasLayer(this)
        }, bringToFront: function () {
            return this._map && ct(this._container), this
        }, bringToBack: function () {
            return this._map && dt(this._container), this
        }, _updateContent: function () {
            if (this._content) {
                var t = this._contentNode,
                    i = "function" == typeof this._content ? this._content(this._source || this) : this._content;
                if ("string" == typeof i) t.innerHTML = i; else {
                    for (; t.hasChildNodes();) t.removeChild(t.firstChild);
                    t.appendChild(i)
                }
                this.fire("contentupdate")
            }
        }, _updatePosition: function () {
            if (this._map) {
                var t = this._map.latLngToLayerPoint(this._latlng), i = w(this.options.offset), e = this._getAnchor();
                this._zoomAnimated ? Lt(this._container, t.add(e)) : i = i.add(t).add(e);
                var n = this._containerBottom = -i.y,
                    o = this._containerLeft = -Math.round(this._containerWidth / 2) + i.x;
                this._container.style.bottom = n + "px", this._container.style.left = o + "px"
            }
        }, _getAnchor: function () {
            return [0, 0]
        }
    }), xn = yn.extend({
        options: {
            maxWidth: 300,
            minWidth: 50,
            maxHeight: null,
            autoPan: !0,
            autoPanPaddingTopLeft: null,
            autoPanPaddingBottomRight: null,
            autoPanPadding: [5, 5],
            keepInView: !1,
            closeButton: !0,
            autoClose: !0,
            closeOnEscapeKey: !0,
            className: ""
        }, openOn: function (t) {
            return t.openPopup(this), this
        }, onAdd: function (t) {
            yn.prototype.onAdd.call(this, t), t.fire("popupopen", {popup: this}), this._source && (this._source.fire("popupopen", {popup: this}, !0), this._source instanceof ln || this._source.on("preclick", Y))
        }, onRemove: function (t) {
            yn.prototype.onRemove.call(this, t), t.fire("popupclose", {popup: this}), this._source && (this._source.fire("popupclose", {popup: this}, !0), this._source instanceof ln || this._source.off("preclick", Y))
        }, getEvents: function () {
            var t = yn.prototype.getEvents.call(this);
            return (void 0 !== this.options.closeOnClick ? this.options.closeOnClick : this._map.options.closePopupOnClick) && (t.preclick = this._close), this.options.keepInView && (t.moveend = this._adjustPan), t
        }, _close: function () {
            this._map && this._map.closePopup(this)
        }, _initLayout: function () {
            var t = "gamap-popup",
                i = this._container = ht("div", t + " " + (this.options.className || "") + " gamap-zoom-animated"),
                e = this._wrapper = ht("div", t + "-content-wrapper", i);
            if (this._contentNode = ht("div", t + "-content", e), K(e), J(this._contentNode), U(e, "contextmenu", Y), this._tipContainer = ht("div", t + "-tip-container", i), this._tip = ht("div", t + "-tip", this._tipContainer), this.options.closeButton) {
                var n = this._closeButton = ht("a", t + "-close-button", i);
                n.href = "#close", n.innerHTML = "&#215;", U(n, "click", this._onCloseButtonClick, this)
            }
        }, _updateLayout: function () {
            var t = this._contentNode, i = t.style;
            i.width = "", i.whiteSpace = "nowrap";
            var e = t.offsetWidth;
            e = Math.min(e, this.options.maxWidth), e = Math.max(e, this.options.minWidth), i.width = e + 1 + "px", i.whiteSpace = "", i.height = "";
            var n = t.offsetHeight, o = this.options.maxHeight;
            o && n > o ? (i.height = o + "px", pt(t, "gamap-popup-scrolled")) : mt(t, "gamap-popup-scrolled"), this._containerWidth = this._container.offsetWidth
        }, _animateZoom: function (t) {
            var i = this._map._latLngToNewLayerPoint(this._latlng, t.zoom, t.center), e = this._getAnchor();
            Lt(this._container, i.add(e))
        }, _adjustPan: function () {
            if (!(!this.options.autoPan || this._map._panAnim && this._map._panAnim._inProgress)) {
                var t = this._map, i = parseInt(rt(this._container, "marginBottom"), 10) || 0,
                    e = this._container.offsetHeight + i, n = this._containerWidth,
                    o = new x(this._containerLeft, -e - this._containerBottom);
                o._add(bt(this._container));
                var s = t.layerPointToContainerPoint(o), a = w(this.options.autoPanPadding),
                    r = w(this.options.autoPanPaddingTopLeft || a), h = w(this.options.autoPanPaddingBottomRight || a),
                    l = t.getSize(), u = 0, c = 0;
                s.x + n + h.x > l.x && (u = s.x + n - l.x + h.x), s.x - u - r.x < 0 && (u = s.x - r.x), s.y + e + h.y > l.y && (c = s.y + e - l.y + h.y), s.y - c - r.y < 0 && (c = s.y - r.y), (u || c) && t.fire("autopanstart").panBy([u, c])
            }
        }, _onCloseButtonClick: function (t) {
            this._close(), $(t)
        }, _getAnchor: function () {
            return w(this._source && this._source._getPopupAnchor ? this._source._getPopupAnchor() : [0, 0])
        }
    });
    Ie.mergeOptions({closePopupOnClick: !0}), Ie.include({
        openPopup: function (t, i, e) {
            return t instanceof xn || (t = new xn(e).setContent(t)), i && t.setLatLng(i), this.hasLayer(t) ? this : (this._popup && this._popup.options.autoClose && this.closePopup(), this._popup = t, this.addLayer(t))
        }, closePopup: function (t) {
            return t && t !== this._popup || (t = this._popup, this._popup = null), t && this.removeLayer(t), this
        }
    }), en.include({
        bindPopup: function (t, i) {
            return t instanceof xn ? (u(t, i), this._popup = t, t._source = this) : (this._popup && !i || (this._popup = new xn(i, this)), this._popup.setContent(t)), this._popupHandlersAdded || (this.on({
                click: this._openPopup,
                keypress: this._onKeyPress,
                remove: this.closePopup,
                move: this._movePopup
            }), this._popupHandlersAdded = !0), this
        }, unbindPopup: function () {
            return this._popup && (this.off({
                click: this._openPopup,
                keypress: this._onKeyPress,
                remove: this.closePopup,
                move: this._movePopup
            }), this._popupHandlersAdded = !1, this._popup = null), this
        }, openPopup: function (t, i) {
            if (t instanceof en || (i = t, t = this), t instanceof on) for (var e in this._layers) {
                t = this._layers[e];
                break
            }
            return i || (i = t.getCenter ? t.getCenter() : t.getLatLng()), this._popup && this._map && (this._popup._source = t, this._popup.update(), this._map.openPopup(this._popup, i)), this
        }, closePopup: function () {
            return this._popup && this._popup._close(), this
        }, togglePopup: function (t) {
            return this._popup && (this._popup._map ? this.closePopup() : this.openPopup(t)), this
        }, isPopupOpen: function () {
            return !!this._popup && this._popup.isOpen()
        }, setPopupContent: function (t) {
            return this._popup && this._popup.setContent(t), this
        }, getPopup: function () {
            return this._popup
        }, _openPopup: function (t) {
            var i = t.layer || t.target;
            this._popup && this._map && ($(t), i instanceof ln ? this.openPopup(t.layer || t.target, t.latlng) : this._map.hasLayer(this._popup) && this._popup._source === i ? this.closePopup() : this.openPopup(i, t.latlng))
        }, _movePopup: function (t) {
            this._popup.setLatLng(t.latlng)
        }, _onKeyPress: function (t) {
            13 === t.originalEvent.keyCode && this._openPopup(t)
        }
    });
    var wn = yn.extend({
        options: {
            pane: "tooltipPane",
            offset: [0, 0],
            direction: "auto",
            permanent: !1,
            sticky: !1,
            interactive: !1,
            opacity: .9
        }, onAdd: function (t) {
            yn.prototype.onAdd.call(this, t), this.setOpacity(this.options.opacity), t.fire("tooltipopen", {tooltip: this}), this._source && this._source.fire("tooltipopen", {tooltip: this}, !0)
        }, onRemove: function (t) {
            yn.prototype.onRemove.call(this, t), t.fire("tooltipclose", {tooltip: this}), this._source && this._source.fire("tooltipclose", {tooltip: this}, !0)
        }, getEvents: function () {
            var t = yn.prototype.getEvents.call(this);
            return ee && !this.options.permanent && (t.preclick = this._close), t
        }, _close: function () {
            this._map && this._map.closeTooltip(this)
        }, _initLayout: function () {
            var t = "gamap-tooltip " + (this.options.className || "") + " gamap-zoom-" + (this._zoomAnimated ? "animated" : "hide");
            this._contentNode = this._container = ht("div", t)
        }, _updateLayout: function () {
        }, _adjustPan: function () {
        }, _setPosition: function (t) {
            var i = this._map, e = this._container, n = i.latLngToContainerPoint(i.getCenter()),
                o = i.layerPointToContainerPoint(t), s = this.options.direction, a = e.offsetWidth, r = e.offsetHeight,
                h = w(this.options.offset), l = this._getAnchor();
            "top" === s ? t = t.add(w(-a / 2 + h.x, -r + h.y + l.y, !0)) : "bottom" === s ? t = t.subtract(w(a / 2 - h.x, -h.y, !0)) : "center" === s ? t = t.subtract(w(a / 2 + h.x, r / 2 - l.y + h.y, !0)) : "right" === s || "auto" === s && o.x < n.x ? (s = "right", t = t.add(w(h.x + l.x, l.y - r / 2 + h.y, !0))) : (s = "left", t = t.subtract(w(a + l.x - h.x, r / 2 - l.y - h.y, !0))), mt(e, "gamap-tooltip-right"), mt(e, "gamap-tooltip-left"), mt(e, "gamap-tooltip-top"), mt(e, "gamap-tooltip-bottom"), pt(e, "gamap-tooltip-" + s), Lt(e, t)
        }, _updatePosition: function () {
            var t = this._map.latLngToLayerPoint(this._latlng);
            this._setPosition(t)
        }, setOpacity: function (t) {
            this.options.opacity = t, this._container && vt(this._container, t)
        }, _animateZoom: function (t) {
            var i = this._map._latLngToNewLayerPoint(this._latlng, t.zoom, t.center);
            this._setPosition(i)
        }, _getAnchor: function () {
            return w(this._source && this._source._getTooltipAnchor && !this.options.sticky ? this._source._getTooltipAnchor() : [0, 0])
        }
    });
    Ie.include({
        openTooltip: function (t, i, e) {
            return t instanceof wn || (t = new wn(e).setContent(t)), i && t.setLatLng(i), this.hasLayer(t) ? this : this.addLayer(t)
        }, closeTooltip: function (t) {
            return t && this.removeLayer(t), this
        }
    }), en.include({
        bindTooltip: function (t, i) {
            return t instanceof wn ? (u(t, i), this._tooltip = t, t._source = this) : (this._tooltip && !i || (this._tooltip = new wn(i, this)), this._tooltip.setContent(t)), this._initTooltipInteractions(), this._tooltip.options.permanent && this._map && this._map.hasLayer(this) && this.openTooltip(), this
        }, unbindTooltip: function () {
            return this._tooltip && (this._initTooltipInteractions(!0), this.closeTooltip(), this._tooltip = null), this
        }, _initTooltipInteractions: function (t) {
            if (t || !this._tooltipHandlersAdded) {
                var i = t ? "off" : "on", e = {remove: this.closeTooltip, move: this._moveTooltip};
                this._tooltip.options.permanent ? e.add = this._openTooltip : (e.mouseover = this._openTooltip, e.mouseout = this.closeTooltip, this._tooltip.options.sticky && (e.mousemove = this._moveTooltip), ee && (e.click = this._openTooltip)), this[i](e), this._tooltipHandlersAdded = !t
            }
        }, openTooltip: function (t, i) {
            if (t instanceof en || (i = t, t = this), t instanceof on) for (var e in this._layers) {
                t = this._layers[e];
                break
            }
            return i || (i = t.getCenter ? t.getCenter() : t.getLatLng()), this._tooltip && this._map && (this._tooltip._source = t, this._tooltip.update(), this._map.openTooltip(this._tooltip, i), this._tooltip.options.interactive && this._tooltip._container && (pt(this._tooltip._container, "gamap-clickable"), this.addInteractiveTarget(this._tooltip._container))), this
        }, closeTooltip: function () {
            return this._tooltip && (this._tooltip._close(), this._tooltip.options.interactive && this._tooltip._container && (mt(this._tooltip._container, "gamap-clickable"), this.removeInteractiveTarget(this._tooltip._container))), this
        }, toggleTooltip: function (t) {
            return this._tooltip && (this._tooltip._map ? this.closeTooltip() : this.openTooltip(t)), this
        }, isTooltipOpen: function () {
            return this._tooltip.isOpen()
        }, setTooltipContent: function (t) {
            return this._tooltip && this._tooltip.setContent(t), this
        }, getTooltip: function () {
            return this._tooltip
        }, _openTooltip: function (t) {
            var i = t.layer || t.target;
            this._tooltip && this._map && this.openTooltip(i, this._tooltip.options.sticky ? t.latlng : void 0)
        }, _moveTooltip: function (t) {
            var i, e, n = t.latlng;
            this._tooltip.options.sticky && t.originalEvent && (i = this._map.mouseEventToContainerPoint(t.originalEvent), e = this._map.containerPointToLayerPoint(i), n = this._map.layerPointToLatLng(e)), this._tooltip.setLatLng(n)
        }
    });
    var Ln = sn.extend({
        options: {iconSize: [12, 12], html: !1, bgPos: null, className: "gamap-div-icon"},
        createIcon: function (t) {
            var i = t && "DIV" === t.tagName ? t : document.createElement("div"), e = this.options;
            if (i.innerHTML = !1 !== e.html ? e.html : "", e.bgPos) {
                var n = w(e.bgPos);
                i.style.backgroundPosition = -n.x + "px " + -n.y + "px"
            }
            return this._setIconStyles(i, "icon"), i
        },
        createShadow: function () {
            return null
        }
    });
    sn.Default = an;
    var bn = en.extend({
        options: {
            tileSize: 256,
            opacity: 1,
            updateWhenIdle: Ki,
            updateWhenZooming: !0,
            updateInterval: 200,
            zIndex: 1,
            bounds: null,
            minZoom: 0,
            maxZoom: void 0,
            maxNativeZoom: void 0,
            minNativeZoom: void 0,
            noWrap: !1,
            pane: "tilePane",
            className: "",
            keepBuffer: 2
        }, initialize: function (t) {
            u(this, t)
        }, onAdd: function () {
            this._initContainer(), this._levels = {}, this._tiles = {}, this._resetView(), this._update()
        }, beforeAdd: function (t) {
            t._addZoomLimit(this)
        }, onRemove: function (t) {
            this._removeAllTiles(), lt(this._container), t._removeZoomLimit(this), this._container = null, this._tileZoom = void 0
        }, bringToFront: function () {
            return this._map && (ct(this._container), this._setAutoZIndex(Math.max)), this
        }, bringToBack: function () {
            return this._map && (dt(this._container), this._setAutoZIndex(Math.min)), this
        }, getContainer: function () {
            return this._container
        }, setOpacity: function (t) {
            return this.options.opacity = t, this._updateOpacity(), this
        }, setZIndex: function (t) {
            return this.options.zIndex = t, this._updateZIndex(), this
        }, isLoading: function () {
            return this._loading
        }, redraw: function () {
            return this._map && (this._removeAllTiles(), this._update()), this
        }, getEvents: function () {
            var t = {
                viewprereset: this._invalidateAll,
                viewreset: this._resetView,
                zoom: this._resetView,
                moveend: this._onMoveEnd
            };
            return this.options.updateWhenIdle || (this._onMove || (this._onMove = o(this._onMoveEnd, this.options.updateInterval, this)), t.move = this._onMove), this._zoomAnimated && (t.zoomanim = this._animateZoom), t
        }, createTile: function () {
            return document.createElement("div")
        }, getTileSize: function () {
            var t = this.options.tileSize;
            return t instanceof x ? t : new x(t, t)
        }, _updateZIndex: function () {
            this._container && void 0 !== this.options.zIndex && null !== this.options.zIndex && (this._container.style.zIndex = this.options.zIndex)
        }, _setAutoZIndex: function (t) {
            for (var i, e = this.getPane().children, n = -t(-1 / 0, 1 / 0), o = 0, s = e.length; o < s; o++) i = e[o].style.zIndex, e[o] !== this._container && i && (n = t(n, +i));
            isFinite(n) && (this.options.zIndex = n + t(-1, 1), this._updateZIndex())
        }, _updateOpacity: function () {
            if (this._map && !Ii) {
                vt(this._container, this.options.opacity);
                var t = +new Date, i = !1, e = !1;
                for (var n in this._tiles) {
                    var o = this._tiles[n];
                    if (o.current && o.loaded) {
                        var s = Math.min(1, (t - o.loaded) / 200);
                        vt(o.el, s), s < 1 ? i = !0 : (o.active ? e = !0 : this._onOpaqueTile(o), o.active = !0)
                    }
                }
                e && !this._noPrune && this._pruneTiles(), i && (g(this._fadeFrame), this._fadeFrame = f(this._updateOpacity, this))
            }
        }, _onOpaqueTile: a, _initContainer: function () {
            this._container || (this._container = ht("div", "gamap-layer " + (this.options.className || "")), this._updateZIndex(), this.options.opacity < 1 && this._updateOpacity(), this.getPane().appendChild(this._container))
        }, _updateLevels: function () {
            var t = this._tileZoom, i = this.options.maxZoom;
            if (void 0 !== t) {
                for (var e in this._levels) this._levels[e].el.children.length || e === t ? (this._levels[e].el.style.zIndex = i - Math.abs(t - e), this._onUpdateLevel(e)) : (lt(this._levels[e].el), this._removeTilesAtZoom(e), this._onRemoveLevel(e), delete this._levels[e]);
                var n = this._levels[t], o = this._map;
                return n || ((n = this._levels[t] = {}).el = ht("div", "gamap-tile-container gamap-zoom-animated", this._container), n.el.style.zIndex = i, n.origin = o.project(o.unproject(o.getPixelOrigin()), t).round(), n.zoom = t, this._setZoomTransform(n, o.getCenter(), o.getZoom()), n.el.offsetWidth, this._onCreateLevel(n)), this._level = n, n
            }
        }, _onUpdateLevel: a, _onRemoveLevel: a, _onCreateLevel: a, _pruneTiles: function () {
            if (this._map) {
                var t, i, e = this._map.getZoom();
                if (e > this.options.maxZoom || e < this.options.minZoom) this._removeAllTiles(); else {
                    for (t in this._tiles) (i = this._tiles[t]).retain = i.current;
                    for (t in this._tiles) if ((i = this._tiles[t]).current && !i.active) {
                        var n = i.coords;
                        this._retainParent(n.x, n.y, n.z, n.z - 5) || this._retainChildren(n.x, n.y, n.z, n.z + 2)
                    }
                    for (t in this._tiles) this._tiles[t].retain || this._removeTile(t)
                }
            }
        }, _removeTilesAtZoom: function (t) {
            for (var i in this._tiles) this._tiles[i].coords.z === t && this._removeTile(i)
        }, _removeAllTiles: function () {
            for (var t in this._tiles) this._removeTile(t)
        }, _invalidateAll: function () {
            for (var t in this._levels) lt(this._levels[t].el), this._onRemoveLevel(t), delete this._levels[t];
            this._removeAllTiles(), this._tileZoom = void 0
        }, _retainParent: function (t, i, e, n) {
            var o = Math.floor(t / 2), s = Math.floor(i / 2), a = e - 1, r = new x(+o, +s);
            r.z = +a;
            var h = this._tileCoordsToKey(r), l = this._tiles[h];
            return l && l.active ? (l.retain = !0, !0) : (l && l.loaded && (l.retain = !0), a > n && this._retainParent(o, s, a, n))
        }, _retainChildren: function (t, i, e, n) {
            for (var o = 2 * t; o < 2 * t + 2; o++) for (var s = 2 * i; s < 2 * i + 2; s++) {
                var a = new x(o, s);
                a.z = e + 1;
                var r = this._tileCoordsToKey(a), h = this._tiles[r];
                h && h.active ? h.retain = !0 : (h && h.loaded && (h.retain = !0), e + 1 < n && this._retainChildren(o, s, e + 1, n))
            }
        }, _resetView: function (t) {
            var i = t && (t.pinch || t.flyTo);
            this._setView(this._map.getCenter(), this._map.getZoom(), i, i)
        }, _animateZoom: function (t) {
            this._setView(t.center, t.zoom, !0, t.noUpdate)
        }, _clampZoom: function (t) {
            var i = this.options;
            return void 0 !== i.minNativeZoom && t < i.minNativeZoom ? i.minNativeZoom : void 0 !== i.maxNativeZoom && i.maxNativeZoom < t ? i.maxNativeZoom : t
        }, _setView: function (t, i, e, n) {
            var o = this._clampZoom(Math.round(i));
            (void 0 !== this.options.maxZoom && o > this.options.maxZoom || void 0 !== this.options.minZoom && o < this.options.minZoom) && (o = void 0);
            var s = this.options.updateWhenZooming && o !== this._tileZoom;
            n && !s || (this._tileZoom = o, this._abortLoading && this._abortLoading(), this._updateLevels(), this._resetGrid(), void 0 !== o && this._update(t), e || this._pruneTiles(), this._noPrune = !!e), this._setZoomTransforms(t, i)
        }, _setZoomTransforms: function (t, i) {
            for (var e in this._levels) this._setZoomTransform(this._levels[e], t, i)
        }, _setZoomTransform: function (t, i, e) {
            var n = this._map.getZoomScale(e, t.zoom),
                o = t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(i, e)).round();
            Ji ? wt(t.el, o, n) : Lt(t.el, o)
        }, _resetGrid: function () {
            var t = this._map, i = t.options.crs, e = this._tileSize = this.getTileSize(), n = this._tileZoom,
                o = this._map.getPixelWorldBounds(this._tileZoom);
            o && (this._globalTileRange = this._pxBoundsToTileRange(o)), this._wrapX = i.wrapLng && !this.options.noWrap && [Math.floor(t.project([0, i.wrapLng[0]], n).x / e.x), Math.ceil(t.project([0, i.wrapLng[1]], n).x / e.y)], this._wrapY = i.wrapLat && !this.options.noWrap && [Math.floor(t.project([i.wrapLat[0], 0], n).y / e.x), Math.ceil(t.project([i.wrapLat[1], 0], n).y / e.y)]
        }, _onMoveEnd: function () {
            this._map && !this._map._animatingZoom && this._update()
        }, _getTiledPixelBounds: function (t) {
            var i = this._map, e = i._animatingZoom ? Math.max(i._animateToZoom, i.getZoom()) : i.getZoom(),
                n = i.getZoomScale(e, this._tileZoom), o = i.project(t, this._tileZoom).floor(),
                s = i.getSize().divideBy(2 * n);
            return new b(o.subtract(s), o.add(s))
        }, _update: function (t) {
            var i = this._map;
            if (i) {
                var e = this._clampZoom(i.getZoom());
                if (void 0 === t && (t = i.getCenter()), void 0 !== this._tileZoom) {
                    var n = this._getTiledPixelBounds(t), o = this._pxBoundsToTileRange(n), s = o.getCenter(), a = [],
                        r = this.options.keepBuffer,
                        h = new b(o.getBottomLeft().subtract([r, -r]), o.getTopRight().add([r, -r]));
                    if (!(isFinite(o.min.x) && isFinite(o.min.y) && isFinite(o.max.x) && isFinite(o.max.y))) throw new Error("Attempted to load an infinite number of tiles");
                    for (var l in this._tiles) {
                        var u = this._tiles[l].coords;
                        u.z === this._tileZoom && h.contains(new x(u.x, u.y)) || (this._tiles[l].current = !1)
                    }
                    if (Math.abs(e - this._tileZoom) > 1) this._setView(t, e); else {
                        for (var c = o.min.y; c <= o.max.y; c++) for (var d = o.min.x; d <= o.max.x; d++) {
                            var _ = new x(d, c);
                            if (_.z = this._tileZoom, this._isValidTile(_)) {
                                var p = this._tiles[this._tileCoordsToKey(_)];
                                p ? p.current = !0 : a.push(_)
                            }
                        }
                        if (a.sort(function (t, i) {
                            return t.distanceTo(s) - i.distanceTo(s)
                        }), 0 !== a.length) {
                            this._loading || (this._loading = !0, this.fire("loading"));
                            var m = document.createDocumentFragment();
                            for (d = 0; d < a.length; d++) this._addTile(a[d], m);
                            this._level.el.appendChild(m)
                        }
                    }
                }
            }
        }, _isValidTile: function (t) {
            var i = this._map.options.crs;
            if (!i.infinite) {
                var e = this._globalTileRange;
                if (!i.wrapLng && (t.x < e.min.x || t.x > e.max.x) || !i.wrapLat && (t.y < e.min.y || t.y > e.max.y)) return !1
            }
            if (!this.options.bounds) return !0;
            var n = this._tileCoordsToBounds(t);
            return M(this.options.bounds).overlaps(n)
        }, _keyToBounds: function (t) {
            return this._tileCoordsToBounds(this._keyToTileCoords(t))
        }, _tileCoordsToNwSe: function (t) {
            var i = this._map, e = this.getTileSize(), n = t.scaleBy(e), o = n.add(e);
            return [i.unproject(n, t.z), i.unproject(o, t.z)]
        }, _tileCoordsToBounds: function (t) {
            var i = this._tileCoordsToNwSe(t), e = new T(i[0], i[1]);
            return this.options.noWrap || (e = this._map.wrapLatLngBounds(e)), e
        }, _tileCoordsToKey: function (t) {
            return t.x + ":" + t.y + ":" + t.z
        }, _keyToTileCoords: function (t) {
            var i = t.split(":"), e = new x(+i[0], +i[1]);
            return e.z = +i[2], e
        }, _removeTile: function (t) {
            var i = this._tiles[t];
            i && (Di || i.el.setAttribute("src", pi), lt(i.el), delete this._tiles[t], this.fire("tileunload", {
                tile: i.el,
                coords: this._keyToTileCoords(t)
            }))
        }, _initTile: function (t) {
            pt(t, "gamap-tile");
            var i = this.getTileSize();
            t.style.width = i.x + "px", t.style.height = i.y + "px", t.onselectstart = a, t.onmousemove = a, Ii && this.options.opacity < 1 && vt(t, this.options.opacity), Oi && !Ri && (t.style.WebkitBackfaceVisibility = "hidden")
        }, _addTile: function (t, i) {
            var n = this._getTilePos(t), o = this._tileCoordsToKey(t),
                s = this.createTile(this._wrapCoords(t), e(this._tileReady, this, t));
            this._initTile(s), this.createTile.length < 2 && f(e(this._tileReady, this, t, null, s)), Lt(s, n), this._tiles[o] = {
                el: s,
                coords: t,
                current: !0
            }, i.appendChild(s), this.fire("tileloadstart", {tile: s, coords: t})
        }, _tileReady: function (t, i, n) {
            if (this._map) {
                i && this.fire("tileerror", {error: i, tile: n, coords: t});
                var o = this._tileCoordsToKey(t);
                (n = this._tiles[o]) && (n.loaded = +new Date, this._map._fadeAnimated ? (vt(n.el, 0), g(this._fadeFrame), this._fadeFrame = f(this._updateOpacity, this)) : (n.active = !0, this._pruneTiles()), i || (pt(n.el, "gamap-tile-loaded"), this.fire("tileload", {
                    tile: n.el,
                    coords: t
                })), this._noTilesToLoad() && (this._loading = !1, this.fire("load"), Ii || !this._map._fadeAnimated ? f(this._pruneTiles, this) : setTimeout(e(this._pruneTiles, this), 250)))
            }
        }, _getTilePos: function (t) {
            return t.scaleBy(this.getTileSize()).subtract(this._level.origin)
        }, _wrapCoords: function (t) {
            var i = new x(this._wrapX ? s(t.x, this._wrapX) : t.x, this._wrapY ? s(t.y, this._wrapY) : t.y);
            return i.z = t.z, i
        }, _pxBoundsToTileRange: function (t) {
            var i = this.getTileSize();
            return new b(t.min.unscaleBy(i).floor(), t.max.unscaleBy(i).ceil().subtract([1, 1]))
        }, _noTilesToLoad: function () {
            for (var t in this._tiles) if (!this._tiles[t].loaded) return !1;
            return !0
        }
    }), Pn = bn.extend({
        options: {
            minZoom: 0,
            maxZoom: 18,
            subdomains: "abc",
            errorTileUrl: "",
            zoomOffset: 0,
            tms: !1,
            zoomReverse: !1,
            detectRetina: !1,
            crossOrigin: !1
        }, initialize: function (t, i) {
            this._url = t, (i = u(this, i)).detectRetina && se && i.maxZoom > 0 && (i.tileSize = Math.floor(i.tileSize / 2), i.zoomReverse ? (i.zoomOffset--, i.minZoom++) : (i.zoomOffset++, i.maxZoom--), i.minZoom = Math.max(0, i.minZoom)), "string" == typeof i.subdomains && (i.subdomains = i.subdomains.split("")), Oi || this.on("tileunload", this._onTileRemove)
        }, setUrl: function (t, i) {
            return this._url = t, i || this.redraw(), this
        }, createTile: function (t, i) {
            var n = document.createElement("img");
            return U(n, "load", e(this._tileOnLoad, this, i, n)), U(n, "error", e(this._tileOnError, this, i, n)), this.options.crossOrigin && (n.crossOrigin = ""), n.alt = "", n.setAttribute("role", "presentation"), n.src = this.getTileUrl(t), n
        }, getTileUrl: function (t) {
            var e = {r: se ? "@2x" : "", s: this._getSubdomain(t), x: t.x, y: t.y, z: this._getZoomForUrl()};
            if (this._map && !this._map.options.crs.infinite) {
                var n = this._globalTileRange.max.y - t.y;
                this.options.tms && (e.y = n), e["-y"] = n
            }
            return d(this._url, i(e, this.options))
        }, _tileOnLoad: function (t, i) {
            Ii ? setTimeout(e(t, this, null, i), 0) : t(null, i)
        }, _tileOnError: function (t, i, e) {
            var n = this.options.errorTileUrl;
            n && i.getAttribute("src") !== n && (i.src = n), t(e, i)
        }, _onTileRemove: function (t) {
            t.tile.onload = null
        }, _getZoomForUrl: function () {
            var t = this._tileZoom, i = this.options.maxZoom, e = this.options.zoomReverse, n = this.options.zoomOffset;
            return e && (t = i - t), t + n
        }, _getSubdomain: function (t) {
            var i = Math.abs(t.x + t.y) % this.options.subdomains.length;
            return this.options.subdomains[i]
        }, _abortLoading: function () {
            var t, i;
            for (t in this._tiles) this._tiles[t].coords.z !== this._tileZoom && ((i = this._tiles[t].el).onload = a, i.onerror = a, i.complete || (i.src = pi, lt(i), delete this._tiles[t]))
        }
    }), Tn = Pn.extend({
        defaultWmsParams: {
            service: "WMS",
            request: "GetMap",
            layers: "",
            styles: "",
            format: "image/jpeg",
            transparent: !1,
            version: "1.1.1"
        }, options: {crs: null, uppercase: !1}, initialize: function (t, e) {
            this._url = t;
            var n = i({}, this.defaultWmsParams);
            for (var o in e) o in this.options || (n[o] = e[o]);
            var s = (e = u(this, e)).detectRetina && se ? 2 : 1, a = this.getTileSize();
            n.width = a.x * s, n.height = a.y * s, this.wmsParams = n
        }, onAdd: function (t) {
            this._crs = this.options.crs || t.options.crs, this._wmsVersion = parseFloat(this.wmsParams.version);
            var i = this._wmsVersion >= 1.3 ? "crs" : "srs";
            this.wmsParams[i] = this._crs.code, Pn.prototype.onAdd.call(this, t)
        }, getTileUrl: function (t) {
            var i = this._tileCoordsToNwSe(t), e = this._crs, n = P(e.project(i[0]), e.project(i[1])), o = n.min,
                s = n.max,
                a = (this._wmsVersion >= 1.3 && this._crs === $e ? [o.y, o.x, s.y, s.x] : [o.x, o.y, s.x, s.y]).join(","),
                r = GAMap.TileLayer.prototype.getTileUrl.call(this, t);
            return r + c(this.wmsParams, r, this.options.uppercase) + (this.options.uppercase ? "&BBOX=" : "&bbox=") + a
        }, setParams: function (t, e) {
            return i(this.wmsParams, t), e || this.redraw(), this
        }
    });
    Pn.WMS = Tn, Yt.wms = function (t, i) {
        return new Tn(t, i)
    };
    var Mn = en.extend({
        options: {padding: .1, tolerance: 0}, initialize: function (t) {
            u(this, t), n(this), this._layers = this._layers || {}
        }, onAdd: function () {
            this._container || (this._initContainer(), this._zoomAnimated && pt(this._container, "gamap-zoom-animated")), this.getPane().appendChild(this._container), this._update(), this.on("update", this._updatePaths, this)
        }, onRemove: function () {
            this.off("update", this._updatePaths, this), this._destroyContainer()
        }, getEvents: function () {
            var t = {viewreset: this._reset, zoom: this._onZoom, moveend: this._update, zoomend: this._onZoomEnd};
            return this._zoomAnimated && (t.zoomanim = this._onAnimZoom), t
        }, _onAnimZoom: function (t) {
            this._updateTransform(t.center, t.zoom)
        }, _onZoom: function () {
            this._updateTransform(this._map.getCenter(), this._map.getZoom())
        }, _updateTransform: function (t, i) {
            var e = this._map.getZoomScale(i, this._zoom), n = bt(this._container),
                o = this._map.getSize().multiplyBy(.5 + this.options.padding), s = this._map.project(this._center, i),
                a = this._map.project(t, i).subtract(s), r = o.multiplyBy(-e).add(n).add(o).subtract(a);
            Ji ? wt(this._container, r, e) : Lt(this._container, r)
        }, _reset: function () {
            this._update(), this._updateTransform(this._center, this._zoom);
            for (var t in this._layers) this._layers[t]._reset()
        }, _onZoomEnd: function () {
            for (var t in this._layers) this._layers[t]._project()
        }, _updatePaths: function () {
            for (var t in this._layers) this._layers[t]._update()
        }, _update: function () {
            var t = this.options.padding, i = this._map.getSize(),
                e = this._map.containerPointToLayerPoint(i.multiplyBy(-t)).round();
            this._bounds = new b(e, e.add(i.multiplyBy(1 + 2 * t)).round()), this._center = this._map.getCenter(), this._zoom = this._map.getZoom()
        }
    }), Cn = Mn.extend({
        getEvents: function () {
            var t = Mn.prototype.getEvents.call(this);
            return t.viewprereset = this._onViewPreReset, t
        }, _onViewPreReset: function () {
            this._postponeUpdatePaths = !0
        }, onAdd: function () {
            Mn.prototype.onAdd.call(this), this._draw()
        }, _initContainer: function () {
            var t = this._container = document.createElement("canvas");
            U(t, "mousemove", o(this._onMouseMove, 32, this), this), U(t, "click dblclick mousedown mouseup contextmenu", this._onClick, this), U(t, "mouseout", this._handleMouseOut, this), this._ctx = t.getContext("2d")
        }, _destroyContainer: function () {
            delete this._ctx, lt(this._container), V(this._container), delete this._container
        }, _updatePaths: function () {
            if (!this._postponeUpdatePaths) {
                this._redrawBounds = null;
                for (var t in this._layers) this._layers[t]._update();
                this._redraw()
            }
        }, _update: function () {
            if (!this._map._animatingZoom || !this._bounds) {
                this._drawnLayers = {}, Mn.prototype._update.call(this);
                var t = this._bounds, i = this._container, e = t.getSize(), n = se ? 2 : 1;
                Lt(i, t.min), i.width = n * e.x, i.height = n * e.y, i.style.width = e.x + "px", i.style.height = e.y + "px", se && this._ctx.scale(2, 2), this._ctx.translate(-t.min.x, -t.min.y), this.fire("update")
            }
        }, _reset: function () {
            Mn.prototype._reset.call(this), this._postponeUpdatePaths && (this._postponeUpdatePaths = !1, this._updatePaths())
        }, _initPath: function (t) {
            this._updateDashArray(t), this._layers[n(t)] = t;
            var i = t._order = {layer: t, prev: this._drawLast, next: null};
            this._drawLast && (this._drawLast.next = i), this._drawLast = i, this._drawFirst = this._drawFirst || this._drawLast
        }, _addPath: function (t) {
            this._requestRedraw(t)
        }, _removePath: function (t) {
            var i = t._order, e = i.next, n = i.prev;
            e ? e.prev = n : this._drawLast = n, n ? n.next = e : this._drawFirst = e, delete t._order, delete this._layers[GAMap.stamp(t)], this._requestRedraw(t)
        }, _updatePath: function (t) {
            this._extendRedrawBounds(t), t._project(), t._update(), this._requestRedraw(t)
        }, _updateStyle: function (t) {
            this._updateDashArray(t), this._requestRedraw(t)
        }, _updateDashArray: function (t) {
            if (t.options.dashArray) {
                var i, e = t.options.dashArray.split(","), n = [];
                for (i = 0; i < e.length; i++) n.push(Number(e[i]));
                t.options._dashArray = n
            }
        }, _requestRedraw: function (t) {
            this._map && (this._extendRedrawBounds(t), this._redrawRequest = this._redrawRequest || f(this._redraw, this))
        }, _extendRedrawBounds: function (t) {
            if (t._pxBounds) {
                var i = (t.options.weight || 0) + 1;
                this._redrawBounds = this._redrawBounds || new b, this._redrawBounds.extend(t._pxBounds.min.subtract([i, i])), this._redrawBounds.extend(t._pxBounds.max.add([i, i]))
            }
        }, _redraw: function () {
            this._redrawRequest = null, this._redrawBounds && (this._redrawBounds.min._floor(), this._redrawBounds.max._ceil()), this._clear(), this._draw(), this._redrawBounds = null
        }, _clear: function () {
            var t = this._redrawBounds;
            if (t) {
                var i = t.getSize();
                this._ctx.clearRect(t.min.x, t.min.y, i.x, i.y)
            } else this._ctx.clearRect(0, 0, this._container.width, this._container.height)
        }, _draw: function () {
            var t, i = this._redrawBounds;
            if (this._ctx.save(), i) {
                var e = i.getSize();
                this._ctx.beginPath(), this._ctx.rect(i.min.x, i.min.y, e.x, e.y), this._ctx.clip()
            }
            this._drawing = !0;
            for (var n = this._drawFirst; n; n = n.next) t = n.layer, (!i || t._pxBounds && t._pxBounds.intersects(i)) && t._updatePath();
            this._drawing = !1, this._ctx.restore()
        }, _updatePoly: function (t, i) {
            if (this._drawing) {
                var e, n, o, s, a = t._parts, r = a.length, h = this._ctx;
                if (r) {
                    for (this._drawnLayers[t._gmap_id] = t, h.beginPath(), e = 0; e < r; e++) {
                        for (n = 0, o = a[e].length; n < o; n++) s = a[e][n], h[n ? "lineTo" : "moveTo"](s.x, s.y);
                        i && h.closePath()
                    }
                    this._fillStroke(h, t)
                }
            }
        }, _updateCircle: function (t) {
            if (this._drawing && !t._empty()) {
                var i = t._point, e = this._ctx, n = Math.max(Math.round(t._radius), 1),
                    o = (Math.max(Math.round(t._radiusY), 1) || n) / n;
                this._drawnLayers[t._gmap_id] = t, 1 !== o && (e.save(), e.scale(1, o)), e.beginPath(), e.arc(i.x, i.y / o, n, 0, 2 * Math.PI, !1), 1 !== o && e.restore(), this._fillStroke(e, t)
            }
        }, _fillStroke: function (t, i) {
            var e = i.options;
            e.fill && (t.globalAlpha = e.fillOpacity, t.fillStyle = e.fillColor || e.color, t.fill(e.fillRule || "evenodd")), e.stroke && 0 !== e.weight && (t.setLineDash && t.setLineDash(i.options && i.options._dashArray || []), t.globalAlpha = e.opacity, t.lineWidth = e.weight, t.strokeStyle = e.color, t.lineCap = e.lineCap, t.lineJoin = e.lineJoin, t.stroke())
        }, _onClick: function (t) {
            for (var i, e, n = this._map.mouseEventToLayerPoint(t), o = this._drawFirst; o; o = o.next) (i = o.layer).options.interactive && i._containsPoint(n) && !this._map._draggableMoved(i) && (e = i);
            e && (et(t), this._fireEvent([e], t))
        }, _onMouseMove: function (t) {
            if (this._map && !this._map.dragging.moving() && !this._map._animatingZoom) {
                var i = this._map.mouseEventToLayerPoint(t);
                this._handleMouseHover(t, i)
            }
        }, _handleMouseOut: function (t) {
            var i = this._hoveredLayer;
            i && (mt(this._container, "gamap-interactive"), this._fireEvent([i], t, "mouseout"), this._hoveredLayer = null)
        }, _handleMouseHover: function (t, i) {
            for (var e, n, o = this._drawFirst; o; o = o.next) (e = o.layer).options.interactive && e._containsPoint(i) && (n = e);
            n !== this._hoveredLayer && (this._handleMouseOut(t), n && (pt(this._container, "gamap-interactive"), this._fireEvent([n], t, "mouseover"), this._hoveredLayer = n)), this._hoveredLayer && this._fireEvent([this._hoveredLayer], t)
        }, _fireEvent: function (t, i, e) {
            this._map._fireDOMEvent(i, e || i.type, t)
        }, _bringToFront: function (t) {
            var i = t._order, e = i.next, n = i.prev;
            e && (e.prev = n, n ? n.next = e : e && (this._drawFirst = e), i.prev = this._drawLast, this._drawLast.next = i, i.next = null, this._drawLast = i, this._requestRedraw(t))
        }, _bringToBack: function (t) {
            var i = t._order, e = i.next, n = i.prev;
            n && (n.next = e, e ? e.prev = n : n && (this._drawLast = n), i.prev = null, i.next = this._drawFirst, this._drawFirst.prev = i, this._drawFirst = i, this._requestRedraw(t))
        }
    }), An = function () {
        try {
            return document.namespaces.add("lvml", "urn:schemas-microsoft-com:vml"), function (t) {
                return document.createElement("<lvml:" + t + ' class="lvml">')
            }
        } catch (t) {
            return function (t) {
                return document.createElement("<" + t + ' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')
            }
        }
    }(), zn = {
        _initContainer: function () {
            this._container = ht("div", "gamap-vml-container")
        }, _update: function () {
            this._map._animatingZoom || (Mn.prototype._update.call(this), this.fire("update"))
        }, _initPath: function (t) {
            var i = t._container = An("shape");
            pt(i, "gamap-vml-shape " + (this.options.className || "")), i.coordsize = "1 1", t._path = An("path"), i.appendChild(t._path), this._updateStyle(t), this._layers[n(t)] = t
        }, _addPath: function (t) {
            var i = t._container;
            this._container.appendChild(i), t.options.interactive && t.addInteractiveTarget(i)
        }, _removePath: function (t) {
            var i = t._container;
            lt(i), t.removeInteractiveTarget(i), delete this._layers[n(t)]
        }, _updateStyle: function (t) {
            var i = t._stroke, e = t._fill, n = t.options, o = t._container;
            o.stroked = !!n.stroke, o.filled = !!n.fill, n.stroke ? (i || (i = t._stroke = An("stroke")), o.appendChild(i), i.weight = n.weight + "px", i.color = n.color, i.opacity = n.opacity, n.dashArray ? i.dashStyle = _i(n.dashArray) ? n.dashArray.join(" ") : n.dashArray.replace(/( *, *)/g, " ") : i.dashStyle = "", i.endcap = n.lineCap.replace("butt", "flat"), i.joinstyle = n.lineJoin) : i && (o.removeChild(i), t._stroke = null), n.fill ? (e || (e = t._fill = An("fill")), o.appendChild(e), e.color = n.fillColor || n.color, e.opacity = n.fillOpacity) : e && (o.removeChild(e), t._fill = null)
        }, _updateCircle: function (t) {
            var i = t._point.round(), e = Math.round(t._radius), n = Math.round(t._radiusY || e);
            this._setPath(t, t._empty() ? "M0 0" : "AL " + i.x + "," + i.y + " " + e + "," + n + " 0,23592600")
        }, _setPath: function (t, i) {
            t._path.v = i
        }, _bringToFront: function (t) {
            ct(t._container)
        }, _bringToBack: function (t) {
            dt(t._container)
        }
    }, Sn = he ? An : k, kn = Mn.extend({
        getEvents: function () {
            var t = Mn.prototype.getEvents.call(this);
            return t.zoomstart = this._onZoomStart, t
        }, _initContainer: function () {
            this._container = Sn("svg"), this._container.setAttribute("pointer-events", "none"), this._rootGroup = Sn("g"), this._container.appendChild(this._rootGroup)
        }, _destroyContainer: function () {
            lt(this._container), V(this._container), delete this._container, delete this._rootGroup, delete this._svgSize
        }, _onZoomStart: function () {
            this._update()
        }, _update: function () {
            if (!this._map._animatingZoom || !this._bounds) {
                Mn.prototype._update.call(this);
                var t = this._bounds, i = t.getSize(), e = this._container;
                this._svgSize && this._svgSize.equals(i) || (this._svgSize = i, e.setAttribute("width", i.x), e.setAttribute("height", i.y)), Lt(e, t.min), e.setAttribute("viewBox", [t.min.x, t.min.y, i.x, i.y].join(" ")), this.fire("update")
            }
        }, _initPath: function (t) {
            var i = t._path = Sn("path");
            t.options.className && pt(i, t.options.className), t.options.interactive && pt(i, "gamap-interactive"), this._updateStyle(t), this._layers[n(t)] = t
        }, _addPath: function (t) {
            this._rootGroup || this._initContainer(), this._rootGroup.appendChild(t._path), t.addInteractiveTarget(t._path)
        }, _removePath: function (t) {
            lt(t._path), t.removeInteractiveTarget(t._path), delete this._layers[n(t)]
        }, _updatePath: function (t) {
            t._project(), t._update()
        }, _updateStyle: function (t) {
            var i = t._path, e = t.options;
            i && (e.stroke ? (i.setAttribute("stroke", e.color), i.setAttribute("stroke-opacity", e.opacity), i.setAttribute("stroke-width", e.weight), i.setAttribute("stroke-linecap", e.lineCap), i.setAttribute("stroke-linejoin", e.lineJoin), e.dashArray ? i.setAttribute("stroke-dasharray", e.dashArray) : i.removeAttribute("stroke-dasharray"), e.dashOffset ? i.setAttribute("stroke-dashoffset", e.dashOffset) : i.removeAttribute("stroke-dashoffset")) : i.setAttribute("stroke", "none"), e.fill ? (i.setAttribute("fill", e.fillColor || e.color), i.setAttribute("fill-opacity", e.fillOpacity), i.setAttribute("fill-rule", e.fillRule || "evenodd")) : i.setAttribute("fill", "none"))
        }, _updatePoly: function (t, i) {
            this._setPath(t, E(t._parts, i))
        }, _updateCircle: function (t) {
            var i = t._point, e = Math.max(Math.round(t._radius), 1),
                n = "a" + e + "," + (Math.max(Math.round(t._radiusY), 1) || e) + " 0 1,0 ",
                o = t._empty() ? "M0 0" : "M" + (i.x - e) + "," + i.y + n + 2 * e + ",0 " + n + 2 * -e + ",0 ";
            this._setPath(t, o)
        }, _setPath: function (t, i) {
            t._path.setAttribute("d", i)
        }, _bringToFront: function (t) {
            ct(t._path)
        }, _bringToBack: function (t) {
            dt(t._path)
        }
    });
    he && kn.include(zn), Ie.include({
        getRenderer: function (t) {
            var i = t.options.renderer || this._getPaneRenderer(t.options.pane) || this.options.renderer || this._renderer;
            return i || (i = this._renderer = this.options.preferCanvas && Jt() || Kt()), this.hasLayer(i) || this.addLayer(i), i
        }, _getPaneRenderer: function (t) {
            if ("overlayPane" === t || void 0 === t) return !1;
            var i = this._paneRenderers[t];
            return void 0 === i && (i = kn && Kt({pane: t}) || Cn && Jt({pane: t}), this._paneRenderers[t] = i), i
        }
    });
    var En = _n.extend({
        initialize: function (t, i) {
            _n.prototype.initialize.call(this, this._boundsToLatLngs(t), i)
        }, setBounds: function (t) {
            return this.setLatLngs(this._boundsToLatLngs(t))
        }, _boundsToLatLngs: function (t) {
            return t = M(t), [t.getSouthWest(), t.getNorthWest(), t.getNorthEast(), t.getSouthEast()]
        }
    });
    kn.create = Sn, kn.pointsToPath = E, pn.geometryToLayer = Ft, pn.coordsToLatLng = Ht, pn.coordsToLatLngs = Wt, pn.latLngToCoords = Gt, pn.latLngsToCoords = Ut, pn.getFeature = Vt, pn.asFeature = qt, Ie.mergeOptions({boxZoom: !0});
    var In = Ne.extend({
        initialize: function (t) {
            this._map = t, this._container = t._container, this._pane = t._panes.overlayPane, this._resetStateTimeout = 0, t.on("unload", this._destroy, this)
        }, addHooks: function () {
            U(this._container, "mousedown", this._onMouseDown, this)
        }, removeHooks: function () {
            V(this._container, "mousedown", this._onMouseDown, this)
        }, moved: function () {
            return this._moved
        }, _destroy: function () {
            lt(this._pane), delete this._pane
        }, _resetState: function () {
            this._resetStateTimeout = 0, this._moved = !1
        }, _clearDeferredResetState: function () {
            0 !== this._resetStateTimeout && (clearTimeout(this._resetStateTimeout), this._resetStateTimeout = 0)
        }, _onMouseDown: function (t) {
            if (!t.shiftKey || 1 !== t.which && 1 !== t.button) return !1;
            this._clearDeferredResetState(), this._resetState(), Mi(), Pt(), this._startPoint = this._map.mouseEventToContainerPoint(t), U(document, {
                contextmenu: $,
                mousemove: this._onMouseMove,
                mouseup: this._onMouseUp,
                keydown: this._onKeyDown
            }, this)
        }, _onMouseMove: function (t) {
            this._moved || (this._moved = !0, this._box = ht("div", "gamap-zoom-box", this._container), pt(this._container, "gamap-crosshair"), this._map.fire("boxzoomstart")), this._point = this._map.mouseEventToContainerPoint(t);
            var i = new b(this._point, this._startPoint), e = i.getSize();
            Lt(this._box, i.min), this._box.style.width = e.x + "px", this._box.style.height = e.y + "px"
        }, _finish: function () {
            this._moved && (lt(this._box), mt(this._container, "gamap-crosshair")), Ci(), Tt(), V(document, {
                contextmenu: $,
                mousemove: this._onMouseMove,
                mouseup: this._onMouseUp,
                keydown: this._onKeyDown
            }, this)
        }, _onMouseUp: function (t) {
            if ((1 === t.which || 1 === t.button) && (this._finish(), this._moved)) {
                this._clearDeferredResetState(), this._resetStateTimeout = setTimeout(e(this._resetState, this), 0);
                var i = new T(this._map.containerPointToLatLng(this._startPoint), this._map.containerPointToLatLng(this._point));
                this._map.fitBounds(i).fire("boxzoomend", {boxZoomBounds: i})
            }
        }, _onKeyDown: function (t) {
            27 === t.keyCode && this._finish()
        }
    });
    Ie.addInitHook("addHandler", "boxZoom", In), Ie.mergeOptions({doubleClickZoom: !0});
    var Bn = Ne.extend({
        addHooks: function () {
            this._map.on("dblclick", this._onDoubleClick, this)
        }, removeHooks: function () {
            this._map.off("dblclick", this._onDoubleClick, this)
        }, _onDoubleClick: function (t) {
            var i = this._map, e = i.getZoom(), n = i.options.zoomDelta, o = t.originalEvent.shiftKey ? e - n : e + n;
            "center" === i.options.doubleClickZoom ? i.setZoom(o) : i.setZoomAround(t.containerPoint, o)
        }
    });
    Ie.addInitHook("addHandler", "doubleClickZoom", Bn), Ie.mergeOptions({
        dragging: !0,
        inertia: !Ri,
        inertiaDeceleration: 3400,
        inertiaMaxSpeed: 1 / 0,
        easeLinearity: .2,
        worldCopyJump: !1,
        maxBoundsViscosity: 0
    });
    var Zn = Ne.extend({
        addHooks: function () {
            if (!this._draggable) {
                var t = this._map;
                this._draggable = new Ve(t._mapPane, t._container), this._draggable.on({
                    dragstart: this._onDragStart,
                    drag: this._onDrag,
                    dragend: this._onDragEnd
                }, this), this._draggable.on("predrag", this._onPreDragLimit, this), t.options.worldCopyJump && (this._draggable.on("predrag", this._onPreDragWrap, this), t.on("zoomend", this._onZoomEnd, this), t.whenReady(this._onZoomEnd, this))
            }
            pt(this._map._container, "gamap-grab gamap-touch-drag"), this._draggable.enable(), this._positions = [], this._times = []
        }, removeHooks: function () {
            mt(this._map._container, "gamap-grab"), mt(this._map._container, "gamap-touch-drag"), this._draggable.disable()
        }, moved: function () {
            return this._draggable && this._draggable._moved
        }, moving: function () {
            return this._draggable && this._draggable._moving
        }, _onDragStart: function () {
            var t = this._map;
            if (t._stop(), this._map.options.maxBounds && this._map.options.maxBoundsViscosity) {
                var i = M(this._map.options.maxBounds);
                this._offsetLimit = P(this._map.latLngToContainerPoint(i.getNorthWest()).multiplyBy(-1), this._map.latLngToContainerPoint(i.getSouthEast()).multiplyBy(-1).add(this._map.getSize())), this._viscosity = Math.min(1, Math.max(0, this._map.options.maxBoundsViscosity))
            } else this._offsetLimit = null;
            t.fire("movestart").fire("dragstart"), t.options.inertia && (this._positions = [], this._times = [])
        }, _onDrag: function (t) {
            if (this._map.options.inertia) {
                var i = this._lastTime = +new Date,
                    e = this._lastPos = this._draggable._absPos || this._draggable._newPos;
                this._positions.push(e), this._times.push(i), this._prunePositions(i)
            }
            this._map.fire("move", t).fire("drag", t)
        }, _prunePositions: function (t) {
            for (; this._positions.length > 1 && t - this._times[0] > 50;) this._positions.shift(), this._times.shift()
        }, _onZoomEnd: function () {
            var t = this._map.getSize().divideBy(2), i = this._map.latLngToLayerPoint([0, 0]);
            this._initialWorldOffset = i.subtract(t).x, this._worldWidth = this._map.getPixelWorldBounds().getSize().x
        }, _viscousLimit: function (t, i) {
            return t - (t - i) * this._viscosity
        }, _onPreDragLimit: function () {
            if (this._viscosity && this._offsetLimit) {
                var t = this._draggable._newPos.subtract(this._draggable._startPos), i = this._offsetLimit;
                t.x < i.min.x && (t.x = this._viscousLimit(t.x, i.min.x)), t.y < i.min.y && (t.y = this._viscousLimit(t.y, i.min.y)), t.x > i.max.x && (t.x = this._viscousLimit(t.x, i.max.x)), t.y > i.max.y && (t.y = this._viscousLimit(t.y, i.max.y)), this._draggable._newPos = this._draggable._startPos.add(t)
            }
        }, _onPreDragWrap: function () {
            var t = this._worldWidth, i = Math.round(t / 2), e = this._initialWorldOffset,
                n = this._draggable._newPos.x, o = (n - i + e) % t + i - e, s = (n + i + e) % t - i - e,
                a = Math.abs(o + e) < Math.abs(s + e) ? o : s;
            this._draggable._absPos = this._draggable._newPos.clone(), this._draggable._newPos.x = a
        }, _onDragEnd: function (t) {
            var i = this._map, e = i.options, n = !e.inertia || this._times.length < 2;
            if (i.fire("dragend", t), n) i.fire("moveend"); else {
                this._prunePositions(+new Date);
                var o = this._lastPos.subtract(this._positions[0]), s = (this._lastTime - this._times[0]) / 1e3,
                    a = e.easeLinearity, r = o.multiplyBy(a / s), h = r.distanceTo([0, 0]),
                    l = Math.min(e.inertiaMaxSpeed, h), u = r.multiplyBy(l / h), c = l / (e.inertiaDeceleration * a),
                    d = u.multiplyBy(-c / 2).round();
                d.x || d.y ? (d = i._limitOffset(d, i.options.maxBounds), f(function () {
                    i.panBy(d, {duration: c, easeLinearity: a, noMoveStart: !0, animate: !0})
                })) : i.fire("moveend")
            }
        }
    });
    Ie.addInitHook("addHandler", "dragging", Zn), Ie.mergeOptions({keyboard: !0, keyboardPanDelta: 80});
    var On = Ne.extend({
        keyCodes: {
            left: [37],
            right: [39],
            down: [40],
            up: [38],
            zoomIn: [187, 107, 61, 171],
            zoomOut: [189, 109, 54, 173]
        }, initialize: function (t) {
            this._map = t, this._setPanDelta(t.options.keyboardPanDelta), this._setZoomDelta(t.options.zoomDelta)
        }, addHooks: function () {
            var t = this._map._container;
            t.tabIndex <= 0 && (t.tabIndex = "0"), U(t, {
                focus: this._onFocus,
                blur: this._onBlur,
                mousedown: this._onMouseDown
            }, this), this._map.on({focus: this._addHooks, blur: this._removeHooks}, this)
        }, removeHooks: function () {
            this._removeHooks(), V(this._map._container, {
                focus: this._onFocus,
                blur: this._onBlur,
                mousedown: this._onMouseDown
            }, this), this._map.off({focus: this._addHooks, blur: this._removeHooks}, this)
        }, _onMouseDown: function () {
            if (!this._focused) {
                var t = document.body, i = document.documentElement, e = t.scrollTop || i.scrollTop,
                    n = t.scrollLeft || i.scrollLeft;
                this._map._container.focus(), window.scrollTo(n, e)
            }
        }, _onFocus: function () {
            this._focused = !0, this._map.fire("focus")
        }, _onBlur: function () {
            this._focused = !1, this._map.fire("blur")
        }, _setPanDelta: function (t) {
            var i, e, n = this._panKeys = {}, o = this.keyCodes;
            for (i = 0, e = o.left.length; i < e; i++) n[o.left[i]] = [-1 * t, 0];
            for (i = 0, e = o.right.length; i < e; i++) n[o.right[i]] = [t, 0];
            for (i = 0, e = o.down.length; i < e; i++) n[o.down[i]] = [0, t];
            for (i = 0, e = o.up.length; i < e; i++) n[o.up[i]] = [0, -1 * t]
        }, _setZoomDelta: function (t) {
            var i, e, n = this._zoomKeys = {}, o = this.keyCodes;
            for (i = 0, e = o.zoomIn.length; i < e; i++) n[o.zoomIn[i]] = t;
            for (i = 0, e = o.zoomOut.length; i < e; i++) n[o.zoomOut[i]] = -t
        }, _addHooks: function () {
            U(document, "keydown", this._onKeyDown, this)
        }, _removeHooks: function () {
            V(document, "keydown", this._onKeyDown, this)
        }, _onKeyDown: function (t) {
            if (!(t.altKey || t.ctrlKey || t.metaKey)) {
                var i, e = t.keyCode, n = this._map;
                if (e in this._panKeys) {
                    if (n._panAnim && n._panAnim._inProgress) return;
                    i = this._panKeys[e], t.shiftKey && (i = w(i).multiplyBy(3)), n.panBy(i), n.options.maxBounds && n.panInsideBounds(n.options.maxBounds)
                } else if (e in this._zoomKeys) n.setZoom(n.getZoom() + (t.shiftKey ? 3 : 1) * this._zoomKeys[e]); else {
                    if (27 !== e || !n._popup || !n._popup.options.closeOnEscapeKey) return;
                    n.closePopup()
                }
                $(t)
            }
        }
    });
    Ie.addInitHook("addHandler", "keyboard", On), Ie.mergeOptions({
        scrollWheelZoom: !0,
        wheelDebounceTime: 40,
        wheelPxPerZoomLevel: 60
    });
    var Rn = Ne.extend({
        addHooks: function () {
            U(this._map._container, "mousewheel", this._onWheelScroll, this), this._delta = 0
        }, removeHooks: function () {
            V(this._map._container, "mousewheel", this._onWheelScroll, this)
        }, _onWheelScroll: function (t) {
            var i = it(t), n = this._map.options.wheelDebounceTime;
            this._delta += i, this._lastMousePos = this._map.mouseEventToContainerPoint(t), this._startTime || (this._startTime = +new Date);
            var o = Math.max(n - (+new Date - this._startTime), 0);
            clearTimeout(this._timer), this._timer = setTimeout(e(this._performZoom, this), o), $(t)
        }, _performZoom: function () {
            var t = this._map, i = t.getZoom(), e = this._map.options.zoomSnap || 0;
            t._stop();
            var n = this._delta / (4 * this._map.options.wheelPxPerZoomLevel),
                o = 4 * Math.log(2 / (1 + Math.exp(-Math.abs(n)))) / Math.LN2, s = e ? Math.ceil(o / e) * e : o,
                a = t._limitZoom(i + (this._delta > 0 ? s : -s)) - i;
            this._delta = 0, this._startTime = null, a && ("center" === t.options.scrollWheelZoom ? t.setZoom(i + a) : t.setZoomAround(this._lastMousePos, i + a))
        }
    });
    Ie.addInitHook("addHandler", "scrollWheelZoom", Rn), Ie.mergeOptions({tap: !0, tapTolerance: 15});
    var jn = Ne.extend({
        addHooks: function () {
            U(this._map._container, "touchstart", this._onDown, this)
        }, removeHooks: function () {
            V(this._map._container, "touchstart", this._onDown, this)
        }, _onDown: function (t) {
            if (t.touches) {
                if (Q(t), this._fireClick = !0, t.touches.length > 1) return this._fireClick = !1, void clearTimeout(this._holdTimeout);
                var i = t.touches[0], n = i.target;
                this._startPos = this._newPos = new x(i.clientX, i.clientY), n.tagName && "a" === n.tagName.toLowerCase() && pt(n, "gamap-active"), this._holdTimeout = setTimeout(e(function () {
                    this._isTapValid() && (this._fireClick = !1, this._onUp(), this._simulateEvent("contextmenu", i))
                }, this), 1e3), this._simulateEvent("mousedown", i), U(document, {
                    touchmove: this._onMove,
                    touchend: this._onUp
                }, this)
            }
        }, _onUp: function (t) {
            if (clearTimeout(this._holdTimeout), V(document, {
                touchmove: this._onMove,
                touchend: this._onUp
            }, this), this._fireClick && t && t.changedTouches) {
                var i = t.changedTouches[0], e = i.target;
                e && e.tagName && "a" === e.tagName.toLowerCase() && mt(e, "gamap-active"), this._simulateEvent("mouseup", i), this._isTapValid() && this._simulateEvent("click", i)
            }
        }, _isTapValid: function () {
            return this._newPos.distanceTo(this._startPos) <= this._map.options.tapTolerance
        }, _onMove: function (t) {
            var i = t.touches[0];
            this._newPos = new x(i.clientX, i.clientY), this._simulateEvent("mousemove", i)
        }, _simulateEvent: function (t, i) {
            var e = document.createEvent("MouseEvents");
            e._simulated = !0, i.target._simulatedClick = !0, e.initMouseEvent(t, !0, !0, window, 1, i.screenX, i.screenY, i.clientX, i.clientY, !1, !1, !1, !1, 0, null), i.target.dispatchEvent(e)
        }
    });
    ee && !ie && Ie.addInitHook("addHandler", "tap", jn), Ie.mergeOptions({
        touchZoom: ee && !Ri,
        bounceAtZoomLimits: !0
    });
    var Dn = Ne.extend({
        addHooks: function () {
            pt(this._map._container, "gamap-touch-zoom"), U(this._map._container, "touchstart", this._onTouchStart, this)
        }, removeHooks: function () {
            mt(this._map._container, "gamap-touch-zoom"), V(this._map._container, "touchstart", this._onTouchStart, this)
        }, _onTouchStart: function (t) {
            var i = this._map;
            if (t.touches && 2 === t.touches.length && !i._animatingZoom && !this._zooming) {
                var e = i.mouseEventToContainerPoint(t.touches[0]), n = i.mouseEventToContainerPoint(t.touches[1]);
                this._centerPoint = i.getSize()._divideBy(2), this._startLatLng = i.containerPointToLatLng(this._centerPoint), "center" !== i.options.touchZoom && (this._pinchStartLatLng = i.containerPointToLatLng(e.add(n)._divideBy(2))), this._startDist = e.distanceTo(n), this._startZoom = i.getZoom(), this._moved = !1, this._zooming = !0, i._stop(), U(document, "touchmove", this._onTouchMove, this), U(document, "touchend", this._onTouchEnd, this), Q(t)
            }
        }, _onTouchMove: function (t) {
            if (t.touches && 2 === t.touches.length && this._zooming) {
                var i = this._map, n = i.mouseEventToContainerPoint(t.touches[0]),
                    o = i.mouseEventToContainerPoint(t.touches[1]), s = n.distanceTo(o) / this._startDist;
                if (this._zoom = i.getScaleZoom(s, this._startZoom), !i.options.bounceAtZoomLimits && (this._zoom < i.getMinZoom() && s < 1 || this._zoom > i.getMaxZoom() && s > 1) && (this._zoom = i._limitZoom(this._zoom)), "center" === i.options.touchZoom) {
                    if (this._center = this._startLatLng, 1 === s) return
                } else {
                    var a = n._add(o)._divideBy(2)._subtract(this._centerPoint);
                    if (1 === s && 0 === a.x && 0 === a.y) return;
                    this._center = i.unproject(i.project(this._pinchStartLatLng, this._zoom).subtract(a), this._zoom)
                }
                this._moved || (i._moveStart(!0, !1), this._moved = !0), g(this._animRequest);
                var r = e(i._move, i, this._center, this._zoom, {pinch: !0, round: !1});
                this._animRequest = f(r, this, !0), Q(t)
            }
        }, _onTouchEnd: function () {
            this._moved && this._zooming ? (this._zooming = !1, g(this._animRequest), V(document, "touchmove", this._onTouchMove), V(document, "touchend", this._onTouchEnd), this._map.options.zoomAnimation ? this._map._animateZoom(this._center, this._map._limitZoom(this._zoom), !0, this._map.options.zoomSnap) : this._map._resetView(this._center, this._map._limitZoom(this._zoom))) : this._zooming = !1
        }
    });
    Ie.addInitHook("addHandler", "touchZoom", Dn), Ie.BoxZoom = In, Ie.DoubleClickZoom = Bn, Ie.Drag = Zn, Ie.Keyboard = On, Ie.ScrollWheelZoom = Rn, Ie.Tap = jn, Ie.TouchZoom = Dn;
    var Nn = i({}, bi, {code: "PGIS", projection: Ye, transformation: S(1 / 512, .5, -1 / 512, .5)}),
        Fn = i({}, bi, {code: "PGIS2", projection: Ye, transformation: S(1 / 360, .5, -1 / 360, .25)}),
        Hn = i({}, bi, {code: "Tianditu", projection: Ye, transformation: S(1 / 360, .5, -1 / 360, .25)});
    Li.PGIS = Nn, Li.PGIS2 = Fn, Li.Tianditu = Hn;
    var Wn = v.extend({
        options: {url: null, params: {}, onDone: ti, onFail: ti, type: "jsonp", cached: !1, timeout: 2e4},
        initialize: function (t) {
            if (!t || !t.url) throw console.log(t), "The url of the json data can't be empty.";
            u(this, t), this._scriptNode = null, this._timer = null, this._key = null
        },
        doGet: function () {
            this._key = "__jsonp_" + ("" + Math.random()).slice(2);
            var t = this;
            this._timer = setTimeout(function () {
                t.cleanup(), t.options.onFail && t.options.onFail(new Error("After " + t.options.timeout / 1e3 + " seconds, request to get JSON data from " + t.options.url + " is timeout"))
            }, this.options.timeout), window[this._key] = function (i) {
                t.cleanup(), t.options.onDone && t.options.onDone(i)
            };
            var i = this.options.url;
            i += ~i.indexOf("?") ? "&" : "?", i += "callback=" + this._key + "&", i = (i = (i += Qt(this.options.params)).replace("?&", "?")).replace("&&", "&");
            var e = document.getElementsByTagName("script")[0] || document.head;
            this._scriptNode = ht("script", "", e.parentNode), this._scriptNode.type = "text/javascript", this._scriptNode.onerror = function (i) {
                t.cleanup(), t.options.onFail && t.options.onFail(new Error("Request to get JSON data from " + t.options.url + " failed"))
            }, this._scriptNode.src = i
        },
        doPost: function () {
            this.doGet()
        },
        cleanup: function () {
            this._timer && (clearTimeout(this._timer), delete this._timer), this._scriptNode && (lt(this._scriptNode), delete this._scriptNode), window[this._key] = ti
        },
        cancel: function () {
            window[this._key] && this.cleanup()
        }
    }), Gn = v.extend({
        options: {url: null, params: {}, onDone: ti, onFail: ti, type: "json", cache: !1, timeout: 2e4},
        initialize: function (t) {
            if (!(t = t || {}).url) throw"The url of the json data can't be empty.";
            u(this, t)
        },
        doGet: function () {
            this._sendGet("GET", null)
        },
        doGetDelete: function () {
            this._sendGet("DELETE", null)
        },
        doGetGeoJson: function () {
            this._sendGet("GET", {"Content-Type": "application/x-www-form-urlencoded", Accept: "application/geo+json"})
        },
        _sendGet: function (t, i) {
            var e = this.options, n = e.url;
            n += ~n.indexOf("?") ? "&" : "?", n += Qt(e.params), n += e.cache ? "" : "&_t=" + (new Date).getTime();
            var o = i || {
                "Content-Type": "application/x-www-form-urlencoded",
                Accept: "xml" == e.type ? "application/xml" : "application/json"
            };
            this.sendRequest(t, n, null, e.type, e.onDone, e.onFail, o)
        },
        doPost: function () {
            var t = Qt(this.options.params);
            t += this.cache ? "" : "&_t=" + (new Date).getTime();
            var i = this.options;
            this.sendRequest("POST", i.url, t, i.type, i.onDone, i.onFail, {
                "Content-Type": "application/x-www-form-urlencoded",
                Accept: "xml" == i.type ? "appliction/xml" : "application/json"
            })
        },
        postJson: function () {
            this.sendJson("POST")
        },
        putJson: function () {
            this.sendJson("PUT")
        },
        sendJson: function (t) {
            var i = this.options;
            this.sendRequest(t, i.url, JSON.stringify(i.params), i.type, i.onDone, i.onFail, {"Content-Type": "application/json"})
        },
        sendRequest: function (t, i, e, n, o, s, a) {
            var r = this.getXHR();
            if (!r) return !1;
            r.open(t, i, !0), r.setRequestHeader("X-Request-With", "XMLHttpRequest");
            for (var h in a || {}) r.setRequestHeader(h, a[h]);
            var l = this;
            r.onreadystatechange = function () {
                r.readyState > 3 && o && (r.status >= 200 && r.status < 300 || 304 == r.status ? o(l.getResponseData(n, r)) : s(r.responseText, r))
            }, r.send(e)
        },
        getXHR: function () {
            for (var t = "undefined" == typeof window ? this : window, i = 4; i--;) try {
                return new (t.XMLHttpRequest || t.ActiveXObject)(["Mxxml2.XMLHTTP", "Msxml2.XMLHTTP.3.0", "Msxml2.XMLHTTP.6.0", "Microsoft.XMLHTTP"][i])
            } catch (t) {
            }
            return !1
        },
        getResponseData: function (t, i) {
            return "xml" == t ? null != i.responseXML ? i.responseXML : $t(i.responseText) : "json" == t ? ii("" == i.responseText ? "{}" : i.responseText) : i.responseText
        }
    }), Un = v.extend({
        filters: {}, options: {geometry: "GEOMETRY"}, initialize: function (t) {
            u(this, t || {}), this.filters = {}
        }, CQL: function (t) {
            return this.filters.CQL = t, this
        }, _CQL2kvp: function (t) {
            return "CQL_FILTER=" + t
        }, bbox: function (t, i, e, n) {
            return this.filters.bbox = t instanceof b ? t : new b(new x(t, i), new x(e, n)), this
        }, _bbox2kvp: function (t) {
            var i = t.getBottomLeft(), e = t.getTopRight();
            return "CQL_FILTER=BBOX(" + this.options.geometry + ", " + i.x + "," + i.y + "," + e.x + "," + e.y + ")"
        }, limit: function (t) {
            return this.filters.limit = parseInt(t), this
        }, _limit2kvp: function (t) {
            return "count=" + t
        }, offset: function (t, i) {
            if (!i) throw Error("Tow parameters needed: offset number and column name which used to be sorted");
            return this.filters.offset = parseInt(t), this.filters.sortBy = i, this
        }, _offset2kvp: function (t) {
            return "startIndex=" + t
        }, sortBy: function (t) {
            return this.filters.sortBy = t, this
        }, _sortBy2kvp: function (t) {
            return "sortBy=" + t
        }, toKvp: function () {
            var t = this.options.namespace + ":" + this.options.layer + "?";
            for (var i in this.filters) {
                var e = this.filters[i];
                t += this["_" + i + "2kvp"](e) + "&"
            }
            return t.endsWith("&") ? t.substring(0, t.length - 1) : t
        }, send: function (t, i) {
            this.options.ims._doQuery(this, t, i)
        }
    }), Vn = v.extend({
        options: {host: "localhost", port: 7749, protocol: "http", type: "ajax", cache: !1, timeout: 2e4},
        initialize: function (t) {
            u(this, t = t || {})
        },
        registerDatastore: function (t, i, e, n) {
            var o = this._getRoot() + "datastore/" + t;
            this._ajax(o, i || {}, e, n).postJson()
        },
        datastores: function (t, i) {
            var e = this._getRoot() + "datastore";
            this._ajax(e, null, t, i).doGet()
        },
        datastore: function (t, i, e) {
            if (!t) throw Error("datastore key can not be empty.");
            var n = this._getRoot() + "datastore/" + t;
            this._ajax(n, null, i, e).doGet()
        },
        namespaces: function (t, i) {
            var e = this._getRoot() + "namespace";
            this._ajax(e, null, t, i).doGet()
        },
        namespace: function (t, i, e) {
            var n = this._getRoot() + "namespace/" + t;
            this._ajax(n, null, i, e).doGet()
        },
        registerNamespace: function (t, i, e) {
            var n = this._getRoot() + "namespace";
            this._ajax(n, {name: t}, i, e).postJson()
        },
        delNamespace: function (t, i, e) {
            var n = this._getRoot() + "namespace/" + t;
            this._ajax(n, null, i, e).doGetDelete()
        },
        renNamespace: function (t, i, e, n) {
            var o = this._getRoot() + "namespace/" + t;
            this._ajax(o, {name: i}, e, n).postJson()
        },
        publish: function (t, i, e, n, o) {
            t && i || o(new Error("namespace and datastore parameters can not be null"));
            for (var s = this._getRoot() + "namespace/" + t, a = e ? Array.isArray(e) ? e : [e] : [], r = [], h = [], l = 0; l < a.length; l++) {
                var u = a[l];
                u.featureType ? (h.push(u.featureType), u.layerName ? r.push(u.layerName) : r.push(u.featureType)) : this._isString(u) ? (h.push(u), r.push(u)) : o(new Error("Invalid map parameter of FeatureTypes and Layers"))
            }
            0 == r.length || 0 == h.length ? this._ajax(s, {featureTypes: [{datastore: i}]}, n, o).putJson() : this._ajax(s, {
                featureTypes: [{
                    datastore: i,
                    featureTypeNames: h,
                    alias: r
                }]
            }, n, o).putJson()
        },
        unpublish: function (t, i, e, n) {
            var o = this._getRoot() + "namespace/" + t + "/layer/" + i;
            this._ajax(o, null, e, n).doGetDelete()
        },
        _isString: function (t) {
            return "[object String]" === Object.prototype.toString.call(t)
        },
        describeLayer: function (t, i, e, n) {
            var o = this._getRoot() + "namespace/" + t + "/layer/" + i;
            this._ajax(o, null, e, n).doGet()
        },
        query: function (t, i, e) {
            var n = t.indexOf(":");
            return new Un(n > 0 ? {
                ims: this,
                namespace: t.substring(0, n),
                layer: t.substring(n + 1),
                geometry: i || "GEOMETRY"
            } : {ims: this, namespace: t, layer: i, geometry: e || "GEOMETRY"})
        },
        _doQuery: function (t, i, e) {
            var n = this._getRoot() + "layer/" + t.toKvp();
            this._ajax(n, null, i, e).doGetGeoJson()
        },
        _getRoot: function () {
            return this.options.protocol + "://" + this.options.host + ":" + this.options.port + "/ims/"
        },
        _ajax: function (t, i, e, n) {
            var o = this;
            return ei({
                url: t,
                type: o.options.type,
                timeout: o.options.timeout,
                params: i || {},
                cache: o.options.cache,
                onDone: e,
                onFail: n
            })
        }
    });
    Ie.include({
        getOverlayById: function (t) {
            return this._layers && this._layers[t] ? this._layers[t] : null
        }, removeOverlayById: function (t) {
            var i = this.getOverlayById(t);
            i && this.removeLayer(i)
        }, centerOverlayById: function (t) {
            var i = this.getOverlayById(t);
            i && (i.getBounds ? this.panTo(i.getBounds().getCenter()) : this.panTo(i.getLatLng()))
        }, fitOverlayBoundsById: function (t) {
            var i = this.getOverlayById(t);
            i && (i.getBounds ? this.fitBounds(i.getBounds()) : this.panTo(i.getLatLng()))
        }, getOverlays: function () {
            return this._layers
        }
    }), ln.include({
        blink: function (t, i) {
            this._original_color = this.options.color, this._original_fill_color = this.options.fillColor, this._blinking_timer = setInterval(function (t, i) {
                t.options.fillColor == t._original_fill_color || t._original_fill_color;
                var e = null;
                e = null != t._original_fill_color ? {fillColor: t.options.fillColor == t._original_fill_color ? i : t._original_fill_color} : {color: t.options.color == t._original_color ? i : t._original_color}, t.setStyle(e)
            }, i, this, t)
        }, isBlink: function () {
            return !(null == this._blinking_timer)
        }, stopBlink: function () {
            null != this._blinking_timer && clearInterval(this._blinking_timer);
            var t = {};
            this._original_color && (t.color = this._original_color), this._original_fill_color && (t.fillColor = this._original_fill_color), this.setStyle(t), this._original_color = null, this._original_fill_color = null, this._blinking_timer = null
        }
    }), Cn.include({
        _fillPattern: function (t, i) {
            var e = i.options.fillPattern;
            if (e && e.fill) {
                var n = e.style, o = null, s = e.interval ? e.interval : 20, a = e.weight ? e.weight : 1,
                    r = e.color ? e.color : "#ff0000", h = e.opacity ? Math.round(255 * e.opacity) : 255,
                    l = e.dash ? e.dash : [], u = !!e.fill && e.fill, c = e.imageUri ? e.imageUri : null,
                    d = e.fillColor ? e.fillColor : r, _ = e.border ? e.border : null,
                    p = e.borderColor ? e.borderColor : r;
                if ("vertical" == n) o = this._patternVerticalLine(s, a, r, h / 255, l); else if ("horizontal" == n) o = this._patternHorizontalLine(s, a, r, h / 255, l); else if ("slash" == n) o = this._rotate(this._patternSlashLine(s, a, r, h, l), 0); else if ("backslash" == n) o = this._rotate(this._patternSlashLine(s, a, r, h, l), 90); else if ("dot" == n) o = this._patternDot(s, a, h / 255, u, d, _, p); else if ("image" == n && c) return void this._patternImage(s, c, h, function (i) {
                    t.fillStyle = t.createPattern(i, "repeat"), t.fill()
                });
                t.fillStyle = t.createPattern(o, "repeat"), t.fill()
            }
        }, _patternDot: function (t, i, e, n, o, s, a) {
            var r = document.createElement("canvas");
            t = t > i ? t : i, r.width = t, r.height = t;
            var h = r.getContext("2d");
            return h.beginPath(), h.arc(t >> 1, t >> 1, i >> 1, 0, 2 * Math.PI), n && (h.globalAlpha = e, h.fillStyle = o, h.fill()), s && (h.globalAlpha = e, h.strokeStyle = a, h.lineWidth = s, h.stroke()), r
        }, _patternImage: function (t, i, e, n) {
            var o = document.createElement("canvas");
            o.width = t, o.height = t;
            var s = o.getContext("2d"), a = document.createElement("img");
            a.src = i, a.onload = function () {
                s.drawImage(a, (t - a.width) / 2, (t - a.height) / 2), n(o)
            }
        }, _patternSlashLine: function (t, i, e, n, o) {
            var s = document.createElement("canvas");
            s.width = t, s.height = t;
            for (var a = s.getContext("2d"), r = ai(e), h = a.createImageData(s.width, s.height), l = i % 2 == 0 ? -i / 2 : (1 - i) / 2, u = i % 2 == 0 ? i / 2 - 1 : (i - 1) / 2, c = l; c <= u; c++) for (var d = 0; d < t; d++) {
                var _ = c + t - d - 1, p = -1;
                o.length > 0 && -1 == (p = this._isDrawDash(_, o, c)) || (_ < 0 && (_ += t), _ >= t && (_ -= t), this._setPixel(h, {
                    x: _,
                    y: d,
                    r: r.r,
                    g: r.g,
                    b: r.b,
                    a: n * (0 == p ? 1 / (Math.abs(c) + 1) : 1)
                }))
            }
            return a.putImageData(h, 0, 0), s
        }, _isDrawDash: function (t, i, e) {
            for (var n = t - e + 1; ;) for (var o = 0; o < i.length; o++) {
                if (0 == (n -= i[o])) return 0;
                if (n < 0) return o % 2 == 0 ? 1 : -1
            }
        }, _patternVerticalLine: function (t, i, e, n, o) {
            var s = document.createElement("canvas");
            s.width = t, s.height = o.length > 1 ? this._dashLength(o) : t;
            var a = s.getContext("2d");
            return o.length > 1 && a.setLineDash(o), a.strokeStyle = e, a.lineWidth = i, a.globalAlpha = n, a.moveTo((s.width - i) / 2, 0), a.lineTo((s.width - i) / 2, s.height), a.stroke(), s
        }, _patternHorizontalLine: function (t, i, e, n, o) {
            var s = document.createElement("canvas");
            s.width = o.length > 1 ? this._dashLength(o) : t, s.height = t;
            var a = s.getContext("2d");
            return o.length > 1 && a.setLineDash(o), a.strokeStyle = e, a.lineWidth = i, a.globalAlpha = n, a.moveTo(0, (s.height - i) / 2), a.lineTo(s.width, (s.height - i) / 2), a.stroke(), s
        }, _setPixel: function (t, i) {
            var e = 4 * (i.x + i.y * t.width);
            t.data[e + 0] = i.r, t.data[e + 1] = i.g, t.data[e + 2] = i.b, t.data[e + 3] = i.a
        }, _rotate: function (t, i) {
            var e = document.createElement("canvas");
            e.width = t.width, e.height = t.height;
            var n = e.getContext("2d");
            return n.save(), n.translate(e.width / 2, e.height / 2), n.rotate(i * Math.PI / 180), n.translate(-e.width / 2, -e.height / 2), n.drawImage(t, 0, 0), n.restore(), e
        }, _dashLength: function (t) {
            for (var i = 0, e = 0; e < t.length; e++) i += t[e];
            return i
        }
    }), Cn.addInitHook(function () {
        var t = this;
        t._fillStroke_origin = t._fillStroke, this._fillStroke = function (i, e) {
            t._fillPattern(i, e), t._fillStroke_origin(i, e)
        }
    });
    var qn = hn.prototype._setPos;
    hn.include({
        _updateImg: function (t, i, e) {
            var n = "";
            n += " translate(" + -(i = new x(e[0], e[1]).divideBy(2)._subtract(new x(i[0], i[1]))).x + "px, " + -i.y + "px)", n += " rotate(" + this.options.iconAngle + "deg)", n += " translate(" + i.x + "px, " + i.y + "px)", t.style[Te] += n
        }, setIconAngle: function (t) {
            this.options.iconAngle = t, this._map && this.update()
        }, getIconAngle: function () {
            return this.options.iconAngle
        }, _setPos: function (t) {
            if (this._icon && (this._icon.style[Te] = ""), this._shadow && (this._shadow.style[Te] = ""), qn.apply(this, [t]), this.options.iconAngle) {
                var i, e = this.options.icon.options.iconAnchor, n = this.options.icon.options.iconSize;
                this._icon && (i = this._icon, this._updateImg(i, e, n))
            }
        }
    });
    var Xn = Pn.extend({
        initialize: function (t, i, e, n) {
            this._url = "http://" + t + ":" + i + "/QuadServer/maprequest?services=" + e + "&level={z}&col={x}&row={y}&rowModel=d", u(this, n)
        }
    }), Yn = sn.extend({
        options: {
            onload: function () {
            }
        }, initialize: function (t) {
            u(this, t), this._prepare()
        }, _prepare: function () {
            var t = this;
            t._icon = sn.prototype.createIcon.call(this), t._rotatedIcons = [], t._icon.onload = function () {
                t._rotatedIcons = t._prepareRotatedIcons(t._icon), t.options.onload.call(t), t.__onload__ && t.__onload__.call(t)
            }
        }, createIcon: function (t) {
            var i = this._rotatedIcons;
            if (null === i || 0 === i.length) return this._icon;
            var e = i[Math.floor((t ? parseFloat(t) : 0) / (360 / i.length))];
            return e || this._icon
        }, _prepareRotatedIcons: function (t) {
            for (var i = new Array(32), e = 0; e < i.length; e++) i[e] = this._rotateIcon(t, e * Math.PI * 2 / i.length);
            return i
        }, _rotateIcon: function (t, i) {
            var e = ht("canvas"), n = e.getContext("2d"), o = t.width, s = t.height;
            return e.width = o, e.height = s, n.save(), n.translate(o / 2, s / 2), n.rotate(i), n.translate(-o / 2, -o / 2), n.drawImage(t, 0, 0, o, s), n.restore(), e
        }
    }), Jn = bn.extend({
        initialize: function (t) {
            var i = this;
            t.icon && "function" != typeof t.icon && (t.icon.__onload__ = function () {
                i.refresh()
            }), this._tileSizeBounds = new b(new x(0, 0), this.getTileSize()), u(this, t)
        }, createTile: function (t) {
            var i = ht("canvas", "gamap-tile"), e = this.getTileSize();
            return i.width = e.x, i.height = e.y, this._drawTile(i.getContext("2d"), t), i
        }, _drawTile: function (t, i) {
            for (var e, n, o = this._tileCoordsToBounds(i), s = o.pad(.1), a = this.options.markers, r = this.getTileSize(), h = this, l = [], u = [], c = "function" == typeof this.options.icon, d = 0; d < a.length; d++) if (s.contains(a[d].coord)) {
                var _ = this._getRelativePosition(o, a[d].coord, r), p = null;
                if (c) {
                    var m = this.options.icon(a[d]);
                    m.__onload__ || (m.__onload__ = function () {
                        h.refresh()
                    }), p = m.createIcon(a[d].angle)
                } else p = this.options.icon.createIcon(a[d].angle);
                e = _[0] - p.width / 2, n = _[1] - p.height / 2, t.drawImage(p, e, n, p.width, p.height), l.push(new b(new x(e, n), new x(e + p.width, n + p.height))), u.push([d, _[0], _[1] + p.height])
            }
            for (d = 0; d < u.length; d++) {
                var f = u[d][0];
                this._drawLabel(t, new x(u[d][1], u[d][2]), a[f].label, a[f].labelStyle, l)
            }
        }, _drawLabel: function (t, i, e, n, o) {
            var n = n || {};
            t.globalAlpha = 1, t.font = n.font ? n.font : "0.9em sans-serif", t.fillStyle = n.textColor ? n.textColor : "#000000", t.textAlign = n.textAlign ? n.textAlign : "center", t.textBaseline = n.textBaseline ? n.textBaseline : "alphabetic", n.shadowColor && (t.shadowColor = n.shadowColor, t.shadowBlur = n.shadowBlur ? n.shadowBlur : 5);
            var s = n.offsetX ? n.offsetX : 0, a = n.offsetY ? n.offsetY : 0, r = 1.1 * t.measureText("M").width,
                h = t.measureText(e).width,
                l = new b(new x(i.x + s - h / 2, i.y + a - r / 2), new x(i.x + s + h / 2, i.y + a + r / 2));
            if (this._tileSizeBounds.contains(l)) {
                for (var u in o) if (o[u].intersects(l)) return;
                o.push(l), t.fillText(e, i.x + s, i.y + a)
            }
        }, _update: function () {
            bn.prototype._update.call(this)
        }, _getRelativePosition: function (t, i, e) {
            return [Math.floor(e.x * (i[1] - t.getWest()) / (t.getEast() - t.getWest())), Math.floor(e.y * (t.getNorth() - i[0]) / (t.getNorth() - t.getSouth()))]
        }, refresh: function () {
            for (var t in this._tiles) {
                var i = this._tiles[t];
                this._drawDoubleBufferImage(i)
            }
        }, _drawDoubleBufferImage: function (t) {
            var i = this.getTileSize(), e = ht("canvas", "gamap-tile"), n = e.getContext("2d"),
                o = t.el.getContext("2d");
            e.width = i.x, e.height = i.y, this._drawTile(n, t.coords), f(function () {
                o.clearRect(0, 0, i.x, i.y), o.drawImage(e, 0, 0, i.x, i.y), e = null, n = null
            })
        }
    }), Kn = Pn.extend({
        options: {}, initialize: function (t, i) {
            u(this, i), Pn.prototype.initialize.call(this, t, i)
        }, demLoaded: function (t, i) {
            var e = UTIF.decode(t.target.response);
            UTIF.decodeImage(t.target.response, e[0]);
            for (var n = UTIF.toRGBA8(e[0]), o = i.getContext("2d"), s = o.createImageData(256, 256), a = 0; a < 256; a++) for (var r = 0; r < 256; r++) {
                var h = 4 * (256 * a + r), l = 4 * (32 * Math.floor(a / 8) + Math.floor(r / 8));
                s.data[h] = n[l], s.data[h + 1] = n[l + 1], s.data[h + 2] = n[l + 2], s.data[h + 3] = n[l + 3]
            }
            o.putImageData(s, 0, 0)
        }, createTile: function (t, i) {
            var e = this, n = ht("canvas", "gamap-tile"), o = this.getTileSize();
            n.width = o.x, n.height = o.y, n.style.visibility = "visible";
            var s = new XMLHttpRequest;
            return s.open("GET", this.getTileUrl(t)), s.responseType = "arraybuffer", s.onload = function (t) {
                e.demLoaded(t, n)
            }, s.send(), this.options.crossOrigin && (n.crossOrigin = ""), n.alt = "", n.setAttribute("role", "presentation"), n
        }, getTileUrl: function (t) {
            console.log(t);
            var e = {
                r: se ? "@2x" : "",
                s: this._getSubdomain(t),
                x: t.x,
                y: Math.pow(2, t.z) - t.y - 1,
                z: this._getZoomForUrl()
            };
            return d(this._url, i(e, this.options))
        }, _update: function () {
            bn.prototype._update.call(this)
        }, refresh: function () {
            console.log("refresh");
            for (var t in this._tiles) {
                this._tiles[t];
                console.log(t)
            }
        }
    }), Qn = Cn.extend({
        options: {collision: !0}, initialize: function (t) {
            Cn.prototype.initialize.call(this), t = u(this, t)
        }, _initContainer: function () {
            Cn.prototype._initContainer.call(this), this._containerText = ht("canvas"), this._ctxLabel = this._containerText.getContext("2d"), pt(this._containerText, "gamap-zoom-animated"), this.getPane().appendChild(this._containerText), U(this._containerText, "mousemove", o(Cn.prototype._onMouseMove, 32, this), this), U(this._containerText, "click dblclick mousedown mouseup contextmenu", Cn.prototype._onClick, this), U(this._containerText, "mouseout", Cn.prototype._handleMouseOut, this)
        }, _destroyContainer: function () {
            delete this._ctxLabel, lt(this._containerText), V(this._containerText), delete this._cotainerText, Cn.prototype._destroyContainer.call(this)
        }, _updateTransform: function (t, i) {
            Cn.prototype._updateTransform.call(this, t, i);
            var e = this._map.getZoomScale(i, this._zoom), n = bt(this._container),
                o = this._map.getSize().multiplyBy(.5 + this.options.padding), s = this._map.project(this._center, i),
                a = this._map.project(t, i).subtract(s), r = o.multiplyBy(-e).add(n).add(o).subtract(a);
            Ji ? wt(this._containerText, r, e) : Lt(this._containerText, r)
        }, _update: function () {
            this._textList = [], Mn.prototype._update.call(this);
            var t = this._bounds, i = this._containerText, e = t.getSize(), n = se ? 2 : 1;
            Lt(i, t.min), i.width = n * e.x, i.height = n * e.y, i.style.width = e.x + "px", i.style.height = e.y + "px", i.style.zIndex = "4", this._container.style.zIndex = "3", se && this._ctxLable && this._ctxLable.scale(2, 2), this._ctxLabel.translate(-t.min.x, -t.min.y), Cn.prototype._update.call(this)
        }, _clear: function () {
            this._textList = [];
            var t = this._redrawBounds;
            if (t) {
                var i = t.getSize();
                this._ctxLabel.clearRect(t.min.x, t.min.y, i.x, i.y)
            } else this._ctxLabel.clearRect(0, 0, this._containerText.width, this._containerText.height);
            Cn.prototype._clear.call(this)
        }, _updatePoly: function (t, i) {
            this._drawing && (Cn.prototype._updatePoly.call(this, t, i), this._text(this._ctxLabel, t))
        }, _updateCircle: function (t) {
            Cn.prototype._updateCircle.call(this, t), this._text(this._ctxLabel, t)
        }, _text: function (t, i) {
            if (this._drawing) {
                var e = i.options;
                if (void 0 != e.label) {
                    var n = i._point ? i._point : i._map.latLngToLayerPoint(i.getCenter()),
                        o = i.options.labelStyle ? i.options.labelStyle : {};
                    t.globalAlpha = 1, t.font = o.font ? o.font : "0.9em sans-serif", t.fillStyle = o.textColor ? o.textColor : "#000000", t.textAlign = o.textAlign ? o.textAlign : "center", t.textBaseline = o.textBaseline ? o.textBaseline : "alphabetic", o.shadowColor && (t.shadowColor = o.shadowColor, t.shadowBlur = o.shadowBlur ? o.shadowBlur : 5);
                    var s = e.label(i.feature), a = o.offsetX ? o.offsetX : 0, r = o.offsetY ? o.offsetY : 0,
                        h = o.padding ? o.padding : 0, l = 1.1 * t.measureText("M").width, u = t.measureText(s).width,
                        c = new b(new x(n.x + a - h, n.y + r - l - h), new x(n.x + a + u + h, n.y + h));
                    if (this.options.collision) for (var d in this._textList) if (this._textList[d].intersects(c)) return;
                    this._textList.push(c), t.fillText(s, n.x + a, n.y + r)
                }
            }
        }
    });
    kn.include({
        _updateEllipse: function (t) {
            t._point;
            var i = t._radiusX, e = t._radiusY, n = t._tiltDeg, o = t._endPointParams,
                s = "M" + o.x0 + "," + o.y0 + "A" + i + "," + e + "," + n + "," + o.largeArc + "," + o.sweep + "," + o.x1 + "," + o.y1 + " z";
            this._setPath(t, s)
        }
    }), Cn.include({
        _updateEllipse: function (t) {
            if (!t._empty()) {
                var i = t._point, e = this._ctx, n = t._radiusX, o = (t._radiusY || n) / n;
                this._drawnLayers[t._gmap_id] = t, e.save(), e.translate(i.x, i.y), 0 !== t._tilt && e.rotate(t._tilt), 1 !== o && e.scale(1, o), e.beginPath(), e.arc(0, 0, n, 0, 2 * Math.PI), e.restore(), this._fillStroke(e, t)
            }
        }
    });
    var $n = ln.extend({
        options: {fill: !0, startAngle: 0, endAngle: 359.9}, initialize: function (t, i, e, n) {
            u(this, n), this._latlng = A(t), this._tiltDeg = e || 0, i && (this._mRadiusX = i[0], this._mRadiusY = i[1])
        }, setRadius: function (t) {
            return this._mRadiusX = t[0], this._mRadiusY = t[1], this.redraw()
        }, getRadius: function () {
            return new x(this._mRadiusX, this._mRadiusY)
        }, setTilt: function (t) {
            return this._tiltDeg = t, this.redraw()
        }, getBounds: function () {
            var t = this._getLngRadius(), i = this._getLatRadius(), e = this._latlng;
            return new T([e.lat - i, e.lng - t], [e.lat + i, e.lng + t])
        }, setLatLng: function (t) {
            return this._latlng = tolatLng(t), this.redraw(), this.fire("move", {latlng: this._latlng})
        }, getLatLng: function () {
            return this._latlng
        }, setStyle: ln.prototype.setStyle, _project: function () {
            var t = this._getLngRadius(), i = this._getLatRadius(), e = this._latlng,
                n = this._map.latLngToLayerPoint([e.lat, e.lng - t]),
                o = this._map.latLngToLayerPoint([e.lat - i, e.lng]);
            this._point = this._map.latLngToLayerPoint(e), this._radiusX = Math.max(this._point.x - n.x, 1), this._radiusY = Math.max(o.y - this._point.y, 1), this._tilt = Math.PI * this._tiltDeg / 180, this._endPointParams = this._centerPointToEndPoint(), this._updateBounds()
        }, _updateBounds: function () {
            var t = Math.sin(this._tilt), i = Math.cos(this._tilt), e = t * t, n = i * i,
                o = this._radiusX * this._radiusX, s = this._radiusY * this._radiusY, a = Math.sqrt(o * n + s * e),
                r = Math.sqrt(o * e + s * n), h = this._clickTolerance(), l = [a + h, r + h];
            this._pxBounds = new b(this._point.subtract(l), this._point.add(l))
        }, _update: function () {
            this._map && this._updatePath()
        }, _updatePath: function () {
            this._renderer._updateEllipse(this)
        }, _getLatRadius: function () {
            return this._mRadiusY / 40075017 * 360
        }, _getLngRadius: function () {
            return this._mRadiusX / 40075017 * 360 / Math.cos(Math.PI / 180 * this._latlng.lat)
        }, _centerPointToEndPoint: function () {
            var t = this._point, i = this._radiusX, e = this._radiusY,
                n = (this.options.startAngle + this.options.endAngle) * (Math.PI / 180),
                o = this.options.startAngle * (Math.PI / 180), s = this.options.endAngle,
                a = this._tiltDeg * (Math.PI / 180),
                r = t.x + Math.cos(a) * i * Math.cos(o) + Math.sin(-a) * e * Math.sin(o),
                h = t.y + Math.sin(a) * i * Math.cos(o) + Math.cos(a) * e * Math.sin(o),
                l = t.x + Math.cos(a) * i * Math.cos(n) + Math.sin(-a) * e * Math.sin(n),
                u = t.y + Math.sin(a) * i * Math.cos(n) + Math.cos(a) * e * Math.sin(n);
            return {x0: r, y0: h, tilt: a, largeArc: s > 180 ? 1 : 0, sweep: s > 0 ? 1 : 0, x1: l, y1: u}
        }, _empty: function () {
            return this._radiusX && this._radiusY && !this._renderer._bounds.intersects(this._pxBounds)
        }, _containsPoint: function (t) {
            var i = Math.sin(this._tilt), e = Math.cos(this._tilt), n = t.x - this._point.x, o = t.y - this._point.y,
                s = e * n + i * o, a = i * n - e * o;
            return s * s / (this._radiusX * this._radiusX) + a * a / (this._radiusY * this._radiusY) <= 1
        }
    });
    kn.include({
        _updateCrossCircle: function (t) {
            console.log("CrossCirle do not implements in SVG Renderer")
        }
    }), Cn.include({
        _updateCrossCircle: function (t) {
            if (this._drawing && !t._empty()) {
                var i = document.createElement("canvas"), e = t._point, n = i.getContext("2d"),
                    o = Math.max(Math.round(t._radius), 1), s = (Math.max(Math.round(t._radiusY), 1) || o) / o;
                i.width = 2 * (o + t.options.weight), i.height = 2 * (o + t.options.weight) / s, this._drawnLayers[t._leaflet_id] = t, 1 !== s && (n.save(), n.scale(1, s)), n.beginPath(), n.arc(i.width / 2, i.height / 2 / s, o, 0, 2 * Math.PI, !1), 1 !== s && n.restore(), this._fillStroke(n, t), this._maskCrossCircle(i, t), this._ctx.drawImage(i, e.x - i.width / 2, e.y - i.height / 2)
            }
        }, _maskCrossCircle: function (t, i) {
            var e = i, n = i._maskCircle;
            if (n) {
                n._map || (n._map = i._map, n._renderer = i._renderer), n._project();
                for (var o = this._map.latLngToLayerPoint(n.getLatLng()), s = this._map.latLngToLayerPoint(e.getLatLng()), a = t.getContext("2d"), r = Math.max(Math.round(n._radius), 1), h = (Math.max(Math.round(n._radiusY), 1) || r) / r, l = -r; l < r; l += 1) {
                    var u = Math.sqrt(Math.pow(r, 2) - Math.pow(l, 2));
                    Math.abs(o.x + l - s.x) <= e._radius && a.clearRect(o.x - s.x + t.width / 2 + l, o.y - s.y + t.height / 2 - u * h, 1, 2 * u * h)
                }
            }
        }
    });
    var to = cn.extend({
            _mainCircle: null, _maskCircle: null, initialize: function (t, i, e) {
                this._mainCircle = t, this._maskCircle = i, (e = e || {}).radius = t.options.radius, this._maskCircle.options = e, cn.prototype.initialize.call(this, t.getLatLng(), e)
            }, setCircle: function (t) {
                return this._mainCircle = t, this.redraw()
            }, setMask: function (t) {
                return this._maskCircle = t, this.redraw()
            }, _updatePath: function () {
                this._renderer._updateCrossCircle(this)
            }
        }),
        io = ["data:image/png;base64,", "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAC7lBMVEUAAAABAQECAgIDAwMEBAQF", "BQUGBgYHBwcICAgJCQkKCgoLCwsMDAwNDQ0ODg4PDw8QEBARERESEhITExMUFBQVFRUWFhYXFxcY", "GBgZGRkaGhobGxscHBwdHR0eHh4fHx8gICAhISEiIiIjIyMkJCQlJSUmJiYnJycoKCgpKSkqKior", "KyssLCwtLS0uLi4vLy8wMDAxMTEyMjIzMzM0NDQ1NTU2NjY3Nzc4ODg5OTk6Ojo7Ozs8PDw9PT0+", "Pj4/Pz9AQEBBQUFCQkJDQ0NERERGRkZHR0dISEhJSUlKSkpLS0tMTExNTU1OTk5PT09QUFBRUVFS", "UlJTU1NUVFRVVVVWVlZXV1dYWFhZWVlaWlpbW1tcXFxdXV1eXl5fX19gYGBhYWFiYmJjY2NkZGRl", "ZWVmZmZnZ2doaGhpaWlqampra2tsbGxtbW1ubm5vb29xcXFycnJzc3N0dHR1dXV2dnZ3d3d4eHh5", "eXl6enp7e3t8fHx9fX1+fn5/f3+AgICBgYGCgoKDg4OEhISFhYWGhoaHh4eIiIiKioqLi4uMjIyN", "jY2Ojo6Pj4+QkJCRkZGSkpKTk5OUlJSVlZWWlpaXl5eYmJiZmZmampqbm5ucnJydnZ2enp6fn5+g", "oKChoaGioqKkpKSlpaWmpqanp6eoqKipqamqqqqrq6usrKytra2urq6vr6+wsLCxsbGysrKzs7O0", "tLS1tbW2tra3t7e4uLi5ubm6urq7u7u9vb2+vr6/v7/AwMDBwcHCwsLDw8PExMTFxcXGxsbHx8fI", "yMjJycnKysrLy8vMzMzNzc3Pz8/Q0NDR0dHS0tLT09PU1NTV1dXW1tbX19fY2NjZ2dna2trb29vc", "3Nzd3d3e3t7f39/g4ODh4eHi4uLj4+Pk5OTl5eXm5ubn5+fo6Ojp6enq6urr6+vs7Ozt7e3u7u7v", "7+/w8PDx8fHy8vLz8/P09PT19fX29vb39/f4+Pj5+fn6+vr7+/v8/Pz9/f3+/v7///9A5nLSAAAA", "AWJLR0QAiAUdSAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB98FHBEuKjDLarAAAAAdaVRY", "dENvbW1lbnQAAAAAAENyZWF0ZWQgd2l0aCBHSU1QZC5lBwAAAjNJREFUOMtjYEACjExMLCzMTIwM", "2AETK7eQhIyslAgvOzM2aU4xTbvAhMyspFAXPWkedCWM7GJGYVUz1+0+fGTfpvnNCdYynEwo8twq", "fo1rTt978fbDh3evHl7c1hOty49kCCOPTtK8U08+fPvx4+fPnz++fXpxaXWhhRAzQr9O5uob7779", "hIPvHx/sqLISgNnCrpK8+s6nHz+RwI8vT3aVGnJDPMwk5jfvBqo8SMWjDUkKrGAFnEYNJ9+hyQNV", "fLo+zUUAZASTeNjaJ99+YoDvr/flKLEAFbBpVp368BML+HJzohU3UAG33Yy7X7Ep+P58XZAwEwOT", "UOC65z+wKfj5/lCmLDMDk0T87rfYFXw6W6HGysAik3kYqxOAjrjcpM3GwCKbdQSXgivNOuwMTFLJ", "+95ht+Lz+Wp1VgYmkdBNL7Er+HA0V56FgZHXef7Db9jkf7zaEikGjC92vaYLn7Ap+HpvliMPMKyZ", "peO3vfiOxYB3x8s02ECRxWPdc+kjpiu+3FvoKwJOESwy0asefEGX//Zid74GOyTBcOoW7njyFdWM", "b6+Ot9rwQ3MIE79F9a5Hn74jJ5cXx3vcxeCJklnIqnTD9TdfoEp+fH13b3ebuxQrIlkzCxgmTdt3", "4/n7T1++fP7w6u6xhfk24qwo+YpbwSVn4rqDZy9fOX90y6wyXw1+tLzFyMqvZBWcUdHUXJ0b6agh", "zI6ZgxlZuIRlVbW11eXFeFhxZHAmZlY2dlYWlPwPAD6nKPWk11d/AAAAAElFTkSuQmCC"].join(""),
        eo = ["data:image/png;base64,", "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBI", "WXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4QoPCTgwPYjdqAAAABl0RVh0Q29tbWVudABDcmVhdGVk", "IHdpdGggR0lNUFeBDhcAAAAsSURBVFjD7c4xAQAACAOgaf/OM4YPJGDaNo82zwQEBAQEBAQEBAQE", "BAQEBA5NiAQ8SOnY7gAAAABJRU5ErkJggg=="].join(""),
        no = Xn.extend({
            options: {maskUrl: io, maskSize: 200}, getMaskSize: function () {
                var t = this.options.maskSize;
                return t instanceof x ? t : new x(t, t)
            }, setCenter: function (t) {
                if (2 !== arguments.length) {
                    if (this._map) {
                        var i = this._map.containerPointToLayerPoint(t);
                        i = i.subtract(this.getMaskSize().divideBy(2)), this._image.setAttribute("x", i.x), this._image.setAttribute("y", i.y)
                    }
                } else this.setCenter(new x(arguments[0], arguments[1]))
            }, setMaskStyle: function (t, i) {
                this.options.maskSize = i || 400, this.options.maskUrl = t && "circle" === t ? io : t && "rect" === t ? eo : t;
                var i = this.getMaskSize();
                this._image && (this._image.setAttribute("width", i.x), this._image.setAttribute("height", i.y), this._image.setAttributeNS("http://www.w3.org/1999/xlink", "xlink:href", this.options.maskUrl))
            }, _initContainer: function () {
                if (!this._container) {
                    var t = this._map.getRenderer(this)._rootGroup, i = t.appendChild(kn.create("defs")),
                        e = t.appendChild(kn.create("g")), o = i.appendChild(kn.create("mask")),
                        s = o.appendChild(kn.create("image")), a = this.getMaskSize();
                    o.setAttribute("id", "gamap-tilelayer-mask-" + n(this)), s.setAttribute("width", a.x), s.setAttribute("height", a.y), s.setAttributeNS("http://www.w3.org/1999/xlink", "xlink:href", this.options.maskUrl), t.setAttribute("mask", "url(#" + o.getAttribute("id") + ")"), this._container = e, this._image = s, this.setCenter(this._map.getSize().divideBy(2))
                }
            }, _updateLevels: function () {
                var t = this._tileZoom;
                if (void 0 != t) {
                    for (var i in this._levels) this._levels[i].el.firstChild || i === t || (lt(this._levels[i].el), this._removeTilesAtZoom(i), delete this._levels[i]);
                    var e = this._levels[t];
                    if (!e) {
                        var n = this._map;
                        e = {
                            el: this._container.appendChild(kn.create("g")),
                            origin: n.project(n.unproject(n.getPixelOrigin()), t).round(),
                            zoom: t
                        }, this._setZoomTransform(e, n.getCenter(), n.getZoom()), this._levels[t] = e
                    }
                    return this._level = e, e
                }
            }, _addTile: function (t, i) {
                var e = this._getTilePos(t), n = this.getTileSize(), o = this._tileCoordsToKey(t),
                    s = this.getTileUrl(this._wrapCoords(t)), a = i.appendChild(kn.create("image"));
                a.setAttribute("width", n.x), a.setAttribute("height", n.y), a.setAttribute("x", e.x), a.setAttribute("y", e.y), a.setAttributeNS("http://www.w3.org/1999/xlink", "xlink:href", s), this._tiles[o] = {
                    el: a,
                    coords: t,
                    current: !0
                }
            }
        }), oo = en.extend({
            initialize: function (t, i) {
                this.cfg = t, this._el = ht("div", "gamap-zoom-hide"), this._data = [], this._max = 1, this._min = 0, this.cfg.container = this._el, this._heatmapRender = i
            }, onAdd: function (t) {
                var i = t.getSize();
                this._map = t, this._width = i.x, this._height = i.y, this._el.style.width = i.x + "px", this._el.style.height = i.y + "px", this._el.style.position = "absolute", this._origin = this._map.layerPointToLatLng(new x(0, 0)), t.getPanes().overlayPane.appendChild(this._el), this._heatmap || (this._heatmap = this._heatmapRender.create(this.cfg)), t.on("moveend", this._reset, this), this._draw()
            }, addTo: function (t) {
                return t.addLayer(this), this
            }, onRemove: function (t) {
                t.getPanes().overlayPane.removeChild(this._el), t.off("moveend", this._reset, this)
            }, _draw: function () {
                if (this._map) {
                    var t = this._map.getPanes().mapPane._gmap_pos;
                    this._el.style[oo.CSS_TRANSFORM] = "translate(" + -Math.round(t.x) + "px," + -Math.round(t.y) + "px)", this._update()
                }
            }, _update: function () {
                var t, i, e, n = {max: this._max, min: this._min, data: []};
                if (t = this._map.getBounds(), i = this._map.getZoom(), e = Math.pow(2, i), 0 != this._data.length) {
                    for (var o = [], s = this.cfg.scaleRadius ? e : 1, a = 0, r = 0, h = this.cfg.valueField, l = this._data.length; l--;) {
                        var u = this._data[l], c = u[h], d = u.latlng;
                        if (t.contains(d)) {
                            a = Math.max(c, a), r = Math.min(c, r);
                            var _ = this._map.latLngToContainerPoint(d), p = {x: Math.round(_.x), y: Math.round(_.y)};
                            p[h] = c;
                            var m;
                            m = u.radius ? u.radius * s : (this.cfg.radius || 2) * s, p.radius = m, o.push(p)
                        }
                    }
                    this.cfg.useLocalExtrema && (n.max = a, n.min = r), n.data = o, this._heatmap.setData(n)
                } else this._heatmap && this._heatmap.setData(n)
            }, setData: function (t) {
                this._max = t.max || this._max, this._min = t.min || this._min;
                for (var i = this.cfg.latField || "lat", e = this.cfg.lngField || "lng", n = this.cfg.valueField || "value", o = (t = t.data).length, s = []; o--;) {
                    var a = t[o], r = {latlng: new C(a[i], a[e])};
                    r[n] = a[n], a.radius && (r.radius = a.radius), s.push(r)
                }
                this._data = s, this._draw()
            }, addData: function (t) {
                if (t.length > 0) for (var i = t.length; i--;) this.addData(t[i]); else {
                    var e = this.cfg.latField || "lat", n = this.cfg.lngField || "lng", o = this.cfg.valueField || "value",
                        s = t, a = {latlng: new C(s[e], s[n])};
                    a[o] = s[o], this._max = Math.max(this._max, a[o]), this._min = Math.min(this._min, a[o]), s.radius && (a.radius = s.radius), this._data.push(a), this._draw()
                }
            }, _reset: function () {
                this._origin = this._map.layerPointToLatLng(new x(0, 0));
                var t = this._map.getSize();
                this._width === t.x && this._height === t.y || (this._width = t.x, this._height = t.y, this._el.style.width = this._width + "px", this._el.style.height = this._height + "px", this._heatmap._renderer.setDimensions(this._width, this._height)), this._draw()
            }
        });
    oo.CSS_TRANSFORM = function () {
        for (var t = document.createElement("div"), i = ["transform", "WebkitTransform", "MozTransform", "OTransform", "msTransform"], e = 0; e < i.length; e++) {
            var n = i[e];
            if (void 0 !== t.style[n]) return n
        }
        return i[0]
    }();
    var so = cn.extend({
        _project: function () {
            cn.prototype._project.call(this), this._radiusY = this._radius
        }
    }), ao = sn.extend({
        options: {bgPos: null, canvas: null}, createIcon: function (t) {
            t && "CANVAS" === t.tagName || document.createElement("canvas");
            var i = this.options, e = this.options.canvas;
            if (this.options.bgPos) {
                var n = point(i.bgPos);
                e.style.backgroundPosition = -n.x + "px " + -n.y + "px"
            }
            return this._setIconStyles(e, "icon"), e
        }, createShadow: function () {
            return null
        }
    }), ro = v.extend({
        options: {cached: !0, load: ri, error: ri, done: ri, files: []}, initialize: function (t) {
            u(this, t || {}), this._css_files = [], this._js_files = [], this._place = document.getElementsByTagName("head")[0], this._splitJSCSS(this._js_files, this._css_files)
        }, _splitJSCSS: function (t, i) {
            for (var e = this.options.files, n = 0; n < e.length; ++n) if (hi(e[n], ".css")) i.push(e[n]); else {
                if (!hi(e[n], ".js")) throw'Error unknown filetype "' + e[n] + '".';
                t.push(e[n])
            }
        }, load: function () {
            for (var t = 0; t < this._css_files.length; ++t) this._loadStyle(this._css_files[t]);
            this._js_files.length > 0 && this._loadScript(0)
        }, _loadStyle: function (t) {
            var i = document.createElement("link");
            i.rel = "stylesheet", i.type = "text/css", i.href = this.options.cached ? t : this._withNoCache(t);
            var e = this;
            i.onload = function () {
                e.options.load(t)
            }, i.onerror = function () {
                e.options.error(t)
            }, this._place.appendChild(i)
        }, _loadScript: function (t) {
            var i = this, e = this._js_files[t], n = document.createElement("script");
            n.type = "text/javascript", n.src = this.options.cached ? e : this._withNoCache(e), n.onload = function () {
                i.options.load(e), t + 1 >= i._js_files.length ? i.options.done() : i._loadScript(t + 1)
            }, n.onerror = function () {
                i.options.error(e), t + 1 >= i._js_files.length ? i.options.done() : i._loadScript(t + 1)
            }, this._place.appendChild(n)
        }, _withNoCache: function (t) {
            return -1 === t.indexOf("?") ? t += "?n_c=" + (new Date).getTime() : t += "&n_c=" + (new Date).getTime(), t
        }
    }), ho = {}, lo = v.extend({
        options: {}, initialize: function (t) {
            u(this, t || {})
        }, load: function (t, i) {
            if (!t) throw"can not in parameters";
            var e = this._tryGAMapPath();
            if (null === e) throw"Can not find default GAMap library folder";
            var n = t.toLowerCase();
            if (this._isload(n)) i.done(t); else {
                if (i = i || {}, "markercluster" === n) this._loadMarkerCluster(e, i); else if ("heatmap" === n) this._loadHeatmap(e, i); else if ("density-heatmap" === n) this._loadDensityHeatmap(e, i); else if ("curve" === n) this._loadCurve(e, i); else if ("vector-tile" === n) this._loadVectorTile(e, i); else if ("gts" === n) this._loadGTS(e, i); else if ("draw" === n) this._loadDraw(e, i); else if ("moving-marker" === n) this._loadMovingMarker(e, i); else if ("animated-sector" === n) this._loadPlugin(e, i, "animated-sector", ["gamap.animated-sector.js"]); else if ("leading-line" === n) this._loadPlugin(e, i, "leading-line", ["gamap.leading-line.js"]); else if ("terrain" === n) this._loadPlugin(e, i, "terrain", ["gamap.terrain.js"]); else if ("print" === n) this._loadPlugin(e, i, "print", ["gamap.print.js"]); else if ("contextmenu" === n) this._loadPlugin(e, i, "contextmenu", ["gamap.contextmenu.js", "gamap.contextmenu.css"]); else if ("spline" === n) this._loadPlugin(e, i, "spline", ["gamap.spline.js"]); else {
                    if ("jsts" !== n) throw'unknown plugin "' + t + '"';
                    this._loadPlugin(e, i, "jsts", ["gamap.jsts.js"])
                }
                this._register(n)
            }
        }, _register: function (t) {
            ho[t] = "loaded"
        }, _isload: function (t) {
            return ho[t]
        }, _tryGAMapPath: function () {
            for (var t = document.querySelectorAll("script[src]"), i = 0; i < t.length; i++) {
                var e = t[i].src.split("?")[0];
                if (hi(e, "gamap.js") || hi(e, "gamap-src.js")) return e.split("/").slice(0, -1).join("/") + "/plugins/"
            }
            return null
        }, _loadMarkerCluster: function (t, i) {
            i.files = [t + "markercluster/MarkerCluster.css", t + "markercluster/MarkerCluster.Default.css", t + "markercluster/gamap.markercluster.js"], new ro(i).load()
        }, _loadHeatmap: function (t, i) {
            i.files = [t + "heatmap/heatmap.min.js"], new ro(i).load()
        }, _loadDensityHeatmap: function (t, i) {
            i.files = [t + "density-heatmap/gamap.density-heatmap.js"], new ro(i).load()
        }, _loadCurve: function (t, i) {
            i.files = [t + "curve/gamap.curve.js"], new ro(i).load()
        }, _loadVectorTile: function (t, i) {
            i.files = [t + "vector-tile/gamap.vector-tile.js"], new ro(i).load()
        }, _loadGTS: function (t, i) {
            i.files = [t + "gts/gamap.topology-suite.js"], new ro(i).load()
        }, _loadDraw: function (t, i) {
            i.files = [t + "draw/gamap.draw.js", t + "draw/gamap.draw.css"], new ro(i).load()
        }, _loadMovingMarker: function (t, i) {
            i.files = [t + "moving-marker/gamap.moving-marker.js"], new ro(i).load()
        }, _loadPlugin: function (t, i, e, n) {
            for (var o = [], s = 0; s < n.length; s++) o.push(t + e + "/" + n[s]);
            i.files = o, new ro(i).load()
        }
    });
    Ie.addInitHook(function () {
        for (var t = this.options.plugins ? this.options.plugins : [], i = GAMap.pluginManager(), e = 0; e < t.length; e++) i.load(t[e], {
            done: function () {
                console.log("plugin loaded.")
            }, fail: function (t, i) {
                console.log("load " + i + " failed")
            }
        })
    });
    var uo = window.GAMap;
    window.GAMap = t, Object.freeze = li, t.version = "1.3.1+HEAD.86099a8", t.noConflict = function () {
        return window.GAMap = uo, this
    }, t.Control = Be, t.control = Ze, t.Browser = le, t.Evented = xi, t.Mixin = He, t.Util = vi, t.Class = v, t.Handler = Ne, t.extend = i, t.bind = e, t.stamp = n, t.setOptions = u, t.DomEvent = Pe, t.DomUtil = ke, t.PosAnimation = Ee, t.Draggable = Ve, t.LineUtil = qe, t.PolyUtil = Xe, t.Point = x, t.point = w, t.Bounds = b, t.bounds = P, t.Transformation = z, t.transformation = S, t.Projection = Ke, t.LatLng = C, t.latLng = A, t.LatLngBounds = T, t.latLngBounds = M, t.CRS = Li, t.GeoJSON = pn, t.geoJSON = Xt, t.geoJson = fn, t.Layer = en, t.LayerGroup = nn, t.layerGroup = function (t, i) {
        return new nn(t, i)
    }, t.FeatureGroup = on, t.featureGroup = function (t) {
        return new on(t)
    }, t.ImageOverlay = gn, t.imageOverlay = function (t, i, e) {
        return new gn(t, i, e)
    }, t.VideoOverlay = vn, t.videoOverlay = function (t, i, e) {
        return new vn(t, i, e)
    }, t.DivOverlay = yn, t.Popup = xn, t.popup = function (t, i) {
        return new xn(t, i)
    }, t.Tooltip = wn, t.tooltip = function (t, i) {
        return new wn(t, i)
    }, t.Icon = sn, t.icon = function (t) {
        return new sn(t)
    }, t.DivIcon = Ln, t.divIcon = function (t) {
        return new Ln(t)
    }, t.Marker = hn, t.marker = function (t, i) {
        return new hn(t, i)
    }, t.TileLayer = Pn, t.tileLayer = Yt, t.GridLayer = bn, t.gridLayer = function (t) {
        return new bn(t)
    }, t.SVG = kn, t.svg = Kt, t.Renderer = Mn, t.Canvas = Cn, t.canvas = Jt, t.Path = ln, t.CircleMarker = un, t.circleMarker = function (t, i) {
        return new un(t, i)
    }, t.Circle = cn, t.circle = function (t, i, e) {
        return new cn(t, i, e)
    }, t.Polyline = dn, t.polyline = function (t, i) {
        return new dn(t, i)
    }, t.Polygon = _n, t.polygon = function (t, i) {
        return new _n(t, i)
    }, t.Rectangle = En, t.rectangle = function (t, i) {
        return new En(t, i)
    }, t.Map = Ie, t.map = function (t, i) {
        return new Ie(t, i)
    }, t.Ajax = Gn, t.ajax = ei, t.Ims4 = Vn, t.ims4 = function (t) {
        return new Vn(t)
    }, t.QuadServerImgLayer = Xn, t.tileLayerQSImg = function (t, i, e, n) {
        return new Xn(t, i, e, n)
    }, t.CanvasMarkerTileLayer = Jn, t.tileLayerCanvasMarker = function (t) {
        return new Jn(t)
    }, t.DemTileLayer = Kn, t.demTileLayer = function (t, i) {
        return new Kn(t, i)
    }, t.LabelCollisionCanvas = Qn, t.canvasEx = function (t) {
        return ae ? new Qn(t) : null
    }, t.Ellipse = $n, t.ellipse = function (t, i, e, n) {
        return new $n(t, i, e, n)
    }, t.CrossCircle = to, t.crossCircle = function (t, i, e) {
        return new to(t, i, e)
    }, t.TileLayerMask = no, t.tileLayerMask = function (t, i, e, n) {
        return new no(t, i, e, n)
    }, t.HeatmapLayer = oo, t.heatmapLayer = function (t, i) {
        return new oo(t, i)
    }, t.RoundCircle = so, t.roundCircle = function (t, i, e) {
        return new so(t, i, e)
    },t.RotateIcon = Yn,t.rotateIcon = function (t) {
        return new Yn(t)
    },t.CanvasIcon = ao,t.canvasIcon = function (t) {
        return new ao(t)
    },t.ScriptLoader = ro,t.scriptLoader = function (t) {
        return new ro(t)
    },t.PluginManager = lo,t.pluginManager = function (t) {
        return new lo(t)
    }
});