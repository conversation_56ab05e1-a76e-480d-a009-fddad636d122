import {commonMixins} from "@/api/commonMethods/common-mixins"
import {common} from "@/api/commonMethods/common"
import {servicesMixins} from "../service-mixins/service-mixins";
import {mapGetters} from "vuex"
import $right from "@/assets/data/right-data/right-data"
import SaveDialog from "../save/SaveDialog"
import Advanced from "../dialog/Advanced"
import {listMixins} from "@/api/commonMethods/list-mixins";
import {coustTableH} from "@/api/commonMethods/count-table-h";
import AuthDialog from "@/projects/DataCenter/views/dataSpace/dataReady/dialog/auth-dialog";

export default {
    name: "list",
    mixins: [commonMixins, servicesMixins, common, listMixins, coustTableH],
    components: {SaveDialog, Advanced, AuthDialog},
    props: {
        renewListLoad: Function,
    },
    watch: {
        "settings.loading": {
            handler(val) {
                this.renewListLoad(val);
            },
            deep: true
        }
    },
    computed: {
        ...mapGetters(["userRight", "userInfo"]),
        rights() {
            let rights = [];
            if (this.userRight) {
                this.userRight.forEach(r => {
                    rights.push(r.funcCode)
                });
            }
            return rights;
        },
        countHasRightMenu() {
            let len = 0;
            const vm = this;
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    len++
                }
            });
            return len;
        },
        hasRightOptIcon() {
            const vm = this;
            let opts = [];
            vm.operateIcons.forEach(me => {
                if (me.condition(vm.rights)) {
                    opts.push(me);
                }
            });
            return opts;
        },
        // 是其他用户
        isOtherUser() {
            return this.currentUserId !== this.userInfo?.id;
        },

    },
    data() {
        return {
            batchShareTxt: "批量分享",
            myShareTxt: "我的分享",
            inputValueTable: "",
            addDashboard: $right["dashboardSaveOrUpdate"],
            buttonTxt: "新建仪表盘",
            tableData: [],
            tHeadData: [
                {
                    prop: "name",
                    label: "名称",
                    minWidth: "160",
                    align: "left"
                },
                {
                    prop: "operateUserId",
                    label: "创建人",
                    width: "",
                    align: "center"
                },
                {
                    prop: "updateTime",
                    label: "最后编辑时间",
                    minWidth: 160,
                    align: "center",
                    resizable: false
                },
                {
                    prop: 'operate',
                    label: '操作',
                    width: 240,
                    align: "center",
                    resizable: false
                }
            ],
            total: 0,
            operateIcons: [

                {
                    icon: "&#xe792;",
                    name: "查看",
                    clickFn: this.viewPanel,
                    condition: (right) => right.indexOf($right["dashboardGetListByGroupId"]) > -1,
                    show: () => true,
                },
                {
                    icon: "&#xe840;",
                    name: "编辑",
                    clickFn: this.editPanel,
                    condition: (right) => right.indexOf($right["visualEditGetNoChart"]) > -1 && !this.isOtherUser,
                    show: () => true,
                },
                {
                    icon: "&#xe6f4;",
                    name: "另存为",
                    clickFn: this.copyPanel,
                    condition: (right) => right.indexOf($right["dashboardCopy"]) > -1 && !this.isOtherUser,
                    show: () => true,
                },
                {
                    icon: "&#xe6bf;",
                    name: "移动到",
                    clickFn: this.moveTo,
                    condition: (right) => right.indexOf($right["dashboardUpdateGroup"]) > -1 && !this.isOtherUser,
                    show: () => true,
                },
                {
                    icon: "&#xe6c0;",
                    name: "分享",
                    clickFn: this.rowShare,
                    show: () => true,
                    condition: (right) => !this.isOtherUser
                },
                // {icon: "&#xe6f3;", tip: "共享仪表盘", clickFn: this.release},
                {
                    icon: "&#xe72f;",
                    name: "高级",
                    clickFn: this.newAdvanced,
                    condition: (right) => right.indexOf($right["dashboardGetDashboard"]) > -1 && !this.isOtherUser,
                    show: () => true,
                },
                {
                    icon: "&#xe65f;",
                    name: "删除",
                    clickFn: this.deletePanel,
                    condition: (right) => right.indexOf($right["dashboardDelete"]) > -1 && !this.isOtherUser,
                    show: () => true,
                },

            ],
            groupId: "",
            currentUserId: "",// 目录用户 Id
        }
    },
    methods: {
        linkToShare() {
            this.$router.push({
                name: "share",
                params: {
                    type: "dashboard",
                    tab: "ShareTableList",
                }
            })
        },
        menuCommand(command, row, index) {
            command.clickFn(row, index);
        },
        changePage(index) {
            const vm = this, {visualServices, visualMock, settings} = this;
            let services = vm.getServices(visualServices, visualMock);
            let id = vm.groupId && vm.groupId !== '0' ? vm.groupId : '';
            settings.loading = true;
            vm.paginationProps.currentPage = index;
            let data = {
                groupId: id,
                keyWord: vm.inputValueTable,
                index: index,
                pageSize: vm.paginationProps.pageSize,
                operatorId: vm.currentUserId,
            };
            services.getListByGroupId(data, settings).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.total = result.totalCount;
                    vm.tableData = result.dataList;
                }
            })
        },
        getData(groupId, filterTxt) {
            this.groupId = groupId;
            this.inputValueTable = filterTxt;
            this.changePage(1);
        },
        searchTableEvent() {
            this.changePage(1);
        },
        initTableData(treeNodeData) {
            let {id, currentUserId} = treeNodeData;
            this.currentUserId = currentUserId;
            this.groupId = id;
            this.changePage(1);
        },
        changeSize(val) {
            this.paginationProps.pageSize = val;
            this.changePage(1);
        },
        addVisualPanel(name = "", dataSetId = "") {
            this.$refs.move.show('', name, [], '新建仪表盘', dataSetId, this.groupId);
        },
        newAdvanced(row) {
            this.$refs.advanced.newDialogAdvanced(row.id);
        },
        moveToSave(groupId) {
            this.getData(groupId, '');
            this.$emit('changeNodeKey', groupId);
        },
        newModel(id, dataSetId) {
            this.$emit('showPanel', 'edit', id, this.inputValueTable, this.groupId, dataSetId);
        },
        viewPanel(row) {
            let routeData = this.$router.resolve({path: '/datacenter/visualview', query: {rowid: row.id}});
            window.open(routeData.href, '_blank');
        },
        editPanel(row, index) {
            this.$emit('showPanel', 'edit', row.id, this.inputValueTable, this.groupId, row.dataSetId);
        },
        copyPanel(row, index) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            /*vm.confirm("复制", `确认复制 \'${row.name}\' 仪表盘？`, () => {
                services.copy(row.id).then(res => {
                    if (res.data.status === 0) {
                        let id = this.groupId ? this.groupId !== '1' && this.groupId !== '0' ? this.groupId : '' : '';
                        this.getData(id, '');
                    }
                })
            })*/

            const saveInter = (params, settings) => {
                return services.copy({
                    id: row.id,
                    groupId: params.classifyId,
                    dashboardName: params.transName
                }, settings)
            }
            const modelTypeName = '仪表盘';
            const layer = this.$dgLayer({
                title: `${modelTypeName}另存为`,
                content: require("@/components/signed-components/save-model"),
                area: ["800px", "600px"],
                move: false,
                maxmin: false,
                props: {
                    modelTypeName,
                    saveInter,
                    showItems: ['transName', 'pos'],
                    loadTreeInter: (settings) => {
                        return services.queryTree(settings).then(res => {
                            if (res.data.status === 0) return vm.setTreeData(res.data.data)
                        })
                    },
                    treeWrapHeight: '350px',
                    name: row.name,
                    dirId: row.groupId,
                },
                on: {
                    submit(data) {
                        layer.close(layer.dialogIndex);
                        let id = data.classifyId;
                        if (id) vm.getData(id, '');
                    }
                }
            })
        },
        setTreeData(data) {
            const vm = this;
            let myList = data.myList;
            let my = {
                label: '我的空间',
                name: 'myFile',
                id: '1',
                pId: "0",
                icon: "dg-iconp icon-notebook",
                isParent: true,
                children: vm.getChildList(myList, "1"),
                currentUserId: vm.userInfo?.id,
            };

            return [my];
        },
        getChildList(list, pId) {
            if (!list) return;
            const vm = this;
            return list.map(d => {
                let children = d.children && d.children.length ?
                    vm.getChildList(d.children, d.id) : [];
                return {
                    id: d.id,
                    pId: pId,
                    name: d.name,
                    label: d.name,
                    isParent: false,
                    children,
                    isActive: false,
                    currentUserId: d.operateUserId,
                };
            });
        },
        moveTo(row) {
            this.$refs.move.moveShow(row);
        },
        deletePanel(row) {
            const vm = this, {visualServices, visualMock} = this;
            let services = vm.getServices(visualServices, visualMock);
            vm.confirm("删除", `确定删除\"${row.name}\"仪表盘吗?`, () => {
                services.deleteFn(row.id).then(res => {
                    if (res.data.status === 0) {
                        vm.$message.success("删除成功!");
                        vm.changePage(1);
                    }
                })
            })
        },
        reNewTable() {
            this.changePage(1);
        },
        rowShare(row) {
            const vm = this;
            let list = [];
            list.push(row);
            vm.$nextTick(() => {
                this.$refs.auth.show(list, "", "visualizing");
            })
        },
        /**
         * 批量分享
         */
        share() {
            // if (this.multipleSelection.length === 0) {
            //     this.$message.warning("请选择要分享的服务");
            //     return;
            // }
            // this.$refs.auth.show(this.multipleSelection,"", "serviceManage");
            const vm = this;
            let layer = this.$dgLayer({
                title: "批量分享",
                content: require("@/projects/DataCenter/views/serviceManage/dialog/addShare"),
                move: false,
                props: {
                    isVisualizing: true,
                },
                btn: ['确定', '取消'],
                on: {
                    refresh() {
                        vm.changePage(1);
                    },
                    close() {
                        layer.close(layer.dialogIndex);
                    }
                },
                yes: function (index, layero) {
                    layer.$children[0].visualizingSave(index);
                },
                cancel: function (index, layero) {
                    // 关闭对应弹窗的ID
                    layer.close(index);
                    return false;
                },
                area: ["1000px", "600px"],
                btnAlign: 'r',

            });
        },
    },
    created() {
        // this.changePage(1);
    }
}
