<template>
    <div class="analyse data-set" v-loading="settings.loading">
        <dg-card class="box-card" shadow="never" :body-style="bodyStyle" :none-border="true">
            <template slot="header">
                <header-page :datasetId="new_datasetId !== '' ? new_datasetId : datasetId" :treeNode="treeNode" :treeNodeId="treeNodeId" :name="name"
                             @showSaveOther="showSaveOther"
                             @editColumnStep="editColumnStep"
                             @addColumnStep="addColumnStep"
                             @filterConditionStep="filterConditionStep"
                             @joinTableStep="joinTableStep"
                             @syncColumnStep="syncColumnStep"
                             @saveDataset="saveDataset"
                             @deleteStepSuccess="deleteStepSuccess"
                             @unionTableStep="unionTableStep"
                />
            </template>
            <tree-and-list class="data-set-content" v-if="hasData" left-width="220">
                <tree slot="left" ref="columns" :datasetId="new_datasetId !== '' ? new_datasetId : datasetId" @editSuccess="editSuccess"
                      @preview="preview" @initTree="initTree"></tree>
                <list ref="list" :datasetId="datasetId_for_req"
                      @mergeSuccess="mergeSuccess"
                      @getAllColumns="getAllColumns"
                      @appendColumns="appendColumns"></list>
                <div slot="other">
                    <rapid-form ref="saveOther" @saveOther="saveOther" ></rapid-form>
                </div>
            </tree-and-list>
            <nodata v-else @addDataset="addDataset"></nodata>
            <add-dataset ref="addDataset" :datasetId="new_datasetId !== '' ? new_datasetId : datasetId" @initColumnsTree="initColumnsTree"></add-dataset>
            <mergeDatasetList @show="show()" ref="unionDatashow" ></mergeDatasetList>
        </dg-card>
    </div>
</template>

<script src="./analyse.js"></script>
<style scoped lang="less" src="./analyse.less"></style>