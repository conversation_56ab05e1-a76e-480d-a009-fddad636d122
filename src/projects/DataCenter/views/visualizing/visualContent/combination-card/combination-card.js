import {chartMxins} from "../chart-mixins/chart-mixins";
import {globalBus} from "@/api/globalBus";
import {common} from "@/api/commonMethods/common";
import numerify from 'numerify'

export default {
    name: "combination",
    mixins: [chartMxins, common],
    data() {

        return {
            chartSettings: {
                showLine: [],
                type: "histogram",
                label: {
                    show: false,
                    position: "outside"
                },
                axisSite: {
                    right: []
                },
                yAxisName: []
            },
            chartData: {},
            extend: {
                title: {
                    show: false,
                    text: "111111"
                },
                tooltip: {
                    trigger: 'axis',
                    appendToBody: true,
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                // toolbox: {
                //     feature: {
                //         dataView: {show: true, readOnly: false},
                //         magicType: {show: true, type: ['line', 'bar']},
                //         restore: {show: true},
                //         saveAsImage: {show: true}
                //     }
                // },
                legend: {},
                dataZoom: [{
                    type: 'inside',
                    start: 0,
                    filterMode: "filter",
                    maxValueSpan: 200
                }, {
                    show: true,
                    filterMode: "filter",
                    start: 0,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '100%',
                    height: 16,
                    showDetail: false,
                    xAxisIndex: 0,
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }, {
                    type: 'slider',
                    show: true,
                    yAxisIndex: 0,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '100%',
                    top: "50",
                    height: "70%",
                    width: "16",
                    showDetail: false,
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    },
                    start: 0,
                    left: "10"
                }],
                series: [],
                xAxis: {
                    nameLocation: 'end',
                    offset : 8,
                    nameTextStyle: {
                        align: 'left',
                        verticalAlign: 'top',
                    },
                    nameRotate: 0,
                    axisLine: {
                        lineStyle: {}
                    },
                    axisLabel: {
                        interval: 0,
                    },
                    position: 'bottom'

                },
                yAxis: {
                    nameLocation: 'end',
                    nameRotate: 0,
                    axisLine: {
                        lineStyle: {}
                    },
                    axisLabel: {
                        interval: 0,
                        verticalAlign:"bottom"
                    },
                    position: "left"
                }
            },
        }
    },
    methods: {
        getNodeData() {
            globalBus.$on('setCombinationWidgetPanel', this.setHistogramPanel);
        },
        /**
         * 配置数据标签 （不能删除该方法）
         * @param show
         * @param pos
         */
        setDataLabel(show, pos) {
            const vm = this;
            vm.chartSettings.label.show = show;
            vm.chartSettings.label.position = pos;
        },
        setChartExtend(settings) {
            this.setTitle(settings.title);
            this.setLegend(settings.legend);
            this.setAxis(settings.xAxis, 'xAxis');
            this.setAxis(settings.yAxis, 'yAxis');
            this.setAlias(settings.fieldSetting);
            this.setTooltip();
            this.setChartSettings(settings.fieldSetting, settings);
        },
        /**
         * dimensionT 横轴名称
         * hasTitle 是否度量数据 再显示名称
         */
        setTooltip() {
            const node = this.node, vm = this;
            vm.extend.tooltip.formatter = function (params) {
                let result = "", dimensionT = "", hasTitle = false;
                let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
                let xLabel = "(" + (nodeData.yData[0].alias || nodeData.yData[0].label) + ")";
                if (vm.chartData && vm.chartData.dimsCodes && vm.chartData.dimsCodes.length) xLabel = `(${vm.chartData.dimsCodes[0]})`;
                dimensionT = `<div class="f16 mb5">${params[0].name + xLabel} :</div>`;
                params.forEach((item, inx) => {
                    let funcLabel = "";
                    let paramsVal = "", fast = "";
                    if (+item.value || +item.value === 0) {
                        hasTitle = true;
                        paramsVal = nodeData.xData[inx].data.numberFormat && nodeData.xData[inx].data.numberFormat !== 'none' ? numerify(item.value, vm.format[nodeData.xData[inx].data.numberFormat]) : item.value;
                        fast = nodeData.xData[inx].fastCount && nodeData.xData[inx].fastCount.value && nodeData.xData[inx].fastCount.value !== "none" ?
                            '-' +nodeData.xData[inx].fastCount.name : "";
                        funcLabel = '[' + nodeData.xData[inx].func.name +  fast+ ']';
                        let reg = /\(.*\)/g;
                        let seriesName = item.seriesName || item.name; seriesName = seriesName.replace(reg,'');
                        result += `<div></div><span class="dib vm mr5" style="width:14px;height: 14px;border-radius:4px;background:${item.color};"></span>\
                                        <span>${seriesName} : ${paramsVal} ${funcLabel}</span></div>`;
                    }
                });

                if (hasTitle) result = dimensionT + result;
                return result;
            };
        },
        setAxis(data, axis) {
            if (!data) return;
            const vm = this, {extend, drillLevel} = this;
            let widget = JSON.parse(vm.node.widget), nodeData = JSON.parse(widget.beforeData);

            if (axis === 'xAxis') {
                if (data.showName) {
                    if (vm.filterDimsName && axis === "xAxis") {
                        extend[axis].name = vm.chartData.dimsCodes.join(">");
                    } else {
                        extend[axis].name = data.text;
                    }
                } else {
                    extend[axis].name = "";
                }
            }
            extend[axis].nameLocation = data.nameLocation;
            extend[axis].axisLine.show = data.show_axisLine;
            extend[axis].axisLine.lineStyle = {
                color: data.line_color,
                width: data.line_width,
                type: data.line_type
            };
            extend[axis].axisLabel.rotate = data.axisLabel;
            data.nameRotate !== undefined ? extend[axis].nameRotate = data.nameRotate : true;
        },
        setChartSettings(fieldSetting, settings) {
            const vm = this;
            vm.chartSettings.showLine = fieldSetting.fields;
            vm.chartSettings.axisSite.right = [];
            vm.chartSettings.yAxisName = [];
            vm.extend['yAxis'].position = "left";
            vm.chartSettings.yAxisName[0] = settings.yAxis.text;
            if (fieldSetting.y_show) {
                vm.extend['yAxis'].position = "";
                vm.chartSettings.axisSite.right = fieldSetting.y_value ? fieldSetting.y_value : [];
                vm.chartSettings.yAxisName[1] = fieldSetting.y_nameShow;
            }
        },
        /**
         * 设置数据格式
         * @param measure
         */
        set_digital_format(measure) {
            const vm = this;
            let formats = {};
            measure.forEach((item, i) => {
                if (item.data.numberFormat && item.data.numberFormat !== "none") {
                    let key = item.data.fieldAlias || item.data.code;
                    formats[item.data.code] = item.data.numberFormat;
                }
            });
            vm.chartSettings.label.formatter = function (v) {
                return formats[v.seriesName] ? numerify(v.value, vm.format[formats[v.seriesName]]) : v.value;
            }
        },
        /**
         * 返回数据 绘制图表
         * @param msg
         */
        getMessage(msg) {
            const vm = this;
            let result = JSON.parse(msg.data), data = result.data;
            if (vm.socket) vm.socket.close();
            if (result.status === 0 && data) {
                vm.chartData = data;
                let {beforeData} = JSON.parse(vm.node.widget),
                    nodeData = JSON.parse(beforeData);
                vm.set_digital_format(nodeData.xData);
                vm.setChartExtend(nodeData.style);
                vm.setZoom(data);
                vm.data_empty = false; //没有数据为true
                vm.loading = false;
            } else {
                vm.data_empty = true; //没有数据为true
                vm.loading = false;
                vm.chartData = {};
            }
        },
    }
}