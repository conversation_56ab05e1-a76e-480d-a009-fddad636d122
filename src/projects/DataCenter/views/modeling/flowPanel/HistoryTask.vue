<template>
    <div>
        <common-dialog custom-class="log" :width="width" title="历史任务日志"
                       :visible.sync="dialogFormVisible"
                       @closed="clearD"
        >
            <common-table
                    height="calc(75vh - 112px)"
                    :data="tableData"
                    :columns="tHeadData"
                    :pagination="false"
                    v-loading="loadData"
            >
                <template slot="operate" slot-scope="{row , $index}">
                    <template v-for="(col , inx) in operateIcons">
                        <el-button
                                type="text"
                                :key="inx"
                                v-if="col.show(col)"
                                :title="col.tip"
                                v-html="col.tip"
                                @click="col.clickFn(row,$index)"
                        ></el-button>
                    </template>

                </template>

            </common-table>

        </common-dialog>
    </div>
</template>

<script>
    export default {
        name: "HistoryTask",
        components:{},
        data() {
            return {
                loadData  :false ,
                dialogFormVisible: false,
                width: '1200px',
                active: true,
                openDialog: false,
                jobId:1,
                rowData: {},
                tableData: [],
                tHeadData: [
                    {
                        prop: "groupId",
                        label: "主键组",
                        minWidth: "140",
                        align: "left"
                    }, {
                        prop: "description",
                        label: "描述",
                        align: "center"
                    },
                    {
                        prop: "name",
                        label: "名称",
                        align: "center"
                    },
                    {
                        prop: "status",
                        label: "执行状态",
                        align: "center"
                    },
                    {
                        prop: "submissionTime",
                        label: "提交时间",
                        minWidth: "200",
                        align: "center"
                    },
                    {
                        prop: "completionTime",
                        label: "完成时间",
                        minWidth: "200",
                        align: "center"
                    },
                    {
                        prop : "operate",
                        label :"详情",
                        align : "center",
                        width : '200',
                        resizable : false


                    },
                ],

                operateIcons: [
                    //{icon: "&#xe660;", tip: "查看历史任务日志",show : ()=> true, clickFn: this.historyTask},
                    /*  {icon: "&#xe72c;", tip: "查看历史执行日志",show : (isSparkTask)=> !isSparkTask, clickFn: this.historyExecute},*/
                    {icon: "&#xe76b;", tip: "详情", show : ()=> true , clickFn: this.logDetails},
                    //{icon: "&#xe65f;", tip: "删除日志", show : ()=> true,clickFn: this.deleteLog},
                ],
                taskInfo: {},
            };
        },
        methods: {
            show(row,HistoryTableData) {
                this.taskInfo = row;
                this.dialogFormVisible = true;
                const _this = this;
                _this.loadData = false;
                _this.tableData=HistoryTableData;
            },
            // changePage(index){
            // },
            logDetails(row) {
                let services = this.$services('modeling');
                services.getJumpLogUrl({jobId : row.jobId, groupId: row.groupId})
                .then(res => {
                    if (res.data.status === 0) {
                        window.open(res.data.data, '_blank');
                    }
                })
            },
            insertStr(soure, start, newStr) {
                return soure.slice(0, start) + newStr + soure.slice(start);
            },
            clearD() {
                this.openDialog = false;
            }
        },
    }
</script>

<style scoped>
    .pagination {
        margin: 5px 0;
        text-align: center;
    }
</style>
