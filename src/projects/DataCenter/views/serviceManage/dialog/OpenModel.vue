<template>
    <div class="openModel">
        <dg-dialog :close-on-click-modal="false"
                   :width="width"
                   :title="title"
                   :visible.sync="dialogVisible"
                   @closed="clearD"
                   :append-to-body="true"
                   class="model-dialog"
        >
            <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="documentCode"></help-document></div>
            <ModelTree @nodeClick="checkNode" :row="row" has-save v-if="reNew" :dirId="row.classifyId"/>
            <div slot="footer">
                <el-button @click="close" size="mini">{{btnCancelTxt}}</el-button>
                <el-button @click="checked" type="primary" size="mini">{{btnCheckTxt}}</el-button>
            </div>
        </dg-dialog>
    </div>
</template>

<script>
import ModelTree from './ModelTree'
import {servicesMixins} from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";

export default {
    name: "OpenModel",
    mixins: [servicesMixins],
    data() {
        return {
            width: '800px',
            dialogVisible: false,
            reNew: false,
            classifyId: "",
            title: "",
            treeNode: "",
            row: {},
            treeType: "",
            operType: "",
            isSQLModel: false,
            modelType : "",//模型类型 ， 快速分析 ， 建模
            documentCode: "",
        }
    },
    components: {
        ModelTree,
    },
    methods: {
        getCenterTable(data) {
            if (data.parentId !== "-1") {
                this.treeNode = {
                    transId: data.id,
                };
            }
        },
        checkNode(label, value, node) {
            this.treeNode = node.data;
        },
        /**
         * 打开
         * @param row
         * @param type
         * @param operType
         * @param mType
         */
        open(row) {
            // this.documentCode = this.modelType === 'QUICK_SPARK' ? 'fastAnalysisQueryTransTree' : '';
            this.row = row;
            this.dialogVisible = true;
            this.reNew = true;
            this.title = "移动到";
        },
        close() {
            this.dialogVisible = false;
        },
        /**
         * 确定
         */
        checked() {
            const vm = this,{settings} = this;
            if (!vm.treeNode.id ) {
                return vm.$message.warning("请选择目录！");
            }
            let services = vm.$services("serviceManage");
            let param = {
                serviceId:  vm.row.serviceId,
                classifyId: vm.treeNode.id,
            }
            settings.loading = true;
            services.moveModelService(param,settings).then(res => {
                if (res.data.status === 0) {
                    vm.$message.success("移动成功");
                    vm.$emit("update");
                    vm.dialogVisible = false;
                } else {
                    vm.$message.warning(res.data.msg);
                }
            })

        },
        clearD() {
            this.reNew = false;
        }
    }
}
</script>

<style scoped lang="less">
.model-dialog {
    .dataTree {
        width: 100%;
        height: calc(75vh - 192px);
        margin: 0 auto;
        float: inherit;
        box-sizing: border-box;

        /deep/ .node-label {
            max-width: 100%;
        }
    }
}
</style>
