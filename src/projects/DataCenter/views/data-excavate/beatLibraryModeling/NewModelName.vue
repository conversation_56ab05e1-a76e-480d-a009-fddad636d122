<template>
    <div class="newModelName">
        <el-form :model="nameForm" :rules="rules" size="small" ref="form">
            <el-form-item :label="label.chLabel" prop="chName" label-width="120px">
                <el-input v-model.trim="nameForm.chName" ref="name"></el-input>
            </el-form-item>
            <el-form-item :label="label.engLabel" prop="engName" label-width="120px" v-show="reType === 'model'">
                <el-input v-model.trim="nameForm.engName"></el-input>
            </el-form-item>
            <el-form-item label="保存位置:" prop="savePath" label-width="120px" v-show="isNew">
                <el-select v-model="nameForm.savePath" placeholder="请选择位置:">
                    <el-option
                            v-for="item in pathOpt"
                            :label="item.label"
                            :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "NewModelName",
        props: {
            label: Object,
            treeNode : {
                type : Object ,
                default : ()=>{
                    return {};
                }
            }
        },
        data() {
            return {
                reType:"",
                isNew: true,
                pathOpt: [],
                nameForm:
                    {
                        chName: '',
                        engName: '',
                        savePath: '',
                    }
                ,
                rules: {
                    chName: [
                        {required: true, message: '请输入中文名', trigger: 'blur'},
                        {required: true, message: '请输入中文名', trigger: 'change'},
                    ],
                    engName: [
                        {required: true, message: '请输入英文名', trigger: 'blur'},
                        {required: true, message: '请输入英文名', trigger: 'change'},
                    ],
                    savePath: [
                        {required: true, message: '请输入中文名', trigger: 'blur'},
                        {required: true, message: '请输入中文名', trigger: 'change'},
                    ],
                }
            }
        },
        methods: {
            validate(){
                this.$refs.form.validate(value =>{})
            },
            showSelect(type) {
                this.reType = type;
                this.isNew = true;
            },
            setSelect() {
                this.isNew = false;
            },
            getFormData() {
                return this.nameForm;
            },
            setName(row,type) {
                this.reType = type;
                this.isNew = false;
                this.nameForm.chName = row.name;
                this.nameForm.engName = row.code;

            },
            clearName() {
                this.nameForm.chName = "";
                this.nameForm.engName = "";
            },
        },
        created() {
            const _this = this;
            _this.$axios.get("/manage/sql/getMyTreeNode").then(res => {
                if (res.data.status === 0) {
                    if(res.data.data.length >0){
                        _this.pathOpt = [];
                        res.data.data.forEach(item => {
                            _this.pathOpt.push({
                                label: item.name,
                                value: item.id,
                            })
                        });
                        _this.nameForm.savePath = _this.treeNode.id && _this.treeNode.parentId !== '-1' ? _this.treeNode.id : _this.pathOpt[0].value;
                    }
                }
            }).catch(err => {
                console.log(err);
                this.$message.error("服务器异常，请联系管理员！");
            })
        }
    }
</script>

<style scoped>

</style>