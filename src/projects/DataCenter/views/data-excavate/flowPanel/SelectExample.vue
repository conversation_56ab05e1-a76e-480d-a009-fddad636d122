<template>
    <el-dialog :close-on-click-modal="false" :width="width" title="选择实例"
               :visible.sync="dialogFormVisible"
               @closed="clearD"
               :append-to-body="true"
               custom-class="ce-prompt"
    >
        <el-form :model="form" size="mini" :label-width="formLabelWidth" :inline="true">
            <el-form-item :label="selectLabel">
                <el-select v-model="form.selectValue" :placeholder="placeholder">
                    <el-option
                            v-for="item in exampleData"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button v-show="active" @click="stop" size="mini">取消</el-button>
            <el-button @click="confirm" type="primary" size="mini">确定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    export default {
        name: "SelectExample",
        data() {
            return {
                active : true ,
                selectLabel:"运行实例:",
                form:{
                    selectValue:"",
                },
                exampleData:[],
                placeholder:"请选择实例",

                dialogFormVisible: false,
                width: '400px',

                openDialog: false,

                formLabelWidth:"105px",
            }
        },
        methods:{
            show(rowData,list,instance){
                this.rowData = rowData;
                this.form.selectValue = list;
                this.exampleData = [];
                list.forEach(item =>{
                    let tableNode = {
                        value:item,
                        label:item,
                    };
                    this.exampleData.push(tableNode);
                });
                this.form.selectValue =this.exampleData[0].value;
                this.dialogFormVisible = true;
                this.form.selectValue = instance;
            },
            clearD(){
                this.openDialog = false;
            },
            confirm(){
                const _this = this;
                _this.$emit("saveValue",_this.form.selectValue);
                _this.$axios.get("/transSchedule/updateTransInstance?transId="+_this.rowData.transId+"&instanceCode="+_this.form.selectValue).then(res =>{
                    if(res.data.status === 0){
                        this.dialogFormVisible = false;
                        this.$message.success("更新实例成功!");
                        _this.$emit("saveValue",_this.form.selectValue);
                    }else{
                        _this.$message.error(res.data.msg);
                    }
                }).catch(err =>{
                    console.log(err);
                    _this.$message.error("服务器异常，请联系服务员！")
                })
            },
            stop(){
                this.dialogFormVisible = false;
            },
        },
    }
</script>

<style scoped>

</style>