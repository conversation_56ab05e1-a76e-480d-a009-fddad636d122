<template>
    <div class="cond-filter__content height100">
        <div class="cond-filter__wrap-table">
            <div class="cond-filter__wrap-header">
                <div class="cond-filter__wrap-actions">
                    <el-radio-group v-model="btnActive">
                        <el-radio-button label="预览数据"></el-radio-button>
                        <el-radio-button label="字段列表"></el-radio-button>
                    </el-radio-group>
                    <div class="cond-filter__wrap-reflash" @click="refreshPreview"><i class="dg-iconp icon-refresh"></i></div>
                </div>
                <div class="page-size">
                    <el-input placeholder="请输入字段名搜索" v-model.trim="filterText" size="medium" class="ce-search mr5" v-if="btnActive=='字段列表'" @keyup.enter.native="findColumn">
                        <i slot="suffix" class="el-input__icon el-icon-search poi" @click="findColumn"></i>
                    </el-input>
                    <div v-else>
                        <el-select
                                v-model="selHead"
                                multiple
                                filterable
                                remote
                                clearable
                                class="selectHead"
                                collapse-tags
                                placeholder="选择显示字段"
                                @change="refresh"
                                @focus="refresh"
                                :remote-method="remoteMethod">
                            <el-option
                                    v-for="item in options"
                                    :key="item.prop"
                                    :label="item.label"
                                    :value="item.label">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="cond-filter__wrap-list">
                <div v-if="btnActive === '预览数据'" class="height100">
                    <common-table
                            row-key=""
                            :data="previewInfo.previewTableData"
                            :columns="tHeadDataPreview"
                            :paging-type="isMock ? 'client' : 'server'"
                            :pagination-props="paginationPropsPre"
                            @change-size="changeSizePre"
                            @change-current="isMock ?  ()=>{} : changePagePre($event)"
                            :pagination-total="previewTotal"
                            class="width100 preview"
                            v-if="!isErr"
                            v-loading="previewInfo.settings.loading || previewInfo.loading"
                    >
                        <template :slot="item" slot-scope="{row,$index}" v-for="item in blobList">
                            <el-image
                                    style="width: 80px;height:50px;"
                                    :src="row[item]"
                                    fit="contain"
                                    :preview-src-list="row.srcList"
                            >
                            </el-image>
                        </template>
                        <template v-for="(item , i) in restTableHead" #[item.prop]="{row}">
                            <el-dropdown v-if="item.hasLink && row[item.prop]" :key="i" @command="handleCommand($event , row[item.prop], row)">
                                <span class="el-dropdown-link">
                                    <span>{{row[item.prop]}}</span>
                                    <i class="el-icon-arrow-down el-icon--right"></i></span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item v-for="(opt , k) in item.linkOptions" :key="k"
                                                      :command="opt"
                                    >{{opt.name}}</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </common-table>
                    <div v-if="isErr" class="height100">
                        <preview-error ref="previewError" :errMsg="errMsg" @previewAgain="previewList"></preview-error>
                    </div>
                </div>

                <common-table
                        :data="previewInfo.columnTableData"
                        :columns="previewInfo.tHeadDataColumn"
                        paging-type="client"
                        :pagination-props="paginationProps"
                        class="width100 preview"
                        v-else
                        v-loading="previewInfo.settings.loading"
                        :pagination-total="columnTotal"
                        @change-size="changeSize"
                        @change-current="changePage"
                >
                    <div slot="code" slot-scope="{row,$index}" >
                        <span :class="{'red':row.isRed}">
                            {{row.columnAlias || row.code}}
                        </span>
                        <el-tooltip content="提示信息：该字段已删除" placement="right" effect="light" v-if="row.isRed">
                            <i class="el-icon-warning-outline" ></i>
                        </el-tooltip>
                    </div>
                </common-table>

            </div>
        </div>
    </div>
</template>

<script>
    import previewError from "./previewError";
    export default {
        name: "preview",
        components:{
            previewError
        },
        props:{
            previewInfo:Object,
        },
        watch:{
            "previewInfo.settings.loading":{
                handler(val){
                    // this.init();
                },
                deep : true
            },
        },
        data(){
            return{
                isErr:false,
                visibleErr:false,
                options:[],
                selHead:[],
                tHeadDataPreview:[],
                paginationPropsPre:{
                    currentPage: 1,
                    pageSizes: [10, 20, 30],
                    pageSize: 10,
                },
                paginationProps:{
                    currentPage: 1,
                    pageSizes: [10, 20, 30],
                    pageSize: 10,
                },
                columnTableData:[],
                previewTotal:0,
                columnTotal:0,
                filterText:'',
                btnActive:'预览数据',
                errMsg:'',
                blobList: [],
            }
        },
        computed:{
            // 除了图片数据 剩下的表头
            restTableHead(){
                return this.tHeadDataPreview.filter(li => !this.blobList.includes(li.prop));
            },
        },
        methods:{
            getValueByKeys(keys=[],row) {
                for(let k of keys) {
                    if(row[k]) return row[k]
                }
            },
            handleCommand(opt, value, row){
                const { linkFieldKeys } = this.$globalConfig;
                const {mark , url} = opt;
                let valSec = mark === "person" ? ( linkFieldKeys && this.getValueByKeys(linkFieldKeys["person"], row) || row['姓名'] || row['xm']) : "";
                let linkUrl = url.replace(/{userToken}/,sessionStorage.getItem("userToken")||"")
                    .replace(/{value}/,value)
                    .replace(/{value1}/,valSec);
                window.open(linkUrl);
            },
            init(){
                const vm = this;
                vm.$nextTick(()=>{
                    vm.previewTotal = vm.previewInfo.totalCount;
                    vm.options = vm.previewInfo.tHeadDataPreview;
                    vm.tHeadDataPreview = vm.previewInfo.tHeadDataPreview;
                    vm.columnTotal = vm.previewInfo.columnTableData.length;
                    vm.columnTableData = vm.previewInfo.columnTableData;
                    vm.isErr = vm.previewInfo.isErr;
                    vm.errMsg = vm.previewInfo.errMsg;
                    let errColumns = vm.previewInfo.errColumns ? vm.previewInfo.errColumns.map(n=>n.id) :[];
                    vm.blobList = [];
                    vm.columnTableData.forEach(n=>{
                        if(errColumns.includes(n.id)){
                            n.isRed = true;
                        }
                        if(n.dataType && (n.dataType.toLowerCase() === "blob" || n.dataType.toLowerCase() === 'binary')){
                            vm.blobList.push(n.name || n.code)
                        }
                    })
                    vm.previewInfo.previewTableData.forEach((n,i)=>{
                        for(let key in n){
                            if(vm.blobList.includes(key)){
                                n[key] = 'data:image/png;base64,' + n[key];
                                n.srcList = [n[key]];
                            }
                        }
                    })
                    vm.refresh();
                })
            },
            refreshPreview(){
                this.$emit('getPreview');
            },
            refresh(){
                this.tHeadDataPreview = [];
                this.options = this.previewInfo.tHeadDataPreview;
                if(this.selHead.length){
                    this.previewInfo.tHeadDataPreview.forEach(n=>{
                        if(this.selHead.includes(n.prop)){
                            this.tHeadDataPreview.push(n);
                        }
                    })
                }
                else{
                    this.tHeadDataPreview = this.previewInfo.tHeadDataPreview;
                }
            },
            changePagePre(index){
                this.paginationPropsPre.currentPage = index;
                this.$emit('refresh',index);
            },
            changePage(index){
                this.paginationProps.currentPage = index;
            },
            changeSize(val){
                this.paginationProps.pageSize = val;
            },
            changeSizePre(val){
                this.paginationPropsPre.pageSize = val;
                this.$emit('refresh',1);
            },
            findColumn(){
                let filterText=this.filterText;
                if(filterText!=='')
                    this.previewInfo.columnTableData=this.previewInfo.columnTableDataCopy.filter(n=>(n.name ? n.name.toLowerCase().search(filterText.toLowerCase())!==-1:false)||n.code.toLowerCase().search(filterText.toLowerCase())!==-1);
                else
                    this.previewInfo.columnTableData=this.previewInfo.columnTableDataCopy;
            },
            remoteMethod(query) {
                if (query !== '') {
                    setTimeout(() => {
                        this.options = this.previewInfo.tHeadDataPreview.filter(item => {
                            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
                        });
                    }, 200);
                } else {
                    this.options = this.previewInfo.tHeadDataPreview;
                }
            },
            //重新预览
            previewList(){
                this.$emit('refresh')
            }
        }
    }
</script>

<style scoped lang="less">
    .card-content{
        height:55vh;
    }
    .cond-filter {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        /deep/.card-content{
            height:100%;
            .el-card__body{
                height: 100%;
                .cond-filter__content{
                    height: 100%;
                }
            }
        }
        /deep/.foldCard{
            .el-card__body{
                max-height: 190px;
                overflow-y: auto;
            }
        }
        /deep/.el-card {
            flex-shrink: 0;
            height: auto;
            box-sizing: border-box;
            &__body{
                /*max-height: 200px;*/
                /*overflow-y: auto;*/
            }
        }
        .el-card + .el-card {
            margin-top: 14px;
        }

        &__header &__content {
            // padding: 14px 30px;
        }
        &__wrap {
            &-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .selectHead{
                    width:240px;
                }
            }
            &-actions {
                display: flex;
                align-items: center;
            }
            &-reflash {
                text-align: center;
                //line-height: 30px;
                //width: 30px;
                //height: 30px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                border-radius: 2px;
                margin-left: 4px;
                cursor: pointer;
                i {
                    color: rgba(0, 0, 0, 0.45);
                }
                &:hover {
                    i {
                        color: #0088ff;
                    }
                }
            }
            &-table{
                height:100%;
            }

            &-list {
                height:calc(100% - 35px);
                padding-top: 14px;
                .preview{
                    overflow-y: hidden;
                    /deep/ .el-table {
                        height:calc(100% - 65px);
                    }
                }
                .red{
                    color:red;
                }
                .error{
                    border:1px solid #dcdcdc;
                    text-align: center;
                    height:95%;
                    display: flex;
                    align-items: center; /*定义body的元素垂直居中*/
                    justify-content: center;
                    .previewError{
                        h1{
                            margin-bottom:20px;
                            font-size: 30px;
                        }
                    }
                }
                /deep/ .el-table__body-wrapper{
                    overflow-y: auto;
                    height:calc(100% - 50px);
                }
                .dataList{
                    height: 100%;
                    .dg-table{
                        height: 100%;
                        /deep/ .dg-table__content{
                            height: calc(100% - 90px);
                            overflow-y: auto;
                        }
                        /deep/.el-table{
                            height:100%;
                        }
                    }
                }

            }
        }
    }
    .comm__header {
        padding: 16px 28px;
    }
    // 图标按钮组
    .icon-group {
        display: flex;
        align-items: center;
        .el-link {
            line-height: 1;
            margin-right: 4px;
        }
        &:before {
            content: "";
        }
    }
    .filter-box {
        flex-grow: 1;
        /deep/.el-card__body {
            position: relative;
            padding-top: 8px;
            padding-bottom: 0;
        }
        &__add {
            position: absolute;
            top: 8px;
            right: 32px;
            z-index: 1;
        }
    }
    .filter-group {
        color: #0088ff;
        display: flex;
        margin-bottom: 24px;

        &__logic {
            flex: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 16px;
            padding-top: 4px;
            &:after {
                flex: 1;
                content: "";
                width: 2px;
                background-color: rgba(#0088ff, 0.12);
            }
        }
        &__main {
            flex: 1;
        }
        &__header {
            display: flex;
            align-items: center;
            .logic-select {
                margin: 0 12px;
            }
        }

        &__btns {
            margin-left: 14px;
        }
        &:not(:hover) &__btns {
            display: none;
        }

        .filter-item {
            margin-top: 14px;
        }
    }

    .filter-item {
        display: flex;

        &__field,
        &__value {
            flex: none;
            width: 300px;
        }
        &__operator {
            flex: none;
            width: 84px;
            margin: 0 6px;
        }
        &__btns {
            margin-left: 14px;
            .el-link {
                font-size: 16px;
            }
        }
        &:not(:hover) &__btns {
            display: none;
        }
    }

    // 逻辑选择器样式
    .logic-select {
        width: 84px;

        /deep/.el-input__inner {
            border-color: #0088ff;
            color: #0088ff;
        }
        /deep/.el-input__icon {
            color: #0088ff;
        }


        &--thin {
            width: 42px;
            height: 24px;


            /deep/.el-input__inner {
                padding: 0 12px 0 6px;
                background-color: rgba(#0088ff, 0.12);
                border: none;
                height: 24px;
                line-height:24px;
            }
            /deep/.el-input__suffix {
                right: 4px;
                height: 24px;
            }
            /deep/.el-input__icon {
                width: 12px;
                height: 24px;
                line-height:24px;
            }
        }
    }
</style>
