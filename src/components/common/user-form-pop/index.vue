<template>
    <dg-dialog :title="title" v-bind="$attrs" v-on="$listeners" @open="reNewData" @closed="closed">
        <div slot="title"><span class="el-dialog__title">{{title}}</span> <help-document :code="functionCode"></help-document></div>
        <el-form size="small" ref="form" :model="data" label-width="100px" :hide-required-asterisk="state === 'detail'" :rules="rules" v-show="showF">
            <el-form-item :label="`${label}ID`" prop="objCode">
                <el-input placeholder="请输入ID"  v-model="data.objCode" disabled></el-input>
            </el-form-item>
            <el-form-item :label="`${label}名称`" prop="objName">
                <el-input placeholder="请输入名称(限100字符以内)" maxlength="100" :title="data.objName" v-input-filter="data.objName" v-model.trim="data.objName" :disabled="state === 'detail'"></el-input>
            </el-form-item>
            <el-form-item prop="fromAuthObj" v-if="data.userGroups" label="所属上级">
                <ce-select-drop
                        ref="tree"
                        :props="defaultProps"
                        v-model="data.fromAuthObj"
                        :data="data.userGroups"
                        :filterNodeMethod="filterNode"
                        filterable
                        :disabled="state === 'detail'"
                ></ce-select-drop>
            </el-form-item>
            <el-form-item label="编辑人">
                <el-input disabled :title="data.editUser" v-model="data.editUser"></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <dg-button v-if="state !== 'detail'" @click="onCancel">取 消</dg-button>
            <dg-button type="primary" v-if="state === 'add'" @click="onSubmit">确 定</dg-button>
            <dg-button type="primary" v-else-if="state === 'edit'" @click="update">确 定</dg-button>
            <dg-button v-else @click="onCancel">关 闭</dg-button>
        </span>
    </dg-dialog>
</template>
<script lang="js" src="./user-form-pop.js"></script>
<style lang="less" src="./user-form-pop.less"></style>
