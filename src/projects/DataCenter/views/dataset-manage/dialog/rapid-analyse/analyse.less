@color: #000;
@alpha15: .15;
@alpha65: .65;
@alpha85: .85;



.box-card {
  height: 100%;
  .el-card__body {
    height: calc(100% - 5.5rem);
  }
}

/deep/ .el-dropdown-link-rote {
  transform: rotate(90deg);
  display: flex;
  align-items: center;
}

/deep/ .el-icon-more {
  cursor: pointer;
  color: #1890ff;
}

/deep/ .tree-icon {
  display: none;
}

/deep/ .tree-item-title {
  margin-right: 8px;
}

.data-set {
  height: 100%;

  .data-set-content {
    .cont-center {
      text-align: center;
    }

    .d-s-c-left {
      // min-width: 216px;
    }

    .d-s-c-right {
      flex: 1;
      margin-left: 15px;
    }

    .cont-position {
      display: flex;
      justify-content: center;

    }

    .d-s-l-bottom {
      margin-top: 16px;
    }
  }

  .m-t-u-button {
    padding-bottom: 14px;
  }

  .m-t-u-content {
    .m-t-u-c-item {
      margin-bottom: 12px;

      .m-t-u-c-i-head {
        display: flex;
        justify-content: space-between;
        // margin-bottom: 14px;
        padding-bottom: 10px;
        box-sizing: border-box;

        label {
          font-size: 16px;
          color: rgba(#000000, 0.85);
          font-weight: bold;
        }

        button {
          padding: 0;
          height: 24px;
        }
      }

      .m-t-u-c-i-box {
        display: flex;
        justify-content: space-between;

        .m-t-u-c-i-b-item {
          width: calc(100% / 6 - 5px);
        }
      }
    }
  }

  .merge-tabs-leftright {
    .m-t-l-item {
      display: flex;
      align-items: center;
      margin-bottom: 19px;

      label {
        min-width: 80px;
        text-align: right;
        padding-right: 10px;
      }
    }

    .m-t-l-item:first-child {
      .m-t-l-i-content {
        width: 544px;
      }
    }

    .m-t-l-item:last-child {
      align-items: flex-start;

      .m-t-l-i-content {
        flex: 1;
      }
    }
  }

  .sync-data-dig {
    .s-d-d-row {
      display: flex;
      margin-bottom: 18px;

      .s-d-d-r-content {
        flex: 1;
        margin-left: 18px;
      }
    }
  }
}

.exp-content /deep/ {
  .dg-dialog__body {
    max-height: 580px;
    overflow-y: auto;
  }
}

.new-column-pop {
  display: flex;

  .new-column-pop-left {
    position: relative;
    width: 480px;
    border-right: 1px solid #e8e8e8;
    padding-right: 16px;
  }

  .new-column-pop-right {
    flex: 1;
    width: 360px;
    border: 1px solid #d7d7d7;
    margin-left: 16px;

    .new-column-pop-right-cont {
      height: 458px;
      overflow: auto;
    }

    .new-column-pop-right-header {
      height: 44px;
      line-height: 44px;
      padding: 0 8px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      background-color: rgba(0, 0, 0, 0.02);
    }

    .right-cont-item {
      padding: 16px 0;
      margin: 0 16px;

      .tip {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
      }

      .new-column-table th {
        color: rgba(0, 0, 0, 0.65);
        padding: 4px 16px 4px 0;
      }

      .new-column-table td {
        padding: 4px 16px 4px 0;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .right-cont-item + .right-cont-item {
      border-top: 1px solid rgba(0, 0, 0, 0.15);
    }
  }

  /deep/ .el-textarea__inner {
    height: 150px;
  }
}