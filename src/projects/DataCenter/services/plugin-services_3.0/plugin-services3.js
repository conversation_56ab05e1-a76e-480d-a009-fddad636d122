/**
 * 数据建模插件 接口
 * @Author: wangjt
 * @Date: 2020-07-14
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'
const root = "/cicada/plugin/"  ;
const add = '/cicada';

/*
* sql输入插件
* @params
* @returns {Promise<*>}
* */

export async function sqlView(transStepId , settings ) {
    return await reqUtil.get({
        url : root + "sql/" + "view",
        settings
    } , {transStepId})
}

/**
 * 获取数据集sql
 * @param dataSetId
 * @return {Promise<*>}
 */
export async function getLogicDataObjSQL(dataSetId){
    return await reqUtil.get({
        url : "/editDataSet/getLogicDataObjSQL"
    },{dataSetId})
}

/*
* sql 参数
* @p
* rams
* @returns {Promise<*>}
* */

export async function sqlColumns(transStepId , dataObjId ,customDataSet ,settings,logicDataSetId ) {
    return await reqUtil.get({
        url : root + "sql/" + "columns" ,
        settings : settings
    } , {transStepId , dataObjId ,customDataSet,logicDataSetId})
}
/*
* sql 预览
* @params
* @returns {Promise<*>}
* */

export async function previewCondition(transStepId , transId, dataObjId ,conditions ,cnt , settings ) {
    return await reqUtil.post({
        url : root + "sql/preview/" + "conditions?transStepId=" + transStepId + "&transId=" + transId + "&dataObjId=" + dataObjId +"&cnt="+cnt,
        settings : settings
    } , conditions )
}
export async function previewSql(transStepId , transId, dataObjId , sql ,  cnt = 10 , settings ) {
    return await reqUtil.post({
        url : root + "sql/preview/" + "sql" ,
        settings : settings
    } , {transStepId , transId, dataObjId ,cnt , sql} )
}

/**
 * 右击输入插件预览
 * @param {*} transStepId
 * @param {*} transId
 * @param cnt
 * @param jobID
 * @param {*} settings
 */
export async function previewRdb(transStepId , transId,cnt,jobID, settings) {
    let url = `cicada/plugin/sql/previewRdb?transStepId=${transStepId}&transId=${transId}&cnt=${cnt}&jobID=${jobID}`;
    return await reqUtil.get({
        url,
        settings : settings,
        errorMsg: false,
    })
}

export async function outPluginPreviewRdb(data, settings) {
    return await reqUtil.post({
        url :  "/manage/dataWarehouse/previewRdb" ,
        settings : settings,
        errorMsg: false,
    }, data)
}
/*
* SQL增量信息
* @params
* @returns {Promise<*>}
* */
export async function sql_incr(params ) {
    return await reqUtil.get({
        url : root + "sql/incr"
    },params)
}
/*
* SQL取消增量信息
* @params
* @returns {Promise<*>}
* */
export async function sql_no_incr(transStepId ) {
    return await reqUtil.get({
        url : root + "sql/no_incr"
    },{transStepId })
}
/*
* SQL保存
* @params
* @returns {Promise<*>}
* */
export async function save_sql(transStepId, dataObjId ,sql ,fetchSize ,customDataSet ,settings) {
    return await reqUtil.post({
        url : root + "sql/save/sql",
        settings : settings
    },{transStepId , dataObjId ,sql ,fetchSize ,customDataSet })
}

/*
* SQL保存
* @params
* @returns {Promise<*>}
* */
export async function save_conditions(transStepId , dataObjId ,fetchSize ,customDataSet,conditions , settings ) {
    return await reqUtil.post({
        url : root + `sql/save/conditions?transStepId=${transStepId}&dataObjId=${dataObjId}&fetchSize=${fetchSize}&customDataSet=${customDataSet}` ,
        settings : settings
    }, conditions)
}

/*高级*/
const root1 = add + "/dataModeling/";

/*
* 初始化
* @params
* @returns {Promise<*>}
* */

export async function advanceConfig(tranStepId) {
    return await reqUtil.get({
        url : root1 +  "advanceConfig"
    },{tranStepId})
}

/*
* 获取输出字段
* @params
* @returns {Promise<*>}
* */

export async function showOutputColumn(tranStepId) {
    return await reqUtil.get({
        url : "/dataModeling/showOutputColumn",
    },{tranStepId})
}
/*
* 获取分区信息
* @params
* @returns {Promise<*>}
* */

export async function partitionPage(url) {
    return await reqUtil.get({
        url : url
    })
}

/*
* 保存 变更
* @params  url , tranStepId   , dynamicParamJson
* @returns {Promise<*>}
* */
export async function saveDynamic( url  ,tranStepId , dynamicParamJson) {
    return await reqUtil.get({
        url: url ,
        successMsg : "保存成功"
    }, {tranStepId, dynamicParamJson} );
}
/*
* 获取 变更
* @params  url , tranStepId   , dynamicParamJson
* @returns {Promise<*>}
* */
export async function getDynamic( url , tranStepId ) {
    return await reqUtil.get({
        url: url
    }, {tranStepId} );
}

/*
* 保存输出字段
* @params
* @returns {Promise<*>}
* */
export async function modifyColumn(  tranStepId , field ,length ,precsn , valType ,uniqueValue, filterExpress ) {
    return await reqUtil.get({
        url: root1 + "modifyColumn"
    }, {tranStepId , field ,length ,precsn , valType ,uniqueValue, filterExpress} );
}
/*
* 保存抽样设置
* @params
* @returns {Promise<*>}
* */
export async function saveAdvanceConfig(  tranStepId , isCopy ,threadCount ,sampleExpr , sampleDistinct ,exceptionMode ) {
    return await reqUtil.get({
        url: root1 + "save/advanceConfig"
    }, {tranStepId , isCopy ,threadCount ,sampleExpr , sampleDistinct ,exceptionMode} );
}
/*
* 保存排序设置
* @params
* @returns {Promise<*>}
* */
export async function saveSort(  tranStepId , sortColumn ,sortType ) {
    return await reqUtil.get({
        url: root + "fulltext/input/saveSort"
    }, {tranStepId , sortColumn ,sortType});
}

const w_root = add + "/plugin/window/";
/*
*  初始化
 * @param tranStepId
 * @returns {Promise<*>}
* */
export async function getPlugin( transtepId , settings) {
    return await reqUtil.get({
        url: w_root + 'getPlugin' ,
        settings : settings
    }, {transtepId} );
}

/*
*  保存
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function savePlugin( params ,settings ) {
    return await reqUtil.post({
        url: w_root + 'savePlugin' ,
        settings : settings
    }, params );
}

//初始化全文库输入的分区类型
/*
*  分区类型
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_columns( tranStepId) {
    return await reqUtil.get({
        url: root + 'fulltext/input/columns'
    }, {tranStepId} );
}

/**
 * 全文库输出 获取路由
 * @param dataObjId
 * @return {Promise<*>}
 */
export async function dataObjColumn(dataObjId){
    return await reqUtil.get({
        url : root + "/fulltext/output/dataObjColumn"
    },{dataObjId})
}

/**
 * 全文输出 索引
 * @param dataSetId
 * @return {Promise<*>}
 */
export async function getElastics(dataSetId){
    return await reqUtil.get({
        url : "/manage/dataWarehouse/getElastics",
    },{dataSetId})
}

export async function fullTextSave(tranStepId,params ,settings){
    return await reqUtil.post({
        url : root + "fulltext/output/save?tranStepId="+tranStepId,
        settings
    },params)
}

/*
*  排序字段
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_getSort( tranStepId) {
    return await reqUtil.get({
        url: root + 'fulltext/input/getSort'
    }, {tranStepId} );
}
/*
*  保存排序字段
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_saveSort( tranStepId , sortColumn , sortType) {
    return await reqUtil.get({
        url: root + 'fulltext/input/saveSort'
    }, {tranStepId , sortColumn , sortType} );
}
/*
*  保存分区
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function savePartition( url , params ) {
    return await reqUtil.post({
        url: url
    },params);
}

/*
*  全文库初始化
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_view( tranStepId , settings ) {
    return await reqUtil.get({
        url: root + 'fulltext/input/query' ,
        settings : settings
    },{tranStepId});
}
/*
*  全文库 预览
 * @param
 * @returns {Promise<*>}
* */
export async function fulltext_preview( params , settings ) {
    return await reqUtil.post({
        url: root + 'fulltext/input/preview' ,
        settings : settings
    },params);
}

/*
*  全文库 保存
 * @param
 * @returns {Promise<*>}
* */
export async function fulltext_save( params , settings ) {
    return await reqUtil.post({
        url: root + 'fulltext/input/save' ,
        settings : settings
    },params);
}
/*
*  全文库输出初始化
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_outView( tranStepId , settings ) {
    return await reqUtil.get({
        url: root + 'fulltext/output/query' ,
        settings : settings
    },{tranStepId});
}
/**
 * 全文库 输出 导入字段
 * @param tranStepId
 * @param schemaId
 * @param dataObjId
 * @param createTable
 * @return {Promise<*>}
 */
export async function fulltext_getColumn(tranStepId , schemaId , dataObjId , createTable){
    return await reqUtil.get({
        url : root + "fulltext/output/getColumn"
    },{tranStepId , schemaId , dataObjId , createTable})
}

/**
 * 全文 获取输入字段
 * @param transStepId
 * @return {Promise<*>}
 */
export async function fulltext_inputCols(transStepId){
    return await reqUtil.get({
        url : root + "fulltext/output/inputCols"
    }, {transStepId})
}

/**
 * 获取输出字段
 * @param dataObjId
 * @return {Promise<*>}
 */
export async function fulltext_outputCols(dataObjId){
    return await reqUtil.get({
        url : root + "fulltext/output/outputCols"
    },{dataObjId})
}

const kv_root = add + "/kvInput/";

/*
*  kv 获取列簇
 * @param
 * @returns {Promise<*>}
* */
export async function getColumnByColumnFamily( tableId , columnFamily ) {
    return await reqUtil.get({
        url: kv_root + 'getColumnByColumnFamily' ,
    },{tableId , columnFamily});
}
/*
*  kv 初始化
 * @param
 * @returns {Promise<*>}
* */
export async function kvInputPage( tableId , tranStepId ,settings ) {
    return await reqUtil.get({
        url: kv_root + 'kvInputPage' ,
        settings : settings
    },{tableId , tranStepId});
}
/*
*  kv 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveKVInputPlugin( tranStepId  , params , settings ) {
    return await reqUtil.post({
        url: kv_root + 'saveKVInputPlugin?tranStepId='+tranStepId ,
        settings : settings
    },params );
}
/*
*  kv 预览
 * @param
 * @returns {Promise<*>}
* */
export async function kvInputPreview(tableId , tranStepId  , params , settings ) {
    return await reqUtil.post({
        url: kv_root + `saveKVInputPlugin?tableId=${tableId}&tranStepId=${tranStepId}` ,
        settings : settings
    },params );
}

const p_root =  "/dataPreview/";
/*
*  插件 预览
 * @param
 * @returns {Promise<*>}
* */
export async function pluginPreview(transId , tranStepId, selectSize , limitSize, settings, errorMsg , jobID){
    return await reqUtil.get({
        url : p_root + "preview",
        settings : settings,
        errorMsg:false
    }, {transId , tranStepId, selectSize, limitSize , jobID})
}
const f_root = "/aggregator/";


export async function saveCollisionPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "collision/saveCollisionPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

/*
*  碰撞插件
 * @param
 * @returns {Promise<*>}
* */
export async function collisionPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "collision/collisionPluginPage",
        settings : settings
    }, {tranStepId})
}



/*
*  全连接
 * @param
 * @returns {Promise<*>}
* */
export async function fullJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "fullJoin/fullJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  全连接 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveFullJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "fullJoin/saveFullJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  交集
 * @param
 * @returns {Promise<*>}
* */
export async function innerJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "innerJoin/innerJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  交集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveInnerJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "innerJoin/saveInnerJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  左右连接
 * @param
 * @returns {Promise<*>}
* */
export async function leftOrRightJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "leftOrRightJoin/leftOrRightJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  左右连接 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveLeftOrRightJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "leftOrRightJoin/saveLeftOrRightJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  左右排除
 * @param
 * @returns {Promise<*>}
* */
export async function subtractByKeyPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "subtractByKey/subtractByKeyPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  左右排除 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveSubtractByKeyPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "subtractByKey/saveSubtractByKeyPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  并集
 * @param
 * @returns {Promise<*>}
* */
export async function unionJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : f_root + "unionJoinPlugin/unionJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  并集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveUnionJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : f_root + "unionJoinPlugin/saveUnionJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

const s_root = add + "/serviceOrganization/";

/*
*  表达式插件 初始化
 * @param
 * @returns {Promise<*>}
* */
export async function operatorPage( tranStepId ,settings){
    return await reqUtil.get({
        url :s_root + "edit",
        settings : settings
    }, {tranStepId})
}

/*
*  算子编排 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveOperator( tranStepId , params , settings){
    return await reqUtil.post({
        url :s_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/*
*  算子编排 删除
 * @param
 * @returns {Promise<*>}
* */
export async function deleteGraph( graphIds){
    return await reqUtil.post({
        url :s_root + "deleteGraph",
    },graphIds)
}

/*
*  算子编排 画布 步骤信息修改
 * @param
 * @returns {Promise<*>}
* */
export async function updataServiceOrchestrationNode( params ){
    return await reqUtil.post({
        url :s_root + "updataServiceOrchestrationNode" ,
    } , params )
}
/*
*  算子编排 画布 保存端点
 * @param
 * @returns {Promise<*>}
* */
export async function savaServiceOrchestrationEdge( params ){
    return await reqUtil.post({
        url : s_root +"savaServiceOrchestrationEdge" ,
    } , params )
}

/*
*  算子编排 获取 连线option
 * @param
 * @returns {Promise<*>}
* */
export async function serviceParams( serviceId ){
    return await reqUtil.get({
        url : s_root +"serviceParams" ,
    } , {serviceId} )
}

/*
*  算子编排 删除 连线
 * @param
 * @returns {Promise<*>}
* */
export async function deleteServiceOrchestrationEdge( fromNodeId , toNodeId ){
    return await reqUtil.post({
        url : s_root +`deleteServiceOrchestrationEdge?fromNodeId=${fromNodeId}&toNodeId=${toNodeId}` ,
    })
}


/*
*  算子编排 新增
 * @param
 * @returns {Promise<*>}
* */
export async function savaServiceOrchestrationNode( params ){
    return await reqUtil.post({
        url : s_root +"savaServiceOrchestrationNode" ,
    } , params )
}
/*
*  全领公式算子 新增
 * @param
 * @returns {Promise<*>}
* */
export async function saveAmountMath( params ){
    return await reqUtil.post({
        url : add + "/amountMathOperator/saveAmountMath" ,
    } , params )
}

/*
*  算子编排 删除步骤
 * @param
 * @returns {Promise<*>}
* */
export async function deleteServiceOrchestrationNode( nodeId ){
    return await reqUtil.post({
        url : s_root +`deleteServiceOrchestrationNode?nodeId=${nodeId}` ,
    })
}

/*
*  表达式插件 删除eda节点
 * @param
 * @returns {Promise<*>}
* */
export async function deleteEDANodeByNodeId( nodeId ){
    return await reqUtil.get({
        url :add + `/amountMathOperator/deleteEDANode?nodeId=${nodeId}` ,
    })
}

/*
*  算子编排 回显
 * @param
 * @returns {Promise<*>}
* */
export async function getRuleGraph( params ){
    return await reqUtil.post({
        url : s_root +"getRuleGraph" ,
    } , params )
}
/*
*  算子编排 连线
 * @param
 * @returns {Promise<*>}
* */
export async function saveEdge( serviceOrgId , params ){
    return await reqUtil.post({
        url : s_root +`saveEdge?serviceOrgId=${serviceOrgId}` ,
    } , params )
}

/*
*  新建表注册
 * @param param id
 * @returns {Promise<*>}
* */
export async function register( id ) {
    return await reqUtil.get({
        url: add + '/spark/register'
    } , {id});
}

/*
*  关系库输出初始化
 * @param saveVo
 * @returns {Promise<*>}
* */
export async function sqlOutView( transStepId ,settings ) {
    return await reqUtil.get({
        url: add + '/plugin/sqlout/view',
        settings : settings
    } , {transStepId});
}

/*
*  关系库输出保存
 * @param saveVo
 * @returns {Promise<*>}
* */
export async function sqlOutSave( saveVo ,settings ) {
    return await reqUtil.post({
        url: add + '/plugin/sqlout/save',
        settings : settings
    } , saveVo);
}
/*
*  关系库输出 导入字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutCreateTableMeta( transStepId ,schemaId ) {
    return await reqUtil.get({
        url: add + '/plugin/sqlout/createTableMeta',
    } , {transStepId ,schemaId});
}

/*
*  关系库输出 获取目标字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutColumns( transStepId ,dataObjId ) {
    return await reqUtil.get({
        url: add + '/plugin/sqlout/columns',
    } , { transStepId ,dataObjId});
}
/*
*  关系库输出 获取来源字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutMeta( transStepId ) {
    return await reqUtil.get({
        url: add + '/plugin/sqlout/meta/src',
    } , { transStepId});
}
/*
*  数据排序
 * @param
 * @returns {Promise<*>}
* */
export async function dataSortPluginPage( tranStepId ,settings ) {
    return await reqUtil.get({
        url: add + '/datasort/dataSortPluginPage',
        settings : settings
    } , { tranStepId });
}

/*
*  数据排序
 * @param
 * @returns {Promise<*>}
* */
export async function getInputColumn( tranStepId ) {
    return await reqUtil.get({
        url: add + '/datasort/getInputColumn',
    } , { tranStepId });
}
/*
*  数据排序保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveDataSortPlugin( tranStepId, pluginMeta ,settings) {
    return await reqUtil.post({
        url: add + '/datasort/saveDataSortPlugin?tranStepId='+tranStepId,
        settings : settings
    } , pluginMeta);
}

const exp_root =add + "/expressionExcuter/";

/**
 * 表达式插件回选
 * @param tranStepId
 * @param settings
 * @returns {Promise<*>}
 */
export async function queryDataExp(tranStepId ,settings){
    return await reqUtil.get({
        url :exp_root + "queryData",
        settings : settings
    }, {tranStepId})
}

/**
 * 表达式保存
 * @param tranStepId
 * @param params
 * @param settings
 * @return {Promise<*>}
 */


export async function saveOperatorExp( tranStepId , params , settings){
    return await reqUtil.post({
        url :s_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

/**
 * 调试
 * @param mlsql
 * @return {Promise<*>}
 */
export async function expressionDebug(mlsql){
    return await reqUtil.get({
        url : "udfOperatorManage/expressionDebug"
    },{mlsql})
}

/**
 * 常用算子
 * @param version
 * @param operatorBelong
 * @return {Promise<*>}
 */
export async function queryUdfModel(version , operatorBelong){
    return await reqUtil.get({
        url : "udfOperatorManage/queryUdfModel"
    },{version , operatorBelong})
}

/**
 * 获取字段
 * @param expression
 * @return {Promise<*>}
 */
export async function expressionParser(expression){
    return await reqUtil.get({
        url : "udfOperatorManage/expressionParser"
    },{expression})
}

export async function getOutputColumnExp(groupId ,settings){
    return await reqUtil.get({
        url :exp_root + "getOutputColumn",
        settings : settings
    }, {groupId})
}

const script_root =add + "/script/";

export async function saveScriptExp( tranStepId , params , settings){
    return await reqUtil.post({
        url :script_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

export async function queryScriptData(tranStepId ,settings){
    return await reqUtil.get({
        url :script_root + "queryData",
        settings : settings
    }, {tranStepId})
}

export async function runScript( params){
    return await reqUtil.post({
        url :"/mlsql/runScript" ,
    }, params)
}
export async function getSchema(param){
    return await reqUtil.get({
        url :"/mlsql/getSchema?tableName="+param ,
    })
}
export async function transStandardType(params){
    return await reqUtil.post({
        url :add + "/script/transStandardType" ,
    }, params)
}

export async function getAfterTableName(transId,tranStepId){
    return await reqUtil.get({
        url :"/mlsql/getAfterTableName?id="+transId+"&stepId="+tranStepId ,
    })
}

export async function inintLabelPluginPage(tranStepId, settings){
    return await reqUtil.get({
        url :add + "/label/labelPluginPage" ,
        settings:settings
    },{tranStepId})
}


export async function initEffective(tranStepId,settings){
    return await reqUtil.get({
        url :add + "/effective/pageEffective" ,
        settings : settings
    },{tranStepId})
}
/*
*  获取字段类型
 * @param
 * @returns {Promise<*>}
* */
export async function getColumnType( settings) {
    return await reqUtil.get({
        url:  '/udfOperatorManage/getParameterTypes',
        settings : settings
    });
}
export async function saveLabelPluginPage(tranStepId,params ,settings){
    return await reqUtil.post({
        url :add + "/label/saveLabel?stepId=" + tranStepId ,
        settings
    },params)
}export async function saveEffectivePluginPage(tranStepId,params,settings){
    return await reqUtil.post({
        url :add + "/effective/saveEffective?stepId=" + tranStepId ,
        settings
    },params)
}

/*
*  表达式处理 获取输入字段
* @param
* @returns {Promise<*>}
* */
export async function queryPrevNodeVariables(tranStepId , type , nodeId ="" ) {
    return await reqUtil.get({
        url : add + "/serviceOrganization/queryPrevNodeVariables"
    },{tranStepId ,nodeId , type})
}

/*
*  表达式处理 获取算子树
* @param
* @returns {Promise<*>}
* */
export async function getOperatorTree(version , settings) {
    return await reqUtil.get({
        url : "/udfOperatorManage/getOperatorTree",
        settings:settings
    },{version})
}

/**
 * 表达式获取算子树
 * @param version
 * @param operatorBelong  表达式：expressionOperator  常用：commonUseOperator
 * @param settings
 */
export async function getExpressionOperatorTree(version ,operatorBelong , settings){
    return await reqUtil.get({
        url : "udfOperatorManage/getExpressionOperatorTree",
        settings
    },{version , operatorBelong})
}

/**
 * 表达式获取算子树(参数设置入口)
 * @param {*} settings
 */
export async function getVariableOperatorTree( settings){
    return await reqUtil.get({
        url : "variableManage/getVariableOperatorTree",
        settings
    })
}
/**
 * 表达式获取算子方法
 * @return {Promise<*>}
 */
export async function getExpression(){
    return await reqUtil.get({
        url : "udfOperatorManage/getExpression",
    })
}

/**
 * 表达式获取算子方法（参数设置入口）
 */
export async function getVariableExpression(){
    return await reqUtil.get({
        url : "variableManage/getVariableExpression",
    })
}

/*udf 画布接口*/
let udf_root = add + "/udfGraphManage/";

/*
*  表达式处理 新增udf节点
* @param udfNodeVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfNode(udfNodeVo) {
    return await reqUtil.post({
        url : udf_root + "saveOrUpdateUdfNode",
    },udfNodeVo)
}

/*
*  表达式处理 获取图
* @param udfGraphVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfGraph(udfGraphVo) {
    return await reqUtil.post({
        url : udf_root + "saveOrUpdateUdfGraph",
    },udfGraphVo)
}

const denormaliser_root = add + "/denormaliser/";

export async function saveDenormaliser( tranStepId , params , settings){
    return await reqUtil.post({
        url :denormaliser_root + "savePluginMeta?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}

const jsonParsing_root = add + "/jsonParsing/";

export async function saveJsonParsing( tranStepId , params , settings){
    return await reqUtil.post({
        url :jsonParsing_root + "savePlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
export async function queryJsonParingData( tranStepId , settings){
    return await reqUtil.get({
        url :jsonParsing_root + "queryJsonParingData?tranStepId=" + tranStepId,
        settings : settings
    })
}
/*
*  表达式处理 删除节点
* @param nodeId
* @returns {Promise<*>}
* */
export async function deleteUdfNodeById(nodeId) {
    return await reqUtil.get({
        url : udf_root + "deleteUdfNodeById",
    },{nodeId})
}

/*
*  表达式处理 获取节点信息
* @param nodeId
* @returns {Promise<*>}
* */
export async function getUdfNodById(nodeId) {
    return await reqUtil.get({
        url : udf_root + "getUdfNodById",
    },{nodeId})
}

/*
*  表达式处理 保存边
* @param udfEdgeVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfEdge(udfEdgeVo) {
    return await reqUtil.post({
        url : udf_root + "saveOrUpdateUdfEdge",
    },udfEdgeVo)
}

/*
*  表达式处理 删除边
* @param edgeId
* @returns {Promise<*>}
* */
export async function deleteUdfEdgeById(edgeId) {
    return await reqUtil.get({
        url : udf_root + "deleteUdfEdgeById",
    },{edgeId})
}

/*
*  表达式处理 删除图
* @param graphId
* @returns {Promise<*>}
* */
export async function deleteUdfGraphById(graphId) {
    return await reqUtil.get({
        url : udf_root + "deleteUdfGraphById",
    },graphId)
}

/*
*  表达式处理 删除图
* @param graphId
* @returns {Promise<*>}
* */
export async function getUdfGraphById(graphId,tansId) {
    return await reqUtil.get({
        url : udf_root + "getUdfGraphById?graphId="+graphId+"&tansId="+tansId,
    })
}
/*
*  表达式处理 获取输入类型
* @param graphId
* @returns {Promise<*>}
* */
export async function getParameterTypes() {
    return await reqUtil.get({
        url : "/udfOperatorManage/getParameterTypes",
    })
}
/*
*  表达式处理 获取填入的信息
* @param nodeId
* @returns {Promise<*>}
* */
export async function getUdfParameterByNodeId(nodeId) {
    return await reqUtil.get({
        url :udf_root + "getUdfParameterByNodeId",
    },{nodeId})
}

/**
 * 获取算子信息
 * @param operatorId
 * @param settings
 * @return {Promise<*>}
 */
export async function getUdfOperatorById(operatorId , settings) {
    return await reqUtil.get({
        url : "/udfOperatorManage/getUdfOperatorById",
        settings
    },{operatorId})
}

/**
 * 字段过滤插件 获取上流所有字段
 * @param tranStepId
 * @returns {Promise<*>}
 */
export async function queryFieldFilteringData(tranStepId) {
    return await reqUtil.get({
        url : add + "/fieldfiltering/queryData",
    },{tranStepId})
}
export async function getFieldFilteringFields(tranStepId,settings) {
    return await reqUtil.get({
        url : add + "/fieldfiltering/getFields",
        settings : settings
    },{tranStepId})
}
export async function saveFieldFiltering(tranStepId, params , settings) {
    return await reqUtil.post({
        url : add + "/fieldfiltering/savePlugin?tranStepId="+tranStepId,
        settings : settings
    },params)
}

/**
 * 警务区计算插件
 * @param tranStepId
 * @returns {Promise<*>}
 */
export async function saveJwqMapper(stepId , params , settings) {
    return await reqUtil.post({
        url : add + "/jwqMapper/saveMeta?stepId=" + stepId,
        settings : settings
    }, params)
}
export async function getJwqMapperPlugin(tranStepId,settings) {
    return await reqUtil.get({
        url :add + "/jwqMapper/getJwqMapperPluginPage",
        settings : settings
    },{tranStepId})
}


const lucene_root =add + "/luceneBuilder/";

export async function saveLuceneBuilder(stepId , params , settings){
    return await reqUtil.post({
        url :lucene_root + "saveMeta?stepId=" + stepId,
        settings : settings
    }, params)
}

/**
 * 条件 过滤插件
 * @param
 * @returns {Promise<*>}
 */
const con_root = "/cicada/conditionFilter/";
export async function conditionFilterQueryData(tranStepId , settings) {
    return await reqUtil.get({
        url : con_root + "queryData" ,
        settings
    } ,{tranStepId})
}
//保存
export async function conditionSavePlugin(tranStepId , pluginMeta , settings) {
    return await reqUtil.post({
        url : con_root + "savePlugin?tranStepId=" + tranStepId,
        settings
    },pluginMeta)
}


/**
 * 归一化插件
 * @param tranStepId
 * @returns {Promise<*>}
 */
const nor_root = add + "/normalization/";
export async function getNormalizationPage(tranStepId , settings) {
    return await reqUtil.get({
        url : nor_root + "getNormalizationPage" ,
        settings
    } ,{tranStepId})
}
//保存
export async function saveNormalizationMeta(tranStepId , pluginMeta , settings) {
    return await reqUtil.post({
        url : nor_root + "saveNormalizationMeta?stepId=" + tranStepId,
        settings
    },pluginMeta)
}



/**
 * 文件上传插件
 * */
const file_root = "/plugin/fileInput/";
//保存
export async function saveFileInputPlugin(tranStepId, file, settings) {
    return await reqUtil.post({
        url : file_root + "savePlugin?tranStepId=" + tranStepId,
        settings
    },file)
}


export async function getFileInputPluginPage(tranStepId, settings) {
    return await reqUtil.get({
        url : file_root +"queryData?tranStepId=" +tranStepId,
        settings
    })
}

export async function getFiledWithRealtime(tranStepId, sheetName, startRow ,startColumn,useHeader,settings) {
    return await reqUtil.get({
        url : file_root + "getFiledWithRealtime?tranStepId=" +
            tranStepId + "&sheetName=" + sheetName+ "&startRow=" +startRow + "&startColumn=" +startColumn + "&useHeader=" +useHeader,
        settings
    })
}
/**
 * 文件输出插件
 * */
const fileout_root = "/cicada/plugin/fileOutput/";
export async function saveFileOutputPlugin(tranStepId, fileOutputMeta, settings) {
    return await reqUtil.post({
        url : fileout_root + "savePlugin?tranStepId=" + tranStepId,
        settings
    },fileOutputMeta)
}

export async function getFileTypeAndFields(tranStepId, settings) {
    return await reqUtil.get({
        url : fileout_root +"getFileTypeAndFields?tranStepId=" +tranStepId,
        settings
    })
}

export async function getFileOutputPluginPage(tranStepId, settings) {
	return await reqUtil.get({
		url : fileout_root +"queryData?tranStepId=" +tranStepId,
		settings
	})
}
export async function getFields(tranStepId, settings) {
	return await reqUtil.get({
		url : fileout_root +"getFields?tranStepId=" +tranStepId,
			settings
	})
}

export async function getDownloadIsOk(tranStepId, settings) {
    return await reqUtil.get({
        url : fileout_root +"getDownloadIsOk?tranStepId=" +tranStepId,
        settings
    })
}

/**
 * 同行插件
 **/

/**
 * 初始化
 * @param tranStepId
 * @return {Promise<*>}
 */
export async function getPeerView(tranStepId){
    return await reqUtil.get({
        url : add + "/peer/view"
    },{tranStepId})
}

/**
 * 保存
 * @param tranStepId
 * @param pluginMeta
 * @param settings
 * @return {Promise<*>}
 */
export async function savePeer(tranStepId , pluginMeta ,settings){
    return await reqUtil.post({
        url : add + "/peer/save?tranStepId="+tranStepId,
        settings
    },pluginMeta)
}

/*
* 时间打标
* */

/**
 * 初始化
 * @param tranStepId
 * @param settings
 * @return {Promise<*>}
 */
const markTimeRoot = add + "/markingtime/";
export async function markingTimeView(tranStepId , settings){
    return await reqUtil.get({
        url : markTimeRoot + "view",
        settings
    },{tranStepId})
}

export async function saveMarkingTime(tranStepId , pluginMeta , settings){
    return await reqUtil.post({
        url :markTimeRoot + "save?tranStepId="+tranStepId,
        settings
    },pluginMeta)
}

/*
*  字段转对象插件
* */
export async function queryObjectJsonData(tranStepId,settings) {
    return await reqUtil.get({
        url : "cicada/objectToJson/queryObjectJsonData?tranStepId=" + tranStepId,
        settings
    })
}
export async function getObjectJsonSelectData(settings) {
    return await reqUtil.get({
        url : "cicada/objectToJson/getConversionType",
        settings
    })
}
export async function objectToJsonSavePlugin(tranStepId,objectToJsonMeta, settings) {
    return await reqUtil.post({
        url : "cicada/objectToJson/savePlugin?tranStepId="+tranStepId,
        settings
    }, objectToJsonMeta)
}

/*
*  RAY执行插件
* */
export async function queryAllTask(tranStepId,settings) {
    return await reqUtil.get({
        url : "/pyTask/queryAllTask?tranStepId=" + tranStepId,
        settings
    })
}
export async function rayRunnerSavePlugin(tranStepId,pluginMeta, settings) {
    return await reqUtil.post({
        url : add + "/pyTaskRunner/save?tranStepId="+tranStepId,
        settings
    }, pluginMeta)
}

export async function rayRunnerPreview(tranStepId,settings) {
    return await reqUtil.post({
        url : add + "/pyTaskRunner/view?tranStepId="+tranStepId,
        settings
    })
}
/**
 * 数据去重插件
 */
export async function queryData(tranStepId,settings) {
    return await reqUtil.get({
        url : add + "/dataDistinct/queryData?tranStepId=" + tranStepId,
        settings
    })
}
export async function removeDistinctSave(tranStepId,dataDistinctMeta,settings) {
    return await reqUtil.post({
        url : add + "/dataDistinct/savePlugin?tranStepId=" + tranStepId,
        settings
    }, dataDistinctMeta)
}

/**
 * 保存插件描述
 * @return {Promise<void>}
 */
export async function savePluginDec(tranStepId , name , memo , settings){
    return await reqUtil.get({
        url : "/transOperator/updateTransMetaNameAndMemo",
        settings
    },{tranStepId , name , memo})
}

/**
 * 回显插件描述
 * @param tranStepId
 * @return {Promise<*>}
 */
export async function getPluginDec(tranStepId){
    return await reqUtil.get({
        url : "/transOperator/getTransMetaByTranStepId"
    },{tranStepId})
}



/**
 * 交集比对
 * */

export async function cicadainnerJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : "cicada/aggregator/innerJoin/innerJoinPluginPage",
        settings
    }, {tranStepId})
}
export async function saveCicadaInnerJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : "cicada/aggregator/innerJoin/saveInnerJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/**
 * 行專列
 * */
export async function cicadaCollisionPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : "cicada/aggregator/collision/collisionPluginPage",
        settings : settings
    }, {tranStepId})
}

export async function saveCicadaCollisionPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : "cicada/aggregator/collision/saveCollisionPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}
/**
* 差集
* **/
export async function cicadaSubtractByKeyPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : "cicada/aggregator/subtractByKey/subtractByKeyPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  左右排除 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveCicadaSubtractByKeyPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : "cicada/aggregator/subtractByKey/saveSubtractByKeyPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}


export async function cicadaUnionJoinPluginPage( tranStepId ,settings){
    return await reqUtil.get({
        url : "cicada/aggregator/unionJoinPlugin/unionJoinPluginPage",
        settings : settings
    }, {tranStepId})
}
/*
*  并集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveCicadaUnionJoinPlugin( tranStepId ,params , settings){
    return await reqUtil.post({
        url : "cicada/aggregator/unionJoinPlugin/saveUnionJoinPlugin?tranStepId=" + tranStepId,
        settings : settings
    }, params)
}


//文件上传
export async function cicadaSaveFileInputPlugin(tranStepId, file, settings) {
    return await reqUtil.post({
        url :  "cicada/plugin/fileInput/savePlugin?tranStepId=" + tranStepId,
        settings
    },file)
}


export async function getCicadaFileInputPluginPage(tranStepId, settings) {
    return await reqUtil.get({
        url : "cicada/plugin/fileInput/queryData?tranStepId=" +tranStepId,
        settings
    })
}

export async function getExcelSheets(fileSuffix,file) {
    return await reqUtil.post({
        url :  "cicada/plugin/fileInput/getExcelSheets?fileSuffix=" + fileSuffix,
    },file)
}

/**
 * 分组统计初始化
 * @param {*} tranStepId
 * @param {*} settings
 */
export async function groupInit(tranStepId, settings) {
    return await reqUtil.get({
        url : "cicada/plugin/group/view?tranStepId=" +tranStepId,
        settings
    })
}

export async function groupSave(tranStepId,pluginMeta,settings) {
    return await reqUtil.post({
        url :  "cicada/plugin/group/save?tranStepId=" + tranStepId,
        settings
    },pluginMeta)
}
//字段设置
export async function saveCicadaFieldsSettings(tranStepId, cicadaOutPutFsSet , settings) {
    return await reqUtil.post({
        url :  "/cicada/fieldSettings/savePlugin?tranStepId=" + tranStepId,
        settings
    },cicadaOutPutFsSet )
}


export async function getCicadaFieldsSettingsPage(tranStepId, settings) {
    return await reqUtil.get({
        url : "/cicada/fieldSettings/queryFieldSettingsData?tranStepId=" +tranStepId,
        settings
    })
}

/*
* 初始化树
*@params
* @returns {Promise<*>}
* */


export async function queryDataSetTree(hasDataObj ,settings,currentDataSetId) {
    return await reqUtil.get({
        url :  "/dataSetOperation/queryDataSetTree",
        settings : settings
    },{hasDataObj,currentDataSetId})
}


/*
* 添加插件
* @params
* @return {Promise<*>}
* */
export async function addPluginTranStep(pluginCode , transName , x , y , parentTransId) {
    return await reqUtil.get({
        url : "/transOperator/addPluginTranStep" ,
    },{pluginCode , transName , x , y , parentTransId})
}

/*
* 连线
* @params
* @return {Promise<*>}
* */
export async function addTransHop(fromTransName , toTransName , fromTransId , toTransId , transId) {
    return await reqUtil.get({
        url : "/transOperator/addTransHop" ,
    },{fromTransName , toTransName , fromTransId , toTransId , transId})
}
/**
 * 连线 带有端点
 * @param fromTransName
 * @param toTransName
 * @param fromTransId
 * @param toTransId
 * @param transId
 * @param direction 左 "/left" 右 "/right"
 * @return {Promise<*>}
 */
export async function addTransHopWithDirection(fromTransName , toTransName , fromTransId , toTransId , transId , direction) {
    return await reqUtil.get({
        url : "/transOperator/addTransHopWithDirection" ,
    },{fromTransName , toTransName , fromTransId , toTransId , transId , direction})
}


/*
* 初始化输入插件
* @params
* @return {Promise<*>}
* */
export async function initPlug(params , root_path) {
    return await reqUtil.get({
        url : root_path +  "init" ,
        errorMsg : false
    },params)
}

/*
* 删除插件
* @params
* @return {Promise<*>}
* */
export async function deleteTransStep(stepId ,transId ) {
    return await reqUtil.get({
        url : "/transOperator/deleteTransStep" ,
    },{stepId ,transId })
}

/**
 * 时间拉链表
 * @param tranStepId
 * @param transId
 * @param settings
 * @return {Promise<*>}
 */
export async function queryDateZipperData(tranStepId , transId , settings){
    return await reqUtil.get({
        url : add + "/zipperTable/queryDateZipperData",
        settings
    },{tranStepId , transId})
}
/**
 * 数值拉链表
 * @param tranStepId
 * @param transId
 * @param settings
 * @return {Promise<*>}
 */
export async function queryNumberZipperData(tranStepId , transId , settings){
    return await reqUtil.get({
        url : add + "/zipperTable/queryNumberZipperData",
        settings
    },{tranStepId , transId})
}


const plug_root = "/cicada/";
/*
* 数据服务输入
* @params
* @returns {Promise<*>}
* */
export async function getServiceInfoList() {
    return await reqUtil.get({
        url : "serviceRequestInfoController/getServiceInfoList" ,
    })
}
export async function getInterfaceInfoListBySId(serviceId, settings) {
    return await reqUtil.get({
        url : "serviceRequestInfoController/getInterfaceInfoListBySId?serviceId=" + serviceId ,
        settings
    })
}
export async function getBodyAttributeByCfgId(cfgId, settings) {
    return await reqUtil.get({
        url : "serviceRequestInfoController/getBodyAttributeByCfgId?cfgId=" + cfgId ,
        settings
    })
}
/**
 * 保存
 */
export async function saveServiceInputPlugin(tranStepId, serviceInputMeta, settings) {
    return await reqUtil.post({
        url :plug_root + "pluginServiceInput/savePlugin?tranStepId=" + tranStepId  ,
        settings
    }, serviceInputMeta)
}
export async function serviceInputAnalyseColumn(tranStepId, transId, settings) {
    return await reqUtil.get({
        url : "serviceRequestInfoController/analyseColumn?transId=" + transId + "&tranStepId=" + tranStepId ,
        settings
    })
}
/**
 * 回显
 */
export async function serviceInputQueryData(tranStepId, settings) {
    return await reqUtil.get({
        url :plug_root + "pluginServiceInput/queryData?tranStepId=" + tranStepId ,
        settings
    })
}
/**
 * 模型服务初始化
 * @param {*} tranStepId
 * @param {*} id
 * @param {*} settings
 */
export async function modelServiceInit(tranStepId, id, serviceClassify,  settings) {
    return await reqUtil.get({
        url : "/cicada/modelServicePlus/init?tranStepId=" + tranStepId + "&serviceId=" + id + "&serviceClassify=" + serviceClassify ,
        settings
    })
}

/**
 * 模型服务回显
 * @param {*} id
 * @param {*} settings
 */
export async function queryModelServiceData(id,  settings) {
    return await reqUtil.get({
        url : "/cicada/modelService/queryModelServiceData?tranStepId=" + id  ,
        settings
    })
}

/**
 * 本地服务插件查看
 * @param {*} id
 * @param {*} settings
 */
export async function queryLocalModelServiceData(id, pluginCode, settings) {
    return await reqUtil.get({
        url : "/cicada/modelServicePlus/queryModelServiceData?tranStepId=" + id + "&pluginCode=" + pluginCode ,
        settings
    })
}

/**
 * 模型服务保存
 * @param {*} id
 * @param {*} params
 * @param {*} settings
 */
export async function saveModelServicePlugin(id, params, settings) {
    return await reqUtil.post({
        url : "cicada/modelServicePlus/savePlugin?tranStepId=" + id  ,
        settings
    }, params)
}

/**
 * 同行插件 - 初始化
 * @param {*} id
 * @param {*} settings
 */
export async function peerQueryData(id,  settings) {
    return await reqUtil.get({
        url : "/cicada/peerParsing/PeerQueryData?tranStepId=" + id  ,
        settings
    })
}

/**
 * 同行插件 - 保存
 * @param {*} id
 * @param {*} settings
 */
export async function PeerSavePlugin(id, params,  settings) {
    return await reqUtil.post({
        url : "/cicada/peerParsing/PeerSavePlugin?tranStepId=" + id  ,
        settings
    }, params)
}

/**
 * 节点复制
 * @param {*} transId
 * @param {*} stepId
 * @param {*} stepName
 * @param {*} x
 * @param {*} y
 * @param {*} settings
 */
export async function copyTransMetaStep(params,  settings) {
    return await reqUtil.post({
        url : "/transOperator/copyTransMetaStep"  ,
        settings
    }, params)
}

/**
 * 多join 保存
 * @param {*} tranStepId
 * @param {*} settings
 */
 export async function sqlSavePlugin(tranStepId, cicadaManyJoinMeta,  settings) {
    return await reqUtil.post({
        url : "/cicada/manyjoin/savePlugin?tranStepId=" + tranStepId ,
        settings
    },cicadaManyJoinMeta)
}

/**
 * 多join 回显
 * @param {*} tranStepId
 * @param {*} pluginType
 * @param {*} settings
 */
 export async function sqlQueryData(tranStepId, pluginType, settings) {
    return await reqUtil.get({
        url : "/cicada/manyjoin/queryData" ,
        settings
    },{tranStepId,pluginType})
}
/**
 * 多join 转换（数据标准化）
 * @param {*} tranStepId
 * @param {*} settings
 */
 export async function sqlTransStandardType(cicadaManyJoinOutputColumns, settings) {
    return await reqUtil.post({
        url : "/cicada/manyjoin/transStandardType" ,
        settings
    },cicadaManyJoinOutputColumns)
}

/**
 * 多join 查外面流下来的表
 * @param {*} tranStepId
 */
 export async function queryJoinTable(tranStepId,settings) {
    return await reqUtil.get({
        url : "/cicada/manyjoin/queryJoinTable?tranStepId=" + tranStepId ,
        settings
    })
}

/**
 * 多join 查表的树
 * @param {*} tranStepId
 */
 export async function getDataSetTree(settings) {
    return await reqUtil.get({
        url : "/cicada/manyjoin/getDataSetTree" ,
        settings
    })
}

/**
 * 多join 查对应表的字段
 * @param {*} tranStepId
 */
 export async function getDataSetColumns(dataSetId,settings) {
    return await reqUtil.get({
        url : "/cicada/manyjoin/getDataSetColumns?dataSetId=" + dataSetId ,
        settings,
    })
}

/**
 * 码表转换插件
 * @param {*} tranStepId
 * @param {*} settings
 */
export async function codeQueryData(tranStepId,settings) {
    return await reqUtil.get({
        url : add + "/codeConversion/queryData?tranStepId=" + tranStepId ,
        settings,
    })
}
/**
 * 码表转换插件保存
 * @param {*} tranStepId
 * @param {*} settings
 */
export async function codeSavePlugin({tranStepId, cicadaCodeConversionSettings,settings}) {
    return await reqUtil.post({
        url : add + "/codeConversion/savePlugin?tranStepId=" + tranStepId ,
        settings,
    }, {cicadaCodeConversionSettings})
}

export async function prviewDataWithColumn({dataSetId, addDataSetId, page, pageSize, columnList,settings}) {
    return await reqUtil.post({
        url : "/editDataSet/prviewDataWithColumn" ,
        settings,
    }, {dataSetId, addDataSetId, page, pageSize, columnList})
}

/**
 * 添加kafka生产步骤
 * @param {*} param0
 */
export async function kafkaOutputStepConfigSaveOrUpdate({ acks,kafkaSourceId,kafkaSourceName,retries,routingStrategyCode,topic  , stepId, settings}) {
    return await reqUtil.post({
        url: '/cicada/kafkaOutput/savePlugin?tranStepId=' + stepId,
        settings
    }, { acks,kafkaSourceId,kafkaSourceName,retries,routingStrategyCode,topic    , stepId,});
}

/**
 * 获取kafka生产步骤
 * @param {*} param0
 */
export async function getKafkaOutputDetail({stepId,  settings}) {
    return await reqUtil.get({
        url: '/cicada/kafkaOutput/queryData?tranStepId=' + stepId,
        settings
    });
}

/**
 * 添加kafka消费
 * @param {*} param0
 */
export async function kafkaInputStepConfigSaveOrUpdate({  kafkaSourceId, groupId, kafkaSourceName, topics , stepId, settings}) {
    return await reqUtil.post({
        url:  '/cicada/kafkaInput/savePlugin?tranStepId=' + stepId,
        settings
    }, { kafkaSourceId, groupId, kafkaSourceName, topics    , stepId,});
}

/**
 * 获取kafka消费步骤
 * @param {*} param0
 */
export async function getKafkaInputDetail({stepId,  settings}) {
    return await reqUtil.get({
        url: '/cicada/kafkaInput/queryData?tranStepId=' + stepId,
        settings
    });
}

export async function getKafkaInstanceTree({stepId,  settings}) {
    return await reqUtil.get({
        url: '/manage/dataWarehouse/getKafkaInstanceTree',
        settings
    });
}

export async function getAcksType({stepId,  settings}) {
    return await reqUtil.get({
        url: '/cicada/kafkaOutput/getAcksType',
        settings
    });
}

export async function getKafkaInputPreview({stepId, cnt = 1, settings}) {
    return await reqUtil.get({
        url: '/cicada/kafkaInput/previewKafka?stepId=' + stepId + "&count="+cnt,
        settings
    });
}

export async function getKafkaOutputPreview({stepId, cnt = 1, settings}) {
    return await reqUtil.get({
        url: '/cicada/kafkaOutput/previewKafka?stepId=' + stepId + "&count="+cnt,
        settings
    });
}

/**
 * kafka消费根据code获取消费主题中文名
 * @param {*} param0
 */
export async function getFactNameByCode({codes,  settings}) {
    return await reqUtil.post({
        url: '/manage/dataWarehouse/getFactNameByCode',
        settings
    }, {codes});
}
