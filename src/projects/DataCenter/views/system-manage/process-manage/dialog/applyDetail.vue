<!--
    @Describe: 申请列表的审批详情
    @Author: wangjt
    @Date: 2024/4/18
-->
<template>
    <el-form label-position="right" label-width="100px">
        <el-form-item v-for="(item , k) in formData" :key="k" :label="`${item.label} :`">
            <el-input v-model="form[k]" :disabled="item.disabled" v-if="item.formType === 'input'"
                      :maxlength="item.maxlength"
                      show-word-limit :placeholder="item.placeholder"></el-input>
            <dg-select v-model="form[k]" v-if="item.formType === 'select'" :disabled="item.disabled"
                       :data="item.data()" :placeholder="item.placeholder"></dg-select>
            <span v-if="item.formType === 'span'">{{ form[k] }}</span>
            <el-input v-model="form[k]" type="textarea" :maxlength="item.maxlength"
                      show-word-limit v-if="item.formType === 'textarea'"
                      :disabled="item.disabled"
                      resize="vertical"
                      :rows="item.rows" :placeholder="item.placeholder"></el-input>
        </el-form-item>
    </el-form>
</template>

<script>
import marketUse from "@store/modules/market/market-constant"

export default {
    name: "approvalDetail",
    mixins:[marketUse],
    props: {
        row: Object,
    },
    data() {
        return {
            form : {},
            formData: {
                resourceType: {
                    label: '资源类型',
                    formType: 'select',
                    disabled: true,
                    data: () => this.m_market_get_model_types()
                },
                resourceName: {
                    label: '资源名称',
                    formType: 'input',
                    disabled: true,
                },
                applyTime : {
                    label : '申请时间',
                    formType: 'input',
                    disabled: true,
                },
                applyUserName : {
                    label : '申请人',
                    formType: 'input',
                    disabled: true
                },
                auditUserName : {
                    label : '审批人',
                    formType: 'input',
                    disabled: true
                },
                modelEntry:{
                    label:'模型入口',
                    formType:'input',
                    disabled: true
                },
                auditState : {
                    label : '审批状态',
                    formType: 'input',
                    disabled: true,
                },
                auditOption : {
                    label : '审批意见',
                    formType: 'textarea',
                    disabled: true,
                    rows:4
                },
                auditTime : {
                    label : '审批时间',
                    formType: 'input',
                    disabled: true,
                },
            }
        }
    },
    methods: {
        getDetail() {
            this.$services('market').viewAuditDetail({id : this.row.id}).then(res => {
                if(res.data.status === 0) {
                    let result = this.form = res.data.data;
                    let rType = this.m_market_get_model_types().find(i => i.value === result.resourceType);
                    this.form.resourceType = rType && rType.label;
                    let state = this.m_market_audit_state.find(i => i.value === result.auditState);
                    this.form.auditState = state && state.label;
                    this.form.modelEntry = this.row.model_entry;
                }
            })
        }
    },
    created() {
        this.getDetail();
    }
}
</script>

<style scoped lang="less">

</style>
