<!--
    linxp
    2021.06.09
 -->
<template>
    <div class="component-panel">
        <!-- <dg-tree
            :data="data"
            node-key="id"
            default-props="defaultProps"
            :filter-node-method="filterNode"
            ref="tree"
            class="tree-card-wrap"
        >
            <span class="custom-tree-node tree-card" slot-scope="{ node, data }">
                <template v-if="node.level === 1">
                    <i :class="['dg-iconp', data.icon]"></i>
                    <span :title="data.label">{{ data.label }}</span>
                </template>
<template v-else>
                    <div class="custom-tree-node__leaf node--primary"
                         :draggable="true"
                         @dragstart.stop="addNode(data)"
                         @dragend="dragend"
                         :class="{
                                'node--info': transPlugins.indexOf(data.keyWord) > -1,
                                'node--success': outputPlugins.indexOf(data.keyWord) > -1,
                                'node--chart': chartsPlugins.indexOf(data.keyWord) > -1,
                            }"
                         :title="data.description ? data.label + ': '+data.description : data.label"
                    >
                        <svg class="eda_icon">
                            <use :xlink:href="data.icon"></use>
                        </svg>
                        <span>{{ data.label }}</span>
                    </div>
                </template>

</span>
</dg-tree> -->
        <plugin-tree ref="plugin" :transPlugins="transPlugins" :outputPlugins="outputPlugins"
            :chartsPlugins="chartsPlugins" :doubleInputPlugins="doubleInputPlugins" :pluginColor="pluginColor"
            @addNode="addNode" @dragend="dragend" :data="data"></plugin-tree>
    </div>
</template>
<script>
import PlugIcons from '@/assets/data/plug-icons.json'
import { treeMethods } from "@/api/treeNameCheck/treeName"
import { sourceMixins } from "@/projects/DataCenter/views/modeling/dialog/newModel/model/function-detail/source-mixins";
import { servicesMixins } from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";
import { getPluginColor, getDescription } from "@/assets/data/model-plugin/plugin-name-description";
import pluginTree from "@/components/flowPanel/flow-plugin/plugin-tree/plugin-tree"

export default {
    name: "component-panel",
    mixins: [treeMethods, sourceMixins, servicesMixins],
    props: {
        loadParams: Object,
        componentType: String,
    },
    components: { pluginTree },
    data() {
        return {
            data: [],
            defaultProps: {
                children: "children",
                label: "label"
            },
            transPlugins: getPluginColor().transPlugins,//中间插件 黄色
            outputPlugins: getPluginColor().outputPlugins, //输出插件 绿色
            chartsPlugins: getPluginColor().chartsPlugins, //图表插件
            doubleInputPlugins: getPluginColor().doubleInputPlugins,
            showComponent: [],
            pluginColor: {
                blue: getPluginColor().blue,
                orange: getPluginColor().orange,
                cyan: getPluginColor().cyan,
                purple: getPluginColor().purple,
            }
        };
    },
    methods: {
        setFilterText(val) {
            this.$refs.plugin.filterList(val);
        },
        setShowPlugin() {
            const vm = this;
            switch (vm.componentType) {
                case '数据查询':
                case '信息核查':
                case 'AI算法':
                    vm.showComponent = [];
                    break;
                case '比对订阅':
                case '数据碰撞':
                case '模型分析':
                    vm.showComponent = ['数据碰撞', '数据筛选', '服务组件'];
                    break;
                default: vm.showComponent = [];
            }
        },
        initTree() {
            const vm = this, { services, loadParams } = vm;
            vm.data = [];
            loadParams.loading = true;
            vm.setShowPlugin();
            services.getPluginDir(loadParams).then(res => {
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.data = vm.setPluginList(result);
                    vm.$store.dispatch("setPluginTree", vm.data)
                    if (vm.showComponent.length) {
                        vm.data = vm.data.filter(n => vm.showComponent.includes(n.label))
                    }
                }
            })
        },
        /**
         * 插件一级目录
         * @param data
         * @return {[]}
         */
        setPluginList(data) {
            let result = [], index = 0;
            for (let k in data) {
                let item = {
                    id: index.toString(),
                    label: k,
                    icon: PlugIcons[k],
                    extend: false,
                    appear: false,
                    children: this.setPluginChild(data[k])
                };
                if (item.children.length > 0) {
                    index++;
                    result.push(item);
                }
            }
            return result;
        },
        /**
         * 插件二级目录
         * @param data
         * @return {[]}
         */
        setPluginChild(data) {
            let result = [];
            data.forEach(li => {

                result.push({
                    id: li.pluginId,
                    label: li.pluginName,
                    icon: PlugIcons[li.pluginCode],
                    pluginDir: li.pluginDir,
                    keyWord: li.pluginCode,
                    description: getDescription()[li.pluginCode],
                    type: 'plug',
                    color: ""
                })
            })
            return result;
        },

    }
};
</script>
<style lang="less" scoped src="../source-style.less"></style>
