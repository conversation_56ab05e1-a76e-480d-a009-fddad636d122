<template>
    <div class="list" v-loading="settings.loading">
        <div class="table-temp-head">
            <div class="table-temp-head-search">{{listTip}}</div>
            <div class="table-temp-head-btns">
                <dg-button v-for="(btn , i) in listBtns" :key="i" :type="btn.type" v-if="btn.show" :icon="btn.icon" @click="btn.clickFn">{{btn.label}}</dg-button>
            </div>
        </div>
        <div class="table-temp-content" v-if="!settings.loading">
            <common-table
                    row-key=""
                    :data="tableData"
                    :columns="tHeadData"
                    :paging-type="isMock ? 'client' : 'server'"
                    :pagination-props="paginationProps"
                    class="width100"
                    height="100%"
                    :pagination-total="total"
                    @change-current="isMock ? ()=>{} : changePage($event) "
                    @change-size="changeSize"
            >
            </common-table>
        </div>
        <sync-logic ref="syncLogic" @appendColumns="appendColumns"></sync-logic>
        <merge-dialog ref="merge" @mergeSuccess="mergeSuccess"></merge-dialog>
        <filter-condition ref="filterCon" @preview="preview"></filter-condition>
    </div>
</template>

<script src="./list.js"></script>
<style scoped lang="less" src="./list.less"></style>