<template>
    <el-card class="fold-card" shadow="never" :body-style="{ padding: 0 }">
        <div class="comm-header" slot="header">
            <div class="fold-card__title">
                <span>{{ title }}</span>
                <el-popover
                    placement="bottom"
                    :title="tipTittle"
                    :width="tipWidth"
                    trigger="hover"
                >
                    <el-image :src="src"></el-image>
                    <i v-if="showTip" slot="reference" class="dg-iconp icon-help"></i>
                </el-popover>
            </div>
            <div class="fold-card__close" @click="open = !open">
                <span v-if="open">
                    <i class="dg-iconp icon-l-down"></i>
                    收起
                </span>
                <span v-else>
                    <i class="dg-iconp icon-l-up"></i>
                    展开
                </span>
            </div>
        </div>
        <el-collapse-transition>
            <div v-show="open" class="fold-card__content">
                <slot />
            </div>
        </el-collapse-transition>
    </el-card>
</template>
<script>
export default {

    name: "fold-card",
    props: {
        // 标题
        title: {
            type: String,
            default: "标题"
        },
        // 是否展示提示
        showTip: {
            type: Boolean,
            default: true
        },
        // tips 提示
        tips: {
            type: String,
            default: ""
        },
        // tips width
        tipWidth: {
            type: [Number,String],
            default: 200
        },
        // tip 标题
        tipTittle: {
            type: String,
            default: '标题'
        },
    },
    data() {
        return {
            open: true
        };
    },
    computed: {
            src(){
                switch (this.tips) {
                    case "mergecol":
                        return require('@/assets/images/common/pic4.png');
                    case "merge":
                        return require('@/assets/images/common/pic2.png');
                    default:
                        return require('@/assets/images/common/pic3.png');
                }
            }
    }
};
</script>
<style lang="less" scoped>
.comm-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 28px;
}
.fold-card {
    /deep/.el-card__header{
        padding: 0;
    }
    &__title {
        span {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bold;
        }
        i {
            margin-left: 7px;
            font-weight: normal;
            color: rgba(0, 0, 0, 0.45);
            cursor: pointer;
        }
    }
    &__close {
        user-select: none;
        font-weight: normal;
        cursor: pointer;
        i {
            font-size: 16px;
        }
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
}
</style>