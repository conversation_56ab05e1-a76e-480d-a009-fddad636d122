import {commonMixins} from "@/api/commonMethods/common-mixins"
import {servicesMixins} from "../../../service-mixins/service-mixins";
import pageHeader from "@/components/layout/page-header/index.vue"
import {common} from "@/api/commonMethods/common"
export default {
    name: "headerPage" ,
    mixins: [ commonMixins, servicesMixins , common],
    props : {
        name : String ,
        treeNodeId : String ,
        datasetId : String,
        treeNode : Object
    },
    components : {
        pageHeader
    },
    computed : {
        linkStep (){
            const vm = this;
            return [...vm.has_edit , vm.syncColumn];
        }
    },
    data(){
        return {
            title_txt : "名称",
            popVis :false ,
            //操作历史记录
            history: [],
            his_btn_txt :"操作轨迹",
            save_btn_txt : "保存",
            save_other_btn_txt : "另存为",
            back_btn_txt : "返回",
            roundBackground: "#65c23a", //圆点颜色
            maxlength : 100 ,
            showTxt : true ,
            anaName : this.name ,
            noData : "暂无数据" ,
            has_edit :["editColumn" , "addColumn" ,"filterCondition" ,"joinTable" ,"unionTable"],
            syncColumn : "syncColumn"
        }
    },
    methods : {
        toRead(){
            this.showTxt  = true;
        },
        toEdit(){
            const vm = this;
            vm.showTxt  = false;
            vm.$nextTick(()=>{
                vm.$refs.name_input.focus();
            })
        },
        editHistory(data , canEdit){
            const vm = this, {history} = this;
            let index = history.indexOf(data);
            if(index < history.length -1 && canEdit){
                vm.confirm("提示" , "编辑步骤后可能对后面的步骤产生影响" , ()=> {
                    vm.getDataSetStepInfo(data , canEdit);
                })
            }else {
                vm.getDataSetStepInfo(data , canEdit);
            }
        },
        getDataSetStepInfo(data , canEdit){
            const vm = this, {datasetServices, datasetMock  ,history} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            services.getDataSetStepInfo(data.dataSetStepId).then(res => {
                if(res.data.status === 0){
                    let result = res.data.data;
                    switch (data.code) {
                        case 'editColumn' :
                            vm.$emit("editColumnStep" , {result , canEdit : canEdit ,stepId : data.dataSetStepId});
                            break;
                        case 'addColumn':
                            vm.$emit("addColumnStep" , {result , canEdit : canEdit, stepId : data.dataSetStepId});
                            break;
                        case 'filterCondition':
                            vm.$emit("filterConditionStep" , {result , canEdit : canEdit, stepId : data.dataSetStepId});
                            break;
                        case 'joinTable':
                            vm.$emit("joinTableStep" , {result , canEdit : canEdit, stepId : data.dataSetStepId});
                            break;
                        case 'syncColumn':
                            vm.$emit("syncColumnStep" , {result , canEdit : false, stepId : data.dataSetStepId});
                            break;
                        case 'unionTable':
                            vm.$emit("unionTableStep" , {result , canEdit : canEdit, stepId : data.dataSetStepId});
                            break;
                        default:
                            break;
                    }
                }
            })
        },
        deleteHistory(data){
            const vm = this, {datasetServices, datasetMock ,datasetId  ,history} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            let index = history.indexOf(data);
            let info = "确认删除此步骤?";
            if(index < history.length -1){
                info = "确认删除此步骤？删除后可能对后面的步骤产生影响";
            }
            vm.confirm("删除" , info , ()=>{
                services.deleteDataStep(data.dataSetStepId , datasetId).then(res => {
                    if(res.data.status === 0){
                        vm.$message.success("删除成功");
                        vm.$emit("deleteStepSuccess");
                    }
                })
            })
        },
        showSaveOther(){
            this.$emit("showSaveOther" );
        },
        saveDataset(){
            this.$emit("saveDataset" , this.anaName);

        },
        backTo(){
            this.$router.push({name : "dataSetOperation" , params : {treeNodeId : this.treeNodeId , treeNode : this.treeNode}});
        },
        init(){
            const vm = this, {datasetServices, datasetMock ,datasetId ,settings} = this;
            let services = vm.getServices(datasetServices, datasetMock);
            services.getDataSetStepRelation(datasetId , settings).then(res => {
                settings.loading = true;
                if(res.data.status === 0){
                    vm.history = res.data.data.map(item => {
                        item.iconColor = item.status === 1 ? "#F56C6C" : '#65c23a';
                        item.icon = item.status === 1 ? "el-icon-warning" : "el-icon-success";
                        item.noBorder = true;
                        return item;
                    });
                }
            })
        }
    },
    created(){

    }
}
