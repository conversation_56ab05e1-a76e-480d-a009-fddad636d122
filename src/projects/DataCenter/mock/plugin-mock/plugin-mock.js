import data from "./index"
import Mock from "mockjs"
import * as reqUtil from "@/api/reqUtil";

/*
* sql输入插件
* @params
* @returns {Promise<*>}
* */

export async function sqlView(transStepId , settings) {
    settings.loading = false;
    return await Mock.mock(data.sqlView);
}
/*
* sql 参数
* @params
* @returns {Promise<*>}
* */

export async function sqlColumns(transStepId , dataObjId ,customDataSet ,settings ) {
    settings.loading = false;
    return await Mock.mock(data.sqlColumns);
}

/*
* sql 预览
* @params
* @returns {Promise<*>}
* */

export async function previewCondition(transStepId , dataObjId ,conditions ) {
    return await Mock.mock(data.previewCondition);
}
export async function previewSql(transStepId , dataObjId ,cnt="10" , sql , settings ) {
    settings.loading = false;
    return await Mock.mock(data.previewSql);
}
/*
* SQL增量信息
* @params
* @returns {Promise<*>}
* */
export async function sql_incr(transStepId ,incrementComparisonOp , insertTime , updateTime ,isIncrement= "1" ) {
    return await Mock.mock(data.sql_incr);
}
/*
* SQL取消增量信息
* @params
* @returns {Promise<*>}
* */
export async function sql_no_incr(transStepId ) {
    return await Mock.mock(data.sql_no_incr);
}
/*
* SQL保存
* @params
* @returns {Promise<*>}
* */
export async function save_conditions(transStepId , dataObjId ,sql ,fetchSize ,customDataSet , settings ) {
    settings.loading = false;
    return await Mock.mock(data.save_conditions);
}
/*
* SQL保存
* @params
* @returns {Promise<*>}
* */
export async function save_sql(transStepId , dataObjId ,fetchSize ,customDataSet,conditions , settings ) {
    settings.loading = false;
    return await Mock.mock(data.save_sql);
}

/*
* 初始化
* @params
* @returns {Promise<*>}
* */

export async function advanceConfig(tranStepId) {
    return await Mock.mock(data.advanceConfig);
}
/*
* 获取输出字段
* @params
* @returns {Promise<*>}
* */

export async function showOutputColumn(tranStepId) {
    return await Mock.mock(data.showOutputColumn);
}

/*
* 保存 变更
* @params  url , tranStepId   , dynamicParamJson
* @returns {Promise<*>}
* */
export async function saveDynamic( url  ,tranStepId , dynamicParamJson) {
    return await Mock.mock(data.saveDynamic);
}
/*
* 获取 变更
* @params  url , tranStepId   , dynamicParamJson
* @returns {Promise<*>}
* */
export async function getDynamic( url , tranStepId ) {
    return await Mock.mock(data.getDynamic);
}
/*
* 获取分区信息
* @params
* @returns {Promise<*>}
* */

export async function partitionPage(url , tranStepId) {
    return await Mock.mock(data.partitionPage);
}
/*
* 保存输出字段
* @params
* @returns {Promise<*>}
* */
export async function modifyColumn(  tranStepId , field ,length ,precsn , valType ,uniqueValue, filterExpress ) {
    return await Mock.mock(data.modifyColumn);
}

/*
* 保存排序设置
* @params
* @returns {Promise<*>}
* */
export async function saveSort(  tranStepId , sortColumn ,sortType ) {
    return await Mock.mock(data.saveSort);
}

/*
流式窗口
*  初始化
 * @param tranStepId
 * @returns {Promise<*>}
* */
export async function getPlugin( transtepId , settings) {
    return await Mock.mock(data.getStreamigPlugin);
}

/*
流式窗口
*  保存
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function savePlugin( params ,settings ) {
    return await Mock.mock(data.saveStreamigPlugin);
}

//初始化全文库输入的分区类型
/*
*  分区类型
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_columns( tranStepId) {
    return await Mock.mock(data.fulltext_columns);
}
/*
*  排序字段
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_getSort( tranStepId) {
    return await Mock.mock(data.fulltext_getSort);
}
/*
*  保存排序字段
 * @param windows transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_saveSort( tranStepId , sortColumn , sortType) {
    return await Mock.mock(data.fulltext_saveSort);
}
/*
*  保存分区
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function savePartition( url ) {
    return await Mock.mock(data.savePartition);
}
/*
*  全文库初始化
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_view( tranStepId , settings ) {
    settings.loading = false;
    return await Mock.mock(data.fulltext_view);
}
/*
*  全文库 预览
 * @param transtepId
 * @returns {Promise<*>}
* */
export async function fulltext_preview( params , settings ) {
    settings.loading = false;
    return await Mock.mock(data.fulltext_preview);
}
/*
*  全文库 保存
 * @param
 * @returns {Promise<*>}
* */
export async function fulltext_save( params , settings ) {
    settings.loading = false;
    return await Mock.mock(data.fulltext_save);
}
/*
*  kv 初始化
 * @param
 * @returns {Promise<*>}
* */
export async function kvInputPage( tableId , tranStepId ,settings ) {
    settings.loading = false;
    return await Mock.mock(data.kvInputPage);
}

/*
*  kv 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveKVInputPlugin( tranStepId  , params , settings ) {
    settings.loading = false;
    return await Mock.mock(data.saveKVInputPlugin);
}
/*
*  kv 预览
 * @param
 * @returns {Promise<*>}
* */
export async function kvInputPreview(tableId , tranStepId  , params , settings ) {
    settings.loading = false;
    return await Mock.mock(data.kvInputPreview);
}

/*
*  插件 预览
 * @param
 * @returns {Promise<*>}
* */
export async function pluginPreview(transId , tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.pluginPreview);
}

/*
*  全连接
 * @param
 * @returns {Promise<*>}
* */
export async function fullJoinPluginPage( tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.fullJoinPluginPage);
}

/*
*  全连接 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveFullJoinPlugin( tranStepId ,params , settings){
    settings.loading = false;
    return await Mock.mock(data.saveFullJoinPlugin);
}
/*
*  交集
 * @param
 * @returns {Promise<*>}
* */
export async function innerJoinPluginPage( tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.innerJoinPluginPage);
}
/*
*  交集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveInnerJoinPlugin( tranStepId ,params , settings){
    settings.loading = false;
    return await Mock.mock(data.saveInnerJoinPlugin);
}
/*
*  左右连接
 * @param
 * @returns {Promise<*>}
* */
export async function leftOrRightJoinPluginPage( tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.leftOrRightJoinPluginPage);
}
/*
*  左右连接 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveLeftOrRightJoinPlugin( tranStepId ,params , settings){
    settings.loading = false;
    return await Mock.mock(data.saveLeftOrRightJoinPlugin);
}
/*
*  左右排除
 * @param
 * @returns {Promise<*>}
* */
export async function subtractByKeyPluginPage( tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.subtractByKeyPluginPage);
}
/*
*  左右排除 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveSubtractByKeyPlugin( tranStepId ,params , settings){
    settings.loading = false;
    return await Mock.mock(data.saveSubtractByKeyPlugin);
}

/*
*  并集
 * @param
 * @returns {Promise<*>}
* */
export async function unionJoinPluginPage( tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.unionJoinPluginPage);
}
/*
*  并集 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveUnionJoinPlugin( tranStepId ,params , settings){
    settings.loading = false;
    return await Mock.mock(data.saveUnionJoinPlugin);
}

/*
*  算子编排
 * @param
 * @returns {Promise<*>}
* */
export async function operatorPage( tranStepId ,settings){
    settings.loading = false;
    return await Mock.mock(data.operatorPage);
}

/*
*  算子编排 保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveOperator( tranStepId , params , settings){
    settings.loading = false;
    return await Mock.mock(data.saveOperator);
}
/*
*  算子编排 删除
 * @param
 * @returns {Promise<*>}
* */
export async function deleteGraph( graphId){
    return await Mock.mock(data.deleteGraph);
}
/*
*  算子编排 画布 步骤名称修改
 * @param
 * @returns {Promise<*>}
* */
export async function updataServiceOrchestrationNode( params ){
    return await Mock.mock(data.updataServiceOrchestrationNode);
}
/*
*  算子编排 画布 保存端点
 * @param
 * @returns {Promise<*>}
* */
export async function savaServiceOrchestrationEdge( params ){
    return await Mock.mock(data.savaServiceOrchestrationEdge);
}
/*
*  算子编排 获取 连线option
 * @param
 * @returns {Promise<*>}
* */
export async function serviceParams( serviceId ){
    return await Mock.mock(data.serviceParams);
}

/*
*  算子编排 删除 连线
 * @param
 * @returns {Promise<*>}
* */
export async function deleteServiceOrchestrationEdge( fromNodeId , toNodeId ){
    return await Mock.mock(data.deleteServiceOrchestrationEdge);
}

/*
*  算子编排 回显
 * @param
 * @returns {Promise<*>}
* */
export async function getRuleGraph( params ){
    return await Mock.mock(data.getRuleGraph);
}
/*
*  算子编排 连线
 * @param
 * @returns {Promise<*>}
* */
export async function saveEdge( serviceOrgId , params ){
    return await Mock.mock(data.saveEdge);
}

/*
*  新建表注册
 * @param param id
 * @returns {Promise<*>}
* */
export async function register( id ) {
    return await Mock.mock(data.register);
}

/*
*  关系库输出初始化
 * @param saveVo
 * @returns {Promise<*>}
* */
export async function sqlOutView( transStepId ,settings ) {
    settings.loading = false;
    return await Mock.mock(data.sqlOutView);
}

/*
*  关系库输出保存
 * @param saveVo
 * @returns {Promise<*>}
* */
export async function sqlOutSave( saveVo ,settings ) {
    settings.loading = false;
    return await Mock.mock(data.sqlOutSave);
}

/*
*  关系库输出 导入字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutCreateTableMeta( transStepId ,schemaId ) {
    return await Mock.mock(data.sqlOutCreateTableMeta);
}

/*
*  关系库输出 获取目标字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutColumns( transStepId ,dataObjId ) {
    return await Mock.mock(data.sqlOutColumns);
}
/*
*  关系库输出 获取来源字段
 * @param
 * @returns {Promise<*>}
* */
export async function sqlOutMeta( transStepId ) {
    return Mock.mock(data.sqlOutMeta);
}

/*
*  数据排序
 * @param
 * @returns {Promise<*>}
* */
export async function dataSortPluginPage( tranStepId ,settings) {
    settings.loading = false;
    return Mock.mock(data.dataSortPluginPage);
}
/*
*  数据排序
 * @param
 * @returns {Promise<*>}
* */
export async function getInputColumn( tranStepId ) {
    return Mock.mock(data.getInputColumn);
}
/*
*  数据排序保存
 * @param
 * @returns {Promise<*>}
* */
export async function saveDataSortPlugin( tranStepId, pluginMeta ,settings) {
    settings.loading = false;
    return Mock.mock(data.saveDataSortPlugin);
}

/*
*  表达式处理 获取输入字段
* @param
* @returns {Promise<*>}
* */
export async function queryPrevNodeVariables(tranStepId , type , nodeId ="" ) {
    return Mock.mock(data.queryPrevNodeVariables);
}
/*
*  表达式处理 获取算子树
* @param
* @returns {Promise<*>}
* */
export async function getOperatorTree(settings) {
    return Mock.mock(data.getOperatorTree);
}

/*
*  表达式处理 新增udf节点
* @param udfNodeVo
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfNode(udfNodeVo) {
    return Mock.mock(data.saveOrUpdateUdfNode);
}

/*
*  表达式处理 获取图
* @param graphId
* @returns {Promise<*>}
* */
export async function saveOrUpdateUdfGraph(udfGraphVo) {
    return Mock.mock(data.saveOrUpdateUdfGraph);
}

/**
 * 条件 过滤插件
 * @param tranStepId
 * @returns {Promise<*>}
 */
const con_root = "/conditionFilter/";
export async function conditionFilterQueryData(tranStepId , settings) {
    return Mock.mock(data.conditionFilterQueryData);
}
//保存
export async function conditionSavePlugin(tranStepId , pluginMeta , settings) {
    return Mock.mock(data.conditionSavePlugin);
}

/*
*  表达式处理 获取输入类型
* @param graphId
* @returns {Promise<*>}
* */
export async function getParameterTypes() {
    return await Mock.mock(data.getParameterTypes);
}


/**
 * 归一化插件
 * @param tranStepId
 * @param settings
 * @returns {Promise<*>}
 */
export async function getNormalizationPage(tranStepId , settings) {
    settings.loading = false;
    return Mock.mock(data.getNormalizationPage);
}
//保存
export async function saveNormalizationMeta(tranStepId , pluginMeta , settings) {
    settings.loading = false;
    return Mock.mock(data.saveNormalizationMeta);
}



/**
 * 文件上传插件
 * */

//保存
export async function saveFileInputPlugin(tranStepId, file, settings) {
    settings.loading = false;
    return Mock.mock(data.saveFileInputPlugin);
}


export async function getFileInputPluginPage(tranStepId, settings) {
    settings.loading = false;
    return Mock.mock(data.getFileInputPluginPage);
}

export async function getFiledWithRealtime(tranStepId, sheetName, startRow ,startColumn,useHeader,settings) {
    settings.loading = false;
    return Mock.mock(data.getFiledWithRealtime);
}
/**
 * 文件输出插件
 * */
const fileout_root = "/plugin/fileOutput/";
export async function saveFileOutputPlugin(tranStepId, fileOutputMeta, settings) {
    settings.loading = false;
    return Mock.mock(data.saveFileOutputPlugin);
}

export async function getFileTypeAndFields(tranStepId, settings) {
    settings.loading = false;
    return Mock.mock(data.getFileTypeAndFields);
}

export async function getFileOutputPluginPage(tranStepId, settings) {
    settings.loading = false;
    return Mock.mock(data.getFileOutputPluginPage);
}
export async function getFields(tranStepId, settings) {
    settings.loading = false;
    return Mock.mock(data.getFields);
}

export async function getDownloadIsOk(tranStepId, settings) {
    settings.loading = false;
    return Mock.mock(data.getDownloadIsOk);
}

/**
 * 同行插件
 **/

/**
 * 初始化
 * @param tranStepId
 * @return {Promise<*>}
 */
export async function getPeerView(tranStepId){
    settings.loading = false;
    return Mock.mock(data.getPeerView);
}

/**
 * 保存
 * @param tranStepId
 * @param pluginMeta
 * @param settings
 * @return {Promise<*>}
 */
export async function savePeer(tranStepId , pluginMeta ,settings){
    settings.loading = false;
    return Mock.mock(data.savePeer);
}

/*
* 时间打标
* */

/**
 * 初始化
 * @param tranStepId
 * @param settings
 * @return {Promise<*>}
 */
const markTimeRoot = "/markingtime/";
export async function markingTimeView(tranStepId , settings){
    settings.loading = false;
    return Mock.mock(data.markingTimeView);
}

export async function saveMarkingTime(tranStepId , pluginMeta , settings){
    settings.loading = false;
    return Mock.mock(data.saveMarkingTime);
}
