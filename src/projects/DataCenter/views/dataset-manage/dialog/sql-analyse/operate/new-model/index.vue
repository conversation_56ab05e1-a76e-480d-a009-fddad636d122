<template>
    <common-dialog custom-class="newModel"
                   v-loading="settings.loading"
                   :title="title" :width="width" :visible.sync="visible" @closed="clearData">
       <new-model-name v-if="reNew" ref="newName" :treeNode="treeNode" :label="label"></new-model-name>
        <span slot="footer" class="dialog-footer">
            <dg-button @click="visible = false">{{btnCancelTxt}}</dg-button>
            <dg-button type="primary" @click="submit">{{btnCheckTxt}}</dg-button>
        </span>
    </common-dialog>
</template>

<script src="./new-model.js"></script>