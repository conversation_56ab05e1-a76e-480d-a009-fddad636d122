/**
 * 数据建模 接口
 * @Author: wangjt
 * @Date: 2020-07-07
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'

const root = "/dataModeling/", root1 = "/transOperator/", root2 = "/transSchedule/", root3 = "/mlsql/", root4 = "/dataMining/";

/*
*  查询 建模 模型 树
 * @param
 * @returns {Promise<*>}
* */

export async function queryTransTree(parentId, parentName, dirType, settings) {
    return await reqUtil.get({
        url: root + "queryTransTree",
        settings: settings
    }, { parentId, parentName, dirType })
}

/*
*  新增子目录
 * @param classifyId , classifyName , dirType
 * @returns {Promise<*>}
* */

export async function createTransClassify(classifyId, classifyName, dirType, settings) {
    return await reqUtil.post({
        url: root + "createTransClassify",
        settings
    }, { classifyId, classifyName, dirType })
}

/*
*  删除子目录
 * @param
 * @returns {Promise<*>}
* */

export async function deleteTransClassify(transClassifyId) {
    return await reqUtil.get({
        url: root + "deleteTransClassify"
    }, { transClassifyId })
}

/*
*  重命名子目录
 * @param
 * @returns {Promise<*>}
* */

export async function updateTransClassify(currClassifyId, transClassifyName) {
    return await reqUtil.get({
        url: root + "updateTransClassify"
    }, { currClassifyId, transClassifyName })
}

/*
*  查询列表
 * @param
 * @returns {Promise<*>}
* */

export async function queryTransList(params, settings) {
    return await reqUtil.post({
        url: root + "queryTransList",
        settings: settings
    }, params)
}

/*
*  执行
 * @param
 * @returns {Promise<*>}
* */

export async function updateSchedule(transId) {
    return await reqUtil.get({
        url: root + "updateSchedule",
    }, { transId })
}

/*
*  Mlsql 执行
 * @param
 * @returns {Promise<*>}
* */

export async function updateScheduleMl(transId) {
    return await reqUtil.get({
        url: "/mlsql/run",
    }, { transId })
}

/*
*  保存发布配置
 * @param
 * @returns {Promise<*>}
* */

export async function updateTransSchedule(week, params) {
    return await reqUtil.post({
        url: root + "updateTransSchedule?scheduleWeek=" + week,
    }, params)
}

export async function updateDataMiningSchedule(transId, scheduleType, cron, declare) {
    return await reqUtil.get({
        url: root4 + "updateSchedule",
    }, { transId, scheduleType, cron, declare })
}

export async function updateDataMiningScheduleWithSubTrans(startProgramsVos) {
    return await reqUtil.post({
        url: root4 + "updateScheduleWithSubTrans",
    }, startProgramsVos)
}


export async function getSchedule(transId, settings) {
    return await reqUtil.get({
        url: root4 + "getSchedule",
        settings
    }, { transId })
}
/*
*  保存分布式
 * @param
 * @returns {Promise<*>}
* */

export async function updateTransDistribute(tranStepId, distributed) {
    return await reqUtil.get({
        url: root + "updateTransDistribute",
    }, { tranStepId, distributed })
}
/*
*  保存错误级别
 * @param
 * @returns {Promise<*>}
* */

export async function updateTransLogLevel(tranStepId, logLevel) {
    return await reqUtil.get({
        url: root1 + "updateTransLogLevel",
    }, { tranStepId, logLevel })
}
/*
*  保存处理模式
 * @param
 * @returns {Promise<*>}
* */

export async function updateHandleMode(tranStepId, handleMode) {
    return await reqUtil.get({
        url: root + "updateHandleMode",
    }, { tranStepId, handleMode })
}
/*
*  激活
 * @param
 * @returns {Promise<*>}
* */

export async function activeJob(transId, settings) {
    return await reqUtil.get({
        url: root2 + "activeJob",
        settings: settings
    }, { transId })
}
/*
*  停用
 * @param
 * @returns {Promise<*>}
* */

export async function stopJob(id, settings) {
    return await reqUtil.get({
        url: root3 + "killJob",
        settings: settings
    }, { id })
}
/*
*  清空
 * @param
 * @returns {Promise<*>}
* */

export async function cleanJob(transId) {
    return await reqUtil.get({
        url: root2 + "cleanJob",
    }, { transId })
}
/*
*  获取状态
 * @param
 * @returns {Promise<*>}
* */

export async function jobStatus(transId) {
    return await reqUtil.get({
        url: root2 + "jobStatus",
    }, { transId })
}

/**
 * 获取状态（mlsql）
 * @param transId
 * @returns {Promise<*>}
 */
export async function jobStatusMlsql(transId) {
    return await reqUtil.get({
        url: root3 + "jobStatus",
    }, { transId })
}
/*
*  初始配置
 * @param
 * @returns {Promise<*>}
* */

export async function baseSettingPage(transId) {
    return await reqUtil.get({
        url: root + "baseSettingPage",
    }, { transId })
}
/*
*  复制方案
 * @param
 * @returns {Promise<*>}
* */

export async function copyTrans(transId, dirParentId) {
    return await reqUtil.get({
        url: root1 + "copyTrans",
    }, { transId, dirParentId })
}
/*
*  删除方案
 * @param
 * @returns {Promise<*>}
* */

export async function deleteTrans(transId) {
    return await reqUtil.get({
        url: root1 + "deleteTrans",
    }, { transId })
}
/*
*  获取模型所有节点
 * @param
 * @returns {Promise<*>}
* */

export async function getTreeAllNode(dirType, settings) {
    return await reqUtil.get({
        url: root + "getTreeAllNode",
        settings: settings
    }, { dirType })
}
/*
* 修改模型名称
* @params
* @return {Promise<*>}
* */
export async function updateTransStepName(transStepId, transName) {
    return await reqUtil.get({
        url: root1 + "updateTransStepName",
    }, { transStepId, transName })
}

/*
* 连线
* @params
* @return {Promise<*>}
* */
export async function addTransHop(fromTransName, toTransName, fromTransId, toTransId, transId) {
    return await reqUtil.get({
        url: root1 + "addTransHop",
    }, { fromTransName, toTransName, fromTransId, toTransId, transId })
}

/**
 * 连线 带有端点
 * @param fromTransName
 * @param toTransName
 * @param fromTransId
 * @param toTransId
 * @param transId
 * @param direction 左 "/left" 右 "/right"
 * @return {Promise<*>}
 */
export async function addTransHopWithDirection(fromTransName, toTransName, fromTransId, toTransId, transId, direction) {
    return await reqUtil.get({
        url: root1 + "addTransHopWithDirection",
    }, { fromTransName, toTransName, fromTransId, toTransId, transId, direction })
}

/*
* 删除连线
* @params
* @return {Promise<*>}
* */
export async function deleteTransHop(fromTransId, toTransId, transId) {
    return await reqUtil.get({
        url: root1 + "deleteTransHop",
    }, { fromTransId, toTransId, transId })
}

/**
 * 快速分析 中间新增插件
 *          beforeTransName  A 插件名字  beforeTransId  A 插件id
 *          currendTransName C 插件名字 currendTransId C 插件id
 *          afterTransName B 插件名字  afterTransId B 插件id
 *          transId 当前方案的id
 * @param beforeTransName
 * @param beforeTransId
 * @param currendTransName
 * @param currendTransId
 * @param afterTransName
 * @param afterTransId
 * @param beforeDirection
 * @param afterDirection
 * @param transId
 * @param settings
 * @return {Promise<*>}
 */

export async function addQuickAnalysisTransHop(beforeTransName, beforeTransId, currendTransName, currendTransId, afterTransName, afterTransId, beforeDirection, afterDirection, transId, settings) {
    return await reqUtil.post({
        url: root1 + "addQuickAnalysisTransHop",
        settings
    }, { beforeTransName, beforeTransId, currendTransName, currendTransId, afterTransName, afterTransId, beforeDirection, afterDirection, transId })
}
/*
* 改变位置
* @params
* @return {Promise<*>}
* */
export async function moveTransStep(x, y, transStepId) {
    return await reqUtil.get({
        url: root1 + "moveTransStep",
    }, { x, y, transStepId })
}
/*
* 添加插件
* @params
* @return {Promise<*>}
* */
export async function addPluginTranStep(pluginCode, transName, x, y, parentTransId) {
    return await reqUtil.get({
        url: root1 + "addPluginTranStep",
    }, { pluginCode, transName, x, y, parentTransId })
}
/*
* 初始化插件
* @params
* @return {Promise<*>}
* */
export async function insertAutoNewPlugin(tranStepId, dataObjId) {
    return await reqUtil.get({
        url: "/plugin/dataSet/init",
        errorMsg: false
    }, { tranStepId, dataObjId })
}
/*
* 初始化输入插件
* @params
* @return {Promise<*>}
* */
export async function initPlug(params, root_path, settings) {
    return await reqUtil.get({
        url: root_path + "init",
        errorMsg: false,
        settings
    }, params)
}
/*
* 删除插件
* @params
* @return {Promise<*>}
* */
export async function deleteTransStep(stepId, transId) {
    return await reqUtil.get({
        url: root1 + "deleteTransStep",
    }, { stepId, transId })
}

/*
* 删除插件
* @params
* @return {Promise<*>}
* */
export async function deleteTransStepforQuick(stepId, transId) {
    return await reqUtil.get({
        url: root1 + "deleteTransStepforQuick",
    }, { stepId, transId })
}
/*
* 验证
* @params
* @return {Promise<*>}
* */
export async function deleteCheck(params) {
    return await reqUtil.post({
        url: "/pluginManage/deleteCheck",
    }, params)
}

/**
 * 添加校验
 * @param params
 * @return {Promise<*>}
 */
export async function addCheck(params) {
    return await reqUtil.post({
        url: "/pluginManage/addCheck",
    }, params)
}

/**
 * 删除输入验证
 * @param params
 * @return {Promise<*>}
 */
export async function deleteInputCheck(params) {
    return await reqUtil.post({
        url: "/pluginManage/deleteInputCheck",
    }, params)
}
/*
* 回显插件
* @params
* @return {Promise<*>}
* */
export async function loadTransPage(transId, settings, loadType = "1") {
    return await reqUtil.get({
        url: root + "loadTransPage",
        settings
    }, { transId, loadType })
}
/*
* 预览结果集
* @params
* @return {Promise<*>}
* */
export async function getDataSets(transId) {
    return await reqUtil.get({
        url: root + "getDataSets",
        errorMsg: false
    }, { transId })
}


/*
*  保存
 * @param
 * @returns {Promise<*>}
* */
export async function updateTrans(params, settings) {
    return await reqUtil.post({
        url: root2 + 'updateTrans',
        settings: settings
    }, params);
}

/*
*  保存
 * @param
 * @returns {Promise<*>}
* */
export async function updateDataMiningTrans(params, settings) {
    return await reqUtil.post({
        url: root4 + 'updateTrans',
        settings: settings
    }, params);
}

/*
*  保存为模板
 * @param transId
 * @returns {Promise<*>}
* */
export async function save(transId) {
    return await reqUtil.get({
        url: '/template/save',
    }, { transId });
}

/*
*  数据视图
 * @param
 * @returns {Promise<*>}
* */
export async function getOriginDataListByView(params) {
    return await reqUtil.post({
        url: root + 'getOriginDataListByView',
    }, params);
}
/*
*  数据视图
 * @param
 * @returns {Promise<*>}
* */
export async function getOriginDataList(params) {
    return await reqUtil.post({
        url: root + 'getOriginDataList',
    }, params);
}
/*
*  开始执行
 * @param
 * @returns {Promise<*>}
* */
export async function startJob(transId) {
    return await reqUtil.get({
        url: root2 + 'startJob',
    }, { transId });
}

/*
*  开始执行
 * @param
 * @returns {Promise<*>}
* */
export async function runJob(transId, executorServerName, type) {
    return await reqUtil.get({
        url: root4 + 'taskRun',
    }, { transId, executorServerName, type });
}

/*
* 获取发布插件参数
* @param
 * @returns {Promise<*>}
* */

export async function getModelServicePublishConfig(transId, settings) {
    return await reqUtil.get({
        url: "modelServicePublish/getModelServicePublishConfig",
        settings: settings
    }, { transId })
}
/*
* 发布模型
* @param
 * @returns {Promise<*>}
* */

export async function createService(params, settings) {
    return await reqUtil.post({
        url: "modelServicePublish/createService",
        settings: settings
    }, params)
}

/*
* 新建模型
* @param transName
 * @returns {Promise<*>}
* */

export async function saveTempTrans(transName, transType = "") {
    return await reqUtil.get({
        url: root + "saveTempTrans",
    }, { transName, transType })
}

/**
 * sql
 * @param transId
 * @param settings
 * @return {Promise<*>}
 */
export async function getFlowSql(transId, type, settings) {
    return await reqUtil.get({
        url: "/dataPreview/getSQL",
        settings
    }, { transId, type })
}

/**
 * 目录移动
 * @param curryClassifyId
 * @param newParentClassifyId
 * @param dirType
 * @param settings
 * @return {Promise<*>}
 */
export async function moveTreeDir(curryClassifyId, newParentClassifyId, dirType, settings) {
    return await reqUtil.get({
        url: "/dataModeling/moveDirectory",
        settings
    }, { curryClassifyId, newParentClassifyId, dirType })
}

/**
 * 3.0 获取建模插件目录
 * @param settings loading参数
 * @return {Promise<*>}
 */
export async function getPluginDir(settings) {
    return await reqUtil.get({
        url: "/pluginManage/getPluginDir",
        settings
    })
}

/**
 * 3.0 获取单个插件的状态
 * @param tranStepId
 * @param settings
 * @return {Promise<*>}
 */
export async function getPluginState(tranStepId, settings) {
    return await reqUtil.get({
        url: "/pluginManage/getPluginState",
        settings
    }, { tranStepId })
}

/**
 * 模型另存为
 * @param params
 * @param settings
 * @return {Promise<*>}
 */
export async function modelSaveOther(params, settings) {
    return await reqUtil.post({
        url: `/transOperator/asSaveTrans`,
        settings
    }, params)
}


/**
 * 全局变量获取数据类型
 */
export async function getDataType() {
    return await reqUtil.get({
        url: "udfOperatorManage/getParameterTypes"
    })
}
/**
 * 全局变量获取
 * 过滤后的 数据类型
 */
export async function getVariableParameterTypes() {
    return await reqUtil.get({
        url: "udfOperatorManage/getVariableParameterTypes"
    })
}

/**
 * 全局变量 根据方案id获取全局变量
 * @param tranStepId
 * @return {Promise<*>}
 */
export async function queryTransByTransId(tranStepId, settings) {
    return await reqUtil.get({
        url: "variableManage/queryTransByTransId?transId=" + tranStepId,
        settings
    })
}

/**
 * 根据方案id获取方案所用变量
 * @param tranStepId
 * @return {Promise<*>}
 */
export async function queryTransOwnerByTransId(transId, settings) {
    return await reqUtil.get({
        url: "variableManage/queryTransOwnerByTransId?transId=" + transId,
        settings
    })
}

/**
 * 全局变量保存
 * @param {}} params
 * @param {*} settings
 */
export async function saveTransVariable(transVariableVo, settings) {
    return await reqUtil.post({
        url: "variableManage/saveTransVariable",
        settings
    }, transVariableVo)
}

/**
 * 定时执行批量删除
 */
export async function deleteBatchSubTrans(maps) {
    return await reqUtil.post({
        url: root4 + "deleteBatchSubTrans",
    }, maps)
}

/**
 * 移动
 * @param elementId
 * @param classifyId
 * @return {Promise<*>}
 */
export async function moveModel(elementId, classifyId) {
    return await reqUtil.get({
        url: root + "moveModel"
    }, { elementId, classifyId })
}


/**
 * 运行日志
 */
/**
 * 运行日志change
 * @param {*} data
 */
export async function changeLogPage(data, settings) {
    return await reqUtil.post({
        url: "/taskLog/transTaskLogListPage",
        settings
    }, data)
}
/**
 * 查看日志
 * @param {*} groupId
 * @param {*} settings
 */
export async function searchLog(groupId, settings) {
    return await reqUtil.get({
        url: "/mlsql/getSubtasksByJobId?groupId=" + groupId,
        settings
    })
}
export async function deleteLog(groupId, settings) {
    return await reqUtil.get({
        url: "/mlsql/transDeleteHistoryTaskLog?taskId=" + groupId,
        settings
    })
}

/**
 * 快速分析转建模
 * @param transId
 * @param plugins
 * @param busiClassifyId 目录id
 * @param settings
 * @return {Promise<*>}
 */
export async function quickTransToProcessTrans(transId, plugins, busiClassifyId, settings) {
    return await reqUtil.post({
        url: root4 + "quickTransToProcessTrans",
        settings
    }, { transId, plugins, busiClassifyId })
}

/**
 * 回显模型数据
 * @param transId
 * @return {Promise<*>}
 */
export async function getSubTrans(transId) {
    return await reqUtil.get({
        url: root4 + "getSubTrans"
    }, { transId })
}

/**
 * 参数设置，算子获取
 * @param {*} settings
 */
export async function queryUdfVariableList(settings) {
    return await reqUtil.get({
        url: "variableManage/queryUdfVariableList",
        settings
    })
}

const aiRoot = "/transOperator/ai/";
/**
 * AI建模定时执行保存
 * @param {*} params
 */
export async function saveScheduledTask(params) {
    return await reqUtil.post({
        url: aiRoot + "saveScheduledTask",

    }, params)
}

/**
 * AI建模定时执行-初始化可选模型
 * @param {*} settings
 */
export async function getAiModelDirWithChildren(settings) {
    return await reqUtil.get({
        url: aiRoot + "getAiModelDirWithChildren",
        settings
    })
}

/**
 * AI建模-定时执行-回显
 * @param {*} id
 * @param {*} settings
 */
export async function getAISchedule(id, settings) {
    return await reqUtil.get({
        url: aiRoot + "getSchedule?transId=" + id,
        settings
    })
}
/**
 * AI建模-定时执行-回显
 * @param {*} id
 * @param {*} settings
 */
export async function getAISubTrans(id, settings) {
    return await reqUtil.get({
        url: aiRoot + "getSubTrans?transId=" + id,
        settings
    })
}

/**
 * 模型服务
 * @param {*} settings
 */
export async function getPluginModelServiceList(firstClassify, secondClassify, settings) {
    return await reqUtil.post({
        url: "/publishManagement/getPluginModelServiceList",
        settings
    }, { firstClassify, secondClassify })
}

/**
 * 模型服务左侧分类
 * @param {*} settings
 */
export async function getModelServiceClassify(settings) {
    return await reqUtil.get({
        url: "/publishManagement/getModelServiceClassify",
        settings
    },)
}

/**
 * 模型详情 - 左侧
 * @param {*} id
 * @param {*} settings
 */
export async function getApiList(id, type, settings) {
    return await reqUtil.get({
        url: "dataModeling/getApiList?modelId=" + id + "&type=" + type,
        settings
    })
}

/**
 * 模型详情 - 右侧列表
 * @param {*} id
 * @param {*} settings
 */
export async function getServiceDetailById(id, settings) {
    return await reqUtil.get({
        url: "dataModeling/getServiceDetailById?serviceId=" + id,
        settings
    })
}


/**
 * 分享情况 - 分享列表
 * @param transId
 * @param objId
 * @param resourceName
 * @param pageNum
 * @param pageSize
 * @param settings
 * @return {Promise<*>}
 */
export async function getSharesByTran(data, settings) {
    return await reqUtil.post({
        url: root + "getSharesByTran",
        settings
    }, data)
}

/**
 * 分享情况 - 获取分享的资源
 * @param tranId
 * @param settings
 * @return {Promise<*>}
 */
export async function getResourceByTran(tranId, settings) {
    return await reqUtil.get({
        url: root + "getResourceByTran?tranId=" + tranId,
        settings
    })
}

/**
*  获取模型节点  过滤了非输入输出插件的模型
 * @param
 * @returns {Promise<*>}
*/

export async function queryModelServiceSourceTree(param, settings) {
    return await reqUtil.post({
        url: root + "queryModelServiceSourceTree",
        settings
    }, param)
}

export async function isCollectionLog(settings) {
    return await reqUtil.get({
        url: '/log/isCollectionLog',
        settings
    })
}

export async function getLogsByGroupId({ groupId, settings }) {
    return await reqUtil.get({
        url: '/mlsql/getLogsByGroupId',
        settings
    }, { groupId })
}

export async function getJumpLogUrl({ jobId, groupId, settings }) {
    return await reqUtil.get({
        url: '/mlsql/getJumpLogUrl',
        settings
    }, { jobId, groupId })
}

/**
 * 任务停止
 * @param {*} param0 
 */
export async function taskStop({ transId, type, settings }) {
    return await reqUtil.get({
        url: root4 + '/taskStop',
        settings
    }, { transId, type })
}

/**
 * 导入解析
 */
export async function parseDataCenterModel({ file, settings }) {
    return await reqUtil.post({
        url: '/dataTransImportExport/parseDataCenterModel',
        settings
    }, file, {
        headers: {
            'Content-Type': 'multipart/form-data;charset=utf-8',
        }
    });
}

export async function importDataCenterModel({ filterIds, filterType, userId, settings }) {
    return await reqUtil.post({
        url: '/dataTransImportExport/importDataCenterModel',
        settings
    }, { filterIds, filterType, userId, })
}

/**
 *  获取引擎列表
 * @returns {Promise<*>}
 */
export async function engineList(settings) {
    return await reqUtil.get({
        url: root4 + "engineList",
        settings
    })
}

/**
 *  判断该目录下是否有模型被引用
 * @returns {Promise<*>}
 */
export async function isDeleteTransClassify(transClassifyId, settings) {
    return await reqUtil.get({
        url: root + "isDeleteTransClassify?transClassifyId=" + transClassifyId,
        settings
    })
}

/**
 *  查看指标（融合）：任务指标日志的接口
 * @returns {Promise<*>}
 */
export async function queryMetricByJobId({ jobId, settings }) {
    return await reqUtil.get({
        url: "/log/queryMetricByJobId?jobId=" + jobId,
        settings
    })
}

/**
 *  flink详情
 * @returns {Promise<*>}
 */
export async function queryLogUrlByJobId({ applicationName, jobId, settings }) {
    return await reqUtil.get({
        url: "/log/queryLogUrlByJobId?applicationName=" + applicationName + "&jobId=" + jobId,
        settings
    })
}

/**
 * 模型列表-刷新状态
 */
export async function refreshTransStatus({ transIds, settings }) {
    return await reqUtil.post({
        url: root + 'refreshTransStatus',
        settings
    }, { transIds });
}

/**
 * 调度器执行日志详情
 * @param transIds
 * @param settings
 * @returns {Promise<*>}
 */
export async function getTransJobDetail({ detailId, settings }) {
    return await reqUtil.get({
        url: root + 'getTransJobDetail',
        settings
    }, { detailId });
}

/**
 * 步骤sql保存方法
 * @param params
 * @param settings
 * @returns {Promise<*>}
 */
export async function saveStepSql(params={},settings){
    return await reqUtil.get({
        url: root1 + 'saveStepSql',
        settings,
    },params)
}
