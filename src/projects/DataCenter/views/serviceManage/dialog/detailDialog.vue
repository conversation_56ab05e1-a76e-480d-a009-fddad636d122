<template>
    <div class="RelationBase">
        <common-dialog
            class="params-dialog"
            title="API详情"
            width="1200px"
            height="100%"
            :visible.sync="visible"
            @closed="clear_data"
        >
            <!--参数配置弹窗内容页-->
            <detail ref="group" v-if="showParams" v-bind="$attrs" :detailInfo="detailInfo"></detail>
        <!-- <div slot="footer">
            <el-button size="mini" @click="visible = false">取消</el-button>
        </div> -->
        </common-dialog>
    </div>
</template>

<script>
import detail from "../detail/index"
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/serviceManage/service-mixins/service-mixins"
import {commonMixins} from "@/api/commonMethods/common-mixins"
export default {
    name: "newDialog",
    mixins : [servicesMixins, commonMixins],
    components: {detail},
    props:{
    },
    data(){
        return {
            visible :false,
            showParams : false,
            detailInfo : [],
        }
    },
    methods: {
        clear_data(){
            this.visible = false;
            this.showParams = false;
        },
        show(id, type){
            type ? this.getModelDetailInfo(id) : this.getDetailInfo(id);
            this.visible = true;
        },
        getDetailInfo(id){
            const vm = this, {serviceManageServices, serviceManageMock, settings} = this;
            let services = vm.getServices(serviceManageServices, serviceManageMock);
            settings.loading = true;
            services.queryServiceDetails(id, settings).then(res => {
                if (res.data.status === 0) {
                    res.data.data.serviceType = res.data.data.serviceType ==='4'?"查询服务":"-";
                    this.detailInfo = res.data.data;
                    this.showParams = true;
                }
            })
        },
        //设置paramId
        setParamName(info){
            info.paramId = info.id ;
            info.paramCode = info.code;
            if(info.children && info.children.length){
                info.children.forEach(n=>{
                    this.setParamName(n)
                })
            }
        },
        getModelDetailInfo(id){
            const vm = this , {settings} = this;
            settings.loading = true;
            let services = this.$services('modeling');
            vm.detailInfo = {};
            services.getServiceDetailById(id).then(res =>{
                if (res.data.status === 0) {
                    let result = res.data.data;
                    vm.detailInfo.interfaceChineseName = result.apiName;
                    vm.detailInfo.serviceType = result.apiType;
                    vm.detailInfo.requestPath = result.requestPath;
                    vm.detailInfo.requestMethod =  result.requestType,
                    vm.detailInfo.interfaceVersion =  result.version,
                    vm.detailInfo.memo =  result.memo;

                    result.inputParams.forEach(n=> {
                        vm.setParamName(n)
                    });
                    result.outputParams.forEach(n=> {
                        vm.setParamName(n)
                    });
                    vm.detailInfo.paramList = result.inputParams[0].children;
                    vm.detailInfo.ginsengList = result.outputParams[0].children;
                    vm.showParams = true;
                }
            })
        }
    },
}
</script>

<style scoped>
</style>
