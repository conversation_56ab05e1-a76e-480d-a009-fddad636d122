.ce-collection {
    height: 100%;
    overflow: hidden;
}
.ce-label_title {
    float: left;
    color: rgba(0,0,0,0.65);
    font-size: 14px;
    width:160px;
    text-align: right;
    > span {
        color: #F56C6C;
    }
}

.tabelName {
    overflow:hidden;
    width: 100%;
    height: 21px;
    white-space: nowrap;
    text-align: left;
    text-overflow:ellipsis;
    color: rgba(0,0,0,0.45);
}

.ce-plugin{
    &_base {
        height: calc(100% - 54px);
        overflow-y: auto;
        overflow-x:hidden;
        width:100%;
    }
    &_attr {
        height: calc(100% - 54px);
        overflow: hidden;
    }
    &_head {
        display:flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    &_input {
        width:270px;
    }
    &_contend {
        padding: 0 10px;
        margin-left: 180px;
        margin-bottom: 10px;
    }
}
.radio-group {
    float: left;
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}