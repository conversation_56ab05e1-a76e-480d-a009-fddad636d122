/**
 * 主题门户 接口
 * @Author: chenzt
 * @Date: 2020-12-23
 * @Project cicada-dataCenter-webui
 */
import * as reqUtil from '@/api/reqUtil'


const root = "/portal";
/*
*  查询 主题门户 树
 * @param
 * @returns {Promise<*>}
* */

export async function queryPortalTree( settings ) {
    return await reqUtil.get({
        url :  root + "/queryTree" ,
        settings : settings
    })
}

/*
*  新增子目录
 * @param
 * @returns {Promise<*>}
* */

export async function addPortalTreeNode( pId , name ) {
    return await reqUtil.get({
        url : root + "/addTreeNode" ,
    }, {pId , name})
}
/*
*  删除子目录
 * @param
 * @returns {Promise<*>}
* */

export async function deletePortalTreeNode( nodeId ) {
    return await reqUtil.post({
        url : root + "/deleteTreeNode?nodeId="+ nodeId,
    })
}
/*
*  编辑子目录
 * @param
 * @returns {Promise<*>}
* */

export async function editPortalTreeNode( id, pId,name ) {
    return await reqUtil.post({
        url : root + "/editTreeNode?id=" + id + "&pId=" + pId +"&name="+name,
    })
}

/*
*  查询主题门户列表
 * @param
 * @returns {Promise<*>}
* */

export async function getPortalList( dateMap , settings ) {
    return await reqUtil.post({
        url : root + "/getPortalList" ,
        settings : settings
    } ,dateMap)
}

/**
 *  创建门户
 * @returns {Promise<*>}
 * @param dirId 树节点id
 * @param PotalConfigVo
 * @param settings
 * */
export async function createOrUpdatePortal(dirId , PotalConfigVo , settings ) {
    return await reqUtil.post({
        url : root + "/createOrUpdatePortal?dirId=" + dirId ,
        settings
    } ,PotalConfigVo)
}
/**
*  删除门户
 * @param portalId 门户id
 * @returns {Promise<*>}
* */

export async function deletePortal( portalId ) {
    return await reqUtil.get({
        url : root + "/deletePortal" ,
    } ,{portalId})
}

/*
*  保存logo
 * @param
 * @returns {Promise<*>}
* */

export async function saveLogo( file, portalConfigId) {
    return await reqUtil.post({
        url : root + "/saveLogo" ,
    } ,{file, portalConfigId})
}

/*
*  获取门户配置
 * @param
 * @returns {Promise<*>}
* */

export async function getPortalConfig( portalId, settings) {
    return await reqUtil.get({
        url : root + "/getPortalConfig" ,
        settings: settings
    } ,{portalId})
}
/*
*  通过门户地址获取门户信息
 * @param
 * @returns {Promise<*>}
* */

export async function getPortalPageByUrl( portalPublishUrl) {
    return await reqUtil.get({
        url : root + "/getPortalPageByUrl" ,
    } ,portalPublishUrl)
}
/*
*  获取数据类型
 * @param
 * @returns {Promise<*>}
* */

export async function getEnumContentType( ) {
    return await reqUtil.get({
        url : root + "/getEnumContentType" ,
    } )
}

/*
*  获取当前编辑的用户id
 * @param
 * @returns boolean
* */

export async function getPortalConcurrentUserId(id ) {
    return await reqUtil.get({
        url : root + "/getPortalConcurrentUserId" ,
    }, id)
}

/*
*  删除当前编辑的用户id
 * @param
 * @returns Result.success()
 * 页面关闭调用
* */

export async function deleteConcurrentUserId(id ) {
    return await reqUtil.get({
        url : root + "/deleteConcurrentUserId" ,
    }, id)
}

/*
*  安全策略
 * @param
 * @returns {Promise<*>}
* */

export async function upDataSecurityModeAndTime( data , settings ) {
    return await reqUtil.get({
        url : root + "/upDataSecurityModeAndTime" ,
        settings : settings
    } ,data)
}


export async function getAllUserAuthCopy(  dataSetId,settings  ) {
    return await reqUtil.post({
        url: '/dataSetAuth/getAllUserAuth',
        settings
    } , dataSetId);
}

export async function dataSetsAuthOneCopy( dataSetAuthVo , settings , path ) {
    return await reqUtil.post({
        url: '/dataSetAuth/' + path ,
        settings : settings
    } , dataSetAuthVo);
}

/*
*  菜单授权
 * @param
 * @returns {Promise<*>}
* */
export async function dataSetsAuth( dataSetAuthVo  ) {
    return await reqUtil.post({
        url: '/portal/portalAuth'
    } , dataSetAuthVo);
}
/*
*  批量取消授权
 * @param dataSetAuthVo
 * @returns {Promise<*>}
* */
export async function cancelPortalAuth( dataSetAuthVo  ) {
    return await reqUtil.post({
        url: '/dataSetAuth/cancelDataSetsAuth'
    } , dataSetAuthVo);
}

/*
*  菜单权限回填
 * @param
 * @returns {Promise<*>}
* */

export async function backfill( portalId ) {
    return await reqUtil.get({
        url :  "/menuConfig/queryTree?portalId=" + portalId ,
    })
}

/*
*  发布门户
 * @param
 * @returns {Promise<*>}
* */
export async function release( url, name, portalId, stat ) {
    return await reqUtil.get({
        url :  root + "/savePublishUrl"  ,
    }, {url,name,portalId,stat})
}

/**
 * 复制另存为
 * @param params
 * classifyId是目录id
 * {"id":"376f9eaf929f4be287581a17b6771412","classifyId":"f0e766cb5ded43dfb1a886c62451df86","portalName":"复制新建门户1"}
 * @param settings
 * @returns {Promise<*>}
 */
export async function copy( params ,settings ) {
    return await reqUtil.post({
        url : root +  "/copy" ,
        settings
    },params)
}
