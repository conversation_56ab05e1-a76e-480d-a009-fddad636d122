import {globalBus} from "@/api/globalBus";
import {common} from "@/api/commonMethods/common";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "../../service-mixins/service-mixins";
import ratio from "../dialog/ratio-form/index"
import AxisNodeList from "../axis-node-list"
import ChartBase from "../chart-base"
import DataFilterSelect from '../dataFilter/DataFilterSelect'
import FilterDialog from '../dataFilter/FilterDialog'
import DataNodeTree from '../DataNodeTree'

export const attrMixins = {
    mixins: [common, commonMixins, servicesMixins],
    components: {ratio, AxisNodeList , ChartBase , DataFilterSelect , FilterDialog , DataNodeTree},
    data() {
        return {
            showDrill: true,
            datasetId: '',
            classifierStatId: '',
            dataset: {},
            attrData: {
                chart: '',
                color: [],
                dataLabel: {
                    show: true,
                    labelPos: "top",
                    labelShow: "all",
                },
            },
            titleStyle: {
                type: "default",
                fontFamily: "sans-serif",
                fontSize: 12,
                fontWeight: false,
                fontStyle: false,
                //textDecoration : false,
                textAlign: "center",
                color: "#555",
                titleTxt: "",
            },
            tip: ' 0 为固定数据不自动刷新',
            dragTreeNode: {},
            selectGroupId: 1,
            filterSelects: [],
            filterType: '',
            chartAttr: [],
        }
    },
    watch: {
        chartData: {
            handler(val) {
                const vm = this;
                if (this.node && !this.node.setData) {
                    vm.$emit('nodeDataRenew', this.node.id);
                }
            },
            deep: true
        }
    },
    computed : {
        hasLegend(){
            if(this.node && this.node.code){
                return this.node.code !== 'TableChartWidget';
            }
        },
    },
    methods: {
        /**
         * 添加过滤条件
         */
        addFilter() {
            this.filterSelects.push({
                value: [],
                options: [],
                data: {},
                filterResult: {},
            });
        },
        deleteDrillDime(inx) {
            const vm = this;
            let list = vm.axisList.drilling.list, listLen = list.length;
            let tip = inx === listLen - 1 ? '确定删除钻取层级?' : '确定删除钻取层级及其下钻层级?';
            let value = list[inx].value;
            if (value.length) {
                vm.confirm('提示', tip, () => {
                    list.splice(inx, listLen - inx);
                })
            } else {
                list.splice(inx, listLen - inx);
            }

        },
        addDrilling(dime, inx, index) {
            const vm = this;
            if (inx > 2) return;
            let pId = dime.value[index];
            let item = JSON.parse(JSON.stringify(dime));
            let field = dime.options[index].data;
            item = Object.assign(item, {value: [], options: [], pId, field});
            let list = vm.axisList.drilling.list[inx + 1];
            if (list) {
                if (list.pId !== pId) {
                    vm.axisList.drilling.list[inx + 1] = Object.assign(list, {value: [], options: [], pId, field})
                }
            } else {
                vm.axisList.drilling.list.push(item);
            }

        },
        deleteDrill(inx) {
            const vm = this;
            vm.confirm('提示', '确定删除钻取?', () => {
                vm.axisList.drilling.list = [];
            })
        },
        showDrilling(data, inx) {
            const vm = this;
            let {beforeData} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
            let item = {
                parent: data.options[inx].label,
                id: data.options[inx].value,
                field: data.options[inx].data,
                datasetId: nodeData.datasetId || nodeData.classifierStatId,
                code: data.options[inx].data.code,
                value: [],
                placeholder: '拖动维度字段到此处',
                options: [],
                multipleLimit: 1,
                showLimit: true,
            };
            if (vm.node.code === "TableChartWidget") {
                item.multipleLimit = 0;
                item.placeholder = "拖动维度字段到此处(不限个数)";
                item.showLimit = false;
            }
            let haveItem = vm.axisList.drilling.list.filter(dri => dri.id === item.id);
            if (!haveItem.length) {
                vm.axisList.drilling.list = [];
                vm.axisList.drilling.list.push(item);
            }
        },
        drillDropEnd(e, key) {
            const vm = this;
            let d_list = vm.axisList.drilling.list;
            vm.inputData();
            let multipleLimit = d_list[key].multipleLimit, value = d_list[key].value;
            if (vm.node.code !== 'TableChartWidget') {
                if (this.dragTreeNode.indexType !== "DIMENSION") {
                    this.$message.warning('请拖拽维度字段');
                    return;
                }
            }
            if (!multipleLimit || value.length < multipleLimit) {
                vm.addDrillData(key, vm.dragTreeNode.indexType);
            } else {
                vm.$message.warning('超出可选数量');
            }
        },

        editAlias(fieldId, val) {
            this.setOptionsAlias('xAxis', fieldId, val);
            this.setOptionsAlias('yAxis', fieldId, val);
        },
        setOptionsAlias(axis, fieldId, val) {
            const vm = this, {axisList} = this;
            if (axisList[axis].options) {
                axisList[axis].options.forEach(opt => {
                    if (opt.value === fieldId) {
                        opt.alias = val;
                        vm.$emit("setAxisTxt", axisList[axis].options, axis);
                    }
                })
            }
        },
        filterData(data, i) { //过滤器
            this.$refs.filter_dialog.open(data, i);
        },
        allowDrop(e) {
            if (e.dataTransfer.types.length) {
                e.preventDefault();
            }
        },
        updateValue(index, newArr) {
            const $this = this;
            this.filterSelects.forEach((sel, i) => {
                if (i === index) {
                    sel.value = newArr;
                    if (newArr.length > 0) {
                        sel.options = sel.options.filter(opt => {
                            return newArr.indexOf(opt.value) !== -1;
                        });
                        if (!$this.dragTreeNode.id) return;
                        sel.data = $this.dragTreeNode
                    } else {
                        sel.options = [];
                        sel.data = {};
                        sel.filterResult = {};
                    }

                }
            });
        },
        dropFilter(e, i, inx) {
            let multipleLimit = this.axisList[inx].multipleLimit,
                value;
            value = this.filterSelects.filter((sel, index) => {
                return index === i;
            })[0].value;

            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedFilterData(i);
            } else {
                this.$message.warning('过滤器仅限一个字段');
            }
        },
        inputData() {
            this.node.setData = false;
        },
        deleteFilter(inx) {
            //this.confirmDelete('确认删除该过滤器' , '删除' , this.doDeleteF ,{inx : inx ,id :id} )
            this.doDeleteF(inx);
            this.inputData();
        },
        doDeleteF(inx) {
            this.filterSelects.splice(inx, 1)
        },
        confirmDelete(msg, title, fn, params) {
            this.$confirm(msg + '?', title, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                fn(params.inx, params.id);
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        setFilter(data, index, isMenu, filterD) {
            if (isMenu) {
                this.filterSelects.push(filterD);
            }
            this.filterSelects.forEach((filter, i) => {
                if (i === index) {
                    filter.filterResult = data;
                }
            })
        },
        typeCheck(val, inx) {
            if (val === '' || isNaN(val)) {
                this.axisList[inx].value = 0;
                this.$message.warning('请输入数字')
            }
        },
        resetList(datasetId, dataset, isNew) { //重置数据
            const vm = this;
            if (isNew) {
                this.axisList.xAxis.value = [];
                this.axisList.xAxis.options = [];
                this.axisList.yAxis.value = [];
                this.axisList.yAxis.options = [];
                this.filterSelects = [];
                this.chartAttr = [];
                if (this.axisList.drilling) {
                    this.axisList.drilling.list = [];
                }
            }
            //设置 datasetId 、tableCode
            this.datasetId = datasetId;
            // this.classifierStatId = datasetId;
            vm.$nextTick(() => {
                let getDataset;
                if (!dataset) {
                    getDataset = vm.$refs.nodeTree.getDataSet();
                } else {
                    getDataset = dataset;
                }
                getDataset && getDataset.classifierStatId ? vm.classifierStatId = getDataset.classifierStatId : true;
                getDataset ? vm.dataset = JSON.parse(JSON.stringify(getDataset)) : true;
            })

        },
        setFormat(data, item, inx) {
            item.options[inx]['format'].value = this.copyArrayObj(data);
        },
        addSelectedFilterData(index) {
            let value, options, select, inx;
            this.filterSelects.forEach((sel, i) => {
                if (i === index) {
                    inx = i;
                    select = sel;
                }
            });
            value = select.value;
            options = select.options;
            this.filterSelects[inx].options = options.filter((opt, i) => {
                return i !== index;
            });
            this.filterSelects[inx].options.push({
                label: this.dragTreeNode.label,
                value: this.dragTreeNode.id,
            });
            if (value.indexOf(this.dragTreeNode.id) === -1) {
                this.filterSelects[inx].value.push(this.dragTreeNode.id);
            }
        },
        setSelectData(node) {
            this.dragTreeNode = node;
        },
        addYAxisAttr(yAxisData) {
            this.chartAttr.push(yAxisData);
        },
        getTreeNode() {
            globalBus.$on('treeNode', this.setSelectData);
        },
        /**
         * 获取 补齐时间
         * @return {{}}
         */
        getMakeUpData(){
            const vm = this , {visualServices, visualMock} = vm;
            let services = this.getServices(visualServices, visualMock);
            let timeData = {};
            timeData["none"] = {label: "无"};
            services.getDateGranularity().then(res => {
                if(res.data.code === 0){
                    let result = res.data.data;
                    for(let key in result){
                        timeData[result[key]] = {
                            label : key
                        }
                    }
                }
            })
            return timeData;
        },
        /**
         * 获取快速计算
         * @param type
         * @return {{}}
         */
        requestFastCount(type=true) {
            let {visualServices, visualMock} = this;
            let services = this.getServices(visualServices, visualMock);
            let fastCountData = {};
            fastCountData["none"] = {label: "无", disabled: false};
            services.requestFastCount().then(res => {
                let keys = Object.keys(res.data.data);
                let values = Object.values(res.data.data);
                for (let i = 0; i < values.length; i++) {
                    let reg = /[求\(\)]/g;
                    fastCountData[values[i]] = {
                        label: keys[i].replace(reg,''),
                        disabled: type
                    };
                }
            });
            return fastCountData;
        },
        setDropOpt(type) {
            let opt;
            if (type === "DIMENSION") {
                let dataMakeUp = this.getMakeUpData();
                opt = {
                    visible: false,
                    label: this.dragTreeNode.label,
                    value: this.dragTreeNode.id,
                    data: this.dragTreeNode,
                    alias: this.dragTreeNode.alias,
                    rank: {},
                    filter: {},
                };
                let dataMakeUpCharts = ["CombinationWidget","BarChartWidget" , "StackBarChartWidget" , "TransverseBarChartWidget" , "TransverseStackBarChartWidget" ,"LineChartWidget" ,"AreaGraphWidget"];
                if(dataMakeUpCharts.indexOf(this.node.code) > -1){
                    opt.dataMakeUp = {
                        label : "数值对齐",
                        value :"none" ,
                        name : "无",
                        children : dataMakeUp
                    }
                }
            } else if (type === "MEASURE") {
                this.addYAxisAttr(this.dragTreeNode);
                let fastCountDatacopy = this.requestFastCount(false); //请求快速计算数据
                opt = {
                    visible: false,
                    label: this.dragTreeNode.label,
                    value: this.dragTreeNode.id,
                    data: this.dragTreeNode,
                    alias: this.dragTreeNode.alias,
                    func: {},
                    rank: {},
                    filter: {},
                    duplicateF: {
                        label: '去重',
                        value: '',
                        func: 'duplicateF' //回调方法
                    },
                    fastCount: {},
                };
                let fastCharts = ['BarChartWidget', 'StackBarChartWidget', "TransverseBarChartWidget", "TransverseStackBarChartWidget", 'LineChartWidget', 'AreaGraphWidget', 'CombinationWidget'];
                if (type === "MEASURE" && fastCharts.indexOf(this.node.code) > -1) {
                    opt.fastCount = {
                        label: "快速计算",
                        value: "none",
                        name: "无",
                        children: fastCountDatacopy
                    };
                }
            }
            opt.rank = {
                label: '排序',
                value: 'none',
                name: '无',
                children: {
                    none: {
                        label: '无'
                    },
                    ascending: {
                        label: '升序'
                    },
                    descending: {
                        label: '降序'
                    },
                }
            };
            /*opt.format = {
                label: '数据格式',
                value: '' ,
                func : 'dataformat' // 回调方法名
            };*/
            opt.filter = {
                label: '过滤',
                func: 'filter'
            };
            let isExp = this.isExp(this.dragTreeNode.exp);
            type === "MEASURE" && this.node.code !== 'TableChartWidget' && !isExp ?
                opt.func = {
                    label: '汇总',
                    value: 'none',
                    name: '无',
                    children: {
                        none: {label: '无'},
                        sum: {label: '总和'},
                        count: {label: '计数'},
                        mean: {label: '平均值'},
                        max: {label: '最大值'},
                        min: {label: '最小值'},
                        // std : {label : '标准差'},
                        // variance : {label : '方差'},
                        //median : {label : '中位数'},
                    }
                } : true;
            return opt;
        },
        /**
         * 去重
         * @param data
         * @param item
         * @param index
         */
        duplicateFun( data , item , index){
            this.inputData();
            //console.log( data ,item , index);
            let duplicate =  item.options[index]['duplicateF'].value ;
            if( duplicate === '' || !duplicate ){
                item.options[index]['duplicateF'].value = true;
                item.options[index]['duplicateF'].name = '<span style="font-family: cursive;">√</span>';
            }else {
                item.options[index]['duplicateF'].value = false;
            }
            item.options[index].visible = false;
        },
        /**
         * //字段过滤
         * @param data
         */
        nodeFilter( data){
            this.inputData();
            let filterD = {
                value: [data.data.id],
                options: [
                    {
                        value :data.data.id ,
                        label : data.label
                    }
                ],
                data: data.data,
                filterResult: {},
            } , inx = this.filterSelects.length;
            this.$refs.filter_dialog.open(filterD , inx , true);
        },
        editRadioField(form) {//修改占比 字段
            const vm = this;
            let inx = form.index;
            let options = vm.axisList.xAxis.options[inx];
            let newVal = `_${inx}`;
            vm.axisList.xAxis.value[inx] += newVal;
            options.value += newVal;
            options.isRatio = true;
            options.label = form.label;
            options.alias = form.label;
            options.ratioOptions = form.options;
            options.func_val = form.func_val;
            options.isDivisor = "true";
            options.denominator = form.denominator;
            let axis = 'xAxis';
            vm.addAxisName(vm['axisList'][axis].options, axis, vm['axisList']);
        },
        addDrillData(inx, type) {
            const vm = this;
            let d_list = vm.axisList.drilling.list;
            let value = d_list[inx].value, options = d_list[inx].options;
            d_list[inx].options = options.filter(opt => {
                return opt.value !== vm.dragTreeNode.id;
            });
            d_list[inx].options.push(vm.setDropOpt(type));
            if (value.indexOf(vm.dragTreeNode.id) === -1) {
                value.push(vm.dragTreeNode.id);
            }
        },
        addSelectedData(inx, type, list) {
            let value = this[list][inx].value, options = this[list][inx].options;
            this[list][inx].options = options.filter(opt => {
                return opt.value !== this.dragTreeNode.id;
            });
            this[list][inx].options.push(this.setDropOpt(type));
            if (value.indexOf(this.dragTreeNode.id) === -1) {
                value.push(this.dragTreeNode.id);
            }
            this.addAxisName(this[list][inx].options, inx, this[list]);
        },
        deleteDrillNode(key, index) {
            const vm = this;
            let d_list = vm.axisList.drilling.list;
            let value = d_list[key].value, options = d_list[key].options;
            d_list[key].options = options.filter((opt, i) => {
                return i !== index;
            });
            d_list[key].value = value.filter((val, i) => {
                return i !== index;
            });
        },
        dropDownHide(optArr) {
            optArr.forEach(opt => {
                opt.forEach(item => {
                    item.visible = false;
                })
            })
        },
        /**
         * 添加维度度量字段名称
         * @param node
         * @param type
         * @param lists
         */
        addAxisName(node, type, lists) {
            this.$emit('setAxisName', node, type, lists);
        },
        deleteNode(key, index) {
            const vm = this;
            this.inputData();
            if (key === 'yAxis' && vm.axisList.drilling) {
                let value = vm.axisList[key].value[index];
                let d_list = vm.axisList.drilling.list;
                let drill = vm.axisList.drilling.list.filter(list => list.id === value);
                if (drill.length) {
                    vm.confirm('提示', '删除该维度会同并删除其钻取维度', () => {
                        vm.deleteAxis(key, index);
                        vm.axisList.drilling.list = d_list.filter(list => list.id !== value);
                    })
                } else {
                    vm.deleteAxis(key, index);
                }
            } else {
                vm.deleteAxis(key, index);
            }
        },
        deleteAxis(key, index) {
            let options = this.axisList[key].options, value = this.axisList[key].value;
            this.axisList[key].options = options.filter((opt, i) => {
                return i !== index;
            });
            this.axisList[key].value = value.filter((val, i) => {
                return i !== index;
            });
            if (key === 'xAxis') {
                this.chartAttr.splice(index, 1);
            }
            this.addAxisName(this.axisList[key].options, key, this.axisList);
        },
        changeFastCount(axis) {
            if (axis.prop !== 'xAxis') return;
            // let optionY = this.axisList.yAxis.options;
            let badge = false;
            /*for (let i = 0; i < optionY.length; i++) {
                if (!optionY[i].data.mbColumn) continue;
                badge = false;
                break;
            }*/
            this.changeFastCountTwo(false);
            if (badge === true && this.axisList.xAxis.options && this.axisList.xAxis.options.length) {
                this.axisList.xAxis.options.forEach(axis =>{
                    axis.fastCount.name =  "无";
                    axis.fastCount.value = "none";
                })
            }
        },
        changeFastCountTwo(type) {
            let optionX = this.axisList.xAxis.options;
            if (!optionX) return;
            for (let i = 0; i < optionX.length; i++) {
                if (optionX[i].fastCount && optionX[i].fastCount.children) {
                    for (let option in optionX[i].fastCount.children) {
                        if (optionX[i].fastCount.children[option].label !== "无")
                            optionX[i].fastCount.children[option].disabled = type;
                    }
                }

            }
        },
        /**
         * 设置时间格式
         * @param params
         * @param item
         * @param i
         */
        setTimeFormat(params, item, i) {

        },
        /**
         * 度量维度配置选项值改变
         *
         * @param params
         * @param item
         * @param i
         */
        changeVal(params, item, i) {
            this.inputData();
            item.options[i][params[1]].value = params[0];
            item.options[i][params[1]].name = params[2];
            item.options[i].visible = false;
            if (params[1] === 'func') { //函数
                this.setFunc && this.setFunc(params, item, i); //组合图 多个同字段度量 处理名称
            } else if (params[1] === 'fastCount') { //快速计算
                this.setFunc && this.setFunc(params, item, i);
            }
        },
    },
    created() {
        this.getTreeNode();
    },
    destroyed() {
        globalBus.$off('treeNode', this.setSelectData);
    }
};
