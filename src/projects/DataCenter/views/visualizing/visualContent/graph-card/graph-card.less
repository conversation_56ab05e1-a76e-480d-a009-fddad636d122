.graph_style{
    height:100%;
    width:100%;
}
.key_style{
    width:30%;
    height:auto;
    float: left;
    height: 40px;
    // overflow:hidden;
    // word-wrap: break-word;
    // word-break: break-all;
    /*溢出的变成。。。。*/
    // overflow: hidden;
    // text-overflow:ellipsis;
    // white-space: nowrap;

    white-space:normal; word-break:break-all;overflow:hidden;

    border-width: 1;
    border-style:none solid none none; 
    text-align:center;
    margin-right: 10px;
}
.value_style{
    width: 70%;
    height: 40px;
}
.all_style{
    border-width: 1px;
    border-style:solid solid solid solid; 
}
.table_style{
    width: 100%;
    table-layout: fixed;
}
.left_style{
    float: left;
    width:100%;
    text-align:center;
    // overflow:hidden;
    // word-wrap: break-word;
    // word-break: break-all;
    /*溢出的变成。。。。*/
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}
.right_style{
}