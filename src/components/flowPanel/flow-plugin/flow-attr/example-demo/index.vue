<template>
    <el-image  :src="src"></el-image>
</template>
<script>
export default {
    name: "example-demo",
    props: {
        code: {
            type: String,
            default: ""
        }
    },
    computed: {
        src() {
            const { code } = this;
            switch (code) {
                case "cicadaCollisionPlugin":
                    return require("@/assets/images/common/pic5.jpg");
                case "cicadaSubtractByKeyPlugin":
                    return require("@/assets/images/common/pic2.png");
                default:
                    return "";
            }
        }
    }
};
</script>
