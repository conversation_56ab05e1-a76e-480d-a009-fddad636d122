<!--
    目录编辑下拉
-->

<template>
    <el-popover ref="popover" v-model="value.isActive" placement="right" width="200" v-bind="$attrs"  trigger="click">
        <ul class="more-actions">
            <template v-for="(li , inx) in data">
                <li  :key="inx" :class="{'li-disabled': disabled ? !li.show(rights , node): false}" v-if="(disabled ? true: li.show(rights , node)) && li.name !== 'divider'" @click="menuClick(li)">{{li.name}}</li>
                <!--<el-divider :key="`${inx}_div`" v-if="li.show(rights , node) && li.name !== 'divider' && data.length >= 2 && inx === data.length - 2 "></el-divider>-->
            </template>
        </ul>
        <slot name="reference" slot="reference">
            <i class="dg-iconp icon-more-b" @click.stop.prevent></i>
        </slot>

    </el-popover>
</template>

<script>
export default {
    name: "dirEditAction" ,
    props : {
        //菜单数据
        data  : {
            type : Array ,
            default (){
                return [];
            }
        },
        //权限数据
        rights : {
            type : Array ,
            default (){
                return [];
            }
        },
        //节点数据
        node : {
            type : Object ,
            default (){
                return {};
            }
        },
        value : {
            type : Object ,
            default (){
                return {
                    isActive : false
                }
            }
        },
        disabled: {
            type : Boolean ,
            default: false,
        }
    },
    methods : {
        menuClick(data){
            this.$refs.popover.doClose();
            this.$emit("command" ,data);
        }
    }
}
</script>

<style scoped lang="less">
.li-disabled{
    color: rgba(0,0,0,.25);
    cursor: not-allowed;
}
.more-actions .li-disabled:hover {
    color: rgba(0,0,0,.25);
    cursor: not-allowed;
    background-color: transparent;
    pointer-events:none;
}
</style>
