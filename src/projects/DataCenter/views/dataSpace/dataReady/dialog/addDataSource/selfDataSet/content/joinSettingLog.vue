<!--join关联设置-->
<template>
    <el-drawer
            :title="title"
            :visible.sync="dialog"
            :before-close="handleClose"
            direction="rtl"
            custom-class="set-drawer"
            append-to-body
            :size="950"
            :show-close="false"
            ref="drawer"
    >
        <div class="set-drawer__content" v-loading="settings.loading">
            <div class="set-drawer__main">
                <setting-page ref="setting" v-if="reload" v-on="$listeners" :loadParams="settings"
                              :dataset-opt="datasetOpt"/>
            </div>
            <div class="set-drawer__footer">
                <el-button @click="cancelFn">{{ btnCancelTxt }}</el-button>
                <el-button type="primary" @click="submit">{{ btnCheckTxt }}</el-button>
            </div>
        </div>
    </el-drawer>
</template>

<script>
import {commonMixins} from "dc-front-plugin/src/api/commonMethods/common-mixins";
import settingPage from "./settingPage";

export default {
    name: "joinSettingLog",
    mixins: [commonMixins],
    components: {
        settingPage
    },
    data() {
        return {
            dialog: false,
            title: '关联设置',
            reload: false,
            datasetOpt: [],
            isNewDrop: false,//未设置join
        }
    },
    methods: {
        /**
         *
         * @param target
         * @param source
         * @param outputColumns
         */
        show({target, source, outputColumns}) {
            this.dialog = true;
            this.reload = true;
            let opt = JSON.parse(JSON.stringify([source, target]));
            this.datasetOpt = opt.map(li => {
                li.value = li.tableId;
                li.label = li.name;
                return li;
            })
            this.$nextTick(() => {
                if (target.joinOption.joinVo) {
                    this.$refs.setting.backData(target.joinOption.joinVo, outputColumns)
                } else {
                    this.isNewDrop = true;
                    this.$refs.setting.initDefData(outputColumns);
                }
            })
        },
        cancelFn() {
            this.dialog = false;
            this.reload = false;
        },
        handleClose(done) {//点击遮罩不关闭
            // done()
            this.cancelFn();
            if (this.isNewDrop) this.$emit("joinSet");
        },
        /**
         * // this.$refs.drawer.closeDrawer();
         */
        submit() {
            const vm = this;
            this.$refs.setting.validate((valid, params) => {
                if (valid) {
                    vm.$emit('save', params);
                    vm.cancelFn();
                }
            })
        }
    },
}
</script>

<style scoped lang="less">
.set-drawer {
  &__content {
    height: calc(100vh - 56px);
  }

  &__main {
    height: calc(100% - 2rem - 21px);
    padding: 20px 14px;
    box-sizing: border-box;
    overflow: auto;
  }

  &__footer {
    padding: 10px 20px;
    text-align: right;
    border-top: 1px solid #ddd;
  }
}
</style>
