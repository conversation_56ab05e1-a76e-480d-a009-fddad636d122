const IS_PROD = ["production", "test"].includes(process.env.NODE_ENV);
const path = require("path");
const resolve = function (src) {
    return path.join(__dirname, src);
}
const publicPath = process.env.BASE_URL;
// const {name} = require("./package");
// const CompressionPlugin = require('compression-webpack-plugin');
let productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
const assetsDir = "assets";
//配置end
module.exports = {
    publicPath: publicPath,
    lintOnSave: false, //禁用eslint
    productionSourceMap: false,
    runtimeCompiler: true,//支持template编译
    chainWebpack: (config) => {
        config.resolve.alias
            .set('@', resolve('src'))
            .set("@ra", resolve('src/projects/DataCenter/views/modeling/rapidAnalysis_plugin'))
            .set('@static', resolve('public/static'))
            .set('@assets', resolve('src/assets'))
            .set('@components', resolve('src/components'))
            .set('@router', resolve('src/projects/DataCenter/router'))
            .set('@store', resolve('src/projects/DataCenter/store'))
            .set('@views', resolve('src/projects/DataCenter/views'));  // key,value自行定义，比如.set('@@', resolve('src/components'))
        config.module.rule('fonts').use('url-loader').loader('url-loader').options({
            name: `[name]-[hash:8].[ext]`
        }).end();
        // 修改打包时css抽离后的filename及抽离所属目录
        config.plugin('extract-css')
            .tap(() => [{
                filename: `${assetsDir}/css/[name]-[contenthash:8].css`,
                chunkFilename: `${assetsDir}/css/[name]-[contenthash:8].css`,
                ignoreOrder: true
            }]);
        config.module.rule('images')
            .test(/\.(png|jpe?g|gif|webp)(\?.*)?$/)
            .use('url-loader')
            .loader('url-loader')
            .options({
                name: '[name]-[contenthash:8].[ext]',
            }).end();
        // HRM热更新
        config.resolve.symlinks(true);
        if (process.env.NODE_ENV === 'production') {
            config.plugin("CompressionPlugin").use('compression-webpack-plugin', [{
                algorithm: 'gzip', // 使用gzip压缩
                test: productionGzipExtensions, // 匹配文件名
                filename: '[path][base].gz', // 压缩后的文件名(保持原文件名，后缀加.gz)
                minRatio: 0.8, // 压缩率小于0.8才会压缩
                threshold: 10240, // 对超过10k的数据压缩
                deleteOriginalAssets: false // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
            }])
        }
        config.entry('main').add('babel-polyfill')
        config.entry.app = ['babel-polyfill', './src/projects/DataCenter/main.js']
        // set svg-sprite-loader
        config.module
            .rule('svg')
            .exclude.add(resolve('src/assets/images/icons'))
            .end();
        config.module
            .rule('svg-sprite-loader')
            .test(/\.svg$/)
            .include.add(resolve('src/assets/images/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: '[name]',
            })
            .end()

    },
    pages: {
        index: {
            // page 的入口
            entry: "src/projects/DataCenter/main.js",
            // 模板来源
            template: "public/index.html",
            // 在 dist/index.html 的输出
            filename: "index.html",
            // 当使用 title 选项时，
            // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
            title: "",
            // 在这个页面中包含的块，默认情况下会包含
            // 提取出来的通用 chunk 和 vendor chunk。
            chunks: ["chunk-vendors", "chunk-common", "index"],
        },
    },
    pwa: {
        iconPaths: {
            favicon32: "favicon.ico",
            favicon16: "favicon.ico",
        },
    },
    filenameHashing: true,
    assetsDir: "assets",
    devServer: {
        headers: {
            "Access-Control-Allow-Origin": "*",
        },
        index: "./index.html", //默认启动serve 打开index页面
        disableHostCheck: true,
        open: process.platform === "darwin",
        //host: '',
        port: 8080,
        https: false,
        hotOnly: false,
        proxy: {
            '/bs-api': {
                target: 'http://***********:8888/dataCenter/', //API服务器的地址
                // target: 'http://*************:8888/dataCenter/', //API服务器的地址
                changeOrigin: true,
                pathRewrite: {
                    '^/bs-api': ''
                },
                onProxyRes(proxyRes, req, res) {
                    const cookies = proxyRes.headers['set-cookie'];
                    if (cookies) {
                        const newCookies = cookies.map(cookie => {
                            return cookie.replace(/Path=\/dataCenter/, 'Path=/')
                        })
                        delete proxyRes.headers['set-cookie']
                        proxyRes.headers['set-cookie'] = newCookies
                    }
                },
            },
            '/dev-api': {
                target: 'http://**********:8888/dataCenter/', //API服务器的地址
                // target: 'http://*************:8888/dataCenter/', //API服务器的地址
                changeOrigin: true,
                pathRewrite: {
                    '^/dev-api': ''
                },
                onProxyRes(proxyRes, req, res) {
                    const cookies = proxyRes.headers['set-cookie'];
                    if (cookies) {
                        const newCookies = cookies.map(cookie => {
                            return cookie.replace(/Path=\/dataCenter/, 'Path=/')
                        })
                        delete proxyRes.headers['set-cookie']
                        proxyRes.headers['set-cookie'] = newCookies
                    }
                },
            },
            '/test-api': {
                target: 'http://*************:8888/dataCenter/', //API服务器的地址
                changeOrigin: true,
                pathRewrite: {
                    '^/test-api': ''
                },
                onProxyRes(proxyRes, req, res) {
                    const cookies = proxyRes.headers['set-cookie'];
                    if (cookies) {
                        const newCookies = cookies.map(cookie => {
                            return cookie.replace(/Path=\/dataCenter/, 'Path=/')
                        })
                        delete proxyRes.headers['set-cookie']
                        proxyRes.headers['set-cookie'] = newCookies
                    }
                },
            },
        }, // 设置代理fest
        historyApiFallback: {
            rewrites: [{from: /^\/datacenter/, to: "/index.html"}],
        },

        before: (app) => {
        },
    },
    // css相关配置
    css: {
        extract: {
            ignoreOrder: true
        },
        // extract: true, // 是否使用css分离插件 ExtractTextPlugin
        sourceMap: false, // 开启 CSS source maps?
        loaderOptions:{
            css: {
                localIdentName: '[name]-[hash]',
                camelCase: 'only',
            }
        }, // css预设器配置项
        modules: false, // 启用 CSS modules for all css / pre-processor files.
    },
    pluginOptions: {
        "style-resources-loader": {
            preProcessor: "less",
            patterns: [path.resolve(__dirname, "src/assets/global/global-var.less")],
        },
    },
    configureWebpack: require("./webpack.config")
};
