<template>
    <div class="SourceStatistics">
        <div class="ce-list">
            <div class="ce-list_item" v-for="(item ,k) in sources" :class="'ce-list_'+k"  :key="k" :title="item.label" @click="enterPage(k)">
                <span>{{item.label}}</span>
                <span class="ce-list_count" :title="item.dataCount">{{item.dataCount}}</span>
            </div>
        </div>
    </div>

</template>

<script>
import {roleData} from "@/projects/DataCenter/views/dataSources/dialog/mixins/roleData";
import {commonMixins} from "@/api/commonMethods/common-mixins";
import {servicesMixins} from "../service-mixins/service-mixins";
import {getMenu , getParentMenu} from "@/projects/DataCenter/router";
import {mapGetters} from "vuex"
export default {
    name: "SourceStatistics",
    mixins: [roleData ,commonMixins, servicesMixins],
    computed : {
        ...mapGetters(["userRight"])
    },
    data(){
        return {
            sources :{
                dataset : {
                    label : "数据集",
                    dataCount : 0 ,
                },
                rapidAnalyse : {
                    label : "快速分析",
                    dataCount : 0
                },
                modeling :{
                    label : "数据模型",
                    dataCount : 0
                },
                dataApi : {
                    label : "数据服务",
                    dataCount : 0
                },
                visual : {
                    label : "仪表盘",
                    dataCount : 0
                },
                portal : {
                    label : "主题门户",
                    dataCount : 0
                }
            }
        }
    },
    methods : {
        enterPage(key){
            let {menus} = getParentMenu(this.userRight), name, routerName;
            switch (key) {
                case 'rapidAnalyse':
                    name = 'fastAnalysisSaveTempTrans';
                    routerName = 'rapidAnalysis';
                    break;
                case 'dataApi':
                    name = 'processModelingSaveTempTrans';
                    routerName = 'processServiceManage';
                    break;
                case 'visual':
                    name = 'dashboardSaveOrUpdate';
                    routerName = 'dashboard';
                    break;
                case 'dataset':
                    name = '';
                    routerName = 'dataReady';
                    break;
                case 'portal':
                    name = '';
                    routerName = 'themePortal';
                    break;
                case 'modeling':
                    name = '';
                    routerName = 'processModeling';
                    break;
                default:
                    break;
            }
            if(menus.indexOf(routerName)>-1){
                this.$router.push({name : routerName});
            }else {
                this.$message.warning("无功能访问权限");
            }
        },
        getSourcesData(){
            const vm = this;
            let {homeServices ,homeMock ,settings} = this;
            settings.loading = true;
            let services = vm.getServices(homeServices ,homeMock);
            services.myResourceCount(settings).then(res=>{
                if (res.data.status === 0) {
                    let result = res.data.data;
                    this.sources.dataset.dataCount = result.dataSetCount;
                    this.sources.rapidAnalyse.dataCount = result.quickAnaCount;
                    this.sources.modeling.dataCount = result.dataModelCount;
                    this.sources.dataApi.dataCount = result.serviceAPICount;
                    this.sources.visual.dataCount = result.dashboardCount;
                    this.sources.portal.dataCount = result.portalCount;
                }
            })
        }
    },
    created(){
        this.getSourcesData();
    }
}
</script>

<style scoped lang="less">

    .ce-list {
        display: grid;
        grid-template-columns: repeat(2 , calc(50% - 4px) );
        grid-row-gap: 10px;
        grid-column-gap: 10px;
        padding: 0 10px;
        &_item {
            display: flex;
            justify-content: space-between;
            padding:14px 10px 14px 54px;
            height: 32px;
            line-height: 32px;
            background-color: rgba(0,136,255,0.04);
            background-position: 12px center;
            background-repeat: no-repeat;
            border-radius: 4px;
            
            cursor: pointer;

            &:hover {
                border: 1px solid #0088FF;
                box-shadow: 0 5px 10px 0 rgba(24,144,255,0.30);
            }
        }
        span {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        &_count {
            min-width: 45%;
            text-align: right;
            font-family: DINCondensed-Bold;
            font-size: 18px;
            color: rgba(0,0,0,0.84);
        }
        &_dataset {
            background-image: url("../../../assets/images/home/<USER>");
        }
        &_rapidAnalyse {
            background-image: url("../../../assets/images/home/<USER>");
        }
        &_modeling {
            background-image: url("../../../assets/images/home/<USER>");
        }
        &_dataApi {
            background-image: url("../../../assets/images/home/<USER>");
        }
        &_visual {
            background-image: url("../../../assets/images/home/<USER>");
        }
        &_portal{
            background-image: url("../../../assets/images/home/<USER>");
        }
    }
</style>
