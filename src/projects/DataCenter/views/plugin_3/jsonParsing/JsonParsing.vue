<template>
    <div class="JsonParsing">
       <!-- <el-form style="width: 100%;" label-position="top">
            <el-form-item prop="json">
                <div slot="label"> <span style="color: red"> * </span>添加解析字段</div>
                <el-button class="width100" type="primary" @click="show">添加解析字段</el-button>
            </el-form-item>
        </el-form>


            <common-dialog
                    class="params-dialog"
                    :width="width"
                    :visible.sync="visible"
                    @closed="clear_data"
                    v-loading="loading"
            >
                <div class="field-title" slot="title">
                    <h3>添加解析字段</h3>
                    <el-popover
                            placement="bottom-start"
                            width="434"
                            trigger="hover"
                            content="选择要解析的字段及解析节点深度后，输入解析的JSON样例数据点击“初始化”，系统自动生成输出字段列表。"
                    >
                    <span slot="reference" class="field-tip">
                        <i class="dg-iconp icon-help"></i>
                    </span>
                    </el-popover>
                </div>-->
                <!--参数配置弹窗内容页-->
                <AddFieldParsingDialog
                                       
                                       v-if="showDialog"
                                       :sourceData="sourceData"
                                       :tableOption="tableOption"
                                       :form="form"
                                       :retainChecked.sync="retainChecked"
                                       :ruleTableData="ruleTableData"
                                       :ruleTypeOptions="ruleTypeOptions"
                                       @updateCfg="updateCfg(arguments)"
                                       ref="addFieldParsing"></AddFieldParsingDialog>
              <!-- &lt;!&ndash; 弹窗按钮&ndash;&gt;
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" style="margin-left: 750px" @click="saveEditDialog">确 定</el-button>
                    <el-button @click="closeEditDialog">取 消</el-button>
                </span>

            </common-dialog>
-->
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
import AddFieldParsingDialog from './AddFieldParsingDialog'
import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"

    export default {
        name: "JsonParsing",
        mixins : [pluginMixins , servicesMixins],
        components: {AddFieldParsingDialog, preView},
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return{
                showDialog:false,
                title:'添加解析字段',
                tableOption:[],
                form: {
                    inputField: "",
                    exampleData:"",
                    outputCfg: []
                },
                ruleTableData: [],
                retainChecked:true,
                ruleTypeOptions: [
                    {}
                ],
                account: [],
                inputField:'',
                exampleData:'',
                retainTmp:true,
                ruleTableTmp:[],
            }
        },
        methods: {
            updateCfg(e){
                this.ruleTableTmp = [];
                this.ruleTableData = [];
                this.inputField = e[0];
                this.exampleData = e[1];
                this.retainTmp = e[2];
                let tmp = e[3];
                tmp.forEach(item =>{
                    this.ruleTableTmp.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        replaceName : item.replaceName,
                        type : item.type
                   }) ;
                });
                this.ruleTableTmp.forEach(item =>{
                    this.ruleTableData.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        replaceName : item.replaceName,
                        type : item.type
                    }) ;
                });
                this.retainChecked = this.retainTmp;

            },
            save(saveName,showTip){
                if (!this.$refs.addFieldParsing.saveChanges()){
                    return;
                }
                if (this.form.inputField === '' || this.form.inputField === undefined || this.form.inputField === null){
                    this.$message.warning("请选择输入字段!");
                    return;
                }
                /*  if (this.form.exampleData === '' || this.form.exampleData === undefined || this.form.exampleData === null){
                      this.$message.warning("请输入json样例数据!");
                      return;
                  }*/
                const vm = this, {pluginServices, pluginMock, settings,loadParams} = this;
                loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                let outputCfg = [];
                if(vm.ruleTableData.length === 0){
                    vm.$message.warning("输出配置不能为空!");
                    return;
                }

                for (let k = 0; k < vm.ruleTableData.length; k++) {
                    for (let key in vm.ruleTableData[k]) {
                        if (key !== 'serviceOrgId' && key !== 'columnName' && vm.ruleTableData[k][key] === '') {
                            if (key === 'columnCode') {
                                vm.$message.warning("输出字段不能为空!");
                                return;
                            } else if (key === 'type') {
                                vm.$message.warning("输出类型不能为空!");
                                return;
                            }
                        }
                    }
                }


               /* for(let i = 0; i < vm.ruleTableData.length; i++) {
                    let key = vm.ruleTableData[i].replaceName;
                    if(vm.account[key] === null || vm.account[key] === undefined) {
                        vm.account[key] = 1;
                    } else {
                        vm.tableOption.forEach(item => {
                            if (vm.ruleTableData[i].replaceName === item.columnCode){
                                vm.ruleTableData[i].replaceName = key + vm.account[key];
                                vm.account[vm.ruleTableData[i].replaceName ] = vm.account[key] + 1;
                            }
                        });
                    }
                }*/

                vm.ruleTableData.forEach((item, i) => {
                    outputCfg.push({
                        type: item.type,
                        length: item.length,
                        columnName: item.columnName,
                        columnCode: item.columnCode,
                        columnId: item.columnId,
                        replaceName: item.replaceName,
                        precision: '0',
                    });
                });

                let jsonParsingMeta = {
                    inputField: vm.form.inputField,
                    cicadaOutputCfgSet: outputCfg,
                    //是否保留原字段
                    originalField: vm.retainChecked
                };
                settings.loading = true;
                services.saveJsonParsing(vm.sourceData.id, jsonParsingMeta, loadParams).then(res => {
                    if (res.data.status === 0) {
                        showTip && this.$message.success("保存成功!");
                        if(saveName)saveName();
                        vm.$emit("setPreviewAble");
                    }
                });
                settings.loading = false;
            },
            preview(){
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            saveEditDialog(){
                if (!this.$refs.addFieldParsing.saveChanges()){
                    return;
                }
                this.close();
            },
            closeEditDialog(){
                //this.$refs.addFieldParsing.clearItems();
                this.form.inputField = this.inputField;
                this.form.exampleData = this.exampleData;
                this.retainChecked = this.retainTmp;
                this.ruleTableData = [];
                this.ruleTableTmp.forEach(item =>{
                    this.ruleTableData.push({
                        columnCode : item.columnCode,
                        columnId : item.columnId,
                        columnName : item.columnName,
                        length : item.length,
                        precision : item.precision,
                        replaceName : item.replaceName,
                        type : item.type
                    }) ;
                });
                this.clear_data();
                this.close();
            },
            async init(node) {
                const vm = this, {pluginServices, pluginMock, loadParams} = this;
                loadParams.loading = true;
                vm.stepId = node.id;
                let services = vm.getServices(pluginServices, pluginMock);
                services.queryJsonParingData(node.id,loadParams).then(res => {
                    vm.tableOption = res.data.data.inputColumn;
                    vm.tableOption.forEach(item => {
                        if (item.columnName == null) {
                            item.label = item.columnCode;
                            vm.account[item.columnCode] = 1;
                        } else {
                            item.label = item.columnName;
                            vm.account[item.columnName] = 1;
                        }
                        item.value = item.columnCode;
                        item.type = item.type;
                        return item;
                    });
                    vm.form.inputField = res.data.data.inputField == null ? '' : res.data.data.inputField;
                    vm.inputField = vm.form.inputField;
                    if (res.data.data.outputColumns.length > 0) {
                        res.data.data.outputColumns.forEach((item, i) => {
                            vm.ruleTableData.push({
                                replaceName: item.replaceName,
                                columnName: item.columnName,
                                type: item.type,
                                length: item.length,
                                precision: '0',
                                columnCode: item.columnCode,
                                columnId: item.columnId,
                            });
                        });
                        vm.ruleTableData.forEach(item =>{
                            vm.ruleTableTmp.push({
                                columnCode : item.columnCode,
                                columnId : item.columnId,
                                columnName : item.columnName,
                                length : item.length,
                                precision : item.precision,
                                replaceName : item.replaceName,
                                type : item.type
                            }) ;
                        });
                    }
                    if(res.data.data.originalField === "" || res.data.data.originalField === undefined || res.data.data.originalField === null) {
                        vm.retainChecked = true;
                    }else if (res.data.data.originalField === 'true'){
                        vm.retainChecked = true;
                    }else if(res.data.data.originalField === 'false'){
                        vm.retainChecked = false;
                    }
                    vm.retainTmp = vm.retainChecked;
                });
                services.getParameterTypes().then(res => {
                    vm.ruleTypeOptions = [];
                    res.data.data.forEach(item =>{
                        if(item.label==="String" || item.label==="Text"){
                            vm.ruleTypeOptions.push(item);
                        }
                    })
                });
                vm.showDialog = true;
            },
        },
        created() {
            this.init(this.sourceData);
        }
    }
</script>
<style scoped>
    .ce-normal_item.is-require::before , .ce-common_item.is-require::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }
    .field-title {
        display: flex;
        align-items: center;
    }
    h3 {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
    }
</style>
