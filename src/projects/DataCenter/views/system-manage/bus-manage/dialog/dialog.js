import UserFormPop from "@/components/common/user-form-pop";
import {commonMixins} from "@/api/commonMethods/common-mixins"
import {listMixins} from "@/api/commonMethods/list-mixins";
import addService from "../dialog/add-service/index"
import addInterface from "../dialog/add-interface-service/index"
export default {
    name: "towDialogs",
    mixins : [commonMixins ,listMixins],
    components: { UserFormPop, addService, addInterface},
    computed : {
    } ,
    props:{
        title:String,
        rowData: Object,
        serviceDetailShow:Boolean,
        interfaceDetailShow:Boolean,
        submitShow:Boolean,
        busId:String,
        functionCode:String,
    },
    data() {
        return {
            renew:false,
            visible:false,
            serviceShow:false,
            rows:"",
        };
    },
    methods: {
        show(value, row) {
            const vm = this;
            this.renew = true,
            this.visible = true;
            if (value === 'service') {
                this.serviceShow = true;
            } else {
                this.serviceShow = false;
                this.rows = row;
                vm.$nextTick(()=>{
                    vm.$refs.addNewInterface.show();
                });
            }

        },
        closeDialog() {
            this.renew = false,
            this.visible = false,
            this.serviceShow = false,
            this.$emit("closeDialog");
        },
        changePage() {
            this.$emit("changePage");
        },
        submit() {
            const vm = this;
            vm.$nextTick(()=>{
                if (this.serviceShow) {
                    vm.$refs.addNewService.submit();
                } else {
                    vm.$refs.addNewInterface.submit();
                }

            });
        }
    },
    created() {
    }
}
