<template>
    <div class="conditionFilterPlugin">
        <div>
                <el-checkbox v-model="checked">拼写sql</el-checkbox>
            </div>
     <!--   <el-form style="width: 100%;" label-position="top">
            <el-form-item prop="json">
                <div slot="label"> <span style="color: red"> * </span>设置过滤条件</div>
                <el-button class="width100" type="primary" @click="show">设置过滤条件</el-button>
            </el-form-item>
        </el-form>
        <common-dialog
                class="params-dialog"
                width="1020px"
                :visible.sync="visible"
                @closed="canceled"
                v-loading="loading"
        >
            <div class="field-title" slot="title">
                <h3>条件过滤</h3>
            </div>-->
            <!--参数配置弹窗内容页-->
            <condition-filter-dialog
                                      :variableColumns="variableColumns"
                                      v-show="showDialog && !checked"
                                      ref="conditionFilter"
                                      :sourceData="sourceData"
                                      :currentColumns="currentColumns"
                                      :currentValueColumns="currentValueColumns"
                                      :currentConditions="currentConditions"
                                      :currentRuleTableData="currentRuleTableData"
                                      @updateOutput="updateOutput(arguments)" @cancel="cancel"
            ></condition-filter-dialog>
            <!-- 弹窗按钮-->
           <!-- <span slot="footer" class="dialog-footer">
                    <el-button type="primary" style="margin-left: 750px" @click="submit">确 定</el-button>
                    <el-button @click="canceled">取 消</el-button>
                </span>

        </common-dialog>-->

        <div v-show="checked" class="mt10">
            <el-input
                type="textarea"
                :rows="10"
                placeholder="sql语句"
                v-model="filterInfo">
            </el-input>
        </div>
        <pre-view ref="preview"></pre-view>
    </div>
</template>

<script>
    import {pluginMixins} from "@/projects/DataCenter/views/plugin_3/plugin-mixins/plugin-mixins";
    import {servicesMixins} from "@/projects/DataCenter/views/plugin_3/service-mixins/service-mixins";
    import preView from "@/projects/DataCenter/views/plugin_3/component/PreView"
    import conditionFilterDialog from './conditionFilterDialog'
    import * as modelServices from "@/projects/DataCenter/views/modeling/service-mixins/service-mixins";

    export default {
        name: "conditionFilterPlugin",
        mixins : [pluginMixins , servicesMixins ,modelServices.servicesMixins],
        components: {preView,conditionFilterDialog},
        props: {
            sourceData: Object,
            rowData: Object,
            loadParams: Object,
        },
        data(){
            return{
                showDialog:false,
                currentColumns:[],
                currentValueColumns: [],
                currentConditions: [
                    {
                        no:0,
                        logic: "",
                        childrenLogic: "and",
                        children: [
                            {
                                field: "",
                                condition: "",
                                value: "",
                                fieldType: "",
                                isInput: true,
                                conditionType:"",
                            }
                        ]
                    }
                ],
                currentRuleTableData:[],
                variableColumns : [],
                checked: false,
                filterInfo: "",
                dateFormat : "yyyy-MM-dd"
            }
        },
        methods:{
            updateOutput(e) {
                this.currentConditions = e[0];
                this.currentRuleTableData = e[1];

                //this.$message.success("保存成功");
                this.clear_data();
                this.close();
            },
            canceled() {
                try {
                    this.$refs.conditionFilter.cancel();
                }catch (e) {

                }
            },
            submit() {
                this.$refs.conditionFilter.saveChanges();
            },
            save(...args){
                this.saveFn(...args);
            },
            preview(){
                this.$refs.preview.show(this.rowData, this.sourceData);
            },
            cancel(e){
                this.currentConditions = e;
                this.clear_data();
                this.close();
            },
             async getVariableColumns(){
                const vm = this, {modelingServices, modelingMock, settings} = this;
                let services = vm.getServices(modelingServices, modelingMock);
                vm.variableColumns = [];
                await services.queryTransByTransId(vm.rowData.transId, settings).then(res => {
                    if (res.data.status === 0) {
                        let result = res.data.data;

                        result.forEach(item => {
                            let fString = item.paramCode.split("@")[1];
                            fString = fString.substring(1);
                            fString = fString.substring(0,fString.length - 1);
                            vm.variableColumns.push({
                                label: fString,
                                type: item.paramType,
                                value: item.paramCode
                            })
                        })

                    }
                })
            },
            async init() { //初始化
                const vm = this, {pluginServices, pluginMock,loadParams} = this;
                let services = vm.getServices(pluginServices, pluginMock);
                await vm.getVariableColumns();
                vm.stepId = vm.sourceData.id;
                loadParams.loading = true;
                vm.filterInfo = '';
                services.conditionFilterQueryData(vm.stepId , loadParams).then( async res => {
                    if (res.data.code === 0) {
                        let result = res.data.data;
                        vm.setColumns(result.inputColumn , vm);
                        let pluginMeta = result.conditionFilterPlugin;
                        let currentConditions = await vm.setCondition(pluginMeta , vm);
                        vm.checked = pluginMeta.isHandWriting === '1';
                        vm.filterInfo = vm.checked ? pluginMeta.busiFilterConditionSql : '';

                        this.currentColumns.forEach(item => {
                            this.currentRuleTableData.push({
                                columnName:item.label,
                                columnCode:item.code,
                                columnType:item.type,
                                fromTable:item.stepName,
                            })
                        })
                        this.showDialog = true;
                        vm.$nextTick(()=>{
                            currentConditions && vm.$refs.conditionFilter.$refs.filter.initCondition(currentConditions , true);
                        })
                    }
                });
            },
            setColumns(inputColumn , vm) {
                if (!inputColumn) return;
                inputColumn.forEach(i => {
                    let column = {
                        label: i.columnZhName || i.columnName,
                        code: i.columnName,
                        value: i.columnName,
                        type: i.columnType,
                        stepName:i.stepName,
                    };
                    let column2 = {
                        label: i.columnZhName || i.columnName,
                        code: "`" + i.columnName + "`",
                        value: "`" + i.columnName + "`",
                        type: i.columnType,
                        stepName:i.stepName,
                    };
                    vm.currentColumns.push(column);
                    vm.currentValueColumns.push(column2);
                });

            },
            getInputType(condition) {
                return 'is null' === condition || 'is not null' === condition;
            },
            setCondition(pluginMeta , vm){
                if (pluginMeta.encasulationJudgContion){
                    let enJudgContion = pluginMeta.encasulationJudgContion.sort(vm.compare("no",true));
                    let currentConditions = [];
                    for (let i = 0; i < enJudgContion.length; i++) {
                        let {no} = enJudgContion[i];
                        let children = [];
                        var parse = JSON.parse(enJudgContion[i].judgCondition);
                        parse.forEach(judg => {
                            let {value , endTime ,valueType ,fieldType , condition ,conditionType } = judg;
                            if(valueType === 'constValue'){
                                if(fieldType === 'Timestamp'){
                                    value = new Date(value * 1000).Format();
                                    if(condition === "between"){
                                        condition = "in";
                                        endTime = new Date(endTime * 1000).Format();
                                    }else if(condition === "not between"){
                                        condition = "not in";
                                        endTime = new Date(endTime * 1000).Format();
                                    }
                                }
                                if(fieldType.toLowerCase() === 'date') {
                                    value = new Date(value).Format(vm.dateFormat);
                                }
                            }
                            let fieldD = vm.currentColumns.find(col =>  col.value === judg.field);
                            if (fieldD) {
                                children.push({
                                    field : judg.field,
                                    condition : condition + '|' + conditionType,
                                    condLabel: conditionType,
                                    condValue: condition,
                                    value : value,
                                    defaultType : fieldD.type,
                                    valueType  ,
                                    valueEnd : endTime

                                });
                            } else {
                                children.push({
                                    field: "",
                                    condition: "=|等于",
                                    condLabel: "等于",
                                    condValue: "=",
                                    value: "",
                                    valueEnd: "",
                                    valueType: "constValue",
                                    defaultType: "",

                                });
                            }
                        });
                        let outRelationContion = '';
                        if (no === 0){
                            outRelationContion = 'and';
                        }else {
                            outRelationContion = enJudgContion[no-1].outRelationContion;
                        }

                        currentConditions.push({
                            no : no,
                            childrenLogic : enJudgContion[no].relationCondition,
                            logic :outRelationContion,
                            children : children
                        });
                    }


                    return currentConditions;
                }
            },
            otherTimeTypeCon(judg) {
                return (judg.defaultType.toLowerCase() === 'time');
            },
            saveFn(saveName,showTip=true) {
                if (!this.checked && !this.$refs.conditionFilter.saveChanges()){
                    return;
                }
                else if(this.checked && !this.filterInfo){
                    return this.$message.warning('请输入过滤条件！');
                }
                const vm = this, {pluginServices, pluginMock, settings ,loadParams , dateFormat} = this;
                loadParams.loading = true;
                let services = vm.getServices(pluginServices, pluginMock);
                let pluginMeta = {};
                let encasulationJudgContion = [];
                if(!vm.checked){
                    for (let i = 0; i < vm.currentConditions.length; i++) {
                        let cond = vm.currentConditions[i];
                        encasulationJudgContion.push({
                            no : i,
                            relationCondition : cond.connectionType,
                            outRelationContion : cond.groupType,
                            judgCondition : cond.judgCondition,
                        });
                    }
                }
                pluginMeta["encasulationJudgContion"] = encasulationJudgContion;
                pluginMeta.isHandWriting = vm.checked ? '1' : '0';
                pluginMeta.busiFilterConditionSql = vm.checked ? vm.filterInfo : '';
                services.conditionSavePlugin(vm.stepId , pluginMeta , loadParams).then(res => {
                    if(res.data.code === 0){
                        if(!vm.checked){
                            vm.$nextTick(()=>{
                                vm.filterInfo = '';
                            })
                        }
                        showTip && vm.$message.success("保存成功");
                        vm.$emit("setPreviewAble");
                        if(saveName)saveName();
                    }
                })
            },
        },
        created() {
            this.init();
        }
    }
</script>

<style scoped>
    h3 {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
    }
</style>
