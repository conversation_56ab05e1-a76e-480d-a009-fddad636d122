import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";
import {servicesMixins} from "../../../service-mixins/service-mixins";
import { ajaxSettings } from "jquery";
export default {
    name: "dataList" ,
    props: {
        node: Object,
        dir_dataSetId : String
    },
    mixins : [attrMixins],
    data(){
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show : ()=>true,
            }
        };
        return {
            relationOptions:[],
            peersOptions:[],
            showDrill:false,
            axisList : {
                relationClass :{
                    label: '关系类别',
                    type: 'selectChoose',
                    value: "",
                    prop: 'relationClass',
                    placeholder: '请选择',
                    options: [],
                    multipleLimit: 2,
                    showLimit: false,
                    rule: [
                        {required: true, message: '请选择关系类别', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                idNumberField: {
                    label: '证件号码字段',
                    type: 'select',
                    value: [],
                    prop: 'idNumberField',
                    placeholder: '拖动证件号码字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加证件号码字段', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                idCard :{
                    label: '证件号码',
                    type: 'input',
                    value: "",
                    prop: 'idCard',
                    placeholder: '请输入',
                    options: [],
                    multipleLimit: 2,
                    showLimit: false,
                    rule: [
                        {required: true, message: '请输入证件号码', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                yAxis: {
                    label: '节点数据项',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动节点数据字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加度量', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                xAxis: {
                    label: '边数据项',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动边数据字段字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                startTime: {
                    label: '开始日期',
                    type: 'select',
                    value: [],
                    prop: 'startTime',
                    placeholder: '拖动日期数据字段字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '日期数据字段', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                endTime: {
                    label: '结束日期',
                    type: 'select',
                    value: [],
                    prop: 'endTime',
                    placeholder: '拖动日期数据字段字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '日期数据字段', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                timeStamp: {
                    label: '时间标签字段',
                    type: 'select',
                    value: [],
                    prop: 'timeStamp',
                    placeholder: '拖动时间标签字段到此处',
                    options: [],
                    multipleLimit: 2,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加时间标签字段', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                businessCol: {
                    label: '业务字段',
                    type: 'select',
                    value: [],
                    prop: 'businessCol',
                    placeholder: '拖动业务字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加业务字段', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                timeInterval: {
                    label: '时间间隔',
                    type: 'select',
                    value: [],
                    prop: 'timeInterval',
                    placeholder: '拖动时间间隔字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    rule: [
                        {required: true, message: '请添加时间间隔字段', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                peerClass :{
                    label: '同行类别',
                    type: 'selectS',
                    value: [],
                    prop: 'peerClass',
                    placeholder: '请选择',
                    options: [],
                    multipleLimit: 2,
                    showLimit: false,
                    rule: [
                        {required: false, message: '请选择同行类别', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                timeIntervalValue :{
                    label: '时间间隔值',
                    type: 'timeInput',
                    value: "",
                    timeValue:"mins",
                    prop: 'timeIntervalValue',
                    placeholder: '请输入',
                    options: [],
                    multipleLimit: 2,
                    showLimit: false,
                    rule: [
                        {required: true, message: '请输入时间间隔值', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                startDate :{
                    label: '开始日期',
                    type: 'dataChoose',
                    value: [],
                    prop: 'startDate',
                    placeholder: '请选择',
                    options: [],
                    multipleLimit: 2,
                    showLimit: false,
                    rule: [
                        {required: true, message: '请选择日期', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                limit :{
                    label: '分析条数',
                    type: 'input',
                    value: "100",
                    prop: 'limit',
                    placeholder: '请输入',
                    options: [],
                    multipleLimit: 2,
                    showLimit: false,
                    rule: [
                        {required: false, message: '请输入查询条数', trigger: 'blur'}
                    ],
                    show : ()=>true,
                },
                
                
            },
            query : null
        }
    },
    computed: {
        chartData() {
            return {
                xAxis: this.axisList.xAxis.value,
                xData: this.axisList.xAxis.options,
                yAxis: this.axisList.yAxis.value,
                yData: this.axisList.yAxis.options,
                relationClass :this.axisList.relationClass.value,
                idNumberFieldAxis: this.axisList.idNumberField.value,
                idNumberFieldData: this.axisList.idNumberField.options,
                idCard: this.axisList.idCard.value,

                startTimeAxis: this.axisList.startTime.value,
                startTimeData: this.axisList.startTime.options,
                endTimeAxis: this.axisList.endTime.value,
                endTimeData: this.axisList.endTime.options,

                timeStampAxis: this.axisList.timeStamp.value,
                timeStampData: this.axisList.timeStamp.options,
                businessColAxis: this.axisList.businessCol.value,
                businessColData: this.axisList.businessCol.options,
                timeIntervalAxis: this.axisList.timeInterval.value,
                timeIntervalData: this.axisList.timeInterval.options,

                peerClass : this.axisList.peerClass.value,
                timeIntervalValue: this.axisList.timeIntervalValue.value  ,
                timeInterUnit : this.axisList.timeIntervalValue.timeValue ,
                startDate : this.axisList.startDate.value,
                limit : this.axisList.limit.value,

                filterData: this.filterSelects,
                datasetId: this.datasetId,
                classifierStatId: this.classifierStatId,
                dataset: this.dataset ,
                filterList : this.query
            };
        }
    },
    methods : {
        getSelectOption() {
            const vm = this, {visualServices, visualMock, settings} = this;
            let services = vm.getServices(visualServices, visualMock);
            settings.loading = true;
            services.getRelationTypes(settings).then(res => { 
                let result = res.data.data;
                result.relationType.forEach(element => {
                    let relationOption = {
                        label: element.name,
                        value: element.code
                    };
                    vm.axisList.relationClass.options.push(relationOption);
                });
                result.peerType.forEach(element => {
                    let peerOption = {
                        label: element,
                        value: element
                    };
                    vm.axisList.peerClass.options.push(peerOption);
                });
            })
        },
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx , this.dragTreeNode.indexType , 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        initValue(){
            const vm = this;
            vm.$nextTick(()=>{
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let {beforeData , query} = JSON.parse(vm.node.widget),nodeData = JSON.parse(beforeData);
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId :true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.xAxis.options = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.xAxis.value = nodeData.xAxis ? nodeData.xAxis : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];

                vm.axisList.relationClass.value = nodeData.relationClass ? JSON.parse(JSON.stringify(nodeData.relationClass)) : "";
                vm.axisList.idNumberField.value = nodeData.idNumberFieldAxis ? nodeData.idNumberFieldAxis : [];
                vm.axisList.idNumberField.options = nodeData.idNumberFieldData ? JSON.parse(JSON.stringify(nodeData.idNumberFieldData)) : [];
                vm.axisList.idCard.value = nodeData.idCard ? JSON.parse(JSON.stringify(nodeData.idCard)) : "";

                vm.axisList.startTime.value = nodeData.startTimeAxis ? nodeData.startTimeAxis : [];
                vm.axisList.startTime.options = nodeData.startTimeData ? JSON.parse(JSON.stringify(nodeData.startTimeData)) : [];
                vm.axisList.endTime.value = nodeData.endTimeAxis ? nodeData.endTimeAxis : [];
                vm.axisList.endTime.options= nodeData.endTimeData ? JSON.parse(JSON.stringify(nodeData.endTimeData)) : [];

                vm.axisList.timeStamp.value = nodeData.timeStampAxis && nodeData.timeStampAxis[0] && nodeData.timeStampAxis[0][0] ? nodeData.timeStampAxis[0][0] : [];
                vm.axisList.timeStamp.options = nodeData.timeStampData ? JSON.parse(JSON.stringify(nodeData.timeStampData)) : [];
                vm.axisList.businessCol.value = nodeData.businessColAxis ? nodeData.businessColAxis : [];
                vm.axisList.businessCol.options = nodeData.businessColData ? JSON.parse(JSON.stringify(nodeData.businessColData)) : [];
                vm.axisList.timeInterval.value = nodeData.timeIntervalAxis ? nodeData.timeIntervalAxis : [];
                vm.axisList.timeInterval.options = nodeData.timeIntervalData ? JSON.parse(JSON.stringify(nodeData.timeIntervalData)) : [];
                
                vm.axisList.peerClass.value = nodeData.peerClass ? JSON.parse(JSON.stringify(nodeData.peerClass)) : [];
                vm.axisList.timeIntervalValue.value = nodeData.timeIntervalValue ? JSON.parse(JSON.stringify(nodeData.timeIntervalValue)) : "";
                vm.axisList.timeIntervalValue.timeValue = nodeData.timeInterUnit ? JSON.parse(JSON.stringify(nodeData.timeInterUnit)) : "mins";
                vm.axisList.startDate.value = nodeData.startDate ? JSON.parse(JSON.stringify(nodeData.startDate)) : [];
                vm.axisList.limit.value = nodeData.limit ? JSON.parse(JSON.stringify(nodeData.limit)) : "100";

                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                //查询
                vm.query  = [];
                //隐藏下拉
                vm.dropDownHide([vm.axisList.xAxis.options , vm.axisList.yAxis.options]);
                vm.inputData();
                vm.addAxisName("","",vm.axisList);
            })
        },
        validate(){
            if (this.chartData.xAxis.length < this.axisList["xAxis"].multipleLimit && this.node.code !== 'TableChartWidget') {
                this.$message.info('请添加一个边数据项!');
                return;
            } else 
            if (this.chartData.yAxis.length < this.axisList["yAxis"].multipleLimit) {
                this.$message.info('请添加一个点数据项!');
                return;
            }
            
            if (this.chartData.timeIntervalValue === "" || this.chartData.startDate.length === 0 ||
                this.chartData.idCard === "" || this.chartData.relationClass === "" ||
                this.chartData.idNumberFieldAxis.length === 0 || this.chartData.startTimeAxis.length === 0 ||
                this.chartData.endTimeAxis.length === 0 ||this.chartData.timeStampAxis.length === 0 ||
                this.chartData.businessColAxis.length === 0 ||this.chartData.timeIntervalAxis.length === 0
            ) {
                this.$message.info('请输入完整信息!');
                return;
            }
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setBuisinessRelationPanel', this.node.id);
        },
    },
    created() {
        this.getSelectOption();
    }
}
