import {attrMixins} from "../../attr-mixins/attr-mixins"
import {globalBus} from "@/api/globalBus";

export default {
    name: "dataList",
    props: {
        node: Object,
        dir_dataSetId: String
    },
    mixins: [attrMixins],
    data() {
        let formList = {
            filterData: {
                label: '过滤器',
                type: 'selectGroup',
                multipleLimit: 1,
                placeholder: '拖动维度/度量字段到此处',
                prop: 'filter',
                show: () => true,
            }
        };
        return {
            axisList: {
                drilling: {
                    label: "钻取/维度",
                    type: 'drill_select',
                    list: [],
                    show: (axis) => axis.list.length
                },
                xAxis: {
                    label: '度量',
                    type: 'select',
                    value: [],
                    prop: 'xAxis',
                    placeholder: '拖动度量字段到此处',
                    options: [],
                    multipleLimit: 5,
                    showLimit: true,
                    show: () => true,
                    rule: [
                        {required: true, message: '请添加度量', trigger: 'blur'}
                    ]
                },
                yAxis: {
                    label: '维度',
                    type: 'select',
                    value: [],
                    prop: 'yAxis',
                    placeholder: '拖动维度字段到此处',
                    options: [],
                    multipleLimit: 1,
                    showLimit: true,
                    show: () => true,
                    rule: [
                        {required: true, message: '请添加维度', trigger: 'blur'}
                    ]
                },
                ...formList,

            },
            query: null,

        }
    },
    computed: {
        chartData() {
            return {
                xAxis: this.axisList.xAxis.value,
                xData: this.axisList.xAxis.options,
                yAxis: this.axisList.yAxis.value,
                yData: this.axisList.yAxis.options,
                filterData: this.filterSelects,
                datasetId: this.datasetId,
                classifierStatId: this.classifierStatId,
                dataset: this.dataset,
                filterList: this.query,
                drill: this.axisList.drilling.list
            };
        }
    },
    methods: {
        /**
         *
         * @param e
         * @param inx
         */
        dropEnd(e, inx) {
            this.inputData();
            let multipleLimit = this.axisList[inx].multipleLimit, value = this.axisList[inx].value;
            /*if (this.axisList[inx].prop === 'xAxis' && this.dragTreeNode.indexType === "DIMENSION") {
                this.$message.warning('请拖入维度输入框');
                return;
            } else if (this.axisList[inx].prop === 'yAxis' && this.dragTreeNode.indexType === "MEASURE") {
                this.$message.warning('请拖入度量输入框');
                return;
            }*/
            if (!multipleLimit || value.length < multipleLimit) {
                this.addSelectedData(inx, this.dragTreeNode.indexType, 'axisList');
            } else {
                this.$message.warning('超出可选数量');
            }
        },
        /**
         * 添加字段
         * @param inx
         * @param type
         * @param list
         */
        async addSelectedData(inx, type, list) {
            let value = this[list][inx].value, options = this[list][inx].options;
            let newValue = new Date().getTime().toString();
            /*this[list][inx].options = options.filter(opt => {
                return opt.value !== this.dragTreeNode.id;
            });*/
            this[list][inx].options.push(this.setDropOpt(type, newValue,()=> this[list][inx].prop === 'xAxis'));
            /* if (value.indexOf(this.dragTreeNode.id) === -1) {
                 value.push(this.dragTreeNode.id);
             }*/
            value.push(newValue);
            if (inx === "yAxis") {
                // let hasTime = await this.hasTimeField();
                // await this.yAxisChange(hasTime);
            }
            this.addAxisName(this[list][inx].options, inx, this[list]);
        },
        /**
         * 是否存在时间字段
         */
        hasTimeField() {
            let optionY = this.axisList.yAxis.options;
            let hasTime = false;
            for (let i = 0; i < optionY.length; i++) {
                if (!optionY[i].data.mbColumn) continue; //optionY[i].data.format === undefined || optionY[i].data.format === 'TEXT' || optionY[i].data.format === 'NUMBER' ||
                hasTime = true;
                break;
            }
            return hasTime;
        },
        /**
         * 维度改变事件
         */
        yAxisChange(hasTime = true) {
            if (hasTime) return;
            for (let axis of this.axisList.xAxis.options) {
                axis.fastCount.name = "无";
                axis.fastCount.value = "none";
                let funcName = axis.func.name !== "无" ? axis.func.name : "",
                    fastName = axis.fastCount && axis.fastCount.name !== "无" ? axis.fastCount.name : "",
                    name = axis.data.label;
                axis.label = funcName && fastName ?
                    `${name}(${funcName}-${fastName})` :
                    fastName ? `${name}(${fastName})` :
                        funcName ? `${name}(${funcName})` : name;
            }
            this.addAxisName(this.axisList.xAxis.options, "xAxis", this.axisList);
        },
        /**
         * 设置选项
         * @param type
         * @param newValue
         * @returns {{visible: boolean, data: {}, alias: *, label: *, value: *}|{filter: {}, duplicateF: {func: string, label: string, value: string}, visible: boolean, data, func: {}, format: {}, alias, rank: {}, fastCount: {}, label: *, value}}
         */
        setDropOpt(type, newValue,classifyFn=()=>{}) {
            let opt;
            if (!classifyFn()) {
                opt = {
                    visible: false,
                    label: this.dragTreeNode.label,
                    value: newValue,
                    data: this.dragTreeNode,
                    alias: this.dragTreeNode.alias,
                    rank: {},
                    filter: {},
                };
                if (this.dragTreeNode.mbColumn) { //&& this.dragTreeNode.format !== undefined && this.dragTreeNode.format !== "TEXT" && this.dragTreeNode.format !== "NUMBER"
                    opt.timeFormat = {
                        label: "时间格式",
                        value: "none",
                        name: '无',
                        children: {
                            "none": {label: "无"},
                            "yyyyMMdd": {label: "yyyyMMdd"},
                            "yyyy-MM-dd": {label: "yyyy-MM-dd"},
                            "yyyy.MM.dd": {label: "yyyy年MM月dd日", val: "yyyy年MM月dd日"},
                            "yyyy/MM/dd": {label: "yyyy/MM/dd"},
                        }
                    };
                }
            } else if (classifyFn()) {
                this.addYAxisAttr(this.dragTreeNode);
                let fastCountDatacopy = this.requestFastCount(false); //请求快速计算数据
                opt = {
                    visible: false,
                    label: this.dragTreeNode.label,
                    value: newValue,
                    data: this.dragTreeNode,
                    alias: this.dragTreeNode.alias,
                    func: {},
                    rank: {},
                    filter: {},
                    duplicateF: {
                        label: '去重',
                        value: '',
                        func: 'duplicateF' //回调方法
                    },
                    fastCount: {},
                };
                let fastCharts = ['BarChartWidget', 'StackBarChartWidget', "TransverseBarChartWidget", "TransverseStackBarChartWidget", 'LineChartWidget', 'AreaGraphWidget', 'CombinationWidget'];
                if (classifyFn() && fastCharts.indexOf(this.node.code) > -1) {
                    opt.fastCount = {
                        label: "快速计算",
                        value: "none",
                        name: "无",
                        children: fastCountDatacopy
                    };
                }
            }
            opt.rank = {
                label: '排序',
                value: 'none',
                name: '无',
                children: {
                    none: {
                        label: '无'
                    },
                    ascending: {
                        label: '升序'
                    },
                    descending: {
                        label: '降序'
                    },
                }
            };
            opt.filter = {
                label: '过滤',
                func: 'filter'
            };
            let isExp = this.isExp(this.dragTreeNode.exp);
            classifyFn() && this.node.code !== 'TableChartWidget' && !isExp ?
                opt.func = {
                    label: '汇总',
                    value: 'none',
                    name: '无',
                    children: {
                        none: {label: '无'},
                        sum: {label: '总和'},
                        count: {label: '计数'},
                        mean: {label: '平均值'},
                        max: {label: '最大值'},
                        min: {label: '最小值'},
                    }
                } : true;
            return opt;
        },
        setTransverseBarChart() {
            this.axisList.xAxis.label = "度量";
            this.axisList.yAxis.label = "维度/Y轴";
        },
        initValue() {
            const vm = this;
            vm.$nextTick(() => {
                vm.node.setData = true;
                if (!vm.node.widget) return;
                let transverseCode = ['TransverseStackBarChartWidget', 'TransverseBarChartWidget'];
                if (transverseCode.indexOf(vm.node.code) > -1) {
                    vm.setTransverseBarChart();
                }
                let {beforeData, query} = JSON.parse(vm.node.widget), nodeData = JSON.parse(beforeData);
                nodeData.classifierStatId ? vm.classifierStatId = nodeData.classifierStatId : true;
                nodeData.datasetId ? vm.datasetId = nodeData.datasetId : true;
                nodeData.datasetId ? vm.$refs.nodeTree.setDataSetId(nodeData.datasetId) : true;
                vm.axisList.xAxis.options = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                vm.axisList.yAxis.options = nodeData.yData ? JSON.parse(JSON.stringify(nodeData.yData)) : [];
                vm.axisList.xAxis.value = nodeData.xAxis ? nodeData.xAxis : [];
                vm.axisList.yAxis.value = nodeData.yAxis ? nodeData.yAxis : [];
                nodeData.filterData ? vm.filterSelects = vm.copyArrayObj(nodeData.filterData) : true;
                vm.dataset = nodeData.dataset ? vm.copyArrayObj(nodeData.dataset) : {};
                vm.chartAttr = nodeData.xData ? JSON.parse(JSON.stringify(nodeData.xData)) : [];
                //查询
                // vm.query  = query && query.length ? JSON.parse(query) : null;
                vm.query = [];
                //钻取
                vm.axisList.drilling.list = nodeData.drill ? JSON.parse(JSON.stringify(nodeData.drill)) : [];
                //隐藏下拉
                vm.dropDownHide([vm.axisList.xAxis.options, vm.axisList.yAxis.options]);
                vm.inputData();
                vm.addAxisName("", "", vm.axisList);
            })
        },
        validate() {
            if (this.chartData.xAxis.length === 0 && this.node.code !== 'TableChartWidget') {
                this.$message.info('请添加度量!');
                return;
            } else if (this.chartData.yAxis.length === 0) {
                this.$message.info('请添加维度!');
                return;
            }
            this.emitSettingData();
        },
        emitSettingData() {
            globalBus.$emit('setCombinationWidgetPanel', this.node.id);
        },
        /**
         *  设置函数名称
         * @param params
         * @param item
         * @param i
         */
        setFunc(params, item, i) {
            let reg = /[求\(\)]/g, funcName = item.options[i].func.name !== "无" ? item.options[i].func.name : "",
                fastName = item.options[i].fastCount && item.options[i].fastCount.name !== "无" ? item.options[i].fastCount.name.replace(reg, '') : "",
                name = item.options[i].data.label;
            item.options[i].label = funcName && fastName ?
                `${name}(${funcName}-${fastName})` :
                fastName ? `${name}(${fastName})` :
                    funcName ? `${name}(${funcName})` : name;
            this.addAxisName(item.options, item.prop, this.axisList);
        }
    }
}
